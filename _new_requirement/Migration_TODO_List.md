# 模組移植具體 TODO List

## 一、OTP 機制模組移植 TODO List

### 1.1 Controller 層重構
- [ ] **從 GeneralController 抽離 OTP 功能**
  - 移除 `/getOtpCode` 和 `/checkOtpCode` 端點
  - 建立新的 `OtpController` 只包含 OTP 相關端點
  - 保留 JWT Token 驗證邏輯
  - 移除非 OTP 相關的方法（如 `saveVerificationFromMydataC3`, `checkPCode2566` 等）

- [ ] **整理其他 Controller 中的 OTP 呼叫**
  - 檢查 `D3StepController` 中的 OTP 相關呼叫
  - 檢查 `CCController` 中的 OTP 相關呼叫
  - 統一改為呼叫新的 OTP 服務介面

### 1.2 Service 層重構
- [ ] **從 GenericService 抽離 OTP 功能**
  - 移除 `getOtp()` 和 `checkOtp()` 方法
  - 建立新的 `OtpApplicationService`
  - 保留 OTP 發送間隔檢查邏輯
  - 移除非 OTP 相關的業務邏輯

- [ ] **整理 ValidationService**
  - 抽離 `getSessionKey()` 和 `checkSessionKey()` 到 OTP 模組
  - 建立 `OtpValidationService` 專門處理 OTP 驗證
  - 保留其他驗證功能在原 Service

- [ ] **整理 KgiService**
  - 抽離 `sendOTP()` 和 `checkOTP()` 方法
  - 建立 `OtpExternalService` 處理外部 API 呼叫
  - 保留其他外部服務呼叫在原 Service

### 1.3 Repository 層整理
- [ ] **建立 OTP 專用 Repository 介面**
  - 從 `OTPDataRepository` 建立領域層介面
  - 實作 `IOtpVerificationRepository`
  - 加入 OTP 相關的查詢方法

### 1.4 配置檔整理
- [ ] **抽離 OTP 相關配置**
  - 從 `GlobalConfig` 抽離 OTP 相關 URL
  - 建立 `OtpConfiguration` 類別
  - 整合 `OTPConst` 的常數定義
  - 配置 OTP 有效期、重試次數等參數

### 1.5 安全機制整理
- [ ] **整理請求限流機制**
  - 確保 `@RequestLimit` 註解在新 Controller 正常運作
  - 建立 OTP 專用的限流策略
  - 配置防暴力破解機制

### 1.6 測試建立
- [ ] **建立單元測試**
  - OTP 生成邏輯測試
  - 時間窗口驗證測試
  - 發送間隔控制測試

- [ ] **建立整合測試**
  - 模擬外部 API 呼叫
  - 測試完整的發送和驗證流程

## 二、發信機制 (Email/SMS) 模組移植 TODO List

### 2.1 Service 層重構
- [ ] **整理 MailHunterService**
  - 保留核心發信功能
  - 移除與特定業務耦合的方法
  - 建立統一的發信介面 `INotificationService`
  - 實作郵件和簡訊的策略模式

- [ ] **整理 SMSHunterService**
  - 抽離簡訊發送核心功能
  - 整合到統一的通知服務
  - 保留短網址生成功能

- [ ] **移除業務耦合**
  - 從各業務 Service 中移除直接呼叫 `createMailHunterHistory`
  - 改為透過事件或介面呼叫
  - 建立 `NotificationFacade` 提供統一入口

### 2.2 Job 層重構
- [ ] **整理 SendMailHunterJob**
  - 移到通知模組專屬 package
  - 優化批次處理邏輯
  - 加入錯誤重試機制

- [ ] **整理 SendSMSHunterJob**
  - 整合到統一的通知排程
  - 優化查詢效能

### 2.3 Template 管理
- [ ] **建立模板管理服務**
  - 整理散落在 `SystemConst` 的模板 ID
  - 建立 `NotificationTemplateService`
  - 支援動態載入模板

### 2.4 FTP 整合
- [ ] **抽離 FTP 功能**
  - 從 `MailHunterService` 抽離 `uploadPdfTemplate`
  - 建立 `AttachmentService` 處理附件
  - 整合 `FTPUtil` 功能

### 2.5 配置整理
- [ ] **統一通知配置**
  - 整合 MailHunter 相關配置
  - 整合 SMS 相關配置
  - 建立環境別配置管理

### 2.6 資料庫優化
- [ ] **優化查詢效能**
  - 為 `MailHunterHistory` 加入適當索引
  - 優化批次查詢 SQL
  - 考慮分表策略

## 三、OtherPCode2566 驗證機制模組移植 TODO List

### 3.1 Service 層重構
- [ ] **整理 BankService**
  - 抽離 `otherPCode2566` 和 `kgiPCode2566` 方法
  - 建立 `AccountVerificationService`
  - 保留其他銀行服務功能

- [ ] **整理 DigitalApplyService**
  - 抽離 `checkPCode2566` 相關方法
  - 移除與 PCode2566 無關的業務邏輯
  - 建立專門的驗證流程服務

### 3.2 Controller 層整理
- [ ] **建立專屬 Controller**
  - 從 `GeneralController` 移除 PCode2566 相關端點
  - 建立 `AccountVerificationController`
  - 整理 API 路徑和參數

### 3.3 外部服務整合
- [ ] **建立外部服務 Client**
  - 封裝 VALIDATION Server 呼叫
  - 統一錯誤處理
  - 加入重試機制

### 3.4 XML 解析優化
- [ ] **優化 XML 解析**
  - 整理 `XmlParser` 使用
  - 建立專門的 PCode2566 回應解析器
  - 處理 90 天手機異動邏輯

### 3.5 錯誤碼管理
- [ ] **整理錯誤碼**
  - 從 `ReturnCode` 抽離 PCode2566 相關錯誤碼
  - 建立驗證專用錯誤碼體系
  - 提供清晰的錯誤訊息

### 3.6 驗證規則管理
- [ ] **集中驗證規則**
  - 建立 `VerificationRuleEngine`
  - 管理錯誤次數限制
  - 管理帳戶類型檢查規則

## 四、共通重構任務

### 4.1 領域模型建立
- [ ] **為每個模組建立領域模型**
  - 定義聚合根
  - 定義值物件
  - 定義領域服務介面

### 4.2 API 文件
- [ ] **建立 API 文件**
  - 使用 SpringDoc OpenAPI 3
  - 為每個模組建立獨立文件
  - 提供範例和說明

### 4.3 監控和日誌
- [ ] **建立監控機制**
  - 整合 Micrometer
  - 建立業務指標
  - 優化日誌格式

### 4.4 Spring Boot 3 升級
- [ ] **處理依賴升級**
  - javax.* → jakarta.*
  - 更新 Spring Security 配置
  - 更新資料庫驅動

### 4.5 測試覆蓋
- [ ] **提升測試覆蓋率**
  - 單元測試覆蓋率達 80%
  - 整合測試覆蓋主要流程
  - 加入契約測試

## 五、實施順序建議

### Phase 1: OTP 模組 (2週)
1. 抽離 OTP Controller 和 Service
2. 建立 OTP 領域模型
3. 整合測試

### Phase 2: 發信機制模組 (3週)
1. 整理 MailHunter 和 SMS 服務
2. 建立統一通知介面
3. 優化排程任務

### Phase 3: PCode2566 驗證模組 (2週)
1. 抽離驗證服務
2. 優化外部服務呼叫
3. ���理錯誤處理

### Phase 4: 整合測試 (1週)
1. 端對端測試
2. 效能測試
3. 安全測試

### Phase 5: 部署和監控 (1週)
1. 漸進式部署
2. 監控設置
3. 文件完善

## 六、風險管理

### 6.1 技術風險
- [ ] 建立 Feature Toggle 機制
- [ ] 保留舊 API 相容性
- [ ] 建立回滾計劃

### 6.2 業務風險
- [ ] 與業務部門充分溝通
- [ ] 建立 A/B 測試機制
- [ ] 準備應急預案

### 6.3 資料風險
- [ ] 資料備份策略
- [ ] 資料遷移驗證
- [ ] 資料一致性檢查