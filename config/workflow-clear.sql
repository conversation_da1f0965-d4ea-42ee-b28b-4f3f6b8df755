-- select* from  act_ge_bytearray;
DELETE FROM public.act_ru_ext_task WHERE TRUE;
DELETE FROM public.act_ru_filter WHERE TRUE;
--  避免使用者 的舊 process 被移除導致錯誤 start
-- DELETE FROM public.act_ru_task WHERE TRUE;
--  避免使用者 的舊 process 被移除導致錯誤 end
DELETE FROM public.act_ru_variable WHERE TRUE;
DELETE FROM public.act_ru_execution WHERE TRUE;
DELETE FROM public.act_re_procdef WHERE TRUE;
DELETE FROM public.act_ru_task_meter_log WHERE TRUE;
DELETE FROM act_ge_bytearray WHERE TRUE;
DELETE FROM public.act_hi_actinst WHERE TRUE;
DELETE FROM public.act_hi_detail WHERE TRUE;
DELETE FROM public.act_hi_identitylink WHERE TRUE;
DELETE FROM public.act_hi_procinst WHERE TRUE;
DELETE FROM public.act_hi_taskinst WHERE TRUE;
DELETE FROM public.act_hi_varinst WHERE TRUE;
DELETE FROM public.act_re_decision_def WHERE TRUE;
DELETE FROM public.act_re_decision_req_def WHERE TRUE;
DELETE FROM public.act_re_deployment WHERE TRUE;
--??? DELETE FROM public.act_ru_event_subscr WHERE TRUE;
