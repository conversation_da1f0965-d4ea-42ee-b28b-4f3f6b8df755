#\u958B\u95DC=JOBNAME+Switch  1=on 0=off
#corn= \u79D2(0-59) \u5206(0-59) \u6642(0-23) \u65E5(1-31) \u6708(1-12) \u9031(1,\u65E5-7,\u516D)
#\u65E5\u8207\u9031\u4E92\u65A5\uFF0C\u5176\u4E2D\u4E4B\u4E00\u5FC5\u9808\u70BA ?
#\u53EF\u4F7F\u7528\u7684\u503C\u6709\uFF1A\u55AE\u4E00\u6578\u503C\uFF0826\uFF09\u3001\u7BC4\u570D\uFF0850-55\uFF09\u3001\u6E05\u55AE\uFF089,10\uFF09\u3001\u4E0D\u6307\u5B9A\uFF08*\uFF09\u8207\u9031\u671F\uFF08*/ 3\uFF09

#CRON\u3000\u7C21\u6613\u8AAA\u660E
#"0 0 12 * * ?"    \u6BCF\u5929\u4E2D\u5348\u5341\u4E8C\u9EDE\u89F8\u767C
#"0 15 10 ? * *"    \u6BCF\u5929\u65E9\u4E0A10\uFF1A15\u89F8\u767C
#"0 15 10 * * ?"    \u6BCF\u5929\u65E9\u4E0A10\uFF1A15\u89F8\u767C
#"0 15 10 * * ? *"    \u6BCF\u5929\u65E9\u4E0A10\uFF1A15\u89F8\u767C
#"0 15 10 * * ? 2005"    2005\u5E74\u7684\u6BCF\u5929\u65E9\u4E0A10\uFF1A15\u89F8\u767C
#"0 * 14 * * ?"    \u6BCF\u5929\u5F9E\u4E0B\u53482\u9EDE\u958B\u59CB\u52302\u9EDE59\u5206\u6BCF\u5206\u9418\u4E00\u6B21\u89F8\u767C
#"0 0/5 14 * * ?"    \u6BCF\u5929\u5F9E\u4E0B\u53482\u9EDE\u958B\u59CB\u52302\uFF1A55\u5206\u7ED3\u675F\u6BCF5\u5206\u9418\u4E00\u6B21\u89F8\u767C
#"0 0/5 14,18 * * ?"    \u6BCF\u5929\u7684\u4E0B\u53482\u9EDE\u81F32\uFF1A55\u548C6\u9EDE\u81F36\u9EDE55\u5206
#"0 0-5 14 * * ?"    \u6BCF\u592914:00\u81F314:05\u6BCF\u5206\u9418\u4E00\u6B21\u89F8\u767C
#"0 10,44 14 ? 3 WED"    \u4E09\u6708\u7684\u6BCF\u5468\u4E09\u768414\uFF1A10\u548C14\uFF1A44\u89F8\u767C
#"0 15 10 ? * MON-FRI"    \u6BCF\u4E2A\u5468\u4E00\u3001\u5468\u4E8C\u3001\u5468\u4E09\u3001\u5468\u56DB\u3001\u5468\u4E94\u768410\uFF1A15\u89F8\u767C

#\u88DC\u4EF6\u4E0A\u50B3\u7167\u7247\u6392\u7A0B\uFF08AIO\uFF09
SendAddPhotoJobSwitch=0
SendAddPhotoJobSchedule=20000

#\u50B3\u9001\u958B\u6236\u5F71\u50CF\u7CFB\u7D71
EOpenSendOrbitJobSwitch=0
EOpenSendOrbitJobSchedule=20000

#\u958B\u6236\u5F71\u50CF\u7CFB\u7D71\u88DC\u4EF6\uFF08\u820A\u55AE\u4E00\u7528\u820A\u7CFB\u7D71\u6392\u7A0B\uFF09
EOpenAdditionalUploadJobSwitch=0
EOpenAdditionalUploadJobSchedule=20000

SendBreakpointMsgJobSwitch=0
SendBreakpointMsgJobSchedule=0 20 9 * * ?

#\u5206\u884C\u8CC7\u6599\u540C\u6B65
SyncKgiBranchJobSwitch=0
SyncKgiBranchJobSchedule=0 0 1 2 * ?

#\u5206\u884C\u5916\u52A0\u8CC7\u8A0A\u8CC7\u6599\u540C\u6B65(\u7D93\u7DEF\u5EA6,BranchCode,\u96FB\u8A71)
#https://uat2.kgibank.com.tw/api/client/service/GetItemDescendants?itemid={9B1C8D94-4D79-4945-B6EE-D3F3AC32CCDC}&clientid={********-FC7B-4BE1-AF20-85AC73FF2967}&lang=kgib-zh-tw
SyncKgiBranchExtraInfoJobSwitch=1
SyncKgiBranchExtraInfoJobSchedule=0 0 4 * * ?
SyncKgiBranchExtraInfoJobName=SyncKgiBranchExtraInfoJob
SyncKgiBranchExtraInfoApiUrl=https://uat2.kgibank.com.tw/api/client/service/GetItemDescendants?itemid={itemid}&clientid={clientid}&lang={lang}
SyncKgiBranchExtraInfoApiItemId={9B1C8D94-4D79-4945-B6EE-D3F3AC32CCDC}
SyncKgiBranchExtraInfoApiClientId={********-FC7B-4BE1-AF20-85AC73FF2967}
SyncKgiBranchExtraInfoApiLang=kgib-zh-tw


#\u51F1\u8B49\u5206\u516C\u53F8\u8CC7\u6599\u540C\u6B65
SyncKgiBranchSECJobSwitch=0
SyncKgiBranchSECJobSchedule=0 0 1 * * ?

#\u903E\u671F\u901A\u77E5\u66F8
CloseCaseJobSwitch=1
CloseCaseJobSchedule=59 59 23 * * ?

#\u6578\u4F4D\u5E33\u6236\u7533\u8FA6\u5B8C\u6210\u63D0\u9192
SendFinishMsgJobSwitch=0
SendFinishMsgJobSchedule=0 0 12 * * ?

#SMS Hunter \u767C\u7C21\u8A0A(SMSHunterHistory)
#0:disable SMS / 1: enable SMS
SendSMSHunterJobSwitch=0
SendSMSHunterJobSchedule=20000

#Mail Hunter \u5BC4\u51FA(MailHunterHistory)
#0:disable  SendMail / 1: enable SendMail
SendMailHunterJobSwitch=0
SendMailHunterJobSchedule= */20 * * * * ?

#\u6BCF\u59291:00\u6AA2\u67E5\u300C\u9001\u51FA\u5F85\u653E\u884C\u300D\u8D85\u904E\u4E00\u5929\u4ECD\u672A\u653E\u884C\u4E4B\u689D\u6B3E\uFF0C\u5C07\u65BC\u6B21\u65E5\u91DD\u5C0D\u672A\u653E\u884C\u689D\u6B3E\u65B0\u589EEMail\u901A\u77E5
# UnapprovedTermsReminderJob/MailHistory)
UnapprovedTermsReminderJobSwitch=1
UnapprovedTermsReminderJobSchedule=0 0 1 * * ?
# 20:50
#UnapprovedTermsReminderJobSchedule=0 50 20 * * ?
# \u6BCF3mins
# UnapproveermsReminderJobSchedule=0 */3 * * * ?

# SMTP \u5BC4\u51FA(MailHistory)
# SMTP \u5BC4\u51FA(MailHistory)
SendSmtpJobSwitch=0
SendSmtpJobSchedule=20000

#\u4E2D\u58FD\u4EE3\u6536\u4ED8\u8A2D\u5B9A
CLPayerJobSwitch=0
CLPayerJobSchedule=0 0 2 * * ?

#\u4E2D\u58FD\u5FB5\u5BE9AUM\u8A2D\u5B9A
CLAumJobSwitch=0
CLAumJobSchedule=0 0 2 * * ?

#\u4E2D\u58FD\u6848\u4EF6\u72C0\u614B\u901A\u77E5\u8A2D\u5B9A
CLSendCaseStsJobSwitch=0
CLSendCaseStsJobSchedule=0 0 5 * * ?

#\u4E2D\u58FD\u6848\u4EF6\u7B49\u5F85\u884C\u52D5\u6295\u4FDD\u540C\u610F\u66F8\u4E0A\u50B3\u903E\u6642\u66F4\u65B0\u8A2D\u5B9A
CLEInsuranceConsentJobSwutch=0
CLEInsuranceConsentJobSchedule=0 0 6 * * ?

#\u4E2D\u58FD\u4E0A\u50B3FTP\u8A2D\u5B9A
CLUploadFTPJobSwutch=0
CLUploadFTPJobSchedule=0 0 1 * * ?

#\u4E2D\u58FD\u7E8C\u6263\u7533\u8FA6\u8D85\u904E120\u5929\u901A\u77E5\u8A2D\u5B9A
CLOver120JobSwitch=0
CLOver120JobSchedule=0 0 5 * * ?

#\u51F1\u8B49\u6848\u4EF6\u72C0\u614B\u901A\u77E5\u8A2D\u5B9A
KSendCaseStsJobSwitch=0
KSendCaseStsJobSchedule=0 20 11 * * MON-FRI

#\u51F1\u8B49\u6848\u4EF6\u72C0\u614B\u5373\u6642\u901A\u77E5\u8A2D\u5B9A
KSendCaseStsRTJobSwitch=0
KSendCaseStsRTJobSchedule=0 0/5 8-22 * * MON-FRI

#\u591C\u9593\u6279\u6B21\u66F4\u65B0\u4FE1\u7528\u5361APS\u72C0\u614B\u4E4B\u6279\u6B21
CcSendJobSwitch=0
CcSendJobSchedule=0 0 23 * * ?


#\u85AA\u8F49
#\u85AA\u8F49
#\u85AA\u8F49

#\u6279\u6B21\u50B3\u9001\u958B\u6236\u5F71\u50CF\u7CFB\u7D71
SALEOpenSendOrbitJobSwitch=0
SALEOpenSendOrbitJobSchedule=0 30 * * * ?

#\u5373\u6642\u50B3\u9001\u958B\u6236\u5F71\u50CF\u7CFB\u7D71
SALEOpenSendOrbitRTJobSwitch=0
SALEOpenSendOrbitRTJobSchedule=20000

SALEOpenAdditionalUploadJobSwitch=0
SALEOpenAdditionalUploadJobSchedule=20000

SALSendBreakpointMsgJobSwitch=0
SALSendBreakpointMsgJobSchedule=0 03 17 * * ?

SALGetKGICardNumJobSwitch=0
SALGetKGICardNumJobSchedule=0 48 15 * * ?

SALSendUnfinishReminderMsgJobSwitch=0
SALSendUnfinishReminderMsgJobSchedule=0 0 9 * * ?

SALApptCancelCaseOrbitJobSwitch=0
SALApptCancelCaseOrbitJobSchedule=0 1 0 * * ?

SALApptDeleteCaseOrbitJobSwitch=0
SALApptDeleteCaseOrbitJobSchedule=0 5 0 * * ?

SendAgreeTransMailJobSwitch=0
SendAgreeTransMailJobSchedule=0 1 0 * * ?

# LOAN CC PDF\u6392\u7A0B\u6642\u9593
SendPDFJobSchedule=0/30 * * * * *
SendPDFJobSwitch=0
LoanCcSendPDFJobSchedule=0/20 * * * * *
LoanCcSendPDFJobSwitch=0
LoanRejectJobSwitch=0
loanRejectJobSchedule=0 0 1 * * ?

# \u7ACB\u7D04\u6488\u53D6\u4EBA\u5DE5\u6574\u4EF6
SendContractJobSwitch = 0
LoanContractUpdate = 0 */5 * * * *
# AIO\u5E8F\u865F\u6A5F\u6392\u7A0B\u958B\u95DC
AIOSeqResetSwitch=0
AIOSeqResetSchedule=0 0 0 * * ?

# Trust\u5E8F\u865F\u6A5F\u6392\u7A0B\u958B\u95DC
TrustSeqResetSwitch=0
TrustSeqResetSchedule=0 0 0 * * ?

# Stock\u5E8F\u865F\u6A5F\u6392\u7A0B\u958B\u95DC
StockSeqResetSwitch=0
StockSeqResetSchedule=0 0 0 * * ?

# DACH\u5E8F\u865F\u6A5F\u6392\u7A0B\u958B\u95DC
DACHSeqResetSwitch=0
DACHSeqResetSchedule=0 0 0 * * ?

# AIO0\u6848\u4EF6 \u5B8C\u6210 \u95DC\u9589\u65B7\u9EDE
CloseBreakpointJobSwitch=0
CloseBreakpointJobSchedule=0 0 0 * * ?

# Workflow???????? / ????4:30??, ?? kgi.useWorkflowCleaner
ProcessInstanceHistoryCleanerSchedule=0 30 4 * * ?

# cold start JOB
ColdStartJobSchedule = 0 30 2 * * ?

LoanRejectionWaitingPeriodJobSwitch=0
LoanRejectionWaitingPeriodSchedule=0 * * * * *
