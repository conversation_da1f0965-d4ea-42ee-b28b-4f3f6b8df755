# port 設定
server:
  port: 8080

# MySQL: org.hibernate.dialect.MySQL5Dialect
# SQL Server 2019: org.hibernate.dialect.SQLServerDialect
spring.jpa.properties.hibernate.dialect-kgi: org.hibernate.dialect.SQLServer2012Dialect
spring.jpa.properties.hibernate.dialect-schedule: org.hibernate.dialect.H2Dialect

# 測試環境特定設定
ibr:
  application:
    environment: "local"
  # OTP 服務配置
  otp:
    storage: redis  # 測試環境建議使用 redis
  # SMS 服務配置
  sms:
    provider: external   # 測試環境使用真實 SMS 服務
    mock:
      delay: 0
      failure-rate: 0.0

# 允許特定位址CrossOrigin
endpoints.cors.allowed-origins: http://localhost:4000

#是否用 mock 資料 測試
kgi.useMockupData: false #true: 打 mockup 資料
kgi.useMockupCallOut: false #true: 打 mockup 電文 或是 跳過電文處理
kgi.mockupServer: false #LOCAL專用, true: 使用 mockup server, 而不用 mockup json data
kgi.mockupServer.url: http://localhost:3000
kgi.useMockupFTP: false #true:跳過FTP處理

spring:
  # PostgreSQL 測試環境資料庫配置
  datasource:
    url: ************************************************
    driverClassName: org.postgresql.Driver
    username: ${DB_USERNAME:kgi_ibr_user}
    password: ${DB_PASSWORD:sit_password}
    testOnBorrow: true
    validationQuery: SELECT 1
    hikari:
      pool-name: pool-sit
      connection-test-query: SELECT 1
      connection-timeout: 5000
      maximum-pool-size: 20
      minimum-idle: 5
  datasource-kgi:
    hikari:
      pool-name: pool-sit-kgi
      connection-test-query: SELECT 1
      maximum-pool-size: 10
      minimum-idle: 2
  jpa:
    hibernate.ddl-auto: validate  # 測試環境不自動更新結構
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  # Quartz 本地環境配置（使用記憶體）
  quartz:
    job-store-type: memory
    properties:
      org.quartz.threadPool.threadCount: 10
  # Redis 測試環境配置
  redis:
    host: sit-redis-server
    port: 6379
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 2
  # Email 測試環境配置
  mail:
    host: sit-smtp-server
    port: '587'
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:}
    properties:
      mail:
        smtp:
          auth: 'true'
          starttls:
            enable: 'true'

logging:
  level:
    com.kgi: INFO  # 測試環境降低日誌層級
    org.hibernate:
      SQL: INFO
      type.descriptor.sql.BasicBinder: INFO
