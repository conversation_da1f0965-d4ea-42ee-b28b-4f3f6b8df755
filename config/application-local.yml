# port 設定
server:
  port: 8080

# H2 資料庫 Dialect 設定
spring.jpa.properties.hibernate.dialect-kgi: org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.dialect-schedule: org.hibernate.dialect.H2Dialect

# 測試環境特定設定
ibr:
  application:
    environment: "local"
  # OTP 服務配置
  otp:
    storage: redis  # 測試環境建議使用 redis
  # SMS 服務配置
  sms:
    provider: external   # 測試環境使用真實 SMS 服務
    mock:
      delay: 0
      failure-rate: 0.0

# 允許特定位址CrossOrigin
endpoints.cors.allowed-origins: http://localhost:4000

#是否用 mock 資料 測試
kgi.useMockupData: true #true: 打 mockup 資料
kgi.useMockupCallOut: true #true: 打 mockup 電文 或是 跳過電文處理
kgi.mockupServer: true #LOCAL專用, true: 使用 mockup server, 而不用 mockup json data
kgi.mockupServer.url: http://localhost:3000
kgi.useMockupFTP: true #true:跳過FTP處理

# Repository 實作模式 (memory/jpa)
kgi.repository.type: memory  # 先測試 Memory 模式

spring:
  # H2 本地開發環境資料庫配置（使用 AUTO_SERVER 模式）
  datasource:
    # AUTO_SERVER=TRUE 自動啟動內嵌伺服器，允許多個連接同時訪問
    url: jdbc:h2:file:./db/kgi-ibr;MODE=PostgreSQL;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1
    driverClassName: org.h2.Driver
    username: sa
    password: 
    testOnBorrow: true
    validationQuery: SELECT 1
    hikari:
      pool-name: pool-local
      connection-test-query: SELECT 1
      connection-timeout: 5000
      maximum-pool-size: 10
      minimum-idle: 2
  # KGI 資料庫配置（使用 H2 AUTO_SERVER 模式）
  datasource-kgi:
    # 使用相同的 AUTO_SERVER 模式，自動處理多連接
    url: jdbc:h2:file:./db/kgi-ibr;MODE=MSSQLServer;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1
    driverClassName: org.h2.Driver
    username: sa
    password: 
    hikari:
      pool-name: pool-local-kgi
      connection-test-query: SELECT 1
      maximum-pool-size: 10
      minimum-idle: 2
  # H2 Console 配置
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  jpa:
    hibernate.ddl-auto: create-drop  # 本地開發環境重建結構
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        # 修復 H2 2.2.x 與 Hibernate 6.6.x 序列相容性問題
        globally_quoted_identifiers: false
        globally_quoted_identifiers_skip_column_definitions: true
        jdbc:
          lob:
            non_contextual_creation: true
    database-platform: org.hibernate.dialect.H2Dialect
  # Quartz 本地環境配置（使用記憶體）
  quartz:
    job-store-type: memory
    properties:
      org.quartz.threadPool.threadCount: 10
  # Redis 本地開發環境配置（使用嵌入式 Redis 或關閉）
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  # Email 本地開發環境配置（使用 Mock）
  mail:
    host: localhost
    port: '1025'  # 使用 MailDev 或其他本地 SMTP 測試工具
    username: test@localhost
    password: 
    properties:
      mail:
        smtp:
          auth: 'false'
          starttls:
            enable: 'false'

logging:
  level:
    com.kgi: DEBUG  # 本地開發環境詳細日誌
    com.kgi.module.notification: DEBUG  # 通知模組詳細日誌
    org.hibernate:
      SQL: DEBUG
      type.descriptor.sql.BasicBinder: TRACE
    org.springframework.jdbc: DEBUG
    com.zaxxer.hikari: DEBUG

# Notification 模組配置
kgi:
  notification:
    batch-size: 50           # 批次處理大小
    retry-delay: 60          # 重試延遲（分鐘）
    retry-count: 3           # 最大重試次數
    scheduler:
      enabled: true          # 啟用排程器
    # MailHunter 配置（Mock 模式下不使用）
    mailhunter:
      url: ${D3MailHunterUrl:http://************/mailhunter_api/SendNow.asmx?wsdl}
      ownerId: ${D3MailHunterOwnerID:3134}
      projectCode: ${D3MailHunterProjectCode:eOpen}
      productCode: ${D3MailHunterProductCode:S05}
      templateId: ${D3MailHunterTemplateId:7531}
      ftp:
        address: ${D3MailHunterFTPServerAddress:************}
        userid: ${D3MailHunterFTPServerUserid:mhu_ftp}
        password: ${D3MailHunterFTPServerPWD:Encryped:MXFhekBXU1g=}
    # SMS Hunter 配置（Mock 模式下不使用）
    smshunter:
      url: ${SMSHunterUrl:http://localhost:8080/sms/api}
      apiKey: ${SMSHunterApiKey:test-api-key}
