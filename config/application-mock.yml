# Mock 服務配置
# 用於開發和測試環境

kgi:
  # 啟用 Mock 資料
  useMockupData: true
  # 啟用 Mock 外部呼叫
  useMockupCallOut: true
  
  # Mock 設定
  mock:
    # 模擬延遲時間 (毫秒)
    delay:
      min: 100
      max: 500
    # 錯誤率 (0.0 - 1.0)
    errorRate: 0.05
    # Mock 資料重置頻率 (分鐘)
    resetInterval: 60

# 測試資料配置
test:
  data:
    # 預設測試使用者
    defaultUser:
      taiwanId: "A********9"
      chineseName: "王小明"
      englishName: "WANG XIAO MING"
      phone: "**********"
      email: "<EMAIL>"
      bankCode: "013"
      bankAccount: "*************"
    
    # 預設個人解款資料
    individual:
      default:
        payerName: "JOHN DOE"
        currency: "USD"
        amount: 10000.00
        exchangeRate: 31.5
        fee: 300
        payeeId: "A********9"
        payeeName: "王小明"
        payeeEngName: "WANG XIAO MING"
        payeeTel: "**********"
        payeeMail: "<EMAIL>"
        payeeBankCode: "013"
        payeeAccount: "*************"
        sourceOfFund: "01"
        remittancePurpose: "01"
    
    # 預設企業解款資料
    corporate:
      default:
        companyUnifiedNumber: "********"
        companyName: "測試企業有限公司"
        companyEngName: "TEST COMPANY LTD."
        payerName: "TEST BANK"
        currency: "EUR"
        amount: 50000.00
        exchangeRate: 34.8
        fee: 800
        bankCode: "808"
        bankAccount: "*************"
        sourceOfFund: "02"
        remittancePurpose: "02"
    
    # 補件測試資料
    supplement:
      default:
        caseNo: "SUP2025010001"
        supplementType: "01"
        deadline: 7
        requiredDocuments:
          - "身分證正反面影本"
          - "收款帳戶存摺影本"
          - "資金來源證明文件"
    
    # OTP 測試資料
    otp:
      default:
        code: "123456"
        expireMinutes: 5
        maxRetry: 3
    
    # 銀行資料
    banks:
      - code: "013"
        name: "國泰世華銀行"
        swiftCode: "UWCBTWTP"
      - code: "808"
        name: "玉山銀行"
        swiftCode: "ESUNTWTP"
      - code: "812"
        name: "台新銀行"
        swiftCode: "TSIBTWTP"
    
    # 匯率資料
    exchangeRates:
      - currency: "USD"
        buyRate: 31.3
        sellRate: 31.5
      - currency: "EUR"
        buyRate: 34.5
        sellRate: 34.8
      - currency: "JPY"
        buyRate: 0.215
        sellRate: 0.220
      - currency: "CNY"
        buyRate: 4.31
        sellRate: 4.36

# 日誌配置
logging:
  level:
    com.kgi.core.service.mock: DEBUG
    com.kgi.module.*.infrastructure.mock: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %highlight(%-5level) %cyan([MOCK]) %logger{36} - %msg%n"