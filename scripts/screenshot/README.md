# IBR 截圖工具使用指南

本工具用於自動化截取 KGI IBR 系統各模組的頁面截圖，支援自然人、法人、補件、查詢等模組的完整流程截圖。

## 目錄結構

```
screenshot/
├── README.md                    # 本說明文件
├── ibr-screenshot.js           # 主要截圖腳本
├── setup-screenshot.js         # 環境檢查與設定腳本
├── install-screenshot-deps.sh  # 依賴套件安裝腳本
└── run-screenshot.sh          # 一鍵執行腳本
```

## 快速開始

### 方法一：使用一鍵執行腳本（推薦）

```bash
# 截取所有模組
./scripts/screenshot/run-screenshot.sh

# 截取特定模組
./scripts/screenshot/run-screenshot.sh --modules=individual
```

此腳本會自動：
- 檢查並安裝必要的套件（Playwright、Chromium）
- 檢查前端開發伺服器狀態
- 執行截圖

### 方法二：手動安裝後執行

1. **安裝依賴套件**
   ```bash
   ./scripts/screenshot/install-screenshot-deps.sh
   ```

2. **啟動前端開發伺服器**（在另一個終端）
   ```bash
   cd frontend
   npm run start
   ```

3. **執行截圖**
   ```bash
   node scripts/screenshot/ibr-screenshot.js --modules=all
   ```

## 詳細使用說明

### 截圖腳本使用方式

```bash
node scripts/screenshot/ibr-screenshot.js --modules=<模組列表>
```

#### 參數說明

- `--modules`: 指定要截圖的模組，可選值：
  - `individual` - 自然人模組（6個頁面）
  - `corporate` - 法人模組（6個頁面）
  - `supplement` - 補件模組（3個頁面）
  - `query` - 查詢模組（3個頁面）
  - `all` - 所有模組

#### 使用範例

```bash
# 截取單一模組
node scripts/screenshot/ibr-screenshot.js --modules=individual

# 截取多個模組（逗號分隔）
node scripts/screenshot/ibr-screenshot.js --modules=individual,corporate

# 截取所有模組
node scripts/screenshot/ibr-screenshot.js --modules=all

# 顯示幫助資訊
node scripts/screenshot/ibr-screenshot.js --help
```

### 輸出位置

截圖會儲存在專案根目錄的 `screenshots` 資料夾中：

```
/Users/<USER>/workspace/_kgi/new/kgi-IBR/screenshots/
├── individual/
│   ├── step1-landing.png
│   ├── step2-identity-selection.png
│   ├── step3-identity-verification.png
│   ├── step4-remittance-detail.png
│   ├── step5-amount-confirmation.png
│   └── step6-transaction-confirmation.png
├── corporate/
│   ├── step1-landing.png
│   ├── step2-company-info.png
│   ├── step3-certificate-verification.png
│   ├── step4-remittance-detail.png
│   ├── step5-amount-confirmation.png
│   └── step6-application-complete.png
├── supplement/
│   ├── step1-notification.png
│   ├── step2-remittance-supplement.png
│   └── step3-complete.png
└── query/
    ├── step1-query.png
    ├── step2-verification.png
    └── step3-result.png
```

## 環境需求

### 系統需求
- Node.js 14.0 或以上版本
- npm 套件管理器
- macOS 10.14+ / Windows 10+ / Ubuntu 18.04+

### 必要套件
- **Playwright**: 自動化測試框架
- **Chromium**: 瀏覽器引擎

### 前端開發伺服器
- 必須在 `http://localhost:4000` 運行
- 執行指令：`cd frontend && npm run start`

## 進階設定

### 視窗大小配置

每個模組使用不同的視窗高度以確保內容完整顯示：
- 自然人：1920x2000
- 法人：1920x2500
- 補件：1920x2500
- 查詢：1920x2000

如需調整，可修改 `ibr-screenshot.js` 中的 `MODULES_CONFIG`。

### 執行選項

腳本預設使用以下設定：
- `headless: false` - 顯示瀏覽器視窗（可觀察執行過程）
- `fullPage: true` - 截取整個頁面
- `animations: 'disabled'` - 禁用動畫以獲得穩定截圖

## 疑難排解

### 常見問題

1. **Playwright 未安裝**
   ```bash
   npm install playwright
   ```

2. **Chromium 瀏覽器未安裝**
   ```bash
   npx playwright install chromium
   ```

3. **前端伺服器未運行**
   ```bash
   cd frontend
   npm run start
   ```

4. **截圖內容被截斷**
   - 檢查模組的 viewport 設定
   - 可能需要增加視窗高度

5. **無法點擊導航連結**
   - 腳本會自動 fallback 到直接導航
   - 不影響截圖結果

### 檢查環境

使用設定腳本檢查環境：
```bash
node scripts/screenshot/setup-screenshot.js
```

## 更新紀錄

- **2025-06-12**: 初始版本
  - 支援四大模組截圖
  - 自動隱藏側邊欄
  - 模組化視窗高度設定
  - 相對路徑輸出
