#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log(`
===========================================
📦 IBR 截圖工具環境設定腳本
===========================================
`);

// 顏色輸出
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

// 輔助函數
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`\n🔧 ${description}...`, 'blue');
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} 完成`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} 失敗`, 'red');
    return false;
  }
}

// 檢查函數
function checkNodeVersion() {
  log('\n🔍 檢查 Node.js 版本...', 'blue');
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.split('.')[0].substring(1));
  
  if (majorVersion >= 14) {
    log(`✅ Node.js 版本 ${nodeVersion} 符合要求 (需要 >= 14.0)`, 'green');
    return true;
  } else {
    log(`❌ Node.js 版本 ${nodeVersion} 太舊 (需要 >= 14.0)`, 'red');
    return false;
  }
}

function checkPlaywright() {
  log('\n🔍 檢查 Playwright 是否已安裝...', 'blue');
  
  try {
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    if (packageJson.dependencies && packageJson.dependencies.playwright) {
      log('✅ Playwright 已在 package.json 中', 'green');
      return true;
    }
    
    // 檢查 node_modules
    const playwrightPath = path.join(__dirname, '..', 'node_modules', 'playwright');
    if (fs.existsSync(playwrightPath)) {
      log('✅ Playwright 已安裝在 node_modules', 'green');
      return true;
    }
  } catch (error) {
    // 忽略錯誤，繼續檢查
  }
  
  log('⚠️  Playwright 未安裝', 'yellow');
  return false;
}

function checkChromium() {
  log('\n🔍 檢查 Chromium 瀏覽器是否已安裝...', 'blue');
  
  try {
    // 檢查 Playwright 瀏覽器是否已安裝
    execSync('npx playwright --version', { stdio: 'pipe' });
    
    // 嘗試列出已安裝的瀏覽器
    const result = execSync('npx playwright install --help', { encoding: 'utf8' });
    
    // 簡單檢查 chromium 是否在輸出中
    if (result.includes('chromium')) {
      // 進一步檢查 chromium 是否真的已安裝
      try {
        const testScript = `
          const { chromium } = require('playwright');
          chromium.executablePath();
        `;
        execSync(`node -e "${testScript}"`, { stdio: 'pipe' });
        log('✅ Chromium 瀏覽器已安裝', 'green');
        return true;
      } catch (e) {
        log('⚠️  Chromium 瀏覽器未安裝', 'yellow');
        return false;
      }
    }
  } catch (error) {
    log('⚠️  無法確認 Chromium 狀態', 'yellow');
    return false;
  }
  
  return false;
}

function checkFrontendServer() {
  log('\n🔍 檢查前端開發伺服器...', 'blue');
  
  try {
    execSync('curl -s -o /dev/null -w "%{http_code}" http://localhost:4000', { stdio: 'pipe' });
    log('✅ 前端開發伺服器正在運行 (http://localhost:4000)', 'green');
    return true;
  } catch (error) {
    log('⚠️  前端開發伺服器未運行', 'yellow');
    log('   請在另一個終端執行: cd frontend && npm run start', 'yellow');
    return false;
  }
}

// 主要流程
async function main() {
  let allChecked = true;
  
  // 1. 檢查 Node.js 版本
  if (!checkNodeVersion()) {
    log('\n請先升級 Node.js 到 14.0 或以上版本', 'red');
    process.exit(1);
  }
  
  // 2. 檢查並安裝 Playwright
  if (!checkPlaywright()) {
    const answer = await prompt('\n是否要安裝 Playwright? (y/n): ');
    if (answer.toLowerCase() === 'y') {
      if (!execCommand('npm install playwright', '安裝 Playwright')) {
        allChecked = false;
      }
    } else {
      allChecked = false;
    }
  }
  
  // 3. 檢查並安裝 Chromium
  if (!checkChromium()) {
    const answer = await prompt('\n是否要安裝 Chromium 瀏覽器? (y/n): ');
    if (answer.toLowerCase() === 'y') {
      if (!execCommand('npx playwright install chromium', '安裝 Chromium 瀏覽器')) {
        allChecked = false;
      }
    } else {
      allChecked = false;
    }
  }
  
  // 4. 檢查前端伺服器
  checkFrontendServer();
  
  // 總結
  console.log('\n' + '='.repeat(43));
  
  if (allChecked) {
    log('✅ 所有必要套件都已安裝！', 'green');
    log('\n您現在可以執行截圖腳本：', 'green');
    log('  node scripts/ibr-screenshot.js --modules=all', 'blue');
  } else {
    log('⚠️  部分套件未安裝或安裝失敗', 'yellow');
    log('\n請手動安裝缺少的套件：', 'yellow');
    log('  npm install playwright', 'blue');
    log('  npx playwright install chromium', 'blue');
  }
  
  console.log('='.repeat(43) + '\n');
}

// 簡單的命令行輸入函數
function prompt(question) {
  return new Promise((resolve) => {
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    readline.question(question, (answer) => {
      readline.close();
      resolve(answer);
    });
  });
}

// 執行主程式
main().catch(error => {
  log(`\n❌ 發生錯誤: ${error.message}`, 'red');
  process.exit(1);
});