#!/bin/bash

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"

echo "==========================================="
echo "🚀 IBR 截圖工具一鍵執行腳本"
echo "==========================================="
echo ""

# 切換到專案根目錄
cd "$PROJECT_ROOT"

# 檢查 Playwright 是否已安裝
echo -e "${BLUE}🔍 檢查 Playwright...${NC}"
if [ ! -d "node_modules/playwright" ]; then
    echo -e "${YELLOW}⚠️  Playwright 未安裝，開始安裝...${NC}"
    npm install playwright
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Playwright 安裝失敗${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Playwright 已安裝${NC}"
fi

# 檢查 Chromium 是否已安裝
echo -e "${BLUE}🔍 檢查 Chromium 瀏覽器...${NC}"
if ! npx playwright install --dry-run chromium 2>&1 | grep -q "already downloaded"; then
    echo -e "${YELLOW}⚠️  Chromium 未安裝，開始安裝...${NC}"
    npx playwright install chromium
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Chromium 安裝失敗${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Chromium 已安裝${NC}"
fi

# 檢查前端伺服器
echo -e "${BLUE}🔍 檢查前端開發伺服器...${NC}"
if ! curl -s -f http://localhost:4000 > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  前端開發伺服器未運行${NC}"
    echo -e "${YELLOW}請在另一個終端執行: cd frontend && npm run start${NC}"
    echo ""
    read -p "是否繼續執行截圖？(y/n): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo -e "${GREEN}✅ 前端開發伺服器正在運行${NC}"
fi

echo ""
echo -e "${BLUE}📸 開始執行截圖...${NC}"
echo ""

# 執行截圖腳本
if [ "$1" ]; then
    # 如果有參數，使用提供的參數
    node scripts/screenshot/ibr-screenshot.js "$@"
else
    # 預設截取所有模組
    node scripts/screenshot/ibr-screenshot.js --modules=all
fi

echo ""
echo -e "${GREEN}✨ 完成！${NC}"