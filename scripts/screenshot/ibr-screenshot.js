const { chromium } = require('playwright');
const path = require('path');
const fs = require('fs');

// 基礎配置
const CONFIG = {
  baseUrl: 'http://localhost:4000',
  outputBaseDir: path.join(__dirname, '..', '..', 'screenshots'), // 使用相對路徑
  viewport: { width: 1920, height: 2000 }, // 增加視窗高度以容納更多內容
  waitTime: 2000,
  // 額外的頁面配置，確保內容不被截斷
  pageConfig: {
    deviceScaleFactor: 1,
    hasTouch: false,
    isMobile: false
  }
};

// 模組配置
const MODULES_CONFIG = {
  individual: {
    name: '自然人',
    navTitle: 'Individual',
    viewport: { width: 1920, height: 2000 }, // 自然人模組使用目前的設定
    pages: [
      { name: 'step1-landing', path: '/ibr/individual/landing', description: '條款同意頁面' },
      { name: 'step2-identity-selection', path: '/ibr/individual/identity-selection', description: '選擇驗證方式' },
      { name: 'step3-identity-verification', path: '/ibr/individual/identity-verification', description: '身份驗證' },
      { name: 'step4-remittance-detail', path: '/ibr/individual/remittance-detail', description: '匯款詳情確認' },
      { name: 'step5-amount-confirmation', path: '/ibr/individual/amount-confirmation', description: '金額確認' },
      { name: 'step6-transaction-confirmation', path: '/ibr/individual/transaction-confirmation', description: '申請完成' }
    ]
  },
  corporate: {
    name: '法人',
    navTitle: 'Corporate',
    viewport: { width: 1920, height: 2500 }, // 法人模組可能需要更高的視窗
    pages: [
      { name: 'step1-landing', path: '/ibr/corporate/landing', description: '企業條款同意' },
      { name: 'step2-company-info', path: '/ibr/corporate/company-info', description: '法人驗證資料輸入' },
      { name: 'step3-certificate-verification', path: '/ibr/corporate/certificate-verification', description: '工商憑證驗證' },
      { name: 'step4-remittance-detail', path: '/ibr/corporate/remittance-detail', description: '匯款確認頁' },
      { name: 'step5-amount-confirmation', path: '/ibr/corporate/amount-confirmation', description: '金額確認頁' },
      { name: 'step6-application-complete', path: '/ibr/corporate/application-complete', description: '申請完成頁' }
    ]
  },
  supplement: {
    name: '補件',
    navTitle: 'Supplement',
    viewport: { width: 1920, height: 2500 }, // 補件模組也可能需要更高的視窗
    pages: [
      { name: 'step1-notification', path: '/ibr/supplement/notification', description: '匯入匯款通知' },
      { name: 'step2-remittance-supplement', path: '/ibr/supplement/remittance-supplement', description: '線上外匯解款補件' },
      { name: 'step3-complete', path: '/ibr/supplement/complete', description: '補件完成' }
    ]
  },
  query: {
    name: '查詢',
    navTitle: 'Query',
    viewport: { width: 1920, height: 2000 }, // 查詢模組使用標準高度
    pages: [
      { name: 'step1-query', path: '/ibr/query', description: '查詢輸入頁' },
      { name: 'step2-verification', path: '/ibr/query/verification', description: '簡訊驗證頁' },
      { name: 'step3-result', path: '/ibr/query/result', description: '查詢結果頁' }
    ]
  }
};

// 解析命令行參數
function parseArgs() {
  const args = process.argv.slice(2);
  const modulesArg = args.find(arg => arg.startsWith('--modules='));

  if (!modulesArg) {
    console.log(`
❌ 錯誤：請指定要截圖的模組

使用方式：
  node ibr-screenshot.js --modules=<模組列表>

範例：
  node ibr-screenshot.js --modules=individual
  node ibr-screenshot.js --modules=individual,corporate
  node ibr-screenshot.js --modules=all

可用模組：
  - individual (自然人)
  - corporate (法人)
  - supplement (補件)
  - query (查詢)
  - all (所有模組)
    `);
    process.exit(1);
  }

  const modulesValue = modulesArg.split('=')[1];
  if (modulesValue === 'all') {
    return Object.keys(MODULES_CONFIG);
  }

  const selectedModules = modulesValue.split(',').filter(m => MODULES_CONFIG[m]);
  if (selectedModules.length === 0) {
    console.error('❌ 錯誤：未找到有效的模組');
    process.exit(1);
  }

  return selectedModules;
}

// 確保目錄存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 建立目錄: ${dirPath}`);
  }
}

// 展開導航區塊
async function expandNavSection(page, navTitle) {
  try {
    await page.evaluate((title) => {
      const navTitles = document.querySelectorAll('.nav-title');
      for (const nav of navTitles) {
        if (nav.textContent.includes(title)) {
          const arrow = nav.querySelector('.arrow');
          if (arrow && arrow.textContent === '▶') {
            nav.click();
          }
          break;
        }
      }
    }, navTitle);
    await page.waitForTimeout(500);
  } catch (e) {
    console.log(`  ⚠️  無法展開 ${navTitle} 區塊`);
  }
}

// 導航到指定頁面
async function navigateToPage(page, path) {
  try {
    // 嘗試點擊導航連結
    const linkSelector = `a[href="${path}"]`;
    await page.click(linkSelector, { timeout: 3000 });
    await page.waitForLoadState('networkidle');
  } catch {
    // 如果點擊失敗，直接導航
    console.log(`  ⚠️  無法點擊連結，直接導航到 ${path}`);
    await page.goto(`${CONFIG.baseUrl}${path}`, { waitUntil: 'networkidle' });
  }
}

// 主要截圖函數
async function captureScreenshots(selectedModules) {
  console.log(`
===========================================
🎯 IBR 模組化截圖工具
===========================================
選定的模組：${selectedModules.map(m => MODULES_CONFIG[m].name).join(', ')}
輸出目錄：${CONFIG.outputBaseDir}
===========================================
  `);

  // 確保基礎輸出目錄存在
  ensureDirectoryExists(CONFIG.outputBaseDir);

  // 啟動瀏覽器
  const browser = await chromium.launch({
    headless: false // 設為 false 可以看到瀏覽器操作過程
  });

  try {
    const context = await browser.newContext({
      viewport: CONFIG.viewport,
      ...CONFIG.pageConfig
    });
    const page = await context.newPage();

    // 導航到首頁
    console.log('📍 導航到首頁...');
    await page.goto(`${CONFIG.baseUrl}/ibr`, { waitUntil: 'networkidle' });
    await page.waitForTimeout(2000);

    // 隱藏 sidebar
    console.log('🔧 隱藏側邊欄...');
    try {
      await page.click('.toggle-btn');
      console.log('✅ 成功隱藏側邊欄\n');
      await page.waitForTimeout(1000);
    } catch (e) {
      console.log('⚠️  無法隱藏側邊欄，繼續執行...\n');
    }

    // 處理每個選定的模組
    for (const moduleKey of selectedModules) {
      const module = MODULES_CONFIG[moduleKey];
      console.log(`📦 處理模組：${module.name} (${module.pages.length} 個頁面)`);
      console.log('─'.repeat(50));

      // 如果模組有特定的視窗設定，則調整視窗大小
      if (module.viewport) {
        console.log(`  📐 調整視窗大小: ${module.viewport.width}x${module.viewport.height}`);
        await page.setViewportSize(module.viewport);
        await page.waitForTimeout(1000);
      }

      // 建立模組輸出目錄
      const outputDir = path.join(CONFIG.outputBaseDir, moduleKey);
      ensureDirectoryExists(outputDir);

      // 展開對應的導航區塊
      await expandNavSection(page, module.navTitle);

      // 截取該模組的所有頁面
      for (let i = 0; i < module.pages.length; i++) {
        const pageConfig = module.pages[i];
        console.log(`  [${i + 1}/${module.pages.length}] 📸 ${pageConfig.description}`);

        try {
          // 導航到頁面
          await navigateToPage(page, pageConfig.path);
          await page.waitForTimeout(CONFIG.waitTime);

          // 確保頁面完全載入
          await page.waitForLoadState('domcontentloaded');
          await page.waitForLoadState('load');

          // 調整頁面縮放和視窗，確保內容完整顯示
          await page.evaluate(() => {
            // 設置頁面縮放
            document.body.style.zoom = '1'; // 保持原始大小

            // 確保主要內容區域完整顯示
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
              mainContent.style.width = '100%';
              mainContent.style.maxWidth = '100%';
              mainContent.style.overflow = 'visible';
            }

            // 如果有固定定位的元素，調整它們
            const fixedElements = document.querySelectorAll('[style*="position: fixed"]');
            fixedElements.forEach(el => {
              el.style.position = 'absolute';
            });
          });

          // 滾動到底部再回到頂部，確保所有內容載入
          await page.evaluate(() => {
            return new Promise((resolve) => {
              // 滾動到底部
              window.scrollTo(0, document.body.scrollHeight);
              setTimeout(() => {
                // 滾動回頂部
                window.scrollTo(0, 0);
                setTimeout(resolve, 500);
              }, 1000);
            });
          });

          // 等待額外時間確保所有元素載入
          await page.waitForTimeout(1500);

          // 截圖 - 使用 fullPage 確保截取整個頁面
          const screenshotPath = path.join(outputDir, `${pageConfig.name}.png`);
          await page.screenshot({
            path: screenshotPath,
            fullPage: true,
            animations: 'disabled' // 禁用動畫以獲得更穩定的截圖
          });

          console.log(`      ✅ 已儲存: ${pageConfig.name}.png`);
        } catch (error) {
          console.error(`      ❌ 錯誤: ${error.message}`);
        }
      }

      console.log(`✅ ${module.name}模組截圖完成\n`);
    }

  } catch (error) {
    console.error('❌ 執行過程中發生錯誤:', error);
  } finally {
    // 關閉瀏覽器
    await browser.close();
    console.log(`
===========================================
✨ 截圖任務完成！
===========================================
輸出位置：${CONFIG.outputBaseDir}
    `);
  }
}

// 顯示幫助信息
function showHelp() {
  console.log(`
===========================================
IBR 模組化截圖工具
===========================================

使用方式：
  node ibr-screenshot.js --modules=<模組列表>

範例：
  node ibr-screenshot.js --modules=individual                # 只截取自然人模組
  node ibr-screenshot.js --modules=individual,corporate      # 截取自然人和法人
  node ibr-screenshot.js --modules=supplement,query          # 截取補件和查詢
  node ibr-screenshot.js --modules=all                       # 截取所有模組

可用模組：
  - individual : 自然人 (6個頁面)
  - corporate  : 法人 (6個頁面)
  - supplement : 補件 (3個頁面)
  - query      : 查詢 (3個頁面)

輸出目錄：
  ${CONFIG.outputBaseDir}/<模組名稱>/

注意事項：
  1. 確保前端開發伺服器運行在 ${CONFIG.baseUrl}
  2. 需要先安裝 playwright: npm install playwright
  3. 需要安裝瀏覽器: npx playwright install chromium
===========================================
  `);
}

// 主程式入口
async function main() {
  // 檢查是否需要顯示幫助
  if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    return;
  }

  // 解析參數並執行
  const selectedModules = parseArgs();
  await captureScreenshots(selectedModules);
}

// 執行主程式
main().catch(console.error);
