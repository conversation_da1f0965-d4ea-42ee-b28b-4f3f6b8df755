#!/bin/bash

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "==========================================="
echo "📦 IBR 截圖工具依賴安裝腳本"
echo "==========================================="
echo ""

# 檢查 Node.js
echo -e "${BLUE}🔍 檢查 Node.js...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js 已安裝: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js 未安裝，請先安裝 Node.js${NC}"
    exit 1
fi

# 檢查 npm
echo -e "${BLUE}🔍 檢查 npm...${NC}"
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm -v)
    echo -e "${GREEN}✅ npm 已安裝: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm 未安裝${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}開始安裝必要套件...${NC}"
echo ""

# 安裝 Playwright
echo -e "${BLUE}📦 安裝 Playwright...${NC}"
npm install playwright
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Playwright 安裝成功${NC}"
else
    echo -e "${RED}❌ Playwright 安裝失敗${NC}"
    exit 1
fi

echo ""

# 安裝 Chromium 瀏覽器
echo -e "${BLUE}🌐 安裝 Chromium 瀏覽器...${NC}"
npx playwright install chromium
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Chromium 瀏覽器安裝成功${NC}"
else
    echo -e "${RED}❌ Chromium 瀏覽器安裝失敗${NC}"
    exit 1
fi

echo ""
echo "==========================================="
echo -e "${GREEN}✅ 所有依賴安裝完成！${NC}"
echo ""
echo -e "${YELLOW}提醒事項：${NC}"
echo "1. 請確保前端開發伺服器正在運行："
echo "   cd frontend && npm run start"
echo ""
echo "2. 執行截圖腳本："
echo "   node scripts/ibr-screenshot.js --modules=all"
echo "==========================================="