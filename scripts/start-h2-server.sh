#!/bin/bash
# H2 Database Server 啟動腳本
# 允許多個應用程式同時連接到 H2 資料庫

echo "Starting H2 Database Server..."

# 設定 H2 版本（與 build.gradle 中的版本一致）
H2_VERSION="2.2.224"

# 設定 Java
export JAVA_HOME=${JAVA_HOME:-/Users/<USER>/Library/Java/JavaVirtualMachines/openjdk-17/Contents/Home}
export PATH=$JAVA_HOME/bin:$PATH

# 建立資料庫目錄
mkdir -p ./db

# 檢查是否已有 H2 jar
H2_JAR="./lib/h2-${H2_VERSION}.jar"
if [ ! -f "$H2_JAR" ]; then
    echo "H2 JAR not found. Downloading..."
    mkdir -p ./lib
    curl -L "https://repo1.maven.org/maven2/com/h2database/h2/${H2_VERSION}/h2-${H2_VERSION}.jar" -o "$H2_JAR"
fi

# 啟動 H2 Server
# -tcp: 啟用 TCP Server (預設端口 9092)
# -tcpAllowOthers: 允許其他主機連接
# -tcpPort: 指定 TCP 端口
# -web: 啟用 Web Console (預設端口 8082)
# -webAllowOthers: 允許其他主機訪問 Web Console
# -webPort: 指定 Web Console 端口
# -baseDir: 指定資料庫檔案的基礎目錄
java -cp "$H2_JAR" org.h2.tools.Server \
    -tcp -tcpAllowOthers -tcpPort 9092 \
    -web -webAllowOthers -webPort 8082 \
    -baseDir ./db \
    -ifNotExists

# 說明：
# TCP Server URL: jdbc:h2:tcp://localhost:9092/./kgi-ibr
# Web Console URL: http://localhost:8082
# 
# 在 Web Console 中連接資料庫：
# JDBC URL: jdbc:h2:tcp://localhost:9092/./kgi-ibr
# User Name: sa
# Password: (留空)