#!/bin/bash
# 停止所有 KGI IBR 相關的 Java 進程

echo "Stopping all KGI IBR Java processes..."

# 查找並停止 Spring Boot 應用
echo "Looking for Spring Boot processes..."
ps aux | grep -E 'java.*kgi-ibr|KgiApplication' | grep -v grep | awk '{print $2}' | while read pid; do
    echo "Stopping process $pid"
    kill -15 $pid 2>/dev/null || true
done

# 等待進程優雅關閉
sleep 2

# 強制關閉仍在運行的進程
ps aux | grep -E 'java.*kgi-ibr|KgiApplication' | grep -v grep | awk '{print $2}' | while read pid; do
    echo "Force stopping process $pid"
    kill -9 $pid 2>/dev/null || true
done

# 查找並停止 H2 Server
echo "Looking for H2 Server processes..."
ps aux | grep 'org.h2.tools.Server' | grep -v grep | awk '{print $2}' | while read pid; do
    echo "Stopping H2 Server process $pid"
    kill -15 $pid 2>/dev/null || true
done

# 清理鎖定檔案（如果存在）
if [ -f "./db/kgi-ibr.lock.db" ]; then
    echo "Removing H2 lock file..."
    rm -f ./db/kgi-ibr.lock.db
fi

echo "All processes stopped."
echo ""
echo "To restart the application:"
echo "1. ./gradlew :ibr-backend:bootRun"
echo "   OR"
echo "2. ./scripts/start-h2-server.sh (if using Server mode)"