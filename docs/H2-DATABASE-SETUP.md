# H2 資料庫設定指南

## 問題說明
當多個應用程式或進程嘗試連接同一個 H2 資料庫檔案時，會出現以下錯誤：
```
Database may be already in use: "/Users/<USER>/workspace/_kgi/new/kgi-IBR/db/kgi-ibr.mv.db"
```

## 解決方案

### 方案 1：使用 AUTO_SERVER 模式（推薦）

在 `application.yml` 或 `application-local.yml` 中配置：

```yaml
spring:
  datasource:
    url: jdbc:h2:file:./db/kgi-ibr;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
```

**優點**：
- 自動啟動內嵌的 H2 Server
- 允許多個進程同時連接
- 不需要額外啟動 H2 Server
- 配置簡單

**使用方式**：
1. 確保所有連接都使用相同的 URL 格式（包含 AUTO_SERVER=TRUE）
2. 第一個連接會自動啟動 Server 模式
3. 後續連接會自動連接到已啟動的 Server

### 方案 2：使用獨立的 H2 Server

1. **啟動 H2 Server**：
   ```bash
   cd /Users/<USER>/workspace/_kgi/new/kgi-IBR
   ./scripts/start-h2-server.sh
   ```

2. **修改資料庫連接配置**：
   ```yaml
   spring:
     datasource:
       url: jdbc:h2:tcp://localhost:9092/./db/kgi-ibr
       username: sa
       password: 
   ```

**優點**：
- 更適合生產環境
- 可以遠程連接
- 提供 Web Console (http://localhost:8082)

### 方案 3：關閉其他連接

如果只需要單一連接，可以：

1. **找出並關閉佔用資料庫的進程**：
   ```bash
   # 查找 Java 進程
   ps aux | grep java | grep kgi-ibr
   
   # 關閉進程
   kill -9 <PID>
   ```

2. **刪除鎖定檔案**（謹慎使用）：
   ```bash
   rm ./db/kgi-ibr.lock.db
   ```

## H2 Console 訪問

### 內嵌模式
- URL: http://localhost:8080/h2-console
- JDBC URL: `jdbc:h2:file:./db/kgi-ibr;AUTO_SERVER=TRUE`
- User Name: `sa`
- Password: (留空)

### Server 模式
- URL: http://localhost:8082
- JDBC URL: `jdbc:h2:tcp://localhost:9092/./db/kgi-ibr`
- User Name: `sa`
- Password: (留空)

## 資料庫檔案位置

資料庫檔案存儲在：
```
/Users/<USER>/workspace/_kgi/new/kgi-IBR/db/
├── kgi-ibr.mv.db    # 主要資料檔案
├── kgi-ibr.lock.db  # 鎖定檔案（使用中才存在）
└── kgi-ibr.trace.db # 追蹤/日誌檔案
```

## 建議配置

對於開發環境，建議使用 **AUTO_SERVER** 模式，已在主要的 `application.yml` 中配置完成。

## 注意事項

1. **MODE 設定**：
   - `MODE=PostgreSQL`：模擬 PostgreSQL 語法
   - `MODE=MSSQLServer`：模擬 SQL Server 語法
   - 可根據需要選擇適合的模式

2. **資料持久性**：
   - `DB_CLOSE_DELAY=-1`：保持資料庫開啟
   - `DB_CLOSE_ON_EXIT=FALSE`：JVM 關閉時不關閉資料庫

3. **效能優化**：
   - 使用 `CACHE_SIZE` 增加快取
   - 使用 `COMPRESS` 壓縮資料

## 故障排除

如果仍然遇到連接問題：

1. 確認沒有其他應用程式使用相同的資料庫檔案
2. 檢查檔案權限
3. 嘗試使用絕對路徑而非相對路徑
4. 查看 `kgi-ibr.trace.db` 檔案中的錯誤日誌