import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout, map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 銀行資訊介面
 */
export interface BankInfo {
  /** 銀行代碼 */
  bankCode: string;
  /** 銀行名稱 */
  bankName: string;
  /** 銀行英文名稱 */
  bankNameEn?: string;
  /** 銀行簡稱 */
  bankShortName?: string;
  /** 銀行類型 */
  bankType: 'COMMERCIAL' | 'CREDIT_UNION' | 'FOREIGN' | 'INVESTMENT' | 'POSTAL' | 'OTHER';
  /** 是否啟用 */
  isActive: boolean;
  /** 支援的幣別 */
  supportedCurrencies: string[];
  /** 支援的服務 */
  supportedServices: {
    serviceCode: string;
    serviceName: string;
    isAvailable: boolean;
    fee?: number;
    description?: string;
  }[];
  /** 營業時間 */
  businessHours?: {
    weekdays: string;
    weekends: string;
    holidays: string;
  };
  /** 聯絡資訊 */
  contactInfo?: {
    phone: string;
    website: string;
    customerService: string;
  };
  /** SWIFT代碼 */
  swiftCode?: string;
  /** 總行地址 */
  headquarterAddress?: string;
  /** 最後更新時間 */
  lastUpdated: Date;
}

/**
 * 銀行分行資訊介面
 */
export interface BankBranchInfo {
  /** 分行代碼 */
  branchCode: string;
  /** 分行名稱 */
  branchName: string;
  /** 銀行代碼 */
  bankCode: string;
  /** 銀行名稱 */
  bankName: string;
  /** 分行地址 */
  address: string;
  /** 分行電話 */
  phone: string;
  /** 分行傳真 */
  fax?: string;
  /** 是否啟用 */
  isActive: boolean;
  /** 支援的服務 */
  availableServices: string[];
  /** 營業時間 */
  businessHours: {
    weekdays: string;
    saturdays: string;
    sundays: string;
    holidays: string;
  };
  /** 地理位置 */
  location?: {
    latitude: number;
    longitude: number;
    city: string;
    district: string;
  };
  /** ATM資訊 */
  atmInfo?: {
    count: number;
    is24Hours: boolean;
    supportedCards: string[];
  };
}

/**
 * 銀行查詢請求介面
 */
export interface BankQueryRequest {
  /** 搜尋關鍵字 */
  keyword?: string;
  /** 銀行類型篩選 */
  bankTypes?: string[];
  /** 支援幣別篩選 */
  supportedCurrencies?: string[];
  /** 支援服務篩選 */
  requiredServices?: string[];
  /** 僅顯示啟用的銀行 */
  activeOnly?: boolean;
  /** 排序方式 */
  sortBy?: 'bankName' | 'bankCode' | 'lastUpdated';
  /** 排序方向 */
  sortDirection?: 'ASC' | 'DESC';
  /** 分頁設定 */
  pagination?: {
    page: number;
    pageSize: number;
  };
}

/**
 * 銀行查詢回應介面
 */
export interface BankQueryResponse {
  /** 銀行列表 */
  banks: BankInfo[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
  /** 查詢時間 */
  queryTime: Date;
}

/**
 * 分行查詢請求介面
 */
export interface BranchQueryRequest {
  /** 銀行代碼 */
  bankCode?: string;
  /** 城市 */
  city?: string;
  /** 區域 */
  district?: string;
  /** 搜尋關鍵字 */
  keyword?: string;
  /** 需要的服務 */
  requiredServices?: string[];
  /** 僅顯示啟用的分行 */
  activeOnly?: boolean;
  /** 地理位置搜尋 */
  locationSearch?: {
    latitude: number;
    longitude: number;
    radius: number; // 搜尋半徑（公里）
  };
  /** 分頁設定 */
  pagination?: {
    page: number;
    pageSize: number;
  };
}

/**
 * 分行查詢回應介面
 */
export interface BranchQueryResponse {
  /** 分行列表 */
  branches: BankBranchInfo[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
  /** 查詢時間 */
  queryTime: Date;
}

/**
 * 銀行帳戶驗證請求介面
 */
export interface BankAccountValidationRequest {
  /** 銀行代碼 */
  bankCode: string;
  /** 分行代碼 */
  branchCode?: string;
  /** 帳戶號碼 */
  accountNumber: string;
  /** 帳戶名稱 */
  accountName: string;
  /** 驗證類型 */
  validationType: 'FORMAT' | 'EXISTENCE' | 'OWNERSHIP';
  /** 額外驗證資料 */
  additionalData?: {
    /** 身份證號 */
    idNumber?: string;
    /** 統一編號 */
    unifiedNumber?: string;
    /** 生日 */
    birthDate?: Date;
  };
}

/**
 * 銀行帳戶驗證回應介面
 */
export interface BankAccountValidationResponse {
  /** 驗證結果 */
  isValid: boolean;
  /** 驗證類型 */
  validationType: string;
  /** 驗證詳細結果 */
  validationDetails: {
    /** 格式驗證 */
    formatValid: boolean;
    /** 帳戶存在驗證 */
    accountExists?: boolean;
    /** 帳戶名稱比對 */
    nameMatch?: boolean;
    /** 身份驗證 */
    identityMatch?: boolean;
  };
  /** 帳戶資訊 */
  accountInfo?: {
    /** 帳戶狀態 */
    accountStatus: 'ACTIVE' | 'INACTIVE' | 'FROZEN' | 'CLOSED';
    /** 帳戶類型 */
    accountType: 'SAVINGS' | 'CHECKING' | 'TIME_DEPOSIT' | 'FOREIGN_CURRENCY' | 'OTHER';
    /** 開戶日期 */
    openDate?: Date;
    /** 幣別 */
    currency: string;
  };
  /** 錯誤訊息 */
  errorMessage?: string;
  /** 驗證時間 */
  validationTime: Date;
}

/**
 * 法人模組銀行列表API服務
 * 
 * 提供完整的銀行相關API功能，包括：
 * - 銀行列表查詢與搜尋
 * - 銀行分行資訊查詢
 * - 銀行帳戶驗證
 * - 銀行服務資訊查詢
 * 
 * @example
 * ```typescript
 * constructor(private bankListApi: BankListApiService) {}
 * 
 * async loadBankList() {
 *   try {
 *     const result = await this.bankListApi.queryBanks({
 *       activeOnly: true,
 *       sortBy: 'bankName'
 *     }).toPromise();
 *     
 *     this.banks = result.banks;
 *     console.log('載入銀行列表成功', result.totalCount);
 *   } catch (error) {
 *     console.error('載入銀行列表失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class BankListApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/v1/corporate/bank`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2; // 重試次數

  constructor(private http: HttpClient) {}

  /**
   * 查詢銀行列表
   * 
   * @param request 銀行查詢請求
   * @returns 銀行列表Observable
   */
  queryBanks(request: BankQueryRequest = {}): Observable<BankQueryResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<BankQueryResponse>(
      `${this.apiUrl}/query`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得所有啟用的銀行列表
   * 
   * @returns 銀行列表Observable
   */
  getAllActiveBanks(): Observable<BankInfo[]> {
    const headers = this.getHeaders();
    
    return this.http.get<BankInfo[]>(
      `${this.apiUrl}/active`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得銀行詳細資訊
   * 
   * @param bankCode 銀行代碼
   * @returns 銀行詳細資訊Observable
   */
  getBankDetails(bankCode: string): Observable<BankInfo> {
    const headers = this.getHeaders();
    
    return this.http.get<BankInfo>(
      `${this.apiUrl}/${encodeURIComponent(bankCode)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢銀行分行
   * 
   * @param request 分行查詢請求
   * @returns 分行列表Observable
   */
  queryBranches(request: BranchQueryRequest): Observable<BranchQueryResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<BranchQueryResponse>(
      `${this.apiUrl}/branches/query`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得銀行分行列表
   * 
   * @param bankCode 銀行代碼
   * @returns 分行列表Observable
   */
  getBankBranches(bankCode: string): Observable<BankBranchInfo[]> {
    const headers = this.getHeaders();
    
    return this.http.get<BankBranchInfo[]>(
      `${this.apiUrl}/${encodeURIComponent(bankCode)}/branches`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得分行詳細資訊
   * 
   * @param bankCode 銀行代碼
   * @param branchCode 分行代碼
   * @returns 分行詳細資訊Observable
   */
  getBranchDetails(bankCode: string, branchCode: string): Observable<BankBranchInfo> {
    const headers = this.getHeaders();
    
    return this.http.get<BankBranchInfo>(
      `${this.apiUrl}/${encodeURIComponent(bankCode)}/branches/${encodeURIComponent(branchCode)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證銀行帳戶
   * 
   * @param request 銀行帳戶驗證請求
   * @returns 驗證結果Observable
   */
  validateBankAccount(request: BankAccountValidationRequest): Observable<BankAccountValidationResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<BankAccountValidationResponse>(
      `${this.apiUrl}/account/validate`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得銀行支援的服務列表
   * 
   * @param bankCode 銀行代碼
   * @returns 服務列表Observable
   */
  getBankServices(bankCode: string): Observable<{
    serviceCode: string;
    serviceName: string;
    description: string;
    isAvailable: boolean;
    fee?: number;
    currency?: string;
    processingTime?: string;
    requirements?: string[];
  }[]> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      serviceCode: string;
      serviceName: string;
      description: string;
      isAvailable: boolean;
      fee?: number;
      currency?: string;
      processingTime?: string;
      requirements?: string[];
    }[]>(`${this.apiUrl}/${encodeURIComponent(bankCode)}/services`, { headers }).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得銀行支援的幣別列表
   * 
   * @param bankCode 銀行代碼
   * @returns 幣別列表Observable
   */
  getSupportedCurrencies(bankCode: string): Observable<{
    currencyCode: string;
    currencyName: string;
    isActive: boolean;
    buyRate?: number;
    sellRate?: number;
    lastUpdated?: Date;
  }[]> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      currencyCode: string;
      currencyName: string;
      isActive: boolean;
      buyRate?: number;
      sellRate?: number;
      lastUpdated?: Date;
    }[]>(`${this.apiUrl}/${encodeURIComponent(bankCode)}/currencies`, { headers }).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 搜尋最近的銀行分行
   * 
   * @param latitude 緯度
   * @param longitude 經度
   * @param radius 搜尋半徑（公里）
   * @param bankCode 銀行代碼（可選）
   * @returns 最近分行列表Observable
   */
  findNearbyBranches(
    latitude: number,
    longitude: number,
    radius = 5,
    bankCode?: string
  ): Observable<(BankBranchInfo & {
    distance: number;
    distanceUnit: string;
  })[]> {
    const headers = this.getHeaders();
    const params: any = {
      latitude: latitude.toString(),
      longitude: longitude.toString(),
      radius: radius.toString()
    };
    
    if (bankCode) {
      params.bankCode = bankCode;
    }
    
    return this.http.get<(BankBranchInfo & {
      distance: number;
      distanceUnit: string;
    })[]>(`${this.apiUrl}/branches/nearby`, { headers, params }).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得銀行類型列表
   * 
   * @returns 銀行類型列表Observable
   */
  getBankTypes(): Observable<{
    typeCode: string;
    typeName: string;
    description: string;
    bankCount: number;
  }[]> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      typeCode: string;
      typeName: string;
      description: string;
      bankCount: number;
    }[]>(`${this.apiUrl}/types`, { headers }).pipe(
      timeout(this.timeout),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得HTTP標頭
   * 
   * @returns HttpHeaders
   * @private
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  /**
   * 統一錯誤處理
   * 
   * @param error HTTP錯誤回應
   * @returns 錯誤Observable
   * @private
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '銀行列表API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '銀行查詢參數錯誤或不完整';
          errorCode = 'INVALID_QUERY_PARAMETERS';
          break;
        case 401:
          errorMessage = '沒有權限查詢銀行資訊';
          errorCode = 'UNAUTHORIZED';
          break;
        case 403:
          errorMessage = '銀行資訊查詢權限不足';
          errorCode = 'FORBIDDEN';
          break;
        case 404:
          errorMessage = '找不到指定的銀行或分行';
          errorCode = 'BANK_NOT_FOUND';
          break;
        case 408:
          errorMessage = '銀行資訊查詢請求超時';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 429:
          errorMessage = '銀行資訊查詢請求過於頻繁';
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorMessage = '銀行資訊服務內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorMessage = '銀行資訊服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        case 503:
          errorMessage = '銀行資訊服務正在維護中';
          errorCode = 'SERVICE_MAINTENANCE';
          break;
        default:
          errorMessage = `銀行資訊服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('銀行列表API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    });
  }
}