import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 憑證資訊介面
 * 定義工商憑證的基本結構
 */
export interface CertificateInfo {
  /** 憑證序號 */
  serialNumber: string;
  /** 發行者DN */
  issuerDN: string;
  /** 主體DN */
  subjectDN: string;
  /** 有效期開始時間 */
  notBefore: Date;
  /** 有效期結束時間 */
  notAfter: Date;
  /** 憑證狀態 */
  status: 'VALID' | 'EXPIRED' | 'REVOKED' | 'UNKNOWN';
  /** 公司統一編號 */
  companyId?: string;
  /** 公司名稱 */
  companyName?: string;
}

/**
 * 憑證驗證請求介面
 */
export interface CertificateVerificationRequest {
  /** Base64編碼的憑證內容 */
  certificateData: string;
  /** 驗證類型 */
  verificationType: 'SIGNATURE' | 'CHAIN' | 'FULL';
  /** 時間戳記 */
  timestamp?: string;
  /** 額外驗證參數 */
  additionalParams?: Record<string, any>;
}

/**
 * 憑證驗證回應介面
 */
export interface CertificateVerificationResponse {
  /** 驗證結果 */
  isValid: boolean;
  /** 憑證資訊 */
  certificateInfo: CertificateInfo;
  /** 驗證訊息 */
  message: string;
  /** 錯誤代碼 */
  errorCode?: string;
  /** 驗證時間 */
  verificationTime: Date;
  /** 信任鏈驗證結果 */
  chainValidation?: {
    isChainValid: boolean;
    chainLength: number;
    rootCA: string;
  };
}

/**
 * 憑證列表請求介面
 */
export interface CertificateListRequest {
  /** 頁數 */
  page?: number;
  /** 每頁筆數 */
  pageSize?: number;
  /** 搜尋關鍵字 */
  keyword?: string;
  /** 狀態篩選 */
  status?: string[];
  /** 有效期篩選 */
  expiryFilter?: 'ALL' | 'EXPIRING_SOON' | 'EXPIRED';
}

/**
 * 憑證列表回應介面
 */
export interface CertificateListResponse {
  /** 憑證列表 */
  certificates: CertificateInfo[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
}

/**
 * 法人模組憑證驗證API服務
 * 
 * 提供與工商憑證系統整合的完整功能，包括：
 * - 憑證驗證
 * - 憑證資訊查詢
 * - 憑證狀態檢查
 * - 信任鏈驗證
 * 
 * @example
 * ```typescript
 * constructor(private certificateApi: CertificateApiService) {}
 * 
 * async verifyCertificate(certData: string) {
 *   try {
 *     const result = await this.certificateApi.verifyCertificate({
 *       certificateData: certData,
 *       verificationType: 'FULL'
 *     }).toPromise();
 *     
 *     if (result.isValid) {
 *       console.log('憑證驗證成功', result.certificateInfo);
 *     }
 *   } catch (error) {
 *     console.error('憑證驗證失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class CertificateApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/v1/corporate/certificate`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2; // 重試次數

  constructor(private http: HttpClient) {}

  /**
   * 驗證工商憑證
   * 
   * @param request 憑證驗證請求
   * @returns 憑證驗證結果Observable
   * 
   * @example
   * ```typescript
   * const request: CertificateVerificationRequest = {
   *   certificateData: 'base64EncodedCertData',
   *   verificationType: 'FULL'
   * };
   * 
   * this.certificateApi.verifyCertificate(request).subscribe({
   *   next: (response) => console.log('驗證成功', response),
   *   error: (error) => console.error('驗證失敗', error)
   * });
   * ```
   */
  verifyCertificate(request: CertificateVerificationRequest): Observable<CertificateVerificationResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<CertificateVerificationResponse>(
      `${this.apiUrl}/verify`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得憑證詳細資訊
   * 
   * @param serialNumber 憑證序號
   * @returns 憑證資訊Observable
   */
  getCertificateInfo(serialNumber: string): Observable<CertificateInfo> {
    const headers = this.getHeaders();
    
    return this.http.get<CertificateInfo>(
      `${this.apiUrl}/info/${encodeURIComponent(serialNumber)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 檢查憑證狀態
   * 
   * @param serialNumber 憑證序號
   * @returns 憑證狀態Observable
   */
  checkCertificateStatus(serialNumber: string): Observable<{ status: string; message: string }> {
    const headers = this.getHeaders();
    
    return this.http.get<{ status: string; message: string }>(
      `${this.apiUrl}/status/${encodeURIComponent(serialNumber)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得憑證列表
   * 
   * @param request 列表查詢請求
   * @returns 憑證列表Observable
   */
  getCertificateList(request: CertificateListRequest = {}): Observable<CertificateListResponse> {
    const headers = this.getHeaders();
    const params = this.buildQueryParams(request);
    
    return this.http.get<CertificateListResponse>(
      `${this.apiUrl}/list`,
      { headers, params }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證憑證信任鏈
   * 
   * @param certificateData Base64編碼的憑證資料
   * @returns 信任鏈驗證結果Observable
   */
  validateCertificateChain(certificateData: string): Observable<{
    isChainValid: boolean;
    chainLength: number;
    rootCA: string;
    intermediates: string[];
    errors?: string[];
  }> {
    const headers = this.getHeaders();
    
    return this.http.post<{
      isChainValid: boolean;
      chainLength: number;
      rootCA: string;
      intermediates: string[];
      errors?: string[];
    }>(
      `${this.apiUrl}/validate-chain`,
      { certificateData },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 撤銷憑證狀態檢查
   * 
   * @param serialNumber 憑證序號
   * @returns 撤銷狀態檢查結果Observable
   */
  checkRevocationStatus(serialNumber: string): Observable<{
    isRevoked: boolean;
    revocationTime?: Date;
    reason?: string;
    crlUrl?: string;
    ocspUrl?: string;
  }> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      isRevoked: boolean;
      revocationTime?: Date;
      reason?: string;
      crlUrl?: string;
      ocspUrl?: string;
    }>(
      `${this.apiUrl}/revocation-status/${encodeURIComponent(serialNumber)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得HTTP標頭
   * 
   * @returns HttpHeaders
   * @private
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  /**
   * 建構查詢參數
   * 
   * @param request 請求物件
   * @returns 查詢參數物件
   * @private
   */
  private buildQueryParams(request: any): Record<string, string> {
    const params: Record<string, string> = {};
    
    Object.keys(request).forEach(key => {
      const value = request[key];
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          params[key] = value.join(',');
        } else {
          params[key] = value.toString();
        }
      }
    });
    
    return params;
  }

  /**
   * 統一錯誤處理
   * 
   * @param error HTTP錯誤回應
   * @returns 錯誤Observable
   * @private
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '憑證API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '憑證資料格式錯誤或不完整';
          errorCode = 'INVALID_CERTIFICATE_DATA';
          break;
        case 401:
          errorMessage = '憑證驗證失敗，請檢查憑證有效性';
          errorCode = 'CERTIFICATE_INVALID';
          break;
        case 403:
          errorMessage = '沒有權限執行此憑證操作';
          errorCode = 'INSUFFICIENT_PERMISSIONS';
          break;
        case 404:
          errorMessage = '找不到指定的憑證資訊';
          errorCode = 'CERTIFICATE_NOT_FOUND';
          break;
        case 408:
          errorMessage = '憑證驗證請求超時';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 429:
          errorMessage = '憑證驗證請求過於頻繁，請稍後再試';
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorMessage = '憑證服務內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorMessage = '憑證服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        case 503:
          errorMessage = '憑證服務正在維護中';
          errorCode = 'SERVICE_MAINTENANCE';
          break;
        default:
          errorMessage = `憑證服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('憑證API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    });
  }
}