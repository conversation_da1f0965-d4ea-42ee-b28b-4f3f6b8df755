/**
 * Corporate Service 單元測試
 * 
 * @description 測試Corporate Service的法人業務邏輯功能
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';

import { CorporateService } from './corporate.service';
import { TestUtils, TEST_CONSTANTS, TEST_DESCRIPTIONS } from '../../../testing/test-utils';

import {
  CompanyInfo,
  RepresentativeInfo,
  CertificateInfo,
  CorporateRemittance,
  CorporateApplication,
  CorporateBankAccount,
  ApplicationStatus,
  RemittanceStatus
} from '../models/corporate.model';

describe('CorporateService', () => {
  let service: CorporateService;
  let httpMock: HttpTestingController;

  const API_BASE_URL = TEST_CONSTANTS.API_BASE_URL + '/corporate';

  const mockCompanyInfo: CompanyInfo = {
    unifiedNumber: TestUtils.createMockUnifiedNumber(),
    companyName: '測試科技股份有限公司',
    companyEnglishName: 'Test Technology Corporation',
    industryCode: 'J401010',
    industryName: '軟體開發業',
    establishedDate: new Date('2010-01-01'),
    registeredAddress: '台北市信義區松智路1號',
    contactPhone: '02-2123-4567',
    contactEmail: TestUtils.createMockEmail(),
    legalRepresentative: '王大明',
    branchCode: '0001',
    accountNumber: '********90123456',
    confirmEnglishName: true
  };

  const mockRepresentativeInfo: RepresentativeInfo = {
    taiwanId: TestUtils.createMockTaiwanId(),
    chineseName: '王大明',
    englishName: 'Wang Da Ming',
    position: '董事長',
    phoneNumber: TestUtils.createMockPhoneNumber(),
    email: TestUtils.createMockEmail(),
    isAuthorized: true,
    authorizationDocument: undefined
  };

  const mockCertificateInfo: CertificateInfo = {
    serialNumber: 'CERT********9',
    issuer: 'Taiwan CA',
    subject: 'CN=測試科技股份有限公司',
    validFrom: new Date('2024-01-01'),
    validTo: new Date('2025-12-31'),
    unifiedNumber: TestUtils.createMockUnifiedNumber(),
    companyName: '測試科技股份有限公司',
    keyUsage: ['digitalSignature', 'nonRepudiation'],
    isValid: true
  };

  const mockBankAccount: CorporateBankAccount = {
    bankCode: '012',
    bankName: '凱基商業銀行',
    branchCode: '0001',
    branchName: '總行',
    accountNumber: '********90123456',
    accountName: '測試科技股份有限公司',
    accountType: 'CHECKING',
    isVerified: true
  };

  const mockRemittance: CorporateRemittance = {
    remittanceId: 'REM123456',
    remittanceDate: new Date('2025-01-01'),
    senderName: 'Test Corp USA',
    senderCountry: 'USA',
    beneficiaryName: '測試科技股份有限公司',
    amount: 50000,
    currency: 'USD',
    purpose: '設備採購款',
    status: RemittanceStatus.PENDING,
    bankReference: 'REF123456',
    swiftCode: 'TESTUSDXXX'
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [CorporateService]
    });

    service = TestBed.inject(CorporateService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('基本功能測試', () => {
    it(TEST_DESCRIPTIONS.SERVICE.SHOULD_BE_CREATED, () => {
      expect(service).toBeTruthy();
    });

    it('應該初始化正確的初始狀態', () => {
      service.application$.subscribe(app => {
        expect(app.status).toBe(ApplicationStatus.DRAFT);
        expect(app.remittances).toEqual([]);
      });
    });
  });

  describe('公司資訊管理', () => {
    it('應該更新公司資訊', () => {
      service.updateCompanyInfo(mockCompanyInfo);

      service.companyInfo$.subscribe(info => {
        expect(info.unifiedNumber).toBe(mockCompanyInfo.unifiedNumber);
        expect(info.companyName).toBe(mockCompanyInfo.companyName);
        expect(info.companyEnglishName).toBe(mockCompanyInfo.companyEnglishName);
      });
    });

    it('應該驗證統一編號', () => {
      const validUnifiedNumber = TestUtils.createMockUnifiedNumber();
      const mockResponse = TestUtils.createMockApiResponse(true, {
        isValid: true,
        companyName: '測試科技股份有限公司',
        registrationStatus: 'ACTIVE'
      });

      service.verifyUnifiedNumber(validUnifiedNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.isValid).toBeTrue();
        expect(response.result?.companyName).toBe('測試科技股份有限公司');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/company/verify-unified-number`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.unifiedNumber).toBe(validUnifiedNumber);
      req.flush(mockResponse);
    });

    it('應該處理無效的統一編號', () => {
      const invalidUnifiedNumber = '********';
      const mockResponse = TestUtils.createMockApiResponse(false, {
        isValid: false,
        reason: '統一編號格式錯誤'
      }, '統一編號驗證失敗');

      service.verifyUnifiedNumber(invalidUnifiedNumber).subscribe(response => {
        expect(response.success).toBeFalse();
        expect(response.result?.isValid).toBeFalse();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/company/verify-unified-number`);
      req.flush(mockResponse);
    });

    it('應該驗證公司註冊狀態', () => {
      const unifiedNumber = TestUtils.createMockUnifiedNumber();
      const mockResponse = TestUtils.createMockApiResponse(true, {
        registrationStatus: 'ACTIVE',
        registrationDate: '2010-01-01',
        industryCode: 'J401010',
        industryName: '軟體開發業'
      });

      service.verifyCompanyRegistration(unifiedNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.registrationStatus).toBe('ACTIVE');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/company/verify-registration`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('代表人資訊管理', () => {
    it('應該更新代表人資訊', () => {
      service.updateRepresentativeInfo(mockRepresentativeInfo);

      service.application$.subscribe(app => {
        expect(app.representativeInfo.taiwanId).toBe(mockRepresentativeInfo.taiwanId);
        expect(app.representativeInfo.chineseName).toBe(mockRepresentativeInfo.chineseName);
        expect(app.representativeInfo.position).toBe(mockRepresentativeInfo.position);
      });
    });

    it('應該驗證代表人身份', () => {
      const mockRequest = {
        taiwanId: TEST_CONSTANTS.VALID_TAIWAN_ID,
        unifiedNumber: TestUtils.createMockUnifiedNumber(),
        representativeName: '王大明'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        isValid: true,
        isAuthorized: true,
        position: '董事長',
        authorizationDate: new Date().toISOString()
      });

      service.verifyRepresentative(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.isValid).toBeTrue();
        expect(response.result?.isAuthorized).toBeTrue();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/representative/verify`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該處理未授權的代表人', () => {
      const mockRequest = {
        taiwanId: TEST_CONSTANTS.VALID_TAIWAN_ID,
        unifiedNumber: TestUtils.createMockUnifiedNumber(),
        representativeName: '李小華'
      };

      const mockResponse = TestUtils.createMockApiResponse(false, {
        isValid: true,
        isAuthorized: false,
        reason: '非公司授權代表人'
      }, '代表人驗證失敗');

      service.verifyRepresentative(mockRequest).subscribe(response => {
        expect(response.success).toBeFalse();
        expect(response.result?.isAuthorized).toBeFalse();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/representative/verify`);
      req.flush(mockResponse);
    });
  });

  describe('憑證管理', () => {
    it('應該更新憑證資訊', () => {
      service.updateCertificateInfo(mockCertificateInfo);

      service.certificateInfo$.subscribe(cert => {
        expect(cert?.serialNumber).toBe(mockCertificateInfo.serialNumber);
        expect(cert?.issuer).toBe(mockCertificateInfo.issuer);
        expect(cert?.isValid).toBe(mockCertificateInfo.isValid);
      });
    });

    it('應該驗證憑證', () => {
      const mockCertificateData = 'certificate-base64-data';
      const mockResponse = TestUtils.createMockApiResponse(true, {
        isValid: true,
        issuer: 'Taiwan CA',
        subject: 'CN=測試科技股份有限公司',
        serialNumber: 'CERT********9',
        validFrom: new Date('2024-01-01').toISOString(),
        validTo: new Date('2025-12-31').toISOString()
      });

      service.verifyCertificate(mockCertificateData).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.isValid).toBeTrue();
        expect(response.result?.issuer).toBe('Taiwan CA');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/certificate/verify`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.certificateData).toBe(mockCertificateData);
      req.flush(mockResponse);
    });

    it('應該處理無效憑證', () => {
      const mockCertificateData = 'invalid-certificate-data';
      const mockResponse = TestUtils.createMockApiResponse(false, {
        isValid: false,
        reason: '憑證已過期'
      }, '憑證驗證失敗');

      service.verifyCertificate(mockCertificateData).subscribe(response => {
        expect(response.success).toBeFalse();
        expect(response.result?.isValid).toBeFalse();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/certificate/verify`);
      req.flush(mockResponse);
    });

    it('應該執行數位簽章', () => {
      const mockSignRequest = {
        dataToSign: 'data-to-sign',
        certificateData: 'certificate-data',
        pin: 'encrypted-pin'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        signature: 'digital-signature-base64',
        algorithm: 'SHA256withRSA',
        timestamp: new Date().toISOString()
      });

      service.digitalSign(mockSignRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.signature).toBe('digital-signature-base64');
        expect(response.result?.algorithm).toBe('SHA256withRSA');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/certificate/sign`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('匯款管理', () => {
    it('應該搜尋匯款記錄', () => {
      const searchCriteria = {
        unifiedNumber: TestUtils.createMockUnifiedNumber(),
        companyName: '測試科技股份有限公司',
        dateRange: {
          startDate: new Date('2025-01-01'),
          endDate: new Date('2025-01-31')
        }
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        remittances: [mockRemittance],
        totalCount: 1,
        currentPage: 1,
        totalPages: 1
      });

      service.searchRemittances(searchCriteria).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.remittances).toHaveLength(1);
        expect(response.result?.remittances[0].remittanceId).toBe('REM123456');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/remittance/search`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該添加匯款到申請', () => {
      service.addRemittanceToApplication(mockRemittance);

      service.application$.subscribe(app => {
        expect(app.remittances).toHaveLength(1);
        expect(app.remittances[0].remittanceId).toBe('REM123456');
      });
    });

    it('應該從申請中移除匯款', () => {
      // 先添加匯款
      service.addRemittanceToApplication(mockRemittance);
      
      // 再移除匯款
      service.removeRemittanceFromApplication('REM123456');

      service.application$.subscribe(app => {
        expect(app.remittances).toHaveLength(0);
      });
    });

    it('應該計算匯款總金額', () => {
      const remittance1 = { ...mockRemittance, amount: 1000 };
      const remittance2 = { ...mockRemittance, remittanceId: 'REM654321', amount: 2000 };

      service.addRemittanceToApplication(remittance1);
      service.addRemittanceToApplication(remittance2);

      const totalAmount = service.calculateTotalAmount();
      expect(totalAmount).toBe(3000);
    });
  });

  describe('銀行帳戶管理', () => {
    it('應該更新銀行帳戶資訊', () => {
      service.updateBankAccount(mockBankAccount);

      service.application$.subscribe(app => {
        expect(app.bankAccount.bankCode).toBe(mockBankAccount.bankCode);
        expect(app.bankAccount.accountNumber).toBe(mockBankAccount.accountNumber);
        expect(app.bankAccount.isVerified).toBe(mockBankAccount.isVerified);
      });
    });

    it('應該驗證銀行帳戶', () => {
      const verifyRequest = {
        bankCode: '012',
        accountNumber: '********90123456',
        accountName: '測試科技股份有限公司'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        isValid: true,
        bankName: '凱基商業銀行',
        accountName: '測試科技股份有限公司',
        accountType: 'CHECKING'
      });

      service.verifyBankAccount(verifyRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.isValid).toBeTrue();
        expect(response.result?.bankName).toBe('凱基商業銀行');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/bank/verify-account`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該取得銀行列表', () => {
      const mockBanks = [
        { bankCode: '012', bankName: '凱基商業銀行', englishName: 'KGI Bank' },
        { bankCode: '822', bankName: '中國信託商業銀行', englishName: 'CTBC Bank' }
      ];

      service.getBankList().subscribe(banks => {
        expect(banks).toHaveLength(2);
        expect(banks[0].bankCode).toBe('012');
        expect(banks[1].bankCode).toBe('822');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBanks);
    });
  });

  describe('申請提交', () => {
    beforeEach(() => {
      // 設定完整的申請資料
      service.updateCompanyInfo(mockCompanyInfo);
      service.updateRepresentativeInfo(mockRepresentativeInfo);
      service.updateCertificateInfo(mockCertificateInfo);
      service.updateBankAccount(mockBankAccount);
      service.addRemittanceToApplication(mockRemittance);
    });

    it('應該成功提交申請', () => {
      const mockResponse = TestUtils.createMockApiResponse(true, {
        applicationId: 'APP123456',
        status: ApplicationStatus.SUBMITTED,
        submittedAt: new Date().toISOString(),
        estimatedProcessTime: '3-5個工作天',
        trackingNumber: 'TRK123456'
      });

      service.submitApplication().subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.applicationId).toBe('APP123456');
        expect(response.result?.status).toBe(ApplicationStatus.SUBMITTED);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/application/submit`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該驗證申請完整性', () => {
      const isComplete = service.isApplicationComplete();
      expect(isComplete).toBeTrue();
    });

    it('應該檢測不完整的申請', () => {
      // 移除必要資料
      service.updateCompanyInfo({} as CompanyInfo);
      
      const isComplete = service.isApplicationComplete();
      expect(isComplete).toBeFalse();
    });

    it('應該計算申請進度', () => {
      const progress = service.getApplicationProgress();
      expect(progress).toBe(100); // 所有資料都完整
    });

    it('應該取得申請狀態', () => {
      const applicationId = 'APP123456';
      const mockResponse = TestUtils.createMockApiResponse(true, {
        applicationId,
        status: ApplicationStatus.PROCESSING,
        submittedAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        progress: 60,
        currentStep: 'BANK_VERIFICATION'
      });

      service.getApplicationStatus(applicationId).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.status).toBe(ApplicationStatus.PROCESSING);
        expect(response.result?.progress).toBe(60);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/application/${applicationId}/status`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('狀態重置', () => {
    it('應該重置申請狀態', () => {
      // 先設定一些資料
      service.updateCompanyInfo(mockCompanyInfo);
      service.addRemittanceToApplication(mockRemittance);

      // 重置狀態
      service.resetApplication();

      service.application$.subscribe(app => {
        expect(app.status).toBe(ApplicationStatus.DRAFT);
        expect(app.remittances).toHaveLength(0);
        expect(app.companyInfo).toEqual({} as CompanyInfo);
      });
    });

    it('應該清除憑證資訊', () => {
      service.updateCertificateInfo(mockCertificateInfo);
      service.clearCertificateInfo();

      service.certificateInfo$.subscribe(cert => {
        expect(cert).toBeNull();
      });
    });
  });

  describe('資料驗證', () => {
    it('應該驗證公司資訊完整性', () => {
      const isValid = service.validateCompanyInfo(mockCompanyInfo);
      expect(isValid).toBeTrue();
    });

    it('應該檢測不完整的公司資訊', () => {
      const incompleteInfo = {
        ...mockCompanyInfo,
        unifiedNumber: '',
        companyName: ''
      };

      const isValid = service.validateCompanyInfo(incompleteInfo);
      expect(isValid).toBeFalse();
    });

    it('應該驗證代表人資訊完整性', () => {
      const isValid = service.validateRepresentativeInfo(mockRepresentativeInfo);
      expect(isValid).toBeTrue();
    });

    it('應該檢測不完整的代表人資訊', () => {
      const incompleteInfo = {
        ...mockRepresentativeInfo,
        taiwanId: '',
        chineseName: ''
      };

      const isValid = service.validateRepresentativeInfo(incompleteInfo);
      expect(isValid).toBeFalse();
    });
  });

  describe('錯誤處理', () => {
    it('應該處理HTTP錯誤', () => {
      const unifiedNumber = TestUtils.createMockUnifiedNumber();

      service.verifyUnifiedNumber(unifiedNumber).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.status).toBe(500);
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/company/verify-unified-number`);
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('應該處理網路錯誤', () => {
      const unifiedNumber = TestUtils.createMockUnifiedNumber();

      service.verifyUnifiedNumber(unifiedNumber).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error).toBeTruthy();
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/company/verify-unified-number`);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('快取管理', () => {
    it('應該快取銀行列表', () => {
      const mockBanks = [
        { bankCode: '012', bankName: '凱基商業銀行', englishName: 'KGI Bank' }
      ];

      // 第一次請求
      service.getBankList().subscribe();
      const req1 = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req1.flush(mockBanks);

      // 第二次請求應該使用快取（如果服務有實作快取）
      service.getBankList().subscribe(banks => {
        expect(banks).toHaveLength(1);
      });

      // 檢查是否有額外的HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/banks`);
    });
  });

  describe('工具方法', () => {
    it('應該格式化統一編號', () => {
      const formatted = service.formatUnifiedNumber('********');
      expect(formatted).toBe('1234-5678');
    });

    it('應該取得申請步驟', () => {
      const steps = service.getApplicationSteps();
      expect(steps).toBeDefined();
      expect(steps.length).toBeGreaterThan(0);
    });

    it('應該檢查當前步驟', () => {
      service.updateCompanyInfo(mockCompanyInfo);
      const currentStep = service.getCurrentStep();
      expect(currentStep).toBeDefined();
    });
  });
});