import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { Router } from '@angular/router';

/**
 * 錯誤類型枚舉
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 錯誤嚴重性枚舉
 */
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * 標準化錯誤介面
 */
export interface StandardizedError {
  /** 錯誤代碼 */
  errorCode: string;
  /** 錯誤類型 */
  errorType: ErrorType;
  /** 錯誤嚴重性 */
  severity: ErrorSeverity;
  /** 使用者友善的錯誤訊息 */
  userMessage: string;
  /** 技術性錯誤訊息 */
  technicalMessage: string;
  /** 建議的解決方案 */
  suggestedActions: string[];
  /** 錯誤發生時間 */
  timestamp: Date;
  /** 錯誤來源 */
  source: string;
  /** HTTP狀態碼（如適用） */
  httpStatus?: number;
  /** 原始錯誤物件 */
  originalError?: any;
  /** 錯誤堆疊追蹤 */
  stackTrace?: string;
  /** 使用者ID（如適用） */
  userId?: string;
  /** 會話ID（如適用） */
  sessionId?: string;
  /** 請求ID（如適用） */
  requestId?: string;
  /** 是否可重試 */
  isRetryable: boolean;
  /** 重試次數限制 */
  maxRetryCount?: number;
}

/**
 * 錯誤處理選項介面
 */
export interface ErrorHandlingOptions {
  /** 是否顯示使用者通知 */
  showUserNotification?: boolean;
  /** 是否記錄錯誤日誌 */
  logError?: boolean;
  /** 是否發送錯誤報告 */
  sendErrorReport?: boolean;
  /** 是否重導向到錯誤頁面 */
  redirectToErrorPage?: boolean;
  /** 自定義錯誤頁面路由 */
  customErrorPageRoute?: string;
  /** 是否自動重試 */
  autoRetry?: boolean;
  /** 重試次數 */
  retryCount?: number;
  /** 重試延遲時間（毫秒） */
  retryDelay?: number;
  /** 錯誤回調函數 */
  onError?: (error: StandardizedError) => void;
}

/**
 * 錯誤統計介面
 */
export interface ErrorStatistics {
  /** 總錯誤數 */
  totalErrors: number;
  /** 按類型分類的錯誤數 */
  errorsByType: Record<ErrorType, number>;
  /** 按嚴重性分類的錯誤數 */
  errorsBySeverity: Record<ErrorSeverity, number>;
  /** 最近的錯誤 */
  recentErrors: StandardizedError[];
  /** 統計時間範圍 */
  timeRange: {
    startTime: Date;
    endTime: Date;
  };
}

/**
 * 錯誤報告介面
 */
export interface ErrorReport {
  /** 報告ID */
  reportId: string;
  /** 錯誤資訊 */
  error: StandardizedError;
  /** 使用者代理資訊 */
  userAgent: string;
  /** 瀏覽器資訊 */
  browserInfo: {
    name: string;
    version: string;
    platform: string;
  };
  /** 應用程式狀態 */
  applicationState?: any;
  /** 使用者行為追蹤 */
  userActions?: {
    action: string;
    timestamp: Date;
    details?: any;
  }[];
}

/**
 * 法人模組錯誤處理服務
 * 
 * 提供統一的錯誤處理機制，包括：
 * - 錯誤標準化與分類
 * - 使用者友善的錯誤訊息
 * - 錯誤日誌記錄
 * - 錯誤統計分析
 * - 自動錯誤恢復
 * - 錯誤報告機制
 * 
 * @example
 * ```typescript
 * constructor(private errorHandler: ErrorHandlerService) {}
 * 
 * handleApiError(error: HttpErrorResponse) {
 *   this.errorHandler.handleHttpError(error, {
 *     showUserNotification: true,
 *     logError: true,
 *     sendErrorReport: true
 *   }).subscribe({
 *     next: (standardizedError) => {
 *       console.log('錯誤已處理:', standardizedError.userMessage);
 *     },
 *     error: (criticalError) => {
 *       console.error('嚴重錯誤:', criticalError);
 *     }
 *   });
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {
  private errorLog: StandardizedError[] = [];
  private readonly maxLogSize = 1000;
  private errorStatistics: ErrorStatistics;

  constructor(private router: Router) {
    this.initializeErrorStatistics();
    this.setupGlobalErrorHandlers();
  }

  /**
   * 處理HTTP錯誤
   * 
   * @param error HTTP錯誤回應
   * @param options 錯誤處理選項
   * @returns 標準化錯誤Observable
   * 
   * @example
   * ```typescript
   * this.errorHandler.handleHttpError(error, {
   *   showUserNotification: true,
   *   logError: true,
   *   autoRetry: false
   * }).subscribe({
   *   next: (standardizedError) => {
   *     // 處理標準化後的錯誤
   *     this.showErrorMessage(standardizedError.userMessage);
   *   }
   * });
   * ```
   */
  handleHttpError(
    error: HttpErrorResponse, 
    options: ErrorHandlingOptions = {}
  ): Observable<StandardizedError> {
    const standardizedError = this.standardizeHttpError(error);
    
    // 設定預設選項
    const defaultOptions: ErrorHandlingOptions = {
      showUserNotification: true,
      logError: true,
      sendErrorReport: false,
      redirectToErrorPage: false,
      autoRetry: false
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    // 執行錯誤處理
    this.processError(standardizedError, finalOptions);
    
    return of(standardizedError);
  }

  /**
   * 處理一般錯誤
   * 
   * @param error 原始錯誤
   * @param source 錯誤來源
   * @param options 錯誤處理選項
   * @returns 標準化錯誤Observable
   */
  handleGeneralError(
    error: any,
    source: string,
    options: ErrorHandlingOptions = {}
  ): Observable<StandardizedError> {
    const standardizedError = this.standardizeGeneralError(error, source);
    
    const defaultOptions: ErrorHandlingOptions = {
      showUserNotification: true,
      logError: true,
      sendErrorReport: false
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    this.processError(standardizedError, finalOptions);
    
    return of(standardizedError);
  }

  /**
   * 處理業務邏輯錯誤
   * 
   * @param errorCode 錯誤代碼
   * @param message 錯誤訊息
   * @param source 錯誤來源
   * @param options 錯誤處理選項
   * @returns 標準化錯誤Observable
   */
  handleBusinessError(
    errorCode: string,
    message: string,
    source: string,
    options: ErrorHandlingOptions = {}
  ): Observable<StandardizedError> {
    const standardizedError: StandardizedError = {
      errorCode,
      errorType: ErrorType.BUSINESS_LOGIC,
      severity: ErrorSeverity.MEDIUM,
      userMessage: this.getUserFriendlyMessage(errorCode, message),
      technicalMessage: message,
      suggestedActions: this.getSuggestedActions(errorCode),
      timestamp: new Date(),
      source,
      isRetryable: false,
      originalError: { errorCode, message }
    };
    
    const defaultOptions: ErrorHandlingOptions = {
      showUserNotification: true,
      logError: true,
      sendErrorReport: false
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    this.processError(standardizedError, finalOptions);
    
    return of(standardizedError);
  }

  /**
   * 取得錯誤統計
   * 
   * @returns 錯誤統計資訊
   */
  getErrorStatistics(): ErrorStatistics {
    this.updateErrorStatistics();
    return { ...this.errorStatistics };
  }

  /**
   * 清除錯誤日誌
   * 
   * @param olderThan 清除指定時間之前的日誌（可選）
   */
  clearErrorLog(olderThan?: Date): void {
    if (olderThan) {
      this.errorLog = this.errorLog.filter(error => error.timestamp > olderThan);
    } else {
      this.errorLog = [];
    }
    this.updateErrorStatistics();
  }

  /**
   * 取得近期錯誤
   * 
   * @param count 取得的錯誤數量
   * @returns 近期錯誤陣列
   */
  getRecentErrors(count = 10): StandardizedError[] {
    return this.errorLog
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, count);
  }

  /**
   * 檢查錯誤是否可重試
   * 
   * @param error 標準化錯誤
   * @returns 是否可重試
   */
  isRetryableError(error: StandardizedError): boolean {
    return error.isRetryable && (
      error.errorType === ErrorType.NETWORK ||
      error.errorType === ErrorType.TIMEOUT ||
      (error.httpStatus && error.httpStatus >= 500)
    );
  }

  /**
   * 產生錯誤報告
   * 
   * @param error 標準化錯誤
   * @returns 錯誤報告
   */
  generateErrorReport(error: StandardizedError): ErrorReport {
    const reportId = this.generateReportId();
    const userAgent = navigator.userAgent;
    const browserInfo = this.getBrowserInfo();
    
    return {
      reportId,
      error,
      userAgent,
      browserInfo,
      applicationState: this.captureApplicationState(),
      userActions: this.getUserActions()
    };
  }

  /**
   * 發送錯誤報告
   * 
   * @param report 錯誤報告
   * @returns 發送結果Promise
   */
  async sendErrorReport(report: ErrorReport): Promise<boolean> {
    try {
      // 這裡應該實作實際的錯誤報告發送邏輯
      console.log('發送錯誤報告:', report);
      
      // 模擬API呼叫
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return true;
    } catch (error) {
      console.error('發送錯誤報告失敗:', error);
      return false;
    }
  }

  /**
   * 標準化HTTP錯誤
   * 
   * @param error HTTP錯誤回應
   * @returns 標準化錯誤
   * @private
   */
  private standardizeHttpError(error: HttpErrorResponse): StandardizedError {
    let errorType: ErrorType;
    let severity: ErrorSeverity;
    let userMessage: string;
    let suggestedActions: string[];
    let isRetryable: boolean;
    
    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorType = ErrorType.NETWORK;
      severity = ErrorSeverity.MEDIUM;
      userMessage = '網路連線發生問題，請檢查您的網路連線';
      suggestedActions = [
        '檢查網路連線',
        '重新載入頁面',
        '聯絡系統管理員'
      ];
      isRetryable = true;
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 401:
          errorType = ErrorType.AUTHENTICATION;
          severity = ErrorSeverity.HIGH;
          userMessage = '身份驗證失敗，請重新登入';
          suggestedActions = [
            '重新登入系統',
            '檢查憑證是否有效',
            '聯絡系統管理員'
          ];
          isRetryable = false;
          break;
        case 403:
          errorType = ErrorType.AUTHORIZATION;
          severity = ErrorSeverity.HIGH;
          userMessage = '您沒有執行此操作的權限';
          suggestedActions = [
            '聯絡系統管理員取得權限',
            '檢查使用者角色設定'
          ];
          isRetryable = false;
          break;
        case 400:
        case 422:
          errorType = ErrorType.VALIDATION;
          severity = ErrorSeverity.MEDIUM;
          userMessage = '資料格式錯誤，請檢查輸入內容';
          suggestedActions = [
            '檢查輸入資料格式',
            '確認必填欄位已填寫',
            '聯絡技術支援'
          ];
          isRetryable = false;
          break;
        case 404:
          errorType = ErrorType.BUSINESS_LOGIC;
          severity = ErrorSeverity.MEDIUM;
          userMessage = '找不到請求的資源';
          suggestedActions = [
            '確認資源是否存在',
            '檢查請求路徑',
            '重新載入頁面'
          ];
          isRetryable = false;
          break;
        case 408:
          errorType = ErrorType.TIMEOUT;
          severity = ErrorSeverity.MEDIUM;
          userMessage = '請求超時，請稍後再試';
          suggestedActions = [
            '稍後重試',
            '檢查網路連線',
            '聯絡系統管理員'
          ];
          isRetryable = true;
          break;
        case 429:
          errorType = ErrorType.BUSINESS_LOGIC;
          severity = ErrorSeverity.MEDIUM;
          userMessage = '請求過於頻繁，請稍後再試';
          suggestedActions = [
            '等待一段時間後重試',
            '減少請求頻率'
          ];
          isRetryable = true;
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          errorType = ErrorType.SERVER_ERROR;
          severity = ErrorSeverity.HIGH;
          userMessage = '系統暫時無法處理您的請求，請稍後再試';
          suggestedActions = [
            '稍後重試',
            '聯絡系統管理員',
            '檢查系統狀態'
          ];
          isRetryable = true;
          break;
        default:
          errorType = ErrorType.UNKNOWN;
          severity = ErrorSeverity.MEDIUM;
          userMessage = '發生未知錯誤，請聯絡系統管理員';
          suggestedActions = [
            '重新載入頁面',
            '聯絡系統管理員',
            '提供錯誤詳細資訊'
          ];
          isRetryable = false;
      }
    }
    
    const errorCode = this.generateErrorCode(error);
    
    return {
      errorCode,
      errorType,
      severity,
      userMessage,
      technicalMessage: error.message,
      suggestedActions,
      timestamp: new Date(),
      source: 'HTTP_REQUEST',
      httpStatus: error.status,
      originalError: error,
      isRetryable,
      maxRetryCount: isRetryable ? 3 : 0
    };
  }

  /**
   * 標準化一般錯誤
   * 
   * @param error 原始錯誤
   * @param source 錯誤來源
   * @returns 標準化錯誤
   * @private
   */
  private standardizeGeneralError(error: any, source: string): StandardizedError {
    let errorType: ErrorType = ErrorType.UNKNOWN;
    let severity: ErrorSeverity = ErrorSeverity.MEDIUM;
    let userMessage: string;
    let technicalMessage: string;
    
    if (error instanceof Error) {
      technicalMessage = error.message;
      userMessage = '系統發生錯誤，請稍後再試';
      
      if (error.name === 'TypeError') {
        errorType = ErrorType.BUSINESS_LOGIC;
        userMessage = '資料處理發生錯誤';
      } else if (error.name === 'ReferenceError') {
        errorType = ErrorType.BUSINESS_LOGIC;
        severity = ErrorSeverity.HIGH;
        userMessage = '系統組件載入失敗';
      }
    } else if (typeof error === 'string') {
      technicalMessage = error;
      userMessage = '系統發生錯誤，請稍後再試';
    } else {
      technicalMessage = JSON.stringify(error);
      userMessage = '系統發生未知錯誤';
    }
    
    return {
      errorCode: this.generateErrorCode(error),
      errorType,
      severity,
      userMessage,
      technicalMessage,
      suggestedActions: [
        '重新載入頁面',
        '清除瀏覽器快取',
        '聯絡系統管理員'
      ],
      timestamp: new Date(),
      source,
      originalError: error,
      isRetryable: false
    };
  }

  /**
   * 處理錯誤
   * 
   * @param error 標準化錯誤
   * @param options 處理選項
   * @private
   */
  private processError(error: StandardizedError, options: ErrorHandlingOptions): void {
    // 記錄錯誤
    if (options.logError) {
      this.logError(error);
    }
    
    // 顯示使用者通知
    if (options.showUserNotification) {
      this.showUserNotification(error);
    }
    
    // 發送錯誤報告
    if (options.sendErrorReport) {
      const report = this.generateErrorReport(error);
      this.sendErrorReport(report);
    }
    
    // 重導向到錯誤頁面
    if (options.redirectToErrorPage) {
      const route = options.customErrorPageRoute || '/error';
      this.router.navigate([route], { 
        queryParams: { 
          errorCode: error.errorCode,
          message: error.userMessage 
        }
      });
    }
    
    // 執行自定義錯誤回調
    if (options.onError) {
      options.onError(error);
    }
  }

  /**
   * 記錄錯誤到日誌
   * 
   * @param error 標準化錯誤
   * @private
   */
  private logError(error: StandardizedError): void {
    // 添加到錯誤日誌
    this.errorLog.unshift(error);
    
    // 限制日誌大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }
    
    // 更新統計
    this.updateErrorStatistics();
    
    // 控制台記錄
    console.error('錯誤已記錄:', {
      errorCode: error.errorCode,
      errorType: error.errorType,
      severity: error.severity,
      message: error.technicalMessage,
      timestamp: error.timestamp
    });
  }

  /**
   * 顯示使用者通知
   * 
   * @param error 標準化錯誤
   * @private
   */
  private showUserNotification(error: StandardizedError): void {
    // 這裡應該整合實際的通知系統
    console.warn('使用者通知:', error.userMessage);
    
    // 可以使用 Angular Material Snackbar 或其他通知元件
    // this.snackBar.open(error.userMessage, '關閉', {
    //   duration: 5000,
    //   horizontalPosition: 'center',
    //   verticalPosition: 'top',
    //   panelClass: this.getSeverityClass(error.severity)
    // });
  }

  /**
   * 產生錯誤代碼
   * 
   * @param error 原始錯誤
   * @returns 錯誤代碼
   * @private
   */
  private generateErrorCode(error: any): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    
    if (error instanceof HttpErrorResponse) {
      return `HTTP_${error.status}_${timestamp}_${random}`;
    } else if (error instanceof Error) {
      return `${error.name.toUpperCase()}_${timestamp}_${random}`;
    } else {
      return `GENERAL_${timestamp}_${random}`;
    }
  }

  /**
   * 取得使用者友善訊息
   * 
   * @param errorCode 錯誤代碼
   * @param defaultMessage 預設訊息
   * @returns 使用者友善訊息
   * @private
   */
  private getUserFriendlyMessage(errorCode: string, defaultMessage: string): string {
    // 這裡可以根據錯誤代碼映射到預定義的使用者友善訊息
    const messageMap: Record<string, string> = {
      'INVALID_CERTIFICATE': '憑證格式不正確或已過期',
      'COMPANY_NOT_FOUND': '找不到指定的公司資訊',
      'UNAUTHORIZED_ACCESS': '您沒有權限執行此操作',
      'NETWORK_ERROR': '網路連線發生問題',
      'SERVER_ERROR': '系統暫時無法處理您的請求'
    };
    
    return messageMap[errorCode] || defaultMessage || '系統發生錯誤，請稍後再試';
  }

  /**
   * 取得建議解決方案
   * 
   * @param errorCode 錯誤代碼
   * @returns 建議解決方案陣列
   * @private
   */
  private getSuggestedActions(errorCode: string): string[] {
    const actionMap: Record<string, string[]> = {
      'INVALID_CERTIFICATE': [
        '檢查憑證是否有效',
        '重新上傳憑證',
        '聯絡憑證發行機構'
      ],
      'COMPANY_NOT_FOUND': [
        '確認統一編號是否正確',
        '檢查公司是否已登記',
        '聯絡相關部門確認'
      ],
      'UNAUTHORIZED_ACCESS': [
        '重新登入系統',
        '聯絡管理員取得權限',
        '檢查使用者角色設定'
      ]
    };
    
    return actionMap[errorCode] || [
      '重新載入頁面',
      '稍後重試',
      '聯絡系統管理員'
    ];
  }

  /**
   * 初始化錯誤統計
   * 
   * @private
   */
  private initializeErrorStatistics(): void {
    const now = new Date();
    this.errorStatistics = {
      totalErrors: 0,
      errorsByType: {
        [ErrorType.NETWORK]: 0,
        [ErrorType.AUTHENTICATION]: 0,
        [ErrorType.AUTHORIZATION]: 0,
        [ErrorType.VALIDATION]: 0,
        [ErrorType.BUSINESS_LOGIC]: 0,
        [ErrorType.SERVER_ERROR]: 0,
        [ErrorType.TIMEOUT]: 0,
        [ErrorType.UNKNOWN]: 0
      },
      errorsBySeverity: {
        [ErrorSeverity.LOW]: 0,
        [ErrorSeverity.MEDIUM]: 0,
        [ErrorSeverity.HIGH]: 0,
        [ErrorSeverity.CRITICAL]: 0
      },
      recentErrors: [],
      timeRange: {
        startTime: now,
        endTime: now
      }
    };
  }

  /**
   * 更新錯誤統計
   * 
   * @private
   */
  private updateErrorStatistics(): void {
    this.errorStatistics.totalErrors = this.errorLog.length;
    
    // 重置計數
    Object.keys(this.errorStatistics.errorsByType).forEach(key => {
      this.errorStatistics.errorsByType[key as ErrorType] = 0;
    });
    Object.keys(this.errorStatistics.errorsBySeverity).forEach(key => {
      this.errorStatistics.errorsBySeverity[key as ErrorSeverity] = 0;
    });
    
    // 計算統計
    this.errorLog.forEach(error => {
      this.errorStatistics.errorsByType[error.errorType]++;
      this.errorStatistics.errorsBySeverity[error.severity]++;
    });
    
    // 更新最近錯誤
    this.errorStatistics.recentErrors = this.getRecentErrors(5);
    
    // 更新時間範圍
    if (this.errorLog.length > 0) {
      const timestamps = this.errorLog.map(e => e.timestamp.getTime());
      this.errorStatistics.timeRange = {
        startTime: new Date(Math.min(...timestamps)),
        endTime: new Date(Math.max(...timestamps))
      };
    }
  }

  /**
   * 設定全域錯誤處理器
   * 
   * @private
   */
  private setupGlobalErrorHandlers(): void {
    // 全域未處理的錯誤
    window.addEventListener('error', (event) => {
      this.handleGeneralError(
        event.error,
        'GLOBAL_ERROR_HANDLER',
        { sendErrorReport: true }
      ).subscribe();
    });
    
    // 全域未處理的Promise拒絕
    window.addEventListener('unhandledrejection', (event) => {
      this.handleGeneralError(
        event.reason,
        'UNHANDLED_PROMISE_REJECTION',
        { sendErrorReport: true }
      ).subscribe();
    });
  }

  /**
   * 產生報告ID
   * 
   * @returns 報告ID
   * @private
   */
  private generateReportId(): string {
    return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 取得瀏覽器資訊
   * 
   * @returns 瀏覽器資訊
   * @private
   */
  private getBrowserInfo(): { name: string; version: string; platform: string } {
    const userAgent = navigator.userAgent;
    
    return {
      name: this.getBrowserName(userAgent),
      version: this.getBrowserVersion(userAgent),
      platform: navigator.platform
    };
  }

  /**
   * 取得瀏覽器名稱
   * 
   * @param userAgent 使用者代理字串
   * @returns 瀏覽器名稱
   * @private
   */
  private getBrowserName(userAgent: string): string {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('Opera')) return 'Opera';
    return 'Unknown';
  }

  /**
   * 取得瀏覽器版本
   * 
   * @param userAgent 使用者代理字串
   * @returns 瀏覽器版本
   * @private
   */
  private getBrowserVersion(userAgent: string): string {
    const match = userAgent.match(/(?:Chrome|Firefox|Safari|Edge|Opera)\/(\d+)/);
    return match ? match[1] : 'Unknown';
  }

  /**
   * 擷取應用程式狀態
   * 
   * @returns 應用程式狀態
   * @private
   */
  private captureApplicationState(): any {
    return {
      url: window.location.href,
      timestamp: new Date(),
      localStorage: this.getLocalStorageSnapshot(),
      sessionStorage: this.getSessionStorageSnapshot(),
      viewportSize: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }

  /**
   * 取得本地儲存快照
   * 
   * @returns 本地儲存快照
   * @private
   */
  private getLocalStorageSnapshot(): any {
    const snapshot: any = {};
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && !key.includes('password') && !key.includes('token')) {
          snapshot[key] = localStorage.getItem(key);
        }
      }
    } catch (error) {
      // 忽略存取錯誤
    }
    return snapshot;
  }

  /**
   * 取得會話儲存快照
   * 
   * @returns 會話儲存快照
   * @private
   */
  private getSessionStorageSnapshot(): any {
    const snapshot: any = {};
    try {
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && !key.includes('password') && !key.includes('token')) {
          snapshot[key] = sessionStorage.getItem(key);
        }
      }
    } catch (error) {
      // 忽略存取錯誤
    }
    return snapshot;
  }

  /**
   * 取得使用者行為追蹤
   * 
   * @returns 使用者行為陣列
   * @private
   */
  private getUserActions(): { action: string; timestamp: Date; details?: any }[] {
    // 這裡應該實作實際的使用者行為追蹤邏輯
    // 可以整合 Google Analytics 或其他追蹤系統
    return [];
  }
}