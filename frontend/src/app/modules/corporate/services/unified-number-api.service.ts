import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout, map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 公司基本資訊介面
 */
export interface CompanyBasicInfo {
  /** 統一編號 */
  unifiedNumber: string;
  /** 公司名稱 */
  companyName: string;
  /** 公司類型 */
  companyType: 'CORPORATION' | 'LIMITED' | 'PARTNERSHIP' | 'SOLE_PROPRIETORSHIP' | 'OTHER';
  /** 營業狀態 */
  businessStatus: 'ACTIVE' | 'SUSPENDED' | 'DISSOLVED' | 'MERGED' | 'UNKNOWN';
  /** 註冊地址 */
  registeredAddress: string;
  /** 負責人姓名 */
  representativeName: string;
  /** 負責人統一編號 */
  representativeId: string;
  /** 資本額 */
  capitalAmount: number;
  /** 設立日期 */
  establishDate: Date;
  /** 營業項目 */
  businessScope: string[];
}

/**
 * 統一編號驗證請求介面
 */
export interface UnifiedNumberVerificationRequest {
  /** 統一編號 */
  unifiedNumber: string;
  /** 驗證層級 */
  verificationLevel: 'BASIC' | 'DETAILED' | 'COMPREHENSIVE';
  /** 是否檢查營業狀態 */
  checkBusinessStatus?: boolean;
  /** 是否檢查資本額 */
  checkCapital?: boolean;
  /** 是否檢查負責人資訊 */
  checkRepresentative?: boolean;
  /** 額外驗證選項 */
  additionalOptions?: {
    /** 檢查分公司資訊 */
    includeBranches?: boolean;
    /** 檢查歷史變更記錄 */
    includeHistory?: boolean;
    /** 檢查關聯企業 */
    includeRelatedCompanies?: boolean;
  };
}

/**
 * 統一編號驗證回應介面
 */
export interface UnifiedNumberVerificationResponse {
  /** 驗證結果 */
  isValid: boolean;
  /** 驗證訊息 */
  message: string;
  /** 驗證時間 */
  verificationTime: Date;
  /** 公司基本資訊 */
  companyInfo?: CompanyBasicInfo;
  /** 驗證詳細結果 */
  verificationDetails: {
    /** 格式驗證 */
    formatValid: boolean;
    /** 檢查碼驗證 */
    checksumValid: boolean;
    /** 工商登記驗證 */
    registrationValid: boolean;
    /** 營業狀態檢查 */
    businessStatusCheck?: {
      isActive: boolean;
      status: string;
      statusDate?: Date;
    };
    /** 資本額檢查 */
    capitalCheck?: {
      amount: number;
      currency: string;
      lastUpdateDate?: Date;
    };
  };
  /** 警告訊息 */
  warnings?: string[];
  /** 錯誤代碼 */
  errorCode?: string;
}

/**
 * 公司搜尋請求介面
 */
export interface CompanySearchRequest {
  /** 搜尋關鍵字 (可為統一編號、公司名稱或負責人) */
  keyword: string;
  /** 搜尋類型 */
  searchType: 'UNIFIED_NUMBER' | 'COMPANY_NAME' | 'REPRESENTATIVE' | 'ALL';
  /** 營業狀態篩選 */
  statusFilter?: string[];
  /** 公司類型篩選 */
  typeFilter?: string[];
  /** 資本額範圍 */
  capitalRange?: {
    min?: number;
    max?: number;
  };
  /** 設立日期範圍 */
  establishDateRange?: {
    startDate?: Date;
    endDate?: Date;
  };
  /** 分頁設定 */
  pagination?: {
    page: number;
    pageSize: number;
  };
  /** 排序設定 */
  sorting?: {
    field: 'companyName' | 'establishDate' | 'capitalAmount';
    direction: 'ASC' | 'DESC';
  };
}

/**
 * 公司搜尋回應介面
 */
export interface CompanySearchResponse {
  /** 搜尋結果 */
  companies: CompanyBasicInfo[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
  /** 搜尋時間 */
  searchTime: Date;
}

/**
 * 公司詳細資訊介面
 */
export interface CompanyDetailInfo extends CompanyBasicInfo {
  /** 分公司資訊 */
  branches?: {
    branchId: string;
    branchName: string;
    branchAddress: string;
    manager: string;
    establishDate: Date;
    status: string;
  }[];
  /** 變更歷史 */
  changeHistory?: {
    changeDate: Date;
    changeType: string;
    changeDescription: string;
    beforeValue?: string;
    afterValue?: string;
  }[];
  /** 關聯企業 */
  relatedCompanies?: {
    relatedCompanyId: string;
    relatedCompanyName: string;
    relationship: string;
    relationshipDate?: Date;
  }[];
  /** 營業項目詳細 */
  detailedBusinessScope?: {
    primaryCode: string;
    primaryDescription: string;
    secondaryCodes: string[];
    secondaryDescriptions: string[];
  };
  /** 財務資訊 */
  financialInfo?: {
    paidUpCapital: number;
    authorizedCapital: number;
    lastAuditDate?: Date;
    auditFirm?: string;
  };
}

/**
 * 法人模組統一編號驗證API服務
 * 
 * 提供完整的統一編號驗證與公司資訊查詢功能，包括：
 * - 統一編號格式與有效性驗證
 * - 工商登記資訊查詢
 * - 公司營業狀態檢查
 * - 公司詳細資訊查詢
 * - 公司搜尋功能
 * 
 * @example
 * ```typescript
 * constructor(private unifiedNumberApi: UnifiedNumberApiService) {}
 * 
 * async verifyCompany(unifiedNumber: string) {
 *   try {
 *     const result = await this.unifiedNumberApi.verifyUnifiedNumber({
 *       unifiedNumber,
 *       verificationLevel: 'COMPREHENSIVE',
 *       checkBusinessStatus: true
 *     }).toPromise();
 *     
 *     if (result.isValid) {
 *       console.log('公司驗證成功', result.companyInfo);
 *     }
 *   } catch (error) {
 *     console.error('公司驗證失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class UnifiedNumberApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/v1/corporate/unified-number`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2; // 重試次數

  constructor(private http: HttpClient) {}

  /**
   * 驗證統一編號
   * 
   * @param request 統一編號驗證請求
   * @returns 驗證結果Observable
   * 
   * @example
   * ```typescript
   * const request: UnifiedNumberVerificationRequest = {
   *   unifiedNumber: '12345678',
   *   verificationLevel: 'COMPREHENSIVE',
   *   checkBusinessStatus: true,
   *   checkCapital: true
   * };
   * 
   * this.unifiedNumberApi.verifyUnifiedNumber(request).subscribe({
   *   next: (response) => {
   *     if (response.isValid) {
   *       console.log('統一編號有效', response.companyInfo);
   *     } else {
   *       console.log('統一編號無效', response.message);
   *     }
   *   },
   *   error: (error) => console.error('驗證失敗', error)
   * });
   * ```
   */
  verifyUnifiedNumber(request: UnifiedNumberVerificationRequest): Observable<UnifiedNumberVerificationResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<UnifiedNumberVerificationResponse>(
      `${this.apiUrl}/verify`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 快速驗證統一編號格式
   * 
   * @param unifiedNumber 統一編號
   * @returns 格式驗證結果Observable
   */
  quickVerifyFormat(unifiedNumber: string): Observable<{ isValid: boolean; message: string }> {
    const headers = this.getHeaders();
    
    return this.http.post<{ isValid: boolean; message: string }>(
      `${this.apiUrl}/quick-verify`,
      { unifiedNumber },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得公司基本資訊
   * 
   * @param unifiedNumber 統一編號
   * @returns 公司基本資訊Observable
   */
  getCompanyBasicInfo(unifiedNumber: string): Observable<CompanyBasicInfo> {
    const headers = this.getHeaders();
    
    return this.http.get<CompanyBasicInfo>(
      `${this.apiUrl}/company/${encodeURIComponent(unifiedNumber)}/basic`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得公司詳細資訊
   * 
   * @param unifiedNumber 統一編號
   * @param includeOptions 包含選項
   * @returns 公司詳細資訊Observable
   */
  getCompanyDetailInfo(
    unifiedNumber: string, 
    includeOptions?: {
      includeBranches?: boolean;
      includeHistory?: boolean;
      includeRelatedCompanies?: boolean;
      includeFinancial?: boolean;
    }
  ): Observable<CompanyDetailInfo> {
    const headers = this.getHeaders();
    const params = includeOptions ? this.buildQueryParams(includeOptions) : {};
    
    return this.http.get<CompanyDetailInfo>(
      `${this.apiUrl}/company/${encodeURIComponent(unifiedNumber)}/detail`,
      { headers, params }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 搜尋公司
   * 
   * @param request 搜尋請求
   * @returns 搜尋結果Observable
   */
  searchCompanies(request: CompanySearchRequest): Observable<CompanySearchResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<CompanySearchResponse>(
      `${this.apiUrl}/search`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 檢查公司營業狀態
   * 
   * @param unifiedNumber 統一編號
   * @returns 營業狀態Observable
   */
  checkBusinessStatus(unifiedNumber: string): Observable<{
    status: string;
    isActive: boolean;
    statusDate?: Date;
    statusReason?: string;
    nextReviewDate?: Date;
  }> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      status: string;
      isActive: boolean;
      statusDate?: Date;
      statusReason?: string;
      nextReviewDate?: Date;
    }>(
      `${this.apiUrl}/company/${encodeURIComponent(unifiedNumber)}/status`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 批次驗證統一編號
   * 
   * @param unifiedNumbers 統一編號陣列
   * @returns 批次驗證結果Observable
   */
  batchVerifyUnifiedNumbers(unifiedNumbers: string[]): Observable<{
    results: {
      unifiedNumber: string;
      isValid: boolean;
      message: string;
      companyName?: string;
      businessStatus?: string;
    }[];
    summary: {
      total: number;
      valid: number;
      invalid: number;
      processTime: Date;
    };
  }> {
    const headers = this.getHeaders();
    
    return this.http.post<{
      results: {
        unifiedNumber: string;
        isValid: boolean;
        message: string;
        companyName?: string;
        businessStatus?: string;
      }[];
      summary: {
        total: number;
        valid: number;
        invalid: number;
        processTime: Date;
      };
    }>(
      `${this.apiUrl}/batch-verify`,
      { unifiedNumbers },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得統一編號格式規則
   * 
   * @returns 格式規則Observable
   */
  getFormatRules(): Observable<{
    pattern: string;
    length: number;
    checksumAlgorithm: string;
    description: string;
    examples: string[];
  }> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      pattern: string;
      length: number;
      checksumAlgorithm: string;
      description: string;
      examples: string[];
    }>(
      `${this.apiUrl}/format-rules`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 本地端統一編號格式驗證
   * 
   * @param unifiedNumber 統一編號
   * @returns 格式是否有效
   */
  validateFormatLocally(unifiedNumber: string): boolean {
    if (!unifiedNumber || typeof unifiedNumber !== 'string') {
      return false;
    }

    // 移除空白字元
    const cleanNumber = unifiedNumber.replace(/\s/g, '');
    
    // 檢查長度（統一編號為8位數）
    if (cleanNumber.length !== 8) {
      return false;
    }

    // 檢查是否為純數字
    if (!/^\d{8}$/.test(cleanNumber)) {
      return false;
    }

    // 檢查碼驗證
    return this.validateChecksum(cleanNumber);
  }

  /**
   * 取得HTTP標頭
   * 
   * @returns HttpHeaders
   * @private
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  /**
   * 建構查詢參數
   * 
   * @param request 請求物件
   * @returns 查詢參數物件
   * @private
   */
  private buildQueryParams(request: any): Record<string, string> {
    const params: Record<string, string> = {};
    
    Object.keys(request).forEach(key => {
      const value = request[key];
      if (value !== null && value !== undefined) {
        if (Array.isArray(value)) {
          params[key] = value.join(',');
        } else if (typeof value === 'object') {
          params[key] = JSON.stringify(value);
        } else {
          params[key] = value.toString();
        }
      }
    });
    
    return params;
  }

  /**
   * 統一編號檢查碼驗證
   * 
   * @param unifiedNumber 8位統一編號
   * @returns 檢查碼是否正確
   * @private
   */
  private validateChecksum(unifiedNumber: string): boolean {
    const multipliers = [1, 2, 1, 2, 1, 2, 4, 1];
    let sum = 0;

    for (let i = 0; i < 8; i++) {
      const digit = parseInt(unifiedNumber[i]);
      let product = digit * multipliers[i];
      
      // 如果乘積大於9，將十位數和個位數相加
      if (product > 9) {
        product = Math.floor(product / 10) + (product % 10);
      }
      
      sum += product;
    }

    // 檢查碼為10減去總和除以10的餘數，如果結果為10則檢查碼為0
    const checksum = (10 - (sum % 10)) % 10;
    const lastDigit = parseInt(unifiedNumber[7]);

    return checksum === lastDigit;
  }

  /**
   * 統一錯誤處理
   * 
   * @param error HTTP錯誤回應
   * @returns 錯誤Observable
   * @private
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '統一編號API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '統一編號格式錯誤或請求參數不正確';
          errorCode = 'INVALID_UNIFIED_NUMBER';
          break;
        case 401:
          errorMessage = '沒有權限查詢統一編號資訊';
          errorCode = 'UNAUTHORIZED';
          break;
        case 403:
          errorMessage = '統一編號查詢權限不足';
          errorCode = 'FORBIDDEN';
          break;
        case 404:
          errorMessage = '找不到對應的統一編號資訊';
          errorCode = 'COMPANY_NOT_FOUND';
          break;
        case 408:
          errorMessage = '統一編號查詢請求超時';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 429:
          errorMessage = '統一編號查詢請求過於頻繁，請稍後再試';
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorMessage = '統一編號服務內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorMessage = '統一編號服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        case 503:
          errorMessage = '統一編號服務正在維護中';
          errorCode = 'SERVICE_MAINTENANCE';
          break;
        default:
          errorMessage = `統一編號服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('統一編號API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    });
  }
}