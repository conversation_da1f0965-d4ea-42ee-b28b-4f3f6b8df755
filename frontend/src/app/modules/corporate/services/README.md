# Corporate Services - 法人解款服務

![Services](https://img.shields.io/badge/Services-9-blue) ![API Integration](https://img.shields.io/badge/API-30%20Endpoints-green) ![Certificate](https://img.shields.io/badge/Certificate-PC/SC-orange) ![Enterprise](https://img.shields.io/badge/Enterprise-Grade-red)

## 🎯 服務概述

Corporate Services 包含法人解款模組的所有企業級業務邏輯服務，提供工商憑證整合、統一編號驗證、批次處理、企業資料管理等核心功能。這些服務確保企業解款流程的專業性、安全性和合規性。

## 📊 服務統計

- **總服務數**: 9個企業級服務
- **API端點**: 30個RESTful接口
- **憑證整合**: PC/SC讀卡機 + 工商憑證驗證
- **批次處理**: 支援大額多筆交易處理

## 🗂️ 服務架構

```
services/
├── corporate-api.service.ts       # 企業API整合服務 - 30個RESTful接口
├── corporate.service.ts           # 企業核心業務服務 - 流程與狀態管理
├── certificate.service.ts        # 工商憑證服務 - PC/SC讀卡機整合
├── certificate-api.service.ts    # 憑證API服務 - 憑證驗證接口
├── unified-number-api.service.ts # 統編驗證服務 - 政府資料庫查詢
├── application-api.service.ts    # 申請API服務 - 企業申請流程
├── bank-list-api.service.ts      # 銀行清單服務 - 企業銀行資料
├── security.service.ts           # 企業安全服務 - 加密與權限
├── error-handler.service.ts      # 錯誤處理服務 - 企業級錯誤管理
└── *.spec.ts                     # 對應測試檔案
```

## 🔧 核心服務詳解

### 1. [Corporate API Service](corporate-api.service.ts) - 企業API整合服務

**功能**: 完整的企業級RESTful API整合服務，提供30個API端點

**主要特色**:
- 🏢 企業級API架構設計
- 🔐 工商憑證安全傳輸
- 📊 批次處理API支援
- ⚡ 企業級效能優化

**API端點分類**:
```typescript
// 企業條款與同意相關 (4個端點)
agreeToTerms(request: CorporateTermsAgreementRequest): Observable<CorporateTermsAgreementResponse>
getCorporateTermsVersion(): Observable<TermsVersionResponse>
recordCorporateAgreement(agreement: CorporateAgreementRecord): Observable<BaseApiResponse>
getTransactionLimits(): Observable<TransactionLimitsResponse>

// 工商憑證驗證相關 (8個端點)
validateCertificate(request: CertificateValidationRequest): Observable<CertificateValidationResponse>
readCertificateInfo(cardReader: string): Observable<CertificateInfoResponse>
verifyPIN(request: PINVerificationRequest): Observable<PINVerificationResponse>
signTransaction(request: TransactionSigningRequest): Observable<SigningResponse>
getCertificateChain(serialNumber: string): Observable<CertificateChainResponse>
checkCertificateRevocation(serialNumber: string): Observable<RevocationResponse>
renewCertificate(request: CertificateRenewalRequest): Observable<RenewalResponse>
lockCertificate(serialNumber: string, reason: string): Observable<LockResponse>

// 企業資料查詢相關 (6個端點)
verifyUnifiedNumber(request: UnifiedNumberVerificationRequest): Observable<UnifiedNumberVerificationResponse>
getCompanyInfo(unifiedNumber: string): Observable<CompanyInfoResponse>
getRepresentativeInfo(unifiedNumber: string): Observable<RepresentativeInfoResponse>
validateBusinessStatus(unifiedNumber: string): Observable<BusinessStatusResponse>
getCompanyHistory(unifiedNumber: string): Observable<CompanyHistoryResponse>
searchCompanyByName(companyName: string): Observable<CompanySearchResponse>

// 批次匯款處理相關 (7個端點)
searchCorporateRemittances(request: CorporateRemittanceSearchRequest): Observable<CorporateRemittanceSearchResponse>
getBatchRemittanceList(criteria: BatchSearchCriteria): Observable<BatchRemittanceListResponse>
selectBatchRemittances(request: BatchSelectionRequest): Observable<BatchSelectionResponse>
calculateBatchAmount(request: BatchCalculationRequest): Observable<BatchCalculationResponse>
confirmBatchSelection(request: BatchConfirmationRequest): Observable<BatchConfirmationResponse>
submitBatchApplication(request: BatchApplicationRequest): Observable<BatchApplicationResponse>
getBatchStatus(batchId: string): Observable<BatchStatusResponse>

// 企業申請管理相關 (5個端點)
createCorporateApplication(request: CorporateApplicationRequest): Observable<ApplicationCreationResponse>
updateCorporateApplication(request: ApplicationUpdateRequest): Observable<ApplicationUpdateResponse>
getCorporateApplicationStatus(applicationId: string): Observable<ApplicationStatusResponse>
cancelCorporateApplication(applicationId: string, reason: string): Observable<CancellationResponse>
getApplicationHistory(companyId: string): Observable<ApplicationHistoryResponse>
```

**企業級錯誤處理**:
```typescript
private handleCorporateApiError(error: HttpErrorResponse): Observable<never> {
  let errorMessage = '企業服務暫時無法使用';
  let errorCode = 'CORPORATE_SYSTEM_ERROR';
  
  switch (error.status) {
    case 400:
      errorMessage = '企業資料格式錯誤，請檢查統一編號和公司資訊';
      errorCode = 'CORPORATE_DATA_INVALID';
      break;
    case 401:
      errorMessage = '工商憑證驗證失敗，請重新插入工商憑證卡';
      errorCode = 'CERTIFICATE_AUTH_FAILED';
      break;
    case 403:
      errorMessage = '企業權限不足，請確認法定代表人身份';
      errorCode = 'CORPORATE_PERMISSION_DENIED';
      break;
    case 409:
      errorMessage = '企業申請衝突，可能已有進行中的申請';
      errorCode = 'CORPORATE_CONFLICT';
      break;
    case 422:
      errorMessage = '企業營業狀態異常，無法進行解款申請';
      errorCode = 'BUSINESS_STATUS_INVALID';
      break;
  }
  
  // 記錄企業級錯誤日誌
  this.logCorporateError(error, errorCode, errorMessage);
  
  return throwError(() => ({
    code: errorCode,
    message: errorMessage,
    details: error.error
  }));
}
```

### 2. [Certificate Service](certificate.service.ts) - 工商憑證服務

**功能**: PC/SC讀卡機整合和工商憑證處理

**主要特色**:
- 💳 PC/SC讀卡機支援
- 🔐 工商憑證讀取與驗證
- 🔢 PIN碼安全管理
- ✍️ 數位簽章功能

**讀卡機整合架構**:
```typescript
export class CertificateService {
  private pcscContext: any;
  private connectedReaders: Map<string, any> = new Map();
  
  // 檢測讀卡機
  async detectReaders(): Promise<string[]> {
    try {
      if (!this.pcscContext) {
        this.pcscContext = await this.initializePCSC();
      }
      
      const readers = await this.pcscContext.listReaders();
      
      return readers.filter(reader => 
        reader.includes('Smart card') || 
        reader.includes('CCID') ||
        reader.includes('ACR')
      );
    } catch (error) {
      throw new Error('無法檢測到讀卡機，請確認讀卡機已正確連接');
    }
  }
  
  // 連接到讀卡機
  async connectToReader(readerName: string): Promise<ReaderConnection> {
    try {
      const connection = await this.pcscContext.connect(readerName, {
        mode: 'SCARD_SHARE_SHARED',
        protocol: 'SCARD_PROTOCOL_T0 | SCARD_PROTOCOL_T1'
      });
      
      this.connectedReaders.set(readerName, connection);
      
      return {
        readerName,
        isConnected: true,
        cardPresent: await this.checkCardPresence(connection),
        cardType: await this.identifyCardType(connection)
      };
    } catch (error) {
      throw new Error(`無法連接到讀卡機 ${readerName}`);
    }
  }
  
  // 讀取憑證資訊
  async readCertificate(readerName: string): Promise<CertificateInfo> {
    const connection = this.connectedReaders.get(readerName);
    if (!connection) {
      throw new Error('讀卡機未連接');
    }
    
    try {
      // 發送APDU指令讀取憑證
      const certificateData = await this.sendAPDUCommand(connection, {
        cla: 0x00,
        ins: 0xA4,  // SELECT FILE
        p1: 0x04,
        p2: 0x00,
        data: [0xD1, 0x58, 0x00, 0x00, 0x33, 0x01] // 工商憑證AID
      });
      
      // 解析憑證資料
      const parsedCert = this.parseCertificateData(certificateData);
      
      return {
        serialNumber: parsedCert.serialNumber,
        issuer: parsedCert.issuer,
        subject: parsedCert.subject,
        validFrom: new Date(parsedCert.validFrom),
        validTo: new Date(parsedCert.validTo),
        unifiedNumber: this.extractUnifiedNumber(parsedCert),
        companyName: this.extractCompanyName(parsedCert),
        representativeName: this.extractRepresentativeName(parsedCert),
        publicKey: parsedCert.publicKey
      };
    } catch (error) {
      throw new Error('讀取工商憑證失敗，請檢查憑證卡是否正確插入');
    }
  }
  
  // PIN碼驗證
  async verifyPIN(readerName: string, pin: string): Promise<PINVerificationResult> {
    const connection = this.connectedReaders.get(readerName);
    if (!connection) {
      throw new Error('讀卡機未連接');
    }
    
    try {
      // 發送PIN驗證APDU指令
      const pinBytes = this.encodePIN(pin);
      const response = await this.sendAPDUCommand(connection, {
        cla: 0x00,
        ins: 0x20,  // VERIFY
        p1: 0x00,
        p2: 0x81,   // PIN reference
        data: pinBytes
      });
      
      const isValid = response.sw1 === 0x90 && response.sw2 === 0x00;
      const remainingAttempts = this.calculateRemainingAttempts(response);
      
      return {
        isValid,
        remainingAttempts,
        isLocked: remainingAttempts === 0,
        message: isValid ? 'PIN碼驗證成功' : `PIN碼錯誤，剩餘 ${remainingAttempts} 次嘗試機會`
      };
    } catch (error) {
      throw new Error('PIN碼驗證過程發生錯誤');
    }
  }
  
  // 數位簽章
  async signData(readerName: string, data: string): Promise<string> {
    const connection = this.connectedReaders.get(readerName);
    if (!connection) {
      throw new Error('讀卡機未連接');
    }
    
    try {
      // 計算資料雜湊
      const hash = await this.calculateSHA256Hash(data);
      
      // 發送簽章APDU指令
      const signature = await this.sendAPDUCommand(connection, {
        cla: 0x00,
        ins: 0x2A,  // PERFORM SECURITY OPERATION
        p1: 0x9E,
        p2: 0x9A,
        data: hash
      });
      
      return this.encodeSignature(signature.data);
    } catch (error) {
      throw new Error('數位簽章失敗');
    }
  }
}
```

### 3. [Unified Number API Service](unified-number-api.service.ts) - 統編驗證服務

**功能**: 統一編號驗證和政府資料庫查詢

**主要特色**:
- 🏛️ 政府資料庫整合
- 🔍 即時統編驗證
- 📊 企業狀態查詢
- ⚡ 快取機制優化

**統編驗證系統**:
```typescript
export class UnifiedNumberApiService {
  // 統一編號驗證
  async verifyUnifiedNumber(request: UnifiedNumberVerificationRequest): Observable<UnifiedNumberVerificationResponse> {
    // 先檢查本地快取
    const cacheKey = `unified_number_${request.unifiedNumber}`;
    const cached = this.cacheService.get<UnifiedNumberVerificationResponse>(cacheKey);
    
    if (cached) {
      return of(cached);
    }
    
    // 格式預檢查
    if (!this.isValidUnifiedNumberFormat(request.unifiedNumber)) {
      return of({
        isValid: false,
        verificationDetails: {
          formatValid: false,
          checksumValid: false,
          companyExists: false,
          statusActive: false
        },
        errorMessage: '統一編號格式不正確'
      });
    }
    
    // 向政府API查詢
    return this.http.post<UnifiedNumberVerificationResponse>('/api/corporate/verify-unified-number', {
      unifiedNumber: request.unifiedNumber,
      verificationLevel: request.verificationLevel || 'COMPREHENSIVE',
      includeCompanyData: true,
      includeRepresentativeData: true
    }).pipe(
      tap(response => {
        // 快取驗證結果 (5分鐘)
        if (response.isValid) {
          this.cacheService.set(cacheKey, response, 300);
        }
      }),
      catchError(this.handleUnifiedNumberError.bind(this))
    );
  }
  
  // 統一編號格式驗證
  private isValidUnifiedNumberFormat(unifiedNumber: string): boolean {
    // 8位數字格式檢查
    if (!/^\d{8}$/.test(unifiedNumber)) {
      return false;
    }
    
    // 檢查碼驗證
    return this.validateUnifiedNumberChecksum(unifiedNumber);
  }
  
  // 統一編號檢查碼演算法
  private validateUnifiedNumberChecksum(unifiedNumber: string): boolean {
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    const digits = unifiedNumber.split('').map(Number);
    
    let sum = 0;
    for (let i = 0; i < 7; i++) {
      const product = digits[i] * weights[i];
      sum += Math.floor(product / 10) + (product % 10);
    }
    
    const checkDigit = digits[7];
    const calculatedCheck = (10 - (sum % 10)) % 10;
    
    // 特殊情況：如果計算結果是10，檢查碼應該是0
    return checkDigit === calculatedCheck || 
           (calculatedCheck === 10 && checkDigit === 0);
  }
  
  // 企業資料查詢
  async getCompanyDetails(unifiedNumber: string): Observable<CompanyDetailsResponse> {
    return this.http.get<CompanyDetailsResponse>(`/api/corporate/company-details/${unifiedNumber}`)
      .pipe(
        map(response => ({
          ...response,
          // 標準化企業資料格式
          companyName: this.normalizeCompanyName(response.companyName),
          businessAddress: this.normalizeAddress(response.businessAddress),
          industry: this.normalizeIndustryCode(response.industry)
        })),
        catchError(this.handleCompanyDetailsError.bind(this))
      );
  }
}
```

### 4. [Corporate Service](corporate.service.ts) - 企業核心業務服務

**功能**: 企業解款流程的核心業務邏輯和狀態管理

**主要特色**:
- 🏢 企業級流程管理
- 📊 批次處理邏輯
- 💼 企業狀態追蹤
- 🔐 企業權限控制

**企業狀態管理**:
```typescript
interface CorporateApplicationState {
  // 企業基本資訊
  companyInfo: CompanyInfo;
  representativeInfo: RepresentativeInfo;
  certificateInfo?: CertificateInfo;
  
  // 流程狀態
  currentStep: CorporateStep;
  totalSteps: number;
  isCompleted: boolean;
  
  // 批次處理
  selectedRemittances: CorporateRemittance[];
  batchProcessing?: BatchProcessing;
  batchCalculation?: BatchCalculation;
  
  // 申請資訊
  applicationId?: string;
  submissionStatus: CorporateSubmissionStatus;
  lastUpdated: Date;
  
  // 企業權限
  transactionLimits: TransactionLimits;
  approvalRequired: boolean;
  specialPermissions: string[];
}

export class CorporateService {
  // 企業流程驗證
  async validateCorporateStep(step: CorporateStep): Promise<ValidationResult> {
    switch (step) {
      case CorporateStep.CERTIFICATE_VERIFICATION:
        return this.validateCertificateStep();
      case CorporateStep.COMPANY_INFO:
        return this.validateCompanyInfoStep();
      case CorporateStep.BATCH_SELECTION:
        return this.validateBatchSelectionStep();
      case CorporateStep.AMOUNT_CONFIRMATION:
        return this.validateAmountConfirmationStep();
      default:
        return { isValid: true };
    }
  }
  
  // 批次處理管理
  async processBatchSelection(remittances: CorporateRemittance[]): Promise<BatchProcessingResult> {
    // 驗證批次選擇
    const validation = this.validateBatchSelection(remittances);
    if (!validation.isValid) {
      throw new Error(validation.errorMessage);
    }
    
    // 計算批次總額
    const totalAmount = remittances.reduce((sum, r) => sum + r.amount, 0);
    
    // 檢查企業限額
    await this.checkCorporateTransactionLimits(totalAmount);
    
    // 計算批次手續費
    const batchFees = this.calculateBatchFees(remittances);
    
    // 建立批次處理記錄
    const batchProcessing: BatchProcessing = {
      batchId: this.generateBatchId(),
      totalCount: remittances.length,
      totalAmount: totalAmount,
      selectedRemittances: remittances,
      processingFee: batchFees.total,
      netAmount: totalAmount - batchFees.total,
      createdAt: new Date(),
      status: 'PENDING'
    };
    
    this.updateCorporateState({ batchProcessing });
    
    return {
      batchId: batchProcessing.batchId,
      isSuccessful: true,
      batchSummary: batchProcessing
    };
  }
  
  // 企業限額檢查
  private async checkCorporateTransactionLimits(amount: number): Promise<void> {
    const limits = this.getCurrentTransactionLimits();
    
    if (amount > limits.singleTransactionLimit) {
      throw new Error(`單筆交易金額超過限額 ${limits.singleTransactionLimit.toLocaleString()} 元`);
    }
    
    // 檢查每日累計限額
    const todayTotal = await this.getTodayTransactionTotal();
    if (todayTotal + amount > limits.dailyLimit) {
      throw new Error(`今日交易金額將超過每日限額 ${limits.dailyLimit.toLocaleString()} 元`);
    }
    
    // 檢查每月累計限額
    const monthlyTotal = await this.getMonthlyTransactionTotal();
    if (monthlyTotal + amount > limits.monthlyLimit) {
      throw new Error(`本月交易金額將超過每月限額 ${limits.monthlyLimit.toLocaleString()} 元`);
    }
  }
}
```

### 5. [Security Service](security.service.ts) - 企業安全服務

**功能**: 企業級安全機制和權限管理

**主要特色**:
- 🔐 資料加密與解密
- 🛡️ 企業權限控制
- 📝 稽核日誌記錄
- 🔍 安全威脅檢測

**安全機制實作**:
```typescript
export class SecurityService {
  // 企業資料加密
  async encryptCorporateData(data: any, certificateInfo: CertificateInfo): Promise<string> {
    try {
      // 使用企業憑證公鑰加密
      const publicKey = await this.extractPublicKey(certificateInfo);
      const encrypted = await this.rsaEncrypt(JSON.stringify(data), publicKey);
      
      // 加入時間戳記和數位簽章
      const signedData = await this.signEncryptedData(encrypted, certificateInfo);
      
      return this.base64Encode(signedData);
    } catch (error) {
      throw new Error('企業資料加密失敗');
    }
  }
  
  // 企業權限驗證
  async verifyCorpoitePermissions(action: string, context: CorporateContext): Promise<PermissionResult> {
    const permissions = await this.getCorporatePermissions(context.companyId);
    
    // 檢查基本權限
    if (!permissions.actions.includes(action)) {
      return {
        isAllowed: false,
        reason: 'INSUFFICIENT_PERMISSIONS',
        requiredRole: this.getRequiredRole(action)
      };
    }
    
    // 檢查特殊限制
    if (action === 'LARGE_AMOUNT_TRANSACTION') {
      const amountCheck = await this.checkLargeAmountPermission(context);
      if (!amountCheck.isAllowed) {
        return amountCheck;
      }
    }
    
    // 檢查時間限制
    if (!this.isWithinBusinessHours(action)) {
      return {
        isAllowed: false,
        reason: 'OUTSIDE_BUSINESS_HOURS'
      };
    }
    
    return { isAllowed: true };
  }
  
  // 記錄企業稽核日誌
  async logCorporateAudit(event: CorporateAuditEvent): Promise<void> {
    const auditLog: CorporateAuditLog = {
      eventId: this.generateEventId(),
      companyId: event.companyId,
      userId: event.userId,
      action: event.action,
      timestamp: new Date(),
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      certificateSerial: event.certificateSerial,
      transactionAmount: event.transactionAmount,
      result: event.result,
      details: event.details,
      riskLevel: this.calculateRiskLevel(event)
    };
    
    // 發送到稽核系統
    await this.sendToAuditSystem(auditLog);
    
    // 高風險事件立即通知
    if (auditLog.riskLevel === 'HIGH') {
      await this.notifySecurityTeam(auditLog);
    }
  }
}
```

## 🔄 企業級服務協作

### 服務依賴關係
```mermaid
graph TD
    A[Corporate Service] --> B[Corporate API Service]
    A --> C[Certificate Service]
    A --> D[Unified Number API Service]
    A --> E[Security Service]
    
    C --> F[PC/SC Hardware]
    D --> G[Government Database]
    E --> H[Audit System]
    
    B --> I[Enterprise Backend]
    
    J[Corporate Components] --> A
    J --> C
    J --> E
```

### 批次處理流程
```typescript
// 企業批次處理的完整流程
export class CorporateBatchProcessor {
  async processBatchRemittance(selections: RemittanceSelection[]): Promise<BatchResult> {
    // 1. 憑證驗證
    const certValidation = await this.certificateService.validateCurrentCertificate();
    if (!certValidation.isValid) {
      throw new Error('工商憑證驗證失敗');
    }
    
    // 2. 批次資料驗證
    const batchValidation = await this.corporateService.validateBatchSelection(selections);
    if (!batchValidation.isValid) {
      throw new Error(batchValidation.errorMessage);
    }
    
    // 3. 企業權限檢查
    const permissionCheck = await this.securityService.verifyBatchPermissions(selections);
    if (!permissionCheck.isAllowed) {
      throw new Error('企業權限不足，無法進行批次處理');
    }
    
    // 4. 金額計算與限額檢查
    const calculation = await this.corporateApiService.calculateBatchAmount(selections);
    
    // 5. 數位簽章
    const signature = await this.certificateService.signBatchData(calculation);
    
    // 6. 提交批次申請
    return await this.corporateApiService.submitBatchApplication({
      selections,
      calculation,
      signature,
      certificateInfo: certValidation.certificateInfo
    });
  }
}
```

## 🧪 企業級測試

### 工商憑證測試
```typescript
describe('Certificate Service', () => {
  let service: CertificateService;
  let mockPCSC: jasmine.SpyObj<any>;
  
  beforeEach(() => {
    // 模擬PC/SC環境
    mockPCSC = jasmine.createSpyObj('PCSC', ['listReaders', 'connect']);
    
    TestBed.configureTestingModule({
      providers: [
        CertificateService,
        { provide: 'PCSC', useValue: mockPCSC }
      ]
    });
  });
  
  it('should detect smart card readers', async () => {
    mockPCSC.listReaders.and.returnValue(['ACR122U PCSC Reader']);
    
    const readers = await service.detectReaders();
    
    expect(readers).toContain('ACR122U PCSC Reader');
  });
  
  it('should read certificate information', async () => {
    const mockCertData = new Uint8Array([/* mock certificate data */]);
    mockPCSC.connect.and.returnValue({
      transmit: jasmine.createSpy().and.returnValue(mockCertData)
    });
    
    const certInfo = await service.readCertificate('ACR122U PCSC Reader');
    
    expect(certInfo.serialNumber).toBeDefined();
    expect(certInfo.unifiedNumber).toMatch(/^\d{8}$/);
  });
});
```

### 批次處理測試
```typescript
describe('Corporate Batch Processing', () => {
  it('should calculate batch total correctly', () => {
    const remittances = [
      { amount: 100000, currency: 'USD' },
      { amount: 200000, currency: 'USD' },
      { amount: 150000, currency: 'USD' }
    ];
    
    const result = corporateService.calculateBatchTotal(remittances);
    
    expect(result.totalAmount).toBe(450000);
    expect(result.totalCount).toBe(3);
  });
  
  it('should apply corporate batch discount', () => {
    const batchSize = 10;
    const amount = 1000000;
    
    const fees = corporateService.calculateBatchFees(batchSize, amount);
    
    expect(fees.discount).toBeGreaterThan(0);
    expect(fees.finalAmount).toBeLessThan(amount);
  });
});
```

## 📋 企業配置

### 企業限額配置
```typescript
export const CORPORATE_LIMITS_CONFIG = {
  TRANSACTION_LIMITS: {
    SINGLE_MAX: 5000000,           // 單筆限額: 500萬
    DAILY_MAX: 10000000,           // 每日限額: 1000萬
    MONTHLY_MAX: 100000000,        // 每月限額: 1億
    YEARLY_MAX: 1000000000         // 每年限額: 10億
  },
  
  BATCH_PROCESSING: {
    MIN_BATCH_SIZE: 2,             // 最小批次大小
    MAX_BATCH_SIZE: 50,            // 最大批次大小
    BATCH_DISCOUNT_RATES: {
      5: 0.05,   // 5筆以上: 5%折扣
      10: 0.10,  // 10筆以上: 10%折扣
      20: 0.15   // 20筆以上: 15%折扣
    }
  },
  
  CERTIFICATE_SECURITY: {
    MAX_PIN_ATTEMPTS: 3,           // PIN碼最大嘗試次數
    SESSION_TIMEOUT: 1800,         // 會話逾時: 30分鐘
    CERTIFICATE_CACHE_TTL: 300     // 憑證快取: 5分鐘
  }
};
```

## 🔗 相關連結

### 模組文檔
- [Corporate Module README](../README.md) - 法人模組總覽
- [Corporate Components](../components/README.md) - 法人元件文檔

### 相關服務
- [Smart Card Service](../../../@core/shared-2/services/smart-card.service.ts) - 智能卡整合
- [IBR Calculation Service](../../../@core/shared-2/services/ibr-calculation.service.ts) - 金額計算

### 技術文檔
- [PC/SC Integration Guide](docs/pcsc-integration.md) - 讀卡機整合指南
- [Certificate Validation Rules](docs/certificate-validation.md) - 憑證驗證規則

---

**🎯 服務完成度**: 9/9 完成 | **🏢 企業級**: 完全支援 | **🔐 安全等級**: AAA+ | **📊 批次處理**: 完整支援

*Corporate Services 提供完整的企業級解款業務邏輯支援，從工商憑證整合到批次處理，每個服務都針對企業用戶的專業需求精心設計，確保系統的安全性、合規性和高效性。*