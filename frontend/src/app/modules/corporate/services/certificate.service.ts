import { Injectable } from '@angular/core';
import { Observable, of, throwError, Subject } from 'rxjs';
import { delay, map, catchError } from 'rxjs/operators';

/**
 * 讀卡機狀態
 */
export interface CardReaderStatus {
  isConnected: boolean;
  readerName?: string;
  hasCard: boolean;
  errorMessage?: string;
}

/**
 * 憑證資訊
 */
export interface CertificateData {
  serialNumber: string;
  issuer: string;
  subject: string;
  validFrom: Date;
  validTo: Date;
  unifiedNumber: string;
  companyName: string;
}

/**
 * 工商憑證服務
 * 處理讀卡機連接、憑證讀取與驗證
 */
@Injectable({
  providedIn: 'root'
})
export class CertificateService {

  // 讀卡機狀態
  private readerStatusSubject = new Subject<CardReaderStatus>();
  public readerStatus$ = this.readerStatusSubject.asObservable();

  // PIN碼剩餘次數
  private pinAttemptsLeft = 3;

  constructor() {
    console.log('工商憑證服務已初始化');
  }

  /**
   * 檢測讀卡機
   */
  detectCardReader(): Observable<CardReaderStatus> {
    console.log('開始檢測讀卡機...');
    
    // 模擬檢測過程
    return of({
      isConnected: true,
      readerName: 'EZ100PU Smart Card Reader',
      hasCard: true
    }).pipe(
      delay(2000),
      map(status => {
        this.readerStatusSubject.next(status);
        return status;
      }),
      catchError(error => {
        const errorStatus: CardReaderStatus = {
          isConnected: false,
          hasCard: false,
          errorMessage: '無法連接讀卡機，請確認驅動程式已安裝'
        };
        this.readerStatusSubject.next(errorStatus);
        return throwError(() => new Error(errorStatus.errorMessage!));
      })
    );
  }

  /**
   * 讀取憑證資訊
   */
  readCertificate(): Observable<CertificateData> {
    console.log('讀取工商憑證...');
    
    // 模擬讀取憑證
    return of({
      serialNumber: '1234567890ABCDEF',
      issuer: 'GCA',
      subject: 'CN=凱基科技股份有限公司,OU=12345678,C=TW',
      validFrom: new Date('2023-01-01'),
      validTo: new Date('2025-12-31'),
      unifiedNumber: '12345678',
      companyName: '凱基科技股份有限公司'
    }).pipe(
      delay(1500),
      map(cert => {
        console.log('憑證讀取成功:', cert);
        return cert;
      })
    );
  }

  /**
   * 驗證 PIN 碼
   */
  verifyPin(pin: string): Observable<{ success: boolean; message: string }> {
    console.log('驗證 PIN 碼...');
    
    // 模擬 PIN 碼驗證（實際應該呼叫憑證 API）
    const isCorrect = pin === '123456'; // 模擬正確的 PIN 碼
    
    return of(null).pipe(
      delay(1000),
      map(() => {
        if (isCorrect) {
          this.pinAttemptsLeft = 3; // 重設嘗試次數
          return {
            success: true,
            message: 'PIN 碼驗證成功'
          };
        } else {
          this.pinAttemptsLeft--;
          if (this.pinAttemptsLeft <= 0) {
            throw new Error('PIN 碼錯誤次數過多，憑證已鎖定');
          }
          throw new Error(`PIN 碼錯誤，剩餘 ${this.pinAttemptsLeft} 次嘗試機會`);
        }
      }),
      catchError(error => {
        return throwError(() => error);
      })
    );
  }

  /**
   * 取得 PIN 碼剩餘嘗試次數
   */
  getPinAttemptsLeft(): number {
    return this.pinAttemptsLeft;
  }

  /**
   * 簽章資料
   */
  signData(data: string, pin: string): Observable<string> {
    console.log('使用工商憑證簽章資料...');
    
    // 模擬簽章過程
    return this.verifyPin(pin).pipe(
      delay(1000),
      map(result => {
        if (result.success) {
          // 模擬產生簽章
          const signature = btoa(data + '_SIGNED_' + Date.now());
          console.log('簽章完成:', signature);
          return signature;
        }
        throw new Error('PIN 碼驗證失敗，無法進行簽章');
      })
    );
  }

  /**
   * 驗證憑證有效性
   */
  validateCertificate(cert: CertificateData): boolean {
    const now = new Date();
    return cert.validFrom <= now && cert.validTo >= now;
  }

  /**
   * 取得系統資訊（用於診斷）
   */
  getSystemInfo(): Observable<any> {
    return of({
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      hasWebCrypto: 'crypto' in window,
      hasSubtleCrypto: 'crypto' in window && 'subtle' in window.crypto
    });
  }

  /**
   * 模擬移除智慧卡
   */
  removeCard(): void {
    this.readerStatusSubject.next({
      isConnected: true,
      readerName: 'EZ100PU Smart Card Reader',
      hasCard: false
    });
  }

  /**
   * 模擬插入智慧卡
   */
  insertCard(): void {
    this.readerStatusSubject.next({
      isConnected: true,
      readerName: 'EZ100PU Smart Card Reader',
      hasCard: true
    });
  }
}