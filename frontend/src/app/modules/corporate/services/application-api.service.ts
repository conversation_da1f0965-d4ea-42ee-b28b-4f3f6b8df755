import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout, map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 申請類型枚舉
 */
export enum ApplicationType {
  ACCOUNT_OPENING = 'ACCOUNT_OPENING',
  LOAN_APPLICATION = 'LOAN_APPLICATION',
  CREDIT_FACILITY = 'CREDIT_FACILITY',
  INVESTMENT_SERVICE = 'INVESTMENT_SERVICE',
  TRADE_FINANCE = 'TRADE_FINANCE',
  OTHER = 'OTHER'
}

/**
 * 申請狀態枚舉
 */
export enum ApplicationStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  PENDING_DOCUMENTS = 'PENDING_DOCUMENTS',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

/**
 * 申請人資訊介面
 */
export interface ApplicantInfo {
  /** 申請人類型 */
  applicantType: 'LEGAL_REPRESENTATIVE' | 'AUTHORIZED_PERSON' | 'BRANCH_MANAGER';
  /** 申請人姓名 */
  name: string;
  /** 申請人身分證字號 */
  idNumber: string;
  /** 申請人職稱 */
  position: string;
  /** 聯絡電話 */
  phone: string;
  /** 電子郵件 */
  email: string;
  /** 授權書號碼（如適用） */
  authorizationNumber?: string;
  /** 授權有效期限（如適用） */
  authorizationExpiry?: Date;
}

/**
 * 公司資訊介面
 */
export interface CompanyApplicationInfo {
  /** 統一編號 */
  unifiedNumber: string;
  /** 公司名稱 */
  companyName: string;
  /** 公司類型 */
  companyType: string;
  /** 營業地址 */
  businessAddress: string;
  /** 通訊地址 */
  mailAddress?: string;
  /** 負責人姓名 */
  representativeName: string;
  /** 負責人身分證字號 */
  representativeId: string;
  /** 資本額 */
  capitalAmount: number;
  /** 員工人數 */
  employeeCount?: number;
  /** 主要營業項目 */
  mainBusinessItems: string[];
  /** 成立日期 */
  establishDate: Date;
}

/**
 * 申請文件介面
 */
export interface ApplicationDocument {
  /** 文件ID */
  documentId: string;
  /** 文件類型 */
  documentType: 'COMPANY_REGISTRATION' | 'TAX_CERTIFICATE' | 'FINANCIAL_STATEMENT' | 'AUTHORIZATION_LETTER' | 'ID_COPY' | 'OTHER';
  /** 文件名稱 */
  documentName: string;
  /** 文件描述 */
  description?: string;
  /** 檔案大小（bytes） */
  fileSize: number;
  /** 檔案類型 */
  mimeType: string;
  /** 上傳時間 */
  uploadTime: Date;
  /** 是否必要文件 */
  isRequired: boolean;
  /** 驗證狀態 */
  verificationStatus: 'PENDING' | 'VERIFIED' | 'REJECTED';
  /** 驗證備註 */
  verificationNote?: string;
}

/**
 * 申請表單資料介面
 */
export interface ApplicationFormData {
  /** 申請類型 */
  applicationType: ApplicationType;
  /** 申請人資訊 */
  applicantInfo: ApplicantInfo;
  /** 公司資訊 */
  companyInfo: CompanyApplicationInfo;
  /** 申請詳細資料 */
  applicationDetails: {
    /** 申請金額 */
    requestedAmount?: number;
    /** 申請幣別 */
    currency?: string;
    /** 申請期限 */
    requestedTerm?: number;
    /** 申請用途 */
    purpose: string;
    /** 特殊需求 */
    specialRequirements?: string;
    /** 預期開始日期 */
    expectedStartDate?: Date;
  };
  /** 財務資訊 */
  financialInfo?: {
    /** 年營業額 */
    annualRevenue: number;
    /** 淨利潤 */
    netProfit: number;
    /** 總資產 */
    totalAssets: number;
    /** 總負債 */
    totalLiabilities: number;
    /** 財務年度 */
    financialYear: number;
  };
  /** 附件文件 */
  documents: ApplicationDocument[];
  /** 備註 */
  remarks?: string;
}

/**
 * 申請提交請求介面
 */
export interface ApplicationSubmitRequest {
  /** 申請表單資料 */
  formData: ApplicationFormData;
  /** 提交類型 */
  submitType: 'DRAFT' | 'FINAL';
  /** 確認聲明 */
  confirmations: {
    /** 確認資料正確性 */
    dataAccuracy: boolean;
    /** 確認授權提交 */
    submitAuthorization: boolean;
    /** 確認了解條款 */
    termsAcknowledgment: boolean;
    /** 確認文件完整性 */
    documentCompleteness: boolean;
  };
  /** 數位簽章（如適用） */
  digitalSignature?: string;
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * 申請提交回應介面
 */
export interface ApplicationSubmitResponse {
  /** 申請編號 */
  applicationId: string;
  /** 申請狀態 */
  status: ApplicationStatus;
  /** 提交時間 */
  submitTime: Date;
  /** 預計處理時間 */
  estimatedProcessTime?: Date;
  /** 追蹤代碼 */
  trackingCode: string;
  /** 下一步驟說明 */
  nextSteps: string[];
  /** 聯絡資訊 */
  contactInfo: {
    /** 承辦人員 */
    assignedOfficer?: string;
    /** 聯絡電話 */
    contactPhone: string;
    /** 聯絡電子郵件 */
    contactEmail: string;
    /** 服務時間 */
    serviceHours: string;
  };
  /** 需要補件清單 */
  requiredDocuments?: string[];
  /** 處理費用 */
  processingFee?: {
    amount: number;
    currency: string;
    dueDate?: Date;
    paymentMethods: string[];
  };
}

/**
 * 申請狀態查詢回應介面
 */
export interface ApplicationStatusResponse {
  /** 申請編號 */
  applicationId: string;
  /** 申請狀態 */
  currentStatus: ApplicationStatus;
  /** 狀態歷史 */
  statusHistory: {
    status: ApplicationStatus;
    timestamp: Date;
    processedBy?: string;
    comments?: string;
  }[];
  /** 處理進度 */
  progressPercentage: number;
  /** 剩餘處理時間估計 */
  estimatedRemainingTime?: string;
  /** 最新更新 */
  lastUpdate: {
    timestamp: Date;
    description: string;
    updatedBy?: string;
  };
  /** 需要的行動 */
  requiredActions?: {
    actionType: string;
    description: string;
    dueDate?: Date;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
  }[];
}

/**
 * 申請列表查詢請求介面
 */
export interface ApplicationListRequest {
  /** 申請人統一編號 */
  companyId?: string;
  /** 申請類型篩選 */
  applicationTypes?: ApplicationType[];
  /** 狀態篩選 */
  statuses?: ApplicationStatus[];
  /** 申請日期範圍 */
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  /** 分頁設定 */
  pagination: {
    page: number;
    pageSize: number;
  };
  /** 排序設定 */
  sorting?: {
    field: 'submitTime' | 'applicationId' | 'status';
    direction: 'ASC' | 'DESC';
  };
}

/**
 * 申請列表回應介面
 */
export interface ApplicationListResponse {
  /** 申請列表 */
  applications: {
    applicationId: string;
    applicationType: ApplicationType;
    companyName: string;
    applicantName: string;
    status: ApplicationStatus;
    submitTime: Date;
    lastUpdateTime: Date;
    trackingCode: string;
  }[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
}

/**
 * 法人模組申請提交API服務
 * 
 * 提供完整的法人申請流程管理功能，包括：
 * - 申請表單提交
 * - 申請狀態追蹤
 * - 文件上傳管理
 * - 申請修改與取消
 * - 申請歷史查詢
 * 
 * @example
 * ```typescript
 * constructor(private applicationApi: ApplicationApiService) {}
 * 
 * async submitApplication(formData: ApplicationFormData) {
 *   try {
 *     const request: ApplicationSubmitRequest = {
 *       formData,
 *       submitType: 'FINAL',
 *       confirmations: {
 *         dataAccuracy: true,
 *         submitAuthorization: true,
 *         termsAcknowledgment: true,
 *         documentCompleteness: true
 *       },
 *       timestamp: new Date()
 *     };
 *     
 *     const result = await this.applicationApi.submitApplication(request).toPromise();
 *     console.log('申請提交成功', result.applicationId);
 *   } catch (error) {
 *     console.error('申請提交失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class ApplicationApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/v1/corporate/application`;
  private readonly timeout = 60000; // 60秒超時（申請提交可能需要較長時間）
  private readonly retryCount = 2; // 重試次數

  constructor(private http: HttpClient) {}

  /**
   * 提交申請
   * 
   * @param request 申請提交請求
   * @returns 申請提交結果Observable
   * 
   * @example
   * ```typescript
   * const request: ApplicationSubmitRequest = {
   *   formData: this.buildFormData(),
   *   submitType: 'FINAL',
   *   confirmations: {
   *     dataAccuracy: true,
   *     submitAuthorization: true,
   *     termsAcknowledgment: true,
   *     documentCompleteness: true
   *   },
   *   timestamp: new Date()
   * };
   * 
   * this.applicationApi.submitApplication(request).subscribe({
   *   next: (response) => {
   *     console.log('申請已提交，編號:', response.applicationId);
   *     console.log('追蹤代碼:', response.trackingCode);
   *   },
   *   error: (error) => console.error('提交失敗', error)
   * });
   * ```
   */
  submitApplication(request: ApplicationSubmitRequest): Observable<ApplicationSubmitResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationSubmitResponse>(
      `${this.apiUrl}/submit`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 儲存申請草稿
   * 
   * @param formData 申請表單資料
   * @returns 草稿儲存結果Observable
   */
  saveDraft(formData: ApplicationFormData): Observable<{ draftId: string; saveTime: Date }> {
    const headers = this.getHeaders();
    
    return this.http.post<{ draftId: string; saveTime: Date }>(
      `${this.apiUrl}/draft`,
      { formData, timestamp: new Date() },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 載入申請草稿
   * 
   * @param draftId 草稿ID
   * @returns 草稿資料Observable
   */
  loadDraft(draftId: string): Observable<ApplicationFormData> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationFormData>(
      `${this.apiUrl}/draft/${encodeURIComponent(draftId)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢申請狀態
   * 
   * @param applicationId 申請編號
   * @returns 申請狀態Observable
   */
  getApplicationStatus(applicationId: string): Observable<ApplicationStatusResponse> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationStatusResponse>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/status`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢申請詳細資料
   * 
   * @param applicationId 申請編號
   * @returns 申請詳細資料Observable
   */
  getApplicationDetails(applicationId: string): Observable<ApplicationFormData & {
    applicationId: string;
    status: ApplicationStatus;
    submitTime: Date;
    trackingCode: string;
  }> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationFormData & {
      applicationId: string;
      status: ApplicationStatus;
      submitTime: Date;
      trackingCode: string;
    }>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/details`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢申請列表
   * 
   * @param request 列表查詢請求
   * @returns 申請列表Observable
   */
  getApplicationList(request: ApplicationListRequest): Observable<ApplicationListResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationListResponse>(
      `${this.apiUrl}/list`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取消申請
   * 
   * @param applicationId 申請編號
   * @param reason 取消原因
   * @returns 取消結果Observable
   */
  cancelApplication(applicationId: string, reason: string): Observable<{
    success: boolean;
    message: string;
    cancelTime: Date;
  }> {
    const headers = this.getHeaders();
    
    return this.http.post<{
      success: boolean;
      message: string;
      cancelTime: Date;
    }>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/cancel`,
      { reason, timestamp: new Date() },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 修改申請資料
   * 
   * @param applicationId 申請編號
   * @param formData 修改後的表單資料
   * @param changeReason 修改原因
   * @returns 修改結果Observable
   */
  updateApplication(
    applicationId: string, 
    formData: ApplicationFormData, 
    changeReason: string
  ): Observable<{
    success: boolean;
    message: string;
    updateTime: Date;
    newVersion: number;
  }> {
    const headers = this.getHeaders();
    
    return this.http.put<{
      success: boolean;
      message: string;
      updateTime: Date;
      newVersion: number;
    }>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}`,
      { formData, changeReason, timestamp: new Date() },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 上傳申請文件
   * 
   * @param applicationId 申請編號
   * @param file 檔案
   * @param documentType 文件類型
   * @param description 文件描述
   * @returns 上傳結果Observable
   */
  uploadDocument(
    applicationId: string,
    file: File,
    documentType: string,
    description?: string
  ): Observable<{
    documentId: string;
    uploadTime: Date;
    message: string;
  }> {
    const headers = new HttpHeaders({
      'X-Requested-With': 'XMLHttpRequest'
    });

    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);
    if (description) {
      formData.append('description', description);
    }
    formData.append('timestamp', new Date().toISOString());
    
    return this.http.post<{
      documentId: string;
      uploadTime: Date;
      message: string;
    }>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/documents`,
      formData,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 刪除申請文件
   * 
   * @param applicationId 申請編號
   * @param documentId 文件ID
   * @returns 刪除結果Observable
   */
  deleteDocument(applicationId: string, documentId: string): Observable<{
    success: boolean;
    message: string;
    deleteTime: Date;
  }> {
    const headers = this.getHeaders();
    
    return this.http.delete<{
      success: boolean;
      message: string;
      deleteTime: Date;
    }>(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/documents/${encodeURIComponent(documentId)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 下載申請文件
   * 
   * @param applicationId 申請編號
   * @param documentId 文件ID
   * @returns 文件下載Observable
   */
  downloadDocument(applicationId: string, documentId: string): Observable<Blob> {
    const headers = this.getHeaders();
    
    return this.http.get(
      `${this.apiUrl}/${encodeURIComponent(applicationId)}/documents/${encodeURIComponent(documentId)}/download`,
      { 
        headers,
        responseType: 'blob' as 'json'
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    ) as Observable<Blob>;
  }

  /**
   * 取得申請類型清單
   * 
   * @returns 申請類型清單Observable
   */
  getApplicationTypes(): Observable<{
    type: ApplicationType;
    name: string;
    description: string;
    requiredDocuments: string[];
    processingTime: string;
    processingFee?: number;
  }[]> {
    const headers = this.getHeaders();
    
    return this.http.get<{
      type: ApplicationType;
      name: string;
      description: string;
      requiredDocuments: string[];
      processingTime: string;
      processingFee?: number;
    }[]>(
      `${this.apiUrl}/types`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證申請資料
   * 
   * @param formData 申請表單資料
   * @returns 驗證結果Observable
   */
  validateApplication(formData: ApplicationFormData): Observable<{
    isValid: boolean;
    errors: {
      field: string;
      message: string;
      severity: 'ERROR' | 'WARNING';
    }[];
    warnings: string[];
    suggestions: string[];
  }> {
    const headers = this.getHeaders();
    
    return this.http.post<{
      isValid: boolean;
      errors: {
        field: string;
        message: string;
        severity: 'ERROR' | 'WARNING';
      }[];
      warnings: string[];
      suggestions: string[];
    }>(
      `${this.apiUrl}/validate`,
      { formData },
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得HTTP標頭
   * 
   * @returns HttpHeaders
   * @private
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  /**
   * 統一錯誤處理
   * 
   * @param error HTTP錯誤回應
   * @returns 錯誤Observable
   * @private
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '申請API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '申請資料格式錯誤或不完整';
          errorCode = 'INVALID_APPLICATION_DATA';
          break;
        case 401:
          errorMessage = '沒有權限提交申請';
          errorCode = 'UNAUTHORIZED';
          break;
        case 403:
          errorMessage = '申請提交權限不足';
          errorCode = 'FORBIDDEN';
          break;
        case 404:
          errorMessage = '找不到指定的申請記錄';
          errorCode = 'APPLICATION_NOT_FOUND';
          break;
        case 408:
          errorMessage = '申請提交請求超時';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 409:
          errorMessage = '申請資料衝突，可能已存在相同申請';
          errorCode = 'APPLICATION_CONFLICT';
          break;
        case 413:
          errorMessage = '上傳檔案過大';
          errorCode = 'FILE_TOO_LARGE';
          break;
        case 415:
          errorMessage = '不支援的檔案格式';
          errorCode = 'UNSUPPORTED_FILE_TYPE';
          break;
        case 422:
          errorMessage = '申請資料驗證失敗';
          errorCode = 'VALIDATION_FAILED';
          break;
        case 429:
          errorMessage = '申請提交過於頻繁，請稍後再試';
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorMessage = '申請系統內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorMessage = '申請服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        case 503:
          errorMessage = '申請服務正在維護中';
          errorCode = 'SERVICE_MAINTENANCE';
          break;
        default:
          errorMessage = `申請服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('申請API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    });
  }
}