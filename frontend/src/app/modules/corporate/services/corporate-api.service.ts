import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout, map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

// 導入基礎服務
import { BaseApiService } from '../../../@core/shared-2/services/base-api.service';
import { ApiConfigService } from '../../../@core/shared-2/services/api-config.service';

// 導入相關模型
import { 
  CompanyInfo, 
  CorporateRemittance, 
  CorporateApplication, 
  CertificateInfo,
  RepresentativeInfo,
  CorporateBankAccount,
  BatchProcessing,
  TransactionLimits,
  ApplicationStatus 
} from '../models/corporate.model';

/**
 * 企業條款同意請求介面
 */
export interface CorporateTermsAgreementRequest {
  /** 統一編號 */
  unifiedNumber?: string;
  /** 同意的條款版本 */
  termsVersion: string;
  /** 同意時間 */
  agreedAt: Date;
  /** IP位址 */
  ipAddress?: string;
  /** 使用者代理 */
  userAgent?: string;
  /** 代表人身分證號 */
  representativeId?: string;
}

/**
 * 企業條款同意回應介面
 */
export interface CorporateTermsAgreementResponse {
  /** 同意記錄ID */
  agreementId: string;
  /** 是否成功 */
  success: boolean;
  /** 同意時間 */
  agreedAt: Date;
  /** 有效期限 */
  expiryDate?: Date;
  /** 訊息 */
  message: string;
  /** 企業交易限額 */
  transactionLimits?: TransactionLimits;
}

/**
 * 統一編號驗證請求介面
 */
export interface UnifiedNumberVerificationRequest {
  /** 統一編號 */
  unifiedNumber: string;
  /** 公司名稱 */
  companyName?: string;
  /** 驗證等級 */
  verificationLevel: 'BASIC' | 'ENHANCED' | 'COMPREHENSIVE';
}

/**
 * 統一編號驗證回應介面
 */
export interface UnifiedNumberVerificationResponse {
  /** 驗證結果 */
  isValid: boolean;
  /** 公司資料 */
  companyData?: {
    companyName: string;
    companyEnglishName?: string;
    businessAddress: string;
    industry: string;
    capital: number;
    establishedDate: string;
    status: 'ACTIVE' | 'DISSOLVED' | 'SUSPENDED';
  };
  /** 代表人資料 */
  representativeData?: {
    name: string;
    title: string;
    representativeSince: string;
  };
  /** 驗證詳細結果 */
  verificationDetails: {
    /** 統一編號格式正確 */
    formatValid: boolean;
    /** 檢查碼正確 */
    checksumValid: boolean;
    /** 公司存在 */
    companyExists: boolean;
    /** 公司狀態正常 */
    statusActive: boolean;
  };
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 工商憑證驗證請求介面
 */
export interface CertificateVerificationRequest {
  /** 憑證資料 (Base64) */
  certificateData: string;
  /** PIN碼 (加密) */
  encryptedPin: string;
  /** 驗證類型 */
  verificationType: 'READ_ONLY' | 'SIGNATURE_REQUIRED';
  /** 會話ID */
  sessionId: string;
}

/**
 * 工商憑證驗證回應介面
 */
export interface CertificateVerificationResponse {
  /** 驗證結果 */
  isValid: boolean;
  /** 憑證資訊 */
  certificateInfo?: CertificateInfo;
  /** 憑證狀態 */
  certificateStatus: {
    /** 是否過期 */
    isExpired: boolean;
    /** 是否被撤銷 */
    isRevoked: boolean;
    /** 是否可信任 */
    isTrusted: boolean;
    /** 剩餘有效天數 */
    daysUntilExpiry: number;
  };
  /** PIN碼驗證結果 */
  pinVerification: {
    /** 驗證成功 */
    isValid: boolean;
    /** 剩餘嘗試次數 */
    remainingAttempts: number;
    /** 是否被鎖定 */
    isLocked: boolean;
    /** 鎖定解除時間 */
    unlockTime?: Date;
  };
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 匯款搜尋請求介面
 */
export interface CorporateRemittanceSearchRequest {
  /** 統一編號 */
  unifiedNumber: string;
  /** 受益企業名稱 */
  beneficiaryName: string;
  /** 匯款性質 */
  remittanceType?: string;
  /** 幣別篩選 */
  currency?: string;
  /** 匯款日期範圍 */
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  /** 金額範圍 */
  amountRange?: {
    minAmount: number;
    maxAmount: number;
  };
  /** 分頁設定 */
  pagination?: {
    page: number;
    pageSize: number;
  };
}

/**
 * 匯款搜尋回應介面
 */
export interface CorporateRemittanceSearchResponse {
  /** 匯款列表 */
  remittances: CorporateRemittance[];
  /** 總筆數 */
  totalCount: number;
  /** 總金額 */
  totalAmount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
  /** 查詢時間 */
  searchTime: Date;
  /** 分類統計 */
  statistics?: {
    /** 各幣別統計 */
    currencyBreakdown: { currency: string; count: number; amount: number }[];
    /** 各性質統計 */
    purposeBreakdown: { purpose: string; count: number; amount: number }[];
  };
}

/**
 * 批次金額計算請求介面
 */
export interface BatchAmountCalculationRequest {
  /** 選取的匯款列表 */
  selectedRemittances: {
    remittanceId: string;
    amount: number;
    currency: string;
    remittanceType: string;
  }[];
  /** 收款銀行 */
  beneficiaryBank: string;
  /** 計算選項 */
  calculationOptions?: {
    /** 包含手續費 */
    includeFees: boolean;
    /** 包含稅費 */
    includeTax: boolean;
    /** 使用即時匯率 */
    useRealTimeRate: boolean;
    /** 批次處理折扣 */
    applyBatchDiscount: boolean;
  };
}

/**
 * 批次金額計算回應介面
 */
export interface BatchAmountCalculationResponse {
  /** 各筆匯款計算結果 */
  calculations: {
    remittanceId: string;
    originalAmount: number;
    currency: string;
    exchangeRate: number;
    twdAmount: number;
    fees: {
      processingFee: number;
      handlingFee: number;
      telegraphicFee: number;
      otherFees: number;
    };
    netAmount: number;
  }[];
  /** 批次總計 */
  batchSummary: {
    totalRemittances: number;
    totalOriginalAmount: { [currency: string]: number };
    totalTwdAmount: number;
    totalFees: number;
    totalNetAmount: number;
    batchDiscount: number;
    finalAmount: number;
  };
  /** 計算時間 */
  calculationTime: Date;
  /** 匯率有效期 */
  rateValidUntil: Date;
}

/**
 * 企業申請提交請求介面
 */
export interface CorporateApplicationSubmitRequest {
  /** 申請資料 */
  applicationData: CorporateApplication;
  /** 數位簽章資料 */
  digitalSignature: {
    /** 簽章資料 (Base64) */
    signatureData: string;
    /** 憑證序號 */
    certificateSerial: string;
    /** 簽章時間 */
    signedAt: Date;
    /** 簽章演算法 */
    algorithm: string;
  };
  /** 確認項目 */
  confirmations: {
    /** 確認企業資料正確 */
    companyDataAccuracy: boolean;
    /** 確認條款同意 */
    termsAgreed: boolean;
    /** 確認憑證驗證 */
    certificateVerified: boolean;
    /** 確認匯款資訊 */
    remittanceConfirmed: boolean;
    /** 確認金額計算 */
    amountConfirmed: boolean;
    /** 確認代表人授權 */
    representativeAuthorized: boolean;
  };
  /** 提交時間 */
  submitTime: Date;
}

/**
 * 企業申請提交回應介面
 */
export interface CorporateApplicationSubmitResponse {
  /** 申請編號 */
  applicationId: string;
  /** 申請狀態 */
  status: ApplicationStatus;
  /** 提交時間 */
  submitTime: Date;
  /** 預計處理時間 */
  estimatedProcessTime?: Date;
  /** 追蹤號碼 */
  trackingNumber: string;
  /** 批次處理編號 */
  batchId?: string;
  /** 收據URL */
  receiptUrl?: string;
  /** 下一步驟 */
  nextSteps: string[];
  /** 企業客服資訊 */
  corporateContactInfo: {
    /** 企業專線 */
    dedicatedLine: string;
    /** 客戶經理 */
    accountManager?: {
      name: string;
      email: string;
      phone: string;
    };
    /** 服務時間 */
    serviceHours: string;
    /** 緊急聯絡 */
    emergencyContact?: string;
  };
}

/**
 * 企業申請狀態查詢回應介面
 */
export interface CorporateApplicationStatusResponse {
  /** 申請編號 */
  applicationId: string;
  /** 當前狀態 */
  currentStatus: ApplicationStatus;
  /** 狀態歷史 */
  statusHistory: {
    status: ApplicationStatus;
    timestamp: Date;
    description: string;
    processedBy?: string;
    department?: string;
  }[];
  /** 處理進度 */
  progressPercentage: number;
  /** 預計完成時間 */
  estimatedCompletionTime?: Date;
  /** 批次處理資訊 */
  batchInfo?: {
    batchId: string;
    totalTransactions: number;
    completedTransactions: number;
    failedTransactions: number;
  };
  /** 需要的操作 */
  requiredActions?: {
    action: string;
    description: string;
    dueDate?: Date;
    priority: 'HIGH' | 'MEDIUM' | 'LOW';
  }[];
  /** 最新更新 */
  lastUpdate: {
    timestamp: Date;
    description: string;
    updatedBy?: string;
    department?: string;
  };
}

/**
 * 法人模組API服務
 * 
 * 提供完整的法人IBR申請API整合，包括：
 * - 企業條款同意處理
 * - 統一編號驗證
 * - 工商憑證驗證與數位簽章
 * - 匯款搜尋與批次處理
 * - 申請提交與狀態追蹤
 * 
 * @example
 * ```typescript
 * constructor(private corporateApi: CorporateApiService) {}
 * 
 * async verifyUnifiedNumber() {
 *   const request: UnifiedNumberVerificationRequest = {
 *     unifiedNumber: '12345678',
 *     verificationLevel: 'COMPREHENSIVE'
 *   };
 *   
 *   try {
 *     const result = await this.corporateApi.verifyUnifiedNumber(request).toPromise();
 *     console.log('統編驗證成功', result.isValid);
 *   } catch (error) {
 *     console.error('統編驗證失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class CorporateApiService extends BaseApiService {
  
  /**
   * 取得當前案件編號 (從session獲便狀態服務)
   * @private
   */
  private getCaseNo(): string {
    // TODO: 實際應該從 IbrStateService 或 sessionStorage 取得
    return sessionStorage.getItem('ibr_case_no') || '';
  }
  
  private readonly MODULE_NAME = 'corporate';
  protected readonly timeout = 45000; // 45秒超時 (法人流程較複雜)
  protected readonly retryCount = 2; // 重試次數

  constructor(
    protected http: HttpClient,
    protected apiConfig: ApiConfigService
  ) {
    super(http, apiConfig);
    console.log('Corporate API Service initialized', {
      mockMode: this.isMockMode,
      module: this.MODULE_NAME
    });
  }

  /**
   * 企業條款同意 (使用共用API)
   * 
   * @param request 企業條款同意請求
   * @returns 企業條款同意結果Observable
   */
  agreeToTerms(request: CorporateTermsAgreementRequest): Observable<CorporateTermsAgreementResponse> {
    // 使用共用API，加上customerType 參數
    const requestWithType = { 
      ...request, 
      customerType: 'CORPORATE',
      caseNo: this.getCaseNo()
    };
    return this.post<CorporateTermsAgreementResponse>(
      this.MODULE_NAME,
      'termsAgree',
      requestWithType
    );
  }

  /**
   * 檢查企業條款同意狀態
   * 
   * @param unifiedNumber 統一編號
   * @returns 企業條款同意狀態Observable
   */
  checkTermsStatus(unifiedNumber?: string): Observable<{
    isAgreed: boolean;
    agreementId?: string;
    agreedAt?: Date;
    expiryDate?: Date;
    transactionLimits?: TransactionLimits;
  }> {
    return this.get<{
      isAgreed: boolean;
      agreementId?: string;
      agreedAt?: Date;
      expiryDate?: Date;
      transactionLimits?: TransactionLimits;
    }>(this.MODULE_NAME, 'terms', unifiedNumber ? { unifiedNumber } : {});
  }

  /**
   * 統一編號驗證 (使用共用API)
   * 
   * @param request 統一編號驗證請求
   * @returns 統一編號驗證結果Observable
   */
  verifyUnifiedNumber(request: UnifiedNumberVerificationRequest): Observable<UnifiedNumberVerificationResponse> {
    // 使用共用API的資料驗證端點
    const requestWithType = { 
      ...request, 
      customerType: 'CORPORATE',
      caseNo: this.getCaseNo()
    };
    return this.post<UnifiedNumberVerificationResponse>(
      this.MODULE_NAME,
      'verify',
      requestWithType
    );
  }

  /**
   * 工商憑證驗證 (使用共用API)
   * 
   * @param request 工商憑證驗證請求
   * @returns 工商憑證驗證結果Observable
   */
  verifyCertificate(request: CertificateVerificationRequest): Observable<CertificateVerificationResponse> {
    // 使用共用API的憑證驗證端點
    const requestWithType = { 
      ...request, 
      customerType: 'CORPORATE',
      caseNo: this.getCaseNo()
    };
    return this.post<CertificateVerificationResponse>(
      this.MODULE_NAME,
      'certificateVerify',
      requestWithType
    );
  }

  /**
   * 取得讀卡機列表
   * 
   * @returns 讀卡機列表Observable
   */
  getCardReaders(): Observable<{
    readers: {
      name: string;
      isConnected: boolean;
      hasCard: boolean;
      cardType?: string;
      driverVersion?: string;
    }[];
    totalReaders: number;
    connectedReaders: number;
  }> {
    return this.get<{
      readers: {
        name: string;
        isConnected: boolean;
        hasCard: boolean;
        cardType?: string;
        driverVersion?: string;
      }[];
      totalReaders: number;
      connectedReaders: number;
    }>(this.MODULE_NAME, 'certificateDetect', {});
  }

  /**
   * 搜尋企業匯款記錄 (使用共用API)
   * 
   * @param request 企業匯款搜尋請求
   * @returns 企業匯款搜尋結果Observable
   */
  searchRemittances(request: CorporateRemittanceSearchRequest): Observable<CorporateRemittanceSearchResponse> {
    // 使用共用查詢API
    const requestWithType = { 
      ...request, 
      customerType: 'CORPORATE' 
    };
    // 查詢功能使用 query 模組
    return this.post<CorporateRemittanceSearchResponse>(
      'query',
      'search',
      requestWithType
    );
  }

  /**
   * 取得企業匯款詳細資訊 (使用共用API)
   * 
   * @param remittanceId 匯款ID或案件編號
   * @returns 企業匯款詳細資訊Observable
   */
  getRemittanceDetails(remittanceId: string): Observable<CorporateRemittance> {
    // 使用共用查詢API
    // 查詢功能使用 query 模組，但由於 API 有路徑參數，需要特別處理
    return this.get<CorporateRemittance>(
      'query',
      `transaction/${remittanceId}`
    );
  }

  /**
   * 批次金額計算 (使用共用API)
   * 
   * @param request 批次金額計算請求
   * @returns 批次金額計算結果Observable
   */
  calculateBatchAmount(request: BatchAmountCalculationRequest): Observable<BatchAmountCalculationResponse> {
    // 使用共用API的費用計算功能
    const requestWithType = { 
      ...request, 
      customerType: 'CORPORATE',
      caseNo: this.getCaseNo()
    };
    return this.post<BatchAmountCalculationResponse>(
      this.MODULE_NAME,
      'remittanceConfirm',
      requestWithType
    );
  }

  /**
   * 提交企業申請 (使用共用API)
   * 
   * @param request 企業申請提交請求
   * @returns 企業申請提交結果Observable
   */
  submitApplication(request: CorporateApplicationSubmitRequest): Observable<CorporateApplicationSubmitResponse> {
    // 使用共用API的提交端點
    const requestWithType = { 
      ...request.applicationData,
      customerType: 'CORPORATE',
      caseNo: this.getCaseNo(),
      digitalSignature: request.digitalSignature,
      confirmations: request.confirmations,
      submitTime: request.submitTime
    };
    return this.post<CorporateApplicationSubmitResponse>(
      this.MODULE_NAME,
      'submit',
      requestWithType
    );
  }

  /**
   * 查詢企業申請狀態 (使用共用API)
   * 
   * @param applicationId 申請編號
   * @returns 企業申請狀態Observable
   */
  getApplicationStatus(applicationId: string): Observable<CorporateApplicationStatusResponse> {
    // 使用共用查詢API
    // 查詢功能使用 query 模組，但由於 API 有路徑參數，需要特別處理
    return this.get<CorporateApplicationStatusResponse>(
      'query',
      `status/${applicationId}`
    );
  }

  /**
   * 取得企業申請歷史記錄
   * 
   * @param unifiedNumber 統一編號
   * @param pageSize 每頁筆數
   * @param page 頁數
   * @returns 企業申請歷史記錄Observable
   */
  getApplicationHistory(
    unifiedNumber: string,
    pageSize = 10,
    page = 1
  ): Observable<{
    applications: {
      applicationId: string;
      submitTime: Date;
      status: ApplicationStatus;
      totalAmount: number;
      totalTransactions: number;
      batchId?: string;
    }[];
    totalCount: number;
    currentPage: number;
    totalPages: number;
  }> {
    return this.get<any>(this.MODULE_NAME, 'remittanceDetail', {
      unifiedNumber,
      pageSize: pageSize.toString(),
      page: page.toString(),
      type: 'history'
    });
  }

  /**
   * 取得凱基銀行分行列表
   * 
   * @returns 凱基銀行分行列表Observable
   */
  getKgiBranches(): Observable<{
    branchCode: string;
    branchName: string;
    address: string;
    phone: string;
    isActive: boolean;
    services: string[];
  }[]> {
    // Mock 模式下返回預設分行列表
    if (this.isMockMode) {
      return new Observable(observer => {
        observer.next([
          { branchCode: '0001', branchName: '營業部', address: '台北市中正區忠孝西路一段66號', phone: '02-23618888', isActive: true, services: ['企業金融', '外匯'] },
          { branchCode: '0002', branchName: '敦化分行', address: '台北市松山區敦化北路88號', phone: '02-27122345', isActive: true, services: ['企業金融', '外匯', '財富管理'] },
          { branchCode: '0003', branchName: '內湖分行', address: '台北市內湖區洲子街83號', phone: '02-87975678', isActive: true, services: ['企業金融', '外匯'] }
        ]);
        observer.complete();
      });
    }
    
    return this.get<any[]>(this.MODULE_NAME, 'remittanceDetail', { type: 'branches' });
  }

  /**
   * 取得匯款性質代碼列表 (使用共用API)
   * 
   * @returns 匯款性質代碼列表Observable
   */
  getRemittanceNatures(): Observable<{
    code: string;
    description: string;
    descriptionEn: string;
    category: 'TRADE' | 'SERVICE' | 'INVESTMENT' | 'OTHER';
    isActive: boolean;
    corporateOnly?: boolean;
  }[]> {
    // Mock 模式下返回預設匯款性質列表
    if (this.isMockMode) {
      return new Observable(observer => {
        observer.next([
          { code: '111', description: '貨物輸入款項', descriptionEn: 'Import of Goods', category: 'TRADE', isActive: true, corporateOnly: true },
          { code: '112', description: '貨物輸出款項', descriptionEn: 'Export of Goods', category: 'TRADE', isActive: true, corporateOnly: true },
          { code: '210', description: '運輸服務', descriptionEn: 'Transportation Services', category: 'SERVICE', isActive: true },
          { code: '220', description: '旅遊服務', descriptionEn: 'Travel Services', category: 'SERVICE', isActive: true },
          { code: '330', description: '直接投資', descriptionEn: 'Direct Investment', category: 'INVESTMENT', isActive: true, corporateOnly: true },
          { code: '410', description: '其他匯款', descriptionEn: 'Other Remittances', category: 'OTHER', isActive: true }
        ]);
        observer.complete();
      });
    }
    
    // 使用共用API，但包含企業獨有的性質
    return this.get<any[]>(
      this.MODULE_NAME,
      'natures',
      { customerType: 'CORPORATE' }
    );
  }

  /**
   * 取得企業匯率資訊 (使用共用API)
   * 
   * @param fromCurrency 來源幣別
   * @param toCurrency 目標幣別
   * @param amount 金額 (用於計算優惠匯率)
   * @returns 企業匯率資訊Observable
   */
  getCorporateExchangeRate(
    fromCurrency: string, 
    toCurrency: string = 'TWD', 
    amount?: number
  ): Observable<{
    fromCurrency: string;
    toCurrency: string;
    baseRate: number;
    corporateRate: number;
    preferentialRate?: number;
    rateDiscount?: number;
    timestamp: Date;
    validity: number; // 有效期限（秒）
    minimumAmount?: number;
  }> {
    const params: any = {
      currency: fromCurrency,
      customerType: 'CORPORATE'
    };
    if (amount) {
      params.amount = amount.toString();
    }
    
    // 使用共用API
    return this.get<any>(
      this.MODULE_NAME,
      'exchangeRate',
      params
    );
  }

  /**
   * 驗證數位簽章
   * 
   * @param signatureData 簽章資料
   * @param originalData 原始資料
   * @param certificateSerial 憑證序號
   * @returns 數位簽章驗證結果Observable
   */
  verifyDigitalSignature(
    signatureData: string,
    originalData: string,
    certificateSerial: string
  ): Observable<{
    isValid: boolean;
    signatureAlgorithm: string;
    signedAt: Date;
    certificateInfo: CertificateInfo;
    verificationDetails: {
      signatureValid: boolean;
      certificateValid: boolean;
      timestampValid: boolean;
      dataIntegrityValid: boolean;
    };
    errorMessage?: string;
  }> {
    return this.post<any>(
      this.MODULE_NAME,
      'certificateVerify',
      {
        signatureData,
        originalData,
        certificateSerial,
        verificationType: 'SIGNATURE'
      }
    );
  }

  /**
   * 覆寫錯誤處理以提供法人專屬錯誤訊息
   * 
   * @param error HTTP錯誤回應
   * @returns 錯誤Observable
   * @protected
   */
  protected handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '法人API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '企業資料格式錯誤或不完整';
          errorCode = 'INVALID_CORPORATE_DATA';
          break;
        case 401:
          errorMessage = '企業身份驗證失敗，請重新驗證憑證';
          errorCode = 'CORPORATE_AUTHENTICATION_FAILED';
          break;
        case 403:
          errorMessage = '企業沒有權限執行此操作';
          errorCode = 'INSUFFICIENT_CORPORATE_PERMISSIONS';
          break;
        case 404:
          errorMessage = '找不到指定的企業資源';
          errorCode = 'CORPORATE_RESOURCE_NOT_FOUND';
          break;
        case 408:
          errorMessage = '企業請求超時，請稍後再試';
          errorCode = 'CORPORATE_REQUEST_TIMEOUT';
          break;
        case 409:
          errorMessage = '企業資料衝突，請檢查統一編號和憑證';
          errorCode = 'CORPORATE_DATA_CONFLICT';
          break;
        case 422:
          errorMessage = '企業資料驗證失敗，請檢查統一編號和憑證';
          errorCode = 'CORPORATE_VALIDATION_FAILED';
          break;
        case 423:
          errorMessage = '工商憑證被鎖定，請稍後再試';
          errorCode = 'CERTIFICATE_LOCKED';
          break;
        case 429:
          errorMessage = '企業請求過於頻繁，請稍後再試';
          errorCode = 'CORPORATE_RATE_LIMIT_EXCEEDED';
          break;
        case 451:
          errorMessage = '企業帳戶受到法規限制';
          errorCode = 'CORPORATE_REGULATORY_RESTRICTION';
          break;
        case 500:
          errorMessage = '企業服務內部錯誤';
          errorCode = 'CORPORATE_INTERNAL_SERVER_ERROR';
          break;
        case 502:
          errorMessage = '企業服務暫時無法使用';
          errorCode = 'CORPORATE_SERVICE_UNAVAILABLE';
          break;
        case 503:
          errorMessage = '企業服務正在維護中';
          errorCode = 'CORPORATE_SERVICE_MAINTENANCE';
          break;
        default:
          errorMessage = `企業服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `CORPORATE_HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('法人API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    });
  }
}