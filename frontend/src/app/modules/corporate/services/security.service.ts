import { Injectable, OnDestroy } from '@angular/core';
import { Observable, of, throwError, BehaviorSubject, Subject, timer, fromEvent } from 'rxjs';
import { delay, map, catchError, retry, timeout, switchMap, takeUntil, share } from 'rxjs/operators';

/**
 * 讀卡機連接狀態
 */
export interface PcscReaderStatus {
  isAvailable: boolean;                 // PC/SC服務是否可用
  readers: PcscReader[];                // 連接的讀卡機列表
  error?: string;                       // 錯誤訊息
}

/**
 * PC/SC讀卡機資訊
 */
export interface PcscReader {
  name: string;                         // 讀卡機名稱
  state: 'present' | 'empty' | 'unavailable'; // 狀態
  atr?: string;                         // ATR (Answer To Reset)
  protocol?: 'T0' | 'T1';               // 通訊協定
  driver?: string;                      // 驅動程式版本
  lastUpdate: Date;                     // 最後更新時間
}

/**
 * 工商憑證資訊
 */
export interface BusinessCertificate {
  serialNumber: string;                 // 憑證序號
  issuer: string;                       // 發證機關
  subject: string;                      // 憑證主體
  validFrom: Date;                      // 有效期起始
  validTo: Date;                        // 有效期結束
  unifiedNumber: string;                // 統一編號
  companyName: string;                  // 公司名稱
  representativeName: string;           // 代表人姓名
  keyUsage: string[];                   // 金鑰用途
  certificateData: string;              // 憑證完整資料 (Base64)
  publicKey: string;                    // 公鑰 (Base64)
  pinAttempts: number;                  // PIN碼剩餘嘗試次數
}

/**
 * 數位簽章結果
 */
export interface DigitalSignature {
  signature: string;                    // 簽章值 (Base64)
  algorithm: string;                    // 簽章演算法
  timestamp: Date;                      // 簽章時間
  signerCertificate: BusinessCertificate; // 簽章者憑證
  hashAlgorithm: string;                // 雜湊演算法
  originalData?: string;                // 原始資料雜湊
}

/**
 * 加密通訊設定
 */
export interface EncryptionConfig {
  algorithm: 'RSA-OAEP' | 'AES-GCM' | 'AES-CBC'; // 加密演算法
  keyLength: 256 | 512 | 1024 | 2048;   // 金鑰長度
  sessionTimeout: number;               // 會話逾時 (秒)
  enableTLS: boolean;                   // 啟用 TLS
  enableHSM: boolean;                   // 啟用 HSM (硬體安全模組)
}

/**
 * 安全性事件
 */
export interface SecurityEvent {
  id: string;                           // 事件ID
  type: SecurityEventType;              // 事件類型
  level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL'; // 嚴重程度
  timestamp: Date;                      // 發生時間
  source: string;                       // 事件來源
  message: string;                      // 事件訊息
  details?: any;                        // 詳細資料
  userId?: string;                      // 使用者ID
  sessionId?: string;                   // 會話ID
}

/**
 * 安全性事件類型
 */
export enum SecurityEventType {
  CERTIFICATE_READ = 'CERTIFICATE_READ',
  CERTIFICATE_VERIFY = 'CERTIFICATE_VERIFY',
  PIN_VERIFY = 'PIN_VERIFY',
  PIN_LOCKED = 'PIN_LOCKED',
  SIGNATURE_CREATE = 'SIGNATURE_CREATE',
  SIGNATURE_VERIFY = 'SIGNATURE_VERIFY',
  ENCRYPTION_START = 'ENCRYPTION_START',
  DECRYPTION_START = 'DECRYPTION_START',
  READER_CONNECT = 'READER_CONNECT',
  READER_DISCONNECT = 'READER_DISCONNECT',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  SESSION_START = 'SESSION_START',
  SESSION_END = 'SESSION_END',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

/**
 * 驗證結果
 */
export interface ValidationResult {
  isValid: boolean;                     // 驗證是否成功
  errorCode?: string;                   // 錯誤代碼
  errorMessage?: string;                // 錯誤訊息
  details?: any;                        // 詳細資料
  timestamp: Date;                      // 驗證時間
}

/**
 * 系統平台資訊
 */
export interface PlatformInfo {
  os: 'windows' | 'macos' | 'linux' | 'unknown'; // 作業系統
  architecture: string;                // 系統架構
  browserName: string;                  // 瀏覽器名稱
  browserVersion: string;               // 瀏覽器版本
  supportsPCSC: boolean;                // 是否支援 PC/SC
  supportsWebCrypto: boolean;           // 是否支援 Web Crypto API
  supportsSmartCard: boolean;           // 是否支援智慧卡
  installedReaderDrivers: string[];     // 已安裝的讀卡機驅動
}

/**
 * 重試設定
 */
export interface RetryConfig {
  maxAttempts: number;                  // 最大重試次數
  delayMs: number;                      // 重試延遲 (毫秒)
  backoffMultiplier: number;            // 退避倍數
  timeoutMs: number;                    // 逾時時間 (毫秒)
}

/**
 * 法人安全性服務
 * 提供企業級安全功能，包含工商憑證驗證、PC/SC讀卡機整合、數位簽章、加密通訊和安全監控
 */
@Injectable({
  providedIn: 'root'
})
export class SecurityService implements OnDestroy {

  // 讀卡機狀態
  private readerStatusSubject = new BehaviorSubject<PcscReaderStatus>({
    isAvailable: false,
    readers: []
  });
  public readerStatus$ = this.readerStatusSubject.asObservable();

  // 目前使用的憑證
  private currentCertificateSubject = new BehaviorSubject<BusinessCertificate | null>(null);
  public currentCertificate$ = this.currentCertificateSubject.asObservable();

  // 安全性事件流
  private securityEventSubject = new Subject<SecurityEvent>();
  public securityEvents$ = this.securityEventSubject.asObservable();

  // 加密會話狀態
  private encryptionSessionSubject = new BehaviorSubject<boolean>(false);
  public encryptionSession$ = this.encryptionSessionSubject.asObservable();

  // 服務銷毀通知
  private destroy$ = new Subject<void>();

  // 預設設定
  private readonly defaultRetryConfig: RetryConfig = {
    maxAttempts: 3,
    delayMs: 1000,
    backoffMultiplier: 2,
    timeoutMs: 30000
  };

  private readonly defaultEncryptionConfig: EncryptionConfig = {
    algorithm: 'RSA-OAEP',
    keyLength: 2048,
    sessionTimeout: 1800, // 30分鐘
    enableTLS: true,
    enableHSM: false
  };

  // 會話管理
  private sessionId = '';
  private sessionStartTime: Date | null = null;
  private sessionTimer: any;

  constructor() {
    this.initializeService();
    this.logSecurityEvent(SecurityEventType.SESSION_START, 'INFO', '安全性服務已初始化');
  }

  /**
   * 初始化服務
   */
  private initializeService(): void {
    this.sessionId = this.generateSessionId();
    this.sessionStartTime = new Date();
    
    // 啟動讀卡機監控
    this.startReaderMonitoring();
    
    // 啟動會話監控
    this.startSessionMonitoring();
    
    console.log('法人安全性服務已初始化', { sessionId: this.sessionId });
  }

  /**
   * 產生會話ID
   */
  private generateSessionId(): string {
    return 'SEC_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 啟動讀卡機監控
   */
  private startReaderMonitoring(): void {
    // 定期檢查讀卡機狀態
    timer(0, 5000) // 每5秒檢查一次
      .pipe(
        takeUntil(this.destroy$),
        switchMap(() => this.detectReaders())
      )
      .subscribe({
        next: (status) => {
          this.readerStatusSubject.next(status);
        },
        error: (error) => {
          this.logSecurityEvent(
            SecurityEventType.SYSTEM_ERROR,
            'ERROR',
            '讀卡機監控發生錯誤',
            { error: error.message }
          );
        }
      });
  }

  /**
   * 啟動會話監控
   */
  private startSessionMonitoring(): void {
    this.sessionTimer = setInterval(() => {
      if (this.sessionStartTime) {
        const now = new Date();
        const elapsed = (now.getTime() - this.sessionStartTime.getTime()) / 1000;
        
        if (elapsed > this.defaultEncryptionConfig.sessionTimeout) {
          this.endSession();
        }
      }
    }, 60000); // 每分鐘檢查一次
  }

  /**
   * 檢測平台資訊
   */
  getPlatformInfo(): Observable<PlatformInfo> {
    return of(null).pipe(
      map(() => {
        const userAgent = navigator.userAgent.toLowerCase();
        const platform = navigator.platform.toLowerCase();
        
        let os: 'windows' | 'macos' | 'linux' | 'unknown' = 'unknown';
        if (platform.includes('win')) os = 'windows';
        else if (platform.includes('mac')) os = 'macos';
        else if (platform.includes('linux')) os = 'linux';

        const browserMatch = userAgent.match(/(chrome|firefox|safari|edge|opera)\/?([\d.]+)/);
        const browserName = browserMatch ? browserMatch[1] : 'unknown';
        const browserVersion = browserMatch ? browserMatch[2] : 'unknown';

        return {
          os,
          architecture: platform,
          browserName,
          browserVersion,
          supportsPCSC: this.checkPCSCSupport(),
          supportsWebCrypto: 'crypto' in window && 'subtle' in window.crypto,
          supportsSmartCard: this.checkSmartCardSupport(),
          installedReaderDrivers: this.getInstalledDrivers()
        };
      }),
      share()
    );
  }

  /**
   * 檢查 PC/SC 支援
   */
  private checkPCSCSupport(): boolean {
    // 檢查是否有 PC/SC 相關的 API 或擴充功能
    return typeof (window as any).pcsclite !== 'undefined' || 
           typeof (window as any).smartcard !== 'undefined' ||
           typeof (window as any).chrome?.smartCard !== 'undefined';
  }

  /**
   * 檢查智慧卡支援
   */
  private checkSmartCardSupport(): boolean {
    return this.checkPCSCSupport() && 
           'crypto' in window && 
           'subtle' in window.crypto;
  }

  /**
   * 取得已安裝的讀卡機驅動
   */
  private getInstalledDrivers(): string[] {
    // 這裡應該透過原生擴充功能或 ActiveX 來檢查
    // 模擬常見的讀卡機驅動
    const commonDrivers = [
      'PC/SC Smart Card',
      'Microsoft Smart Card Base Components',
      'EZ100PU Smart Card Reader',
      'ACR38 Smart Card Reader',
      'CCID Driver'
    ];
    
    return commonDrivers; // 實際應該檢查系統
  }

  /**
   * 檢測讀卡機
   */
  detectReaders(retryConfig?: RetryConfig): Observable<PcscReaderStatus> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };
    
    return this.simulateReaderDetection().pipe(
      timeout(config.timeoutMs),
      retry({
        count: config.maxAttempts,
        delay: (error, retryIndex) => timer(config.delayMs * Math.pow(config.backoffMultiplier, retryIndex))
      }),
      map(status => {
        this.logSecurityEvent(
          SecurityEventType.READER_CONNECT,
          'INFO',
          '讀卡機檢測完成',
          { readersFound: status.readers.length }
        );
        return status;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.SYSTEM_ERROR,
          'ERROR',
          '讀卡機檢測失敗',
          { error: error.message }
        );
        return of({
          isAvailable: false,
          readers: [],
          error: error.message
        });
      })
    );
  }

  /**
   * 模擬讀卡機檢測 (實際應該使用 PC/SC API)
   */
  private simulateReaderDetection(): Observable<PcscReaderStatus> {
    return of({
      isAvailable: true,
      readers: [
        {
          name: 'EZ100PU Smart Card Reader 0',
          state: 'present',
          atr: '3BDF96FF8131FE455A018048564953414C4C33302D53333733',
          protocol: 'T1',
          driver: 'CCID Driver v1.4.30',
          lastUpdate: new Date()
        },
        {
          name: 'Microsoft Usbccid Smartcard Reader (WUDF) 1',
          state: 'empty',
          driver: 'Microsoft WUDF CCID Driver v6.3.9600',
          lastUpdate: new Date()
        }
      ]
    }).pipe(delay(2000));
  }

  /**
   * 讀取工商憑證
   */
  readBusinessCertificate(readerName?: string): Observable<BusinessCertificate> {
    this.logSecurityEvent(
      SecurityEventType.CERTIFICATE_READ,
      'INFO',
      '開始讀取工商憑證',
      { readerName }
    );

    return this.simulateCertificateRead().pipe(
      timeout(this.defaultRetryConfig.timeoutMs),
      map(cert => {
        this.currentCertificateSubject.next(cert);
        this.logSecurityEvent(
          SecurityEventType.CERTIFICATE_READ,
          'INFO',
          '工商憑證讀取成功',
          { 
            companyName: cert.companyName,
            unifiedNumber: cert.unifiedNumber,
            serialNumber: cert.serialNumber
          }
        );
        return cert;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.SYSTEM_ERROR,
          'ERROR',
          '工商憑證讀取失敗',
          { error: error.message }
        );
        return throwError(() => new Error(`憑證讀取失敗: ${error.message}`));
      })
    );
  }

  /**
   * 模擬憑證讀取
   */
  private simulateCertificateRead(): Observable<BusinessCertificate> {
    return of({
      serialNumber: '1A2B3C4D5E6F7890ABCDEF1234567890',
      issuer: 'CN=GCA-工商憑證管理中心, O=政府憑證總管理中心, C=TW',
      subject: 'CN=凱基科技股份有限公司, OU=12345678, O=凱基科技股份有限公司, C=TW',
      validFrom: new Date('2023-01-01'),
      validTo: new Date('2025-12-31'),
      unifiedNumber: '12345678',
      companyName: '凱基科技股份有限公司',
      representativeName: '王大明',
      keyUsage: ['digitalSignature', 'keyEncipherment', 'dataEncipherment'],
      certificateData: 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...',
      publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...',
      pinAttempts: 3
    }).pipe(delay(3000));
  }

  /**
   * 驗證 PIN 碼
   */
  verifyPin(pin: string, retryConfig?: RetryConfig): Observable<ValidationResult> {
    if (!pin || pin.length < 4) {
      return throwError(() => new Error('PIN碼格式錯誤'));
    }

    this.logSecurityEvent(
      SecurityEventType.PIN_VERIFY,
      'INFO',
      'PIN碼驗證開始'
    );

    const config = { ...this.defaultRetryConfig, ...retryConfig };
    
    return this.simulatePinVerification(pin).pipe(
      timeout(config.timeoutMs),
      map(result => {
        if (result.isValid) {
          this.logSecurityEvent(
            SecurityEventType.PIN_VERIFY,
            'INFO',
            'PIN碼驗證成功'
          );
        } else {
          this.logSecurityEvent(
            SecurityEventType.PIN_VERIFY,
            'WARNING',
            'PIN碼驗證失敗',
            { errorCode: result.errorCode }
          );
        }
        return result;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.PIN_LOCKED,
          'ERROR',
          'PIN碼驗證發生錯誤',
          { error: error.message }
        );
        return throwError(() => error);
      })
    );
  }

  /**
   * 模擬 PIN 碼驗證
   */
  private simulatePinVerification(pin: string): Observable<ValidationResult> {
    const isCorrect = pin === '123456'; // 模擬正確的PIN碼
    
    return of({
      isValid: isCorrect,
      errorCode: isCorrect ? undefined : 'INVALID_PIN',
      errorMessage: isCorrect ? undefined : 'PIN碼錯誤',
      timestamp: new Date()
    }).pipe(delay(2000));
  }

  /**
   * 創建數位簽章
   */
  createDigitalSignature(
    data: string, 
    pin: string, 
    hashAlgorithm: 'SHA-256' | 'SHA-512' = 'SHA-256'
  ): Observable<DigitalSignature> {
    const currentCert = this.currentCertificateSubject.value;
    if (!currentCert) {
      return throwError(() => new Error('尚未讀取憑證'));
    }

    this.logSecurityEvent(
      SecurityEventType.SIGNATURE_CREATE,
      'INFO',
      '開始創建數位簽章',
      { algorithm: hashAlgorithm, dataLength: data.length }
    );

    return this.verifyPin(pin).pipe(
      switchMap(pinResult => {
        if (!pinResult.isValid) {
          throw new Error('PIN碼驗證失敗');
        }
        return this.simulateSignatureCreation(data, currentCert, hashAlgorithm);
      }),
      map(signature => {
        this.logSecurityEvent(
          SecurityEventType.SIGNATURE_CREATE,
          'INFO',
          '數位簽章創建成功',
          { algorithm: signature.algorithm }
        );
        return signature;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.SYSTEM_ERROR,
          'ERROR',
          '數位簽章創建失敗',
          { error: error.message }
        );
        return throwError(() => error);
      })
    );
  }

  /**
   * 模擬數位簽章創建
   */
  private simulateSignatureCreation(
    data: string, 
    certificate: BusinessCertificate,
    hashAlgorithm: string
  ): Observable<DigitalSignature> {
    return of({
      signature: btoa(data + '_SIGNED_' + Date.now()),
      algorithm: 'RSA-SHA256',
      timestamp: new Date(),
      signerCertificate: certificate,
      hashAlgorithm,
      originalData: btoa(data)
    }).pipe(delay(3000));
  }

  /**
   * 驗證數位簽章
   */
  verifyDigitalSignature(
    signature: DigitalSignature, 
    originalData: string
  ): Observable<ValidationResult> {
    this.logSecurityEvent(
      SecurityEventType.SIGNATURE_VERIFY,
      'INFO',
      '開始驗證數位簽章',
      { algorithm: signature.algorithm }
    );

    return this.simulateSignatureVerification(signature, originalData).pipe(
      map(result => {
        this.logSecurityEvent(
          SecurityEventType.SIGNATURE_VERIFY,
          result.isValid ? 'INFO' : 'WARNING',
          `數位簽章驗證${result.isValid ? '成功' : '失敗'}`,
          { errorCode: result.errorCode }
        );
        return result;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.SYSTEM_ERROR,
          'ERROR',
          '數位簽章驗證發生錯誤',
          { error: error.message }
        );
        return throwError(() => error);
      })
    );
  }

  /**
   * 模擬數位簽章驗證
   */
  private simulateSignatureVerification(
    signature: DigitalSignature,
    originalData: string
  ): Observable<ValidationResult> {
    const isValid = signature.originalData === btoa(originalData);
    
    return of({
      isValid,
      errorCode: isValid ? undefined : 'SIGNATURE_MISMATCH',
      errorMessage: isValid ? undefined : '簽章驗證失敗',
      timestamp: new Date()
    }).pipe(delay(2000));
  }

  /**
   * 啟動加密會話
   */
  startEncryptionSession(config?: EncryptionConfig): Observable<boolean> {
    const sessionConfig = { ...this.defaultEncryptionConfig, ...config };
    
    this.logSecurityEvent(
      SecurityEventType.ENCRYPTION_START,
      'INFO',
      '啟動加密會話',
      { algorithm: sessionConfig.algorithm, keyLength: sessionConfig.keyLength }
    );

    return this.simulateEncryptionSessionStart().pipe(
      map(success => {
        this.encryptionSessionSubject.next(success);
        if (success) {
          this.logSecurityEvent(
            SecurityEventType.ENCRYPTION_START,
            'INFO',
            '加密會話啟動成功'
          );
        }
        return success;
      }),
      catchError(error => {
        this.logSecurityEvent(
          SecurityEventType.SYSTEM_ERROR,
          'ERROR',
          '加密會話啟動失敗',
          { error: error.message }
        );
        return of(false);
      })
    );
  }

  /**
   * 模擬加密會話啟動
   */
  private simulateEncryptionSessionStart(): Observable<boolean> {
    return of(true).pipe(delay(1000));
  }

  /**
   * 加密資料
   */
  encryptData(data: string, algorithm?: string): Observable<string> {
    if (!this.encryptionSessionSubject.value) {
      return throwError(() => new Error('加密會話尚未啟動'));
    }

    return this.simulateDataEncryption(data).pipe(
      map(encryptedData => {
        this.logSecurityEvent(
          SecurityEventType.ENCRYPTION_START,
          'INFO',
          '資料加密完成',
          { algorithm, dataLength: data.length }
        );
        return encryptedData;
      })
    );
  }

  /**
   * 解密資料
   */
  decryptData(encryptedData: string, algorithm?: string): Observable<string> {
    if (!this.encryptionSessionSubject.value) {
      return throwError(() => new Error('加密會話尚未啟動'));
    }

    return this.simulateDataDecryption(encryptedData).pipe(
      map(decryptedData => {
        this.logSecurityEvent(
          SecurityEventType.DECRYPTION_START,
          'INFO',
          '資料解密完成',
          { algorithm }
        );
        return decryptedData;
      })
    );
  }

  /**
   * 模擬資料加密
   */
  private simulateDataEncryption(data: string): Observable<string> {
    return of(btoa(data + '_ENCRYPTED')).pipe(delay(500));
  }

  /**
   * 模擬資料解密
   */
  private simulateDataDecryption(encryptedData: string): Observable<string> {
    const decoded = atob(encryptedData);
    const originalData = decoded.replace('_ENCRYPTED', '');
    return of(originalData).pipe(delay(500));
  }

  /**
   * 驗證憑證有效性
   */
  validateCertificate(certificate?: BusinessCertificate): Observable<ValidationResult> {
    const cert = certificate || this.currentCertificateSubject.value;
    if (!cert) {
      return throwError(() => new Error('沒有可驗證的憑證'));
    }

    return of(null).pipe(
      delay(1000),
      map(() => {
        const now = new Date();
        const isValid = cert.validFrom <= now && cert.validTo >= now;
        
        this.logSecurityEvent(
          SecurityEventType.CERTIFICATE_VERIFY,
          isValid ? 'INFO' : 'WARNING',
          `憑證驗證${isValid ? '成功' : '失敗'}`,
          { 
            serialNumber: cert.serialNumber,
            validFrom: cert.validFrom,
            validTo: cert.validTo
          }
        );

        return {
          isValid,
          errorCode: isValid ? undefined : 'CERTIFICATE_EXPIRED',
          errorMessage: isValid ? undefined : '憑證已過期或尚未生效',
          timestamp: new Date(),
          details: {
            validFrom: cert.validFrom,
            validTo: cert.validTo,
            daysUntilExpiry: Math.ceil((cert.validTo.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
          }
        };
      })
    );
  }

  /**
   * 記錄安全性事件
   */
  private logSecurityEvent(
    type: SecurityEventType,
    level: 'INFO' | 'WARNING' | 'ERROR' | 'CRITICAL',
    message: string,
    details?: any
  ): void {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      type,
      level,
      timestamp: new Date(),
      source: 'SecurityService',
      message,
      details,
      sessionId: this.sessionId
    };

    this.securityEventSubject.next(event);
    console.log(`[${level}] ${message}`, event);
  }

  /**
   * 產生事件ID
   */
  private generateEventId(): string {
    return 'EVT_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
  }

  /**
   * 取得安全性統計
   */
  getSecurityStatistics(): Observable<any> {
    return this.securityEvents$.pipe(
      map(events => {
        // 這裡應該實作統計邏輯
        return {
          totalEvents: 0,
          eventsByType: {},
          eventsByLevel: {},
          lastEventTime: null
        };
      })
    );
  }

  /**
   * 結束會話
   */
  endSession(): void {
    this.logSecurityEvent(
      SecurityEventType.SESSION_END,
      'INFO',
      '安全性會話結束'
    );

    this.currentCertificateSubject.next(null);
    this.encryptionSessionSubject.next(false);
    
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer);
    }

    this.sessionStartTime = null;
  }

  /**
   * 銷毀服務
   */
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.endSession();
  }

  /**
   * 重設 PIN 碼嘗試次數 (管理員功能)
   */
  resetPinAttempts(): Observable<boolean> {
    const currentCert = this.currentCertificateSubject.value;
    if (currentCert) {
      currentCert.pinAttempts = 3;
      this.currentCertificateSubject.next(currentCert);
      
      this.logSecurityEvent(
        SecurityEventType.PIN_VERIFY,
        'INFO',
        'PIN碼嘗試次數已重設'
      );
    }
    
    return of(true).pipe(delay(500));
  }

  /**
   * 取得目前會話資訊
   */
  getSessionInfo(): { sessionId: string; startTime: Date | null; isActive: boolean } {
    return {
      sessionId: this.sessionId,
      startTime: this.sessionStartTime,
      isActive: this.sessionStartTime !== null
    };
  }

  /**
   * 檢查服務健康狀態
   */
  healthCheck(): Observable<any> {
    return this.getPlatformInfo().pipe(
      switchMap(platform => this.detectReaders()),
      map(readerStatus => ({
        serviceStatus: 'healthy',
        timestamp: new Date(),
        platform: platform,
        readers: readerStatus,
        session: this.getSessionInfo(),
        encryptionActive: this.encryptionSessionSubject.value
      }))
    );
  }
}