import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// Corporate 元件 (6個頁面)
import { CLandingComponent } from './components/c-landing/c-landing.component';
import { CCertificateVerificationComponent } from './components/c-certificate-verification/c-certificate-verification.component';
import { CCompanyInfoComponent } from './components/c-company-info/c-company-info.component';
import { CRemittanceDetailComponent } from './components/c-remittance-detail/c-remittance-detail.component';
import { CAmountConfirmationComponent } from './components/c-amount-confirmation/c-amount-confirmation.component';
import { CApplicationCompleteComponent } from './components/c-application-complete/c-application-complete.component';

/**
 * Corporate Module 路由配置
 * 
 * 法人解款流程路由規劃 (6個頁面)
 */
const routes: Routes = [
  {
    path: '',
    redirectTo: 'landing',
    pathMatch: 'full'
  },
  // 主要流程路由 (6個頁面)
  {
    path: 'landing',
    component: CLandingComponent,
    data: { 
      title: '企業條款同意',
      description: '企業解款服務入口'
    }
  },
  {
    path: 'company-info',
    component: CCompanyInfoComponent,
    data: { 
      title: '法人驗證資料輸入',
      step: 1,
      totalSteps: 3,
      description: '企業基本資料確認'
    }
  },
  {
    path: 'certificate-verification',
    component: CCertificateVerificationComponent,
    data: { 
      title: '工商憑證驗證',
      step: 1,
      totalSteps: 3,
      description: '工商憑證讀取與PIN碼驗證'
    }
  },
  {
    path: 'remittance-detail',
    component: CRemittanceDetailComponent,
    data: { 
      title: '匯款確認頁',
      step: 2,
      totalSteps: 3,
      description: '確認金額'
    }
  },
  {
    path: 'amount-confirmation',
    component: CAmountConfirmationComponent,
    data: { 
      title: '金額確認頁',
      step: 3,
      totalSteps: 3,
      description: '確認交易'
    }
  },
  {
    path: 'application-complete',
    component: CApplicationCompleteComponent,
    data: { 
      title: '申請完成頁',
      step: 0,
      totalSteps: 3,
      description: '企業解款申請完成'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CorporateRoutingModule { }