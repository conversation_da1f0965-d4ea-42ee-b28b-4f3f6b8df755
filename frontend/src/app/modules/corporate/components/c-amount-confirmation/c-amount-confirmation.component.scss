/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 步驟指示器 === */
.step-indicator {
  margin-bottom: 40px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-current {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
}

.step-total {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.step-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-current-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-total-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0044ad;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* === 確認資訊卡片 === */
.confirmation-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

/* === 參與者卡片 === */
.participant-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.participant-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
  color: #ffffff;
  background: #8e9aaf;
}

.avatar-circle.euro {
  background: #4a90e2;
}

.currency-symbol {
  font-size: 20px;
}

.avatar-initial {
  font-size: 18px;
}

.participant-info {
  flex: 1;
}

.participant-name {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.participant-bank {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 2px 0;
}

.participant-branch {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 2px 0;
}

.participant-account {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.transfer-arrow {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  background: #ffffff;
  padding: 0 8px;
}

.arrow-down {
  color: #4a90e2;
  font-size: 16px;
}

/* === 資訊區塊 === */
.info-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
}

.section-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.info-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: right;
}

.info-value.highlight {
  color: #0044ad;
  font-weight: 500;
  font-size: 16px;
}

/* === 手續費區塊 === */
.fee-section {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
}

.fee-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.fee-item:not(:last-child) {
  border-bottom: 1px dashed #ffeaa7;
}

.fee-item.total {
  padding-top: 12px;

  .fee-label {
    font-weight: 500;
    font-size: 16px;
  }

  .fee-value {
    font-size: 20px;
    font-weight: 600;
    color: #856404;
  }
}

.fee-label {
  color: #856404;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
}

.fee-value {
  color: #856404;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

/* === 通知設定 === */
.notification-settings {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
}

.settings-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
}

.settings-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* === 自訂勾選框 === */
.checkbox-container {
  display: flex;
  align-items: flex-start;
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  color: #041c43;
  line-height: 150%;
}

.checkbox-container input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-custom {
  position: absolute;
  top: 2px;
  left: 0;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border: 2px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.checkbox-container input[type="checkbox"]:checked ~ .checkbox-custom {
  background: #0044ad;
  border-color: #0044ad;
}

.checkbox-container input[type="checkbox"]:checked ~ .checkbox-custom::after {
  content: '';
  position: absolute;
  display: block;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.phone-number {
  color: #666666;
  font-size: 13px;
  margin-left: 8px;
}

.sub-text {
  display: block;
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

/* === Email 輸入區 === */
.email-input-wrapper {
  margin-top: -8px;
  margin-left: 32px;
}

.email-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.email-input:focus {
  border-color: #0044ad;
  box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
}

.email-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 8px;
}

.error-icon {
  font-size: 14px;
}

.error-text {
  color: #dc3545;
  font-size: 13px;
}

/* === 重要提醒 === */
.important-notice {
  background: #e8f4f8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.notice-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.notice-icon {
  font-size: 20px;
}

.notice-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.notice-list {
  margin: 0;
  padding-left: 20px;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 180%;
}

.notice-list li {
  margin-bottom: 8px;
}

/* === 最終確認 === */
.final-confirmation {
  background: #f0f7ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.checkbox-container.large {
  .checkbox-text {
    strong {
      color: #041c43;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 單一按鈕樣式 */
.btn-primary.submit-only {
  width: 100%;
  max-width: 320px;
  margin: 0 auto;
}

.btn-primary, .btn-secondary {
  min-width: 140px;
  height: 56px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  padding: 0 24px;
}

.btn-primary {
  background: #0044ad;
  flex: 1;
  max-width: 200px;

  &:hover:not(:disabled) {
    background: #003390;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 68, 173, 0.3);
  }

  &:disabled {
    background: #e2e2e2;
    cursor: not-allowed;

    .button-text-zh,
    .button-text-en {
      color: #a6a6a6;
    }
  }
}

.btn-secondary {
  background: #ffffff;
  border: 2px solid #0044ad;

  &:hover {
    background: #f0f7ff;
  }

  .button-text-zh,
  .button-text-en {
    color: #0044ad;
  }
}

.button-text-zh {
  color: #ffffff;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
}

.button-text-en {
  color: #ffffff;
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
}

/* === 測試資訊 === */
.test-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  margin: 20px;
}

.test-info h4 {
  margin: 0 0 10px 0;
  color: #0044ad;
}

.test-info p {
  margin: 5px 0;
  font-size: 0.9em;
  color: #666;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }

  .content-section {
    padding: 48px;
  }

  .main-title {
    font-size: 26px;
  }

  .subtitle {
    font-size: 15px;
  }

  .step-current {
    font-size: 20px;
  }

  .confirmation-card {
    padding: 32px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }

  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }

  .content-section {
    padding: 60px;
  }

  .action-section {
    gap: 24px;
  }

  .btn-primary,
  .btn-secondary {
    min-width: 160px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }

  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }

  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  .content-section {
    padding: 24px 20px;
  }

  .main-title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 12px;
  }

  .step-current {
    font-size: 16px;
  }

  .step-total {
    font-size: 14px;
  }

  .action-section {
    flex-direction: column;
    gap: 12px;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 100%;
    height: 48px;
    min-width: 100%;
  }

  .button-text-zh,
  .button-text-en {
    font-size: 16px;
  }

  .confirmation-card {
    padding: 16px;
  }

  .notification-settings,
  .important-notice,
  .final-confirmation {
    padding: 16px;
  }

  /* 參與者卡片手機版調整 */
  .participant-card {
    padding: 16px;
    margin-bottom: 12px;
  }

  .participant-name {
    font-size: 16px;
  }

  .participant-bank,
  .participant-branch,
  .participant-account {
    font-size: 13px;
  }

  .avatar-circle {
    width: 40px;
    height: 40px;
  }

  .currency-symbol,
  .avatar-initial {
    font-size: 16px;
  }

  /* 預計入帳金額手機版調整 */
  .deposit-amount-section {
    padding: 20px 16px;
  }

  .amount-display {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .amount-number {
    font-size: 28px;
  }

  .amount-title {
    font-size: 16px;
  }

  .currency-info {
    justify-content: center;
  }

  .flag-icon {
    width: 20px;
    height: 14px;
  }

  /* 展開詳情按鈕手機版調整 */
  .expand-btn {
    padding: 12px 20px;
    font-size: 16px;
  }

  .expand-icon {
    font-size: 14px;
  }
}

/* === 展開詳情按鈕 === */
.expand-details {
  text-align: center;
  margin-bottom: 24px;
}

.expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.expand-btn:hover {
  background: #f0f7ff;
}

.expand-details-image {
  width: 24px;
  height: 24px;
  display: block;
  transition: transform 0.2s ease;
}

.expand-details-image.expanded {
  transform: rotate(180deg);
}

/* === 詳細資訊區域 === */
.details-section {
  margin-bottom: 24px;
  animation: slideDown 0.3s ease;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e8e8e8;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 800px;
  }
}

/* === 詳細資訊項目行 === */
.detail-item-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-item-row:last-of-type {
  border-bottom: none;
}

.detail-item-row .detail-label {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-item-row .label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.detail-item-row .label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.3;
}

.detail-item-row .detail-value {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  max-width: 50%;
  word-break: break-word;
  line-height: 1.4;
}

.info-icon {
  color: #666666;
  font-size: 14px;
  margin-left: 4px;
}

/* === 預計入帳金額特殊樣式 === */
.detail-item-row:first-child .detail-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.currency-flag {
  width: 20px;
  height: 14px;
  border-radius: 2px;
  display: inline-block;
  margin-right: 8px;
}

.currency-text {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 600;
}

.amount-large {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  font-weight: 700;
}

/* === 同意條款區域 === */
.agreement-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

.checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.agreement-checkbox {
  width: 18px;
  height: 18px;
  border: 1.5px solid #0044ad;
  border-radius: 3px;
  background: #ffffff;
  cursor: pointer;
  flex-shrink: 0;
  margin-top: 2px;
}

.agreement-checkbox:checked {
  background: #0044ad;
  border-color: #0044ad;
}

.agreement-checkbox:checked::after {
  content: '✓';
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.agreement-label {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
  cursor: pointer;
  flex: 1;
}

.agreement-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin: 0;
}

.highlight-text {
  color: #4a90e2;
  font-weight: 500;
}

/* === 收合按鈕 === */
.collapse-details {
  text-align: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f5f5f5;
}

.collapse-btn {
  background: none;
  border: none;
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  padding: 8px 16px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.collapse-btn:hover {
  background-color: #f8f9fa;
}

.collapse-text {
  color: #0044ad;
}

.collapse-icon {
  width: 16px;
  height: 16px;
}

.collapse-icon.up-arrow {
  transform: rotate(180deg);
}

/* === Info項目樣式 === */
.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  color: #666666;
  font-size: 14px;
  cursor: help;
}

/* === 預計入帳金額區域 === */
.deposit-amount-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  text-align: center;
}

.amount-header {
  margin-bottom: 20px;
}

.amount-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.amount-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.currency-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.flag-icon {
  width: 24px;
  height: 16px;
  border-radius: 2px;
}

.currency-code {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 600;
}

.currency-name {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.amount-value {
  flex: 1;
  text-align: right;
}

.amount-number {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 32px;
  font-weight: 700;
}

.amount-breakdown {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.breakdown-item:last-child {
  border-bottom: none;
}

.breakdown-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.breakdown-value {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

/* === 交易編號特殊樣式 === */
.info-section.transaction-number {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0;
  border-bottom: none;
}

.detail-label {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label .label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: inline;
  margin: 0;
}

.detail-label .label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  display: inline;
  margin: 0;
}

.detail-value {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  max-width: 50%;
  word-break: break-all;
}