/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 申請完成狀態 === */
.completion-status {
  text-align: center;
  margin-bottom: 32px;
}

.status-card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 0;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
}

.status-icon {
  margin-bottom: 16px;
  padding-top: 24px;
}

.success-icon {
  width: 60px;
  height: 60px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: white;
  font-size: 32px;
  font-weight: bold;
}

.status-content {
  padding: 0 24px 24px;
  margin-bottom: 0;
}

.status-text {
  margin-bottom: 12px;
}

.status-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.status-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

.completion-time {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

/* === 預計入帳金額 === */
.expected-amount {
  background: #ffffff;
  border-top: 1px solid #e8e8e8;
  padding: 24px;
  text-align: center;
  margin-top: 16px;
}

.amount-label {
  margin-bottom: 12px;
}

.label-zh {
  display: block;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.label-en {
  display: block;
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.amount-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 12px;
}

.currency {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 20px;
  font-weight: 500;
}

.amount {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 36px;
  font-weight: 600;
  letter-spacing: -0.5px;
}

/* === 匯款人和受款企業資訊 === */
.transfer-parties {
  margin-bottom: 32px;
}

.party-card {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.party-icon {
  flex-shrink: 0;
}

.icon-bg {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-bg.foreign-currency {
  background: #e3f2fd;
}

.icon-bg.recipient {
  background: #f3e5f5;
}

.icon {
  font-size: 24px;
}

.icon-letter {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 20px;
  font-weight: 600;
}

.party-info {
  flex: 1;
}

.party-name {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.party-details {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
  line-height: 1.4;
}

.party-details p {
  margin: 0 0 2px 0;
}

.arrow-down {
  color: #0044ad;
  font-size: 20px;
  font-weight: bold;
}

/* === 交易詳情 === */
.transaction-details {
  margin-bottom: 32px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item.transaction-number {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border-bottom: none;
}

.detail-label {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label .label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: inline;
  margin: 0;
}

.detail-label .label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  display: inline;
  margin: 0;
}

.info-icon {
  margin-left: 4px;
  font-size: 12px;
  color: #666666;
}

.detail-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  max-width: 50%;
}

/* === Karry 推廣區塊 === */
.karry-promotion {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.promotion-content {
  flex: 1;
}

.promotion-text h4 {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.promotion-text p {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 12px 0;
}

.karry-btn {
  background: #0044ad;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.karry-btn:hover {
  background: #003a94;
}

.promotion-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
}

.promotion-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 32px;
  align-items: center;
}

.btn-primary,
.btn-secondary {
  width: 280px;  // 固定寬度
  padding: 16px 24px;
  border-radius: 8px;
  border: none;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  min-height: 60px;  // 確保按鈕高度一致
}

.btn-primary {
  background: #0044ad;
  color: white;
}

.btn-primary:hover {
  background: #003a94;
}

.btn-secondary {
  background: #ffffff;
  color: #0044ad;
  border: 1px solid #0044ad;
}

.btn-secondary:hover {
  background: #f8f9fa;
}

.button-text-zh {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
}

.button-text-en {
  font-size: 12px;
  font-weight: 400;
  opacity: 0.8;
  line-height: 1.2;
}

.full-width {
  width: 100%;
}

/* === 測試資訊 === */
.test-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
  font-family: "Noto Sans TC", sans-serif;
}

.test-info h4 {
  color: #856404;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.test-info p {
  color: #856404;
  font-size: 12px;
  font-weight: 400;
  margin: 4px 0;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }

  .content-section {
    padding: 48px;
  }

  .main-title {
    font-size: 26px;
  }

  .subtitle {
    font-size: 15px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }

  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }

  .content-section {
    padding: 60px;
  }

  .action-section {
    gap: 24px;
  }

  .btn-primary,
  .btn-secondary {
    width: 320px;  // 在桌機版稍微加寬按鈕
    min-height: 64px;  // 增加高度
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }

  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }

  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  .content-section {
    padding: 24px 20px;
  }

  .main-title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 12px;
  }

  .action-section {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;  // 讓按鈕在小螢幕時佔滿寬度
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 100%;
    min-height: 56px;  // 增加高度以容納兩行文字
    min-width: 100%;
  }

  .button-text-zh,
  .button-text-en {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .main-content-wrapper {
    padding: 0 16px 16px;
  }

  .content-section {
    padding: 24px;
  }

  .main-title {
    font-size: 20px;
  }

  .status-icon {
    padding-top: 20px;
  }

  .status-content {
    padding: 0 20px 20px;
  }

  .expected-amount {
    padding: 20px;
  }

  .amount {
    font-size: 28px;
  }

  .currency {
    font-size: 16px;
  }

  .karry-promotion {
    flex-direction: column;
    text-align: center;
  }

  .promotion-image {
    width: 80px;
    height: 80px;
  }
}