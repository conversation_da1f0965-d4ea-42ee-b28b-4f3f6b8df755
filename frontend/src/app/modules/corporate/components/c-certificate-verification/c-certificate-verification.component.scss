/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 步驟指示器 === */
.step-indicator {
  margin-bottom: 32px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-current {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.step-total {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-current-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-total-en {
  color: #999999;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.step-progress {
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0044ad;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* === Step 1: 讀卡機連接中 === */
.reader-connecting-section {
  text-align: center;
  padding: 40px 0;
}

.connecting-animation {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.reader-icon svg {
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.connecting-info {
  margin-bottom: 24px;
}

.connecting-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 8px;
}

.connecting-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 24px;
}

.connecting-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.status-text {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.loading-dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #0044ad;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 60% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* === Step 2: 軟硬體下載 === */
.download-driver-section {
  text-align: center;
  padding: 20px 0;
}

.download-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.download-info {
  margin-bottom: 32px;
}

.download-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 8px;
}

.download-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.system-info-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
  text-align: left;
}

.system-info-card h4 {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 16px 0;
}

.system-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.detail-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.detail-value.compatible {
  color: #28a745;
}

.download-components {
  margin-bottom: 32px;
}

.download-components h4 {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 20px 0;
}

.download-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  text-align: left;
}

.item-number {
  width: 32px;
  height: 32px;
  background: #0044ad;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 600;
  flex-shrink: 0;
}

.item-content {
  flex: 1;
}

.item-content h5 {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.item-content p {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
  margin: 0 0 12px 0;
}

.btn-download {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #0044ad;
  color: #ffffff;
  border-radius: 6px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-download:hover {
  background: #003390;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
}

.btn-download svg {
  width: 16px;
  height: 16px;
}

.installation-note {
  background: #e8f4f8;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
}

.note-text {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  margin-bottom: 8px;
}

.note-text-en {
  color: #0066cc;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.6;
}

/* === Step 3: 選擇讀卡機 === */
.select-reader-section {
  padding: 20px 0;
}

.reader-list-header {
  text-align: center;
  margin-bottom: 32px;
}

.reader-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 8px;
}

.reader-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.reader-list {
  margin-bottom: 24px;
}

.reader-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reader-item:hover {
  border-color: #0044ad;
  background: #f0f6ff;
}

.reader-item.selected {
  border-color: #0044ad;
  background: #e8f4f8;
}

.reader-radio {
  flex-shrink: 0;
}

.radio-outer {
  width: 20px;
  height: 20px;
  border: 2px solid #999999;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s ease;
}

.reader-item.selected .radio-outer {
  border-color: #0044ad;
}

.radio-inner {
  width: 10px;
  height: 10px;
  background: #0044ad;
  border-radius: 50%;
}

.reader-info {
  flex: 1;
  text-align: left;
}

.reader-name {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.reader-status {
  color: #28a745;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

.reader-icon {
  flex-shrink: 0;
}

.reader-help-text {
  background: #e8f4f8;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  text-align: center;
}

.reader-help-text p {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 4px;
}

.help-text-en {
  color: #0066cc;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

/* === Step 4: 驗證工商憑證 === */
.verify-certificate-section {
  padding: 20px 0;
}

.certificate-prompt {
  text-align: center;
  margin-bottom: 32px;
}

.prompt-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.prompt-icon svg {
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
}

.prompt-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 8px;
}

.prompt-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 16px;
}

.reader-info {
  color: #999999;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.certificate-info-display {
  margin-bottom: 32px;
}

/* === PIN碼輸入 === */
.pin-input-section {
  text-align: center;
}

.certificate-display {
  margin-bottom: 32px;
}

.cert-card {
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e8e8e8;
}

.cert-card-header {
  background: #0044ad;
  color: #ffffff;
  padding: 12px 20px;
}

.cert-card-header h4 {
  margin: 0;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.cert-card-body {
  padding: 20px;
}

.cert-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.cert-info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.info-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

/* === PIN碼輸入區 === */
.pin-input-wrapper {
  margin-bottom: 32px;
}

.pin-labels {
  text-align: center;
  margin-bottom: 20px;
}

.pin-label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 4px;
}

.pin-label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.pin-input-container {
  margin-bottom: 16px;
}

.pin-input-field {
  position: relative;
  display: inline-block;
  width: 100%;
  max-width: 280px;
}

.pin-input {
  width: 100%;
  padding: 16px 48px 16px 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  letter-spacing: 4px;
  color: #041c43;
  background: #ffffff;
  transition: all 0.2s ease;
}

.pin-input::placeholder {
  color: #999999;
  letter-spacing: normal;
  font-size: 14px;
  font-weight: 400;
}

.pin-input:focus {
  outline: none;
  border-color: #0044ad;
  box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
}

.pin-input:disabled {
  background: #f8f9fa;
  color: #999999;
  cursor: not-allowed;
}

.pin-input.error {
  border-color: #dc3545;
}

.pin-input.error:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.pin-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 8px;
  cursor: pointer;
  color: #666666;
  transition: color 0.2s ease;
}

.pin-toggle:hover {
  color: #0044ad;
}

.pin-info {
  text-align: center;
}

.attempts-info {
  margin-bottom: 8px;
}

.attempts-warning {
  color: #ff6b00;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.attempts-warning.danger {
  color: #dc3545;
}

.pin-hint {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

.pin-error {
  color: #dc3545;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

/* === 驗證中進度 === */
.verification-progress {
  text-align: center;
  padding: 40px 0;
}

.progress-circle {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  position: relative;
}

.progress-circle svg {
  width: 120px;
  height: 120px;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  font-weight: 600;
}

.verification-message {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

/* === 載入中狀態 === */
.loading-section {
  text-align: center;
  padding: 40px 0;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.loading-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 8px;
}

.loading-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

/* === 錯誤訊息顯示 === */
.error-message-display {
  margin: 24px 0;
}

/* === 訊息顯示區 === */
.message-section {
  margin: 24px 0;
}

.alert {
  padding: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-error {
  background: #fee;
  border: 1px solid #ffcccc;
}

.alert-icon {
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
  color: #dc3545;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: left;
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  width: 100%;
  justify-content: center;
}

.btn-primary,
.btn-secondary {
  padding: 16px 32px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 120px;
}

.btn-primary {
  background: #0044ad;
  color: #ffffff;
}

.btn-primary:hover {
  background: #003390;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.3);
}

.btn-primary:disabled {
  background: #e2e2e2;
  color: #a6a6a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background: #ffffff;
  color: #0044ad;
  border: 2px solid #0044ad;
}

.btn-secondary:hover {
  background: #f0f6ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.2);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.2);
}

.btn-secondary:disabled {
  background: #f8f9fa;
  color: #999999;
  border-color: #e8e8e8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* === 測試資訊 === */
.test-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 32px;
  font-family: "Noto Sans TC", sans-serif;
}

.test-info h4 {
  color: #856404;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.test-info p {
  color: #856404;
  font-size: 12px;
  font-weight: 400;
  margin: 4px 0;
}

.dev-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.dev-btn {
  padding: 6px 12px;
  background: #ffc107;
  border-radius: 4px;
  color: #856404;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dev-btn:hover {
  background: #e0a800;
  color: #533f03;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }

  .content-section {
    padding: 48px;
  }

  .main-title {
    font-size: 26px;
  }

  .subtitle {
    font-size: 15px;
  }

  .pin-input {
    font-size: 20px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }

  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }

  .content-section {
    padding: 60px;
  }

  .btn-primary,
  .btn-secondary {
    min-width: 160px;
    padding: 18px 40px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }

  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }

  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  .content-section {
    padding: 24px 20px;
  }

  .main-title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 12px;
  }

  .substep-indicator {
    padding: 0;
  }

  .substep-label {
    font-size: 11px;
  }

  .pin-input {
    font-size: 16px;
    letter-spacing: 2px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}