<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container">
      
      <!-- 內容區塊 -->
      <div class="content-section">
        <!-- 標題區 -->
        <div class="title-section">
          <h1 class="main-title">線上外匯解款</h1>
          <p class="subtitle">Online Foreign Exchange Remittance</p>
        </div>

        <!-- 步驟指示器 -->
        <div class="step-indicator">
          <div class="step-info">
            <div class="step-current">第 1 步 驗證身份</div>
            <div class="step-total">共 3 步</div>
          </div>
          <div class="step-subtitle">
            <div class="step-current-en">Step 1 Verify Identity</div>
            <div class="step-total-en">3 Steps</div>
          </div>
          <div class="step-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="33.33"></div>
            </div>
          </div>
        </div>

        <!-- Step 1: 讀卡機連接中 -->
        <div class="reader-connecting-section" *ngIf="currentSubStep === 'reader-connecting'">
          <div class="connecting-animation">
            <div class="reader-icon">
              <svg width="160" height="120" viewBox="0 0 160 120" fill="none">
                <!-- 讀卡機圖示 -->
                <rect x="40" y="30" width="80" height="60" rx="8" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="2"/>
                <rect x="50" y="50" width="60" height="5" fill="#333333"/>
                <rect x="50" y="60" width="60" height="5" fill="#333333"/>
                <!-- 連接動畫 -->
                <g opacity="0.8">
                  <circle cx="80" cy="60" r="30" fill="none" stroke="#0044ad" stroke-width="2" opacity="0.3">
                    <animate attributeName="r" values="20;40;20" dur="2s" repeatCount="indefinite"/>
                    <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" repeatCount="indefinite"/>
                  </circle>
                  <circle cx="80" cy="60" r="25" fill="none" stroke="#0044ad" stroke-width="2" opacity="0.5">
                    <animate attributeName="r" values="15;35;15" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                    <animate attributeName="opacity" values="0.8;0.2;0.8" dur="2s" begin="0.5s" repeatCount="indefinite"/>
                  </circle>
                </g>
              </svg>
            </div>
          </div>
          
          <div class="connecting-info">
            <h3 class="connecting-title">讀卡機連接中</h3>
            <p class="connecting-subtitle">Connecting to Card Reader</p>
            
            <div class="connecting-status">
              <div class="status-text">{{ isDetectingReader ? '正在偵測讀卡機服務...' : '請稍候...' }}</div>
              <div class="loading-dots">
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: 軟硬體規格與元件下載 -->
        <div class="download-driver-section" *ngIf="currentSubStep === 'download-driver'">
          <div class="download-icon">
            <svg width="120" height="120" viewBox="0 0 120 120" fill="none">
              <circle cx="60" cy="60" r="50" fill="#fff3cd" stroke="#ffc107" stroke-width="2"/>
              <path d="M60 30 L60 70" stroke="#856404" stroke-width="4" stroke-linecap="round"/>
              <path d="M45 55 L60 70 L75 55" stroke="#856404" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
              <rect x="40" y="80" width="40" height="5" rx="2" fill="#856404"/>
            </svg>
          </div>
          
          <div class="download-info">
            <h3 class="download-title">請下載並安裝必要元件</h3>
            <p class="download-subtitle">Please Download and Install Required Components</p>
          </div>

          <!-- 系統資訊 -->
          <div class="system-info-card">
            <h4>您的系統資訊 System Information</h4>
            <div class="system-details">
              <div class="detail-row">
                <span class="detail-label">作業系統 OS：</span>
                <span class="detail-value">{{ systemInfo.os }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">瀏覽器 Browser：</span>
                <span class="detail-value">{{ systemInfo.browser }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">相容性 Compatibility：</span>
                <span class="detail-value" [class.compatible]="systemInfo.isCompatible">
                  {{ systemInfo.isCompatible ? '支援' : '不支援' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 下載區域 -->
          <div class="download-components">
            <h4>請依序下載並安裝以下元件</h4>
            
            <div class="download-item">
              <div class="item-number">1</div>
              <div class="item-content">
                <h5>讀卡機驅動程式</h5>
                <p>Smart Card Reader Driver</p>
                <button class="btn-download" (click)="downloadDriver()">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 2v8m0 0l3-3m-3 3l-3-3"/>
                    <path d="M3 12h10v2H3z"/>
                  </svg>
                  下載驅動程式
                </button>
              </div>
            </div>
            
            <div class="download-item">
              <div class="item-number">2</div>
              <div class="item-content">
                <h5>瀏覽器安全元件</h5>
                <p>Browser Security Plugin</p>
                <button class="btn-download" (click)="downloadPlugin()">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M8 2v8m0 0l3-3m-3 3l-3-3"/>
                    <path d="M3 12h10v2H3z"/>
                  </svg>
                  下載安全元件
                </button>
              </div>
            </div>
          </div>

          <!-- 說明文字 -->
          <div class="installation-note">
            <p class="note-text">
              下載完成後，請關閉瀏覽器並執行安裝程式。<br>
              安裝完成後，請重新開啟瀏覽器繼續。
            </p>
            <p class="note-text-en">
              After download, please close browser and run the installer.<br>
              Reopen browser to continue after installation.
            </p>
          </div>

          <!-- 繼續按鈕 -->
          <div class="action-section">
            <button class="btn-secondary" (click)="goBack()">
              返回 Back
            </button>
            <button class="btn-primary" (click)="continueAfterDownload()">
              我已完成安裝 Installation Completed
            </button>
          </div>
        </div>

        <!-- Step 3: 選擇讀卡機 -->
        <div class="select-reader-section" *ngIf="currentSubStep === 'select-reader'">
          <div class="reader-list-header">
            <h3 class="reader-title">選擇讀卡機</h3>
            <p class="reader-subtitle">Select Card Reader</p>
          </div>

          <div class="reader-list">
            <button
                 type="button" 
                 class="reader-item" 
                 *ngFor="let reader of availableReaders"
                 [class.selected]="reader.selected"
                 (click)="selectReader(reader.id)"
                 (keydown.enter)="selectReader(reader.id)"
                 (keydown.space)="selectReader(reader.id)"
                 [attr.aria-label]="'選擇讀卡機: ' + reader.name"
                 [attr.aria-pressed]="reader.selected"
                 role="button">
              <div class="reader-radio">
                <div class="radio-outer">
                  <div class="radio-inner" *ngIf="reader.selected"></div>
                </div>
              </div>
              <div class="reader-info">
                <div class="reader-name">{{ reader.name }}</div>
                <div class="reader-status">已連接 Connected</div>
              </div>
              <div class="reader-icon">
                <svg width="40" height="30" viewBox="0 0 40 30" fill="none">
                  <rect x="5" y="5" width="30" height="20" rx="4" fill="#e8e8e8" stroke="#999" stroke-width="1"/>
                  <rect x="10" y="12" width="20" height="2" fill="#666"/>
                  <rect x="10" y="16" width="20" height="2" fill="#666"/>
                </svg>
              </div>
            </button>
          </div>

          <div class="reader-help-text">
            <p>請選擇您要使用的讀卡機，然後點擊下一步。</p>
            <p class="help-text-en">Please select the card reader you want to use, then click Next.</p>
          </div>

          <!-- 操作按鈕 -->
          <div class="action-section">
            <button class="btn-secondary" (click)="goBack()">
              上一步 Previous
            </button>
            <button class="btn-primary" [disabled]="!selectedReader" (click)="selectReader(selectedReader)">
              下一步 Next
            </button>
          </div>
        </div>

        <!-- Step 4: 驗證工商憑證 -->
        <div class="verify-certificate-section" *ngIf="currentSubStep === 'verify-certificate'">
          <!-- 憑證插入提示 -->
          <div class="certificate-prompt" *ngIf="!certificateDetected">
            <div class="prompt-icon">
              <svg width="160" height="120" viewBox="0 0 160 120" fill="none">
                <!-- 讀卡機 -->
                <rect x="30" y="40" width="100" height="50" rx="8" fill="#f8f9fa" stroke="#e0e0e0" stroke-width="2"/>
                <rect x="40" y="55" width="80" height="3" fill="#333333"/>
                <rect x="40" y="62" width="80" height="3" fill="#333333"/>
                <!-- 工商憑證卡片動畫 -->
                <g>
                  <rect x="20" y="20" width="60" height="40" rx="4" fill="#0044ad" opacity="0.9">
                    <animateTransform 
                      attributeName="transform" 
                      type="translate"
                      values="0,0; 30,25; 0,0"
                      dur="3s" 
                      repeatCount="indefinite"/>
                  </rect>
                  <rect x="26" y="26" width="48" height="2" fill="white" opacity="0.8">
                    <animateTransform 
                      attributeName="transform" 
                      type="translate"
                      values="0,0; 30,25; 0,0"
                      dur="3s" 
                      repeatCount="indefinite"/>
                  </rect>
                  <rect x="26" y="30" width="35" height="2" fill="white" opacity="0.8">
                    <animateTransform 
                      attributeName="transform" 
                      type="translate"
                      values="0,0; 30,25; 0,0"
                      dur="3s" 
                      repeatCount="indefinite"/>
                  </rect>
                  <rect x="26" y="38" width="10" height="8" rx="2" fill="#ffd700" stroke="#e6c200" stroke-width="1">
                    <animateTransform 
                      attributeName="transform" 
                      type="translate"
                      values="0,0; 30,25; 0,0"
                      dur="3s" 
                      repeatCount="indefinite"/>
                  </rect>
                </g>
                <!-- 箭頭 -->
                <path d="M100 30 L110 35 L100 40" stroke="#0044ad" stroke-width="2" fill="none" stroke-linecap="round">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
                </path>
              </svg>
            </div>
            <h3 class="prompt-title">請插入工商憑證IC卡</h3>
            <p class="prompt-subtitle">Insert Business Certificate IC Card</p>
            <p class="reader-info">讀卡機：{{ readerStatus.readerName || '未選擇' }}</p>
          </div>

          <!-- 憑證資訊顯示 -->
          <div class="certificate-info-display" *ngIf="certificateDetected && certificateValid">
            <div class="cert-card">
              <div class="cert-card-header">
                <h4>工商憑證資訊 Certificate Information</h4>
              </div>
              <div class="cert-card-body">
                <div class="cert-info-row">
                  <span class="info-label">公司名稱 Company：</span>
                  <span class="info-value">{{ certificateInfo?.companyName || '測試公司' }}</span>
                </div>
                <div class="cert-info-row">
                  <span class="info-label">統一編號 Tax ID：</span>
                  <span class="info-value">{{ certificateInfo?.unifiedNumber || '12345678' }}</span>
                </div>
                <div class="cert-info-row">
                  <span class="info-label">有效期限 Valid Until：</span>
                  <span class="info-value">{{ certificateInfo ? formatDate(certificateInfo.validTo) : '2025/12/31' }}</span>
                </div>
              </div>
            </div>

            <!-- PIN碼輸入 -->
            <div class="pin-input-section">
              <div class="pin-input-wrapper">
                <div class="pin-labels">
                  <div class="pin-label-zh">請輸入工商憑證PIN碼</div>
                  <div class="pin-label-en">Enter Certificate PIN Code</div>
                </div>
                
                <div class="pin-input-container">
                  <div class="pin-input-field">
                    <input 
                      [type]="showPin ? 'text' : 'password'"
                      [(ngModel)]="pinCode"
                      class="pin-input"
                      [class.error]="hasError"
                      placeholder="6-8位數字"
                      maxlength="8"
                      [disabled]="remainingAttempts <= 0 || isVerifyingPin"
                      (keyup.enter)="verifyPin()"
                      (input)="onPinInput()"
                      autocomplete="off"
                    >
                    <button class="pin-toggle" (click)="togglePinVisibility()" type="button">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path *ngIf="!showPin" d="M2 10s3-7 8-7 8 7 8 7-3 7-8 7-8-7-8-7z" stroke="currentColor" stroke-width="2" fill="none"/>
                        <circle *ngIf="!showPin" cx="10" cy="10" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path *ngIf="showPin" d="M2 10s3-7 8-7 8 7 8 7-3 7-8 7-8-7-8-7z" stroke="currentColor" stroke-width="2" fill="none"/>
                        <line *ngIf="showPin" x1="2" y1="2" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                      </svg>
                    </button>
                  </div>
                </div>
                
                <div class="pin-info">
                  <div class="attempts-info" *ngIf="remainingAttempts < 3">
                    <p class="attempts-warning" [class.danger]="remainingAttempts === 0">
                      {{ remainingAttempts === 0 ? '憑證已鎖定，請聯繫客服' : '剩餘嘗試次數：' + remainingAttempts + ' 次' }}
                    </p>
                  </div>
                  
                  <div class="pin-hint" *ngIf="!hasError && remainingAttempts > 0">
                    請輸入6-8位數字密碼
                  </div>
                  
                  <div class="pin-error" *ngIf="hasError">
                    {{ errorMessage || 'PIN碼錯誤，請重新輸入' }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 載入中狀態 -->
          <div class="loading-section" *ngIf="isReadingCertificate">
            <div class="loading-spinner">
              <svg width="40" height="40" viewBox="0 0 40 40">
                <circle cx="20" cy="20" r="15" stroke="#e0e0e0" stroke-width="3" fill="none"/>
                <circle cx="20" cy="20" r="15" stroke="#0044ad" stroke-width="3" fill="none"
                        stroke-dasharray="94" stroke-dashoffset="23.5"
                        transform="rotate(-90 20 20)">
                  <animateTransform attributeName="transform" type="rotate" 
                                    from="0 20 20" to="360 20 20" dur="1s" repeatCount="indefinite"/>
                </circle>
              </svg>
            </div>
            <p class="loading-text">正在讀取工商憑證資訊...</p>
            <p class="loading-text-en">Reading business certificate information...</p>
          </div>

          <!-- 驗證中進度 -->
          <div class="verification-progress" *ngIf="isVerifyingPin">
            <div class="progress-circle">
              <svg width="120" height="120" viewBox="0 0 120 120">
                <circle cx="60" cy="60" r="50" stroke="#e0e0e0" stroke-width="10" fill="none"/>
                <circle cx="60" cy="60" r="50" stroke="#0044ad" stroke-width="10" fill="none"
                        [attr.stroke-dasharray]="314"
                        [attr.stroke-dashoffset]="314 - (314 * verificationProgress / 100)"
                        transform="rotate(-90 60 60)"
                        style="transition: stroke-dashoffset 0.3s ease"/>
              </svg>
              <div class="progress-text">{{ verificationProgress }}%</div>
            </div>
            <div class="verification-message">{{ verificationMessage }}</div>
          </div>

          <!-- 錯誤訊息 -->
          <div class="error-message-display" *ngIf="errorMessage && currentSubStep === 'verify-certificate'">
            <div class="alert alert-error">
              <div class="alert-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="9" stroke="#ff4444" stroke-width="2" fill="none"/>
                  <path d="M10 6v4M10 14h.01" stroke="#ff4444" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </div>
              <div class="alert-content">{{ errorMessage }}</div>
            </div>
          </div>

          <!-- 操作按鈕 -->
          <div class="action-section">
            <button class="btn-secondary" (click)="goBack()">
              上一步 Previous
            </button>
            <button class="btn-secondary" (click)="reinstallComponents()" *ngIf="!certificateDetected && !isReadingCertificate">
              重新安裝元件
            </button>
            <button class="btn-primary" 
                    *ngIf="certificateDetected && certificateValid"
                    [disabled]="!canProceed || remainingAttempts === 0 || isVerifyingPin"
                    (click)="verifyPin()">
              {{ isVerifyingPin ? '驗證中...' : '驗證' }} {{ isVerifyingPin ? 'Verifying...' : 'Verify' }}
            </button>
          </div>
        </div>

        <!-- 開發模式工具 -->
        <div class="test-info" *ngIf="isDevelopment">
          <h4>🧪 Corporate Module - 工商憑證驗證</h4>
          <p>當前步驟: {{ currentSubStep }}</p>
          <p>系統: {{ systemInfo.os }} / {{ systemInfo.browser }}</p>
          <p>讀卡機狀態: {{ readerStatus.isConnected ? '已連接' : '未連接' }}</p>
          <p>憑證狀態: {{ certificateData ? '已讀取' : '未讀取' }}</p>
          <p>剩餘PIN嘗試次數: {{ remainingAttempts }}</p>
          <div class="dev-actions">
            <button class="dev-btn" (click)="currentSubStep = 'reader-connecting'">步驟1</button>
            <button class="dev-btn" (click)="currentSubStep = 'download-driver'">步驟2</button>
            <button class="dev-btn" (click)="currentSubStep = 'select-reader'; checkAvailableReaders()">步驟3</button>
            <button class="dev-btn" (click)="currentSubStep = 'verify-certificate'">步驟4</button>
            <button class="dev-btn" (click)="simulateInsertCard()">模擬插卡</button>
            <button class="dev-btn" (click)="simulateRemoveCard()">模擬移卡</button>
            <button class="dev-btn" (click)="pinCode = '123456'">填入PIN</button>
          </div>
        </div>
      </div>
      
    </div>
  </main>
</div>