import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { CorporateService } from '../../services/corporate.service';
import { CertificateService, CardReaderStatus, CertificateData } from '../../services/certificate.service';
import { SharedTestDataService, UnifiedTestData, RemittanceDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 法人工商憑證驗證頁面組件 (Corporate Step 2)
 * 
 * === 頁面功能說明 ===
 * 此頁面處理工商憑證IC卡讀取、PIN碼驗證、企業身份確認等功能
 * 支援多讀卡機環境、系統相容性檢查、驅動程式下載引導
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 讀卡機管理: 偵測、選擇、連接多讀卡機環境
 *    - 憑證處理: 讀取、驗證、PIN碼認證
 *    - 系統相容性: OS/瀏覽器檢測、驅動程式下載引導
 *    - 狀態管理: 複雜的多步驟流程控制和進度追蹤
 *    - 錯誤處理: 完整的重試機制和用戶引導
 *    - 開發輔助: 模擬卡片插入/移除、測試流程
 * 
 * ✅ CertificateService整合:
 *    - detectCardReader() → CardReaderStatus模型
 *    - readCertificate() → CertificateData模型  
 *    - verifyPin() → PIN碼驗證與剩餘次數管理
 *    - validateCertificate() → 憑證有效期驗證
 * 
 * 🔶 後端API狀態分析:
 *    - CertificateService已實作完整，但需對應後端API:
 *      * POST /api/ibr/corporate/certificate/detect - 讀卡機偵測
 *      * POST /api/ibr/corporate/certificate/read - 憑證讀取
 *      * POST /api/ibr/corporate/certificate/verify-pin - PIN驗證
 *      * GET /api/ibr/corporate/certificate/drivers - 驅動程式下載
 * 
 * === 狀態管理 ===
 * ✅ 完整的狀態管理 (讀卡機連接、憑證讀取、PIN驗證)
 * ✅ 進度追蹤與用戶反饋
 * ✅ 錯誤處理與重試機制
 * ✅ 整合Corporate Service憑證資料儲存
 * 
 * === 導航流程 ===
 * 工商憑證驗證頁 (Step 2) → 匯款詳情頁 (Step 3): /ibr/corporate/remittance-detail
 * 
 * 對應UI設計: Corporate-02.jpg
 */
@Component({
  selector: 'app-c-certificate-verification',
  standalone: true,
  imports: [CommonModule, FormsModule, IbrSharedModule],
  templateUrl: './c-certificate-verification.component.html',
  styleUrls: ['./c-certificate-verification.component.scss']
})
export class CCertificateVerificationComponent implements OnInit, OnDestroy {
  
  // 開發模式標記
  isDevelopment = true;
  
  // 銷毀主題
  private destroy$ = new Subject<void>();
  
  // 讀卡機狀態
  readerStatus: CardReaderStatus = {
    isConnected: false,
    hasCard: false,
    readerName: ''
  };
  
  // 憑證資訊
  certificateInfo: CertificateData | null = null;
  certificateData: CertificateData | null = null; // 添加 certificateData 屬性
  
  // 測試資料
  testData: UnifiedTestData | null = null;
  remittanceInfo: RemittanceDisplayInfo | null = null;
  
  // UI 狀態
  certificateDetected = false;
  certificateValid = false;
  pinCode = '';
  showPin = false;
  hasError = false; // 錯誤狀態
  errorMessage = '';
  remainingAttempts = 3;
  isProcessing = false;
  isDetectingReader = false;
  isReadingCertificate = false;
  isVerifyingPin = false;
  
  // 流程步驟控制
  currentSubStep: 'reader-connecting' | 'download-driver' | 'select-reader' | 'verify-certificate' = 'reader-connecting';
  verificationProgress = 0;
  verificationMessage = '';
  
  // 讀卡機列表
  availableReaders: {id: string, name: string, selected: boolean}[] = [];
  selectedReader = '';
  
  // 系統相容性
  systemInfo = {
    os: '',
    browser: '',
    isCompatible: false
  };
  
  // 下載連結
  downloadLinks = {
    windows: {
      driver: 'https://example.com/download/windows/driver-setup.exe',
      plugin: 'https://example.com/download/windows/browser-plugin.exe'
    },
    mac: {
      driver: 'https://example.com/download/mac/driver-setup.pkg',
      plugin: 'https://example.com/download/mac/browser-plugin.pkg'
    }
  };
  
  // 流程控制 - 檢查是否可以繼續
  get canProceed(): boolean {
    // 只有在驗證憑證步驟才需要檢查
    if (this.currentSubStep !== 'verify-certificate') {
      return true;
    }
    
    // 開發模式下只需要PIN碼長度，正式模式需要憑證和PIN碼
    if (this.isDevelopment) {
      return this.pinCode.length >= 6;
    }
    return this.certificateDetected && this.certificateValid && this.pinCode.length >= 6;
  }

  constructor(
    private router: Router,
    private stateService: IbrStateService,
    private corporateService: CorporateService,
    private certificateService: CertificateService,
    private testDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    this.initializePage();
    this.loadTestData();
    this.detectSystemInfo();
    this.startReaderConnection();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 初始化頁面
   */
  private initializePage(): void {
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.IDENTITY_VERIFYING,
      stepTitle: '驗證身份',
      currentStep: 1,
      totalSteps: 3,
      userType: 'corporate'
    });
  }

  /**
   * 偵測系統資訊
   */
  private detectSystemInfo(): void {
    // 偵測作業系統
    const userAgent = navigator.userAgent;
    const userAgentLower = userAgent.toLowerCase();
    if (userAgentLower.includes('win')) {
      this.systemInfo.os = 'Windows';
    } else if (userAgentLower.includes('mac')) {
      this.systemInfo.os = 'macOS';
    } else {
      this.systemInfo.os = 'Other';
    }
    
    // 偵測瀏覽器
    if (userAgent.includes('Chrome')) {
      this.systemInfo.browser = 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      this.systemInfo.browser = 'Firefox';
    } else if (userAgent.includes('Safari')) {
      this.systemInfo.browser = 'Safari';
    } else if (userAgent.includes('Edge')) {
      this.systemInfo.browser = 'Edge';
    } else {
      this.systemInfo.browser = 'Other';
    }
    
    // 檢查相容性
    this.systemInfo.isCompatible = 
      (this.systemInfo.os === 'Windows' || this.systemInfo.os === 'macOS') &&
      (this.systemInfo.browser === 'Chrome' || this.systemInfo.browser === 'Firefox' || 
       this.systemInfo.browser === 'Safari' || this.systemInfo.browser === 'Edge');
  }
  
  /**
   * 開始讀卡機連接流程
   */
  startReaderConnection(): void {
    this.currentSubStep = 'reader-connecting';
    this.connectToReaderService();
  }
  
  /**
   * 連接讀卡機服務
   */
  private connectToReaderService(): void {
    this.isDetectingReader = true;
    this.isProcessing = true;
    
    // 模擬連接過程
    setTimeout(() => {
      this.certificateService.detectCardReader()
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (status) => {
            this.readerStatus = status;
            if (status.isConnected) {
              // 連接成功，檢查是否有多個讀卡機
              this.checkAvailableReaders();
            } else {
              // 連接失敗，進入下載頁面
              this.currentSubStep = 'download-driver';
            }
            this.isDetectingReader = false;
            this.isProcessing = false;
          },
          error: () => {
            // 連接失敗，進入下載頁面
            this.currentSubStep = 'download-driver';
            this.errorMessage = '無法連接讀卡機服務，請下載並安裝必要元件';
            this.isDetectingReader = false;
            this.isProcessing = false;
          }
        });
    }, 2000);
  }
  
  /**
   * 檢查可用的讀卡機
   */
  checkAvailableReaders(): void {
    // 模擬取得讀卡機列表
    if (this.isDevelopment) {
      this.availableReaders = [
        { id: 'reader1', name: 'EZ100PU 智慧卡讀卡機', selected: false },
        { id: 'reader2', name: 'EZUSB ATM智慧晶片讀卡機', selected: false },
        { id: 'reader3', name: 'Generic Smart Card Reader Interface', selected: false }
      ];
    }
    
    if (this.availableReaders.length === 1) {
      // 只有一個讀卡機，自動選擇
      this.selectReader(this.availableReaders[0].id);
    } else if (this.availableReaders.length > 1) {
      // 多個讀卡機，進入選擇頁面
      this.currentSubStep = 'select-reader';
    } else {
      // 沒有讀卡機
      this.errorMessage = '未偵測到讀卡機';
      this.currentSubStep = 'download-driver';
    }
  }
  
  /**
   * 選擇讀卡機
   */
  selectReader(readerId: string): void {
    // 更新選擇狀態
    this.availableReaders.forEach(reader => {
      reader.selected = reader.id === readerId;
    });
    this.selectedReader = readerId;
    
    // 設定選擇的讀卡機
    const selectedReaderInfo = this.availableReaders.find(r => r.id === readerId);
    if (selectedReaderInfo) {
      this.readerStatus.readerName = selectedReaderInfo.name;
      // 進入驗證憑證步驟
      this.currentSubStep = 'verify-certificate';
      // 檢查是否有插入卡片
      this.checkCardInsertion();
    }
  }
  
  /**
   * 檢查卡片插入
   */
  private checkCardInsertion(): void {
    this.isReadingCertificate = true;
    
    // 模擬檢查卡片
    setTimeout(() => {
      if (this.readerStatus.hasCard || this.isDevelopment) {
        this.detectCertificate();
      } else {
        this.isReadingCertificate = false;
        this.errorMessage = '請插入工商憑證IC卡';
      }
    }, 1000);
  }

  /**
   * 偵測憑證
   */
  public detectCertificate(): void {
    this.isReadingCertificate = true;
    this.errorMessage = '';
    
    this.certificateService.readCertificate()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (certificate) => {
          if (certificate) {
            this.certificateInfo = certificate;
            this.certificateData = certificate;
            this.certificateDetected = true;
            this.certificateValid = this.certificateService.validateCertificate(certificate);
            this.isReadingCertificate = false;
            
            if (!this.certificateValid) {
              this.errorMessage = '憑證無效或已過期';
            }
          } else {
            this.certificateDetected = false;
            this.isReadingCertificate = false;
            this.errorMessage = '無法讀取憑證資訊';
          }
        },
        error: (error) => {
          this.certificateDetected = false;
          this.errorMessage = error.message || '憑證偵測失敗';
          this.isReadingCertificate = false;
        }
      });
  }

  /**
   * 驗證 PIN 碼
   */
  verifyPin(): void {
    if (!this.canProceed) {
      return;
    }

    this.isVerifyingPin = true;
    this.isProcessing = true;
    this.errorMessage = '';
    this.verificationProgress = 0;
    this.verificationMessage = '正在驗證PIN碼...';

    // 模擬驗證進度
    const progressInterval = setInterval(() => {
      if (this.verificationProgress < 90) {
        this.verificationProgress += 10;
        this.updateVerificationMessage();
      }
    }, 300);

    this.certificateService.verifyPin(this.pinCode)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (result) => {
          clearInterval(progressInterval);
          this.verificationProgress = 100;
          
          if (result.success) {
            this.verificationMessage = '驗證成功！';
            
            // 儲存憑證資訊到服務
            if (this.certificateInfo) {
              this.corporateService.setCertificateInfo({
                serialNumber: this.certificateInfo.serialNumber,
                issuer: this.certificateInfo.issuer,
                subject: this.certificateInfo.subject,
                validFrom: this.formatDate(this.certificateInfo.validFrom),
                validTo: this.formatDate(this.certificateInfo.validTo),
                unifiedNumber: this.certificateInfo.unifiedNumber,
                companyName: this.certificateInfo.companyName
              });
            }
            
            // 延遲後導航到下一步
            setTimeout(() => {
              // 更新申請狀態
              this.stateService.updateApplicationStatus({
                status: ApplicationStatus.FORM_FILLING,
                stepTitle: '匯款詳情',
                currentStep: 2,
                totalSteps: 3
              });
              
              // 導航到下一步
              this.router.navigate(['/ibr/corporate/remittance-detail']).then(() => {
                console.log('導航成功至匯款詳情頁');
              });
            }, 1500);
          } else {
            // 從錯誤訊息中解析剩餘次數
            const match = result.message.match(/剩餘 (\d+) 次/);
            if (match) {
              this.remainingAttempts = parseInt(match[1], 10);
            } else {
              this.remainingAttempts = Math.max(0, this.remainingAttempts - 1);
            }
            this.errorMessage = result.message || 'PIN碼驗證失敗';
            this.hasError = true;
            this.pinCode = '';
          }
          this.isVerifyingPin = false;
          this.isProcessing = false;
        },
        error: (error) => {
          clearInterval(progressInterval);
          
          // 處理錯誤物件格式
          const errorObj = error?.error || error;
          const errorMessage = errorObj?.message || errorObj || 'PIN碼驗證失敗';
          
          // 從錯誤訊息中解析剩餘次數
          const match = errorMessage.match(/剩餘 (\d+) 次/);
          if (match) {
            this.remainingAttempts = parseInt(match[1], 10);
          } else if (errorMessage.includes('已鎖定')) {
            this.remainingAttempts = 0;
          }
          this.errorMessage = errorMessage;
          this.hasError = true;
          this.pinCode = '';
          this.isVerifyingPin = false;
          this.isProcessing = false;
        }
      });
  }
  
  /**
   * 更新驗證訊息
   */
  private updateVerificationMessage(): void {
    if (this.verificationProgress < 30) {
      this.verificationMessage = '正在驗證PIN碼...';
    } else if (this.verificationProgress < 60) {
      this.verificationMessage = '正在連接銀行系統...';
    } else if (this.verificationProgress < 90) {
      this.verificationMessage = '正在確認企業身份...';
    } else {
      this.verificationMessage = '即將完成驗證...';
    }
  }


  /**
   * 載入測試資料
   */
  private loadTestData(): void {
    // 檢查是否有既存的測試資料
    this.testData = this.testDataService.getCurrentTestData();
    
    if (!this.testData || !this.testDataService.isCorporateData(this.testData)) {
      // 如果沒有資料或不是法人資料，載入預設法人測試資料
      this.testData = this.testDataService.getDefaultCorporateTestData();
      this.testDataService.setTestData(this.testData);
      console.log('載入預設法人測試資料:', this.testData);
    } else {
      console.log('使用既有法人測試資料:', this.testData);
    }
    
    // 轉換為顯示資訊
    if (this.testData) {
      this.remittanceInfo = this.testDataService.toRemittanceDisplayInfo(this.testData);
      console.log('匯款顯示資訊:', this.remittanceInfo);
    }
  }

  /**
   * 開啟客服服務
   */
  openCustomerService(): void {
    console.log('開啟客服視窗');
    if (this.isDevelopment) {
      alert('企業客服專線\n\n憑證問題協助：\n0800-088-988\n\n服務時間：\n週一至週五 09:00-17:00');
    }
  }

  /**
   * 格式化日期
   */
  formatDate(date: Date | string): string {
    if (!date) return '';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('zh-TW');
  }

  /**
   * 切換PIN碼顯示/隱藏
   */
  togglePinVisibility(): void {
    this.showPin = !this.showPin;
  }

  /**
   * 模擬插入卡片（開發模式）
   */
  simulateInsertCard(): void {
    if (this.isDevelopment) {
      this.certificateService.insertCard();
      this.readerStatus.hasCard = true;
      setTimeout(() => {
        this.detectCertificate();
      }, 500);
    }
  }
  
  /**
   * 重新安裝元件
   */
  reinstallComponents(): void {
    this.currentSubStep = 'download-driver';
  }
  
  /**
   * 下載驅動程式
   */
  downloadDriver(): void {
    const os = this.systemInfo.os.toLowerCase();
    if (os === 'windows') {
      window.open(this.downloadLinks.windows.driver, '_blank');
    } else if (os === 'macos') {
      window.open(this.downloadLinks.mac.driver, '_blank');
    }
  }
  
  /**
   * 下載瀏覽器外掛
   */
  downloadPlugin(): void {
    const os = this.systemInfo.os.toLowerCase();
    if (os === 'windows') {
      window.open(this.downloadLinks.windows.plugin, '_blank');
    } else if (os === 'macos') {
      window.open(this.downloadLinks.mac.plugin, '_blank');
    }
  }
  
  /**
   * 完成下載後繼續
   */
  continueAfterDownload(): void {
    // 重新開始連接流程
    this.startReaderConnection();
  }

  /**
   * 模擬移除卡片（開發模式）
   */
  simulateRemoveCard(): void {
    if (this.isDevelopment) {
      this.certificateService.removeCard();
      this.certificateData = null;
      this.certificateInfo = null;
      this.certificateDetected = false;
      this.certificateValid = false;
      this.pinCode = '';
      this.errorMessage = '';
      this.readerStatus.hasCard = false;
    }
  }
  
  /**
   * 重試讀卡機偵測
   */
  retryReaderDetection(): void {
    this.errorMessage = '';
    this.startReaderConnection();
  }

  /**
   * PIN碼輸入處理
   */
  onPinInput(): void {
    this.hasError = false;
    this.errorMessage = '';
  }

  /**
   * 導航到上一步
   */
  goBack(): void {
    // 根據當前子步驟決定返回行為
    switch (this.currentSubStep) {
      case 'download-driver':
        this.currentSubStep = 'reader-connecting';
        this.startReaderConnection();
        break;
      case 'select-reader':
        this.currentSubStep = 'reader-connecting';
        this.startReaderConnection();
        break;
      case 'verify-certificate':
        if (this.availableReaders.length > 1) {
          this.currentSubStep = 'select-reader';
        } else {
          this.currentSubStep = 'reader-connecting';
          this.startReaderConnection();
        }
        break;
      default:
        this.router.navigate(['/ibr/corporate/company-info']).then(() => {
          console.log('返回公司資料頁');
        });
    }
  }
}
