# Corporate Components - 法人解款元件

![Components](https://img.shields.io/badge/Components-6-blue) ![Flow](https://img.shields.io/badge/Flow-Enterprise-green) ![Certificate](https://img.shields.io/badge/Certificate-PC/SC-red) ![Batch](https://img.shields.io/badge/Batch-Processing-orange)

## 🎯 元件概述

Corporate Components 包含法人解款模組的所有UI元件，實現企業級的跨境匯款解付流程。支援工商憑證驗證、批次處理、大額交易等企業特有功能，提供專業的B2B解款服務體驗。

## 📊 元件統計

- **總元件數**: 6個核心元件
- **流程步驟**: 企業級6步驟流程
- **驗證方式**: 工商憑證 + PIN碼驗證
- **特殊功能**: 批次處理、大額限額

## 🗂️ 元件結構

```
components/
├── c-landing/                  # Step 1: 企業條款同意
├── c-certificate-verification/ # Step 2: 工商憑證驗證
├── c-company-info/             # Step 3: 法人基本資料
├── c-remittance-detail/        # Step 4: 匯款確認與批次選擇
├── c-amount-confirmation/      # Step 5: 金額確認
└── c-application-complete/     # Step 6: 申請完成
```

## 📱 完整流程圖

```mermaid
graph TD
    A[企業用戶入口] --> B[c-landing<br/>企業條款同意]
    B --> C[c-certificate-verification<br/>工商憑證驗證]
    C --> D{憑證驗證}
    D -->|成功| E[c-company-info<br/>法人基本資料]
    D -->|失敗| F[重新讀取憑證]
    F --> C
    E --> G[c-remittance-detail<br/>匯款確認]
    G --> H{批次選擇}
    H -->|單筆| I[c-amount-confirmation<br/>金額確認]
    H -->|多筆| J[批次處理]
    J --> I
    I --> K[c-application-complete<br/>申請完成]
    
    C --> L[PC/SC讀卡機]
    L --> M[PIN碼輸入]
    M --> D
```

## 🧩 核心元件詳解

### 🏢 Enterprise Landing & Verification

#### [c-landing](c-landing/) - 企業條款同意元件
**功能**: 企業解款服務條款展示與同意確認

**主要功能**:
- 企業級服務條款展示
- 法人用戶同意確認
- 企業交易限額說明
- 統一編號預填功能

**企業特色**:
- 企業級交易限額: 500萬台幣
- 批次處理說明
- 企業專用客服通道
- 法人代表身份確認

#### [c-certificate-verification](c-certificate-verification/) - 工商憑證驗證元件
**功能**: 工商憑證讀取與驗證頁面

**主要功能**:
- PC/SC讀卡機設備檢測
- 工商憑證讀取
- PIN碼安全驗證
- 憑證有效性檢查

**技術特色**:
```typescript
// PC/SC API整合
async detectReaders(): Promise<string[]> {
  return this.smartCardService.detectReaders();
}

// 憑證讀取
async readCertificate(): Promise<CertificateInfo> {
  return this.certificateService.readCertificate();
}

// PIN碼驗證
async verifyPIN(pin: string): Promise<boolean> {
  return this.certificateService.verifyPIN(pin);
}
```

**安全機制**:
- PIN碼錯誤次數限制 (3次)
- 憑證到期檢查
- 憑證鏈驗證
- 數位簽章驗證

### 🏛️ Company Information & Data

#### [c-company-info](c-company-info/) - 法人基本資料元件
**功能**: 法人基本資料確認頁面

**主要功能**:
- 憑證資料自動帶入
- 企業基本資料確認
- 代表人資訊驗證
- 營業地址確認

**資料驗證**:
```typescript
interface CompanyInfo {
  companyName: string;              // 公司名稱
  unifiedNumber: string;            // 統一編號
  representativeName: string;       // 代表人姓名
  businessAddress: string;          // 營業地址
  capital: number;                  // 資本額
  industry: string;                 // 產業別
}
```

**政府資料庫整合**:
- 商工登記資料查詢
- 統一編號驗證
- 代表人資格確認
- 營業狀態檢查

#### [c-remittance-detail](c-remittance-detail/) - 匯款確認與批次選擇元件
**功能**: 匯款確認與批次選擇頁面

**主要功能**:
- 企業匯款清單顯示
- 批次匯款選擇
- 匯款性質確認
- 受益帳戶驗證

**批次處理功能**:
```typescript
interface BatchProcessing {
  batchId: string;                  // 批次編號
  totalCount: number;               // 總筆數
  totalAmount: number;              // 總金額
  selectedRemittances: CorporateRemittance[]; // 選取的匯款
  processingFee: number;            // 手續費
  netAmount: number;                // 實收金額
}
```

**企業匯款特色**:
- 支援同時處理多筆匯款
- 智能批次分組
- 大額匯款特殊處理
- 企業專用匯款性質

### 💰 Amount & Completion

#### [c-amount-confirmation](c-amount-confirmation/) - 金額確認元件
**功能**: 批次金額計算與確認頁面

**主要功能**:
- 批次金額統計
- 企業級手續費計算
- 交易限額檢查
- 最終確認項目

**企業費用結構**:
```typescript
interface EnterpriseFeeStructure {
  remittanceFee: number;            // 匯款手續費: 50 NTD
  releaseFee: number;               // 解款手續費: 300 NTD
  additionalFee: number;            // 企業額外費用: 100 NTD
  batchDiscountRate: number;        // 批次折扣率
  totalFees: number;                // 總費用
}
```

**批次優惠計算**:
- 5筆以上: 95折
- 10筆以上: 9折
- 20筆以上: 85折
- VIP企業: 額外優惠

#### [c-application-complete](c-application-complete/) - 申請完成元件
**功能**: 企業申請完成頁面

**主要功能**:
- 批次申請成功確認
- 企業級追蹤號碼
- 預計入帳時間
- 企業收據列印

**企業服務特色**:
- 批次交易追蹤
- 企業級收據格式
- 專屬客戶經理聯絡
- 大額匯款特殊通知

## 🔐 企業級安全機制

### 工商憑證安全
```typescript
export class CertificateSecurityService {
  // 憑證鏈驗證
  async validateCertificateChain(cert: CertificateInfo): Promise<boolean> {
    // 驗證憑證鏈完整性
    // 檢查根憑證有效性
    // 確認憑證未被撤銷
  }
  
  // PIN碼安全
  async secureVerifyPIN(pin: string): Promise<VerificationResult> {
    // PIN碼加密傳輸
    // 嘗試次數限制
    // 錯誤鎖定機制
  }
  
  // 數位簽章
  async signTransaction(data: TransactionData): Promise<string> {
    // 使用工商憑證私鑰簽章
    // 確保交易不可否認性
  }
}
```

### 企業權限控制
```typescript
interface CorporatePermissions {
  maxSingleAmount: number;          // 單筆最大金額
  maxDailyAmount: number;           // 每日最大金額
  maxMonthlyAmount: number;         // 每月最大金額
  allowedCurrencies: string[];      // 允許的幣別
  requireSecondApproval: boolean;   // 是否需要二次審核
  specialPermissions: string[];     // 特殊權限
}
```

### 稽核記錄
```typescript
interface AuditLog {
  transactionId: string;            // 交易編號
  companyId: string;                // 企業ID
  representativeId: string;         // 代表人ID
  action: string;                   // 操作行為
  timestamp: Date;                  // 操作時間
  ipAddress: string;                // IP位址
  deviceInfo: string;               // 設備資訊
  certificateSerial: string;        // 憑證序號
  result: 'SUCCESS' | 'FAILED';     // 操作結果
}
```

## 📱 企業UI設計

### 企業品牌色調
```scss
// 企業級色彩系統
$corporate-primary: #1a365d;        // 深藍色 - 信任感
$corporate-secondary: #2c5282;      // 中藍色 - 專業感
$corporate-accent: #805ad5;         // 紫色 - 尊貴感
$corporate-success: #38a169;        // 綠色 - 成功感
$corporate-warning: #d69e2e;        // 金色 - 警示感

// 企業級字型
$corporate-font-family: 'Montserrat', 'Noto Sans TC', sans-serif;
$corporate-font-weight-normal: 400;
$corporate-font-weight-medium: 500;
$corporate-font-weight-bold: 600;
```

### 企業級元件樣式
```scss
.corporate-card {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(26, 54, 93, 0.1);
  
  &.premium {
    border: 2px solid #805ad5;
    box-shadow: 0 8px 30px rgba(128, 90, 213, 0.15);
  }
}

.corporate-button {
  background: linear-gradient(135deg, #1a365d, #2c5282);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: linear-gradient(135deg, #2c5282, #3182ce);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(26, 54, 93, 0.3);
  }
}
```

## 🧪 企業級測試

### 工商憑證測試
```typescript
describe('Certificate Verification', () => {
  it('should detect smart card readers', async () => {
    const readers = await component.detectReaders();
    expect(readers.length).toBeGreaterThan(0);
  });
  
  it('should read certificate successfully', async () => {
    const certificate = await component.readCertificate();
    expect(certificate.unifiedNumber).toMatch(/^\d{8}$/);
  });
  
  it('should verify PIN correctly', async () => {
    const result = await component.verifyPIN('123456');
    expect(result).toBeTruthy();
  });
});
```

### 批次處理測試
```typescript
describe('Batch Processing', () => {
  it('should calculate batch total correctly', () => {
    const remittances = [
      { amount: 100000 },
      { amount: 200000 },
      { amount: 300000 }
    ];
    const total = component.calculateBatchTotal(remittances);
    expect(total).toBe(600000);
  });
  
  it('should apply batch discount', () => {
    const batchSize = 10;
    const discount = component.calculateBatchDiscount(batchSize);
    expect(discount).toBe(0.9); // 10筆以上9折
  });
});
```

## 🔧 企業級配置

### 企業參數配置
```typescript
export const CORPORATE_CONFIG = {
  TRANSACTION_LIMITS: {
    SINGLE_MAX: 5000000,           // 單筆限額: 500萬
    DAILY_MAX: 10000000,           // 每日限額: 1000萬
    MONTHLY_MAX: 100000000         // 每月限額: 1億
  },
  
  BATCH_PROCESSING: {
    MIN_BATCH_SIZE: 2,             // 最小批次數量
    MAX_BATCH_SIZE: 50,            // 最大批次數量
    DISCOUNT_THRESHOLDS: {
      5: 0.95,                     // 5筆以上95折
      10: 0.9,                     // 10筆以上9折
      20: 0.85                     // 20筆以上85折
    }
  },
  
  CERTIFICATE: {
    MAX_PIN_ATTEMPTS: 3,           // PIN碼最大嘗試次數
    CERTIFICATE_VALIDITY_DAYS: 365, // 憑證有效期檢查
    REQUIRED_KEY_LENGTH: 2048      // 最小金鑰長度
  }
};
```

### 企業服務整合
```typescript
// 企業專用API端點
export const CORPORATE_API_ENDPOINTS = {
  CERTIFICATE_VALIDATION: '/api/corporate/certificate/validate',
  COMPANY_INFO: '/api/corporate/company-info',
  BATCH_REMITTANCE: '/api/corporate/remittance/batch',
  ENTERPRISE_FEES: '/api/corporate/fees/calculate',
  AUDIT_LOG: '/api/corporate/audit/log'
};
```

## 📚 開發指南

### 新增企業元件
```bash
# 建立企業元件
ng generate component components/c-new-feature

# 遵循企業命名慣例
# c-{功能名稱} 格式
# 例: c-approval-workflow
```

### 企業元件開發規範
```typescript
@Component({
  selector: 'app-c-component-name',
  templateUrl: './c-component-name.component.html',
  styleUrls: ['./c-component-name.component.scss']
})
export class CComponentNameComponent extends FormBaseComponent implements OnInit {
  // 企業級表單
  corporateForm: FormGroup;
  
  // 憑證資訊
  certificateInfo: CertificateInfo;
  
  // 批次處理
  batchData: BatchProcessing;
  
  constructor(
    private corporateService: CorporateService,
    private certificateService: CertificateService
  ) {
    super();
  }
  
  ngOnInit(): void {
    this.initializeCorporateForm();
    this.loadCertificateInfo();
  }
}
```

## 🔗 相關連結

### 企業流程文檔
- [Corporate Module README](../README.md) - 法人模組總覽
- [Corporate API Documentation](../services/README.md) - 企業API文檔

### 安全相關
- [Certificate Service](../services/certificate.service.ts) - 憑證服務
- [Smart Card Integration](../../@core/shared-2/services/smart-card.service.ts) - 讀卡機整合

### 設計系統
- [IBR Design System](../../@core/shared-2/README.md) - IBR設計系統
- [Corporate UI Guidelines](../../@core/shared/README.md) - 企業UI指南

---

**🎯 元件完成度**: 6/6 完成 | **🏢 企業級**: 完全支援 | **🔐 安全等級**: AAA+ | **📊 批次處理**: 完整支援

*Corporate Components 提供完整的企業級解款用戶界面，從工商憑證驗證到批次申請完成，每個環節都針對企業用戶的專業需求精心設計。*