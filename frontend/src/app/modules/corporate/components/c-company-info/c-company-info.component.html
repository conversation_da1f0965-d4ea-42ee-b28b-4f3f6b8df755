<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container" [ngClass]="getResponsiveClass()">
      <div class="content-section">
        <!-- 頁面標題 -->
        <div class="title-section">
          <h1 class="main-title">線上外匯解款</h1>
          <p class="subtitle">Online Foreign Exchange Remittance</p>
        </div>

        <!-- 步驟指示器 -->
        <div class="step-indicator">
          <div class="step-info">
            <div class="step-current">第 1 步 驗證身份</div>
            <div class="step-total">共 3 步</div>
          </div>
          <div class="step-subtitle">
            <div class="step-current-en">Step 1 Verify Identity</div>
            <div class="step-total-en">3 Steps</div>
          </div>
          <div class="step-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 33.33%;"></div>
            </div>
          </div>
        </div>

        <!-- 匯款資訊區域 -->
        <div class="remittance-info-section">
          <div class="remittance-header">
            <div class="remittance-title">
              <span class="title-text">你有一筆來自</span>
              <span class="sender-name">{{ remittanceInfo?.senderName || '匯款人' }}</span>
              <span class="title-text">的匯款</span>
            </div>
            <div class="remittance-title-en">
              <span class="title-text-en">You have received a remittance from </span>
              <span class="sender-name-en">{{ remittanceInfo?.senderName || 'Sender' }}</span>
              <span class="title-text-en">.</span>
            </div>
          </div>

          <div class="time-reminder">
            <div class="time-icon">
              <img src="../../../../../assets/image/icon/basic-time.svg" alt="時間圖示" />
            </div>
            <div class="time-text">
              <div class="reminder-text">
                請於 {{ remittanceInfo?.daysRemaining || 30 }} 天內完成解匯，若未完成需至臨櫃辦理
              </div>
              <div class="reminder-text-en">
                Complete the remittance within {{ remittanceInfo?.daysRemaining || 30 }} days, or visit a branch to process it
              </div>
            </div>
          </div>

          <div class="remittance-details">
            <div class="detail-item">
              <div class="detail-label">匯款國別</div>
              <div class="detail-label-en">Remittance Country</div>
              <div class="detail-value">{{ remittanceInfo?.country || 'US' }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">通知日期</div>
              <div class="detail-label-en">Notification Date</div>
              <div class="detail-value">{{ remittanceInfo?.notificationDate || '-' }}</div>
            </div>
          </div>

          <!-- 展開的詳細資訊 -->
          <div class="expanded-details" *ngIf="isDetailsExpanded">
            <div class="detail-row">
              <div class="detail-labels">
                <div class="detail-label">匯款性質</div>
                <div class="detail-label-en">Remittance Type</div>
              </div>
              <div class="detail-value">{{ remittanceInfo?.remittanceType || '待確認' }}</div>
            </div>
            <div class="detail-row">
              <div class="detail-labels">
                <div class="detail-label">附言</div>
                <div class="detail-label-en">Message</div>
              </div>
              <div class="detail-value">{{ remittanceInfo?.message || '-' }}</div>
            </div>
          </div>

          <button 
            type="button"
            class="expand-toggle" 
            (click)="toggleDetails()"
            (keydown.enter)="toggleDetails()"
            (keydown.space)="toggleDetails()"
            [attr.aria-label]="isDetailsExpanded ? '收闔詳情' : '展開詳情'"
            [attr.aria-expanded]="isDetailsExpanded"
          >
            <img src="../../../../../assets/image/icon/center.svg" alt="" aria-hidden="true" />
            <div class="toggle-text">
              <span class="toggle-zh">{{ isDetailsExpanded ? '收闔' : '展開詳情' }}</span>
              <span class="toggle-en">{{ isDetailsExpanded ? 'Less' : 'More' }}</span>
            </div>
            <img 
              src="../../../../../assets/image/icon/pull.svg"
              alt="" 
              aria-hidden="true"
              [class.rotated]="isDetailsExpanded"
            />
          </button>
        </div>

        <!-- 表單區域 -->
        <div class="form-section">
          <div class="form-header">
            <h2 class="form-title">填寫你的收款資料</h2>
            <p class="form-subtitle">Fill In Payment Details</p>
            <p class="form-description">
              請輸入您當時提供予匯款人之帳號，以供凱基銀行確認身份 Please fill in the account number you provided to the remitter for KGI Bank to verify your identity.
            </p>
          </div>

          <form [formGroup]="companyForm" class="identity-form">
            <!-- 企業中文名稱 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">企業中文名稱/銀行帳戶名稱</div>
                <div class="label-en">Full Name / Bank Account Name</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="companyForm.get('companyName')?.invalid && companyForm.get('companyName')?.touched"
                  placeholder="輸入中文名稱" 
                  formControlName="companyName"
                />
                <div class="error-message" *ngIf="companyForm.get('companyName')?.invalid && companyForm.get('companyName')?.touched">
                  {{ getErrorMessage('companyName') }}
                </div>
              </div>
            </div>

            <!-- 企業英文名稱與確認 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">企業英文名稱</div>
                <div class="label-en">English Name</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="companyForm.get('companyEnglishName')?.invalid && companyForm.get('companyEnglishName')?.touched"
                  placeholder="OOXX Ltd." 
                  formControlName="companyEnglishName"
                />
                <div class="error-message" *ngIf="companyForm.get('companyEnglishName')?.invalid && companyForm.get('companyEnglishName')?.touched">
                  {{ getErrorMessage('companyEnglishName') }}
                </div>
              </div>
              
              <div class="checkbox-field">
                <div class="checkbox-wrapper">
                  <input 
                    type="checkbox" 
                    id="confirmEnglishName"
                    class="checkbox-input"
                    formControlName="confirmEnglishName"
                  />
                  <label for="confirmEnglishName" class="checkbox-label"></label>
                </div>
                <div class="checkbox-content">
                  <div class="checkbox-text">確認企業英文名稱無誤</div>
                  <div class="checkbox-text-en">Confirm the recipient's English name is correct.</div>
                  <div class="error-message" *ngIf="companyForm.get('confirmEnglishName')?.invalid && companyForm.get('confirmEnglishName')?.touched">
                    請確認公司英文名稱
                  </div>
                </div>
              </div>
            </div>

            <!-- 企業統一編號 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">企業統一編號</div>
                <div class="label-en">Business ID number</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="companyForm.get('unifiedNumber')?.invalid && companyForm.get('unifiedNumber')?.touched"
                  placeholder="輸入統一編號" 
                  formControlName="unifiedNumber"
                />
                <div class="error-message" *ngIf="companyForm.get('unifiedNumber')?.invalid && companyForm.get('unifiedNumber')?.touched">
                  {{ getErrorMessage('unifiedNumber') }}
                </div>
              </div>
            </div>

            <!-- 工商憑證狀態 (來自上一步驟) -->
            <div class="form-field" *ngIf="certificateData">
              <div class="field-label">
                <div class="label-zh">工商憑證</div>
                <div class="label-en">Business Certificate</div>
              </div>
              <div class="certificate-status">
                <div class="cert-icon">✅</div>
                <div class="cert-info">
                  <div class="cert-name">{{ certificateData.companyName }}</div>
                  <div class="cert-number">統編：{{ certificateData.unifiedNumber }}</div>
                </div>
              </div>
            </div>

            <!-- 銀行代碼 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">銀行代碼及分行代碼 (共7碼)</div>
                <div class="label-en">Bank Code</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  value="中國信託商業銀行" 
                  formControlName="bankName"
                  readonly
                />
                <div class="bank-code-input" [class.error]="companyForm.get('branchCode')?.invalid && companyForm.get('branchCode')?.touched">
                  <div class="bank-code-prefix">
                    <span class="code-number">822</span>
                    <div class="divider"></div>
                  </div>
                  <input 
                    type="text" 
                    class="branch-code-input"
                    placeholder="輸入分行代碼" 
                    formControlName="branchCode"
                    maxlength="4"
                  />
                </div>
                <div class="error-message" *ngIf="companyForm.get('branchCode')?.invalid && companyForm.get('branchCode')?.touched">
                  {{ getErrorMessage('branchCode') }}
                </div>
              </div>
            </div>

            <!-- 銀行帳號 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">銀行帳號</div>
                <div class="label-en">Bank Account No.</div>
              </div>
              <div class="field-input">
                <div class="account-input-wrapper" [class.error]="companyForm.get('accountNumber')?.invalid && companyForm.get('accountNumber')?.touched">
                  <input 
                    type="text" 
                    class="account-input"
                    placeholder="輸入銀行帳號" 
                    formControlName="accountNumber"
                  />
                </div>
                <div class="error-message" *ngIf="companyForm.get('accountNumber')?.invalid && companyForm.get('accountNumber')?.touched">
                  {{ getErrorMessage('accountNumber') }}
                </div>
              </div>
            </div>
          </form>

          <!-- 操作按鈕 -->
          <div class="action-section">
            <button 
              type="button" 
              class="btn-primary btn-next"
              [disabled]="!isFormValid"
              (click)="proceedToNext()"
            >
              下一步 Next
            </button>
          </div>
        </div>

        <!-- 開發模式工具 -->
        <div class="test-info" *ngIf="isDevelopment">
          <h4>🧪 Corporate Module - 第1頁 (公司資料)</h4>
          <p>頁面: 法人驗證資料輸入</p>
          <p>表單狀態：{{ companyForm.valid ? '有效' : '無效' }}</p>
          <div class="dev-actions">
            <button class="dev-btn" (click)="fillTestData()">填入測試資料</button>
            <button class="dev-btn" (click)="companyForm.reset()">清除表單</button>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
