import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { CorporateService } from '../../services/corporate.service';
import { CompanyInfo, CertificateInfo } from '../../models/corporate.model';
import { SharedTestDataService, UnifiedTestData, RemittanceDisplayInfo, CorporateDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 法人公司資料輸入頁面組件 (Corporate Step 2)
 * 
 * === 頁面功能說明 ===
 * 此頁面處理企業基本資料輸入，包含統一編號、公司名稱、銀行帳戶、聯絡資訊等
 * 支援從工商憑證自動填入部分資料，並提供完整的表單驗證機制
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 表單驗證: 統一編號檢查碼算法、格式驗證、必填驗證
 *    - 憑證整合: certificateData自動填入、CorporateService狀態管理
 *    - 資料模型: CompanyInfo完整模型定義
 *    - 響應式表單: ReactiveFormsModule、即時驗證、錯誤處理
 *    - 開發輔助: fillTestData()測試資料功能
 * 
 * 🔶 後端API狀態分析:
 *    - 需要新增企業資料驗證API:
 *      * POST /api/ibr/corporate/company/verify - 公司資料驗證
 *      * GET /api/ibr/corporate/company/lookup - 統編查詢公司資料
 *      * POST /api/ibr/corporate/bank/validate - 企業銀行帳戶驗證
 *    - 整合現有CorporateRemittanceController架構
 * 
 * ✅ 資料模型對應:
 *    - CompanyInfo前端模型與後端企業實體設計一致
 *    - unifiedNumber ↔ 統一編號驗證邏輯完整
 *    - 表單資料結構適合後端處理和存儲
 * 
 * === 業務流程 ===
 * 1. 如有憑證資料則自動填入統編和公司名稱
 * 2. 用戶填寫/確認公司基本資料
 * 3. 填寫銀行帳戶和聯絡資訊
 * 4. 即時表單驗證和狀態管理
 * 5. 完成後導航至工商憑證驗證頁
 * 
 * === 狀態管理 ===
 * ✅ CorporateService整合: companyInfo$, certificateInfo$狀態流
 * ✅ IbrStateService狀態更新: IDENTITY_VERIFYING → FORM_FILLING
 * ✅ 導航流程: → /ibr/corporate/certificate-verification
 * 
 * @description 企業資料收集和驗證的核心組件
 * <AUTHOR> Code
 * @date 2025/06/03
 */
@Component({
  selector: 'app-c-company-info',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, IbrSharedModule],
  templateUrl: './c-company-info.component.html',
  styleUrls: ['./c-company-info.component.scss']
})
export class CCompanyInfoComponent implements OnInit, OnDestroy {
  
  // 開發模式標記
  isDevelopment = true;
  
  // 銷毀主題
  private destroy$ = new Subject<void>();
  
  // 表單
  companyForm!: FormGroup;
  
  // 從憑證讀取的資料
  certificateData: CertificateInfo | null = null;
  
  // 載入狀態
  isLoading = false;
  
  // 展開狀態
  isDetailsExpanded = false;
  
  // 測試資料
  testData: UnifiedTestData | null = null;
  corporateDisplayInfo: CorporateDisplayInfo | null = null;
  
  // 匯款資訊（使用動態資料）
  remittanceInfo: RemittanceDisplayInfo | null = null;

  constructor(
    private router: Router,
    private fb: FormBuilder,
    private stateService: IbrStateService,
    private corporateService: CorporateService,
    private testDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    this.initializePage();
    this.initForm();
    this.loadTestData();
    this.loadCertificateData();
    this.subscribeToFormChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 初始化頁面
   */
  private initializePage(): void {
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.IDENTITY_VERIFYING,
      stepTitle: '驗證身份',
      currentStep: 1,
      totalSteps: 3,
      userType: 'corporate'
    });
  }

  /**
   * 初始化表單
   */
  private initForm(): void {
    this.companyForm = this.fb.group({
      // 公司基本資料
      unifiedNumber: ['', [Validators.required, this.validateUnifiedNumber]],
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyEnglishName: ['', [Validators.required, Validators.pattern(/^[A-Za-z0-9\s\-.,&'()]+$/)]],
      confirmEnglishName: [false, Validators.requiredTrue],
      
      // 銀行資訊
      bankName: [{ value: '凱基商業銀行', disabled: true }],
      branchCode: ['', [Validators.required, Validators.pattern(/^\d{4}$/)]],
      accountNumber: ['', [Validators.required, Validators.pattern(/^\d{10,16}$/)]],
      
      // 聯絡資訊
      contactPhone: ['', [Validators.required, Validators.pattern(/^0\d{1,2}-?\d{6,8}$/)]],
      contactEmail: ['', [Validators.required, Validators.email]]
    });
  }

  /**
   * 載入測試資料
   */
  private loadTestData(): void {
    // 檢查是否有既存的測試資料
    this.testData = this.testDataService.getCurrentTestData();
    
    if (!this.testData || !this.testDataService.isCorporateData(this.testData)) {
      // 如果沒有資料或不是法人資料，載入預設法人測試資料
      this.testData = this.testDataService.getDefaultCorporateTestData();
      this.testDataService.setTestData(this.testData);
      console.log('載入預設法人測試資料:', this.testData);
    } else {
      console.log('使用既有法人測試資料:', this.testData);
    }
    
    // 轉換為顯示資訊
    if (this.testData) {
      this.remittanceInfo = this.testDataService.toRemittanceDisplayInfo(this.testData);
      this.corporateDisplayInfo = this.testDataService.toCorporateDisplayInfo(this.testData);
      console.log('法人顯示資訊:', this.corporateDisplayInfo);
      
      // 如果表單已初始化，填入測試資料
      if (this.companyForm) {
        this.fillFormWithTestData();
      }
    }
  }
  
  /**
   * 使用測試資料填入表單
   */
  private fillFormWithTestData(): void {
    if (this.corporateDisplayInfo) {
      this.companyForm.patchValue({
        unifiedNumber: this.corporateDisplayInfo.unifiedNumber,
        companyName: this.corporateDisplayInfo.companyName,
        companyEnglishName: this.corporateDisplayInfo.companyEnglishName,
        confirmEnglishName: true,
        branchCode: this.corporateDisplayInfo.branchCode,
        accountNumber: this.corporateDisplayInfo.accountNumber,
        contactPhone: this.corporateDisplayInfo.contactPhone,
        contactEmail: this.corporateDisplayInfo.contactEmail
      });
      console.log('已使用測試資料填入表單');
    }
  }
  
  /**
   * 載入憑證資料
   */
  private loadCertificateData(): void {
    // 訂閱憑證資訊
    this.corporateService.certificateInfo$
      .pipe(takeUntil(this.destroy$))
      .subscribe(certInfo => {
        if (certInfo) {
          this.certificateData = certInfo;
          // 如果有憑證資料，自動填入統編和公司名稱
          this.companyForm.patchValue({
            unifiedNumber: certInfo.unifiedNumber,
            companyName: certInfo.companyName
          });
        }
      });
    
    // 訂閱公司資料（如果有暫存的）
    this.corporateService.companyInfo$
      .pipe(takeUntil(this.destroy$))
      .subscribe(companyInfo => {
        if (companyInfo && Object.keys(companyInfo).length > 0) {
          this.companyForm.patchValue(companyInfo);
        }
      });
  }

  /**
   * 訂閱表單變更
   */
  private subscribeToFormChanges(): void {
    this.companyForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // 即時儲存表單資料
        const formData = {
          ...this.companyForm.getRawValue(),
          lastUpdated: new Date().toISOString()
        };
        this.corporateService.updateCompanyInfo(formData);
      });
  }

  /**
   * 統一編號驗證
   */
  private validateUnifiedNumber(control: AbstractControl): ValidationErrors | null {
    const value = control?.value;
    if (!value) return null;
    
    // 基本格式檢查
    if (!/^\d{8}$/.test(value)) {
      return { invalidFormat: true };
    }
    
    // 統一編號檢查碼驗證邏輯
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    const digits = value.split('').map(Number);
    let sum = 0;
    
    for (let i = 0; i < 8; i++) {
      const product = digits[i] * weights[i];
      sum += Math.floor(product / 10) + (product % 10);
    }
    
    if (sum % 10 !== 0) {
      // 特殊情況：第七碼為7時的替代驗證
      if (digits[6] === 7) {
        sum = sum - 10;
        if (sum % 10 !== 0) {
          return { invalidChecksum: true };
        }
      } else {
        return { invalidChecksum: true };
      }
    }
    
    return null;
  }

  /**
   * 取得錯誤訊息
   */
  getErrorMessage(fieldName: string): string {
    const control = this.companyForm.get(fieldName);
    if (!control || !control.errors) return '';
    
    const errorMessages: Record<string, Record<string, string>> = {
      unifiedNumber: {
        required: '請輸入統一編號',
        invalidFormat: '統一編號格式錯誤，請輸入8位數字',
        invalidChecksum: '統一編號驗證錯誤，請確認輸入正確'
      },
      companyName: {
        required: '請輸入公司名稱',
        minlength: '公司名稱至少需要2個字'
      },
      companyEnglishName: {
        required: '請輸入公司英文名稱',
        pattern: '英文名稱只能包含英文字母、數字及特定符號'
      },
      confirmEnglishName: {
        requiredTrue: '請確認公司英文名稱'
      },
      branchCode: {
        required: '請輸入分行代碼',
        pattern: '分行代碼必須為4位數字'
      },
      accountNumber: {
        required: '請輸入銀行帳號',
        pattern: '銀行帳號格式錯誤'
      },
      contactPhone: {
        required: '請輸入聯絡電話',
        pattern: '電話號碼格式錯誤'
      },
      contactEmail: {
        required: '請輸入電子郵件',
        email: '電子郵件格式錯誤'
      }
    };
    
    const errors = control.errors;
    const fieldErrors = errorMessages[fieldName];
    
    if (fieldErrors) {
      for (const error in errors) {
        if (fieldErrors[error]) {
          return fieldErrors[error];
        }
      }
    }
    
    return '此欄位輸入有誤';
  }

  /**
   * 取得響應式樣式類別
   */
  getResponsiveClass(): string {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }

  /**
   * 切換詳情展開狀態
   */
  toggleDetails(): void {
    this.isDetailsExpanded = !this.isDetailsExpanded;
  }

  /**
   * 檢查表單是否有效
   */
  get isFormValid(): boolean {
    return this.companyForm.valid;
  }

  /**
   * 進行到下一步
   */
  proceedToNext(): void {
    if (!this.isFormValid) {
      // 標記所有欄位為已觸碰，以顯示驗證錯誤
      Object.keys(this.companyForm.controls).forEach(key => {
        this.companyForm.get(key)?.markAsTouched();
      });
      
      // 滾動到第一個錯誤
      setTimeout(() => {
        const firstError = document.querySelector('.text-input.error');
        if (firstError) {
          firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      return;
    }

    // 儲存完整的公司資料
    const completeCompanyInfo: CompanyInfo = {
      ...this.companyForm.getRawValue(),
      verifiedAt: new Date().toISOString()
    };
    
    this.corporateService.setCompanyInfo(completeCompanyInfo);
    
    // 更新狀態
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.FORM_FILLING,
      stepTitle: '匯款詳情',
      currentStep: 2,
      totalSteps: 3
    });
    
    // 導航到下一頁 - 前往工商憑證驗證
    this.router.navigate(['/ibr/corporate/certificate-verification']).then(() => {
      console.log('導航成功至工商憑證驗證頁');
    });
  }

  /**
   * 填入測試資料（開發用）
   */
  fillTestData(): void {
    if (this.isDevelopment) {
      // 優先使用統一測試資料
      if (this.corporateDisplayInfo) {
        this.fillFormWithTestData();
      } else {
        // 備援：使用預設測試資料
        this.companyForm.patchValue({
          unifiedNumber: '********',
          companyName: '緯創資通股份有限公司',
          companyEnglishName: 'Wistron Corporation',
          confirmEnglishName: true,
          branchCode: '0015',
          accountNumber: '*************',
          contactPhone: '02-2720-1234',
          contactEmail: '<EMAIL>'
        });
      }
    }
  }

  /**
   * 開啟客服服務
   */
  openCustomerService(): void {
    console.log('開啟客服視窗');
    if (this.isDevelopment) {
      alert('企業客服專線\n\n公司資料問題協助：\n0800-088-988\n\n服務時間：\n週一至週五 09:00-17:00');
    }
  }
}
