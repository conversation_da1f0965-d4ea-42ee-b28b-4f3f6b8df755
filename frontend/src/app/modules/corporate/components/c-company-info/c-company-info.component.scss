/* === 基礎重置 === */
a,
button,
input,
select,
h1,
h2,
h3,
h4,
h5,
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  text-decoration: none;
  background: none;
  -webkit-font-smoothing: antialiased;
}

menu, ol, ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 步驟指示器 === */
.step-indicator {
  margin-bottom: 40px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-current {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
}

.step-total {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.step-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-current-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-total-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0044ad;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* === 匯款資訊區域 === */
.remittance-info-section {
  background: #ffffff;
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  margin-bottom: 32px;
}

.remittance-header {
  margin-bottom: 20px;
}

.remittance-title {
  margin-bottom: 8px;
}

.title-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.sender-name {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.remittance-title-en {
  margin-bottom: 0;
}

.title-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.sender-name-en {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.time-reminder {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #fff3cd;
  border-radius: 8px;
}

.time-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}

.time-text {
  flex: 1;
}

.reminder-text {
  color: #856404;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  margin-bottom: 4px;
}

.reminder-text-en {
  color: #856404;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
}

.remittance-details {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 16px;
}

.detail-item {
  flex: 1;
}

.detail-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 2px;
}

.detail-label-en {
  color: #999999;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 8px;
}

.detail-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

/* === 展開的詳細資訊 === */
.expanded-details {
  padding: 20px 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-labels {
  flex: 0 0 140px;
}

/* === 展開切換按鈕 === */
.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.expand-toggle:hover {
  background: #f8f9fa;
  border-radius: 8px;
}

.toggle-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.toggle-zh {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.toggle-en {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.rotated {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

/* === 表單區域 === */
.form-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 0;
}

.form-header {
  margin-bottom: 32px;
}

.form-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.form-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 16px 0;
}

.form-description {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
}

/* === 表單欄位 === */
.identity-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-label {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

.field-input {
  position: relative;
}

.text-input {
  width: 100%;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #041c43;
  background: #ffffff;
  transition: all 0.2s ease;
}

.text-input::placeholder {
  color: #999999;
}

.text-input:focus {
  outline: none;
  border-color: #0044ad;
  box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
}

.text-input:disabled {
  background: #f8f9fa;
  color: #999999;
  cursor: not-allowed;
}

.text-input.error {
  border-color: #dc3545;
}

.text-input.error:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* === 錯誤訊息 === */
.error-message {
  color: #dc3545;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
  margin-top: 4px;
}

/* === 勾選框欄位 === */
.checkbox-field {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 8px;
}

.checkbox-wrapper {
  position: relative;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 100%;
  height: 100%;
}

.checkbox-label {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border: 2px solid #ddd;
  border-radius: 4px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.checkbox-input:checked ~ .checkbox-label {
  background: #0044ad;
  border-color: #0044ad;
}

.checkbox-input:checked ~ .checkbox-label::after {
  content: '';
  position: absolute;
  display: block;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.checkbox-content {
  flex: 1;
}

.checkbox-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  margin-bottom: 2px;
}

.checkbox-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 150%;
}

/* === 工商憑證狀態顯示 === */
.certificate-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #e8f4f8;
  border-radius: 8px;
}

.cert-icon {
  font-size: 24px;
}

.cert-info {
  flex: 1;
}

.cert-name {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.cert-number {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

/* === 銀行代碼輸入 === */
.bank-code-input {
  display: flex;
  align-items: center;
  margin-top: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.bank-code-input.error {
  border-color: #dc3545;
}

.bank-code-prefix {
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: #f8f9fa;
  height: 52px;
}

.code-number {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.divider {
  width: 1px;
  height: 30px;
  background: #e8e8e8;
  margin-left: 16px;
}

.branch-code-input {
  flex: 1;
  padding: 16px;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #041c43;
  background: #ffffff;
  outline: none;
}

.branch-code-input::placeholder {
  color: #999999;
}

/* === 帳號輸入 === */
.account-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.account-input-wrapper.error {
  border-color: #dc3545;
}

.account-input {
  flex: 1;
  padding: 16px;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #041c43;
  background: #ffffff;
  outline: none;
}

.account-input::placeholder {
  color: #999999;
}

/* === 欄位備註 === */
.field-note {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 150%;
  margin-top: 8px;
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.btn-primary {
  min-width: 200px;
  padding: 16px 32px;
  background: #0044ad;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.btn-primary:hover {
  background: #003390;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.3);
}

.btn-primary:disabled {
  background: #e2e2e2;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-primary:disabled .button-text-zh,
.btn-primary:disabled .button-text-en {
  color: #a6a6a6;
}

.button-text-zh {
  color: #ffffff;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 100%;
}

.button-text-en {
  color: #ffffff;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 100%;
  opacity: 0.9;
}

/* === 測試資訊 === */
.test-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
  font-family: "Noto Sans TC", sans-serif;
}

.test-info h4 {
  color: #856404;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.test-info p {
  color: #856404;
  font-size: 12px;
  font-weight: 400;
  margin: 4px 0;
}

.dev-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.dev-btn {
  padding: 6px 12px;
  background: #ffc107;
  border-radius: 4px;
  color: #856404;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dev-btn:hover {
  background: #e0a800;
  color: #533f03;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }

  .content-section {
    padding: 48px;
  }

  .main-title {
    font-size: 26px;
  }

  .subtitle {
    font-size: 15px;
  }

  .step-current {
    font-size: 20px;
  }

  .remittance-info-section {
    padding: 32px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }

  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }

  .content-section {
    padding: 60px;
  }

  .btn-primary {
    min-width: 240px;
    padding: 18px 40px;
  }

  .button-text-zh {
    font-size: 18px;
  }

  .button-text-en {
    font-size: 15px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }

  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }

  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }

  .content-section {
    padding: 24px 20px;
  }

  .main-title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 12px;
  }

  .step-current {
    font-size: 16px;
  }

  .step-total {
    font-size: 14px;
  }

  .remittance-info-section {
    padding: 16px;
  }

  .form-header {
    margin-bottom: 24px;
  }

  .form-title {
    font-size: 18px;
  }

  .identity-form {
    gap: 20px;
  }

  .text-input,
  .branch-code-input,
  .account-input {
    font-size: 14px;
    padding: 14px;
  }

  .btn-primary {
    width: 100%;
    min-width: unset;
  }
}
