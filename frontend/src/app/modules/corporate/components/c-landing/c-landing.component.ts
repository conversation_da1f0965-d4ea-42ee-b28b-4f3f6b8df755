import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';
import { PersonalDataDialogComponent } from '../../../../@core/shared-2/components/personal-data-dialog/personal-data-dialog.component';
import { SharedTestDataService, UnifiedTestData, RemittanceDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 法人條款同意頁面組件 (Corporate Step 1)
 * 
 * === 頁面功能說明 ===
 * 此頁面處理法人數位解款服務條款同意，包含企業資料保護聲明和數位解款條款
 * 企業用戶需要同意相關條款後才能進入工商憑證驗證流程
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 條款同意狀態: corporateDataAgreed, digitalTermsAgreed
 *    - 企業資料保護聲明對話框整合 PersonalDataDialogComponent
 *    - 雙重確認機制(企業條款+數位解款條款)
 *    - 客服功能整合和友善提示
 * 
 * 🔶 後端API狀態分析:
 *    - 後端有管理功能API (`CorporateRemittanceController`)
 *    - 需要新增企業用戶專用條款API:
 *      * GET /api/ibr/corporate/terms - 取得企業服務條款
 *      * POST /api/ibr/corporate/terms/agree - 記錄企業同意狀態
 *      * GET /api/ibr/corporate/terms/status - 查詢同意狀態
 *    - 與現有管理API架構一致，實作相對容易
 * 
 * ✅ 狀態管理完整:
 *    - 雙重條款同意機制確保合規性
 *    - IbrStateService整合和導航流程清晰
 *    - canProceed() getter提供完整驗證邏輯
 * 
 * === 業務流程 ===
 * 1. 企業用戶閱讀企業資料保護聲明
 * 2. 企業用戶閱讀數位解款服務條款
 * 3. 雙重確認後記錄同意狀態
 * 4. 導航至工商憑證驗證頁面
 * 
 * === 狀態管理 ===
 * ✅ 前端狀態控制完整
 * 🔶 待整合後端狀態服務
 * ✅ 導航流程: → /ibr/corporate/certificate-verification
 * 
 * === 導航流程 ===
 * 法人條款同意頁 (Step 1) → 工商憑證驗證頁 (Step 2): /ibr/corporate/certificate-verification
 * 
 * 對應UI設計: Corporate-01.jpg
 */

@Component({
  selector: 'app-c-landing',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IbrSharedModule
  ],
  templateUrl: './c-landing.component.html',
  styleUrls: ['./c-landing.component.scss']
})
export class CLandingComponent implements OnInit {
  
  // 同意狀態
  corporateDataAgreed = false;
  digitalTermsAgreed = false;
  
  // 測試資料
  testData: UnifiedTestData | null = null;
  remittanceInfo: RemittanceDisplayInfo | null = null;
  
  constructor(
    private router: Router,
    private dialogService: SlideDialogService,
    private testDataService: SharedTestDataService
  ) { }

  ngOnInit(): void {
    this.loadTestData();
  }
  
  /**
   * 載入測試資料
   */
  private loadTestData(): void {
    // 檢查是否有既存的測試資料
    this.testData = this.testDataService.getCurrentTestData();
    
    if (!this.testData || !this.testDataService.isCorporateData(this.testData)) {
      // 如果沒有資料或不是法人資料，載入預設法人測試資料
      this.testData = this.testDataService.getDefaultCorporateTestData();
      this.testDataService.setTestData(this.testData);
      console.log('載入預設法人測試資料:', this.testData);
    } else {
      console.log('使用既有法人測試資料:', this.testData);
    }
    
    // 轉換為顯示資訊
    if (this.testData) {
      this.remittanceInfo = this.testDataService.toRemittanceDisplayInfo(this.testData);
      console.log('匯款顯示資訊:', this.remittanceInfo);
    }
  }

  // 檢查是否可以繼續
  get canProceed(): boolean {
    return this.corporateDataAgreed && this.digitalTermsAgreed;
  }

  // 點擊我同意按鈕
  onAgreeClick(): void {
    if (this.canProceed) {
      // 導航到下一頁 - 公司資料驗證
      this.router.navigate(['/ibr/corporate/company-info']);
    }
  }

  // 開啟企業資料保護聲明
  openCorporateDataStatement(): void {
    const dialogRef = this.dialogService.customOpen(PersonalDataDialogComponent, {
      style: 'offcanvas',
      width: '100%',
      height: '100vh',
      hasCloseBtn: false,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        // 使用者已閱讀完整內容並確認
        console.log('使用者已確認企業資料保護聲明');
      }
    });
  }

  // 開啟數位解款條款
  openDigitalTerms(): void {
    alert(`法人數位解款條款

歡迎使用凱基銀行法人數位解款服務，以下為服務條款：

第一條 服務內容
本服務提供企業法人線上外匯匯入款項解款功能

第二條 使用者責任
法人代表應確保所提供資料之真實性與正確性，並有權代表該法人進行解款

第三條 交易限額
法人單筆解款限額為新台幣500萬元

第四條 身份驗證
需使用工商憑證進行身份驗證

第五條 手續費用
依本行公告之法人費率收取相關手續費

第六條 服務時間
本服務提供時間為營業日上午9:00至下午3:30

（此為模擬內容，實際內容請參考銀行正式條款）`);
  }

  // 開啟客服
  openCustomerService(): void {
    alert('企業客服服務\n\n如需協助請撥打企業客服專線：\n0800-255-777\n\n服務時間：\n週一至週五 9:00-18:00');
  }
}
