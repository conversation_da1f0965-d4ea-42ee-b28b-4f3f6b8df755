/* IBR Landing Component Styles */

// 頁面容器
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

// 主要內容包裝器 (無上方間距，確保與 header 無縫連接)
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px; // 消除 header 與 content 之間的縫隙
}

// 內容容器 (獨立的白色卡片)
.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

// Banner圖片區
.app-update-banner {
  width: 100%;
  height: 120px;
  overflow: hidden;
  position: relative;
}

.banner-content {
  width: 100%;
  height: 100%;
  position: relative;
}

.group-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

// 裝飾元素
.vector-decor-1,
.vector-decor-2,
.group-decor {
  position: absolute;
  pointer-events: none;
}

.vector-decor-1 {
  width: 12px;
  height: 12px;
  left: 20%;
  top: 65%;
}

.vector-decor-2 {
  width: 8px;
  height: 8px;
  right: 15%;
  bottom: 10%;
}

.group-decor {
  width: 30px;
  height: 30px;
  right: 20%;
  top: 20%;
}

// 置中內容區塊
.center-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 25px 40px;
  gap: 24px;
}

// 標題區
.title-section {
  text-align: center;
  width: 100%;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

// 勾選框容器
.checkbox-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 勾選框區塊
.check-box {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

// 勾選框包裝器
.checkbox-wrapper {
  position: relative;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  width: 20px;
  height: 20px;
  margin: 0;
}

.checkbox-label {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #0044ad;
  border-radius: 3px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &::after {
    content: '';
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid #ffffff;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }
}

.checkbox-input:checked + .checkbox-label {
  background-color: #0044ad;
  
  &::after {
    display: block;
  }
}

// 勾選框內容
.checkbox-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.checkbox-text {
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  line-height: 150%;
  font-weight: 400;
  color: #041c43;
}

.text-normal {
  color: #041c43;
}

.text-link {
  color: #0044ad;
  cursor: pointer;
  text-decoration: none;
  transition: opacity 0.2s;
  
  &:hover {
    opacity: 0.8;
    text-decoration: underline;
  }
}

.checkbox-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
}

// 按鈕區塊
.button-section {
  width: 100%;
  margin-top: 12px;
}

.agree-button {
  width: 100%;
  height: 48px;
  background: #0044ad;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  
  &:hover:not(.disabled) {
    background: #003390;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 68, 173, 0.2);
  }
  
  &:active:not(.disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 68, 173, 0.2);
  }
  
  &.disabled {
    background: #e2e2e2;
    cursor: not-allowed;
    
    .button-text-zh,
    .button-text-en {
      color: #a6a6a6;
    }
  }
}

.button-text-zh {
  color: #ffffff;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  line-height: 100%;
  font-weight: 500;
}

.button-text-en {
  color: #ffffff;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  line-height: 100%;
  font-weight: 500;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
    padding: 0;
  }
  
  .center-content {
    padding: 32px;
  }
  
  .page-title {
    font-size: 26px;
  }
}

/* 桌機版本 - 2/3 寬度 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
  
  .center-content {
    padding: 40px;
  }
  
  .app-update-banner {
    height: 160px;
  }
  
  .main-title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 16px;
  }
  
  .checkbox-text {
    font-size: 18px;
  }
  
  .checkbox-text-en {
    font-size: 15px;
  }
  
  .button-section {
    margin-top: 20px;
  }
  
  .agree-button {
    height: 52px;
  }
  
  .button-text-zh,
  .button-text-en {
    font-size: 18px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }
  
  .center-content {
    padding: 48px;
  }
  
  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }
  
  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }
  
  .center-content {
    padding: 20px;
  }
  
  .main-title {
    font-size: 22px;
  }
  
  .subtitle {
    font-size: 13px;
  }
  
  .page-subtitle {
    font-size: 15px;
  }
}