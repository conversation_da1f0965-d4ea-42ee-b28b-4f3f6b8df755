import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DIALOG_DATA, DialogRef } from '../../../../@core/shared/service/slide-dialog.service';

export interface RemittanceNature {
  code: string;
  description: string;
  descriptionEn: string;
}

@Component({
  selector: 'app-remittance-nature-dialog',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="remittance-nature-dialog">      
      <div class="dialog-content">
        <div class="nature-list">
          <div 
            class="nature-item" 
            *ngFor="let nature of remittanceNatures"
            (click)="selectNature(nature)"
          >
            <div class="nature-code">{{ nature.code }}</div>
            <div class="nature-descriptions">
              <div class="nature-desc-zh">{{ nature.description }}</div>
              <div class="nature-desc-en">{{ nature.descriptionEn }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .remittance-nature-dialog {
      width: 100%;
      font-family: "Noto Sans TC", sans-serif;
      padding: 0;
    }

    .dialog-content {
      max-height: 400px;
      overflow-y: auto;
    }

    .nature-list {
      display: flex;
      flex-direction: column;
    }

    .nature-item {
      display: flex;
      align-items: center;
      padding: 16px 24px;
      cursor: pointer;
      transition: background-color 0.2s;
      border-bottom: 1px solid #f0f0f0;
    }

    .nature-item:hover {
      background-color: #f8f9fa;
    }

    .nature-item:last-child {
      border-bottom: none;
    }

    .nature-code {
      font-size: 16px;
      font-weight: 600;
      color: #041c43;
      min-width: 60px;
      margin-right: 16px;
    }

    .nature-descriptions {
      flex: 1;
    }

    .nature-desc-zh {
      font-size: 14px;
      color: #333;
      margin-bottom: 4px;
    }

    .nature-desc-en {
      font-size: 12px;
      color: #666;
    }

    /* 滾動條樣式 */
    .dialog-content::-webkit-scrollbar {
      width: 6px;
    }

    .dialog-content::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .dialog-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .dialog-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  `]
})
export class RemittanceNatureDialogComponent implements OnInit {
  
  remittanceNatures: RemittanceNature[] = [
    {
      code: '19D',
      description: '專業技術及事務收入',
      descriptionEn: 'Professional and technical services income'
    },
    {
      code: '250',
      description: '收回國外存款',
      descriptionEn: 'Repatriation of overseas deposits'
    },
    {
      code: '262',
      description: '收回投資國外股權證券',
      descriptionEn: 'Repatriation of overseas equity investment'
    },
    {
      code: '280',
      description: '收回對外融資',
      descriptionEn: 'Repatriation of overseas financing'
    },
    {
      code: '410',
      description: '薪資款匯入',
      descriptionEn: 'Salary remittance'
    },
    {
      code: '510',
      description: '贍家匯款收入',
      descriptionEn: 'Family support remittance'
    }
  ];

  constructor(
    private dialogRef: DialogRef<unknown>,
    @Inject(DIALOG_DATA) public data: unknown
  ) {}

  ngOnInit(): void {
    // 可以從 data 中獲取當前選中的匯款性質
  }

  selectNature(nature: RemittanceNature): void {
    // 回傳選中的匯款性質
    this.dialogRef.close(nature);
  }

  close(): void {
    this.dialogRef.close();
  }
}
