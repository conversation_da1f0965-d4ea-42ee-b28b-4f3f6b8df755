<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container">
      
      <!-- 內容區塊 -->
      <div class="content-section">
        <!-- 標題區 -->
        <div class="title-section">
          <h1 class="main-title">線上外匯解款</h1>
          <p class="subtitle">Online Foreign Exchange Remittance</p>
        </div>

        <!-- 步驟指示器 -->
        <div class="step-indicator">
          <div class="step-info">
            <div class="step-current">第 2 步 確認金額</div>
            <div class="step-total">共 3 步</div>
          </div>
          <div class="step-subtitle">
            <div class="step-current-en">Step 2 Confirm Amount</div>
            <div class="step-total-en">3 Steps</div>
          </div>
          <div class="step-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="66.66"></div>
            </div>
          </div>
        </div>

        <!-- 匯款資訊卡片 -->
        <div class="remittance-card">
          <!-- 匯入金額區塊 -->
          <div class="amount-section">
            <div class="amount-header">
              <h3 class="amount-title">匯入金額</h3>
              <h3 class="amount-title-en">Remittance Amount</h3>
            </div>
            <div class="amount-content">
              <div class="amount-main">
                <div class="currency-display">
                  <img [src]="getCurrencyFlagUrl(remittanceData.currency)" [alt]="remittanceData.currency" class="currency-flag">
                  <span class="currency">{{ remittanceData.currency }}</span>
                </div>
                <span class="amount">{{ remittanceData.amount | number:'1.2-2' }}</span>
              </div>
              <div class="exchange-info">
                <div class="exchange-rate">
                  <span class="rate-label">匯率 Exchange Rate</span>
                  <span class="rate-value">{{ remittanceData.exchangeRate }}</span>
                </div>
                <div class="twd-amount">
                  <span class="twd-label">台幣金額 TWD Amount</span>
                  <span class="twd-value">NTD {{ remittanceData.twdAmount | number:'1.0-0' }}</span>
                </div>
              </div>
            </div>
          </div>


          <!-- 受款人資訊 -->
          <div class="info-section">
            <h4 class="section-title">受款人資料 Beneficiary Information</h4>
            <div class="beneficiary-info-vertical">
              <div class="beneficiary-item">
                <div class="beneficiary-label">受款人 Beneficiary</div>
                <div class="beneficiary-value">{{ remittanceData.beneficiary.name }}</div>
              </div>
              <div class="beneficiary-item">
                <div class="beneficiary-label">銀行帳號 Bank Account</div>
                <div class="beneficiary-value">{{ remittanceData.beneficiary.account }}</div>
              </div>
            </div>
          </div>

          <!-- 匯款性質 -->
          <div class="info-section">
            <h4 class="section-title">匯款性質 Remittance Nature</h4>
            <div class="nature-content clickable" (click)="openRemittanceNatureDialog()">
              <div class="nature-display">
                <div class="nature-code">{{ remittanceData.nature.code }}</div>
                <div class="nature-desc">{{ remittanceData.nature.description }}</div>
                <div class="nature-desc-en">{{ remittanceData.nature.descriptionEn }}</div>
              </div>
              <div class="dropdown-icon">
                <span>▼</span>
              </div>
            </div>
          </div>

          <!-- 聯絡人資訊 -->
          <div class="info-section">
            <div class="contact-inputs">
              <div class="input-wrapper">
                <label class="input-label">聯絡資訊 (Email / 手機號碼)</label>
                <input 
                  type="text" 
                  [(ngModel)]="contactInfo" 
                  placeholder="輸入常用 Email 或手機號碼 Please enter email or mobile number"
                  class="contact-input"
                  (blur)="validateContactInfo()"
                  [class.error]="contactInfoError"
                >
                <div class="input-format-hint" *ngIf="!contactInfoError && contactInfo">
                  <span *ngIf="contactInfoType === 'email'" class="format-valid">✓ Email 格式</span>
                  <span *ngIf="contactInfoType === 'phone'" class="format-valid">✓ 手機號碼格式</span>
                </div>
                <div class="error-message" *ngIf="contactInfoError">
                  {{ contactInfoError }}
                </div>
                <div class="input-hint">
                    將寄發解款通知給您 Remittance notification will be sent to you
                </div>
              </div>
            </div>
          </div>

        </div>

        <!-- 同意條款區塊 -->
        <div class="agreement-section">
          <div class="checkbox-item">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                [(ngModel)]="checkboxStates.agreeFinancialService"
                class="checkbox-input"
              >
              <span class="checkbox-custom"></span>
              <div class="checkbox-text">
                <div class="checkbox-text-zh">同意透過財金公司跨行通匯系統，進行新台幣資金撥轉至本次驗證帳號</div>
                <div class="checkbox-text-en">I agree to use the Financial Information Service Co. Ltd. cross-bank remittance system to transfer New Taiwan Dollar funds to the account verified this time</div>
              </div>
            </label>
          </div>
          
          <div class="checkbox-item">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                [(ngModel)]="checkboxStates.agreeAutoRemittance"
                class="checkbox-input"
              >
              <span class="checkbox-custom"></span>
              <div class="checkbox-text">
                <div class="checkbox-text-zh">
                  若收款公司同時勾選「同意凱基銀行依約定匯款性質進行
                  <span class="clickable-link" (click)="openAutoRemittanceDialog()">《自動解款服務》</span>
                  」，往後來自同一匯款人匯入同一收款帳號時得以自動解付，不再需要手動操作解款程序
                </div>
                <div class="checkbox-text-en">
                  If the beneficiary company also checks "Agree to KGI Bank's 
                  <span class="clickable-link" (click)="openAutoRemittanceDialog()">automatic remittance service</span>
                  according to the agreed remittance nature", future remittances from the same remitter to the same beneficiary account can be automatically processed without manual operation
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="action-section">
          <button 
            class="btn-primary btn-next" 
            [class.disabled]="!canProceed()"
            [disabled]="!canProceed()"
            (click)="proceedToNext()"
          >
            <span class="button-text-zh">下一步</span>
            <span class="button-text-en">Next</span>
          </button>
        </div>
      </div>

      <!-- 測試資訊 (開發時使用) -->
      <div class="test-info" *ngIf="isDevelopment">
        <h4>🏢 Corporate Module - 第2頁 (確認金額)</h4>
        <p>頁面: 匯款詳情確認</p>
        <p>狀態: {{ currentStatus }}</p>
        <p>匯款金額: {{ remittanceData.currency }} {{ remittanceData.amount }}</p>
        <p>台幣金額: NTD {{ remittanceData.twdAmount }}</p>
        <p>受款人: {{ remittanceData.beneficiary.name }}</p>
        <p>聯絡資訊: {{ contactInfo || '未填寫' }} <span *ngIf="contactInfoType">({{ contactInfoType === 'email' ? 'Email' : '手機' }})</span></p>
      </div>
      
    </div>
  </main>
</div>
