import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';

/**
 * 法人自動解款服務條款對話框組件
 * 顯示法人專用的自動解款服務詳細條款內容
 */
@Component({
  selector: 'app-c-auto-remittance-dialog',
  standalone: true,
  imports: [
    CommonModule,
    IbrSharedModule
  ],
  template: `
    <div class="auto-remittance-dialog">
      <!-- 對話框標題 -->
      <div class="dialog-header">
        <h2 class="dialog-title">企業自動解款服務條款</h2>
        <h3 class="dialog-subtitle">Corporate Automatic Remittance Service Terms</h3>
      </div>

      <!-- 對話框內容 -->
      <div class="dialog-content">
        <!-- 服務說明 -->
        <section class="content-section">
          <h3 class="section-title">一、服務說明 Service Description</h3>
          <div class="content-text">
            <p>本行「企業自動解款服務」係指當貴公司同意本服務後，往後來自同一匯款人匯入貴公司同一收款帳號之款項，將由本行系統自動進行解款作業，無需貴公司再次進行手動操作。</p>
            <p class="english-text">The "Corporate Automatic Remittance Service" means that after your company agrees to this service, future remittances from the same remitter to the same corporate beneficiary account will be automatically processed by our bank's system without requiring manual operation.</p>
          </div>
        </section>

        <!-- 適用範圍 -->
        <section class="content-section">
          <h3 class="section-title">二、適用範圍 Scope of Application</h3>
          <div class="content-text">
            <p>1. 本服務僅適用於相同匯款人、相同公司收款帳號、相同匯款性質之企業匯入款項。</p>
            <p>2. 自動解款將依據貴公司首次設定之匯款性質進行處理。</p>
            <p>3. 若匯款資料有異動或不符，系統將通知貴公司授權人員進行確認。</p>
            <p>4. 本服務適用於各類貿易款項、服務費用、投資收益等企業經常性收款。</p>
            <p class="english-text">
              1. This service only applies to corporate remittances from the same remitter to the same company beneficiary account with the same remittance nature.<br>
              2. Automatic processing will be based on the remittance nature your company initially set.<br>
              3. If there are any changes or discrepancies in the remittance data, the system will notify your company's authorized personnel for confirmation.<br>
              4. This service is applicable to various trade payments, service fees, investment income, and other regular corporate receipts.
            </p>
          </div>
        </section>

        <!-- 授權與管理 -->
        <section class="content-section">
          <h3 class="section-title">三、授權與管理 Authorization and Management</h3>
          <div class="content-text">
            <p>1. 貴公司應指定授權人員管理本服務，並提供有效之聯絡方式。</p>
            <p>2. 授權人員之變更應即時通知本行，以確保服務正常運作。</p>
            <p>3. 本行將依據貴公司提供之授權層級進行相關作業。</p>
            <p class="english-text">
              1. Your company shall designate authorized personnel to manage this service and provide valid contact information.<br>
              2. Any changes to authorized personnel should be promptly notified to our bank to ensure normal service operation.<br>
              3. Our bank will process operations according to the authorization levels provided by your company.
            </p>
          </div>
        </section>

        <!-- 服務期限與終止 -->
        <section class="content-section">
          <h3 class="section-title">四、服務期限與終止 Service Period and Termination</h3>
          <div class="content-text">
            <p>1. 本服務自貴公司同意啟用之日起生效。</p>
            <p>2. 貴公司可隨時透過本行企業網路銀行或書面申請終止本服務。</p>
            <p>3. 終止申請應於三個營業日前提出，以利本行進行相關作業。</p>
            <p class="english-text">
              1. This service takes effect from the date your company agrees to activate it.<br>
              2. Your company may terminate this service at any time through our corporate online banking or written application.<br>
              3. Termination applications should be submitted three business days in advance for our bank to process related operations.
            </p>
          </div>
        </section>

        <!-- 注意事項 -->
        <section class="content-section">
          <h3 class="section-title">五、注意事項 Important Notes</h3>
          <div class="content-text">
            <p>1. 自動解款服務將依據主管機關相關法令規定執行，包括但不限於洗錢防制法、資恐防制法等。</p>
            <p>2. 大額匯款或特殊性質匯款，本行保留要求額外文件或人工確認之權利。</p>
            <p>3. 本行將於每次自動解款完成後，發送通知至貴公司指定之聯絡方式及授權人員。</p>
            <p>4. 貴公司應定期檢視自動解款紀錄，如有異常應立即通知本行。</p>
            <p class="english-text">
              1. The automatic remittance service will be executed in accordance with relevant regulations, including but not limited to anti-money laundering and counter-terrorism financing laws.<br>
              2. For large or special nature remittances, our bank reserves the right to request additional documents or manual confirmation.<br>
              3. Our bank will send notifications to your company's designated contact methods and authorized personnel after each automatic remittance is completed.<br>
              4. Your company should regularly review automatic remittance records and immediately notify our bank of any anomalies.
            </p>
          </div>
        </section>

        <!-- 責任與義務 -->
        <section class="content-section">
          <h3 class="section-title">六、責任與義務 Responsibilities and Obligations</h3>
          <div class="content-text">
            <p>1. 貴公司應確保所提供之公司資料、帳戶資訊及授權文件正確無誤。</p>
            <p>2. 如因貴公司提供錯誤資料或未及時更新資料導致之損失，本行不負賠償責任。</p>
            <p>3. 貴公司同意配合本行進行必要之企業身分驗證、資料更新及合規審查。</p>
            <p>4. 本行保留修改或終止本服務之權利，並將提前通知貴公司。</p>
            <p class="english-text">
              1. Your company shall ensure that the provided company information, account details, and authorization documents are accurate.<br>
              2. Our bank shall not be liable for any losses caused by incorrect information provided by your company or failure to update information timely.<br>
              3. Your company agrees to cooperate with our bank for necessary corporate identity verification, data updates, and compliance reviews.<br>
              4. Our bank reserves the right to modify or terminate this service with advance notice to your company.
            </p>
          </div>
        </section>
      </div>

      <!-- 對話框底部 -->
      <div class="dialog-footer">
        <p class="footer-note">
          本條款如有修改，將於本行企業網銀公告，並以電子郵件通知貴公司授權人員。<br>
          <span class="english-text">Any modifications to these terms will be announced on our corporate online banking and notified to your company's authorized personnel via email.</span>
        </p>
      </div>
    </div>
  `,
  styles: [`
    .auto-remittance-dialog {
      padding: 0;
      max-height: 80vh;
      overflow-y: auto;
    }

    .dialog-header {
      background: #f8f9fa;
      padding: 24px;
      border-bottom: 1px solid #e8e8e8;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .dialog-title {
      color: #041c43;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .dialog-subtitle {
      color: #666666;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
      font-weight: 400;
      margin: 0;
    }

    .dialog-content {
      padding: 32px;
    }

    .content-section {
      margin-bottom: 32px;
    }

    .content-section:last-child {
      margin-bottom: 0;
    }

    .section-title {
      color: #0044ad;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e8e8e8;
    }

    .content-text {
      color: #333333;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 15px;
      line-height: 1.8;
    }

    .content-text p {
      margin: 0 0 12px 0;
    }

    .content-text p:last-child {
      margin-bottom: 0;
    }

    .english-text {
      color: #666666;
      font-family: "Montserrat", sans-serif;
      font-size: 14px;
      font-style: italic;
      margin-top: 8px;
    }

    .dialog-footer {
      background: #f8f9fa;
      padding: 20px 32px;
      border-top: 1px solid #e8e8e8;
      margin-top: 32px;
    }

    .footer-note {
      color: #666666;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 13px;
      line-height: 1.6;
      margin: 0;
      text-align: center;
    }

    /* 捲軸樣式 */
    .auto-remittance-dialog::-webkit-scrollbar {
      width: 8px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-thumb {
      background: #cccccc;
      border-radius: 4px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-thumb:hover {
      background: #999999;
    }

    /* 響應式設計 */
    @media (max-width: 767px) {
      .dialog-header {
        padding: 20px;
      }

      .dialog-title {
        font-size: 20px;
      }

      .dialog-subtitle {
        font-size: 14px;
      }

      .dialog-content {
        padding: 20px;
      }

      .section-title {
        font-size: 16px;
      }

      .content-text {
        font-size: 14px;
      }

      .english-text {
        font-size: 13px;
      }

      .dialog-footer {
        padding: 16px 20px;
      }

      .footer-note {
        font-size: 12px;
      }
    }
  `]
})
export class AutoRemittanceDialogComponent {
  constructor() {}
}