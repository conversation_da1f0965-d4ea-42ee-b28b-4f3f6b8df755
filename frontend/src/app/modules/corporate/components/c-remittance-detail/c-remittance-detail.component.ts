import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';
import { RemittanceNatureDialogComponent, RemittanceNature } from './remittance-nature-dialog.component';
import { AutoRemittanceDialogComponent } from './auto-remittance-dialog.component';
import { SharedTestDataService } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 法人匯款詳情頁面組件 (Corporate Step 3)
 * 
 * === 頁面功能說明 ===
 * 此頁面處理企業匯款詳情確認，包含匯款資料顯示、匯款性質選擇、聯絡資訊填寫
 * 提供手續費計算、實收金額計算，以及相關服務條款同意功能
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 匯款資料模型: remittanceData包含完整的匯入匯款資訊
 *    - 金額計算: 手續費計算、實收金額(net amount)計算邏輯
 *    - 匯款性質: RemittanceNatureDialogComponent整合，支援性質代碼選擇
 *    - 表單驗證: 聯絡Email格式驗證、必填項目檢查
 *    - 條款同意: 雙重確認機制(金融服務條款+自動解款同意)
 *    - 狀態管理: IbrStateService整合，完整的導航流程控制
 * 
 * ✅ RemittanceNatureDialogComponent整合:
 *    - 標準化匯款性質代碼(19D, 250, 262, 280, 410, 510)
 *    - 雙語顯示(中文+英文)
 *    - SlideDialogService模態對話框集成
 * 
 * 🔶 後端API狀態分析:
 *    - 需要新增企業匯款詳情API:
 *      * GET /api/ibr/corporate/remittance/{id}/detail - 取得匯款詳情
 *      * POST /api/ibr/corporate/remittance/fees/calculate - 手續費計算
 *      * GET /api/ibr/corporate/remittance/natures - 匯款性質列表
 *      * POST /api/ibr/corporate/remittance/confirm - 確認匯款資料
 *    - 整合現有CorporateRemittanceController架構
 * 
 * === 資料模型對應 ===
 * ✅ 前端remittanceData模型包含:
 *    - currency, amount: 外幣金額和幣別
 *    - exchangeRate, twdAmount: 匯率和台幣金額
 *    - remitter/beneficiary: 匯款人/受款人資訊
 *    - nature: 匯款性質(代碼+中英文說明)
 *    - remarks: 匯款備註
 * 
 * 🔶 後端實體對應需求:
 *    - CorporateRemittanceDetail實體設計
 *    - 手續費計算規則實體
 *    - 匯款性質代碼表
 * 
 * === 業務流程 ===
 * 1. 從CorporateService載入憑證驗證後的匯款資料
 * 2. 顯示匯款詳情並計算手續費
 * 3. 用戶選擇/確認匯款性質
 * 4. 填寫聯絡Email並同意相關條款
 * 5. 導航至下一步金額確認頁面
 * 
 * === 狀態管理 ===
 * ✅ IbrStateService狀態管理: FORM_FILLING → AMOUNT_CONFIRMING
 * ✅ 導航流程: → /ibr/corporate/amount-confirmation
 * 
 * === 導航流程 ===
 * 工商憑證驗證頁 (Step 2) → 匯款詳情頁 (Step 3) → 金額確認頁 (Step 4): /ibr/corporate/amount-confirmation
 * 
 * 對應UI設計: Corporate-03.jpg
 */
@Component({
  selector: 'app-c-remittance-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IbrSharedModule
  ],
  templateUrl: './c-remittance-detail.component.html',
  styleUrls: ['./c-remittance-detail.component.scss']
})
export class CRemittanceDetailComponent implements OnInit {
  currentStatus: ApplicationStatus = ApplicationStatus.AMOUNT_CONFIRMING;
  
  // 開發模式標記
  isDevelopment = true;
  
  // 測試資料相關屬性
  testData: any;
  remittanceInfo: any;
  corporateInfo: any;
  
  // 匯款資料
  remittanceData = {
    currency: 'USD',
    amount: 5000.00,
    exchangeRate: '32.35',
    twdAmount: 161750,
    remitter: {
      name: 'GLOBAL TECH CORPORATION',
      account: '****5678',
      bank: 'HSBC BANK USA'
    },
    beneficiary: {
      name: 'OOXX Ltd.',
      account: '****9012',
      bank: '凱基銀行 (809)'
    },
    nature: {
      code: '19D',
      description: '專業技術及事務收入',
      descriptionEn: 'Professional and technical services income'
    },
    remarks: 'Technical service payment - Q4 2024'
  };
  
  // 聯絡人資訊（共用欄位）
  contactInfo = '';
  contactInfoError = '';
  contactInfoType: 'email' | 'phone' | '' = ''; // 記錄輸入類型
  
  // 手續費計算
  feeAmount = 300;
  
  // 實收金額
  get netAmount(): number {
    return this.remittanceData.twdAmount - this.feeAmount;
  }

  // Checkbox 狀態
  checkboxStates = {
    agreeFinancialService: false,
    agreeAutoRemittance: false
  };

  constructor(
    private router: Router,
    private stateService: IbrStateService,
    private slideDialogService: SlideDialogService,
    private testDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    // 頁面初始化邏輯
    // 在實際環境中，這裡應該從服務獲取匯款資料
    this.loadTestData();
    this.loadRemittanceData();
  }

  /**
   * 載入測試資料
   */
  private loadTestData(): void {
    // 檢查是否有既存的測試資料
    this.testData = this.testDataService.getCurrentTestData();
    
    if (!this.testData || !this.testDataService.isCorporateData(this.testData)) {
      // 如果沒有資料或不是法人資料，載入預設法人測試資料
      this.testData = this.testDataService.getDefaultCorporateTestData();
      this.testDataService.setTestData(this.testData);
      console.log('載入預設法人測試資料:', this.testData);
    } else {
      console.log('使用既有法人測試資料:', this.testData);
    }
    
    // 轉換為顯示資訊
    if (this.testData) {
      this.remittanceInfo = this.testDataService.toRemittanceDisplayInfo(this.testData);
      this.corporateInfo = this.testDataService.toCorporateDisplayInfo(this.testData);
      console.log('法人匯款顯示資訊:', this.remittanceInfo);
      
      // 更新匯款資料
      this.updateRemittanceData();
    }
  }
  
  /**
   * 使用測試資料更新匯款資料
   */
  private updateRemittanceData(): void {
    if (this.testData && this.remittanceInfo && this.corporateInfo) {
      this.remittanceData = {
        ...this.remittanceData,
        currency: this.testData.Currency,
        amount: parseFloat(this.testData.Amount),
        twdAmount: Math.round(parseFloat(this.testData.Amount) * parseFloat(this.remittanceData.exchangeRate)),
        remitter: {
          name: this.testData.PayerName,
          account: '****' + Math.random().toString().slice(-4),
          bank: this.testData.PayerCountry === 'US' ? 'Wells Fargo Bank' : 'International Bank'
        },
        beneficiary: {
          name: this.corporateInfo.companyName,
          account: '****' + this.corporateInfo.accountNumber.slice(-4),
          bank: this.corporateInfo.bankName + ' (809)'
        },
        nature: {
          code: this.testData.SourceOfFund || '001',
          description: this.getRemittanceNatureText(this.testData.SourceOfFund || '001'),
          descriptionEn: this.getRemittanceNatureEnglish(this.testData.SourceOfFund || '001')
        },
        remarks: this.testData.Memo
      };
      console.log('更新後的匯款資料:', this.remittanceData);
    }
  }
  
  /**
   * 取得匯款性質中文名稱
   */
  private getRemittanceNatureText(code: string): string {
    const natureMap: { [key: string]: string } = {
      '001': '貨物貿易',
      '002': '服務貿易',
      '210': '國外直接投資',
      '230': '其他投資',
      '320': '投資收益',
      '510': '其他經常移轉'
    };
    return natureMap[code] || '其他匯款';
  }
  
  /**
   * 取得匯款性質英文名稱
   */
  private getRemittanceNatureEnglish(code: string): string {
    const natureMap: { [key: string]: string } = {
      '001': 'Goods Trade',
      '002': 'Service Trade',
      '210': 'Foreign Direct Investment',
      '230': 'Other Investment',
      '320': 'Investment Income',
      '510': 'Other Current Transfer'
    };
    return natureMap[code] || 'Other Remittance';
  }
  
  /**
   * 載入匯款資料
   */
  private loadRemittanceData(): void {
    // 模擬從 API 載入資料
    console.log('載入匯款資料...');
    
    // 實際環境中應該：
    // this.remittanceService.getRemittanceDetails(remittanceId)
    //   .subscribe(data => {
    //     this.remittanceData = data;
    //     this.calculateFees();
    //   });
  }
  
  /**
   * 客服按鈕點擊處理
   */
  openCustomerService(): void {
    console.log('開啟客服視窗');
    // 實際環境中應開啟客服對話或撥打電話
  }

  /**
   * 開啟匯款性質選擇對話框
   */
  openRemittanceNatureDialog(): void {
    console.log('點擊匯款性質，準備開啟對話框');
    
    try {
      const dialogRef = this.slideDialogService.customOpen(RemittanceNatureDialogComponent, {
        style: 'dialog',
        title: '匯款性質選擇',
        width: '600px',
        hasCloseBtn: true,
        centerTitle: true,
        data: {
          currentNature: this.remittanceData.nature
        }
      });

      console.log('對話框已開啟:', dialogRef);

      dialogRef.afterClosed().subscribe((selectedNature: RemittanceNature) => {
        console.log('對話框關閉，選擇的性質:', selectedNature);
        if (selectedNature) {
          this.remittanceData.nature = selectedNature;
          console.log('更新匯款性質:', selectedNature);
        }
      });
    } catch (error) {
      console.error('開啟對話框時發生錯誤:', error);
    }
  }

  /**
   * 取得貨幣旗幟圖片 URL
   */
  getCurrencyFlagUrl(currency: string): string {
    // 這裡應該根據貨幣代碼返回對應的旗幟圖片 URL
    // 暫時使用占位符
    const flagMap: Record<string, string> = {
      'USD': 'assets/image/flags/us.png',
      'EUR': 'assets/image/flags/eu.png',
      'JPY': 'assets/image/flags/jp.png',
      'CNY': 'assets/image/flags/cn.png',
      'HKD': 'assets/image/flags/hk.png',
      'GBP': 'assets/image/flags/gb.png',
      'AUD': 'assets/image/flags/au.png',
      'SGD': 'assets/image/flags/sg.png'
    };
    
    return flagMap[currency] || 'assets/image/flags/default.png';
  }

  /**
   * 驗證聯絡資訊（Email 或手機號碼）
   */
  validateContactInfo(): void {
    if (!this.contactInfo) {
      this.contactInfoError = '請輸入 Email 或手機號碼';
      this.contactInfoType = '';
      return;
    }
    
    // 移除空白
    const trimmedInfo = this.contactInfo.trim();
    
    // 檢查是否為 Email 格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(trimmedInfo)) {
      this.contactInfoError = '';
      this.contactInfoType = 'email';
      return;
    }
    
    // 檢查是否為手機號碼格式（移除破折號後檢查）
    const phoneNumber = trimmedInfo.replace(/-/g, '');
    const phoneRegex = /^09\d{8}$/;
    if (phoneRegex.test(phoneNumber)) {
      this.contactInfoError = '';
      this.contactInfoType = 'phone';
      return;
    }
    
    // 都不符合
    this.contactInfoError = '請輸入有效的 Email 或手機號碼（09開頭的10位數字）';
    this.contactInfoType = '';
  }

  /**
   * 檢查是否可以繼續下一步
   */
  canProceed(): boolean {
    // 檢查聯絡人資訊是否填寫且格式正確
    if (!this.contactInfo || this.contactInfoError || !this.contactInfoType) {
      return false;
    }
    
    return this.checkboxStates.agreeFinancialService && 
           this.checkboxStates.agreeAutoRemittance;
  }

  /**
   * 確認並繼續到下一步
   */
  proceedToNext(): void {
    // 先驗證聯絡人資訊
    this.validateContactInfo();
    
    if (!this.canProceed()) {
      if (!this.contactInfo || this.contactInfoError || !this.contactInfoType) {
        alert('請先填寫聯絡資訊（Email 或手機號碼）');
      } else {
        alert('請先勾選所有必要的同意項目');
      }
      return;
    }

    console.log('確認匯款資訊，前往下一步');
    console.log('聯絡資訊:', this.contactInfo);
    console.log('資訊類型:', this.contactInfoType);
    console.log('測試資料:', this.testData);
    
    // 儲存聯絡資訊到測試資料
    if (this.testData) {
      if (this.contactInfoType === 'email') {
        this.testData.PayeeMail = this.contactInfo;
      } else if (this.contactInfoType === 'phone') {
        this.testData.PayeeTel = this.contactInfo;
      }
      this.testDataService.setTestData(this.testData);
    }
    
    // 更新狀態
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.AMOUNT_CONFIRMING,
      stepTitle: '金額確認',
      currentStep: 4
    });
    
    // 導航到金額確認頁面 (Step 4)
    this.router.navigate(['/ibr/corporate/amount-confirmation']);
  }

  /**
   * 開啟自動解款服務條款對話框
   */
  openAutoRemittanceDialog(): void {
    console.log('開啟企業自動解款服務條款對話框');
    
    try {
      const dialogRef = this.slideDialogService.customOpen(AutoRemittanceDialogComponent, {
        style: 'dialog',
        title: '企業自動解款服務條款',
        width: '700px',
        hasCloseBtn: true,
        centerTitle: true
      });

      console.log('企業自動解款服務條款對話框已開啟');

      dialogRef.afterClosed().subscribe(() => {
        console.log('企業自動解款服務條款對話框已關閉');
      });
    } catch (error) {
      console.error('開啟企業自動解款服務條款對話框時發生錯誤:', error);
    }
  }
}
