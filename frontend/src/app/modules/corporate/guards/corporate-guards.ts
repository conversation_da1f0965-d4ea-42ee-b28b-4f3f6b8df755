import { Injectable } from '@angular/core';
import { 
  CanActivate, 
  CanActivateChild,
  CanDeactivate,
  CanLoad,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Route,
  UrlSegment,
  UrlTree
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

// 導入相關服務
import { CertificateApiService } from '../services/certificate-api.service';
import { UnifiedNumberApiService } from '../services/unified-number-api.service';
import { ErrorHandlerService } from '../services/error-handler.service';

/**
 * 可離開頁面的組件介面
 */
export interface CanComponentDeactivate {
  canDeactivate(): Observable<boolean> | Promise<boolean> | boolean;
}

/**
 * 路由守衛回應類型
 */
type GuardResponse = Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree;

/**
 * 法人憑證驗證守衛
 * 
 * 用於保護需要有效憑證才能存取的路由
 * 驗證使用者是否擁有有效的工商憑證
 * 
 * @example
 * ```typescript
 * // 在路由設定中使用
 * {
 *   path: 'certificate-required',
 *   component: SomeComponent,
 *   canActivate: [CertificateVerificationGuard]
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class CertificateVerificationGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private certificateApi: CertificateApiService,
    private errorHandler: ErrorHandlerService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟用路由
   * 
   * @param route 啟用的路由快照
   * @param state 路由器狀態快照
   * @returns 是否允許啟用
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    return this.checkCertificateValidity(route, state);
  }

  /**
   * 檢查是否可以啟用子路由
   * 
   * @param childRoute 子路由快照
   * @param state 路由器狀態快照
   * @returns 是否允許啟用
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    return this.checkCertificateValidity(childRoute, state);
  }

  /**
   * 檢查憑證有效性
   * 
   * @param route 路由快照
   * @param state 路由器狀態快照
   * @returns 憑證驗證結果
   * @private
   */
  private checkCertificateValidity(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    // 從localStorage或sessionStorage取得憑證資訊
    const certificateData = this.getCertificateFromStorage();
    
    if (!certificateData) {
      console.warn('未找到憑證資訊，重導向到憑證驗證頁面');
      return this.router.createUrlTree(['/corporate/certificate-verification'], {
        queryParams: { returnUrl: state.url }
      });
    }

    // 驗證憑證
    return this.certificateApi.verifyCertificate({
      certificateData: certificateData.data,
      verificationType: 'FULL'
    }).pipe(
      map(response => {
        if (response.isValid) {
          console.log('憑證驗證成功');
          return true;
        } else {
          console.warn('憑證驗證失敗:', response.message);
          return this.router.createUrlTree(['/corporate/certificate-verification'], {
            queryParams: { 
              returnUrl: state.url,
              error: 'invalid_certificate'
            }
          });
        }
      }),
      catchError(error => {
        console.error('憑證驗證過程發生錯誤:', error);
        
        this.errorHandler.handleHttpError(error, {
          showUserNotification: true,
          logError: true
        }).subscribe();
        
        return of(this.router.createUrlTree(['/corporate/certificate-verification'], {
          queryParams: { 
            returnUrl: state.url,
            error: 'verification_failed'
          }
        }));
      })
    );
  }

  /**
   * 從儲存中取得憑證資訊
   * 
   * @returns 憑證資訊或null
   * @private
   */
  private getCertificateFromStorage(): { data: string; expiry: Date } | null {
    try {
      const certificateInfo = sessionStorage.getItem('corporateCertificate');
      if (certificateInfo) {
        const parsed = JSON.parse(certificateInfo);
        
        // 檢查憑證是否過期
        const expiry = new Date(parsed.expiry);
        if (expiry > new Date()) {
          return parsed;
        } else {
          console.warn('儲存的憑證已過期');
          sessionStorage.removeItem('corporateCertificate');
        }
      }
    } catch (error) {
      console.error('讀取憑證儲存資訊時發生錯誤:', error);
      sessionStorage.removeItem('corporateCertificate');
    }
    
    return null;
  }
}

/**
 * 法人統一編號驗證守衛
 * 
 * 用於保護需要有效統一編號的路由
 * 驗證公司統一編號是否有效且狀態正常
 * 
 * @example
 * ```typescript
 * // 在路由設定中使用
 * {
 *   path: 'company-info',
 *   component: CompanyInfoComponent,
 *   canActivate: [UnifiedNumberVerificationGuard]
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class UnifiedNumberVerificationGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private unifiedNumberApi: UnifiedNumberApiService,
    private errorHandler: ErrorHandlerService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟用路由
   * 
   * @param route 啟用的路由快照
   * @param state 路由器狀態快照
   * @returns 是否允許啟用
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    return this.checkUnifiedNumberValidity(route, state);
  }

  /**
   * 檢查是否可以啟用子路由
   * 
   * @param childRoute 子路由快照
   * @param state 路由器狀態快照
   * @returns 是否允許啟用
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    return this.checkUnifiedNumberValidity(childRoute, state);
  }

  /**
   * 檢查統一編號有效性
   * 
   * @param route 路由快照
   * @param state 路由器狀態快照
   * @returns 統一編號驗證結果
   * @private
   */
  private checkUnifiedNumberValidity(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    // 從路由參數或儲存中取得統一編號
    const unifiedNumber = route.params['unifiedNumber'] || 
                         route.queryParams['unifiedNumber'] || 
                         this.getUnifiedNumberFromStorage();
    
    if (!unifiedNumber) {
      console.warn('未找到統一編號，重導向到公司資訊填寫頁面');
      return this.router.createUrlTree(['/corporate/company-info'], {
        queryParams: { returnUrl: state.url }
      });
    }

    // 本地格式驗證
    if (!this.unifiedNumberApi.validateFormatLocally(unifiedNumber)) {
      console.warn('統一編號格式不正確');
      return this.router.createUrlTree(['/corporate/company-info'], {
        queryParams: { 
          returnUrl: state.url,
          error: 'invalid_format'
        }
      });
    }

    // 遠端驗證
    return this.unifiedNumberApi.verifyUnifiedNumber({
      unifiedNumber,
      verificationLevel: 'COMPREHENSIVE',
      checkBusinessStatus: true,
      checkCapital: true
    }).pipe(
      map(response => {
        if (response.isValid && response.verificationDetails.businessStatusCheck?.isActive) {
          console.log('統一編號驗證成功');
          
          // 儲存驗證成功的公司資訊
          if (response.companyInfo) {
            this.storeCompanyInfo(response.companyInfo);
          }
          
          return true;
        } else {
          console.warn('統一編號驗證失敗或公司狀態異常:', response.message);
          return this.router.createUrlTree(['/corporate/company-info'], {
            queryParams: { 
              returnUrl: state.url,
              error: 'verification_failed',
              message: response.message
            }
          });
        }
      }),
      catchError(error => {
        console.error('統一編號驗證過程發生錯誤:', error);
        
        this.errorHandler.handleHttpError(error, {
          showUserNotification: true,
          logError: true
        }).subscribe();
        
        return of(this.router.createUrlTree(['/corporate/company-info'], {
          queryParams: { 
            returnUrl: state.url,
            error: 'verification_error'
          }
        }));
      })
    );
  }

  /**
   * 從儲存中取得統一編號
   * 
   * @returns 統一編號或null
   * @private
   */
  private getUnifiedNumberFromStorage(): string | null {
    try {
      const companyInfo = sessionStorage.getItem('corporateCompanyInfo');
      if (companyInfo) {
        const parsed = JSON.parse(companyInfo);
        return parsed.unifiedNumber || null;
      }
    } catch (error) {
      console.error('讀取公司資訊儲存時發生錯誤:', error);
    }
    
    return null;
  }

  /**
   * 儲存公司資訊
   * 
   * @param companyInfo 公司基本資訊
   * @private
   */
  private storeCompanyInfo(companyInfo: any): void {
    try {
      sessionStorage.setItem('corporateCompanyInfo', JSON.stringify({
        ...companyInfo,
        verifiedAt: new Date().toISOString()
      }));
    } catch (error) {
      console.error('儲存公司資訊時發生錯誤:', error);
    }
  }
}

/**
 * 法人申請流程完整性守衛
 * 
 * 確保使用者已完成必要的申請步驟
 * 驗證申請資料的完整性和有效性
 * 
 * @example
 * ```typescript
 * {
 *   path: 'confirmation',
 *   component: ConfirmationComponent,
 *   canActivate: [ApplicationCompletenessGuard]
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class ApplicationCompletenessGuard implements CanActivate {
  
  constructor(
    private errorHandler: ErrorHandlerService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟用路由
   * 
   * @param route 啟用的路由快照
   * @param state 路由器狀態快照
   * @returns 是否允許啟用
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): GuardResponse {
    const requiredSteps = this.getRequiredSteps(route);
    const completedSteps = this.getCompletedSteps();
    
    const missingSteps = requiredSteps.filter(step => !completedSteps.includes(step));
    
    if (missingSteps.length > 0) {
      console.warn('申請流程不完整，缺少步驟:', missingSteps);
      
      // 重導向到第一個缺少的步驟
      const nextStep = this.getNextStepRoute(missingSteps[0]);
      return this.router.createUrlTree([nextStep], {
        queryParams: { 
          returnUrl: state.url,
          missingStep: missingSteps[0]
        }
      });
    }
    
    console.log('申請流程完整性檢查通過');
    return true;
  }

  /**
   * 取得當前路由所需的完成步驟
   * 
   * @param route 路由快照
   * @returns 必要步驟陣列
   * @private
   */
  private getRequiredSteps(route: ActivatedRouteSnapshot): string[] {
    // 根據路由設定或路由資料決定必要步驟
    const routeData = route.data;
    
    if (routeData && routeData['requiredSteps']) {
      return routeData['requiredSteps'];
    }
    
    // 預設必要步驟
    const allSteps = [
      'certificate_verification',
      'company_info',
      'applicant_info',
      'application_details'
    ];
    
    // 根據當前路由決定需要完成的步驟
    const currentPath = route.routeConfig?.path || '';
    
    if (currentPath.includes('confirmation')) {
      return allSteps;
    } else if (currentPath.includes('application-details')) {
      return allSteps.slice(0, 3);
    } else if (currentPath.includes('applicant-info')) {
      return allSteps.slice(0, 2);
    } else if (currentPath.includes('company-info')) {
      return allSteps.slice(0, 1);
    }
    
    return [];
  }

  /**
   * 取得已完成的步驟
   * 
   * @returns 已完成步驟陣列
   * @private
   */
  private getCompletedSteps(): string[] {
    const completedSteps: string[] = [];
    
    try {
      // 檢查憑證驗證
      if (sessionStorage.getItem('corporateCertificate')) {
        completedSteps.push('certificate_verification');
      }
      
      // 檢查公司資訊
      if (sessionStorage.getItem('corporateCompanyInfo')) {
        completedSteps.push('company_info');
      }
      
      // 檢查申請人資訊
      if (sessionStorage.getItem('corporateApplicantInfo')) {
        completedSteps.push('applicant_info');
      }
      
      // 檢查申請詳細資料
      if (sessionStorage.getItem('corporateApplicationDetails')) {
        completedSteps.push('application_details');
      }
      
    } catch (error) {
      console.error('檢查已完成步驟時發生錯誤:', error);
    }
    
    return completedSteps;
  }

  /**
   * 取得下一步驟的路由
   * 
   * @param step 步驟名稱
   * @returns 路由路徑
   * @private
   */
  private getNextStepRoute(step: string): string {
    const stepRouteMap: Record<string, string> = {
      'certificate_verification': '/corporate/certificate-verification',
      'company_info': '/corporate/company-info',
      'applicant_info': '/corporate/applicant-info',
      'application_details': '/corporate/application-details'
    };
    
    return stepRouteMap[step] || '/corporate/landing';
  }
}

/**
 * 法人頁面離開確認守衛
 * 
 * 在使用者離開頁面前確認是否要儲存資料
 * 防止意外離開導致資料遺失
 * 
 * @example
 * ```typescript
 * {
 *   path: 'form',
 *   component: FormComponent,
 *   canDeactivate: [CanDeactivateGuard]
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class CanDeactivateGuard implements CanDeactivate<CanComponentDeactivate> {
  
  constructor(private errorHandler: ErrorHandlerService) {}

  /**
   * 檢查是否可以離開當前路由
   * 
   * @param component 當前組件
   * @param currentRoute 當前路由
   * @param currentState 當前狀態
   * @param nextState 下一個狀態
   * @returns 是否允許離開
   */
  canDeactivate(
    component: CanComponentDeactivate,
    currentRoute: ActivatedRouteSnapshot,
    currentState: RouterStateSnapshot,
    nextState?: RouterStateSnapshot
  ): GuardResponse {
    
    // 如果組件實作了 canDeactivate 方法，呼叫該方法
    if (component && typeof component.canDeactivate === 'function') {
      try {
        const result = component.canDeactivate();
        
        if (result instanceof Observable) {
          return result.pipe(
            catchError(error => {
              console.error('離開頁面檢查時發生錯誤:', error);
              this.errorHandler.handleGeneralError(
                error,
                'CAN_DEACTIVATE_GUARD',
                { logError: true }
              ).subscribe();
              return of(true); // 發生錯誤時允許離開
            })
          );
        } else if (result instanceof Promise) {
          return result.catch(error => {
            console.error('離開頁面檢查時發生錯誤:', error);
            this.errorHandler.handleGeneralError(
              error,
              'CAN_DEACTIVATE_GUARD',
              { logError: true }
            ).subscribe();
            return true; // 發生錯誤時允許離開
          });
        } else {
          return result;
        }
      } catch (error) {
        console.error('離開頁面檢查時發生錯誤:', error);
        this.errorHandler.handleGeneralError(
          error,
          'CAN_DEACTIVATE_GUARD',
          { logError: true }
        ).subscribe();
        return true; // 發生錯誤時允許離開
      }
    }
    
    // 預設行為：檢查是否有未儲存的資料
    return this.checkUnsavedChanges(currentRoute);
  }

  /**
   * 檢查是否有未儲存的變更
   * 
   * @param route 當前路由
   * @returns 是否允許離開
   * @private
   */
  private checkUnsavedChanges(route: ActivatedRouteSnapshot): boolean {
    // 這裡可以實作檢查未儲存變更的邏輯
    // 例如檢查表單狀態、localStorage中的草稿等
    
    try {
      const draftData = sessionStorage.getItem('corporateApplicationDraft');
      if (draftData) {
        const draft = JSON.parse(draftData);
        const lastModified = new Date(draft.lastModified);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        // 如果草稿在5分鐘內有修改，提示使用者
        if (lastModified > fiveMinutesAgo) {
          return confirm(
            '您有未儲存的變更，確定要離開此頁面嗎？\n' +
            '離開後未儲存的資料將會遺失。'
          );
        }
      }
    } catch (error) {
      console.error('檢查未儲存變更時發生錯誤:', error);
    }
    
    return true;
  }
}

/**
 * 法人模組載入守衛
 * 
 * 控制整個法人模組的載入權限
 * 檢查使用者是否有權限存取法人功能
 * 
 * @example
 * ```typescript
 * {
 *   path: 'corporate',
 *   loadChildren: () => import('./corporate.module').then(m => m.CorporateModule),
 *   canLoad: [CorporateModuleLoadGuard]
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class CorporateModuleLoadGuard implements CanLoad {
  
  constructor(
    private errorHandler: ErrorHandlerService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以載入模組
   * 
   * @param route 路由
   * @param segments URL片段
   * @returns 是否允許載入
   */
  canLoad(route: Route, segments: UrlSegment[]): GuardResponse {
    // 檢查使用者權限
    const hasPermission = this.checkUserPermission();
    
    if (!hasPermission) {
      console.warn('使用者沒有存取法人模組的權限');
      return this.router.createUrlTree(['/unauthorized']);
    }
    
    // 檢查系統狀態
    const isSystemAvailable = this.checkSystemAvailability();
    
    if (!isSystemAvailable) {
      console.warn('法人模組系統暫時無法使用');
      return this.router.createUrlTree(['/maintenance']);
    }
    
    console.log('法人模組載入權限檢查通過');
    return true;
  }

  /**
   * 檢查使用者權限
   * 
   * @returns 是否有權限
   * @private
   */
  private checkUserPermission(): boolean {
    try {
      // 從localStorage或sessionStorage檢查使用者權限
      const userInfo = sessionStorage.getItem('userInfo');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        
        // 檢查使用者角色或權限
        return user.roles?.includes('CORPORATE_USER') ||
               user.permissions?.includes('ACCESS_CORPORATE_MODULE') ||
               user.userType === 'CORPORATE';
      }
    } catch (error) {
      console.error('檢查使用者權限時發生錯誤:', error);
      this.errorHandler.handleGeneralError(
        error,
        'PERMISSION_CHECK',
        { logError: true }
      ).subscribe();
    }
    
    return false;
  }

  /**
   * 檢查系統可用性
   * 
   * @returns 系統是否可用
   * @private
   */
  private checkSystemAvailability(): boolean {
    try {
      // 檢查系統維護狀態
      const maintenanceInfo = localStorage.getItem('systemMaintenance');
      if (maintenanceInfo) {
        const maintenance = JSON.parse(maintenanceInfo);
        const now = new Date();
        const startTime = new Date(maintenance.startTime);
        const endTime = new Date(maintenance.endTime);
        
        if (now >= startTime && now <= endTime && maintenance.affectedModules?.includes('CORPORATE')) {
          return false;
        }
      }
      
      // 檢查其他系統狀態指標
      const systemStatus = localStorage.getItem('systemStatus');
      if (systemStatus) {
        const status = JSON.parse(systemStatus);
        return status.corporate !== 'DOWN';
      }
      
    } catch (error) {
      console.error('檢查系統可用性時發生錯誤:', error);
    }
    
    return true;
  }
}

/**
 * 匯出所有守衛
 */
export const CORPORATE_GUARDS = [
  CertificateVerificationGuard,
  UnifiedNumberVerificationGuard,
  ApplicationCompletenessGuard,
  CanDeactivateGuard,
  CorporateModuleLoadGuard
];

/**
 * 匯出守衛提供者
 */
export const CORPORATE_GUARD_PROVIDERS = [
  ...CORPORATE_GUARDS
];