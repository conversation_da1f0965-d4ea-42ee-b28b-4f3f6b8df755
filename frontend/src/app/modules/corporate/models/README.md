# Corporate Models - 法人解款資料模型

![Models](https://img.shields.io/badge/Models-1-blue) ![Certificate](https://img.shields.io/badge/Certificate-PC/SC-green) ![Batch](https://img.shields.io/badge/Batch-Processing-orange) ![Enterprise](https://img.shields.io/badge/Enterprise-Grade-red)

## 🎯 模型概述

Corporate Models 包含法人解款模組的所有資料結構定義，涵蓋工商憑證驗證、企業資料管理、批次匯款處理、代表人資訊等企業級解款流程的核心資料模型。這些模型確保了企業業務邏輯的正確實作和資料一致性。

## 📊 模型統計

- **核心模型數**: 1個主要資料模型檔案
- **企業資料結構**: 10+ 複雜企業資料介面
- **憑證整合**: PC/SC讀卡機與工商憑證
- **批次處理**: 企業級多筆匯款處理能力

## 🗂️ 模型架構

```
models/
├── corporate.model.ts       # 法人解款核心資料模型
└── index.ts                # 統一匯出檔案 (待建立)
```

## 🏢 核心模型詳解

### [Corporate Model](corporate.model.ts) - 法人解款核心資料模型

**功能**: 企業解款流程的完整資料結構定義

### 1. 企業基本資料模型

#### CompanyInfo - 公司基本資料
```typescript
interface CompanyInfo {
  // 基本識別資訊
  companyName: string;              // 公司中文名稱
  companyNameEn?: string;           // 公司英文名稱
  companyEnglishName?: string;      // 公司英文名稱 (表單用)
  confirmEnglishName?: boolean;     // 確認英文名稱無誤
  unifiedNumber: string;            // 統一編號 (8位數)
  registrationNumber?: string;      // 公司登記號碼
  
  // 企業詳細資訊
  establishedDate?: string;         // 設立日期 (YYYY-MM-DD)
  capital?: number;                 // 資本額 (新台幣)
  industry?: string;                // 產業別代碼
  businessAddress?: string;         // 營業地址
  
  // 代表人資訊
  representativeName?: string;      // 代表人姓名
  
  // 聯絡資訊
  contactPhone?: string;            // 聯絡電話
  contactEmail?: string;            // 聯絡信箱
  
  // 銀行帳戶資訊
  bankName?: string;                // 銀行名稱
  branchCode?: string;              // 分行代碼
  accountNumber?: string;           // 帳號
  
  // 狀態與時間戳記
  lastUpdated?: string;             // 最後更新時間
  verifiedAt?: string;              // 驗證時間
}
```

**使用範例**:
```typescript
const companyInfo: CompanyInfo = {
  companyName: '台灣科技股份有限公司',
  companyNameEn: 'Taiwan Technology Co., Ltd.',
  confirmEnglishName: true,
  unifiedNumber: '********',
  establishedDate: '2010-01-15',
  capital: ********,
  industry: '2910',  // 資訊及通訊傳播業
  businessAddress: '台北市信義區信義路五段7號',
  representativeName: '張董事長',
  contactPhone: '02-2345-6789',
  contactEmail: '<EMAIL>'
};
```

#### RepresentativeInfo - 代表人資料
```typescript
interface RepresentativeInfo {
  // 個人基本資料
  name: string;                     // 代表人中文姓名
  nameEn: string;                   // 代表人英文姓名
  title: string;                    // 職稱 (董事長、總經理等)
  idNumber: string;                 // 身分證字號
  birthDate: string;                // 出生日期 (YYYY-MM-DD)
  
  // 聯絡資訊
  mobilePhone: string;              // 手機號碼
  email: string;                    // 電子郵件
  
  // 職務資訊
  representativeSince?: string;     // 擔任代表人起始日 (YYYY-MM-DD)
}
```

**驗證範例**:
```typescript
// 代表人資料驗證
function validateRepresentativeInfo(rep: RepresentativeInfo): boolean {
  // 身分證號格式檢查
  if (!TaiwanIdValidator.validate(rep.idNumber)) {
    throw new Error('代表人身分證號格式錯誤');
  }
  
  // 手機號碼格式檢查
  if (!TaiwanMobileValidator.validate(rep.mobilePhone)) {
    throw new Error('代表人手機號碼格式錯誤');
  }
  
  // Email格式檢查
  if (!EmailValidator.validate(rep.email)) {
    throw new Error('代表人Email格式錯誤');
  }
  
  return true;
}
```

### 2. 工商憑證與安全模型

#### CertificateInfo - 工商憑證資料
```typescript
interface CertificateInfo {
  // 憑證基本資訊
  serialNumber: string;             // 憑證序號 (唯一識別)
  issuer: string;                   // 發證單位 (通常是MOICA)
  subject: string;                  // 憑證主體 (包含企業資訊)
  
  // 有效期限
  validFrom: string;                // 有效期起始 (ISO 8601格式)
  validTo: string;                  // 有效期結束 (ISO 8601格式)
  
  // 企業識別資訊
  unifiedNumber: string;            // 統一編號
  companyName: string;              // 公司名稱
  businessAddress?: string;         // 營業地址
  representativeName?: string;      // 代表人姓名
  
  // 安全狀態
  pinAttempts?: number;             // PIN碼剩餘嘗試次數 (預設3次)
}
```

**憑證處理範例**:
```typescript
// 憑證有效性檢查
class CertificateValidator {
  static validateCertificate(cert: CertificateInfo): CertificateValidationResult {
    const now = new Date();
    const validFrom = new Date(cert.validFrom);
    const validTo = new Date(cert.validTo);
    
    // 檢查憑證是否在有效期內
    if (now < validFrom) {
      return {
        isValid: false,
        errorCode: 'CERT_NOT_YET_VALID',
        message: '工商憑證尚未生效'
      };
    }
    
    if (now > validTo) {
      return {
        isValid: false,
        errorCode: 'CERT_EXPIRED',
        message: '工商憑證已過期，請申請新憑證'
      };
    }
    
    // 檢查PIN碼嘗試次數
    if (cert.pinAttempts !== undefined && cert.pinAttempts <= 0) {
      return {
        isValid: false,
        errorCode: 'PIN_LOCKED',
        message: 'PIN碼已鎖定，請聯繫發證單位'
      };
    }
    
    return {
      isValid: true,
      message: '工商憑證驗證通過'
    };
  }
}

interface CertificateValidationResult {
  isValid: boolean;
  errorCode?: string;
  message: string;
}
```

### 3. 匯款處理與批次模型

#### CorporateRemittance - 法人匯款資料
```typescript
interface CorporateRemittance {
  // 匯款識別
  remittanceId: string;             // 匯款編號 (唯一識別)
  
  // 匯款人資訊
  senderName: string;               // 匯款人姓名/公司名稱
  senderCompany?: string;           // 匯款公司名稱 (如果是企業對企業)
  senderCountry: string;            // 匯款國家 (ISO 3166-1 alpha-2)
  
  // 金額資訊
  amount: number;                   // 原幣金額
  currency: string;                 // 幣別 (ISO 4217)
  exchangeRate: number;             // 匯率
  twdAmount: number;                // 台幣金額
  
  // 匯款性質
  purpose: string;                  // 匯款性質說明
  purposeCode: string;              // 匯款性質代碼
  
  // 時間資訊
  notificationDate: string;         // 通知日期 (YYYY-MM-DD)
  expiryDate: string;               // 到期日期 (YYYY-MM-DD)
  
  // 附加資訊
  message?: string;                 // 匯款附言
  
  // 狀態管理
  status: RemittanceStatus;         // 匯款狀態
  selected?: boolean;               // 是否選取 (用於批次處理)
}

// 匯款狀態枚舉
enum RemittanceStatus {
  PENDING = 'PENDING',              // 待處理
  PROCESSING = 'PROCESSING',        // 處理中
  COMPLETED = 'COMPLETED',          // 已完成
  EXPIRED = 'EXPIRED',              // 已逾期
  CANCELLED = 'CANCELLED'           // 已取消
}
```

#### BatchProcessing - 批次處理資料
```typescript
interface BatchProcessing {
  // 批次識別
  batchId: string;                  // 批次編號 (系統生成)
  
  // 統計資訊
  totalCount: number;               // 總筆數
  totalAmount: number;              // 總金額 (台幣)
  
  // 處理內容
  selectedRemittances: CorporateRemittance[]; // 選取的匯款清單
  
  // 費用計算
  processingFee: number;            // 批次處理手續費
  netAmount: number;                // 扣除手續費後實收金額
}
```

**批次處理範例**:
```typescript
class BatchProcessor {
  static createBatch(remittances: CorporateRemittance[]): BatchProcessing {
    // 計算總金額
    const totalAmount = remittances.reduce((sum, r) => sum + r.twdAmount, 0);
    
    // 計算批次手續費 (依筆數和金額計算)
    const processingFee = this.calculateBatchFee(remittances.length, totalAmount);
    
    return {
      batchId: `BATCH_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      totalCount: remittances.length,
      totalAmount,
      selectedRemittances: remittances,
      processingFee,
      netAmount: totalAmount - processingFee
    };
  }
  
  private static calculateBatchFee(count: number, amount: number): number {
    // 基本手續費: 每筆100元
    let baseFee = count * 100;
    
    // 批次折扣
    if (count >= 10) baseFee *= 0.9;  // 10筆以上9折
    if (count >= 20) baseFee *= 0.8;  // 20筆以上8折
    
    // 大額手續費: 金額的0.05%
    const amountFee = amount * 0.0005;
    
    return Math.round(baseFee + amountFee);
  }
}
```

### 4. 銀行帳戶與交易模型

#### CorporateBankAccount - 法人銀行帳戶
```typescript
interface CorporateBankAccount {
  // 銀行基本資訊
  bankCode: string;                 // 銀行代碼 (3位數)
  bankName: string;                 // 銀行名稱
  bankSwiftCode?: string;           // SWIFT代碼 (國際匯款用)
  
  // 分行資訊
  branchCode: string;               // 分行代碼 (4位數)
  branchName?: string;              // 分行名稱
  
  // 帳戶資訊
  accountNumber: string;            // 帳號
  accountName: string;              // 戶名 (必須與公司名稱一致)
  accountType: 'CHECKING' | 'SAVINGS'; // 帳戶類型
  
  // 狀態標記
  isDefault?: boolean;              // 是否為預設帳戶
  isVerified?: boolean;             // 是否已驗證
}
```

#### TransactionLimits - 交易限額設定
```typescript
interface TransactionLimits {
  singleTransactionLimit: number;   // 單筆交易限額
  dailyLimit: number;               // 每日交易限額
  monthlyLimit: number;             // 每月交易限額
  yearlyLimit: number;              // 每年交易限額
}

// 預設企業交易限額
const DEFAULT_CORPORATE_LIMITS: TransactionLimits = {
  singleTransactionLimit: 5000000,    // 單筆500萬
  dailyLimit: ********,               // 每日1000萬
  monthlyLimit: *********,            // 每月1億
  yearlyLimit: *********0             // 每年10億
};
```

### 5. 應用程式狀態與流程模型

#### CorporateApplication - 法人申請資料
```typescript
interface CorporateApplication {
  // 申請識別
  applicationId?: string;           // 申請編號 (提交後生成)
  
  // 核心資料
  companyInfo: CompanyInfo;         // 公司基本資料
  representativeInfo: RepresentativeInfo; // 代表人資料
  certificateInfo?: CertificateInfo; // 工商憑證資料 (驗證後填入)
  
  // 匯款處理
  remittances: CorporateRemittance[]; // 可用匯款清單
  selectedRemittances?: any[];      // 選擇的匯款 (用於搜尋結果)
  
  // 收款資訊
  bankAccount: CorporateBankAccount; // 收款帳戶
  
  // 批次處理
  batchProcessing?: BatchProcessing; // 批次處理資料 (多筆匯款時)
  
  // 狀態與時間
  createdAt?: Date;                 // 建立時間
  updatedAt?: Date;                 // 更新時間
  status?: ApplicationStatus;       // 申請狀態
}

// 申請狀態枚舉
enum ApplicationStatus {
  DRAFT = 'DRAFT',                  // 草稿 (初始狀態)
  CERTIFICATE_VERIFIED = 'CERTIFICATE_VERIFIED', // 憑證已驗證
  INFO_FILLED = 'INFO_FILLED',      // 基本資料已填寫
  REMITTANCE_SELECTED = 'REMITTANCE_SELECTED', // 已選擇匯款
  AMOUNT_CONFIRMED = 'AMOUNT_CONFIRMED', // 金額已確認
  SUBMITTED = 'SUBMITTED',          // 已提交申請
  COMPLETED = 'COMPLETED'           // 申請完成
}
```

**申請流程狀態管理**:
```typescript
class CorporateApplicationManager {
  static validateTransition(
    currentStatus: ApplicationStatus, 
    nextStatus: ApplicationStatus
  ): boolean {
    const validTransitions: Record<ApplicationStatus, ApplicationStatus[]> = {
      [ApplicationStatus.DRAFT]: [ApplicationStatus.CERTIFICATE_VERIFIED],
      [ApplicationStatus.CERTIFICATE_VERIFIED]: [ApplicationStatus.INFO_FILLED],
      [ApplicationStatus.INFO_FILLED]: [ApplicationStatus.REMITTANCE_SELECTED],
      [ApplicationStatus.REMITTANCE_SELECTED]: [ApplicationStatus.AMOUNT_CONFIRMED],
      [ApplicationStatus.AMOUNT_CONFIRMED]: [ApplicationStatus.SUBMITTED],
      [ApplicationStatus.SUBMITTED]: [ApplicationStatus.COMPLETED],
      [ApplicationStatus.COMPLETED]: [] // 終止狀態
    };
    
    return validTransitions[currentStatus]?.includes(nextStatus) || false;
  }
  
  static getNextRequiredStep(status: ApplicationStatus): string {
    const stepMap: Record<ApplicationStatus, string> = {
      [ApplicationStatus.DRAFT]: '請插入工商憑證並驗證',
      [ApplicationStatus.CERTIFICATE_VERIFIED]: '請填寫公司基本資料',
      [ApplicationStatus.INFO_FILLED]: '請選擇要解款的匯款',
      [ApplicationStatus.REMITTANCE_SELECTED]: '請確認解款金額',
      [ApplicationStatus.AMOUNT_CONFIRMED]: '請確認並提交申請',
      [ApplicationStatus.SUBMITTED]: '申請處理中',
      [ApplicationStatus.COMPLETED]: '申請已完成'
    };
    
    return stepMap[status] || '未知狀態';
  }
}
```

#### CorporateVerification - 法人驗證結果
```typescript
interface CorporateVerification {
  isValid: boolean;                 // 驗證是否通過
  
  // 驗證成功時的資訊
  companyName?: string;             // 驗證後的公司名稱
  unifiedNumber?: string;           // 驗證後的統一編號
  
  // 驗證失敗時的資訊
  errorMessage?: string;            // 錯誤訊息
  
  // 驗證時間
  verifiedAt?: Date;                // 驗證完成時間
}
```

## 🔧 統一編號驗證工具

```typescript
// 統一編號驗證器
export class UnifiedNumberValidator {
  /**
   * 驗證統一編號格式和檢查碼
   */
  static validate(unifiedNumber: string): boolean {
    // 格式檢查: 必須是8位數字
    if (!/^\d{8}$/.test(unifiedNumber)) {
      return false;
    }
    
    // 檢查碼演算法
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    const digits = unifiedNumber.split('').map(Number);
    
    let sum = 0;
    for (let i = 0; i < 7; i++) {
      const product = digits[i] * weights[i];
      sum += Math.floor(product / 10) + (product % 10);
    }
    
    const checkDigit = digits[7];
    const calculatedCheck = (10 - (sum % 10)) % 10;
    
    // 特殊情況: 如果計算結果是10，檢查碼應該是0
    return checkDigit === calculatedCheck || 
           (calculatedCheck === 10 && checkDigit === 0);
  }
  
  /**
   * 格式化統一編號顯示
   */
  static format(unifiedNumber: string): string {
    if (!unifiedNumber || unifiedNumber.length !== 8) {
      return unifiedNumber;
    }
    
    // 格式: ******** -> ********
    return unifiedNumber;
  }
  
  /**
   * 產生測試用的有效統一編號
   */
  static generateTestNumber(): string {
    // 產生前7位隨機數字
    const prefix = Math.floor(Math.random() * ********).toString().padStart(7, '0');
    
    // 計算檢查碼
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    const digits = prefix.split('').map(Number);
    
    let sum = 0;
    for (let i = 0; i < 7; i++) {
      const product = digits[i] * weights[i];
      sum += Math.floor(product / 10) + (product % 10);
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    
    return prefix + checkDigit.toString();
  }
}
```

## 🧪 類型守衛與工具函數

```typescript
// 類型守衛
export function isValidCompanyInfo(data: any): data is CompanyInfo {
  return data && 
         typeof data.companyName === 'string' &&
         typeof data.unifiedNumber === 'string' &&
         UnifiedNumberValidator.validate(data.unifiedNumber);
}

export function isValidCertificateInfo(data: any): data is CertificateInfo {
  return data &&
         typeof data.serialNumber === 'string' &&
         typeof data.issuer === 'string' &&
         typeof data.subject === 'string' &&
         typeof data.validFrom === 'string' &&
         typeof data.validTo === 'string';
}

export function isCorporateRemittance(data: any): data is CorporateRemittance {
  return data &&
         typeof data.remittanceId === 'string' &&
         typeof data.senderName === 'string' &&
         typeof data.amount === 'number' &&
         typeof data.currency === 'string';
}

// 資料轉換工具
export class CorporateModelTransformer {
  static toApiFormat(application: CorporateApplication): any {
    return {
      companyInfo: {
        companyName: application.companyInfo.companyName,
        companyNameEn: application.companyInfo.companyNameEn,
        unifiedNumber: application.companyInfo.unifiedNumber,
        representativeName: application.companyInfo.representativeName
      },
      representativeInfo: application.representativeInfo,
      selectedRemittances: application.selectedRemittances,
      bankAccount: application.bankAccount,
      batchProcessing: application.batchProcessing
    };
  }
  
  static fromApiResponse(response: any): CorporateApplication {
    return {
      applicationId: response.applicationId,
      companyInfo: response.companyInfo,
      representativeInfo: response.representativeInfo,
      remittances: response.remittances || [],
      bankAccount: response.bankAccount,
      status: response.status || ApplicationStatus.DRAFT,
      createdAt: response.createdAt ? new Date(response.createdAt) : new Date(),
      updatedAt: response.updatedAt ? new Date(response.updatedAt) : new Date()
    };
  }
}
```

## 📋 使用範例

### 完整企業申請流程
```typescript
// 1. 初始化企業申請
const corporateApp: CorporateApplication = {
  companyInfo: {
    companyName: '科技創新股份有限公司',
    companyNameEn: 'Tech Innovation Co., Ltd.',
    unifiedNumber: '********',
    establishedDate: '2015-03-20',
    capital: *********,
    businessAddress: '台北市內湖區民權東路六段123號',
    representativeName: '李執行長',
    contactPhone: '02-8751-2345',
    contactEmail: '<EMAIL>'
  },
  representativeInfo: {
    name: '李執行長',
    nameEn: 'Li CEO',
    title: '執行長',
    idNumber: 'A********9',
    birthDate: '1975-08-15',
    mobilePhone: '09********',
    email: '<EMAIL>'
  },
  remittances: [],
  bankAccount: {
    bankCode: '004',
    bankName: '台灣銀行',
    branchCode: '0001',
    accountNumber: '********90123',
    accountName: '科技創新股份有限公司',
    accountType: 'CHECKING',
    isDefault: true
  },
  status: ApplicationStatus.DRAFT
};

// 2. 憑證驗證
const certificateInfo: CertificateInfo = {
  serialNumber: 'CERT********9',
  issuer: 'MOICA',
  subject: 'CN=科技創新股份有限公司,O=MOICA',
  validFrom: '2024-01-01T00:00:00.000Z',
  validTo: '2025-12-31T23:59:59.999Z',
  unifiedNumber: '********',
  companyName: '科技創新股份有限公司',
  representativeName: '李執行長',
  pinAttempts: 3
};

// 3. 批次處理
const selectedRemittances: CorporateRemittance[] = [
  {
    remittanceId: 'REM001',
    senderName: 'US Tech Company',
    senderCountry: 'US',
    amount: 100000,
    currency: 'USD',
    exchangeRate: 31.5,
    twdAmount: 3150000,
    purpose: '技術授權費',
    purposeCode: '442',
    notificationDate: '2024-06-01',
    expiryDate: '2024-06-30',
    status: RemittanceStatus.PENDING
  }
];

const batchProcessing = BatchProcessor.createBatch(selectedRemittances);
```

## 🔗 相關連結

### 模組文檔
- [Corporate Module README](../README.md) - 法人模組總覽
- [Corporate Components](../components/README.md) - 法人元件文檔
- [Corporate Services](../services/README.md) - 法人服務文檔

### 相關模型
- [Individual Models](../../individual/models/README.md) - 自然人模組資料模型
- [Supplement Models](../../supplement/models/README.md) - 補件模組資料模型

### 技術整合
- [PC/SC Integration](../docs/pcsc-integration.md) - 讀卡機整合文檔
- [Certificate Validation](../docs/certificate-validation.md) - 憑證驗證規則

---

**🎯 模型完成度**: 1/1 完成 | **🏢 企業資料**: 完整 | **🔐 憑證整合**: 支援 | **📊 批次處理**: 完整

*Corporate Models 提供完整的企業級資料結構，涵蓋工商憑證驗證、批次匯款處理、企業資料管理等法人解款流程的所有資料需求，確保企業業務邏輯的正確實作。*