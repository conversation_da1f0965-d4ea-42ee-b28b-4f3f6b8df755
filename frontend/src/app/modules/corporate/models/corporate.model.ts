/**
 * 法人解款相關資料模型
 */

/**
 * 公司基本資料
 */
export interface CompanyInfo {
  companyName: string;              // 公司名稱
  companyNameEn?: string;           // 公司英文名稱
  companyEnglishName?: string;      // 公司英文名稱 (表單用)
  confirmEnglishName?: boolean;     // 確認英文名稱
  unifiedNumber: string;            // 統一編號
  registrationNumber?: string;      // 公司登記號碼
  establishedDate?: string;         // 設立日期
  capital?: number;                 // 資本額
  industry?: string;                // 產業別
  businessAddress?: string;         // 營業地址
  representativeName?: string;      // 代表人姓名
  contactPhone?: string;            // 聯絡電話
  contactEmail?: string;            // 聯絡信箱
  bankName?: string;                // 銀行名稱
  branchCode?: string;              // 分行代碼
  accountNumber?: string;           // 帳號
  lastUpdated?: string;             // 最後更新時間
  verifiedAt?: string;              // 驗證時間
}

/**
 * 代表人資料
 */
export interface RepresentativeInfo {
  name: string;                     // 代表人姓名
  nameEn: string;                   // 代表人英文姓名
  title: string;                    // 職稱
  idNumber: string;                 // 身分證字號
  birthDate: string;                // 出生日期
  mobilePhone: string;              // 手機號碼
  email: string;                    // 電子郵件
  representativeSince?: string;     // 擔任代表人起始日
}

/**
 * 工商憑證資料
 */
export interface CertificateInfo {
  serialNumber: string;             // 憑證序號
  issuer: string;                   // 發證單位
  subject: string;                  // 憑證主體
  validFrom: string;                // 有效期起始
  validTo: string;                  // 有效期結束
  unifiedNumber: string;            // 統一編號
  companyName: string;              // 公司名稱
  businessAddress?: string;         // 營業地址
  representativeName?: string;      // 代表人姓名
  pinAttempts?: number;             // PIN碼剩餘嘗試次數
}

/**
 * 法人匯款資料
 */
export interface CorporateRemittance {
  remittanceId: string;             // 匯款編號
  senderName: string;               // 匯款人姓名
  senderCompany?: string;           // 匯款公司名稱
  senderCountry: string;            // 匯款國家
  amount: number;                   // 匯款金額
  currency: string;                 // 幣別
  exchangeRate: number;             // 匯率
  twdAmount: number;                // 台幣金額
  purpose: string;                  // 匯款性質
  purposeCode: string;              // 匯款性質代碼
  notificationDate: string;         // 通知日期
  expiryDate: string;               // 到期日期
  message?: string;                 // 附言
  status: RemittanceStatus;         // 狀態
  selected?: boolean;               // 是否選取（批次處理用）
}

/**
 * 匯款狀態
 */
export enum RemittanceStatus {
  PENDING = 'PENDING',              // 待處理
  PROCESSING = 'PROCESSING',        // 處理中
  COMPLETED = 'COMPLETED',          // 已完成
  EXPIRED = 'EXPIRED',              // 已逾期
  CANCELLED = 'CANCELLED'           // 已取消
}

/**
 * 批次處理資料
 */
export interface BatchProcessing {
  batchId: string;                  // 批次編號
  totalCount: number;               // 總筆數
  totalAmount: number;              // 總金額
  selectedRemittances: CorporateRemittance[]; // 選取的匯款
  processingFee: number;            // 手續費
  netAmount: number;                // 實收金額
}

/**
 * 法人銀行帳戶
 */
export interface CorporateBankAccount {
  bankCode: string;                 // 銀行代碼
  bankName: string;                 // 銀行名稱
  bankSwiftCode?: string;           // SWIFT代碼
  branchCode: string;               // 分行代碼
  branchName?: string;              // 分行名稱
  accountNumber: string;            // 帳號
  accountName: string;              // 戶名
  accountType: 'CHECKING' | 'SAVINGS'; // 帳戶類型
  isDefault?: boolean;              // 是否為預設帳戶
  isVerified?: boolean;             // 是否已驗證
}

/**
 * 法人申請資料
 */
export interface CorporateApplication {
  applicationId?: string;           // 申請編號
  companyInfo: CompanyInfo;         // 公司資料
  representativeInfo: RepresentativeInfo; // 代表人資料
  certificateInfo?: CertificateInfo; // 憑證資料
  remittances: CorporateRemittance[]; // 匯款清單
  selectedRemittances?: any[];      // 選擇的匯款（用於搜尋結果）
  bankAccount: CorporateBankAccount; // 收款帳戶
  batchProcessing?: BatchProcessing; // 批次處理資料
  createdAt?: Date;                 // 建立時間
  updatedAt?: Date;                 // 更新時間
  status?: ApplicationStatus;        // 申請狀態
}

/**
 * 申請狀態
 */
export enum ApplicationStatus {
  DRAFT = 'DRAFT',                  // 草稿
  CERTIFICATE_VERIFIED = 'CERTIFICATE_VERIFIED', // 憑證已驗證
  INFO_FILLED = 'INFO_FILLED',      // 資料已填寫
  REMITTANCE_SELECTED = 'REMITTANCE_SELECTED', // 已選擇匯款
  AMOUNT_CONFIRMED = 'AMOUNT_CONFIRMED', // 金額已確認
  SUBMITTED = 'SUBMITTED',          // 已提交
  COMPLETED = 'COMPLETED'           // 已完成
}

/**
 * 法人驗證結果
 */
export interface CorporateVerification {
  isValid: boolean;                 // 是否有效
  companyName?: string;             // 公司名稱
  unifiedNumber?: string;           // 統一編號
  errorMessage?: string;            // 錯誤訊息
  verifiedAt?: Date;                // 驗證時間
}

/**
 * 交易限額設定
 */
export interface TransactionLimits {
  singleTransactionLimit: number;   // 單筆限額
  dailyLimit: number;               // 每日限額
  monthlyLimit: number;             // 每月限額
  yearlyLimit: number;              // 每年限額
}
