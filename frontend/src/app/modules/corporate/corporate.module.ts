import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

// 核心共用模組
import { SharedModule } from '../../@core/shared/shared.module';

// IBR 共用模組
import { IbrSharedModule } from '../../@core/shared-2/ibr-shared.module';

// Corporate 路由
import { CorporateRoutingModule } from './corporate-routing.module';

// Corporate 元件 (共6個頁面) - Standalone Components 不需要在 Module 中 import
// import { CLandingComponent } from './components/c-landing/c-landing.component';
// import { CCertificateVerificationComponent } from './components/c-certificate-verification/c-certificate-verification.component';
// import { CCompanyInfoComponent } from './components/c-company-info/c-company-info.component';
// import { CRemittanceDetailComponent } from './components/c-remittance-detail/c-remittance-detail.component';
// import { CAmountConfirmationComponent } from './components/c-amount-confirmation/c-amount-confirmation.component';
// import { CApplicationCompleteComponent } from './components/c-application-complete/c-application-complete.component';

// Corporate 服務
import { CorporateApiService } from './services/corporate-api.service';
import { CorporateService } from './services/corporate.service';
import { CertificateService } from './services/certificate.service';
// import { CorporateStateService } from './services/corporate-state.service';
// import { CorporateValidationService } from './services/corporate-validation.service';
// import { DriverDownloadService } from './services/driver-download.service';

// Corporate 守衛 (待建立)
// import { CorporateAuthGuard } from './guards/corporate-auth.guard';

/**
 * Corporate Module - 法人解款模組
 * 
 * 負責處理企業用戶的跨境匯入匯款解款流程
 * 包含 6 個主要頁面和完整的工商憑證驗證流程
 * 
 * 主要流程：
 * 1. 企業條款同意 (landing)
 * 2. 法人驗證資料輸入 (company-info)
 * 3. 工商憑證驗證 (certificate-verification)
 * 4. 匯款確認頁 (remittance-detail)
 * 5. 金額確認頁 (amount-confirmation)
 * 6. 申請完成頁 (application-complete)
 * 
 * 技術特色：
 * - PC/SC 讀卡機整合
 * - 工商憑證驗證
 * - 統一編號驗證
 * - 跨平台驅動支援
 * - 企業級安全機制
 */
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    SharedModule,
    IbrSharedModule,
    CorporateRoutingModule
    // 註解：Standalone Components (6個頁面) 不需要在 imports 中宣告
    // CLandingComponent,                    // 01 - 企業條款同意 (standalone)
    // CCompanyInfoComponent,                // 02 - 法人驗證資料輸入 (standalone)
    // CCertificateVerificationComponent,    // 03 - 工商憑證驗證 (standalone)
    // CRemittanceDetailComponent,           // 04 - 匯款確認頁 (standalone)
    // CAmountConfirmationComponent,         // 05 - 金額確認頁 (standalone)
    // CApplicationCompleteComponent         // 06 - 申請完成頁 (standalone)
  ],
  declarations: [
    // 所有組件都是 standalone，無需在 declarations 中宣告
  ],
  providers: [
    // Corporate 專用服務
    CorporateApiService,
    CorporateService,
    CertificateService,
    // CorporateStateService,
    // CorporateValidationService,
    // DriverDownloadService,
    
    // 路由守衛 (待建立)
    // CorporateAuthGuard
  ]
})
export class CorporateModule { }
