import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TestNotificationComponent } from './components/test-notification/test-notification.component';
import { IExternalEntryComponent } from '../individual/components/i-external-entry/i-external-entry.component';
import { ModuleApiTestComponent } from './components/module-api-test/module-api-test.component';

const routes: Routes = [
  {
    path: 'test-notification',
    component: TestNotificationComponent,
    data: { 
      title: '01 測試通知頁',
      breadcrumb: '測試通知'
    }
  },
  {
    path: 'external-entry',
    component: IExternalEntryComponent,
    data: { 
      title: '02 外部入口頁',
      breadcrumb: '外部入口',
      description: '外部系統匯款通知頁面'
    }
  },
  {
    path: 'module-api-test',
    component: ModuleApiTestComponent,
    data: { 
      title: '03 模組 API 測試',
      breadcrumb: 'API 測試',
      description: '三大模組 API 介面測試工具'
    }
  },
  {
    path: '',
    redirectTo: 'test-notification',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TestingRoutingModule { }