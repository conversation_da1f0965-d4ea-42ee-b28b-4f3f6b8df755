import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { TestingRoutingModule } from './testing-routing.module';
import { TestNotificationComponent } from './components/test-notification/test-notification.component';
import { IExternalEntryComponent } from '../individual/components/i-external-entry/i-external-entry.component';
import { ModuleApiTestComponent } from './components/module-api-test/module-api-test.component';
import { HttpClientModule } from '@angular/common/http';

/**
 * IBR 測試模組
 * 提供開發和測試時使用的工具和頁面
 */
@NgModule({
  declarations: [
    TestNotificationComponent,
    ModuleApiTestComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    TestingRoutingModule,
    IExternalEntryComponent  // 使用 standalone component，放在 imports 中
  ]
})
export class TestingModule { }