# Testing Module

## 概述

Testing 模組是 IBR 系統的開發測試工具模組，提供各種測試和開發輔助功能。此模組僅在開發環境中使用，不會包含在生產環境中。

## 功能元件

### 1. TestNotificationComponent（測試通知頁）
- **路徑**: `/ibr/test/test-notification`
- **功能**: 模擬外部系統發送匯款通知，用於測試各種情境
- **特色**:
  - 預設測試資料（一般、補件、大額、法人、錯誤、自訂）
  - JSON 編輯器與格式化功能
  - 一鍵複製測試資料
  - 根據資料類型自動導航到對應流程

## 使用說明

### 測試通知頁使用流程

1. **選擇測試資料類型**
   - 一般測試資料：標準個人匯款流程
   - 補件測試資料：姓名不符需要補件的情境
   - 大額匯款測試資料：接近個人匯款上限的測試
   - 法人測試資料：企業匯款流程測試
   - 錯誤測試資料：各種異常情況的測試
   - 自訂測試資料：自行定義測試資料內容

2. **編輯測試資料**
   - 使用內建 JSON 編輯器修改資料
   - 點擊「格式化」按鈕美化 JSON
   - 點擊「複製」按鈕複製到剪貼簿

3. **模擬接收通知**
   - 點擊「模擬接收通知」按鈕
   - 系統會根據資料內容自動導航到對應頁面：
     - 補件通知 → `/ibr/supplement/notification`
     - 法人通知 → `/ibr/corporate/landing`
     - 個人通知 → `/ibr/individual/external-entry`

## 技術架構

### 模組結構
```
testing/
├── testing.module.ts          # 模組定義
├── testing-routing.module.ts  # 路由配置
├── components/
│   └── test-notification/     # 測試通知元件
│       ├── test-notification.component.ts
│       ├── test-notification.component.html
│       └── test-notification.component.scss
└── README.md                  # 本檔案
```

### 依賴項目
- `MockNotificationService`: 來自 Individual 模組，用於儲存測試資料
- Angular Forms: 用於表單處理
- Angular Router: 用於頁面導航

## 開發指引

### 新增測試元件
1. 在 `components` 資料夾下建立新元件
2. 在 `testing-routing.module.ts` 中新增路由
3. 在 `testing.module.ts` 中宣告元件
4. 更新 `ibr-test.component.ts` 中的導航選單

### 測試資料格式
```typescript
interface TestNotificationData {
  notificationId: string;      // 通知編號（必填）
  remittanceType: string;      // 匯款類型 (INDIVIDUAL/CORPORATE)
  remitterId?: string;         // 匯款人身份證號
  corporateId?: string;        // 法人統一編號
  remitterName?: string;       // 匯款人姓名
  corporateName?: string;      // 法人名稱
  remittanceAmount: number;    // 匯款金額
  currency: string;            // 幣別
  purpose?: string;            // 匯款用途
  bankCode?: string;           // 銀行代碼
  accountNumber?: string;      // 帳號
  supplementReason?: string;   // 補件原因
  notificationDate: string;    // 通知日期
}
```

## 注意事項

1. **僅限開發環境使用**：此模組包含測試功能，不應在生產環境中啟用
2. **資料安全**：測試資料僅存在記憶體中，重新整理頁面會清除
3. **路由保護**：建議在生產環境的路由配置中移除此模組的載入

## 未來擴充計劃

- [ ] API 模擬工具
- [ ] 表單自動填充工具
- [ ] 錯誤情境模擬器
- [ ] 效能測試工具
- [ ] E2E 測試輔助工具