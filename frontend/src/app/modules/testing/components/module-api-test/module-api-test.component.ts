import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  status?: number;
}

@Component({
  selector: 'app-module-api-test',
  template: `
    <div class="module-test-container">
      <h1>KGI IBR 模組測試工具</h1>
      
      <!-- Repository 狀態 -->
      <div class="test-module">
        <h2>Repository 模式狀態</h2>
        <p class="description">檢查目前系統使用的資料儲存模式 (JPA資料庫 或 Memory記憶體)</p>
        <button (click)="checkRepositoryStatus()" class="btn btn-primary">檢查狀態</button>
        <div class="response" *ngIf="repositoryStatus">
          <pre>{{ repositoryStatus | json }}</pre>
        </div>
      </div>

      <!-- Notification Module -->
      <div class="test-module">
        <h2>1. Notification Module (通知模組)</h2>
        
        <div class="test-section">
          <h3>發送 Email 通知</h3>
          <div class="form-group">
            <input type="email" [(ngModel)]="emailNotification.recipient" placeholder="收件人 Email" class="form-control">
            <input type="text" [(ngModel)]="emailNotification.templateId" placeholder="模板 ID" class="form-control">
            <button (click)="sendEmailNotification()" class="btn btn-success">發送 Email</button>
          </div>
          <div class="response" *ngIf="emailResponse">
            <pre>{{ emailResponse | json }}</pre>
          </div>
        </div>
        
        <div class="test-section">
          <h3>發送 SMS 通知</h3>
          <div class="form-group">
            <input type="tel" [(ngModel)]="smsNotification.recipient" placeholder="手機號碼" class="form-control">
            <input type="text" [(ngModel)]="smsNotification.templateId" placeholder="模板 ID" class="form-control">
            <button (click)="sendSmsNotification()" class="btn btn-success">發送 SMS</button>
          </div>
          <div class="response" *ngIf="smsResponse">
            <pre>{{ smsResponse | json }}</pre>
          </div>
        </div>
        
        <div class="test-section">
          <h3>查詢通知狀態</h3>
          <p class="section-desc">查詢已發送通知的當前狀態 (待發送/已發送/失敗)</p>
          <div class="api-details">
            <h4>API 資訊：</h4>
            <ul>
              <li><strong>端點：</strong>GET /api/notification/status/{notificationId}</li>
              <li><strong>參數：</strong>notificationId (String) - 通知唯一識別碼，格式：NOTIF-{timestamp}-{random}</li>
            </ul>
            <h4>回應資料：</h4>
            <ul>
              <li><strong>notificationId</strong> (String) - 通知ID</li>
              <li><strong>channel</strong> (String) - 通道類型: EMAIL | SMS</li>
              <li><strong>recipient</strong> (String) - 收件人 (Email地址或手機號碼)</li>
              <li><strong>templateId</strong> (String) - 模板ID</li>
              <li><strong>status</strong> (String) - 狀態碼: PENDING | SENT | FAILED | NOT_FOUND</li>
              <li><strong>statusDescription</strong> (String) - 狀態說明</li>
              <li><strong>parameters</strong> (Map) - 模板參數</li>
              <li><strong>errorMessage</strong> (String) - 錯誤訊息 (僅失敗時)</li>
              <li><strong>retryCount</strong> (Integer) - 重試次數 (0-3)</li>
              <li><strong>createdAt</strong> (LocalDateTime) - 建立時間</li>
              <li><strong>sentAt</strong> (LocalDateTime) - 發送時間 (可能為空)</li>
              <li><strong>updatedAt</strong> (LocalDateTime) - 最後更新時間</li>
            </ul>
          </div>
          <div class="form-group">
            <input type="text" [(ngModel)]="notificationId" placeholder="通知 ID (從上方發送結果取得)" class="form-control">
            <button (click)="checkNotificationStatus()" class="btn btn-info">查詢狀態</button>
          </div>
          <div class="response" *ngIf="notificationStatusResponse">
            <pre>{{ notificationStatusResponse | json }}</pre>
          </div>
        </div>
      </div>

      <!-- OTP Module -->
      <div class="test-module">
        <h2>2. OTP Module (一次性密碼模組)</h2>
        <p class="description">測試 OTP 發送與驗證功能。系統會生成真實的 6 位數 OTP 碼，有效期限 5 分鐘。</p>
        
        <div class="test-section">
          <h3>發送 OTP</h3>
          <div class="form-group">
            <input type="text" [(ngModel)]="otpRequest.uniqId" placeholder="案件編號" class="form-control">
            <input type="tel" [(ngModel)]="otpRequest.phoneNumber" placeholder="手機號碼" class="form-control">
            <select [(ngModel)]="otpRequest.channel" class="form-control">
              <option value="SMS">SMS</option>
              <option value="EMAIL">EMAIL</option>
            </select>
            <button (click)="sendOtp()" class="btn btn-success">發送 OTP</button>
          </div>
          <div class="response" *ngIf="otpSendResponse">
            <pre>{{ otpSendResponse | json }}</pre>
          </div>
        </div>
        
        <div class="test-section">
          <h3>驗證 OTP</h3>
          <div class="form-group">
            <input type="text" [(ngModel)]="otpVerify.uniqId" placeholder="案件編號" class="form-control">
            <input type="text" [(ngModel)]="otpVerify.otp" placeholder="OTP 驗證碼" class="form-control">
            <input type="text" [(ngModel)]="otpVerify.sk" placeholder="Session Key" class="form-control">
            <button (click)="verifyOtp()" class="btn btn-info">驗證 OTP</button>
          </div>
          <div class="response" *ngIf="otpVerifyResponse">
            <pre>{{ otpVerifyResponse | json }}</pre>
          </div>
        </div>
      </div>

      <!-- PCode2566 Module -->
      <div class="test-module">
        <h2>3. PCode2566 Module (銀行帳戶驗證模組)</h2>
        <p class="description">測試銀行帳戶驗證功能。目前使用模擬資料，僅檢查格式正確性而非真實驗證。</p>
        
        <div class="test-section">
          <h3>驗證銀行帳戶</h3>
          <div class="form-group">
            <input type="text" [(ngModel)]="bankVerify.idno" placeholder="身分證號" class="form-control">
            <input type="text" [(ngModel)]="bankVerify.birthday" placeholder="生日 (YYYY-MM-DD)" class="form-control">
            <input type="tel" [(ngModel)]="bankVerify.phone" placeholder="手機號碼" class="form-control">
            <select [(ngModel)]="bankVerify.bank" class="form-control" (change)="onBankChange()">
              <option value="012">012 - 凱基銀行</option>
              <option value="822">822 - 中國信託</option>
              <option value="013">013 - 國泰世華</option>
            </select>
            <input type="text" [(ngModel)]="bankVerify.account" placeholder="銀行帳號" class="form-control">
            <button (click)="verifyBankAccount()" class="btn btn-warning">驗證帳戶</button>
          </div>
          <div class="response" *ngIf="bankVerifyResponse">
            <pre>{{ bankVerifyResponse | json }}</pre>
          </div>
        </div>
      </div>

      <!-- API 測試統計 -->
      <div class="test-module">
        <h2>API 測試統計</h2>
        <p class="description">記錄本次測試過程中所有 API 呼叫的成功與失敗次數，幫助追蹤測試覆蓋率和錯誤率。</p>
        <div class="stats">
          <p>總測試次數: {{ testStats.total }}</p>
          <p>成功: <span class="success">{{ testStats.success }}</span></p>
          <p>失敗: <span class="error">{{ testStats.failed }}</span></p>
        </div>
        <button (click)="resetStats()" class="btn btn-secondary">重置統計</button>
      </div>
    </div>
  `,
  styles: [`
    .module-test-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    h1 {
      color: #0044ad;
      text-align: center;
      margin-bottom: 30px;
    }

    .test-module {
      background: white;
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    h2 {
      color: #333;
      border-bottom: 2px solid #0044ad;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }

    .test-section {
      margin: 15px 0;
      padding: 15px;
      background: #f9f9f9;
      border-radius: 5px;
    }

    h3 {
      color: #666;
      margin-bottom: 15px;
    }
    
    .description {
      color: #666;
      font-size: 14px;
      margin-bottom: 15px;
      font-style: italic;
    }
    
    .section-desc {
      color: #888;
      font-size: 13px;
      margin-bottom: 10px;
    }
    
    .api-details {
      background: #f0f4f8;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #17a2b8;
    }
    
    .api-details h4 {
      color: #17a2b8;
      font-size: 14px;
      margin-bottom: 10px;
      font-weight: 600;
    }
    
    .api-details ul {
      margin: 0;
      padding-left: 20px;
    }
    
    .api-details li {
      font-size: 13px;
      line-height: 1.8;
      color: #555;
    }
    
    .api-details strong {
      color: #333;
    }

    .form-group {
      display: flex;
      gap: 10px;
      margin-bottom: 10px;
      flex-wrap: wrap;
    }

    .form-control {
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      flex: 1;
      min-width: 200px;
    }

    .btn {
      padding: 8px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-success:hover {
      background: #218838;
    }

    .btn-info {
      background: #17a2b8;
      color: white;
    }

    .btn-info:hover {
      background: #138496;
    }

    .btn-warning {
      background: #ffc107;
      color: #212529;
    }

    .btn-warning:hover {
      background: #e0a800;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .response {
      background: #f8f9fa;
      padding: 15px;
      margin-top: 15px;
      border-radius: 5px;
      border: 1px solid #dee2e6;
      font-family: monospace;
      max-height: 300px;
      overflow-y: auto;
    }

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .stats {
      display: flex;
      gap: 30px;
      margin-bottom: 20px;
    }

    .stats p {
      margin: 0;
      font-size: 16px;
    }

    .success {
      color: #28a745;
      font-weight: bold;
    }

    .error {
      color: #dc3545;
      font-weight: bold;
    }

    @media (max-width: 768px) {
      .form-group {
        flex-direction: column;
      }

      .form-control {
        min-width: 100%;
      }

      .stats {
        flex-direction: column;
        gap: 10px;
      }
    }
  `]
})
export class ModuleApiTestComponent implements OnInit {
  
  // API Base URL
  private apiUrl = 'http://localhost:8080';
  
  // Repository 狀態
  repositoryStatus: any = null;
  
  // Notification Module
  emailNotification = {
    recipient: '<EMAIL>',
    templateId: 'REMITTANCE_NOTIFICATION'
  };
  
  smsNotification = {
    recipient: '**********',
    templateId: 'OTP_SMS'
  };
  
  notificationId = '1';
  emailResponse: any = null;
  smsResponse: any = null;
  notificationStatusResponse: any = null;
  
  // OTP Module
  otpRequest = {
    uniqId: 'TEST-OTP-001',
    phoneNumber: '**********',
    channel: 'SMS'
  };
  
  otpVerify = {
    uniqId: 'TEST-OTP-001',
    otp: '123456',
    sk: 'session-key-123'
  };
  
  otpSendResponse: any = null;
  otpVerifyResponse: any = null;
  
  // PCode2566 Module
  bankVerify = {
    idno: 'A123456789',
    birthday: '1990-01-01',
    phone: '**********',
    bank: '012',
    bankName: '凱基銀行',
    account: '****************'
  };
  
  bankVerifyResponse: any = null;
  
  // 測試統計
  testStats = {
    total: 0,
    success: 0,
    failed: 0
  };
  
  constructor(private http: HttpClient) {}
  
  ngOnInit(): void {
    this.checkRepositoryStatus();
  }
  
  // Repository 狀態檢查
  async checkRepositoryStatus() {
    try {
      const result = await this.makeRequest('/api/test/repository/status', 'GET');
      if (result.success) {
        this.repositoryStatus = result.data;
      } else {
        this.repositoryStatus = { 
          error: result.error || 'Failed to fetch repository status',
          status: result.status,
          message: '無法連接到後端服務，請確認服務已啟動'
        };
      }
      this.updateStats(result.success);
    } catch (error: any) {
      console.error('Repository status check failed:', error);
      this.repositoryStatus = { 
        error: error.message || 'Unknown error',
        message: '請求失敗，請檢查後端服務是否在 http://localhost:8080 運行'
      };
      this.updateStats(false);
    }
  }
  
  // Notification Module 測試
  async sendEmailNotification() {
    const body = {
      channel: 'EMAIL',
      recipient: this.emailNotification.recipient,
      templateId: this.emailNotification.templateId,
      parameters: {
        customerName: '測試用戶',
        amount: '50000',
        currency: 'USD',
        transactionId: 'TXN-' + Date.now()
      },
      uniqId: 'TEST-' + Date.now(),
      uniqType: 'REMITTANCE',
      async: true
    };
    
    try {
      const result = await this.makeRequest('/api/notification/send', 'POST', body);
      this.emailResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.emailResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  async sendSmsNotification() {
    const body = {
      channel: 'SMS',
      recipient: this.smsNotification.recipient,
      templateId: this.smsNotification.templateId,
      parameters: {
        otpCode: '123456'  // 修正參數名稱為 otpCode
      },
      uniqId: 'TEST-' + Date.now(),
      uniqType: 'OTP',
      async: false
    };
    
    try {
      const result = await this.makeRequest('/api/notification/send', 'POST', body);
      this.smsResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.smsResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  async checkNotificationStatus() {
    try {
      const result = await this.makeRequest(`/api/notification/status/${this.notificationId}`, 'GET');
      this.notificationStatusResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.notificationStatusResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  // OTP Module 測試
  async sendOtp() {
    const body = {
      uniqId: this.otpRequest.uniqId,
      email: '<EMAIL>',
      usePhone: 'Phone',
      phoneNumber: this.otpRequest.phoneNumber,
      uniqType: 'INDIVIDUAL',
      channel: this.otpRequest.channel
    };
    
    try {
      const result = await this.makeRequest('/api/otp/send', 'POST', body);
      this.otpSendResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.otpSendResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  async verifyOtp() {
    const body = {
      uniqId: this.otpVerify.uniqId,
      sk: this.otpVerify.sk,
      txnId: 'TXN-' + Date.now(),
      txnDate: new Date().toISOString().split('T')[0],
      otp: this.otpVerify.otp
    };
    
    try {
      const result = await this.makeRequest('/api/otp/verify', 'POST', body);
      this.otpVerifyResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.otpVerifyResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  // PCode2566 Module 測試
  async verifyBankAccount() {
    const body = {
      phone: this.bankVerify.phone,
      bank: this.bankVerify.bank,
      bankName: this.bankVerify.bankName,
      account: this.bankVerify.account,
      branchId: this.bankVerify.bank + '6',
      idno: this.bankVerify.idno,
      birthday: this.bankVerify.birthday,
      uniqId: 'TEST-PCODE-' + Date.now(),
      uniqType: 'INDIVIDUAL',
      tmnlId: 'TERM001',
      tmnlType: 'WEB'
    };
    
    try {
      const result = await this.makeRequest('/api/pcode2566/verify', 'POST', body);
      this.bankVerifyResponse = result.data;
      this.updateStats(result.success);
    } catch (error) {
      this.bankVerifyResponse = { error: error.message };
      this.updateStats(false);
    }
  }
  
  // 銀行選擇變更
  onBankChange() {
    const bankMap = {
      '012': '凱基銀行',
      '822': '中國信託',
      '013': '國泰世華'
    };
    this.bankVerify.bankName = bankMap[this.bankVerify.bank];
  }
  
  // 通用請求函數
  private async makeRequest(url: string, method: string = 'GET', body: any = null): Promise<TestResult> {
    try {
      const options: any = {
        observe: 'response',
        responseType: 'json'
      };
      
      let response: any;
      if (method === 'GET') {
        response = await this.http.get(this.apiUrl + url, options).toPromise();
      } else if (method === 'POST') {
        response = await this.http.post(this.apiUrl + url, body, options).toPromise();
      }
      
      return {
        success: response.status >= 200 && response.status < 300,
        data: response.body,
        status: response.status
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Request failed',
        status: error.status
      };
    }
  }
  
  // 更新統計
  private updateStats(success: boolean) {
    this.testStats.total++;
    if (success) {
      this.testStats.success++;
    } else {
      this.testStats.failed++;
    }
  }
  
  // 重置統計
  resetStats() {
    this.testStats = {
      total: 0,
      success: 0,
      failed: 0
    };
  }
}