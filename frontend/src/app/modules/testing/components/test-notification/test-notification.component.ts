import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MockNotificationService } from '../../../individual/services/mock-notification.service';

/**
 * IBR 測試通知頁面元件
 * 用於模擬外部通知並填入測試資料
 */
@Component({
  selector: 'app-test-notification',
  templateUrl: './test-notification.component.html',
  styleUrls: ['./test-notification.component.scss']
})
export class TestNotificationComponent implements OnInit {
  
  // 測試資料類型
  testDataTypes = [
    { value: 'normal', label: '一般測試資料' },
    { value: 'supplement', label: '補件測試資料' },
    { value: 'large', label: '大額匯款測試資料' },
    { value: 'corporate', label: '法人測試資料' },
    { value: 'error', label: '錯誤測試資料' },
    { value: 'custom', label: '自訂測試資料' }
  ];
  
  selectedDataType = 'normal';
  testDataJson = '';
  
  // 預設測試資料 (符合 TO API 格式)
  private defaultTestData = {
    normal: {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812345',
      PayerName: 'Shiang Ru',
      PayerCountry: 'US',
      Currency: 'USD',
      Amount: '5000.00',
      PayeeEngName: 'Qing Lan, Wang',
      PayeeName: '王清蘭',
      PayeeID: 'A********9',
      PayeeAccount: '****************',
      PayeeBankCode: '0080016',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '410',
      WhileFlag: '',
      Memo: 'Monthly Salary'
    },
    supplement: {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812346',
      PayerName: 'MARY JOHNSON',
      PayerCountry: 'UK',
      Currency: 'GBP',
      Amount: '3000.00',
      PayeeEngName: 'CHEN MEI LING',
      PayeeName: '陳美玲',
      PayeeID: 'B234567890',
      PayeeAccount: '****************',
      PayeeBankCode: '0120034',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '510',
      WhileFlag: 'S',
      Memo: 'Family support',
      SupplementNo: 'IBRSUP2024060800001'
    },
    large: {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812347',
      PayerName: 'GLOBAL TRADING LLC',
      PayerCountry: 'SG',
      Currency: 'USD',
      Amount: '450000.00',
      PayeeEngName: 'LIN DA WEI',
      PayeeName: '林大偉',
      PayeeID: 'B234567890',
      PayeeAccount: '****************',
      PayeeBankCode: '0120034',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '001',
      WhileFlag: '',
      Memo: 'Business transaction - Invoice #2024-001'
    },
    corporate: {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812348',
      PayerName: 'ABC CORPORATION',
      PayerCountry: 'JP',
      Currency: 'USD',
      Amount: '250000.00',
      PayeeEngName: 'KGI TECHNOLOGY CO LTD',
      PayeeName: '凱基科技股份有限公司',
      PayeeID: '********',
      PayeeAccount: '****************',
      PayeeBankCode: '0090012',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '002',
      WhileFlag: '',
      Memo: 'Equipment Purchase Order PO-2024-0608'
    },
    error: {
      TheirRefNo: '',
      RemitRefNo: '',
      PayerName: '',
      PayerCountry: '',
      Currency: '',
      Amount: '',
      PayeeEngName: '',
      PayeeName: '',
      PayeeID: '',
      PayeeAccount: '',
      PayeeBankCode: '',
      PayeeTel: '',
      PayeeMail: '',
      SourceOfFund: '',
      WhileFlag: '',
      Memo: ''
    },
    custom: {}
  };
  
  constructor(
    private router: Router,
    private mockNotificationService: MockNotificationService
  ) {}
  
  ngOnInit(): void {
    // 載入預設測試資料
    this.loadTestData();
  }
  
  /**
   * 載入測試資料
   */
  loadTestData(): void {
    const testData = this.defaultTestData[this.selectedDataType] || {};
    this.testDataJson = JSON.stringify(testData, null, 2);
    console.log('TestNotification: 載入測試資料:', {
      selectedDataType: this.selectedDataType,
      testData: testData,
      testDataJson: this.testDataJson
    });
  }
  
  /**
   * 當測試資料類型改變時
   */
  onDataTypeChange(): void {
    console.log('TestNotification: 資料類型改變為:', this.selectedDataType);
    if (this.selectedDataType !== 'custom') {
      this.loadTestData();
    } else {
      // 自訂模式時清空資料
      this.testDataJson = JSON.stringify({}, null, 2);
    }
  }
  
  /**
   * 模擬接收通知
   */
  simulateNotification(): void {
    try {
      // 解析 JSON 資料
      const notificationData = JSON.parse(this.testDataJson);
      
      // 驗證必要欄位 (TO API 格式)
      if (!notificationData.RemitRefNo) {
        alert('錯誤：缺少匯入匯款編號 (RemitRefNo)');
        return;
      }
      
      if (!notificationData.TheirRefNo) {
        alert('錯誤：缺少跨境平台編號 (TheirRefNo)');
        return;
      }
      
      // 除錯：顯示要設定的資料
      console.log('TestNotification: 準備設定通知資料:', {
        RemitRefNo: notificationData.RemitRefNo,
        PayeeID: notificationData.PayeeID,
        PayeeIDLength: notificationData.PayeeID ? notificationData.PayeeID.length : 'null',
        WhileFlag: notificationData.WhileFlag,
        selectedDataType: this.selectedDataType,
        fullData: notificationData
      });
      
      // 直接使用當前編輯器中的資料，確保用戶的修改會被使用
      this.mockNotificationService.setNotificationData(notificationData);
      
      console.log('模擬通知已設定:', notificationData);
      
      // 統一導航到 external-entry 頁面，由該頁面判斷後續流程
      this.router.navigate(['/ibr/test/external-entry'], {
        queryParams: {
          remitrefno: notificationData.RemitRefNo,
          theirrefno: notificationData.TheirRefNo
        }
      });
      
    } catch (error) {
      alert('JSON 格式錯誤：' + error.message);
    }
  }
  
  /**
   * 重置測試資料
   */
  resetTestData(): void {
    this.selectedDataType = 'normal';
    this.loadTestData();
  }
  
  /**
   * 格式化 JSON
   */
  formatJson(): void {
    try {
      const jsonObj = JSON.parse(this.testDataJson);
      this.testDataJson = JSON.stringify(jsonObj, null, 2);
    } catch (error) {
      alert('JSON 格式錯誤：無法格式化');
    }
  }
  
  /**
   * 複製到剪貼簿
   */
  copyToClipboard(): void {
    navigator.clipboard.writeText(this.testDataJson).then(() => {
      alert('已複製到剪貼簿！');
    }).catch(err => {
      alert('複製失敗：' + err);
    });
  }
}