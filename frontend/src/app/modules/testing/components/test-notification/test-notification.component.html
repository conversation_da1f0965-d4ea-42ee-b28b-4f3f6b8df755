<div class="test-notification-container">
  <!-- 頁面標題 -->
  <div class="page-header">
    <h1>IBR 測試通知頁面</h1>
    <p>模擬外部系統發送匯款通知，用於測試各種情境</p>
  </div>
  
  <!-- 測試資料控制區 -->
  <div class="control-section">
    <div class="control-header">
      <h2>測試資料設定</h2>
      <div class="control-buttons">
        <button class="btn btn-secondary" (click)="formatJson()">
          <i class="bi bi-code-slash"></i> 格式化
        </button>
        <button class="btn btn-secondary" (click)="copyToClipboard()">
          <i class="bi bi-clipboard"></i> 複製
        </button>
        <button class="btn btn-warning" (click)="resetTestData()">
          <i class="bi bi-arrow-clockwise"></i> 重置
        </button>
      </div>
    </div>
    
    <!-- 測試資料類型選擇 -->
    <div class="data-type-selector">
      <label>選擇測試資料類型：</label>
      <select 
        class="form-select" 
        [(ngModel)]="selectedDataType" 
        (change)="onDataTypeChange()">
        <option *ngFor="let type of testDataTypes" [value]="type.value">
          {{ type.label }}
        </option>
      </select>
    </div>
    
    <!-- JSON 編輯器 -->
    <div class="json-editor">
      <label>測試資料 JSON：</label>
      <textarea 
        class="form-control json-textarea" 
        [(ngModel)]="testDataJson"
        rows="20"
        placeholder="請輸入測試資料 JSON...">
      </textarea>
    </div>
    
    <!-- 說明文字 -->
    <div class="help-text">
      <h4>TO 數位解款 API 欄位說明 (符合 API v1.3 規格)：</h4>
      <ul>
        <li><strong>TheirRefNo</strong>: 跨境平台編號（必填，長度 20）</li>
        <li><strong>RemitRefNo</strong>: 匯入匯款編號（必填，長度 16，來自資通）</li>
        <li><strong>PayerName</strong>: 匯款人英文名（必填，長度 70）</li>
        <li><strong>PayerCountry</strong>: 匯款國別（長度 2，如: US, UK, JP）</li>
        <li><strong>Currency</strong>: 匯款幣別（必填，長度 3，如: USD, EUR, JPY）</li>
        <li><strong>Amount</strong>: 匯款金額（必填，Decimal 17,2）</li>
        <li><strong>PayeeEngName</strong>: 收款人英文姓名（必填，長度 70）</li>
        <li><strong>PayeeName</strong>: 收款人中文名稱（必填，長度 70）</li>
        <li><strong>PayeeID</strong>: 收款人ID（必填，長度 16，身分證號或統一編號）</li>
        <li><strong>PayeeAccount</strong>: 收款人帳號（必填，長度 35）</li>
        <li><strong>PayeeBankCode</strong>: 收款行代碼（必填，長度 7）</li>
        <li><strong>PayeeTel</strong>: 收款人電話/手機（必填，長度 20）</li>
        <li><strong>PayeeMail</strong>: 收款人信箱（必填，長度 40）</li>
        <li><strong>SourceOfFund</strong>: 匯款性質（必填，長度 3，如: 410=薪資, 001=貨物貿易）</li>
        <li><strong>WhileFlag</strong>: 白名單標記（長度 1，值=S時為補件通訊用）</li>
        <li><strong>Memo</strong>: 附言（長度 40）</li>
      </ul>
      
      <div class="alert alert-info mt-3">
        <strong>重要提示：</strong>
        <ul class="mb-0">
          <li>WhileFlag = 'S' 時，系統會導向補件流程</li>
          <li>PayeeID 長度為 8 碼時，系統判定為法人（統一編號）</li>
          <li>PayeeID 長度為 10 碼時，系統判定為個人（身分證號）</li>
          <li>所有欄位名稱使用大寫開頭的駝峰命名（PascalCase）</li>
        </ul>
      </div>
    </div>
  </div>
  
  <!-- 執行按鈕 -->
  <div class="action-section">
    <button class="btn btn-primary btn-lg" (click)="simulateNotification()">
      <i class="bi bi-send"></i> 模擬接收通知
    </button>
  </div>
  
  <!-- 測試情境說明 -->
  <div class="scenario-section">
    <h3>測試情境說明</h3>
    <div class="scenario-grid">
      <div class="scenario-card">
        <h4>🧑 一般測試資料 (個人)</h4>
        <p><strong>情境：</strong>標準個人匯款流程</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>PayeeID: 10碼身分證號 (A123456789)</li>
          <li>Amount: 5,000 USD (限額內)</li>
          <li>SourceOfFund: 410 (薪資款)</li>
          <li>WhileFlag: 空白</li>
        </ul>
        <p><strong>導向：</strong>/ibr/individual/external-entry</p>
      </div>
      <div class="scenario-card">
        <h4>📋 補件測試資料</h4>
        <p><strong>情境：</strong>姓名不符需要補件</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>WhileFlag: S (補件標記)</li>
          <li>只有姓名和編號，其他欄位空白</li>
          <li>用於測試補件流程</li>
        </ul>
        <p><strong>導向：</strong>/ibr/supplement/notification</p>
      </div>
      <div class="scenario-card">
        <h4>💰 大額匯款測試資料</h4>
        <p><strong>情境：</strong>接近個人限額測試</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>Amount: 450,000 USD</li>
          <li>SourceOfFund: 001 (貨物貿易)</li>
          <li>測試大額匯款提醒</li>
          <li>手續費計算驗證</li>
        </ul>
        <p><strong>導向：</strong>/ibr/individual/external-entry</p>
      </div>
      <div class="scenario-card">
        <h4>🏢 法人測試資料 (企業)</h4>
        <p><strong>情境：</strong>企業匯款流程</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>PayeeID: 8碼統編 (12345678)</li>
          <li>Amount: 250,000 USD</li>
          <li>SourceOfFund: 002 (服務貿易)</li>
          <li>需要工商憑證驗證</li>
        </ul>
        <p><strong>導向：</strong>/ibr/corporate/landing</p>
      </div>
      <div class="scenario-card">
        <h4>❌ 錯誤測試資料</h4>
        <p><strong>情境：</strong>異常狀況處理</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>缺少必要欄位</li>
          <li>測試錯誤處理機制</li>
          <li>驗證前端防護</li>
        </ul>
        <p><strong>預期：</strong>顯示錯誤訊息</p>
      </div>
      <div class="scenario-card">
        <h4>✏️ 自訂測試資料</h4>
        <p><strong>情境：</strong>客製化測試</p>
        <p><strong>特徵：</strong></p>
        <ul>
          <li>可自由編輯 JSON</li>
          <li>測試特殊情境</li>
          <li>支援各種欄位組合</li>
        </ul>
        <p><strong>注意：</strong>請確保必填欄位完整</p>
      </div>
    </div>
  </div>
  
  <!-- API 流程說明 -->
  <div class="api-flow-section mt-4">
    <h3>API 資料流程</h3>
    <div class="alert alert-warning">
      <h5>📡 TO 數位解款 API (Step 0)</h5>
      <p>此頁面模擬外部系統（Thunes）發送初始通知到凱基銀行系統。這是整個解款流程的起點。</p>
      <ol>
        <li>外部系統呼叫 <code>POST /notification</code> 發送匯款通知</li>
        <li>系統根據 PayeeID 長度判斷個人(10碼)或法人(8碼)</li>
        <li>系統根據 WhileFlag='S' 判斷是否為補件</li>
        <li>導向對應的處理流程頁面</li>
      </ol>
    </div>
    
    <div class="alert alert-success mt-3">
      <h5>📤 FROM 數位解款 API (Final Step)</h5>
      <p>當用戶完成所有解款步驟後，系統會呼叫 <code>POST /transaction/releaseinfo/update</code> 將完整資料回傳給外部系統。</p>
      <p><strong>額外欄位包括：</strong>英文姓名確認、居留證日期、出生日期、匯款性質詳情、數位簽章、解款日期等。</p>
    </div>
  </div>
</div>