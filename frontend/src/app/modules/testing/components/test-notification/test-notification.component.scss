.test-notification-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

// 頁面標題
.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, #0044ad, #0066cc);
  color: white;
  border-radius: 12px;
  
  h1 {
    margin: 0 0 10px 0;
    font-size: 2.5rem;
    font-weight: 300;
  }
  
  p {
    margin: 0;
    font-size: 1.2rem;
    opacity: 0.9;
  }
}

// 控制區域
.control-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  
  .control-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #333;
      font-size: 1.5rem;
    }
    
    .control-buttons {
      display: flex;
      gap: 10px;
    }
  }
}

// 資料類型選擇器
.data-type-selector {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
  }
  
  .form-select {
    width: 100%;
    max-width: 400px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      border-color: #0044ad;
      box-shadow: 0 0 0 0.2rem rgba(0, 68, 173, 0.25);
    }
  }
}

// JSON 編輯器
.json-editor {
  margin-bottom: 20px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
  }
  
  .json-textarea {
    width: 100%;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    resize: vertical;
    
    &:focus {
      background-color: white;
      border-color: #0044ad;
      box-shadow: 0 0 0 0.2rem rgba(0, 68, 173, 0.25);
    }
  }
}

// 說明文字
.help-text {
  background: #f8f9fa;
  border-left: 4px solid #0044ad;
  padding: 20px;
  border-radius: 8px;
  
  h4 {
    margin: 0 0 15px 0;
    color: #0044ad;
    font-size: 1.1rem;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;
      color: #666;
      
      strong {
        color: #333;
        font-weight: 600;
      }
    }
  }
}

// 執行按鈕區域
.action-section {
  text-align: center;
  margin: 40px 0;
  
  .btn-lg {
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    
    i {
      font-size: 1.3rem;
    }
  }
}

// 測試情境說明
.scenario-section {
  margin-top: 50px;
  
  h3 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 1.8rem;
  }
  
  .scenario-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .scenario-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #0044ad;
      box-shadow: 0 4px 15px rgba(0, 68, 173, 0.1);
      transform: translateY(-2px);
    }
    
    h4 {
      margin: 0 0 15px 0;
      color: #0044ad;
      font-size: 1.3rem;
    }
    
    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }
}

// 按鈕樣式
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  
  &.btn-primary {
    background: #0044ad;
    color: white;
    
    &:hover {
      background: #003399;
    }
  }
  
  &.btn-secondary {
    background: #6c757d;
    color: white;
    
    &:hover {
      background: #545b62;
    }
  }
  
  &.btn-warning {
    background: #ffc107;
    color: #212529;
    
    &:hover {
      background: #e0a800;
    }
  }
  
  i {
    font-size: 1.1em;
  }
}

// 響應式設計
@media (max-width: 768px) {
  .page-header {
    padding: 20px;
    
    h1 {
      font-size: 2rem;
    }
    
    p {
      font-size: 1rem;
    }
  }
  
  .control-section {
    padding: 20px;
    
    .control-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
      
      .control-buttons {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
  
  .json-editor {
    .json-textarea {
      font-size: 12px;
    }
  }
  
  .action-section {
    .btn-lg {
      width: 100%;
      justify-content: center;
    }
  }
  
  .scenario-section {
    .scenario-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 480px) {
  .test-notification-container {
    padding: 10px;
  }
  
  .control-buttons {
    flex-wrap: wrap;
    
    .btn {
      flex: 1;
      min-width: 100px;
    }
  }
}