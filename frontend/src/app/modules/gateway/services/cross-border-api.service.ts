import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 跨境API閘道服務
 * 對應後端 CrossBorderApiController
 * 
 * 根據URD需求：
 * 1. 接收跨境平台TO API資料
 * 2. 回傳FROM API資料給跨境平台
 * 3. 處理補件FROM API
 * 4. 案件資料查詢
 */

export interface ToApiData {
  TheirRefNo: string;           // 跨境平台編號
  RemitRefNo: string;           // 匯入匯款編號
  payername: string;            // 匯款英文名
  Currency: string;             // 匯款幣別
  Amount: number;               // 匯款金額
  PayeeEngName: string;         // 英文姓名
  PayeeName: string;            // 收款人中文名稱
  PayeeID: string;              // 收款人ID (身分證號/統編)
  PayeeAccount: string;         // 收款人帳號
  PayeeBankCode: string;        // 收款行代碼
  PayeeTel: string;             // 收款人電話/手機
  PayeeMail: string;            // 收款人信箱
  SourceOfFund: string;         // 匯款性質
  WhileFlag: string;            // 白名單 (補通訊用)
}

export interface CustomerUrls {
  applicationUrl: string;        // 申請連結 (type=A)
  supplementUrl: string;         // 補件連結 (type=S)
  queryUrl: string;             // 查詢連結 (type=Q)
}

export interface GatewayApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class CrossBorderApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/ibr/gateway`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2;

  constructor(private http: HttpClient) {}

  /**
   * 接收跨境平台TO API資料
   * 對應後端: CrossBorderApiController.receiveToApi()
   */
  receiveToApi(toApiData: ToApiData): Observable<GatewayApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<GatewayApiResponse<any>>(
      `${this.apiUrl}/receive-to-api`,
      toApiData,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 發送FROM API資料給跨境平台
   * 對應後端: CrossBorderApiController.sendFromApi()
   */
  sendFromApi(caseNo: string, applicationId: string): Observable<GatewayApiResponse<any>> {
    const headers = this.getHeaders();
    const requestData = { caseNo, applicationId };
    
    return this.http.post<GatewayApiResponse<any>>(
      `${this.apiUrl}/send-from-api`,
      requestData,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 發送補件FROM API資料給跨境平台
   * 對應後端: CrossBorderApiController.sendSupplementFromApi()
   */
  sendSupplementFromApi(caseNo: string, supplementId: string): Observable<GatewayApiResponse<any>> {
    const headers = this.getHeaders();
    const requestData = { caseNo, supplementId };
    
    return this.http.post<GatewayApiResponse<any>>(
      `${this.apiUrl}/send-supplement-from-api`,
      requestData,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢案件資料
   * 對應後端: CrossBorderApiController.getCaseData()
   */
  getCaseData(caseNo: string): Observable<GatewayApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<GatewayApiResponse<any>>(
      `${this.apiUrl}/case/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證TO API資料格式
   */
  validateToApiData(data: Partial<ToApiData>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const requiredFields: (keyof ToApiData)[] = [
      'TheirRefNo', 'RemitRefNo', 'payername', 'Currency', 'Amount',
      'PayeeEngName', 'PayeeName', 'PayeeID', 'PayeeAccount', 
      'PayeeBankCode', 'PayeeTel', 'PayeeMail', 'SourceOfFund', 'WhileFlag'
    ];

    requiredFields.forEach(field => {
      if (!data[field] || data[field] === '') {
        errors.push(`必要欄位缺失: ${field}`);
      }
    });

    // 金額驗證
    if (data.Amount && (typeof data.Amount !== 'number' || data.Amount <= 0)) {
      errors.push('金額必須大於0');
    }

    // PayeeID格式驗證
    if (data.PayeeID) {
      if (!this.isValidPayeeId(data.PayeeID)) {
        errors.push('PayeeID格式不正確 (應為10位身分證號或8位統編)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 確定客戶類型
   */
  determineCustomerType(payeeId: string): 'INDIVIDUAL' | 'CORPORATE' | null {
    if (!payeeId) return null;
    
    // 自然人: 身分證字號 (10位，第一位英文)
    if (payeeId.length === 10 && /^[A-Z][12][0-9]{8}$/.test(payeeId)) {
      return 'INDIVIDUAL';
    }
    // 法人: 統一編號 (8位數字)
    else if (payeeId.length === 8 && /^\d{8}$/.test(payeeId)) {
      return 'CORPORATE';
    }
    
    return null;
  }

  /**
   * 生成客戶URL (模擬後端邏輯)
   */
  generateCustomerUrls(caseNo: string, customerType: 'INDIVIDUAL' | 'CORPORATE'): CustomerUrls {
    const baseUrl = window.location.origin;
    const moduleType = customerType === 'INDIVIDUAL' ? 'individual' : 'corporate';
    
    return {
      applicationUrl: `${baseUrl}/ibr/${moduleType}?type=A&case=${caseNo}`,
      supplementUrl: `${baseUrl}/ibr/${moduleType}?type=S&case=${caseNo}`,
      queryUrl: `${baseUrl}/ibr/query?type=Q&case=${caseNo}`
    };
  }

  /**
   * 解析URL參數
   */
  parseUrlParams(url: string): { type: string | null; caseNo: string | null } {
    try {
      const urlObj = new URL(url);
      return {
        type: urlObj.searchParams.get('type'),
        caseNo: urlObj.searchParams.get('case')
      };
    } catch {
      return { type: null, caseNo: null };
    }
  }

  /**
   * 驗證URL參數
   */
  validateUrlParams(type: string | null, caseNo: string | null): { isValid: boolean; error?: string } {
    if (!type || !['A', 'S', 'Q'].includes(type)) {
      return { isValid: false, error: '無效的流程類型 (必須是A、S或Q)' };
    }
    
    if (!caseNo || !caseNo.startsWith('IBR')) {
      return { isValid: false, error: '無效的案件編號格式' };
    }
    
    return { isValid: true };
  }

  // ==================== 私有方法 ====================

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  private isValidPayeeId(payeeId: string): boolean {
    // 身分證號: 10位，第一位英文，第二位1或2，後8位數字
    const taiwanIdPattern = /^[A-Z][12][0-9]{8}$/;
    // 統一編號: 8位數字
    const unifiedNumberPattern = /^\d{8}$/;
    
    return taiwanIdPattern.test(payeeId) || unifiedNumberPattern.test(payeeId);
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '跨境API服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = 'API資料格式錯誤或不完整';
          errorCode = 'INVALID_API_DATA';
          break;
        case 401:
          errorMessage = 'API權限驗證失敗';
          errorCode = 'AUTHENTICATION_FAILED';
          break;
        case 403:
          errorMessage = '沒有權限呼叫此API';
          errorCode = 'INSUFFICIENT_PERMISSIONS';
          break;
        case 404:
          errorMessage = '找不到指定的案件資料';
          errorCode = 'CASE_NOT_FOUND';
          break;
        case 408:
          errorMessage = 'API呼叫超時，請稍後再試';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 500:
          errorMessage = '跨境API服務內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 503:
          errorMessage = '跨境API服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        default:
          errorMessage = `跨境API服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('跨境API服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError(() => ({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    }));
  }
}