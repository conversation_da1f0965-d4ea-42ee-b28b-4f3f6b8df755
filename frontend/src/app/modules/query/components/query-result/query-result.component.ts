import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { RemittanceQueryService } from '../../services/remittance-query.service';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';

interface RemittanceRecord {
  id: string;
  type: string;
  date: string;
  amount: number;
  currency: string;
  status: string;
  senderName?: string;
  senderBank?: string;
  reference?: string;
  expanded?: boolean;
}

interface MonthGroup {
  month: string;
  dateRange: string;
  records: RemittanceRecord[];
}

@Component({
  selector: 'app-query-result',
  templateUrl: './query-result.component.html',
  styleUrls: ['./query-result.component.scss']
})
export class QueryResultComponent implements OnInit {
  loading = false;
  error: string | null = null;
  
  // 篩選選項
  timeRanges = [
    { value: '1w', label: '近1週' },
    { value: '1m', label: '近1個月' },
    { value: '3m', label: '近3個月' },
    { value: '6m', label: '近6個月' },
    { value: '1y', label: '近1年' }
  ];
  
  transactionTypes = [
    { value: 'all', label: '交易類型' },
    { value: 'inbound', label: '匯入匯款' },
    { value: 'outbound', label: '匯出匯款' }
  ];
  
  selectedTimeRange = '1w';
  selectedType = 'all';
  
  // 查詢結果
  monthGroups: MonthGroup[] = [];
  totalRecords = 0;
  
  constructor(
    private queryService: RemittanceQueryService,
    private router: Router,
    private slideDialogService: SlideDialogService
  ) {}
  
  ngOnInit(): void {
    this.loadQueryResults();
  }
  
  /**
   * 載入查詢結果
   */
  private loadQueryResults(): void {
    this.loading = true;
    this.error = null;
    
    this.queryService.getQueryResults().subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success && response.data) {
          this.processResults(response.data);
        } else {
          this.error = response.message || '無法載入查詢結果';
        }
      },
      error: (error) => {
        this.loading = false;
        this.error = error.message || '查詢失敗，請稍後再試';
      }
    });
  }
  
  /**
   * 處理查詢結果，按月份分組
   */
  private processResults(data: {
    records?: RemittanceRecord[];
    [key: string]: unknown;
  }): void {
    // 模擬資料處理
    const records: RemittanceRecord[] = data.records || this.getMockData();
    
    // 按月份分組
    const groups = new Map<string, RemittanceRecord[]>();
    
    records.forEach(record => {
      const date = new Date(record.date);
      const monthKey = `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月`;
      
      if (!groups.has(monthKey)) {
        groups.set(monthKey, []);
      }
      groups.get(monthKey)!.push(record);
    });
    
    // 轉換為月份群組陣列
    this.monthGroups = Array.from(groups.entries()).map(([month, records]) => {
      // 計算日期範圍
      const dates = records.map(r => new Date(r.date));
      const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
      const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
      
      return {
        month,
        dateRange: `${this.formatDate(minDate)}-${this.formatDate(maxDate)}`,
        records: records.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      };
    });
    
    // 按月份倒序排序
    this.monthGroups.sort((a, b) => b.month.localeCompare(a.month));
    
    // 計算總筆數
    this.totalRecords = records.length;
  }
  
  /**
   * 取得模擬資料
   */
  private getMockData(): RemittanceRecord[] {
    return [
      {
        id: 'IBR2023092601',
        type: '匯入匯款',
        date: '2023-09-26',
        amount: 1000,
        currency: 'USD',
        status: 'PENDING',
        senderName: 'John Doe',
        senderBank: 'Bank of America',
        reference: 'REF20230926001'
      },
      {
        id: 'IBR2023091501',
        type: '匯入匯款',
        date: '2023-09-15',
        amount: 2500,
        currency: 'USD',
        status: 'COMPLETED',
        senderName: 'Jane Smith',
        senderBank: 'Chase Bank',
        reference: 'REF20230915001'
      },
      {
        id: 'IBR2023090701',
        type: '匯入匯款',
        date: '2023-09-07',
        amount: 500,
        currency: 'USD',
        status: 'COMPLETED',
        senderName: 'Bob Johnson',
        senderBank: 'Wells Fargo',
        reference: 'REF20230907001'
      },
      {
        id: 'IBR2023082801',
        type: '匯入匯款',
        date: '2023-08-28',
        amount: 3000,
        currency: 'USD',
        status: 'COMPLETED',
        senderName: 'Alice Brown',
        senderBank: 'Citibank',
        reference: 'REF20230828001'
      }
    ];
  }
  
  /**
   * 格式化日期
   */
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}/${month}/${day}`;
  }
  
  /**
   * 時間範圍變更
   */
  onTimeRangeChange(range: string): void {
    this.selectedTimeRange = range;
    this.applyFilters();
  }
  
  /**
   * 交易類型變更
   */
  onTypeChange(type: string): void {
    this.selectedType = type;
    this.applyFilters();
  }
  
  /**
   * 套用篩選條件
   */
  private applyFilters(): void {
    // 重新載入資料並套用篩選
    this.loadQueryResults();
  }
  
  /**
   * 展開/收合詳細資訊
   */
  toggleExpand(record: RemittanceRecord): void {
    record.expanded = !record.expanded;
  }
  
  /**
   * 取得狀態顯示文字
   */
  getStatusText(status: string): string {
    switch (status) {
      case 'PENDING':
        return '待解款';
      case 'COMPLETED':
        return '已完成';
      case 'PROCESSING':
        return '處理中';
      default:
        return status;
    }
  }
  
  /**
   * 判斷是否為待處理狀態
   */
  isPending(status: string): boolean {
    return status === 'PENDING';
  }
  
  /**
   * 格式化金額
   */
  formatAmount(amount: number, currency: string): string {
    return `${currency} ${amount.toLocaleString()}`;
  }
  
  /**
   * 下載紀錄
   */
  downloadRecords(): void {
    this.loading = true;
    
    this.queryService.downloadRecords(this.selectedTimeRange, this.selectedType).subscribe({
      next: (blob) => {
        this.loading = false;
        // 建立下載連結
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `remittance_records_${new Date().getTime()}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: () => {
        this.loading = false;
        this.error = '下載失敗，請稍後再試';
      }
    });
  }
  
  /**
   * 查看詳細資訊
   */
  viewDetail(record: RemittanceRecord): void {
    if (record.status === 'PENDING') {
      // 待解款的記錄，導向解款頁面
      this.router.navigate(['/ibr/individual/remittance-detail'], {
        queryParams: { caseNo: record.id }
      });
    } else {
      // 已完成的記錄，展開顯示詳細資訊
      this.toggleExpand(record);
    }
  }
  
  /**
   * 返回查詢頁面
   */
  goBack(): void {
    this.router.navigate(['/ibr/query']);
  }
  
  /**
   * 開啟客服
   */
  openCustomerService(): void {
    console.log('Open customer service dialog');
    // TODO: 實作客服對話框
  }
}