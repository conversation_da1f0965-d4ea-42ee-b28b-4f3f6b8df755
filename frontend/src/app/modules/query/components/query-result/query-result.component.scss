/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 0 0 8px 8px;  /* 只有底部圓角，與 header 銜接 */
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.05), 4px 0 8px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.05);  /* 側面和底部陰影 */
  overflow: hidden;
  margin-top: -1px;  /* 確保與 header 完美銜接 */
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}


/* === 篩選區域 === */
.filter-section {
  margin-bottom: 24px;
  
  .filter-group {
    display: flex;
    gap: 8px;
    align-items: center;
    
    .filter-select {
      flex: 1;
      padding: 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      background-color: white;
      font-size: 16px;
      font-family: "Noto Sans TC", sans-serif;
      cursor: pointer;
      
      &:focus {
        outline: none;
        border-color: #0044ad;
        box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
      }
    }
    
    .btn-search {
      background-color: #0044ad;
      border: none;
      border-radius: 4px;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        width: 20px;
        height: 20px;
        color: white;
      }
      
      &:hover {
        background-color: #003399;
      }
    }
  }
}

/* === 查詢結果區 === */
.results-section {
  .no-data {
    text-align: center;
    padding: 48px;
    color: #666666;
    font-size: 16px;
    font-family: "Noto Sans TC", sans-serif;
  }
  
  .month-group {
    margin-bottom: 24px;
    
    .month-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      
      .month-title {
        font-size: 20px;
        font-weight: 500;
        color: #041c43;
        margin: 0;
        font-family: "Noto Sans TC", sans-serif;
      }
      
      .date-range {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666666;
        font-size: 14px;
        font-family: "Noto Sans TC", sans-serif;
        
        .sort-icon {
          width: 16px;
          height: 16px;
          cursor: pointer;
          transition: transform 0.3s ease;
          
          &:hover {
            transform: rotate(180deg);
          }
        }
      }
    }
    
    .records-list {
      background-color: #f8f9fa;
      border-radius: 8px;
      overflow: hidden;
      
      .record-item {
        background-color: white;
        margin-bottom: 8px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .record-main {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          
          .record-info {
            .record-type {
              font-size: 16px;
              font-weight: 500;
              color: #041c43;
              margin-bottom: 4px;
              font-family: "Noto Sans TC", sans-serif;
            }
            
            .record-date {
              font-size: 14px;
              color: #666666;
              font-family: "Noto Sans TC", sans-serif;
            }
          }
          
          .record-right {
            display: flex;
            align-items: center;
            gap: 12px;
            
            .record-amount {
              font-size: 18px;
              font-weight: 600;
              color: #041c43;
              font-family: "Montserrat", sans-serif;
            }
            
            .btn-pending {
              background-color: #fff5f2;
              border: 1px solid #ff6b35;
              color: #ff6b35;
              padding: 4px 12px;
              border-radius: 20px;
              font-size: 14px;
              font-family: "Noto Sans TC", sans-serif;
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:hover {
                background-color: #ff6b35;
                color: white;
              }
            }
            
            .expand-icon {
              width: 20px;
              height: 20px;
              transition: transform 0.3s ease;
              
              &.expanded {
                transform: rotate(180deg);
              }
            }
          }
        }
        
        .record-detail {
          padding: 0 16px 16px 16px;
          background-color: #f8f9fa;
          border-radius: 0 0 8px 8px;
          
          .detail-row {
            display: flex;
            padding: 8px 0;
            
            .detail-label {
              flex: 0 0 120px;
              color: #666666;
              font-size: 14px;
              font-family: "Noto Sans TC", sans-serif;
            }
            
            .detail-value {
              flex: 1;
              color: #041c43;
              font-size: 14px;
              font-family: "Noto Sans TC", sans-serif;
            }
          }
        }
      }
    }
  }
}

/* === 錯誤訊息 === */
.alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* === 下載按鈕區 === */
.download-section {
  margin-top: 40px;
  text-align: center;
  
  .btn-download {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 32px;
    background-color: white;
    border: 1px solid #0044ad;
    color: #0044ad;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    font-family: "Noto Sans TC", sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .download-icon {
      width: 20px;
      height: 20px;
    }
    
    &:hover {
      background-color: #0044ad;
      color: white;
      
      .download-icon {
        filter: brightness(0) invert(1);
      }
    }
  }
}

/* === 載入動畫 === */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0044ad;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }
}

/* 桌機版本 - 2/3 寬度 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 20px;  /* 桌機版也保持 padding */
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .main-content-wrapper {
    padding: 0 40px 40px;  /* 大螢幕增加 padding */
  }
  
  .content-container {
    max-width: 900px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;  /* 與 header 的 padding 一致 */
  }
  
  .content-container {
    max-width: 100%;
    border-radius: 0;  /* 手機版移除圓角 */
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);  /* 較淡的陰影 */
    margin-top: 0;  /* 手機版直接連接 */
  }
  
  .content-section {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  
  .filter-section {
    .filter-group {
      flex-direction: column;
      
      .filter-select,
      .btn-search {
        width: 100%;
      }
    }
  }
  
  .results-section {
    .month-group {
      .month-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
      
      .records-list {
        .record-item {
          .record-main {
            .record-right {
              flex-direction: column;
              align-items: flex-end;
              gap: 8px;
            }
          }
        }
      }
    }
  }
}