<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container">
      
      <!-- 內容區塊 -->
      <div class="content-section">
        <!-- 標題區 -->
        <div class="title-section">
          <h1 class="main-title">外匯解款紀錄查詢</h1>
        </div>
    
        <!-- 篩選區域 -->
        <div class="filter-section">
          <div class="filter-group">
            <select class="filter-select" [(ngModel)]="selectedTimeRange" (change)="onTimeRangeChange(selectedTimeRange)">
              <option *ngFor="let range of timeRanges" [value]="range.value">{{ range.label }}</option>
            </select>
            
            <select class="filter-select" [(ngModel)]="selectedType" (change)="onTypeChange(selectedType)">
              <option *ngFor="let type of transactionTypes" [value]="type.value">{{ type.label }}</option>
            </select>
            
            <button type="button" class="btn-search">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
    
    <!-- 查詢結果 -->
    <div class="results-section" *ngIf="!loading && !error">
      <!-- 無資料 -->
      <div class="no-data" *ngIf="monthGroups.length === 0">
        <p>查無匯款紀錄</p>
      </div>
      
      <!-- 月份群組 -->
      <div class="month-group" *ngFor="let group of monthGroups">
        <!-- 月份標題 -->
        <div class="month-header">
          <h2 class="month-title">{{ group.month }}</h2>
          <div class="date-range">
            {{ group.dateRange }}
            <img src="assets/image/icon/sort.svg" alt="Sort" class="sort-icon">
          </div>
        </div>
        
        <!-- 記錄列表 -->
        <div class="records-list">
          <div class="record-item" *ngFor="let record of group.records" (click)="viewDetail(record)" (keyup.enter)="viewDetail(record)" tabindex="0" role="button">
            <div class="record-main">
              <div class="record-info">
                <div class="record-type">{{ record.type }}</div>
                <div class="record-date">{{ record.date }}</div>
              </div>
              
              <div class="record-right">
                <div class="record-amount">{{ formatAmount(record.amount, record.currency) }}</div>
                <button type="button" class="btn-pending" *ngIf="isPending(record.status)">
                  {{ getStatusText(record.status) }}
                </button>
                <img src="assets/image/icon/down_arrow_black.svg" alt="Expand" 
                     class="expand-icon"
                     [class.expanded]="record.expanded">
              </div>
            </div>
            
            <!-- 展開的詳細資訊 -->
            <div class="record-detail" *ngIf="record.expanded">
              <div class="detail-row">
                <span class="detail-label">匯款人：</span>
                <span class="detail-value">{{ record.senderName || '-' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">匯款銀行：</span>
                <span class="detail-value">{{ record.senderBank || '-' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">參考編號：</span>
                <span class="detail-value">{{ record.reference || '-' }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">狀態：</span>
                <span class="detail-value">{{ getStatusText(record.status) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 錯誤訊息 -->
    <div class="alert alert-danger" *ngIf="error">
      {{ error }}
    </div>
    
        <!-- 下載按鈕 -->
        <div class="download-section" *ngIf="totalRecords > 0">
          <button type="button" class="btn-download" (click)="downloadRecords()">
            <img src="assets/image/icon/download.svg" alt="Download" class="download-icon">
            下載紀錄
          </button>
        </div>
      </div>
    </div>
  </main>
  
  <!-- 載入動畫 -->
  <div class="loading-overlay" *ngIf="loading">
    <div class="spinner"></div>
  </div>
</div>