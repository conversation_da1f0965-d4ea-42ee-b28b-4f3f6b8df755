import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { RemittanceQueryService } from '../../services/remittance-query.service';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';

@Component({
  selector: 'app-query-input',
  templateUrl: './query-input.component.html',
  styleUrls: ['./query-input.component.scss']
})
export class QueryInputComponent implements OnInit {
  queryForm: FormGroup;
  loading = false;
  error: string | null = null;
  
  // 日期選擇器設定
  maxDate = new Date(); // 最大日期為今天
  minDate = new Date(new Date().getFullYear() - 100, 0, 1); // 最小日期為100年前
  
  // 銀行選項
  banks = [
    { code: '004', name: '臺灣銀行' },
    { code: '005', name: '臺灣土地銀行' },
    { code: '006', name: '合作金庫商業銀行' },
    { code: '007', name: '第一商業銀行' },
    { code: '008', name: '華南商業銀行' },
    { code: '009', name: '彰化商業銀行' },
    { code: '011', name: '上海商業儲蓄銀行' },
    { code: '012', name: '台北富邦商業銀行' },
    { code: '013', name: '國泰世華商業銀行' },
    { code: '016', name: '高雄銀行' },
    { code: '017', name: '兆豐國際商業銀行' },
    { code: '021', name: '花旗(台灣)商業銀行' },
    { code: '048', name: '王道商業銀行' },
    { code: '050', name: '臺灣中小企業銀行' },
    { code: '052', name: '渣打國際商業銀行' },
    { code: '053', name: '台中商業銀行' },
    { code: '054', name: '京城商業銀行' },
    { code: '081', name: '匯豐(台灣)商業銀行' },
    { code: '101', name: '瑞興商業銀行' },
    { code: '102', name: '華泰商業銀行' },
    { code: '103', name: '臺灣新光商業銀行' },
    { code: '104', name: '臺北五信' },
    { code: '106', name: '臺北九信' },
    { code: '108', name: '陽信商業銀行' },
    { code: '118', name: '板信商業銀行' },
    { code: '147', name: '三信商業銀行' },
    { code: '803', name: '聯邦商業銀行' },
    { code: '805', name: '遠東國際商業銀行' },
    { code: '806', name: '元大商業銀行' },
    { code: '807', name: '永豐商業銀行' },
    { code: '808', name: '玉山商業銀行' },
    { code: '809', name: '凱基商業銀行' },
    { code: '810', name: '星展(台灣)商業銀行' },
    { code: '812', name: '台新國際商業銀行' },
    { code: '815', name: '日盛國際商業銀行' },
    { code: '816', name: '安泰商業銀行' },
    { code: '822', name: '中國信託商業銀行' }
  ];
  
  constructor(
    private fb: FormBuilder,
    private queryService: RemittanceQueryService,
    private router: Router,
    private slideDialogService: SlideDialogService
  ) {
    this.queryForm = this.createForm();
  }
  
  ngOnInit(): void {
    // 不需要產生日期選項，使用日期選擇器
    this.initializeComponent();
  }
  
  private initializeComponent(): void {
    // 初始化元件
  }
  
  /**
   * 創建表單
   */
  private createForm(): FormGroup {
    return this.fb.group({
      idNumber: ['', [
        Validators.required,
        Validators.pattern(/^[A-Z][12][0-9]{8}$/)  // 身分證號格式
      ]],
      birthDate: ['', Validators.required],
      bankCode: ['', Validators.required],
      accountNumber: ['', [
        Validators.required,
        Validators.pattern(/^[0-9]{10,16}$/)  // 銀行帳號10-16位數字
      ]],
      phoneNumber: ['', [
        Validators.required,
        Validators.pattern(/^09[0-9]{8}$/)  // 手機號碼格式
      ]]
    });
  }
  
  /**
   * 提交查詢
   */
  onSubmit(): void {
    if (this.queryForm.invalid) {
      this.markFormGroupTouched();
      return;
    }
    
    const formValue = this.queryForm.value;
    // 格式化生日為 YYYYMMDD
    const birthDate = formValue.birthDate;
    const birthday = birthDate ? 
      `${birthDate.getFullYear()}${(birthDate.getMonth() + 1).toString().padStart(2, '0')}${birthDate.getDate().toString().padStart(2, '0')}` : 
      '';
    
    // 儲存查詢資料
    const queryData = {
      idNumber: formValue.idNumber,
      birthDate: birthday,  // 格式化為 YYYYMMDD
      bankCode: formValue.bankCode,
      accountNumber: formValue.accountNumber,
      phoneNumber: formValue.phoneNumber
    };
    
    this.queryService.setQueryData(queryData);
    
    // 導向驗證頁面
    this.router.navigate(['/ibr/query/verification']);
  }
  
  /**
   * 標記表單為已觸碰
   */
  private markFormGroupTouched(): void {
    Object.keys(this.queryForm.controls).forEach(key => {
      const control = this.queryForm.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }
  
  /**
   * 取得表單控制項錯誤訊息
   */
  getFieldError(fieldName: string): string | null {
    const control = this.queryForm.get(fieldName);
    if (control && control.errors && control.touched) {
      if (control.errors['required']) {
        switch (fieldName) {
          case 'idNumber':
            return '請輸入身分證字號';
          case 'birthDate':
            return '請選擇出生日期';
          case 'bankCode':
            return '請選擇銀行';
          case 'accountNumber':
            return '請輸入銀行帳號';
          case 'phoneNumber':
            return '請輸入手機號碼';
          default:
            return '此欄位為必填';
        }
      }
      if (control.errors['pattern']) {
        switch (fieldName) {
          case 'idNumber':
            return '身分證字號格式不正確';
          case 'accountNumber':
            return '銀行帳號應為10-16位數字';
          case 'phoneNumber':
            return '手機號碼格式不正確 (例: **********)';
          default:
            return '格式不正確';
        }
      }
    }
    return null;
  }
  
  /**
   * 取得欄位是否有錯誤
   */
  hasError(fieldName: string): boolean {
    const control = this.queryForm.get(fieldName);
    return !!(control && control.errors && control.touched);
  }
  
  /**
   * 開啟客服對話框
   */
  openCustomerService(): void {
    // 開啟客服對話框的邏輯
    console.log('開啟客服對話框');
    // 可以使用 slideDialogService 開啟客服資訊
  }
}