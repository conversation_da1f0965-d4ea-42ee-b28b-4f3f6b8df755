/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 0 0 8px 8px;  /* 只有底部圓角，與 header 銜接 */
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.05), 4px 0 8px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.05);  /* 側面和底部陰影 */
  overflow: hidden;
  margin-top: -1px;  /* 確保與 header 完美銜接 */
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 表單提示文字 === */
.form-hint {
  font-size: 12px;
  color: #666666;
  margin-top: 4px;
  font-family: "Noto Sans TC", sans-serif;
}

/* === 說明文字區塊 === */
.description-box {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 32px;
}

.description-text {
  color: #495057;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  margin: 0;
  text-align: center;
}

/* === 表單樣式 === */
.form-group {
  margin-bottom: 24px;
}

/* === Angular Material 日期選擇器樣式 === */
::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: #ced4da;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: #0044ad;
  }
  
  .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick {
    color: #dc3545;
  }
  
  .mat-datepicker-toggle {
    color: #0044ad;
  }
  
  .mat-form-field {
    width: 100%;
    font-family: "Noto Sans TC", sans-serif;
  }
  
  .mat-input-element {
    cursor: pointer;
  }
}

.full-width {
  width: 100%;
}

.form-group label {
  display: block;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.required {
  color: #dc3545;
  margin-left: 4px;
}

.form-control {
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  color: #495057;
  background: #ffffff;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #0044ad;
  box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control::placeholder {
  color: #adb5bd;
}

/* === 生日選擇器群組 === */
.birthday-group {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 8px;
}

/* === 錯誤訊息 === */
.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 4px;
  font-family: "Noto Sans TC", sans-serif;
}

.alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* === 按鈕群組 === */
.button-group {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.btn-submit {
  min-width: 160px;
  height: 48px;
  padding: 12px 32px;
  background: #0044ad;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover:not(:disabled) {
  background: #003399;
  box-shadow: 0 4px 8px rgba(0, 68, 173, 0.2);
}

.btn-submit:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

/* === 載入動畫 === */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0044ad;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }
}

/* 桌機版本 - 2/3 寬度 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 20px;  /* 桌機版也保持 padding */
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .main-content-wrapper {
    padding: 0 40px 40px;  /* 大螢幕增加 padding */
  }
  
  .content-container {
    max-width: 900px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;  /* 與 header 的 padding 一致 */
  }
  
  .content-container {
    max-width: 100%;
    border-radius: 0;  /* 手機版移除圓角 */
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);  /* 較淡的陰影 */
    margin-top: 0;  /* 手機版直接連接 */
  }
  
  .content-section {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  .step-current {
    font-size: 16px;
  }
  
  .step-total {
    font-size: 14px;
  }
  
  .form-group label {
    font-size: 14px;
  }
  
  .form-control {
    font-size: 14px;
    height: 44px;
  }
  
  .btn-submit {
    width: 100%;
    font-size: 16px;
  }
}