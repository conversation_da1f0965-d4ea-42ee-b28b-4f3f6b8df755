<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container">
      
      <!-- 內容區塊 -->
      <div class="content-section">
        <!-- 標題區 -->
        <div class="title-section">
          <h1 class="main-title">外匯解款紀錄查詢</h1>
          <p class="subtitle">請輸入欲查詢之匯款帳戶，以供凱基銀行確認身份</p>
        </div>
        
        <!-- 查詢表單 -->
        <form [formGroup]="queryForm" (ngSubmit)="onSubmit()">
          <!-- 身分證字號 -->
          <div class="form-group">
            <label for="idNumber">身分證字號</label>
            <input 
              type="text" 
              id="idNumber"
              class="form-control"
              [class.is-invalid]="hasError('idNumber')"
              formControlName="idNumber"
              placeholder="輸入身分證字號"
              maxlength="10">
            <div class="error-message" *ngIf="getFieldError('idNumber')">
              {{ getFieldError('idNumber') }}
            </div>
          </div>
          
          <!-- 生日 -->
          <div class="form-group">
            <label for="birthDate">生日</label>
            <mat-form-field appearance="outline" class="full-width">
              <input 
                matInput 
                [matDatepicker]="picker"
                formControlName="birthDate"
                placeholder="選擇出生日期"
                [max]="maxDate"
                [min]="minDate"
                readonly
                (click)="picker.open()">
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="hasError('birthDate')">
                {{ getFieldError('birthDate') }}
              </mat-error>
            </mat-form-field>
          </div>
          
          <!-- 銀行代碼 -->
          <div class="form-group">
            <label for="bankCode">銀行代碼</label>
            <select 
              id="bankCode"
              class="form-control"
              [class.is-invalid]="hasError('bankCode')"
              formControlName="bankCode">
              <option value="">請選擇銀行代碼</option>
              <option value="809">凱基銀行</option>
            </select>
            <div class="error-message" *ngIf="getFieldError('bankCode')">
              {{ getFieldError('bankCode') }}
            </div>
          </div>
          
          <!-- 銀行帳號 -->
          <div class="form-group">
            <label for="accountNumber">銀行帳號</label>
            <input 
              type="text" 
              id="accountNumber"
              class="form-control"
              [class.is-invalid]="hasError('accountNumber')"
              formControlName="accountNumber"
              placeholder="輸入銀行帳戶">
            <div class="error-message" *ngIf="getFieldError('accountNumber')">
              {{ getFieldError('accountNumber') }}
            </div>
          </div>
          
          <!-- 手機號碼 -->
          <div class="form-group">
            <label for="phoneNumber">手機號碼</label>
            <input 
              type="tel" 
              id="phoneNumber"
              class="form-control"
              [class.is-invalid]="hasError('phoneNumber')"
              formControlName="phoneNumber"
              placeholder="輸入手機號碼"
              maxlength="10">
            <div class="error-message" *ngIf="getFieldError('phoneNumber')">
              {{ getFieldError('phoneNumber') }}
            </div>
            <div class="form-hint">請填寫留存於上述銀行之號碼</div>
          </div>
          
          <!-- 錯誤訊息 -->
          <div class="alert alert-danger" *ngIf="error">
            {{ error }}
          </div>
          
          <!-- 按鈕群組 -->
          <div class="button-group">
            <button 
              type="submit" 
              class="btn-submit"
              [disabled]="loading || queryForm.invalid">
              {{ loading ? '查詢中...' : '驗證手機號碼' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </main>
  
  <!-- 載入動畫 -->
  <div class="loading-overlay" *ngIf="loading">
    <div class="spinner"></div>
  </div>
</div>