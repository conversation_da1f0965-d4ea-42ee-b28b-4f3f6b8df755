import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, interval } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { RemittanceQueryService } from '../../services/remittance-query.service';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-query-verification',
  templateUrl: './query-verification.component.html',
  styleUrls: ['./query-verification.component.scss']
})
export class QueryVerificationComponent implements OnInit, OnDestroy {
  verificationForm: FormGroup;
  loading = false;
  error: string | null = null;

  // 倒數計時
  countdown = 0;
  private countdownSubscription?: any;

  // 重新發送次數限制
  resendCount = 0;
  maxResendCount = 3;

  // 查詢資料
  queryData: any = null;
  maskedPhone = '';

  // g-otp 組件所需屬性
  otpPrefix = 'XYZ';
  sentTime = '';
  private otpCode = '';

  private destroy$ = new Subject<void>();

  constructor(
      private fb: FormBuilder,
      private queryService: RemittanceQueryService,
      private router: Router,
      private slideDialogService: SlideDialogService
  ) {
    this.verificationForm = this.createForm();
  }

  ngOnInit(): void {
    // 取得前一步的查詢資料
    this.queryData = this.queryService.getQueryData();

    if (!this.queryData) {
      // 開發模式下使用測試資料
      if (!environment.production) {
        this.queryData = {
          phoneNumber: '0912345678',
          idNumber: 'A123456789',
          birthDate: '1990-01-01'
        };
        console.warn('使用測試資料進行開發');
      } else {
        // 生產環境如果沒有查詢資料，返回第一步
        this.router.navigate(['/ibr/query']);
        return;
      }
    }

    // 遮罩手機號碼
    this.maskedPhone = this.maskPhoneNumber(this.queryData.phoneNumber);

    // 自動發送簡訊驗證碼
    this.sendSms();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }
  }

  /**
   * 創建表單
   */
  private createForm(): FormGroup {
    return this.fb.group({
      // g-otp 組件會處理 OTP 輸入，這裡只需要保留其他欄位
    });
  }

  /**
   * 遮罩手機號碼
   */
  private maskPhoneNumber(phone: string): string {
    if (!phone || phone.length < 10) return phone;
    return phone.substring(0, 4) + '***' + phone.substring(7);
  }

  /**
   * 發送簡訊驗證碼
   */
  sendSms(): void {
    if (this.resendCount >= this.maxResendCount) {
      this.error = '已達發送次數上限，請稍後再試';
      return;
    }

    this.loading = true;
    this.error = null;

    // 模擬發送簡訊 API
    this.queryService.sendOtp({ usePhone: this.queryData.phoneNumber } as any).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          this.resendCount++;
          this.startCountdown();
          this.sentTime = this.getCurrentTime();
          // 設定 OTP 前綴
          if (response.data && response.data.dynacPwd) {
            this.otpPrefix = response.data.dynacPwd;
          }
          // g-otp 組件會自動清空
        } else {
          this.error = response.message || '發送失敗，請稍後再試';
        }
      },
      error: (error) => {
        this.loading = false;
        this.error = error.message || '發送失敗，請稍後再試';
      }
    });
  }

  /**
   * 開始倒數計時
   */
  private startCountdown(): void {
    this.countdown = 60; // 60秒倒數

    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }

    this.countdownSubscription = interval(1000)
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => {
          this.countdown--;
          if (this.countdown <= 0) {
            this.countdownSubscription.unsubscribe();
          }
        });
  }

  /**
   * 處理 g-otp 組件變更事件
   */
  onOtpChange(otpCode: string): void {
    this.otpCode = otpCode;
    // 清除錯誤訊息
    if (this.error) {
      this.error = null;
    }
  }

  /**
   * 處理 g-otp 組件完成事件
   */
  onOtpComplete(otpCode: string): void {
    this.otpCode = otpCode;
    // 自動提交驗證
    this.onSubmit();
  }

  /**
   * 提交驗證
   */
  onSubmit(): void {
    if (!this.otpCode || this.otpCode.length !== 6) {
      this.error = '請輸入完整的驗證碼';
      return;
    }

    this.loading = true;
    this.error = null;

    // 驗證 OTP - 需要組合前綴和輸入的數字
    const fullOtpCode = this.otpPrefix + this.otpCode;
    this.queryService.verifyOtp({ otp: fullOtpCode, ...this.queryData } as any).subscribe({
      next: (response) => {
        this.loading = false;
        if (response.success) {
          // 驗證成功，導向查詢結果頁面
          this.router.navigate(['/ibr/query/result']);
        } else {
          this.error = response.message || '驗證碼錯誤，請重新輸入';
        }
      },
      error: (error) => {
        this.loading = false;
        this.error = error.message || '驗證失敗，請稍後再試';
      }
    });
  }

  /**
   * 返回上一步
   */
  goBack(): void {
    this.router.navigate(['/ibr/query']);
  }

  /**
   * 取得當前時間
   */
  getCurrentTime(): string {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 格式化倒數計時
   */
  formatCountdown(): string {
    const minutes = Math.floor(this.countdown / 60);
    const seconds = this.countdown % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  /**
   * 開啟客服
   */
  openCustomerService(): void {
    console.log('Open customer service dialog');
    // TODO: 實作客服對話框
  }
}
