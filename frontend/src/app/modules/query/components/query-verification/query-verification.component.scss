/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 0 0 8px 8px;  /* 只有底部圓角，與 header 銜接 */
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.05), 4px 0 8px rgba(0, 0, 0, 0.05), 0 4px 8px rgba(0, 0, 0, 0.05);  /* 側面和底部陰影 */
  overflow: hidden;
  margin-top: -1px;  /* 確保與 header 完美銜接 */
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}


/* === 文件圖示區域 === */
.document-icon-section {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.document-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* === 驗證碼輸入區域 === */
.verification-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.code-input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
  border-radius: 12px;
  border: 1px solid #e1ecf7;
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.08);
}

.code-prefix-section {
  display: flex;
  justify-content: center;
  width: 100%;
}

.code-prefix {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 3px;
  text-shadow: 0 1px 2px rgba(0, 68, 173, 0.1);
  position: relative;
}

.code-prefix::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0044ad 0%, #0066cc 100%);
  border-radius: 1px;
}

/* === OTP 輸入框 === */
.otp-input-container {
  display: flex;
  gap: 10px;
}

.otp-input {
  width: 52px;
  height: 60px;
  border: 2px solid #d1e7ff;
  border-radius: 8px;
  text-align: center;
  font-family: "Montserrat", sans-serif;
  font-size: 26px;
  font-weight: 700;
  color: #041c43;
  background: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.otp-input:focus {
  border-color: #0044ad;
  box-shadow: 0 0 0 4px rgba(0, 68, 173, 0.15), 0 2px 8px rgba(0, 68, 173, 0.2);
  transform: translateY(-1px);
}

.otp-input.filled {
  background: linear-gradient(135deg, #f0f7ff 0%, #e8f2ff 100%);
  border-color: #0044ad;
  color: #0044ad;
  box-shadow: 0 2px 6px rgba(0, 68, 173, 0.15);
}

.otp-input.error {
  border-color: #ff4444;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
  color: #cc0000;
  animation: shake 0.5s ease-in-out;
  box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.15);
}

/* === 文字提示 === */
.code-hint {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  text-align: center;
  margin-top: 8px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #0044ad;
}

.code-error {
  color: #ff4444;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  margin-top: 8px;
  padding: 8px 16px;
  background: #fff5f5;
  border-radius: 6px;
  border-left: 3px solid #ff4444;
  animation: fadeInError 0.3s ease-out;
}

/* === 新增動畫效果 === */
@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === 抖動動畫 === */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

.otp-input-container.shake {
  animation: shake 0.5s ease-in-out;
}

/* === 狀態訊息區域 === */
.status-section {
  margin-bottom: 24px;
}

.status-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f0f7ff;
  border-radius: 8px;
  border-left: 4px solid #0044ad;
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.status-text {
  flex: 1;
}

.status-main {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  margin-bottom: 4px;
}

.status-sub {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  line-height: 140%;
}

/* === 重新發送區域 === */
.resend-section {
  display: flex;
  justify-content: center;
}

.resend-button {
  background: none;
  border: none;
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.resend-button:hover:not(:disabled) {
  background: #f0f7ff;
}

.resend-button:disabled {
  color: #999999;
  cursor: not-allowed;
}

.resend-text {
  font-weight: 500;
}

.countdown-timer {
  color: #ff4444;
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  font-size: 14px;
  margin-left: 4px;
}

/* === 發送目標訊息 === */
.target-info {
  text-align: center;
  margin-bottom: 40px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.target-text {
  display: block;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  margin-bottom: 4px;
}

.target-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  line-height: 140%;
}

/* === 錯誤訊息 === */
.alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 24px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* === 按鈕群組 === */
.button-group {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.btn-submit {
  min-width: 160px;
  height: 48px;
  padding: 12px 32px;
  background: #e0e0e0;
  color: #495057;
  border: none;
  border-radius: 4px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-submit:hover:not(:disabled) {
  background: #d0d0d0;
}

.btn-submit:disabled {
  background: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
}

/* === 頁尾操作區 === */
.footer-actions {
  text-align: center;
  
  .btn-back {
    background: none;
    border: none;
    color: #0044ad;
    font-size: 16px;
    cursor: pointer;
    padding: 8px 16px;
    font-family: "Noto Sans TC", sans-serif;
    transition: all 0.3s ease;
    
    .back-arrow {
      margin-right: 4px;
    }
    
    &:hover {
      color: #003399;
      text-decoration: underline;
    }
  }
}

/* === 載入動畫 === */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #0044ad;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }
}

/* 桌機版本 - 2/3 寬度 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 20px;  /* 桌機版也保持 padding */
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .main-content-wrapper {
    padding: 0 40px 40px;  /* 大螢幕增加 padding */
  }
  
  .content-container {
    max-width: 900px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;  /* 與 header 的 padding 一致 */
  }
  
  .content-container {
    max-width: 100%;
    border-radius: 0;  /* 手機版移除圓角 */
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);  /* 較淡的陰影 */
    margin-top: 0;  /* 手機版直接連接 */
  }
  
  .content-section {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  
  .otp-input-container {
    .otp-box {
      width: 42px;
      height: 42px;
      font-size: 18px;
    }
  }
  
  .btn-submit {
    width: 100%;
    font-size: 16px;
  }
}