import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, retry, timeout, delay } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { VerifyOTPModel } from '../models/VerifyOTP.model';
import { ApiConfigService } from '../../../@core/shared-2/services/api-config.service';

/**
 * 查詢服務API
 * 對應後端 QueryController
 * 
 * 根據URD需求：
 * 1. URL參數驗證 (type=Q, case=案件編號)
 * 2. 依編號查詢交易資料
 * 3. 顯示交易詳細資訊
 */

// ==================== 新的 URD-based API 介面 ====================

export interface QueryInitRequest {
  type: string; // Q=查詢
  caseNo: string;
}

export interface QueryInitResponse {
  caseNo: string;
  flowType: 'QUERY';
  transactionFound: boolean;
  transactionData: Record<string, unknown>;
  queryTime: string;
  sessionId: string;
}

export interface TransactionDetailsResponse {
  caseNo: string;
  found: boolean;
  transactionData?: Record<string, unknown>;
  statusDetails?: Record<string, unknown>;
  processingHistory?: Array<Record<string, unknown>>;
  queryTime: string;
  message?: string;
}

export interface TransactionStatusResponse {
  caseNo: string;
  currentStatus: string;
  statusDescription: string;
  progressPercentage: number;
  currentStep: string;
  nextStep: string;
  canTakeAction: boolean;
  availableActions: Array<{
    action: string;
    label: string;
    enabled: boolean;
  }>;
  lastUpdatedTime: string;
  estimatedCompletionTime?: string;
  contactInfo: {
    customerService: string;
    email: string;
    serviceHours: string;
  };
}

export interface TransactionSearchRequest {
  searchType: string;
  searchValue: string;
}

export interface TransactionSearchResponse {
  searchType: string;
  searchValue: string;
  resultCount: number;
  transactions: Array<Record<string, unknown>>;
  searchTime: string;
}

export interface CustomerTransactionsRequest {
  customerId: string;
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  page: number;
  pageSize: number;
}

export interface CustomerTransactionsResponse {
  customerId: string;
  customerType: string;
  pagination: {
    currentPage: number;
    pageSize: number;
    totalPages: number;
    totalRecords: number;
  };
  transactions: Array<Record<string, unknown>>;
  summary: {
    totalTransactions: number;
    completedTransactions: number;
    processingTransactions: number;
    totalAmount: number;
  };
  queryTime: string;
}

export interface QueryApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

// ==================== 原有介面 (保持向後相容) ====================

/**
 * 匯款解款查詢服務介面
 */
export interface RemittanceSearchRequest {
  queryType: 'ID' | 'CASE_NO' | 'PHONE';
  queryValue: string;
  applicantType: 'INDIVIDUAL' | 'CORPORATE';
}

export interface RemittanceHistoryRequest {
  applicantId: string;
  applicantType: 'INDIVIDUAL' | 'CORPORATE';
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}

export interface QueryApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

/**
 * 匯款解款查詢API服務
 * 
 * 提供完整的匯款查詢功能，包括：
 * - 多種查詢方式 (身分證號、案件編號、手機號碼)
 * - 匯款狀態查詢
 * - 歷史記錄查詢
 * - 快速狀態檢查
 */
@Injectable({
  providedIn: 'root'
})
export class RemittanceQueryService {
  private readonly apiUrl = `${environment.apiUrl}/api/ibr/query`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2; // 重試次數
  private mockMode = !environment.production;
  
  // 儲存查詢資料
  private queryData: any = null;

  constructor(
    private http: HttpClient,
    private apiConfig: ApiConfigService
  ) {}

  // ==================== 新的 URD-based API 方法 ====================

  /**
   * 初始化查詢流程
   * 對應後端: QueryController.initializeQuery()
   */
  initializeQuery(type: string, caseNo: string): Observable<QueryApiResponse<QueryInitResponse>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<QueryInitResponse>>(
      `${this.apiUrl}/initialize`,
      { 
        headers,
        params: { type, caseNo }
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢交易詳細資訊
   * 對應後端: QueryController.getTransactionDetails()
   */
  getTransactionDetails(caseNo: string): Observable<QueryApiResponse<TransactionDetailsResponse>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<TransactionDetailsResponse>>(
      `${this.apiUrl}/transaction/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢交易狀態
   * 對應後端: QueryController.getTransactionStatus()
   */
  getTransactionStatus(caseNo: string): Observable<QueryApiResponse<TransactionStatusResponse>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<TransactionStatusResponse>>(
      `${this.apiUrl}/status/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 多條件查詢交易
   * 對應後端: QueryController.searchTransactions()
   */
  searchTransactions(request: TransactionSearchRequest): Observable<QueryApiResponse<TransactionSearchResponse>> {
    const headers = this.getHeaders();
    
    return this.http.post<QueryApiResponse<TransactionSearchResponse>>(
      `${this.apiUrl}/search`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得客戶所有交易記錄
   * 對應後端: QueryController.getCustomerTransactions()
   */
  getCustomerTransactions(
    customerId: string, 
    customerType: 'INDIVIDUAL' | 'CORPORATE' = 'INDIVIDUAL',
    page: number = 1,
    pageSize: number = 10
  ): Observable<QueryApiResponse<CustomerTransactionsResponse>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<CustomerTransactionsResponse>>(
      `${this.apiUrl}/customer/${encodeURIComponent(customerId)}/transactions`,
      { 
        headers,
        params: {
          customerType,
          page: page.toString(),
          pageSize: pageSize.toString()
        }
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  // ==================== URL參數驗證方法 ====================

  /**
   * 驗證URL參數
   */
  validateUrlParams(type: string | null, caseNo: string | null): { isValid: boolean; error?: string } {
    if (!type || type !== 'Q') {
      return { isValid: false, error: '無效的流程類型 (必須是Q)' };
    }
    
    if (!caseNo || !caseNo.startsWith('IBR')) {
      return { isValid: false, error: '無效的案件編號格式' };
    }
    
    return { isValid: true };
  }

  /**
   * 解析URL參數
   */
  parseUrlParams(url: string): { type: string | null; caseNo: string | null } {
    try {
      const urlObj = new URL(url);
      return {
        type: urlObj.searchParams.get('type'),
        caseNo: urlObj.searchParams.get('case')
      };
    } catch {
      return { type: null, caseNo: null };
    }
  }

  // ==================== 原有方法 (保持向後相容) ====================

  /**
   * 匯款搜尋 - 支援多種查詢條件
   * 對應後端: QueryControllerRefactored.searchTransactions()
   */
  searchRemittance(request: RemittanceSearchRequest): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    
    // 轉換前端請求格式為後端期望的格式
    const backendRequest = {
      searchType: request.queryType,
      searchValue: request.queryValue
    };
    
    return this.http.post<QueryApiResponse<any>>(
      `${this.apiUrl}/search`,
      backendRequest,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得匯款詳細狀態
   * 對應後端: QueryControllerRefactored.getTransactionStatus()
   */
  getRemittanceStatus(caseNo: string, verificationCode?: string): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<any>>(
      `${this.apiUrl}/status/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢歷史解款記錄
   */
  getRemittanceHistory(request: RemittanceHistoryRequest): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<QueryApiResponse<any>>(
      `${this.apiUrl}/history`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 發送OTP驗證碼
   */
  sendOtp(request: VerifyOTPModel): Observable<any> {
    if (this.mockMode) {
      // 開發模式：模擬發送成功
      return of({
        success: true,
        message: '驗證碼已發送',
        data: {
          sk: 'MOCK_SK_' + Date.now(),
          txnId: 'MOCK_TXN_' + Date.now(),
          txnDate: new Date().toISOString(),
          dynacPwd: 'XYZ',
          displayPhone: this.maskPhone(request.usePhone || '')
        }
      }).pipe(delay(1000));
    }
    
    const headers = this.getHeaders();
    const apiUrl = this.apiConfig.getApiUrl('query.otp.send', '/api/ibr/query/otp/send');
    
    return this.http.post<{
      success: boolean;
      message: string;
      data?: {
        sk: string;
        txnId: string;
        txnDate: string;
        dynacPwd: string;
        displayPhone: string;
      };
    }>(
      apiUrl,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證OTP
   */
  verifyOtp(request: VerifyOTPModel): Observable<any> {
    if (this.mockMode) {
      // 開發模式：模擬驗證
      const mockOtpCode = 'XYZ123456'; // dynacPwd + 6位數
      const isValid = request.otp === mockOtpCode;
      return of({
        success: isValid,
        message: isValid ? '驗證成功' : '驗證碼錯誤',
        remainingAttempts: isValid ? undefined : 2
      }).pipe(delay(1000));
    }
    
    const headers = this.getHeaders();
    const apiUrl = this.apiConfig.getApiUrl('query.otp.verify', '/api/ibr/query/otp/verify');
    
    return this.http.post<{
      success: boolean;
      message: string;
      data?: {
        sk: string;
        txnId: string;
        txnDate: string;
        dynacPwd: string;
        displayPhone: string;
      };
    }>(
      apiUrl,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢匯款列表（帶分頁）
   */
  searchRemittanceList(params: any): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    let httpParams = new HttpParams()
      .set('queryType', params.queryType)
      .set('queryValue', params.queryValue)
      .set('applicantType', params.applicantType)
      .set('page', params.page.toString())
      .set('size', params.size.toString())
      .set('sortField', params.sortField)
      .set('sortOrder', params.sortOrder);

    if (params.status) {
      httpParams = httpParams.set('status', params.status);
    }
    
    return this.http.get<QueryApiResponse<any>>(
      `${this.apiUrl}/remittance-list`,
      { headers, params: httpParams }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 快速狀態檢查
   */
  quickStatusCheck(caseNo: string): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    const params = { caseNo };
    
    return this.http.get<QueryApiResponse<any>>(
      `${this.apiUrl}/quick-status`,
      { headers, params }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢通知設定
   */
  getNotificationSettings(caseNo: string, applicantId: string, applicantType: string = 'INDIVIDUAL'): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    const params = { applicantId, applicantType };
    
    return this.http.get<QueryApiResponse<any>>(
      `${this.apiUrl}/notifications/${encodeURIComponent(caseNo)}`,
      { headers, params }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證身分證號格式
   */
  validateTaiwanId(taiwanId: string): boolean {
    if (!taiwanId || taiwanId.length !== 10) {
      return false;
    }
    
    const pattern = /^[A-Z][12][0-9]{8}$/;
    return pattern.test(taiwanId);
  }

  /**
   * 驗證統一編號格式
   */
  validateUnifiedNumber(unifiedNumber: string): boolean {
    if (!unifiedNumber || unifiedNumber.length !== 8) {
      return false;
    }
    
    try {
      // 統一編號檢查碼驗證算法
      const weights = [1, 2, 1, 2, 1, 2, 4, 1];
      let sum = 0;
      
      for (let i = 0; i < 8; i++) {
        const digit = parseInt(unifiedNumber.charAt(i));
        if (isNaN(digit)) {
          return false;
        }
        const product = digit * weights[i];
        sum += Math.floor(product / 10) + (product % 10);
      }
      
      return sum % 10 === 0;
    } catch (error) {
      return false;
    }
  }

  /**
   * 驗證案件編號格式
   */
  validateCaseNumber(caseNo: string): boolean {
    if (!caseNo) {
      return false;
    }
    
    const pattern = /^IBR[0-9]{10}$/;
    return pattern.test(caseNo);
  }

  /**
   * 驗證手機號碼格式
   */
  validatePhoneNumber(phone: string): boolean {
    if (!phone) {
      return false;
    }
    
    const pattern = /^09[0-9]{8}$/;
    return pattern.test(phone);
  }

  /**
   * 格式化金額顯示
   */
  formatCurrency(amount: number, currency: string): string {
    try {
      return new Intl.NumberFormat('zh-TW', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(amount);
    } catch (error) {
      return `${currency} ${amount.toLocaleString()}`;
    }
  }

  /**
   * 計算日期差異
   */
  calculateDateDifference(targetDate: string, fromDate?: string): number {
    const target = new Date(targetDate);
    const from = fromDate ? new Date(fromDate) : new Date();
    const diffTime = target.getTime() - from.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 取得狀態顯示文字
   */
  getStatusDisplayText(status: string): string {
    const statusMap: { [key: string]: string } = {
      'PROCESSING': '處理中',
      'COMPLETED': '已完成',
      'PENDING': '待處理',
      'REJECTED': '已拒絕',
      'CANCELLED': '已取消',
      'SUPPLEMENT_NEEDED': '需要補件'
    };
    
    return statusMap[status] || status;
  }

  /**
   * 取得緊急程度顯示文字
   */
  getUrgencyDisplayText(urgency: string): string {
    const urgencyMap: { [key: string]: string } = {
      'HIGH': '緊急',
      'NORMAL': '一般',
      'LOW': '低'
    };
    
    return urgencyMap[urgency] || urgency;
  }

  /**
   * 取得HTTP標頭
   */
  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  /**
   * 統一錯誤處理
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '查詢服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '查詢條件格式錯誤或不完整';
          errorCode = 'INVALID_QUERY_DATA';
          break;
        case 401:
          errorMessage = '查詢權限驗證失敗';
          errorCode = 'AUTHENTICATION_FAILED';
          break;
        case 403:
          errorMessage = '沒有權限執行此查詢';
          errorCode = 'INSUFFICIENT_PERMISSIONS';
          break;
        case 404:
          errorMessage = '查無相關匯款資料';
          errorCode = 'REMITTANCE_NOT_FOUND';
          break;
        case 408:
          errorMessage = '查詢超時，請稍後再試';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 429:
          errorMessage = '查詢過於頻繁，請稍後再試';
          errorCode = 'RATE_LIMIT_EXCEEDED';
          break;
        case 500:
          errorMessage = '系統內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 503:
          errorMessage = '查詢服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        default:
          errorMessage = `查詢服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('匯款查詢服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError(() => ({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    }));
  }
  
  // ==================== 新增的查詢模組方法 ====================
  
  /**
   * 設定查詢資料
   */
  setQueryData(data: any): void {
    this.queryData = data;
  }
  
  /**
   * 取得查詢資料
   */
  getQueryData(): any {
    return this.queryData;
  }
  
  /**
   * 取得查詢結果
   */
  getQueryResults(): Observable<QueryApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<QueryApiResponse<any>>(
      `${this.apiUrl}/results`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }
  
  /**
   * 下載記錄
   */
  downloadRecords(timeRange: string, type: string): Observable<Blob> {
    const headers = new HttpHeaders({
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    return this.http.get(
      `${this.apiUrl}/download`,
      { 
        headers,
        params: { timeRange, type },
        responseType: 'blob'
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }
  
  /**
   * 遮罩手機號碼
   */
  private maskPhone(phone: string): string {
    if (!phone || phone.length < 10) return phone;
    return phone.substring(0, 4) + '***' + phone.substring(7);
  }
}