import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

interface OtpResponse {
  rtnCode: string;
  rtnMessage?: string;
  rtnObj?: any;
}

@Injectable({
  providedIn: 'root'
})
export class OtpService {

  constructor(
    private http: HttpClient
  ) { }

  getOTPApi(data: any): Observable<OtpResponse> {
    // 直接使用後端 API 路徑，不通過 apiConfig
    const url = '/api/ibr/query/otp/send';
    return this.http.post<OtpResponse>(url, data);
  }

  checkOtpApi(data: any): Observable<OtpResponse> {
    // 直接使用後端 API 路徑，不通過 apiConfig
    const url = '/api/ibr/query/otp/verify';
    return this.http.post<OtpResponse>(url, data);
  }
}