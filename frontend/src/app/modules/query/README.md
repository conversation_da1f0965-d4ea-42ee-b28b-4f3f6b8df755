# 查詢模組 (Query Module)

## 概述
查詢模組提供三步驟的匯款解款查詢功能，包含查詢條件輸入、手機簡訊驗證和查詢結果顯示。

## 模組結構

### 元件
1. **QueryStep1Component** - 查詢條件輸入
   - 支援三種查詢方式：身分證號/統一編號、案件編號、手機號碼
   - 自動驗證輸入格式
   - 支援URL參數自動查詢

2. **QueryStep2Component** - 手機簡訊驗證
   - OTP簡訊發送與驗證
   - 60秒重發倒數計時
   - 最多3次驗證嘗試

3. **QueryStep3Component** - 查詢結果列表
   - 分頁顯示查詢結果
   - 狀態篩選與排序功能
   - 線上申請解款功能

### 服務
- **RemittanceQueryService** - 提供查詢相關的API服務

### 路由配置
```typescript
/ibr/query          → 重導向到 /ibr/query/step1
/ibr/query/step1    → 查詢條件輸入
/ibr/query/step2    → 手機簡訊驗證
/ibr/query/step3    → 查詢結果列表
```

## 使用方式

### 1. 基本查詢流程
```typescript
// Step 1: 使用者輸入查詢條件
// Step 2: 系統發送OTP到使用者手機
// Step 3: 驗證成功後顯示查詢結果
```

### 2. URL參數查詢
```
/ibr/query/step1?type=Q&case=IBR2025010101
```

### 3. 查詢類型
- **ID**: 身分證號或統一編號
- **CASE_NO**: 案件編號 (格式: IBR + 10位數字)
- **PHONE**: 手機號碼

## API 端點

### 查詢相關
- `POST /api/v1/query/send-otp` - 發送OTP驗證碼
- `POST /api/v1/query/verify-otp` - 驗證OTP
- `GET /api/v1/query/remittance-list` - 查詢匯款列表
- `GET /api/v1/query/transaction/{caseNo}` - 查詢交易詳情
- `GET /api/v1/query/status/{caseNo}` - 查詢交易狀態

## 樣式設計

### 共用樣式
- 步驟指示器 (Step Indicator)
- Bootstrap 5 卡片布局
- 響應式設計支援手機、平板、桌面

### 顏色配置
- 主色: Bootstrap Primary (#0d6efd)
- 成功: Bootstrap Success (#198754)
- 警告: Bootstrap Warning (#ffc107)
- 錯誤: Bootstrap Danger (#dc3545)

## 驗證規則

### 身分證號
- 格式: 1個英文字母 + 1或2 + 8個數字
- 範例: A123456789

### 統一編號
- 格式: 8個數字
- 範例: 12345678

### 案件編號
- 格式: IBR + 10個數字
- 範例: IBR2025010101

### 手機號碼
- 格式: 09 + 8個數字
- 範例: 0912345678

## 錯誤處理
- 網路錯誤自動重試 (最多2次)
- 30秒請求超時
- 友善的錯誤訊息顯示
- 詳細的錯誤日誌記錄

## 測試
```bash
# 執行單元測試
ng test --include='**/query/**/*.spec.ts'

# 執行E2E測試
ng e2e --spec='**/query/**/*.e2e-spec.ts'
```

## 注意事項
1. OTP驗證碼有效期限為5分鐘
2. 查詢結果會進行分頁處理，預設每頁10筆
3. 所有查詢都需要通過手機驗證才能查看結果
4. 查詢記錄會保留在系統中供稽核使用