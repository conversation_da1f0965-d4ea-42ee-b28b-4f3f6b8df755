import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { QueryInputComponent } from './components/query-input/query-input.component';
import { QueryVerificationComponent } from './components/query-verification/query-verification.component';
import { QueryResultComponent } from './components/query-result/query-result.component';

/**
 * 查詢模組路由配置
 * 
 * 支援三步驟查詢流程：
 * - 查詢輸入: 輸入查詢條件
 * - 簡訊驗證: 手機簡訊驗證
 * - 查詢結果: 查詢結果列表
 */
const routes: Routes = [
  {
    path: '',
    component: QueryInputComponent,
    data: { 
      title: '外匯解款紀錄查詢',
      description: '輸入查詢條件以查詢匯款記錄'
    }
  },
  {
    path: 'verification',
    component: QueryVerificationComponent,
    data: { 
      title: '簡訊驗證',
      description: '驗證手機簡訊以確認身份'
    }
  },
  {
    path: 'result',
    component: QueryResultComponent,
    data: { 
      title: '查詢結果',
      description: '查看匯款記錄列表'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class QueryRoutingModule { }