import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

// Angular Material - 只引入日期選擇器相關模組
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CountdownModule } from 'ngx-countdown';

// 共用模組
import { IbrSharedModule } from '../../@core/shared-2/ibr-shared.module';

// 路由模組
import { QueryRoutingModule } from './query-routing.module';

// 元件
import { QueryInputComponent } from './components/query-input/query-input.component';
import { QueryVerificationComponent } from './components/query-verification/query-verification.component';
import { QueryResultComponent } from './components/query-result/query-result.component';

// 服務
import { RemittanceQueryService } from './services/remittance-query.service';
import { GlobalDataService } from '../../@core/shared/service/global.service';
import { OtpService } from './services/otp.service';

/**
 * 查詢模組
 * 提供匯款解款查詢功能
 * 
 * 優化說明：
 * - 只引入必要的 Angular Material 日期選擇器模組
 * - 保留必要的表單和共用模組
 */
@NgModule({
  declarations: [
    QueryInputComponent,
    QueryVerificationComponent,
    QueryResultComponent,
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    QueryRoutingModule,
    
    // 共用模組 - 包含 IBR header 等共用元件
    IbrSharedModule,
    
    // Angular Material 日期選擇器
    MatDatepickerModule,
    MatNativeDateModule,
    MatInputModule,
    MatFormFieldModule,
    
    // Countdown 模組
    CountdownModule
  ],
  providers: [
    RemittanceQueryService,
    GlobalDataService,
    OtpService
  ]
})
export class QueryModule { }
