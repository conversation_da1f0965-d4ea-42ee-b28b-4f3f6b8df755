/**
 * 自然人解款模組 - 資料驗證服務
 * 
 * @description 提供各種資料格式驗證、業務規則驗證、表單驗證的統一服務
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

// Import validation utilities from models
import { TaiwanIdValidator } from '../models/identity.model';
import { TaiwanMobileValidator } from '../models/verification.model';
import { AmountCalculationHelper } from '../models/amount.model';

/**
 * 驗證結果介面
 */
export interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  
  /** 錯誤訊息列表 */
  errors: ValidationError[];
  
  /** 警告訊息列表 */
  warnings: ValidationWarning[];
  
  /** 建議訊息列表 */
  suggestions: ValidationSuggestion[];
}

/**
 * 驗證錯誤介面
 */
export interface ValidationError {
  /** 欄位名稱 */
  fieldName: string;
  
  /** 錯誤類型 */
  errorType: ValidationErrorType;
  
  /** 錯誤訊息 */
  message: string;
  
  /** 錯誤代碼 */
  errorCode?: string;
  
  /** 相關參數 */
  params?: Record<string, any>;
}

/**
 * 驗證警告介面
 */
export interface ValidationWarning {
  /** 欄位名稱 */
  fieldName: string;
  
  /** 警告類型 */
  warningType: string;
  
  /** 警告訊息 */
  message: string;
  
  /** 是否可忽略 */
  canIgnore: boolean;
}

/**
 * 驗證建議介面
 */
export interface ValidationSuggestion {
  /** 欄位名稱 */
  fieldName: string;
  
  /** 建議訊息 */
  message: string;
  
  /** 建議值 */
  suggestedValue?: string;
  
  /** 建議類型 */
  suggestionType: 'FORMAT' | 'COMPLETION' | 'CORRECTION';
}

/**
 * 複合驗證規則介面
 */
export interface CompositeValidationRule {
  /** 規則名稱 */
  ruleName: string;
  
  /** 規則描述 */
  description: string;
  
  /** 驗證函數 */
  validator: (data: any) => ValidationResult;
  
  /** 是否為必要規則 */
  required: boolean;
  
  /** 規則優先級 */
  priority: number;
}

/**
 * 銀行帳號驗證結果介面
 */
export interface BankAccountValidationResult extends ValidationResult {
  /** 銀行代碼 */
  bankCode?: string;
  
  /** 銀行名稱 */
  bankName?: string;
  
  /** 分行代碼 */
  branchCode?: string;
  
  /** 分行名稱 */
  branchName?: string;
  
  /** 帳號類型 */
  accountType?: 'SAVINGS' | 'CHECKING' | 'TIME_DEPOSIT';
  
  /** 格式化後的帳號 */
  formattedAccountNumber?: string;
}

/**
 * 金額驗證設定介面
 */
export interface AmountValidationConfig {
  /** 最小金額 */
  minAmount: number;
  
  /** 最大金額 */
  maxAmount: number;
  
  /** 允許的幣別列表 */
  allowedCurrencies: string[];
  
  /** 小數位數限制 */
  decimalPlaces: number;
  
  /** 是否允許零金額 */
  allowZero: boolean;
}

/**
 * 驗證錯誤類型枚舉
 */
export enum ValidationErrorType {
  /** 必填欄位 */
  REQUIRED = 'REQUIRED',
  
  /** 格式錯誤 */
  FORMAT = 'FORMAT',
  
  /** 長度錯誤 */
  LENGTH = 'LENGTH',
  
  /** 範圍錯誤 */
  RANGE = 'RANGE',
  
  /** 模式不符 */
  PATTERN = 'PATTERN',
  
  /** 業務規則違反 */
  BUSINESS_RULE = 'BUSINESS_RULE',
  
  /** 邏輯錯誤 */
  LOGIC = 'LOGIC',
  
  /** 相依性錯誤 */
  DEPENDENCY = 'DEPENDENCY',
  
  /** 自訂錯誤 */
  CUSTOM = 'CUSTOM'
}

/**
 * 資料驗證服務
 */
@Injectable({
  providedIn: 'root'
})
export class ValidationService {
  
  // 預設金額驗證設定
  private readonly defaultAmountConfig: AmountValidationConfig = {
    minAmount: 1,
    maxAmount: 500000, // 自然人解款上限50萬
    allowedCurrencies: ['USD', 'EUR', 'JPY', 'GBP', 'AUD', 'CAD', 'SGD', 'HKD', 'CNY'],
    decimalPlaces: 2,
    allowZero: false
  };

  // 台灣銀行代碼列表（簡化版）
  private readonly bankCodes: Record<string, string> = {
    '004': '臺灣銀行',
    '005': '臺灣土地銀行',
    '006': '合作金庫商業銀行',
    '007': '第一商業銀行',
    '008': '華南商業銀行',
    '009': '彰化商業銀行',
    '010': '上海商業儲蓄銀行',
    '011': '台北富邦銀行',
    '012': '台新國際商業銀行',
    '013': '國泰世華商業銀行',
    '017': '兆豐國際商業銀行',
    '048': '王道商業銀行',
    '050': '臺灣中小企業銀行',
    '052': '渣打國際商業銀行',
    '053': '台中商業銀行',
    '054': '京城商業銀行',
    '081': '匯豐(台灣)商業銀行',
    '103': '臺灣新光商業銀行',
    '108': '陽信商業銀行',
    '114': '花旗(台灣)商業銀行',
    '115': '聯邦商業銀行',
    '117': '三信商業銀行',
    '118': '板信商業銀行',
    '119': '凱基商業銀行',
    '822': '中國信託商業銀行',
    '824': '聯邦商業銀行'
  };

  constructor() {}

  // ==================== 基礎欄位驗證 ====================

  /**
   * 驗證台灣身分證字號
   */
  validateTaiwanId(idNumber: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    if (!idNumber) {
      errors.push({
        fieldName: 'idNumber',
        errorType: ValidationErrorType.REQUIRED,
        message: '身分證字號為必填欄位'
      });
    } else {
      // 清理輸入
      const cleanId = idNumber.toUpperCase().trim();
      
      // 長度檢查
      if (cleanId.length !== 10) {
        errors.push({
          fieldName: 'idNumber',
          errorType: ValidationErrorType.LENGTH,
          message: '身分證字號須為10碼'
        });
      }
      
      // 格式檢查
      const pattern = /^[A-Z][12]\d{8}$/;
      if (!pattern.test(cleanId)) {
        errors.push({
          fieldName: 'idNumber',
          errorType: ValidationErrorType.FORMAT,
          message: '身分證字號格式不正確'
        });
        
        suggestions.push({
          fieldName: 'idNumber',
          message: '身分證字號格式：第1碼為英文字母，第2碼為1或2，後8碼為數字',
          suggestionType: 'FORMAT'
        });
      } else {
        // 檢查碼驗證
        if (!TaiwanIdValidator.validate(cleanId)) {
          errors.push({
            fieldName: 'idNumber',
            errorType: ValidationErrorType.BUSINESS_RULE,
            message: '身分證字號檢查碼錯誤'
          });
        }
      }
      
      // 提供發證地區資訊
      if (cleanId.length >= 1) {
        const location = TaiwanIdValidator.getIssueLocation(cleanId);
        if (location) {
          suggestions.push({
            fieldName: 'idNumber',
            message: `發證地區：${location}`,
            suggestionType: 'COMPLETION'
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 驗證台灣手機號碼
   */
  validateTaiwanMobile(phoneNumber: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    if (!phoneNumber) {
      errors.push({
        fieldName: 'mobilePhone',
        errorType: ValidationErrorType.REQUIRED,
        message: '手機號碼為必填欄位'
      });
    } else {
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      
      if (!TaiwanMobileValidator.validate(phoneNumber)) {
        errors.push({
          fieldName: 'mobilePhone',
          errorType: ValidationErrorType.FORMAT,
          message: '手機號碼格式不正確'
        });
        
        suggestions.push({
          fieldName: 'mobilePhone',
          message: '台灣手機號碼格式：09xxxxxxxx (10位數字)',
          suggestionType: 'FORMAT'
        });
      } else {
        // 提供電信業者資訊
        const carrier = TaiwanMobileValidator.getCarrier(phoneNumber);
        if (carrier) {
          suggestions.push({
            fieldName: 'mobilePhone',
            message: `電信業者：${carrier}`,
            suggestionType: 'COMPLETION'
          });
        }
        
        // 提供格式化建議
        const formatted = TaiwanMobileValidator.format(phoneNumber);
        if (formatted !== phoneNumber) {
          suggestions.push({
            fieldName: 'mobilePhone',
            message: '建議格式',
            suggestedValue: formatted,
            suggestionType: 'FORMAT'
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 驗證電子郵件
   */
  validateEmail(email: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    if (!email) {
      // Email通常為選填，不加入必填錯誤
    } else {
      const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      
      if (!emailPattern.test(email)) {
        errors.push({
          fieldName: 'email',
          errorType: ValidationErrorType.FORMAT,
          message: '電子郵件格式不正確'
        });
      }
      
      // 檢查常見錯誤
      if (email.includes('..')) {
        warnings.push({
          fieldName: 'email',
          warningType: 'SUSPICIOUS_FORMAT',
          message: '電子郵件包含連續點號，請確認是否正確',
          canIgnore: true
        });
      }
      
      // 建議常見網域
      const domain = email.split('@')[1];
      if (domain && !['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'].includes(domain.toLowerCase())) {
        const commonDomains = ['gmail.com', 'yahoo.com.tw', 'hotmail.com'];
        const similarDomain = this.findSimilarDomain(domain, commonDomains);
        
        if (similarDomain) {
          suggestions.push({
            fieldName: 'email',
            message: `您是否想輸入：${email.split('@')[0]}@${similarDomain}？`,
            suggestedValue: `${email.split('@')[0]}@${similarDomain}`,
            suggestionType: 'CORRECTION'
          });
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 驗證銀行帳號
   */
  validateBankAccount(accountNumber: string, bankCode?: string): BankAccountValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    if (!accountNumber) {
      errors.push({
        fieldName: 'accountNumber',
        errorType: ValidationErrorType.REQUIRED,
        message: '銀行帳號為必填欄位'
      });
    } else {
      const cleanAccount = accountNumber.replace(/\D/g, '');
      
      // 長度檢查
      if (cleanAccount.length < 10 || cleanAccount.length > 16) {
        errors.push({
          fieldName: 'accountNumber',
          errorType: ValidationErrorType.LENGTH,
          message: '銀行帳號長度須為10-16位數字'
        });
      }
      
      // 格式檢查
      if (!/^\d+$/.test(cleanAccount)) {
        errors.push({
          fieldName: 'accountNumber',
          errorType: ValidationErrorType.FORMAT,
          message: '銀行帳號只能包含數字'
        });
      }
      
      // 銀行特定驗證
      if (bankCode && this.bankCodes[bankCode]) {
        const bankName = this.bankCodes[bankCode];
        
        suggestions.push({
          fieldName: 'accountNumber',
          message: `銀行：${bankName}`,
          suggestionType: 'COMPLETION'
        });
        
        // 特定銀行的帳號格式驗證
        const bankValidation = this.validateBankSpecificAccount(cleanAccount, bankCode);
        errors.push(...bankValidation.errors);
        warnings.push(...bankValidation.warnings);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      bankCode,
      bankName: bankCode ? this.bankCodes[bankCode] : undefined,
      formattedAccountNumber: accountNumber ? this.formatAccountNumber(accountNumber) : undefined
    };
  }

  /**
   * 驗證金額
   */
  validateAmount(amount: number | string, currency = 'TWD', config?: Partial<AmountValidationConfig>): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];
    
    const validationConfig = { ...this.defaultAmountConfig, ...config };
    
    // 轉換為數字
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numericAmount)) {
      errors.push({
        fieldName: 'amount',
        errorType: ValidationErrorType.FORMAT,
        message: '金額必須為有效數字'
      });
    } else {
      // 檢查是否為零
      if (numericAmount === 0 && !validationConfig.allowZero) {
        errors.push({
          fieldName: 'amount',
          errorType: ValidationErrorType.RANGE,
          message: '金額不可為零'
        });
      }
      
      // 檢查範圍
      if (numericAmount < validationConfig.minAmount) {
        errors.push({
          fieldName: 'amount',
          errorType: ValidationErrorType.RANGE,
          message: `金額不可小於 ${validationConfig.minAmount.toLocaleString()}`
        });
      }
      
      if (numericAmount > validationConfig.maxAmount) {
        errors.push({
          fieldName: 'amount',
          errorType: ValidationErrorType.RANGE,
          message: `金額不可大於 ${validationConfig.maxAmount.toLocaleString()}`
        });
      }
      
      // 檢查小數位數
      const decimalPlaces = this.getDecimalPlaces(numericAmount);
      if (decimalPlaces > validationConfig.decimalPlaces) {
        errors.push({
          fieldName: 'amount',
          errorType: ValidationErrorType.FORMAT,
          message: `小數位數不可超過 ${validationConfig.decimalPlaces} 位`
        });
      }
      
      // 檢查幣別
      if (!validationConfig.allowedCurrencies.includes(currency)) {
        errors.push({
          fieldName: 'currency',
          errorType: ValidationErrorType.BUSINESS_RULE,
          message: `不支援的幣別：${currency}`
        });
      }
      
      // 大額警告
      if (numericAmount > validationConfig.maxAmount * 0.8) {
        warnings.push({
          fieldName: 'amount',
          warningType: 'LARGE_AMOUNT',
          message: '您輸入的是大額金額，請再次確認',
          canIgnore: true
        });
      }
      
      // 格式化建議
      if (typeof amount === 'string' && amount !== numericAmount.toLocaleString()) {
        suggestions.push({
          fieldName: 'amount',
          message: '建議格式',
          suggestedValue: numericAmount.toLocaleString(),
          suggestionType: 'FORMAT'
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  // ==================== 複合驗證 ====================

  /**
   * 驗證完整身份資料
   */
  validateIdentityData(data: {
    idNumber: string;
    chineseName: string;
    englishName: string;
    birthDate: string;
    mobilePhone: string;
    email?: string;
  }): ValidationResult {
    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationWarning[] = [];
    const allSuggestions: ValidationSuggestion[] = [];

    // 逐一驗證各欄位
    const idResult = this.validateTaiwanId(data.idNumber);
    const mobileResult = this.validateTaiwanMobile(data.mobilePhone);
    const emailResult = data.email ? this.validateEmail(data.email) : { isValid: true, errors: [], warnings: [], suggestions: [] };
    
    allErrors.push(...idResult.errors, ...mobileResult.errors, ...emailResult.errors);
    allWarnings.push(...idResult.warnings, ...mobileResult.warnings, ...emailResult.warnings);
    allSuggestions.push(...idResult.suggestions, ...mobileResult.suggestions, ...emailResult.suggestions);

    // 姓名驗證
    if (!data.chineseName?.trim()) {
      allErrors.push({
        fieldName: 'chineseName',
        errorType: ValidationErrorType.REQUIRED,
        message: '中文姓名為必填欄位'
      });
    } else if (data.chineseName.length < 2 || data.chineseName.length > 10) {
      allErrors.push({
        fieldName: 'chineseName',
        errorType: ValidationErrorType.LENGTH,
        message: '中文姓名長度須為2-10個字'
      });
    }

    if (!data.englishName?.trim()) {
      allErrors.push({
        fieldName: 'englishName',
        errorType: ValidationErrorType.REQUIRED,
        message: '英文姓名為必填欄位'
      });
    } else if (!/^[A-Za-z\s]+$/.test(data.englishName)) {
      allErrors.push({
        fieldName: 'englishName',
        errorType: ValidationErrorType.FORMAT,
        message: '英文姓名只能包含英文字母和空格'
      });
    }

    // 出生日期驗證
    if (!data.birthDate) {
      allErrors.push({
        fieldName: 'birthDate',
        errorType: ValidationErrorType.REQUIRED,
        message: '出生日期為必填欄位'
      });
    } else {
      const birthDateResult = this.validateBirthDate(data.birthDate);
      allErrors.push(...birthDateResult.errors);
      allWarnings.push(...birthDateResult.warnings);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
      suggestions: allSuggestions
    };
  }

  // ==================== Angular表單驗證器 ====================

  /**
   * 台灣身分證號驗證器
   */
  static taiwanIdValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null; // 讓required validator處理空值
      }
      
      const isValid = TaiwanIdValidator.validate(control.value);
      return isValid ? null : { taiwanId: { value: control.value } };
    };
  }

  /**
   * 台灣手機號碼驗證器
   */
  static taiwanMobileValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      
      const isValid = TaiwanMobileValidator.validate(control.value);
      return isValid ? null : { taiwanMobile: { value: control.value } };
    };
  }

  /**
   * 金額範圍驗證器
   */
  static amountRangeValidator(min: number, max: number): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }
      
      const value = parseFloat(control.value);
      if (isNaN(value)) {
        return { invalidAmount: { value: control.value } };
      }
      
      if (value < min) {
        return { amountTooSmall: { value, min } };
      }
      
      if (value > max) {
        return { amountTooLarge: { value, max } };
      }
      
      return null;
    };
  }

  // ==================== 私有輔助方法 ====================

  /**
   * 驗證出生日期
   */
  private validateBirthDate(birthDate: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const suggestions: ValidationSuggestion[] = [];

    const date = new Date(birthDate);
    const today = new Date();
    
    if (isNaN(date.getTime())) {
      errors.push({
        fieldName: 'birthDate',
        errorType: ValidationErrorType.FORMAT,
        message: '出生日期格式不正確'
      });
    } else {
      // 檢查是否未來日期
      if (date > today) {
        errors.push({
          fieldName: 'birthDate',
          errorType: ValidationErrorType.LOGIC,
          message: '出生日期不可為未來日期'
        });
      }
      
      // 檢查是否過於久遠
      const age = today.getFullYear() - date.getFullYear();
      if (age > 120) {
        warnings.push({
          fieldName: 'birthDate',
          warningType: 'UNUSUAL_AGE',
          message: '年齡超過120歲，請確認出生日期是否正確',
          canIgnore: false
        });
      }
      
      // 檢查是否未成年
      if (age < 18) {
        warnings.push({
          fieldName: 'birthDate',
          warningType: 'MINOR',
          message: '申請人未滿18歲，可能需要法定代理人同意',
          canIgnore: true
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  /**
   * 特定銀行帳號驗證
   */
  private validateBankSpecificAccount(accountNumber: string, bankCode: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 這裡可以加入各銀行特定的帳號格式驗證規則
    // 例如：某些銀行的帳號有特定的檢查碼算法
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: []
    };
  }

  /**
   * 格式化帳號顯示
   */
  private formatAccountNumber(accountNumber: string): string {
    const clean = accountNumber.replace(/\D/g, '');
    // 每4位數字加一個空格
    return clean.replace(/(\d{4})(?=\d)/g, '$1 ');
  }

  /**
   * 取得小數位數
   */
  private getDecimalPlaces(num: number): number {
    const str = num.toString();
    if (str.indexOf('.') !== -1) {
      return str.split('.')[1].length;
    }
    return 0;
  }

  /**
   * 尋找相似網域
   */
  private findSimilarDomain(domain: string, commonDomains: string[]): string | null {
    const threshold = 2; // 允許的編輯距離
    
    for (const commonDomain of commonDomains) {
      if (this.levenshteinDistance(domain.toLowerCase(), commonDomain) <= threshold) {
        return commonDomain;
      }
    }
    
    return null;
  }

  /**
   * 計算編輯距離
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}