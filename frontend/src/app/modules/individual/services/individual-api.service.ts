import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

// 導入基礎服務
import { BaseApiService } from '../../../@core/shared-2/services/base-api.service';
import { ApiConfigService } from '../../../@core/shared-2/services/api-config.service';

// 導入相關模型
import { IdentitySelectionData as IdentityData } from '../models/identity.model';
import { RemittanceInfo } from '../models/remittance.model';
import { AmountCalculationResponse as AmountCalculation } from '../models/amount.model';
import { ApplicationConfirmationRequest as ApplicationSubmission } from '../models/confirmation.model';

// 定義應用程式狀態枚舉
export enum ApplicationStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  PROCESSING = 'PROCESSING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  COMPLETED = 'COMPLETED'
}

/**
 * 條款同意請求介面 (已移到 landing.model.ts，這裡保留相容性)
 */
export interface TermsAgreementRequest {
  /** 台灣身分證號 */
  taiwanId: string;
  /** 同意的條款版本 */
  termsVersion: string;
  /** 同意時間 */
  agreedAt: Date;
  /** IP位址 */
  ipAddress?: string;
  /** 使用者代理 */
  userAgent?: string;
  /** 同意方式 */
  agreementMethod?: string;
}

/**
 * 條款同意回應介面
 */
export interface TermsAgreementResponse {
  /** 同意記錄ID */
  agreementId: string;
  /** 是否成功 */
  success: boolean;
  /** 同意時間 */
  agreedAt: Date;
  /** 有效期限 */
  expiryDate?: Date;
  /** 訊息 */
  message: string;
}

/**
 * 身份驗證請求介面 (更新為符合後端DTO)
 */
export interface IdentityVerificationRequest {
  /** 台灣身分證號 */
  taiwanId: string;
  /** 姓名 */
  name: string;
  /** 生日 (可選) */
  birthDate?: Date;
  /** 手機號碼 */
  phoneNumber?: string;
  /** 驗證類型 */
  verificationType?: 'BASIC' | 'ENHANCED' | 'FULL';
  /** 驗證來源IP */
  ipAddress?: string;
  /** 使用者代理 */
  userAgent?: string;
}

/**
 * 身份驗證回應介面 (更新為符合後端DTO)
 */
export interface IdentityVerificationResponse {
  /** 驗證是否成功 */
  verified: boolean;
  /** 驗證結果代碼 */
  resultCode: string;
  /** 驗證結果訊息 */
  message: string;
  /** 驗證時間 */
  verificationTime: Date;
  /** 驗證ID (用於後續流程追蹤) */
  verificationId: string;
  /** 驗證詳情 */
  details?: {
    /** 身分證號驗證結果 */
    idNumberValid?: boolean;
    /** 姓名驗證結果 */
    nameMatched?: boolean;
    /** 生日驗證結果 */
    birthDateMatched?: boolean;
    /** 手機號碼驗證結果 */
    phoneNumberValid?: boolean;
    /** 政府資料庫驗證結果 */
    governmentDataVerified?: boolean;
    /** 黑名單檢查結果 */
    blacklistClean?: boolean;
    /** 信用評分 (可選) */
    creditScore?: number;
  };
  /** 下一步建議動作 */
  nextActions?: string[];
  /** 驗證有效期限 */
  expiryTime?: Date;
  /** 風險等級 */
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

/**
 * OTP驗證請求介面
 */
export interface OtpVerificationRequest {
  /** 手機號碼 */
  phoneNumber: string;
  /** OTP驗證碼 */
  otpCode?: string;
  /** 操作類型 */
  action: 'SEND' | 'VERIFY';
  /** 身份驗證ID */
  verificationId: string;
  /** 語言偏好 */
  language?: 'zh-TW' | 'en-US';
}

/**
 * OTP驗證回應介面
 */
export interface OtpVerificationResponse {
  /** 驗證結果 */
  isVerified: boolean;
  /** OTP會話ID */
  sessionId: string;
  /** 有效期限 */
  expiryTime?: Date;
  /** 剩餘嘗試次數 */
  remainingAttempts?: number;
  /** 下次可發送時間 */
  nextSendTime?: Date;
  /** 訊息 */
  message: string;
}

/**
 * 匯款查詢請求介面
 */
export interface RemittanceSearchRequest {
  /** 身份證號 */
  idNumber: string;
  /** 受益人姓名 */
  beneficiaryName: string;
  /** 匯款性質 */
  remittanceType?: string;
  /** 匯款日期範圍 */
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  /** 分頁設定 */
  pagination?: {
    page: number;
    pageSize: number;
  };
}

/**
 * 匯款查詢回應介面
 */
export interface RemittanceSearchResponse {
  /** 匯款列表 */
  remittances: RemittanceInfo[];
  /** 總筆數 */
  totalCount: number;
  /** 當前頁數 */
  currentPage: number;
  /** 每頁筆數 */
  pageSize: number;
  /** 總頁數 */
  totalPages: number;
  /** 查詢時間 */
  searchTime: Date;
}

/**
 * 金額計算請求介面
 */
export interface AmountCalculationRequest {
  /** 匯款金額 */
  remittanceAmount: number;
  /** 匯款幣別 */
  currency: string;
  /** 匯款性質 */
  remittanceType: string;
  /** 受益人銀行 */
  beneficiaryBank?: string;
  /** 計算選項 */
  calculationOptions?: {
    /** 包含手續費 */
    includeFees: boolean;
    /** 包含稅費 */
    includeTax: boolean;
    /** 即時匯率 */
    useRealTimeRate: boolean;
  };
}

/**
 * 申請提交請求介面
 */
export interface ApplicationSubmitRequest {
  /** 申請資料 */
  applicationData: ApplicationSubmission;
  /** 確認項目 */
  confirmations: {
    /** 確認資料正確 */
    dataAccuracy: boolean;
    /** 確認條款同意 */
    termsAgreed: boolean;
    /** 確認身份驗證 */
    identityVerified: boolean;
    /** 確認匯款資訊 */
    remittanceConfirmed: boolean;
  };
  /** 數位簽章 */
  digitalSignature?: string;
  /** 提交時間 */
  submitTime: Date;
}

/**
 * 申請提交回應介面
 */
export interface ApplicationSubmitResponse {
  /** 申請編號 */
  applicationId: string;
  /** 申請狀態 */
  status: ApplicationStatus;
  /** 提交時間 */
  submitTime: Date;
  /** 預計處理時間 */
  estimatedProcessTime?: Date;
  /** 追蹤號碼 */
  trackingNumber: string;
  /** 收據URL */
  receiptUrl?: string;
  /** 下一步驟 */
  nextSteps: string[];
  /** 聯絡資訊 */
  contactInfo: {
    /** 客服電話 */
    phoneNumber: string;
    /** 客服信箱 */
    email: string;
    /** 服務時間 */
    serviceHours: string;
  };
}

/**
 * 申請狀態查詢回應介面
 */
export interface ApplicationStatusResponse {
  /** 申請編號 */
  applicationId: string;
  /** 當前狀態 */
  currentStatus: ApplicationStatus;
  /** 狀態歷史 */
  statusHistory: {
    status: ApplicationStatus;
    timestamp: Date;
    description: string;
    processedBy?: string;
  }[];
  /** 處理進度 */
  progressPercentage: number;
  /** 預計完成時間 */
  estimatedCompletionTime?: Date;
  /** 需要的操作 */
  requiredActions?: {
    action: string;
    description: string;
    dueDate?: Date;
  }[];
  /** 最新更新 */
  lastUpdate: {
    timestamp: Date;
    description: string;
    updatedBy?: string;
  };
}

/**
 * 自然人模組API服務
 * 
 * 提供完整的自然人IBR申請API整合，包括：
 * - 條款同意處理
 * - 身份驗證與OTP驗證
 * - 匯款查詢與金額計算
 * - 申請提交與狀態追蹤
 * 
 * 支援 Mock/Real API 切換機制
 * 
 * @example
 * ```typescript
 * constructor(private individualApi: IndividualApiService) {}
 * 
 * // 檢查當前模式
 * if (this.individualApi.isMockMode) {
 *   console.log('使用 Mock 資料');
 * }
 * 
 * // API 呼叫會自動根據配置切換
 * async agreeTerms() {
 *   const request: TermsAgreementRequest = {
 *     termsVersion: '1.0',
 *     agreedAt: new Date()
 *   };
 *   
 *   try {
 *     const result = await this.individualApi.agreeToTerms(request).toPromise();
 *     console.log('條款同意成功', result.agreementId);
 *   } catch (error) {
 *     console.error('條款同意失敗', error);
 *   }
 * }
 * ```
 */
@Injectable({
  providedIn: 'root'
})
export class IndividualApiService extends BaseApiService {
  
  /**
   * 取得當前案件編號 (從狀態服務或session)
   * @private
   */
  private getCaseNo(): string {
    // TODO: 實際應該從 IbrStateService 或 sessionStorage 取得
    return sessionStorage.getItem('ibr_case_no') || '';
  }
  
  private readonly MODULE_NAME = 'individual';

  constructor(
    protected http: HttpClient,
    protected apiConfig: ApiConfigService
  ) {
    super(http, apiConfig);
    console.log('Individual API Service initialized', {
      mockMode: this.isMockMode,
      module: this.MODULE_NAME
    });
  }

  /**
   * 條款同意 (使用共用API)
   * 
   * @param request 條款同意請求
   * @returns 條款同意結果Observable
   */
  agreeToTerms(request: TermsAgreementRequest): Observable<TermsAgreementResponse> {
    // 使用共用API，加上 customerType 參數
    const requestWithType = { ...request, customerType: 'INDIVIDUAL' };
    return this.post<TermsAgreementResponse>(
      this.MODULE_NAME, 
      'termsAgree', 
      requestWithType
    );
  }

  /**
   * 檢查條款同意狀態
   * 
   * @param userId 使用者ID
   * @returns 條款同意狀態Observable
   */
  checkTermsStatus(userId?: string): Observable<{
    isAgreed: boolean;
    agreementId?: string;
    agreedAt?: Date;
    expiryDate?: Date;
  }> {
    return this.get<{
      isAgreed: boolean;
      agreementId?: string;
      agreedAt?: Date;
      expiryDate?: Date;
    }>(this.MODULE_NAME, 'terms', userId ? { userId } : {});
  }

  /**
   * 取得條款內容 (使用共用API)
   * 
   * @param version 條款版本
   * @param language 語言
   * @returns 條款內容Observable
   */
  getTermsContent(version?: string, language = 'zh_TW'): Observable<any> {
    const params: any = { 
      customerType: 'INDIVIDUAL',
      language 
    };
    if (version) {
      params.version = version;
    }
    
    return this.get<any>(
      this.MODULE_NAME,
      'terms',
      params
    );
  }

  /**
   * 取得最新條款版本
   * 
   * @returns 最新條款版本Observable
   */
  getLatestTermsVersion(): Observable<string> {
    return this.get<{ version: string }>(this.MODULE_NAME, 'terms')
      .pipe(map(response => response.version));
  }

  /**
   * 身份驗證 (使用共用API)
   * 
   * @param request 身份驗證請求
   * @returns 身份驗證結果Observable
   */
  verifyIdentity(request: IdentityVerificationRequest): Observable<IdentityVerificationResponse> {
    // 使用共用API的資料驗證端點
    const requestWithType = { 
      ...request, 
      customerType: 'INDIVIDUAL',
      caseNo: this.getCaseNo() // 從狀態服務或session取得
    };
    return this.post<IdentityVerificationResponse>(
      this.MODULE_NAME,
      'verify',
      requestWithType
    );
  }

  /**
   * OTP驗證操作 (使用共用API)
   * 
   * @param request OTP驗證請求
   * @returns OTP驗證結果Observable
   */
  handleOtpVerification(request: OtpVerificationRequest): Observable<OtpVerificationResponse> {
    // 使用共用API的OTP端點
    const endpointName = request.action === 'SEND' ? 'otpSend' : 'otpVerify';
    
    const requestWithType = {
      ...request,
      customerType: 'INDIVIDUAL',
      caseNo: this.getCaseNo()
    };
    
    return this.post<OtpVerificationResponse>(
      this.MODULE_NAME,
      endpointName,
      requestWithType
    );
  }

  /**
   * 驗證身分證號格式
   * 
   * @param taiwanId 身分證號
   * @returns 格式驗證結果Observable
   */
  validateIdFormat(taiwanId: string): Observable<boolean> {
    // 在 Mock 模式下，本地驗證
    if (this.isMockMode) {
      return new Observable(observer => {
        const isValid = this.validateTaiwanIdNumber(taiwanId);
        observer.next(isValid);
        observer.complete();
      });
    }
    
    return this.post<boolean>(
      this.MODULE_NAME,
      'verify',
      { taiwanId, validateOnly: true }
    );
  }

  /**
   * 取得驗證狀態
   * 
   * @param taiwanId 身分證號
   * @param remittanceId 匯款ID (可選)
   * @returns 驗證狀態Observable
   */
  getVerificationStatus(taiwanId: string, remittanceId?: string): Observable<any> {
    const params: any = { taiwanId };
    if (remittanceId) {
      params.remittanceId = remittanceId;
    }
    
    return this.get<any>(this.MODULE_NAME, 'verify', params);
  }

  /**
   * 驗證銀行帳戶
   * 
   * @param request 銀行帳戶驗證請求
   * @returns 銀行帳戶驗證結果Observable
   */
  validateBankAccount(request: {
    bankCode?: string;
    branchCode: string;
    accountNumber: string;
    accountHolder?: string;
  }): Observable<{
    valid: boolean;
    message: string;
    accountHolder?: string;
  }> {
    // Mock 模式下的簡單驗證
    if (this.isMockMode) {
      return new Observable(observer => {
        setTimeout(() => {
          observer.next({
            valid: true,
            message: '銀行帳戶驗證成功',
            accountHolder: request.accountHolder || '王小明'
          });
          observer.complete();
        }, 500);
      });
    }
    
    return this.post<any>(this.MODULE_NAME, 'verify', {
      ...request,
      verificationType: 'BANK_ACCOUNT'
    });
  }

  /**
   * 搜尋匯款記錄 (使用共用API)
   * 
   * @param request 匯款搜尋請求
   * @returns 匯款搜尋結果Observable
   */
  searchRemittances(request: RemittanceSearchRequest): Observable<RemittanceSearchResponse> {
    // 使用共用查詢API
    const requestWithType = { 
      ...request, 
      customerType: 'INDIVIDUAL' 
    };
    // 查詢功能使用 query 模組
    return this.post<RemittanceSearchResponse>(
      'query',
      'search',
      requestWithType
    );
  }

  /**
   * 取得匯款詳細資訊 (使用共用API)
   * 
   * @param remittanceId 匯款ID或案件編號
   * @returns 匯款詳細資訊Observable
   */
  getRemittanceDetails(remittanceId: string): Observable<RemittanceInfo> {
    // 使用共用查詢API
    // 查詢功能使用 query 模組，但由於 API 有路徑參數，需要特別處理
    return this.get<RemittanceInfo>(
      'query',
      `transaction/${remittanceId}`
    );
  }

  /**
   * 金額計算 (使用共用API)
   * 
   * @param request 金額計算請求
   * @returns 金額計算結果Observable
   */
  calculateAmount(request: AmountCalculationRequest): Observable<AmountCalculation> {
    // 使用共用API的費用計算功能
    const requestWithType = { 
      ...request, 
      customerType: 'INDIVIDUAL',
      caseNo: this.getCaseNo()
    };
    return this.post<AmountCalculation>(
      this.MODULE_NAME,
      'remittanceConfirm',
      requestWithType
    );
  }

  /**
   * 提交申請 (使用共用API)
   * 
   * @param request 申請提交請求
   * @returns 申請提交結果Observable
   */
  submitApplication(request: ApplicationSubmitRequest): Observable<ApplicationSubmitResponse> {
    // 使用共用API的提交端點
    const requestWithType = { 
      ...request.applicationData,
      customerType: 'INDIVIDUAL',
      caseNo: this.getCaseNo(),
      confirmations: request.confirmations,
      submitTime: request.submitTime
    };
    return this.post<ApplicationSubmitResponse>(
      this.MODULE_NAME,
      'submit',
      requestWithType
    );
  }

  /**
   * 查詢申請狀態 (使用共用API)
   * 
   * @param applicationId 申請編號
   * @returns 申請狀態Observable
   */
  getApplicationStatus(applicationId: string): Observable<ApplicationStatusResponse> {
    // 使用共用查詢API
    // 查詢功能使用 query 模組，但由於 API 有路徑參數，需要特別處理
    return this.get<ApplicationStatusResponse>(
      'query',
      `status/${applicationId}`
    );
  }

  /**
   * 取得申請歷史記錄
   * 
   * @param idNumber 身份證號
   * @param pageSize 每頁筆數
   * @param page 頁數
   * @returns 申請歷史記錄Observable
   */
  getApplicationHistory(
    idNumber: string,
    pageSize = 10,
    page = 1
  ): Observable<{
    applications: {
      applicationId: string;
      submitTime: Date;
      status: ApplicationStatus;
      remittanceAmount: number;
      currency: string;
    }[];
    totalCount: number;
    currentPage: number;
    totalPages: number;
  }> {
    return this.get<any>(this.MODULE_NAME, 'remittanceDetail', {
      idNumber,
      pageSize: pageSize.toString(),
      page: page.toString(),
      type: 'history'
    });
  }

  /**
   * 取得支援的銀行列表 (使用共用API)
   * 
   * @returns 銀行列表Observable
   */
  getSupportedBanks(): Observable<{
    bankCode: string;
    bankName: string;
    isActive: boolean;
    supportedCurrencies: string[];
  }[]> {
    // Mock 模式下返回預設銀行列表
    if (this.isMockMode) {
      return new Observable(observer => {
        observer.next([
          { bankCode: '012', bankName: '台北富邦商業銀行', isActive: true, supportedCurrencies: ['TWD', 'USD', 'EUR'] },
          { bankCode: '013', bankName: '國泰世華商業銀行', isActive: true, supportedCurrencies: ['TWD', 'USD'] },
          { bankCode: '017', bankName: '兆豐國際商業銀行', isActive: true, supportedCurrencies: ['TWD', 'USD', 'EUR', 'JPY'] },
          { bankCode: '812', bankName: '台新國際商業銀行', isActive: true, supportedCurrencies: ['TWD', 'USD'] },
          { bankCode: '808', bankName: '玉山商業銀行', isActive: true, supportedCurrencies: ['TWD', 'USD', 'EUR'] }
        ]);
        observer.complete();
      });
    }
    
    // 使用共用API
    return this.get<any[]>(
      this.MODULE_NAME,
      'banks'
    );
  }

  /**
   * 取得匯率資訊 (使用共用API)
   * 
   * @param fromCurrency 來源幣別
   * @param toCurrency 目標幣別
   * @returns 匯率資訊Observable
   */
  getExchangeRate(fromCurrency: string, toCurrency: string = 'TWD'): Observable<{
    fromCurrency: string;
    toCurrency: string;
    rate: number;
    timestamp: Date;
    validity: number; // 有效期限（秒）
  }> {
    // 使用共用API
    return this.get<any>(
      this.MODULE_NAME,
      'exchangeRate',
      {
        currency: fromCurrency,
        customerType: 'INDIVIDUAL'
      }
    );
  }

  /**
   * 本地台灣身分證號驗證
   * @private
   */
  private validateTaiwanIdNumber(id: string): boolean {
    if (!id || !/^[A-Z][12]\d{8}$/.test(id)) {
      return false;
    }

    const letterMapping: { [key: string]: number } = {
      A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, G: 16, H: 17, I: 34,
      J: 18, K: 19, L: 20, M: 21, N: 22, O: 35, P: 23, Q: 24, R: 25,
      S: 26, T: 27, U: 28, V: 29, W: 32, X: 30, Y: 31, Z: 33
    };

    const firstLetter = id.charAt(0);
    const letterValue = letterMapping[firstLetter];
    
    if (!letterValue) return false;

    let sum = Math.floor(letterValue / 10) + (letterValue % 10) * 9;

    for (let i = 1; i < 9; i++) {
      sum += parseInt(id.charAt(i)) * (9 - i);
    }

    sum += parseInt(id.charAt(9));

    return sum % 10 === 0;
  }
}