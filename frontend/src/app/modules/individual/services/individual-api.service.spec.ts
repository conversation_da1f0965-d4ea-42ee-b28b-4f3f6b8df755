import { TestBed } from '@angular/core/testing';
import { provideHttpClientTesting, HttpTestingController } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { IndividualApiService, RemittanceSearchRequest, OtpVerificationRequest, TermsAgreementRequest } from './individual-api.service';

describe('IndividualApiService', () => {
  let service: IndividualApiService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        IndividualApiService,
        provideHttpClient(),
        provideHttpClientTesting()
      ]
    });
    
    service = TestBed.inject(IndividualApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('條款同意', () => {
    it('應該正確處理條款同意', () => {
      const mockRequest: TermsAgreementRequest = {
        termsVersion: '1.0',
        agreedAt: new Date()
      };

      const mockResponse = {
        agreementId: 'AGR001',
        success: true,
        agreedAt: new Date(),
        message: '條款同意成功'
      };

      service.agreeToTerms(mockRequest).subscribe(response => {
        expect(response.success).toBe(true);
        expect(response.agreementId).toBe('AGR001');
      });

      const req = httpMock.expectOne(req => req.url.includes('/terms/agree'));
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('匯款查詢', () => {
    it('應該正確搜尋匯款記錄', () => {
      const mockRequest: RemittanceSearchRequest = {
        idNumber: 'A123456789',
        beneficiaryName: '測試用戶'
      };

      const mockResponse = {
        remittances: [
          {
            id: 'REM001',
            senderName: 'JOHN DOE',
            amount: 10000,
            currency: 'USD'
          }
        ],
        totalCount: 1,
        currentPage: 1,
        pageSize: 10,
        totalPages: 1,
        searchTime: new Date()
      };

      service.searchRemittances(mockRequest).subscribe(response => {
        expect(response.remittances.length).toBe(1);
        expect(response.totalCount).toBe(1);
      });

      const req = httpMock.expectOne(req => req.url.includes('/remittance/search'));
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('OTP驗證', () => {
    it('應該正確發送OTP', () => {
      const mockRequest: OtpVerificationRequest = {
        phoneNumber: '0912345678',
        action: 'SEND',
        verificationId: 'VER001'
      };

      const mockResponse = {
        isVerified: false,
        sessionId: 'SES001',
        message: 'OTP已發送'
      };

      service.handleOtpVerification(mockRequest).subscribe(response => {
        expect(response.sessionId).toBe('SES001');
        expect(response.message).toBe('OTP已發送');
      });

      const req = httpMock.expectOne(req => req.url.includes('/verification/otp/send'));
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該正確驗證OTP', () => {
      const mockRequest: OtpVerificationRequest = {
        phoneNumber: '0912345678',
        otpCode: '123456',
        action: 'VERIFY',
        verificationId: 'VER001'
      };

      const mockResponse = {
        isVerified: true,
        sessionId: 'SES001',
        message: 'OTP驗證成功'
      };

      service.handleOtpVerification(mockRequest).subscribe(response => {
        expect(response.isVerified).toBe(true);
        expect(response.message).toBe('OTP驗證成功');
      });

      const req = httpMock.expectOne(req => req.url.includes('/verification/otp/verify'));
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('錯誤處理', () => {
    it('應該處理HTTP錯誤', () => {
      const mockRequest: RemittanceSearchRequest = {
        idNumber: 'A123456789',
        beneficiaryName: '測試用戶'
      };

      service.searchRemittances(mockRequest).subscribe({
        next: () => {},
        error: (error) => {
          expect(error).toBeDefined();
          expect(error.message).toContain('錯誤');
        }
      });

      const req = httpMock.expectOne(req => req.url.includes('/remittance/search'));
      req.flush('伺服器錯誤', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('銀行資訊', () => {
    it('應該取得支援的銀行列表', () => {
      const mockResponse = [
        {
          bankCode: '004',
          bankName: '台灣銀行',
          isActive: true,
          supportedCurrencies: ['USD', 'EUR', 'JPY']
        }
      ];

      service.getSupportedBanks().subscribe(response => {
        expect(response.length).toBe(1);
        expect(response[0].bankCode).toBe('004');
      });

      const req = httpMock.expectOne(req => req.url.includes('/banks'));
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('匯率查詢', () => {
    it('應該取得匯率資訊', () => {
      const mockResponse = {
        fromCurrency: 'USD',
        toCurrency: 'TWD',
        rate: 31.5,
        timestamp: new Date(),
        validity: 300
      };

      service.getExchangeRate('USD', 'TWD').subscribe(response => {
        expect(response.rate).toBe(31.5);
        expect(response.fromCurrency).toBe('USD');
      });

      const req = httpMock.expectOne(req => req.url.includes('/exchange-rate'));
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });
});