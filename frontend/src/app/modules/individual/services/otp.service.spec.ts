/**
 * OTP Service 單元測試
 * 
 * @description 測試OTP Service的雙重驗證功能
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpErrorResponse } from '@angular/common/http';

import { OtpService, OtpSessionState, CountdownState } from './otp.service';
import { TestUtils, TEST_CONSTANTS, TEST_DESCRIPTIONS } from '../../../testing/test-utils';

import {
  OtpVerificationRequest,
  OtpVerifyRequest,
  VerificationType,
  OtpStatus,
  OtpErrorType,
  TaiwanMobileValidator
} from '../models/verification.model';

describe('OtpService', () => {
  let service: OtpService;
  let httpMock: HttpTestingController;

  const API_BASE_URL = TEST_CONSTANTS.API_BASE_URL + '/individual/verification/otp';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [OtpService]
    });

    service = TestBed.inject(OtpService);
    httpMock = TestBed.inject(HttpTestingController);

    // 清除任何現有的儲存狀態
    sessionStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    sessionStorage.clear();
  });

  describe('基本功能測試', () => {
    it(TEST_DESCRIPTIONS.SERVICE.SHOULD_BE_CREATED, () => {
      expect(service).toBeTruthy();
    });

    it('應該初始化正確的初始狀態', () => {
      const state = service.getCurrentSessionState();
      
      expect(state.hasActiveSession).toBeFalse();
      expect(state.remainingAttempts).toBe(3);
      expect(state.remainingResends).toBe(3);
      expect(state.isLocked).toBeFalse();
      expect(state.status).toBe(OtpStatus.PENDING);
    });

    it('應該提供會話狀態Observable', (done) => {
      service.sessionState$.subscribe(state => {
        expect(state).toBeTruthy();
        expect(state.hasActiveSession).toBeFalse();
        done();
      });
    });

    it('應該提供倒數計時狀態Observable', (done) => {
      service.countdownState$.subscribe(state => {
        expect(state).toBeTruthy();
        expect(state.isActive).toBeFalse();
        done();
      });
    });

    it('應該提供載入狀態Observable', (done) => {
      service.loading$.subscribe(loading => {
        expect(typeof loading).toBe('boolean');
        done();
      });
    });
  });

  describe('狀態管理', () => {
    it('應該正確儲存和載入會話狀態到sessionStorage', () => {
      const testState: Partial<OtpSessionState> = {
        hasActiveSession: true,
        verificationToken: 'test-token',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        status: OtpStatus.SENT
      };

      // 觸發狀態更新
      service['updateSessionState'](testState);

      // 檢查sessionStorage是否有儲存
      const savedState = sessionStorage.getItem('otp-session-state');
      expect(savedState).toBeTruthy();
      
      const parsedState = JSON.parse(savedState!);
      expect(parsedState.hasActiveSession).toBeTrue();
      expect(parsedState.verificationToken).toBe('test-token');
    });

    it('應該在初始化時從sessionStorage載入狀態', () => {
      const mockState = {
        hasActiveSession: true,
        verificationToken: 'stored-token',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        expiresAt: new Date(Date.now() + 300000).toISOString(), // 5分鐘後過期
        status: OtpStatus.SENT
      };

      sessionStorage.setItem('otp-session-state', JSON.stringify(mockState));

      // 重新建立服務實例
      const newService = TestBed.inject(OtpService);
      const currentState = newService.getCurrentSessionState();

      expect(currentState.hasActiveSession).toBeTrue();
      expect(currentState.verificationToken).toBe('stored-token');
      expect(currentState.mobilePhone).toBe(TEST_CONSTANTS.VALID_PHONE_NUMBER);
    });

    it('應該清除過期的會話狀態', () => {
      const expiredState = {
        hasActiveSession: true,
        verificationToken: 'expired-token',
        expiresAt: new Date(Date.now() - 1000).toISOString(), // 已過期
        status: OtpStatus.SENT
      };

      sessionStorage.setItem('otp-session-state', JSON.stringify(expiredState));

      // 重新建立服務實例
      const newService = TestBed.inject(OtpService);
      const currentState = newService.getCurrentSessionState();

      expect(currentState.hasActiveSession).toBeFalse();
      expect(currentState.verificationToken).toBeUndefined();
    });

    it('應該能夠重置會話狀態', () => {
      // 先設定一些狀態
      service['updateSessionState']({
        hasActiveSession: true,
        verificationToken: 'test-token',
        status: OtpStatus.SENT
      });

      // 重置狀態
      service.resetSession();

      const state = service.getCurrentSessionState();
      expect(state.hasActiveSession).toBeFalse();
      expect(state.verificationToken).toBeUndefined();
      expect(state.status).toBe(OtpStatus.PENDING);
      
      // 檢查sessionStorage是否已清除
      const savedState = sessionStorage.getItem('otp-session-state');
      expect(savedState).toBeNull();
    });
  });

  describe('手機號碼驗證', () => {
    it('應該驗證有效的台灣手機號碼', (done) => {
      const validPhones = [
        '0912345678',
        '0987654321', 
        '0955123456'
      ];

      validPhones.forEach(phone => {
        service.validateMobilePhone(phone).subscribe(result => {
          expect(result.isValid).toBeTrue();
          expect(result.numberType).toBe('MOBILE');
          expect(result.countryCode).toBe('+886');
        });
      });
      
      done();
    });

    it('應該拒絕無效的手機號碼', (done) => {
      const invalidPhones = [
        '1234567890', // 不是09開頭
        '091234567',  // 太短
        '09123456789', // 太長
        '0812345678',  // 錯誤前綴
        'abcd123456'   // 包含字母
      ];

      invalidPhones.forEach(phone => {
        service.validateMobilePhone(phone).subscribe(result => {
          expect(result.isValid).toBeFalse();
          expect(result.numberType).toBe('UNKNOWN');
        });
      });
      
      done();
    });

    it('應該正確格式化手機號碼', () => {
      const phoneNumber = '0912345678';
      const formatted = service.formatMobileNumber(phoneNumber);
      expect(formatted).toBe('0912-345-678');
    });

    it('應該能夠遮蔽敏感的手機號碼', () => {
      const phoneNumber = '0912345678';
      const masked = service.formatMobileNumber(phoneNumber, true);
      expect(masked).toBe('0912-***-678');
    });
  });

  describe('發送OTP功能', () => {
    it('應該成功發送OTP驗證碼', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        verificationToken: 'OTP_TOKEN_123',
        messageId: 'MSG123456',
        sentAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        remainingResends: 2
      });

      service.sendOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.verificationToken).toBe('OTP_TOKEN_123');
        
        // 檢查會話狀態是否已更新
        const sessionState = service.getCurrentSessionState();
        expect(sessionState.hasActiveSession).toBeTrue();
        expect(sessionState.verificationToken).toBe('OTP_TOKEN_123');
        expect(sessionState.mobilePhone).toBe(TEST_CONSTANTS.VALID_PHONE_NUMBER);
        expect(sessionState.status).toBe(OtpStatus.SENT);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockRequest);
      req.flush(mockResponse);
    });

    it('應該拒絕無效的手機號碼格式', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: '1234567890', // 無效格式
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      service.sendOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.INVALID_PHONE);
          expect(error.error.message).toBe('手機號碼格式不正確');
        }
      });

      // 不應該發送HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/send`);
    });

    it('應該檢查鎖定狀態並拒絕發送', () => {
      // 先設定鎖定狀態
      service['updateSessionState']({
        isLocked: true,
        lockedUntil: new Date(Date.now() + 10 * 60 * 1000) // 10分鐘後解鎖
      });

      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      service.sendOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.MAX_ATTEMPTS_EXCEEDED);
          expect(error.error.message).toContain('帳號已鎖定');
        }
      });

      // 不應該發送HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/send`);
    });

    it('應該簡化發送OTP', () => {
      const idNumber = TEST_CONSTANTS.VALID_TAIWAN_ID;
      const phoneNumber = TEST_CONSTANTS.VALID_PHONE_NUMBER;
      const mockResponse = TestUtils.createMockApiResponse(true, {
        verificationToken: 'OTP_TOKEN_123'
      });

      service.sendOtp(idNumber, phoneNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.idNumber).toBe(idNumber);
      expect(req.request.body.mobilePhone).toBe(phoneNumber);
      expect(req.request.body.verificationType).toBe(VerificationType.IDENTITY_VERIFICATION);
      req.flush(mockResponse);
    });
  });

  describe('驗證OTP功能', () => {
    beforeEach(() => {
      // 設定活躍會話狀態
      service['updateSessionState']({
        hasActiveSession: true,
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000),
        remainingAttempts: 3,
        status: OtpStatus.SENT
      });
    });

    it('應該成功驗證OTP驗證碼', () => {
      const mockRequest: OtpVerifyRequest = {
        verificationCode: TEST_CONSTANTS.VALID_OTP_CODE,
        sessionToken: 'session-123',
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        requestId: 'verify-123',
        timestamp: new Date().toISOString()
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        status: OtpStatus.VERIFIED,
        verificationId: 'VER123456',
        remainingAttempts: 3
      });

      service.verifyOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result.status).toBe(OtpStatus.VERIFIED);
        
        // 檢查會話狀態是否已更新
        const sessionState = service.getCurrentSessionState();
        expect(sessionState.status).toBe(OtpStatus.VERIFIED);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/verify`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.verificationCode).toBe(TEST_CONSTANTS.VALID_OTP_CODE);
      req.flush(mockResponse);
    });

    it('應該處理驗證碼錯誤', () => {
      const mockRequest: OtpVerifyRequest = {
        verificationCode: '000000', // 錯誤的驗證碼
        sessionToken: 'session-123',
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        requestId: 'verify-123',
        timestamp: new Date().toISOString()
      };

      const mockResponse = TestUtils.createMockApiResponse(false, {
        status: OtpStatus.FAILED,
        remainingAttempts: 2
      }, 'OTP驗證碼錯誤');

      service.verifyOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeFalse();
        
        // 檢查會話狀態是否已更新
        const sessionState = service.getCurrentSessionState();
        expect(sessionState.status).toBe(OtpStatus.FAILED);
        expect(sessionState.remainingAttempts).toBe(2);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/verify`);
      req.flush(mockResponse);
    });

    it('應該在超過最大嘗試次數時鎖定', () => {
      const mockRequest: OtpVerifyRequest = {
        verificationCode: '000000',
        sessionToken: 'session-123',
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        requestId: 'verify-123',
        timestamp: new Date().toISOString()
      };

      const mockResponse = TestUtils.createMockApiResponse(false, {
        status: OtpStatus.LOCKED,
        remainingAttempts: 0
      }, '超過最大嘗試次數');

      service.verifyOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeFalse();
        
        // 檢查會話狀態是否已更新為鎖定
        const sessionState = service.getCurrentSessionState();
        expect(sessionState.status).toBe(OtpStatus.LOCKED);
        expect(sessionState.isLocked).toBeTrue();
        expect(sessionState.lockedUntil).toBeTruthy();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/verify`);
      req.flush(mockResponse);
    });

    it('應該拒絕在無活躍會話時驗證', () => {
      // 清除會話狀態
      service.resetSession();

      const mockRequest: OtpVerifyRequest = {
        verificationCode: TEST_CONSTANTS.VALID_OTP_CODE,
        sessionToken: 'session-123',
        verificationToken: '',
        mobilePhone: '',
        requestId: 'verify-123',
        timestamp: new Date().toISOString()
      };

      service.verifyOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.EXPIRED);
          expect(error.error.message).toContain('驗證會話已過期');
        }
      });

      // 不應該發送HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/verify`);
    });

    it('應該檢查驗證碼過期', () => {
      // 設定過期的會話
      service['updateSessionState']({
        hasActiveSession: true,
        expiresAt: new Date(Date.now() - 1000) // 已過期
      });

      const mockRequest: OtpVerifyRequest = {
        verificationCode: TEST_CONSTANTS.VALID_OTP_CODE,
        sessionToken: 'session-123',
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        requestId: 'verify-123',
        timestamp: new Date().toISOString()
      };

      service.verifyOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.EXPIRED);
          expect(error.error.message).toContain('驗證碼已過期');
        }
      });

      // 不應該發送HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/verify`);
    });

    it('應該簡化驗證OTP', () => {
      const verificationCode = TEST_CONSTANTS.VALID_OTP_CODE;
      const mockResponse = TestUtils.createMockApiResponse(true, {
        status: OtpStatus.VERIFIED
      });

      service.verifyOtp(verificationCode).subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/verify`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.verificationCode).toBe(verificationCode);
      req.flush(mockResponse);
    });

    it('應該在無活躍會話時拒絕簡化驗證', () => {
      service.resetSession();

      service.verifyOtp(TEST_CONSTANTS.VALID_OTP_CODE).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.EXPIRED);
          expect(error.error.message).toContain('無活躍的OTP會話');
        }
      });

      httpMock.expectNone(`${API_BASE_URL}/verify`);
    });
  });

  describe('重發OTP功能', () => {
    beforeEach(() => {
      // 設定活躍會話狀態
      service['updateSessionState']({
        hasActiveSession: true,
        verificationToken: 'OTP_TOKEN_123',
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        remainingResends: 2,
        status: OtpStatus.SENT
      });
    });

    it('應該成功重發OTP驗證碼', () => {
      const mockResponse = TestUtils.createMockApiResponse(true, {
        newVerificationToken: 'NEW_OTP_TOKEN_123',
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        remainingResends: 1
      });

      service.resendOtpCode('USER_REQUEST').subscribe(response => {
        expect(response.success).toBeTruthy();
        
        // 檢查會話狀態是否已更新
        const sessionState = service.getCurrentSessionState();
        expect(sessionState.verificationToken).toBe('NEW_OTP_TOKEN_123');
        expect(sessionState.remainingResends).toBe(1);
        expect(sessionState.status).toBe(OtpStatus.SENT);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/resend`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.reason).toBe('USER_REQUEST');
      req.flush(mockResponse);
    });

    it('應該在重發次數用盡時拒絕重發', () => {
      // 設定重發次數為0
      service['updateSessionState']({
        remainingResends: 0
      });

      service.resendOtpCode().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.MAX_ATTEMPTS_EXCEEDED);
          expect(error.error.message).toBe('已達重發次數上限');
        }
      });

      // 不應該發送HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/resend`);
    });

    it('應該在無活躍會話時拒絕重發', () => {
      service.resetSession();

      service.resendOtpCode().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.MAX_ATTEMPTS_EXCEEDED);
          expect(error.error.message).toBe('已達重發次數上限');
        }
      });

      httpMock.expectNone(`${API_BASE_URL}/resend`);
    });

    it('應該在會話資訊不完整時拒絕重發', () => {
      // 設定不完整的會話狀態
      service['updateSessionState']({
        hasActiveSession: true,
        remainingResends: 2,
        verificationToken: undefined,
        mobilePhone: undefined
      });

      service.resendOtpCode().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.error.errorCode).toBe(OtpErrorType.SYSTEM_ERROR);
          expect(error.error.message).toBe('會話資訊不完整，請重新開始');
        }
      });

      httpMock.expectNone(`${API_BASE_URL}/resend`);
    });
  });

  describe('倒數計時功能', () => {
    it('應該在發送OTP後啟動倒數計時', fakeAsync(() => {
      const expiryTime = new Date(Date.now() + 300000); // 5分鐘
      service['updateSessionState']({
        hasActiveSession: true,
        expiresAt: expiryTime,
        status: OtpStatus.SENT
      });

      service['startCountdown']();
      tick(1000); // 前進1秒

      service.countdownState$.subscribe(state => {
        expect(state.isActive).toBeTrue();
        expect(state.type).toBe('EXPIRY');
        expect(state.remainingSeconds).toBeLessThan(300);
        expect(state.description).toContain('驗證碼將於');
      });
    }));

    it('應該在倒數結束時更新狀態為過期', fakeAsync(() => {
      const expiryTime = new Date(Date.now() + 2000); // 2秒後過期
      service['updateSessionState']({
        hasActiveSession: true,
        expiresAt: expiryTime,
        status: OtpStatus.SENT
      });

      service['startCountdown']();
      tick(3000); // 前進3秒，超過過期時間

      const sessionState = service.getCurrentSessionState();
      expect(sessionState.status).toBe(OtpStatus.EXPIRED);
    }));

    it('應該能夠停止倒數計時', () => {
      service['updateSessionState']({
        hasActiveSession: true,
        expiresAt: new Date(Date.now() + 300000),
        status: OtpStatus.SENT
      });

      service['startCountdown']();
      service['stopCountdown']();

      service.countdownState$.subscribe(state => {
        expect(state.isActive).toBeFalse();
        expect(state.remainingSeconds).toBe(0);
      });
    });

    it('應該正確格式化時間顯示', () => {
      const formatTime = service['formatTime'];
      
      expect(formatTime(0)).toBe('00:00');
      expect(formatTime(30)).toBe('00:30');
      expect(formatTime(90)).toBe('01:30');
      expect(formatTime(300)).toBe('05:00');
      expect(formatTime(3661)).toBe('61:01');
    });
  });

  describe('狀態檢查功能', () => {
    it('應該正確檢查OTP狀態', () => {
      service['updateSessionState']({
        hasActiveSession: true,
        status: OtpStatus.SENT,
        remainingAttempts: 2,
        remainingResends: 1,
        isLocked: false,
        expiresAt: new Date(Date.now() + 300000)
      });

      const status = service.getOtpStatus();
      
      expect(status.hasActiveSession).toBeTrue();
      expect(status.status).toBe(OtpStatus.SENT);
      expect(status.remainingAttempts).toBe(2);
      expect(status.remainingResends).toBe(1);
      expect(status.isExpired).toBeFalse();
      expect(status.isLocked).toBeFalse();
      expect(status.canVerify).toBeTrue();
      expect(status.canResend).toBeTrue();
      expect(status.timeUntilExpiry).toBeGreaterThan(0);
    });

    it('應該檢查活躍會話狀態', () => {
      // 無活躍會話
      expect(service.hasActiveSession()).toBeFalse();

      // 有活躍會話
      service['updateSessionState']({
        hasActiveSession: true,
        status: OtpStatus.SENT,
        expiresAt: new Date(Date.now() + 300000)
      });
      expect(service.hasActiveSession()).toBeTrue();

      // 已過期的會話
      service['updateSessionState']({
        expiresAt: new Date(Date.now() - 1000)
      });
      expect(service.hasActiveSession()).toBeFalse();

      // 已驗證的會話
      service['updateSessionState']({
        status: OtpStatus.VERIFIED,
        expiresAt: new Date(Date.now() + 300000)
      });
      expect(service.hasActiveSession()).toBeFalse();
    });

    it('應該檢查是否可以重發', () => {
      // 無活躍會話
      expect(service.canResend()).toBeFalse();

      // 有活躍會話且可以重發
      service['updateSessionState']({
        hasActiveSession: true,
        remainingResends: 1,
        isLocked: false,
        status: OtpStatus.SENT
      });
      expect(service.canResend()).toBeTrue();

      // 重發次數用盡
      service['updateSessionState']({
        remainingResends: 0
      });
      expect(service.canResend()).toBeFalse();

      // 已鎖定
      service['updateSessionState']({
        remainingResends: 1,
        isLocked: true
      });
      expect(service.canResend()).toBeFalse();

      // 已驗證
      service['updateSessionState']({
        remainingResends: 1,
        isLocked: false,
        status: OtpStatus.VERIFIED
      });
      expect(service.canResend()).toBeFalse();
    });
  });

  describe('驗證歷史管理', () => {
    it('應該添加驗證歷史記錄', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        verificationToken: 'OTP_TOKEN_123',
        messageId: 'MSG123456',
        sentAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(),
        remainingResends: 2
      });

      service.sendOtpCode(mockRequest).subscribe(() => {
        service.getVerificationHistory().subscribe(history => {
          expect(history).toHaveLength(1);
          expect(history[0].mobilePhone).toBe(TEST_CONSTANTS.VALID_PHONE_NUMBER);
          expect(history[0].status).toBe(OtpStatus.SENT);
        });
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      req.flush(mockResponse);
    });

    it('應該限制歷史記錄數量為10筆', () => {
      // 添加15筆記錄
      for (let i = 0; i < 15; i++) {
        service['addToHistory']({
          id: `history-${i}`,
          mobilePhone: `091234567${i % 10}`,
          verificationType: VerificationType.IDENTITY_VERIFICATION,
          status: OtpStatus.SENT,
          sentAt: new Date().toISOString(),
          attemptCount: 0,
          ipAddress: '127.0.0.1',
          userAgent: 'test'
        });
      }

      service.getVerificationHistory().subscribe(history => {
        expect(history).toHaveLength(10);
        expect(history[0].id).toBe('history-14'); // 最新的記錄在前面
      });
    });
  });

  describe('錯誤處理', () => {
    it('應該處理HTTP錯誤', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      service.sendOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.userMessage).toBe('系統發生錯誤，請稍後再試');
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('應該根據錯誤類型更新狀態', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      const errorResponse = {
        error: {
          errorCode: OtpErrorType.EXPIRED,
          message: 'OTP已過期'
        }
      };

      service.sendOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.errorCode).toBe(OtpErrorType.EXPIRED);
          
          // 檢查狀態是否已更新
          const sessionState = service.getCurrentSessionState();
          expect(sessionState.status).toBe(OtpStatus.EXPIRED);
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      req.flush(errorResponse, { status: 400, statusText: 'Bad Request' });
    });

    it('應該處理網路錯誤', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      service.sendOtpCode(mockRequest).subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.userMessage).toBe('系統發生錯誤，請稍後再試');
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('載入狀態管理', () => {
    it('應該在API請求期間設定載入狀態', () => {
      const loadingStates: boolean[] = [];
      
      service.loading$.subscribe(loading => {
        loadingStates.push(loading);
      });

      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        mobilePhone: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123',
        requestId: 'req-123',
        timestamp: new Date().toISOString()
      };

      service.sendOtpCode(mockRequest).subscribe();

      const req = httpMock.expectOne(`${API_BASE_URL}/send`);
      req.flush(TestUtils.createMockApiResponse(true, {}));

      // 應該有：初始false、開始請求true、完成請求false
      expect(loadingStates).toContain(true);
      expect(loadingStates[loadingStates.length - 1]).toBeFalse();
    });
  });

  describe('台灣手機號碼驗證器', () => {
    it('應該正確驗證台灣手機號碼格式', () => {
      expect(TaiwanMobileValidator.validate('0912345678')).toBeTrue();
      expect(TaiwanMobileValidator.validate('0987654321')).toBeTrue();
      expect(TaiwanMobileValidator.validate('1234567890')).toBeFalse();
      expect(TaiwanMobileValidator.validate('091234567')).toBeFalse(); // 太短
    });

    it('應該正確格式化手機號碼', () => {
      expect(TaiwanMobileValidator.format('0912345678')).toBe('0912-345-678');
      expect(TaiwanMobileValidator.format('0987654321')).toBe('0987-654-321');
    });

    it('應該正確遮蔽手機號碼', () => {
      expect(TaiwanMobileValidator.mask('0912345678')).toBe('0912-***-678');
      expect(TaiwanMobileValidator.mask('0987654321')).toBe('0987-***-321');
    });

    it('應該正確識別電信業者', () => {
      expect(TaiwanMobileValidator.getCarrier('0912345678')).toBe('中華電信');
      expect(TaiwanMobileValidator.getCarrier('0987654321')).toBe('台灣大哥大');
      expect(TaiwanMobileValidator.getCarrier('0955123456')).toBe('遠傳電信');
    });
  });
});