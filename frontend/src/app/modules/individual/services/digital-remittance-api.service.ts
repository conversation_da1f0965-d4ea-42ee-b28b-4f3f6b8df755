import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * 數位解款申請API服務
 * 對應後端 DigitalRemittanceController
 * 
 * 根據URD需求：
 * 1. 統一處理自然人/法人申請流程
 * 2. 實作6步驟申請流程
 * 3. 資料比對驗證
 * 4. OTP/工商憑證驗證
 * 5. 呼叫FROM API完成申請
 */

export interface ApplicationInitData {
  caseNo: string;
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  flowType: 'APPLICATION';
  originalData: any;
  step: string;
  nextStepUrl: string;
  sessionId: string;
  initTime: string;
}

export interface TermsAgreementRequest {
  caseNo: string;
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  agreedTerms: string[];
  userAgent: string;
  ipAddress: string;
}

export interface CustomerDataVerificationRequest {
  caseNo: string;
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  inputData: {
    // 自然人資料
    payeeId?: string;
    payeeName?: string;
    payeeTel?: string;
    payeeMail?: string;
    // 法人資料
    unifiedNumber?: string;
    companyName?: string;
    contactPerson?: string;
    contactTel?: string;
    contactMail?: string;
    // 共同資料
    payeeAccount: string;
    payeeBankCode: string;
  };
}

export interface DataComparisonResult {
  isValid: boolean;
  matchedFields: string[];
  missingFields: string[];
  mismatchedFields: Array<{
    field: string;
    inputValue: any;
    expectedValue: any;
    errorMessage: string;
  }>;
  comparisonTime: string;
}

export interface OtpRequest {
  caseNo: string;
  mobileNumber: string;
  verificationType: 'REGISTRATION' | 'LOGIN';
}

export interface OtpVerificationRequest {
  caseNo: string;
  otpCode: string;
  mobileNumber: string;
}

export interface CertificateVerificationRequest {
  caseNo: string;
  certificateData: string;
  pin: string;
  readerName: string;
}

export interface RemittanceNatureRequest {
  caseNo: string;
  selectedNature: string;
  purposeCode: string;
  description?: string;
}

export interface FinalConfirmationRequest {
  caseNo: string;
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  confirmedData: any;
  digitalSignature?: string;
  finalConfirmation: boolean;
}

export interface ApplicationApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
  timestamp: string;
}

@Injectable({
  providedIn: 'root'
})
export class DigitalRemittanceApiService {
  private readonly apiUrl = `${environment.apiUrl}/api/ibr/remittance`;
  private readonly timeout = 30000; // 30秒超時
  private readonly retryCount = 2;

  constructor(private http: HttpClient) {}

  /**
   * 初始化申請流程
   * 對應後端: DigitalRemittanceController.initializeApplication()
   */
  initializeApplication(type: string, caseNo: string): Observable<ApplicationApiResponse<ApplicationInitData>> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationApiResponse<ApplicationInitData>>(
      `${this.apiUrl}/initialize`,
      { 
        headers,
        params: { type, caseNo }
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 同意條款
   * 對應後端: DigitalRemittanceController.agreeTerms()
   */
  agreeTerms(request: TermsAgreementRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/terms/agree`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證客戶資料
   * 對應後端: DigitalRemittanceController.verifyCustomerData()
   */
  verifyCustomerData(request: CustomerDataVerificationRequest): Observable<ApplicationApiResponse<DataComparisonResult>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<DataComparisonResult>>(
      `${this.apiUrl}/data/verify`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 發送OTP
   * 對應後端: DigitalRemittanceController.sendOtp()
   */
  sendOtp(request: OtpRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/otp/send`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證OTP
   * 對應後端: DigitalRemittanceController.verifyOtp()
   */
  verifyOtp(request: OtpVerificationRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/otp/verify`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 驗證工商憑證
   * 對應後端: DigitalRemittanceController.verifyCertificate()
   */
  verifyCertificate(request: CertificateVerificationRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/certificate/verify`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 選擇匯款性質
   * 對應後端: DigitalRemittanceController.selectRemittanceNature()
   */
  selectRemittanceNature(request: RemittanceNatureRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/remittance/nature`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 確認申請並呼叫FROM API
   * 對應後端: DigitalRemittanceController.confirmApplication()
   */
  confirmApplication(request: FinalConfirmationRequest): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.post<ApplicationApiResponse<any>>(
      `${this.apiUrl}/confirm`,
      request,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得申請完成頁面資料
   * 對應後端: DigitalRemittanceController.getApplicationComplete()
   */
  getApplicationComplete(caseNo: string): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationApiResponse<any>>(
      `${this.apiUrl}/complete`,
      { 
        headers,
        params: { caseNo }
      }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 查詢申請狀態
   */
  getApplicationStatus(caseNo: string): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationApiResponse<any>>(
      `${this.apiUrl}/status/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  /**
   * 取得申請歷程
   */
  getApplicationHistory(caseNo: string): Observable<ApplicationApiResponse<any>> {
    const headers = this.getHeaders();
    
    return this.http.get<ApplicationApiResponse<any>>(
      `${this.apiUrl}/history/${encodeURIComponent(caseNo)}`,
      { headers }
    ).pipe(
      timeout(this.timeout),
      retry(this.retryCount),
      catchError(this.handleError.bind(this))
    );
  }

  // ==================== 驗證工具方法 ====================

  /**
   * 驗證URL參數
   */
  validateUrlParams(type: string | null, caseNo: string | null): { isValid: boolean; error?: string } {
    if (!type || type !== 'A') {
      return { isValid: false, error: '無效的流程類型 (必須是A)' };
    }
    
    if (!caseNo || !caseNo.startsWith('IBR')) {
      return { isValid: false, error: '無效的案件編號格式' };
    }
    
    return { isValid: true };
  }

  /**
   * 驗證客戶資料格式
   */
  validateCustomerDataFormat(customerType: string, data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (customerType === 'INDIVIDUAL') {
      // 自然人驗證
      if (!data.payeeId || !this.isValidTaiwanId(data.payeeId)) {
        errors.push('身分證號格式不正確');
      }
      if (!data.payeeName || data.payeeName.trim().length === 0) {
        errors.push('收款人姓名不能為空');
      }
      if (!data.payeeTel || !this.isValidPhoneNumber(data.payeeTel)) {
        errors.push('電話號碼格式不正確');
      }
      if (!data.payeeMail || !this.isValidEmail(data.payeeMail)) {
        errors.push('電子郵件格式不正確');
      }
    } else if (customerType === 'CORPORATE') {
      // 法人驗證
      if (!data.unifiedNumber || !this.isValidUnifiedNumber(data.unifiedNumber)) {
        errors.push('統一編號格式不正確');
      }
      if (!data.companyName || data.companyName.trim().length === 0) {
        errors.push('公司名稱不能為空');
      }
      if (!data.contactPerson || data.contactPerson.trim().length === 0) {
        errors.push('聯絡人姓名不能為空');
      }
      if (!data.contactTel || !this.isValidPhoneNumber(data.contactTel)) {
        errors.push('聯絡電話格式不正確');
      }
      if (!data.contactMail || !this.isValidEmail(data.contactMail)) {
        errors.push('聯絡信箱格式不正確');
      }
    }
    
    // 共同驗證
    if (!data.payeeAccount || data.payeeAccount.trim().length === 0) {
      errors.push('收款帳號不能為空');
    }
    if (!data.payeeBankCode || !this.isValidBankCode(data.payeeBankCode)) {
      errors.push('銀行代碼格式不正確');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 確定客戶類型
   */
  determineCustomerType(identifier: string): 'INDIVIDUAL' | 'CORPORATE' | null {
    if (!identifier) return null;
    
    // 自然人: 身分證字號 (10位，第一位英文)
    if (identifier.length === 10 && /^[A-Z][12][0-9]{8}$/.test(identifier)) {
      return 'INDIVIDUAL';
    }
    // 法人: 統一編號 (8位數字)
    else if (identifier.length === 8 && /^\d{8}$/.test(identifier)) {
      return 'CORPORATE';
    }
    
    return null;
  }

  /**
   * 生成客戶URL (模擬後端邏輯)
   */
  generateCustomerUrls(caseNo: string, customerType: 'INDIVIDUAL' | 'CORPORATE'): any {
    const baseUrl = window.location.origin;
    const moduleType = customerType === 'INDIVIDUAL' ? 'individual' : 'corporate';
    
    return {
      applicationUrl: `${baseUrl}/ibr/${moduleType}?type=A&case=${caseNo}`,
      supplementUrl: `${baseUrl}/ibr/${moduleType}?type=S&case=${caseNo}`,
      queryUrl: `${baseUrl}/ibr/query?type=Q&case=${caseNo}`
    };
  }

  // ==================== 私有方法 ====================

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    });
  }

  private isValidTaiwanId(id: string): boolean {
    // 台灣身分證號驗證邏輯
    const pattern = /^[A-Z][12][0-9]{8}$/;
    if (!pattern.test(id)) return false;
    
    // 檢查碼驗證
    const charCode = id.charCodeAt(0) - 65;
    const n1 = Math.floor(charCode / 10) + 10;
    const n2 = charCode % 10;
    
    let sum = n1 + n2 * 9;
    for (let i = 1; i < 9; i++) {
      sum += parseInt(id[i]) * (9 - i);
    }
    
    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === parseInt(id[9]);
  }

  private isValidUnifiedNumber(num: string): boolean {
    // 統一編號驗證邏輯
    if (!/^\d{8}$/.test(num)) return false;
    
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    let sum = 0;
    
    for (let i = 0; i < 8; i++) {
      const product = parseInt(num[i]) * weights[i];
      sum += Math.floor(product / 10) + (product % 10);
    }
    
    return sum % 10 === 0;
  }

  private isValidPhoneNumber(phone: string): boolean {
    // 台灣電話號碼格式驗證
    const patterns = [
      /^09\d{8}$/,                    // 手機
      /^0[2-8]\d{7,8}$/,             // 市話
      /^\+886[1-9]\d{7,8}$/          // 國際格式
    ];
    
    return patterns.some(pattern => pattern.test(phone));
  }

  private isValidEmail(email: string): boolean {
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return pattern.test(email);
  }

  private isValidBankCode(code: string): boolean {
    // 台灣銀行代碼驗證 (3位數字)
    const pattern = /^\d{3}$/;
    return pattern.test(code);
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = '數位解款申請服務發生未知錯誤';
    let errorCode = 'UNKNOWN_ERROR';

    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `網路錯誤: ${error.error.message}`;
      errorCode = 'NETWORK_ERROR';
    } else {
      // 伺服器錯誤
      switch (error.status) {
        case 400:
          errorMessage = '申請資料格式錯誤或不完整';
          errorCode = 'INVALID_APPLICATION_DATA';
          break;
        case 401:
          errorMessage = '身份驗證失敗';
          errorCode = 'AUTHENTICATION_FAILED';
          break;
        case 403:
          errorMessage = '沒有權限執行此操作';
          errorCode = 'INSUFFICIENT_PERMISSIONS';
          break;
        case 404:
          errorMessage = '找不到指定的申請案件';
          errorCode = 'APPLICATION_NOT_FOUND';
          break;
        case 408:
          errorMessage = '申請處理超時，請稍後再試';
          errorCode = 'REQUEST_TIMEOUT';
          break;
        case 409:
          errorMessage = '申請資料衝突或重複';
          errorCode = 'DATA_CONFLICT';
          break;
        case 422:
          errorMessage = '申請資料驗證失敗';
          errorCode = 'VALIDATION_FAILED';
          break;
        case 500:
          errorMessage = '申請服務內部錯誤';
          errorCode = 'INTERNAL_SERVER_ERROR';
          break;
        case 503:
          errorMessage = '申請服務暫時無法使用';
          errorCode = 'SERVICE_UNAVAILABLE';
          break;
        default:
          errorMessage = `申請服務錯誤 (${error.status}): ${error.message}`;
          errorCode = `HTTP_${error.status}`;
      }

      // 嘗試從回應中取得詳細錯誤資訊
      if (error.error && typeof error.error === 'object') {
        if (error.error.message) {
          errorMessage = error.error.message;
        }
        if (error.error.errorCode) {
          errorCode = error.error.errorCode;
        }
      }
    }

    console.error('數位解款申請服務錯誤:', {
      errorCode,
      errorMessage,
      status: error.status,
      url: error.url,
      timestamp: new Date().toISOString()
    });

    return throwError(() => ({
      errorCode,
      message: errorMessage,
      status: error.status,
      timestamp: new Date(),
      originalError: error
    }));
  }
}