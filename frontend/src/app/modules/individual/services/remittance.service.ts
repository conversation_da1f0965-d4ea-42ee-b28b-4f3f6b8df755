/**
 * 自然人解款模組 - 匯款處理服務
 * 
 * @description 專門處理匯款查詢、匯款詳情、匯款確認的業務邏輯服務
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of, combineLatest } from 'rxjs';
import { map, catchError, tap, switchMap, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

// Import remittance related models
import {
  RemittanceSearchCriteria,
  RemittanceSearchRequest,
  RemittanceSearchResponse,
  RemittanceDetailRequest,
  RemittanceDetailResponse,
  RemittanceConfirmRequest,
  RemittanceConfirmResponse,
  RemittanceInfo,
  RemittanceDetail,
  RemittanceStatus,
  ProcessingStatus,
  RemittanceFlag,
  RemittanceQueryHelper,
  PaginationRequest,
  PaginationResponse,
  SortingRequest
} from '../models/remittance.model';

/**
 * 匯款搜尋狀態介面
 */
export interface RemittanceSearchState {
  /** 搜尋條件 */
  searchCriteria?: RemittanceSearchCriteria;
  
  /** 搜尋結果 */
  searchResults: RemittanceInfo[];
  
  /** 分頁資訊 */
  pagination?: PaginationResponse;
  
  /** 載入中狀態 */
  isLoading: boolean;
  
  /** 是否有更多資料 */
  hasMore: boolean;
  
  /** 搜尋歷史 */
  searchHistory: RemittanceSearchCriteria[];
  
  /** 選中的匯款 */
  selectedRemittance?: RemittanceInfo;
  
  /** 最後搜尋時間 */
  lastSearchTime?: Date;
}

/**
 * 匯款詳情狀態介面
 */
export interface RemittanceDetailState {
  /** 匯款詳情 */
  detail?: RemittanceDetail;
  
  /** 載入中狀態 */
  isLoading: boolean;
  
  /** 可執行的操作 */
  availableActions: string[];
  
  /** 處理歷史 */
  processHistory: any[];
  
  /** 相關文件 */
  documents: any[];
}

/**
 * 匯款過濾選項介面
 */
export interface RemittanceFilterOptions {
  /** 狀態過濾 */
  statusFilter: RemittanceStatus[];
  
  /** 幣別過濾 */
  currencyFilter: string[];
  
  /** 金額範圍過濾 */
  amountRange: {
    min?: number;
    max?: number;
  };
  
  /** 日期範圍過濾 */
  dateRange: {
    from?: string;
    to?: string;
  };
  
  /** 匯款人過濾 */
  remitterFilter?: string;
  
  /** 是否顯示已處理 */
  showProcessed: boolean;
}

/**
 * 匯款統計資訊介面
 */
export interface RemittanceStatistics {
  /** 總筆數 */
  totalCount: number;
  
  /** 總金額 */
  totalAmount: number;
  
  /** 待處理筆數 */
  pendingCount: number;
  
  /** 已完成筆數 */
  completedCount: number;
  
  /** 平均金額 */
  averageAmount: number;
  
  /** 最大金額 */
  maxAmount: number;
  
  /** 最小金額 */
  minAmount: number;
  
  /** 幣別分布 */
  currencyDistribution: Record<string, number>;
  
  /** 狀態分布 */
  statusDistribution: Record<string, number>;
}

/**
 * 匯款服務配置介面
 */
interface RemittanceServiceConfig {
  /** API基礎URL */
  baseUrl: string;
  
  /** 預設頁面大小 */
  defaultPageSize: number;
  
  /** 最大頁面大小 */
  maxPageSize: number;
  
  /** 搜尋防抖時間（毫秒） */
  searchDebounceTime: number;
  
  /** 快取時間（毫秒） */
  cacheTimeout: number;
  
  /** 自動重新整理間隔（毫秒） */
  autoRefreshInterval: number;
}

/**
 * 匯款處理服務
 */
@Injectable({
  providedIn: 'root'
})
export class RemittanceService {
  private readonly config: RemittanceServiceConfig;
  
  // 搜尋狀態管理
  private searchStateSubject = new BehaviorSubject<RemittanceSearchState>(this.getInitialSearchState());
  public readonly searchState$ = this.searchStateSubject.asObservable();
  
  // 詳情狀態管理
  private detailStateSubject = new BehaviorSubject<RemittanceDetailState>(this.getInitialDetailState());
  public readonly detailState$ = this.detailStateSubject.asObservable();
  
  // 過濾選項管理
  private filterOptionsSubject = new BehaviorSubject<RemittanceFilterOptions>(this.getDefaultFilterOptions());
  public readonly filterOptions$ = this.filterOptionsSubject.asObservable();
  
  // 統計資訊管理
  private statisticsSubject = new BehaviorSubject<RemittanceStatistics | null>(null);
  public readonly statistics$ = this.statisticsSubject.asObservable();
  
  // 載入狀態
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this.loadingSubject.asObservable();
  
  // 搜尋快取
  private searchCache = new Map<string, { data: RemittanceSearchResponse; timestamp: number }>();

  constructor(private http: HttpClient) {
    this.config = {
      baseUrl: `${environment.apiUrl}/api/ibr/individual/remittance`,
      defaultPageSize: 20,
      maxPageSize: 100,
      searchDebounceTime: 500,
      cacheTimeout: 300000, // 5分鐘
      autoRefreshInterval: 60000 // 1分鐘
    };
    
    // 載入儲存的狀態
    this.loadStoredState();
  }

  /**
   * 取得初始搜尋狀態
   */
  private getInitialSearchState(): RemittanceSearchState {
    return {
      searchResults: [],
      isLoading: false,
      hasMore: false,
      searchHistory: []
    };
  }

  /**
   * 取得初始詳情狀態
   */
  private getInitialDetailState(): RemittanceDetailState {
    return {
      isLoading: false,
      availableActions: [],
      processHistory: [],
      documents: []
    };
  }

  /**
   * 取得預設過濾選項
   */
  private getDefaultFilterOptions(): RemittanceFilterOptions {
    return {
      statusFilter: [],
      currencyFilter: [],
      amountRange: {},
      dateRange: {},
      showProcessed: true
    };
  }

  /**
   * 載入儲存的狀態
   */
  private loadStoredState(): void {
    try {
      const savedHistory = localStorage.getItem('remittance-search-history');
      if (savedHistory) {
        const history = JSON.parse(savedHistory);
        this.updateSearchState({ searchHistory: history });
      }
    } catch (error) {
      console.warn('Failed to load remittance search history:', error);
    }
  }

  /**
   * 儲存搜尋歷史
   */
  private saveSearchHistory(criteria: RemittanceSearchCriteria): void {
    try {
      const currentState = this.searchStateSubject.value;
      const history = [criteria, ...currentState.searchHistory.slice(0, 9)]; // 保留最近10筆
      localStorage.setItem('remittance-search-history', JSON.stringify(history));
      this.updateSearchState({ searchHistory: history });
    } catch (error) {
      console.warn('Failed to save search history:', error);
    }
  }

  /**
   * 更新搜尋狀態
   */
  private updateSearchState(updates: Partial<RemittanceSearchState>): void {
    const currentState = this.searchStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.searchStateSubject.next(newState);
  }

  /**
   * 更新詳情狀態
   */
  private updateDetailState(updates: Partial<RemittanceDetailState>): void {
    const currentState = this.detailStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.detailStateSubject.next(newState);
  }

  /**
   * 建立HTTP Headers
   */
  private createHeaders(sessionToken?: string): HttpHeaders {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    if (sessionToken) {
      headers = headers.set('Authorization', `Bearer ${sessionToken}`);
    }

    return headers;
  }

  /**
   * 產生快取鍵值
   */
  private generateCacheKey(criteria: RemittanceSearchCriteria, pagination?: PaginationRequest): string {
    return JSON.stringify({ criteria, pagination });
  }

  /**
   * 檢查快取
   */
  private getCachedSearch(cacheKey: string): RemittanceSearchResponse | null {
    const cached = this.searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.config.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * 設定快取
   */
  private setCachedSearch(cacheKey: string, data: RemittanceSearchResponse): void {
    this.searchCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  // ==================== 核心搜尋功能 ====================

  /**
   * 搜尋匯款記錄
   */
  searchRemittances(
    criteria: RemittanceSearchCriteria, 
    pagination?: PaginationRequest,
    sorting?: SortingRequest,
    sessionToken?: string
  ): Observable<RemittanceSearchResponse> {
    
    // 檢查快取
    const cacheKey = this.generateCacheKey(criteria, pagination);
    const cachedResult = this.getCachedSearch(cacheKey);
    
    if (cachedResult) {
      this.updateSearchState({
        searchResults: cachedResult.remittances,
        pagination: cachedResult.pagination,
        searchCriteria: criteria,
        lastSearchTime: new Date()
      });
      return of(cachedResult);
    }

    this.updateSearchState({ isLoading: true });

    const request: RemittanceSearchRequest = {
      criteria,
      pagination: pagination || { page: 1, pageSize: this.config.defaultPageSize },
      sorting,
      searchType: 'EXACT',
      sessionToken: sessionToken || ''
    };

    return this.http.post<RemittanceSearchResponse>(
      `${this.config.baseUrl}/search`,
      request,
      { headers: this.createHeaders(sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          // 更新狀態
          this.updateSearchState({
            searchResults: response.remittances,
            pagination: response.pagination,
            searchCriteria: criteria,
            isLoading: false,
            hasMore: response.pagination.hasNext,
            lastSearchTime: new Date()
          });
          
          // 更新統計
          this.updateStatistics(response.remittances);
          
          // 儲存快取
          this.setCachedSearch(cacheKey, response);
          
          // 儲存搜尋歷史
          this.saveSearchHistory(criteria);
        }
      }),
      catchError(error => {
        this.updateSearchState({ isLoading: false });
        return this.handleError(error);
      })
    );
  }

  /**
   * 載入更多匯款記錄（分頁）
   */
  loadMoreRemittances(sessionToken?: string): Observable<RemittanceSearchResponse> {
    const currentState = this.searchStateSubject.value;
    
    if (!currentState.searchCriteria || !currentState.pagination || !currentState.hasMore) {
      return throwError('No more data to load');
    }

    const nextPage = currentState.pagination.currentPage + 1;
    const pagination: PaginationRequest = {
      page: nextPage,
      pageSize: currentState.pagination.pageSize
    };

    return this.searchRemittances(currentState.searchCriteria, pagination, undefined, sessionToken).pipe(
      tap(response => {
        if (response.success) {
          // 合併結果
          const combinedResults = [...currentState.searchResults, ...response.remittances];
          this.updateSearchState({
            searchResults: combinedResults,
            pagination: response.pagination,
            hasMore: response.pagination.hasNext
          });
        }
      })
    );
  }

  /**
   * 重新整理搜尋結果
   */
  refreshSearchResults(sessionToken?: string): Observable<RemittanceSearchResponse> {
    const currentState = this.searchStateSubject.value;
    
    if (!currentState.searchCriteria) {
      return throwError('No search criteria available');
    }

    // 清除快取
    this.searchCache.clear();
    
    return this.searchRemittances(
      currentState.searchCriteria,
      { page: 1, pageSize: this.config.defaultPageSize },
      undefined,
      sessionToken
    );
  }

  // ==================== 匯款詳情功能 ====================

  /**
   * 取得匯款詳情
   */
  getRemittanceDetail(remittanceId: string, idNumber: string, sessionToken?: string): Observable<RemittanceDetailResponse> {
    this.updateDetailState({ isLoading: true });

    const request: RemittanceDetailRequest = {
      remittanceId,
      idNumber,
      sessionToken: sessionToken || '',
      queryType: 'DETAILED'
    };

    return this.http.post<RemittanceDetailResponse>(
      `${this.config.baseUrl}/detail`,
      request,
      { headers: this.createHeaders(sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateDetailState({
            detail: response.remittanceDetail,
            isLoading: false,
            availableActions: response.availableActions.map(action => action.actionCode),
            processHistory: response.processHistory,
            documents: response.documents
          });
          
          // 更新搜尋狀態中的選中項目
          this.updateSearchState({
            selectedRemittance: this.convertDetailToInfo(response.remittanceDetail)
          });
        }
      }),
      catchError(error => {
        this.updateDetailState({ isLoading: false });
        return this.handleError(error);
      })
    );
  }

  /**
   * 確認匯款
   */
  confirmRemittance(request: RemittanceConfirmRequest, sessionToken?: string): Observable<RemittanceConfirmResponse> {
    this.loadingSubject.next(true);

    return this.http.post<RemittanceConfirmResponse>(
      `${this.config.baseUrl}/confirm`,
      request,
      { headers: this.createHeaders(sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          // 更新詳情狀態
          const currentDetail = this.detailStateSubject.value.detail;
          if (currentDetail) {
            this.updateDetailState({
              detail: {
                ...currentDetail,
                status: RemittanceStatus.PROCESSING
              }
            });
          }
          
          // 更新搜尋結果中的狀態
          this.updateRemittanceStatusInResults(request.remittanceId, RemittanceStatus.PROCESSING);
        }
      }),
      catchError(this.handleError),
      tap(() => this.loadingSubject.next(false))
    );
  }

  // ==================== 過濾與排序功能 ====================

  /**
   * 設定過濾選項
   */
  setFilterOptions(options: Partial<RemittanceFilterOptions>): void {
    const currentOptions = this.filterOptionsSubject.value;
    const newOptions = { ...currentOptions, ...options };
    this.filterOptionsSubject.next(newOptions);
    
    // 自動應用過濾
    this.applyFilters();
  }

  /**
   * 應用過濾條件
   */
  private applyFilters(): void {
    const currentState = this.searchStateSubject.value;
    const filterOptions = this.filterOptionsSubject.value;
    
    let filteredResults = [...currentState.searchResults];
    
    // 狀態過濾
    if (filterOptions.statusFilter.length > 0) {
      filteredResults = filteredResults.filter(item => 
        filterOptions.statusFilter.includes(item.status)
      );
    }
    
    // 幣別過濾
    if (filterOptions.currencyFilter.length > 0) {
      filteredResults = filteredResults.filter(item => 
        filterOptions.currencyFilter.includes(item.currency)
      );
    }
    
    // 金額範圍過濾
    if (filterOptions.amountRange.min !== undefined) {
      filteredResults = filteredResults.filter(item => 
        item.amount >= filterOptions.amountRange.min!
      );
    }
    
    if (filterOptions.amountRange.max !== undefined) {
      filteredResults = filteredResults.filter(item => 
        item.amount <= filterOptions.amountRange.max!
      );
    }
    
    // 日期範圍過濾
    if (filterOptions.dateRange.from) {
      filteredResults = filteredResults.filter(item => 
        item.remittanceDate >= filterOptions.dateRange.from!
      );
    }
    
    if (filterOptions.dateRange.to) {
      filteredResults = filteredResults.filter(item => 
        item.remittanceDate <= filterOptions.dateRange.to!
      );
    }
    
    // 匯款人過濾
    if (filterOptions.remitterFilter) {
      const filterText = filterOptions.remitterFilter.toLowerCase();
      filteredResults = filteredResults.filter(item => 
        item.remitter.nameEnglish.toLowerCase().includes(filterText) ||
        item.remitter.nameChinese?.toLowerCase().includes(filterText)
      );
    }
    
    // 更新狀態（這裡可能需要另一個state來存放過濾後的結果）
    this.updateStatistics(filteredResults);
  }

  /**
   * 清除所有過濾條件
   */
  clearFilters(): void {
    this.filterOptionsSubject.next(this.getDefaultFilterOptions());
    this.updateStatistics(this.searchStateSubject.value.searchResults);
  }

  // ==================== 統計功能 ====================

  /**
   * 更新統計資訊
   */
  private updateStatistics(remittances: RemittanceInfo[]): void {
    if (remittances.length === 0) {
      this.statisticsSubject.next(null);
      return;
    }

    const totalCount = remittances.length;
    const totalAmount = remittances.reduce((sum, item) => sum + item.twdAmount, 0);
    const pendingCount = remittances.filter(item => 
      item.status === RemittanceStatus.PENDING || 
      item.status === RemittanceStatus.AWAITING_PAYMENT
    ).length;
    const completedCount = remittances.filter(item => 
      item.status === RemittanceStatus.PAID
    ).length;

    // 幣別分布
    const currencyDistribution: Record<string, number> = {};
    remittances.forEach(item => {
      currencyDistribution[item.currency] = (currencyDistribution[item.currency] || 0) + 1;
    });

    // 狀態分布
    const statusDistribution: Record<string, number> = {};
    remittances.forEach(item => {
      statusDistribution[item.status] = (statusDistribution[item.status] || 0) + 1;
    });

    const statistics: RemittanceStatistics = {
      totalCount,
      totalAmount,
      pendingCount,
      completedCount,
      averageAmount: totalAmount / totalCount,
      maxAmount: Math.max(...remittances.map(item => item.twdAmount)),
      minAmount: Math.min(...remittances.map(item => item.twdAmount)),
      currencyDistribution,
      statusDistribution
    };

    this.statisticsSubject.next(statistics);
  }

  // ==================== 工具方法 ====================

  /**
   * 建立基本搜尋條件
   */
  createBasicSearchCriteria(idNumber: string, name: string): RemittanceSearchCriteria {
    return RemittanceQueryHelper.createBasicSearchCriteria(idNumber, name);
  }

  /**
   * 檢查匯款是否接近期限
   */
  isNearDeadline(remittance: RemittanceInfo, warningDays = 3): boolean {
    return RemittanceQueryHelper.isNearDeadline(remittance.paymentDeadline, warningDays);
  }

  /**
   * 格式化匯款金額
   */
  formatRemittanceAmount(remittance: RemittanceInfo): string {
    return RemittanceQueryHelper.formatAmount(remittance.amount, remittance.currency);
  }

  /**
   * 取得狀態顯示文字
   */
  getStatusDisplayText(status: RemittanceStatus): string {
    return RemittanceQueryHelper.getStatusDisplayText(status);
  }

  /**
   * 計算從匯款日期至今的天數
   */
  calculateDaysFromRemittance(remittanceDate: string): number {
    return RemittanceQueryHelper.calculateDaysFromRemittance(remittanceDate);
  }

  /**
   * 取得當前搜尋狀態
   */
  getCurrentSearchState(): RemittanceSearchState {
    return this.searchStateSubject.value;
  }

  /**
   * 取得當前詳情狀態
   */
  getCurrentDetailState(): RemittanceDetailState {
    return this.detailStateSubject.value;
  }

  /**
   * 清除所有狀態
   */
  clearAllStates(): void {
    this.searchStateSubject.next(this.getInitialSearchState());
    this.detailStateSubject.next(this.getInitialDetailState());
    this.filterOptionsSubject.next(this.getDefaultFilterOptions());
    this.statisticsSubject.next(null);
    this.searchCache.clear();
    localStorage.removeItem('remittance-search-history');
  }

  /**
   * 轉換詳情為基本資訊
   */
  private convertDetailToInfo(detail: RemittanceDetail): RemittanceInfo {
    return {
      remittanceId: detail.remittanceId,
      remittanceDate: detail.remittanceDate,
      currency: detail.currency,
      amount: detail.amount,
      exchangeRate: detail.exchangeRate,
      twdAmount: detail.twdAmount,
      remitter: detail.remitter,
      beneficiary: detail.beneficiary,
      status: detail.status,
      purpose: detail.purpose,
      paymentDeadline: detail.paymentDeadline,
      fees: detail.fees,
      flags: detail.flags || []
    };
  }

  /**
   * 更新搜尋結果中的匯款狀態
   */
  private updateRemittanceStatusInResults(remittanceId: string, newStatus: RemittanceStatus): void {
    const currentState = this.searchStateSubject.value;
    const updatedResults = currentState.searchResults.map(item => 
      item.remittanceId === remittanceId ? { ...item, status: newStatus } : item
    );
    
    this.updateSearchState({ searchResults: updatedResults });
    this.updateStatistics(updatedResults);
  }

  /**
   * 錯誤處理
   */
  private handleError = (error: any): Observable<never> => {
    console.error('RemittanceService error:', error);
    
    let errorMessage = '系統發生錯誤，請稍後再試';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    return throwError({ ...error, userMessage: errorMessage });
  };
}