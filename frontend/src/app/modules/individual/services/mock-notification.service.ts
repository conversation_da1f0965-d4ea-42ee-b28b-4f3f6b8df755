import { Injectable } from '@angular/core';
import { Observable, of, delay, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { SharedTestDataService } from '../../../@core/shared-2/services/shared-test-data.service';

export interface NotificationRequest {
  TheirRefNo: string;
  RemitRefNo: string;
  PayerName: string;
  PayerCountry: string;
  Currency: string;
  Amount: string;
  PayeeEngName: string;
  PayeeName: string;
  PayeeID: string;
  PayeeAccount: string;
  PayeeBankCode: string;
  PayeeTel: string;
  PayeeMail: string;
  SourceOfFund: string;
  WhileFlag: string;
  Memo: string;
  SupplementNo?: string;
}

export interface NotificationResponse {
  StatusCode: string;
  TxntMsg: string;
  Remark: string;
}

@Injectable({
  providedIn: 'root'
})
export class MockNotificationService {

  constructor(private sharedTestDataService: SharedTestDataService) { }

  private mockData: { [key: string]: NotificationRequest } = {
    'normal': {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812345',
      PayerName: 'Shiang Ru',
      PayerCountry: 'US',
      Currency: 'USD',
      Amount: '5000.00',
      PayeeEngName: 'Qing Lan, Wang',
      PayeeName: '王清蘭',
      PayeeID: 'A123456789',
      PayeeAccount: '****************',
      PayeeBankCode: '0080016',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '410',
      WhileFlag: '',
      Memo: 'Monthly Salary'
    },
    'supplement': {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812346',
      PayerName: 'MARY JOHNSON',
      PayerCountry: 'UK',
      Currency: 'GBP',
      Amount: '3000.00',
      PayeeEngName: 'CHEN MEI LING',
      PayeeName: '陳美玲',
      PayeeID: 'B234567890',
      PayeeAccount: '****************',
      PayeeBankCode: '0120034',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '510',
      WhileFlag: 'S',
      Memo: 'Family support',
      SupplementNo: 'IBRSUP2024060800001'
    },
    'large': {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812347',
      PayerName: 'GLOBAL TRADING LLC',
      PayerCountry: 'SG',
      Currency: 'USD',
      Amount: '450000.00',
      PayeeEngName: 'LIN DA WEI',
      PayeeName: '林大偉',
      PayeeID: 'B234567890',
      PayeeAccount: '****************',
      PayeeBankCode: '0120034',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '001',
      WhileFlag: '',
      Memo: 'Business transaction - Invoice #2024-001'
    }
  };


  /**
   * 模擬接收 TO API 通知
   * @param type 測試資料類型: 'normal' | 'supplement' | 'large'
   */
  simulateNotification(type: string = 'normal'): Observable<NotificationRequest> {
    const data = this.mockData[type] || this.mockData['normal'];
    
    // 模擬網路延遲
    return of(data).pipe(
      delay(500),
      map(notification => {
        // 將資料存入統一測試資料服務
        this.sharedTestDataService.setTestData(notification);
        
        // 同時存入 sessionStorage 保持向下相容
        sessionStorage.setItem('ibr_notification_data', JSON.stringify(notification));
        sessionStorage.setItem('ibr_notification_received', 'true');
        sessionStorage.setItem('ibr_notification_time', new Date().toISOString());
        
        return notification;
      })
    );
  }

  /**
   * 取得儲存的通知資料
   */
  getStoredNotification(): NotificationRequest | null {
    const storedData = sessionStorage.getItem('ibr_notification_data');
    if (storedData) {
      return JSON.parse(storedData);
    }
    return null;
  }

  /**
   * 清除通知資料
   */
  clearNotification(): void {
    sessionStorage.removeItem('ibr_notification_data');
    sessionStorage.removeItem('ibr_notification_received');
    sessionStorage.removeItem('ibr_notification_time');
  }

  /**
   * 檢查是否有待處理的通知
   */
  hasNotification(): boolean {
    return sessionStorage.getItem('ibr_notification_received') === 'true';
  }

  /**
   * 模擬發送回應給 TO 系統
   */
  sendResponse(success: boolean = true): Observable<NotificationResponse> {
    const response: NotificationResponse = success ? {
      StatusCode: '00',
      TxntMsg: 'Notification received successfully',
      Remark: 'Processing initiated'
    } : {
      StatusCode: '99',
      TxntMsg: 'Failed to process notification',
      Remark: 'Invalid data format'
    };

    return of(response).pipe(delay(300));
  }

  /**
   * 設定通知資料 (供測試頁面使用)
   * @param data 通知資料，可以是 NotificationRequest 或任意格式
   */
  setNotificationData(data: NotificationRequest | any): void {
    // 除錯：顯示設定的資料
    console.log('MockNotificationService.setNotificationData 被呼叫:', {
      RemitRefNo: data.RemitRefNo,
      PayeeID: data.PayeeID,
      PayeeIDLength: data.PayeeID ? data.PayeeID.length : 'null',
      WhileFlag: data.WhileFlag,
      isCorporate: data.PayeeID && data.PayeeID.length === 8,
      isIndividual: data.PayeeID && data.PayeeID.length === 10,
      fullData: data
    });
    
    // 將資料存入統一測試資料服務
    this.sharedTestDataService.setTestData(data);
    
    // 同時存入 sessionStorage 保持向下相容
    sessionStorage.setItem('ibr_notification_data', JSON.stringify(data));
    sessionStorage.setItem('ibr_notification_received', 'true');
    sessionStorage.setItem('ibr_notification_time', new Date().toISOString());
    
    // 如果不是標準的 NotificationRequest 格式，標記為自訂
    if (!data.TheirRefNo || !data.RemitRefNo) {
      sessionStorage.setItem('ibr_notification_custom', 'true');
    }
  }
}