/**
 * Individual Service 單元測試
 * 
 * @description 測試Individual Service的核心業務邏輯功能
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpErrorResponse } from '@angular/common/http';
import { of, throwError } from 'rxjs';

import { IndividualService, IndividualApplicationState, IndividualFlowStep } from './individual.service';
import { IndividualApiService } from './individual-api.service';
import { OtpService } from './otp.service';
import { TestUtils, TEST_CONSTANTS, TEST_DESCRIPTIONS } from '../../../testing/test-utils';

import {
  TermsAgreementRequest,
  TermsAgreementData
} from '../models/landing.model';

import {
  IdentitySelectionData
} from '../models/identity.model';

import {
  OtpVerificationRequest,
  OtpVerifyRequest,
  VerificationType
} from '../models/verification.model';

import {
  RemittanceSearchCriteria
} from '../models/remittance.model';

import {
  AmountCalculationRequest,
  AmountConfirmationRequest,
  RateInquiryRequest,
  FeeEstimationRequest
} from '../models/amount.model';

import {
  ApplicationConfirmationRequest
} from '../models/confirmation.model';

describe('IndividualService', () => {
  let service: IndividualService;
  let httpMock: HttpTestingController;
  let mockIndividualApi: jasmine.SpyObj<IndividualApiService>;
  let mockOtpService: jasmine.SpyObj<OtpService>;

  const API_BASE_URL = TEST_CONSTANTS.API_BASE_URL + '/individual';

  beforeEach(() => {
    // 建立模擬服務
    mockIndividualApi = jasmine.createSpyObj('IndividualApiService', [
      'verifyIdentity', 
      'searchRemittances', 
      'submitApplication'
    ]);
    
    mockOtpService = jasmine.createSpyObj('OtpService', [
      'sendOtpCode',
      'sendOtp', 
      'verifyOtpCode',
      'verifyOtp',
      'resendOtpCode',
      'getOtpStatus'
    ], {
      sessionState$: of({
        isActive: false,
        sessionId: null,
        expiryTime: null
      }),
      countdownState$: of({
        remainingTime: 0,
        isActive: false,
        canResend: true
      })
    });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        IndividualService,
        { provide: IndividualApiService, useValue: mockIndividualApi },
        { provide: OtpService, useValue: mockOtpService }
      ]
    });

    service = TestBed.inject(IndividualService);
    httpMock = TestBed.inject(HttpTestingController);

    // 清除任何現有的儲存狀態
    sessionStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    sessionStorage.clear();
  });

  describe('基本功能測試', () => {
    it(TEST_DESCRIPTIONS.SERVICE.SHOULD_BE_CREATED, () => {
      expect(service).toBeTruthy();
    });

    it('應該初始化正確的初始狀態', () => {
      const state = service.getCurrentState();
      
      expect(state.termsAgreed).toBeFalse();
      expect(state.identityDataCompleted).toBeFalse();
      expect(state.identityVerified).toBeFalse();
      expect(state.otpVerified).toBeFalse();
      expect(state.remittanceConfirmed).toBeFalse();
      expect(state.amountConfirmed).toBeFalse();
      expect(state.applicationSubmitted).toBeFalse();
      expect(state.currentStep).toBe(IndividualFlowStep.TERMS_AGREEMENT);
    });

    it('應該提供載入狀態Observable', (done) => {
      service.loading$.subscribe(loading => {
        expect(typeof loading).toBe('boolean');
        done();
      });
    });

    it('應該提供申請狀態Observable', (done) => {
      service.applicationState$.subscribe(state => {
        expect(state).toBeTruthy();
        expect(state.currentStep).toBe(IndividualFlowStep.TERMS_AGREEMENT);
        done();
      });
    });
  });

  describe('狀態管理', () => {
    it('應該正確儲存和載入狀態到sessionStorage', () => {
      const testState: Partial<IndividualApplicationState> = {
        termsAgreed: true,
        currentStep: IndividualFlowStep.IDENTITY_DATA
      };

      // 觸發狀態更新
      service['updateApplicationState'](testState);

      // 檢查sessionStorage是否有儲存
      const savedState = sessionStorage.getItem('individual-application-state');
      expect(savedState).toBeTruthy();
      
      const parsedState = JSON.parse(savedState!);
      expect(parsedState.termsAgreed).toBeTrue();
      expect(parsedState.currentStep).toBe(IndividualFlowStep.IDENTITY_DATA);
    });

    it('應該在初始化時從sessionStorage載入狀態', () => {
      const mockState = {
        termsAgreed: true,
        identityDataCompleted: true,
        currentStep: IndividualFlowStep.OTP_VERIFICATION
      };

      sessionStorage.setItem('individual-application-state', JSON.stringify(mockState));

      // 重新建立服務實例
      const newService = TestBed.inject(IndividualService);
      const currentState = newService.getCurrentState();

      expect(currentState.termsAgreed).toBeTrue();
      expect(currentState.identityDataCompleted).toBeTrue();
      expect(currentState.currentStep).toBe(IndividualFlowStep.OTP_VERIFICATION);
    });

    it('應該能夠重置申請狀態', () => {
      // 先設定一些狀態
      service['updateApplicationState']({
        termsAgreed: true,
        identityDataCompleted: true,
        currentStep: IndividualFlowStep.OTP_VERIFICATION
      });

      // 重置狀態
      service.resetApplicationState();

      const state = service.getCurrentState();
      expect(state.termsAgreed).toBeFalse();
      expect(state.identityDataCompleted).toBeFalse();
      expect(state.currentStep).toBe(IndividualFlowStep.TERMS_AGREEMENT);
      
      // 檢查sessionStorage是否已清除
      const savedState = sessionStorage.getItem('individual-application-state');
      expect(savedState).toBeNull();
    });
  });

  describe('條款同意功能', () => {
    it('應該成功提交條款同意', () => {
      const mockRequest: TermsAgreementRequest = {
        personalDataAgreed: true,
        digitalTermsAgreed: true,
        agreementTime: new Date(),
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        sessionId: 'session-123'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        agreementId: 'TERMS123456',
        status: 'AGREED'
      });

      service.submitTermsAgreement(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        
        const currentState = service.getCurrentState();
        expect(currentState.termsAgreed).toBeTrue();
        expect(currentState.currentStep).toBe(IndividualFlowStep.IDENTITY_DATA);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/terms/agreement`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockRequest);
      req.flush(mockResponse);
    });

    it('應該正確驗證條款同意狀態', (done) => {
      const mockData: TermsAgreementData = {
        personalDataAgreed: true,
        digitalTermsAgreed: true,
        hasReadTerms: true,
        agreementTime: new Date(),
        ipAddress: '***********'
      };

      service.validateTermsAgreement(mockData).subscribe(result => {
        expect(result.isValid).toBeTrue();
        expect(result.missingAgreements).toHaveLength(0);
        done();
      });
    });

    it('應該檢測缺少的同意項目', (done) => {
      const mockData: TermsAgreementData = {
        personalDataAgreed: false,
        digitalTermsAgreed: true,
        hasReadTerms: true,
        agreementTime: new Date(),
        ipAddress: '***********'
      };

      service.validateTermsAgreement(mockData).subscribe(result => {
        expect(result.isValid).toBeFalse();
        expect(result.missingAgreements).toContain('個資告知聲明');
        done();
      });
    });
  });

  describe('身份驗證功能', () => {
    it('應該成功提交身份驗證資料', () => {
      const mockData = TestUtils.createMockPersonalInfo();
      const mockApiResponse = TestUtils.createMockApiResponse(true, {
        isVerified: true,
        confidenceScore: 95,
        verificationDetails: {
          nameMatch: true,
          idNumberMatch: true,
          phoneMatch: true,
          addressMatch: true
        }
      });

      mockIndividualApi.verifyIdentity.and.returnValue(of(mockApiResponse));

      service.submitIdentityData(mockData).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result.identityStatus).toBe('VERIFIED');
        
        const currentState = service.getCurrentState();
        expect(currentState.identityDataCompleted).toBeTrue();
        expect(currentState.identityVerified).toBeTrue();
      });

      expect(mockIndividualApi.verifyIdentity).toHaveBeenCalled();
    });

    it('應該處理身份驗證失敗', () => {
      const mockData = TestUtils.createMockPersonalInfo();
      const mockApiResponse = TestUtils.createMockApiResponse(true, {
        isVerified: false,
        confidenceScore: 30,
        errorMessage: '身份驗證失敗',
        verificationDetails: {
          nameMatch: false,
          idNumberMatch: true,
          phoneMatch: true,
          addressMatch: true
        }
      });

      mockIndividualApi.verifyIdentity.and.returnValue(of(mockApiResponse));

      service.submitIdentityData(mockData).subscribe(response => {
        expect(response.success).toBeFalse();
        expect(response.message).toContain('身份驗證失敗');
      });
    });

    it('應該取得銀行列表', () => {
      const mockBanks = [
        { code: '012', name: '凱基商業銀行', englishName: 'KGI Bank' },
        { code: '822', name: '中國信託商業銀行', englishName: 'CTBC Bank' }
      ];

      service.getBankList().subscribe(banks => {
        expect(banks).toHaveLength(2);
        expect(banks[0].code).toBe('012');
        expect(banks[0].name).toBe('凱基商業銀行');
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBanks);
    });

    it('應該快取銀行列表', () => {
      const mockBanks = [
        { code: '012', name: '凱基商業銀行', englishName: 'KGI Bank' }
      ];

      // 第一次請求
      service.getBankList().subscribe();
      const req1 = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req1.flush(mockBanks);

      // 第二次請求應該使用快取，不會發送HTTP請求
      service.getBankList().subscribe(banks => {
        expect(banks).toHaveLength(1);
      });

      // 應該沒有額外的HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/banks`);
    });
  });

  describe('OTP驗證功能', () => {
    it('應該發送OTP驗證碼', () => {
      const mockRequest: OtpVerificationRequest = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        phoneNumber: TEST_CONSTANTS.VALID_PHONE_NUMBER,
        verificationType: VerificationType.IDENTITY_VERIFICATION,
        sessionToken: 'session-123'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        otpId: 'OTP123456',
        expiryTime: new Date(Date.now() + 5 * 60 * 1000).toISOString()
      });

      mockOtpService.sendOtpCode.and.returnValue(of(mockResponse));

      service.sendOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.otpId).toBe('OTP123456');
      });

      expect(mockOtpService.sendOtpCode).toHaveBeenCalledWith(mockRequest);
    });

    it('應該簡化發送OTP', () => {
      const idNumber = TEST_CONSTANTS.VALID_TAIWAN_ID;
      const phoneNumber = TEST_CONSTANTS.VALID_PHONE_NUMBER;
      const mockResponse = TestUtils.createMockApiResponse(true, {
        otpId: 'OTP123456'
      });

      mockOtpService.sendOtp.and.returnValue(of(mockResponse));

      service.sendOtp(idNumber, phoneNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      expect(mockOtpService.sendOtp).toHaveBeenCalledWith(
        idNumber,
        phoneNumber,
        VerificationType.IDENTITY_VERIFICATION,
        jasmine.any(String)
      );
    });

    it('應該驗證OTP驗證碼', () => {
      const mockRequest: OtpVerifyRequest = {
        otpId: 'OTP123456',
        verificationCode: TEST_CONSTANTS.VALID_OTP_CODE,
        sessionToken: 'session-123'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        status: 'VERIFIED',
        verificationId: 'VER123456'
      });

      mockOtpService.verifyOtpCode.and.returnValue(of(mockResponse));

      service.verifyOtpCode(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        
        const currentState = service.getCurrentState();
        expect(currentState.otpVerified).toBeTrue();
        expect(currentState.identityVerified).toBeTrue();
        expect(currentState.currentStep).toBe(IndividualFlowStep.REMITTANCE_SEARCH);
      });
    });

    it('應該簡化驗證OTP', () => {
      const verificationCode = TEST_CONSTANTS.VALID_OTP_CODE;
      const mockResponse = TestUtils.createMockApiResponse(true, {
        status: 'VERIFIED'
      });

      mockOtpService.verifyOtp.and.returnValue(of(mockResponse));

      service.verifyOtp(verificationCode).subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      expect(mockOtpService.verifyOtp).toHaveBeenCalledWith(
        verificationCode,
        jasmine.any(String)
      );
    });

    it('應該重發OTP驗證碼', () => {
      const mockResponse = TestUtils.createMockApiResponse(true, {
        otpId: 'OTP654321'
      });

      mockOtpService.resendOtpCode.and.returnValue(of(mockResponse));

      service.resendOtp().subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      expect(mockOtpService.resendOtpCode).toHaveBeenCalledWith('USER_REQUEST');
    });
  });

  describe('匯款查詢功能', () => {
    it('應該搜尋匯款記錄', () => {
      const mockCriteria: RemittanceSearchCriteria = {
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        beneficiaryName: '王小明',
        remittanceType: 'SALARY',
        dateRange: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31')
        }
      };

      const mockApiResponse = TestUtils.createMockApiResponse(true, {
        remittances: [TestUtils.createMockRemittanceInfo()],
        totalCount: 1,
        currentPage: 1,
        totalPages: 1
      });

      mockIndividualApi.searchRemittances.and.returnValue(of(mockApiResponse));

      service.searchRemittances(mockCriteria).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result.remittances).toHaveLength(1);
        expect(response.result.totalCount).toBe(1);
      });

      expect(mockIndividualApi.searchRemittances).toHaveBeenCalled();
    });

    it('應該取得匯款詳情', () => {
      const remittanceId = 'REM123456';
      const idNumber = TEST_CONSTANTS.VALID_TAIWAN_ID;
      const mockResponse = TestUtils.createMockApiResponse(true, TestUtils.createMockRemittanceInfo());

      service.getRemittanceDetail(remittanceId, idNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/remittance/detail`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.remittanceId).toBe(remittanceId);
      expect(req.request.body.idNumber).toBe(idNumber);
      req.flush(mockResponse);
    });

    it('應該確認匯款', () => {
      const mockRequest = {
        remittanceId: 'REM123456',
        idNumber: TEST_CONSTANTS.VALID_TAIWAN_ID,
        confirmed: true,
        sessionToken: 'session-123'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        confirmationId: 'CONF123456',
        status: 'CONFIRMED'
      });

      service.confirmRemittance(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        
        const currentState = service.getCurrentState();
        expect(currentState.remittanceConfirmed).toBeTrue();
        expect(currentState.currentStep).toBe(IndividualFlowStep.AMOUNT_CALCULATION);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/remittance/confirm`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('金額計算功能', () => {
    it('應該計算金額', () => {
      const mockRequest: AmountCalculationRequest = {
        remittanceId: 'REM123456',
        originalAmount: 1000,
        originalCurrency: 'USD',
        targetCurrency: 'TWD',
        calculationType: 'STANDARD'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        originalAmount: 1000,
        convertedAmount: 31000,
        exchangeRate: 31.0,
        fees: {
          remittanceFee: 200,
          processingFee: 50,
          totalFees: 250
        }
      });

      service.calculateAmount(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.convertedAmount).toBe(31000);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/amount/calculate`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該確認金額', () => {
      const mockRequest: AmountConfirmationRequest = {
        calculationId: 'CALC123456',
        finalAmount: 30750,
        confirmed: true,
        sessionToken: 'session-123'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        confirmationId: 'AMOUNT_CONF123456',
        status: 'CONFIRMED'
      });

      service.confirmAmount(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        
        const currentState = service.getCurrentState();
        expect(currentState.amountConfirmed).toBeTrue();
        expect(currentState.currentStep).toBe(IndividualFlowStep.APPLICATION_SUBMISSION);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/amount/confirm`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該查詢匯率', () => {
      const mockRequest: RateInquiryRequest = {
        fromCurrency: 'USD',
        toCurrency: 'TWD',
        inquiryType: 'INDICATIVE',
        amount: 1000
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        fromCurrency: 'USD',
        toCurrency: 'TWD',
        rate: 31.0,
        timestamp: new Date().toISOString()
      });

      service.inquireExchangeRate(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.rate).toBe(31.0);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/amount/rate-inquiry`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });

    it('應該快取指示性匯率查詢', () => {
      const mockRequest: RateInquiryRequest = {
        fromCurrency: 'USD',
        toCurrency: 'TWD',
        inquiryType: 'INDICATIVE',
        amount: 1000
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        rate: 31.0
      });

      // 第一次請求
      service.inquireExchangeRate(mockRequest).subscribe();
      const req1 = httpMock.expectOne(`${API_BASE_URL}/amount/rate-inquiry`);
      req1.flush(mockResponse);

      // 第二次請求應該使用快取
      service.inquireExchangeRate(mockRequest).subscribe(response => {
        expect(response.result?.rate).toBe(31.0);
      });

      // 應該沒有額外的HTTP請求
      httpMock.expectNone(`${API_BASE_URL}/amount/rate-inquiry`);
    });

    it('應該試算手續費', () => {
      const mockRequest: FeeEstimationRequest = {
        amount: 1000,
        currency: 'USD',
        remittanceType: 'SALARY',
        serviceType: 'STANDARD'
      };

      const mockResponse = TestUtils.createMockApiResponse(true, {
        remittanceFee: 200,
        processingFee: 50,
        totalFees: 250
      });

      service.estimateFees(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.totalFees).toBe(250);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/amount/fee-estimation`);
      expect(req.request.method).toBe('POST');
      req.flush(mockResponse);
    });
  });

  describe('申請提交功能', () => {
    it('應該成功提交申請', () => {
      const mockRequest: ApplicationConfirmationRequest = {
        applicationData: {
          applicantInfo: TestUtils.createMockPersonalInfo(),
          remittanceInfo: TestUtils.createMockRemittanceInfo(),
          amountInfo: {
            originalAmount: 1000,
            convertedAmount: 31000,
            exchangeRate: 31.0
          }
        },
        confirmations: {
          dataAccuracy: true,
          termsAgreed: true,
          identityVerified: true,
          remittanceConfirmed: true
        },
        digitalSignature: 'digital-signature-hash',
        sessionToken: 'session-123'
      };

      const mockApiResponse = TestUtils.createMockApiResponse(true, {
        applicationId: 'APP123456',
        status: 'SUBMITTED',
        submitTime: new Date(),
        estimatedProcessTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        trackingNumber: 'TRK123456',
        nextSteps: ['等待銀行審核', '預計1-2個工作天完成'],
        contactInfo: {
          phoneNumber: '02-2181-8888',
          email: '<EMAIL>',
          serviceHours: '週一至週五 09:00-17:30'
        },
        receiptUrl: 'https://example.com/receipt/APP123456'
      });

      mockIndividualApi.submitApplication.and.returnValue(of(mockApiResponse));

      service.submitApplication(mockRequest).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.applicationNumber).toBe('APP123456');
        
        const currentState = service.getCurrentState();
        expect(currentState.applicationSubmitted).toBeTrue();
        expect(currentState.applicationNumber).toBe('APP123456');
        expect(currentState.currentStep).toBe(IndividualFlowStep.COMPLETION);
      });

      expect(mockIndividualApi.submitApplication).toHaveBeenCalled();
    });

    it('應該查詢申請狀態', () => {
      const applicationNumber = 'APP123456';
      const idNumber = TEST_CONSTANTS.VALID_TAIWAN_ID;
      const mockResponse = TestUtils.createMockApiResponse(true, {
        applicationNumber,
        status: 'PROCESSING',
        submitTime: new Date(),
        lastUpdateTime: new Date(),
        progress: 60
      });

      service.getApplicationStatus(applicationNumber, idNumber).subscribe(response => {
        expect(response.success).toBeTruthy();
        expect(response.result?.applicationNumber).toBe(applicationNumber);
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/application/status`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.applicationNumber).toBe(applicationNumber);
      expect(req.request.body.idNumber).toBe(idNumber);
      req.flush(mockResponse);
    });
  });

  describe('流程控制功能', () => {
    it('應該正確檢查步驟進入權限', () => {
      // 初始狀態只能進入條款同意
      expect(service.canEnterStep(IndividualFlowStep.TERMS_AGREEMENT)).toBeTrue();
      expect(service.canEnterStep(IndividualFlowStep.IDENTITY_DATA)).toBeFalse();

      // 更新狀態：條款已同意
      service['updateApplicationState']({ termsAgreed: true });
      expect(service.canEnterStep(IndividualFlowStep.IDENTITY_DATA)).toBeTrue();
      expect(service.canEnterStep(IndividualFlowStep.IDENTITY_VERIFICATION)).toBeFalse();

      // 更新狀態：身份資料已完成
      service['updateApplicationState']({ identityDataCompleted: true });
      expect(service.canEnterStep(IndividualFlowStep.IDENTITY_VERIFICATION)).toBeTrue();

      // 更新狀態：身份已驗證
      service['updateApplicationState']({ identityVerified: true });
      expect(service.canEnterStep(IndividualFlowStep.REMITTANCE_SEARCH)).toBeTrue();
    });

    it('應該取得下一個步驟', () => {
      expect(service.getNextStep(IndividualFlowStep.TERMS_AGREEMENT))
        .toBe(IndividualFlowStep.IDENTITY_DATA);
      
      expect(service.getNextStep(IndividualFlowStep.IDENTITY_DATA))
        .toBe(IndividualFlowStep.IDENTITY_VERIFICATION);
      
      expect(service.getNextStep(IndividualFlowStep.COMPLETION))
        .toBeNull();
    });

    it('應該取得上一個步驟', () => {
      expect(service.getPreviousStep(IndividualFlowStep.IDENTITY_DATA))
        .toBe(IndividualFlowStep.TERMS_AGREEMENT);
      
      expect(service.getPreviousStep(IndividualFlowStep.OTP_VERIFICATION))
        .toBe(IndividualFlowStep.IDENTITY_VERIFICATION);
      
      expect(service.getPreviousStep(IndividualFlowStep.TERMS_AGREEMENT))
        .toBeNull();
    });
  });

  describe('快取管理', () => {
    it('應該正確設定和取得快取資料', () => {
      const testData = { test: 'data' };
      const cacheKey = 'test-key';

      service['setCachedData'](cacheKey, testData);
      const cachedData = service['getCachedData'](cacheKey);

      expect(cachedData).toEqual(testData);
    });

    it('應該在快取過期後返回null', () => {
      const testData = { test: 'data' };
      const cacheKey = 'test-key';

      // 設定快取並手動修改時間戳記使其過期
      service['setCachedData'](cacheKey, testData);
      service['cacheTimestamps'].set(cacheKey, Date.now() - 400000); // 超過5分鐘

      const cachedData = service['getCachedData'](cacheKey);
      expect(cachedData).toBeNull();
    });

    it('應該清除所有快取', () => {
      service['setCachedData']('key1', 'data1');
      service['setCachedData']('key2', 'data2');

      service.clearCache();

      expect(service['getCachedData']('key1')).toBeNull();
      expect(service['getCachedData']('key2')).toBeNull();
    });
  });

  describe('錯誤處理', () => {
    it('應該處理HTTP錯誤', () => {
      const errorResponse = new HttpErrorResponse({
        error: { message: '系統錯誤' },
        status: 500,
        statusText: 'Internal Server Error'
      });

      service.getBankList().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.userMessage).toBe('系統錯誤');
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('應該在認證錯誤時重置狀態', () => {
      // 先設定一些狀態
      service['updateApplicationState']({
        termsAgreed: true,
        identityDataCompleted: true
      });

      const errorResponse = new HttpErrorResponse({
        status: 401,
        statusText: 'Unauthorized'
      });

      service.getBankList().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.userMessage).toBe('認證失效，請重新開始');
          
          // 檢查狀態是否已重置
          const currentState = service.getCurrentState();
          expect(currentState.termsAgreed).toBeFalse();
          expect(currentState.identityDataCompleted).toBeFalse();
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
    });

    it('應該處理網路錯誤', () => {
      service.getBankList().subscribe({
        next: () => fail('應該要失敗'),
        error: (error) => {
          expect(error.userMessage).toBe('系統發生錯誤，請稍後再試');
        }
      });

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req.error(new ErrorEvent('Network error'));
    });
  });

  describe('載入狀態管理', () => {
    it('應該在API請求期間設定載入狀態', () => {
      const loadingStates: boolean[] = [];
      
      service.loading$.subscribe(loading => {
        loadingStates.push(loading);
      });

      service.getBankList().subscribe();

      const req = httpMock.expectOne(`${API_BASE_URL}/banks`);
      req.flush([]);

      // 應該有：初始false、開始請求true、完成請求false
      expect(loadingStates).toContain(true);
      expect(loadingStates[loadingStates.length - 1]).toBeFalse();
    });
  });
});