/**
 * 自然人解款模組 - OTP驗證服務
 * 
 * @description 專門處理OTP雙重驗證、手機號碼驗證、驗證碼管理的服務
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, timer } from 'rxjs';
import { map, catchError, tap, takeUntil, finalize } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';


// Import OTP related models
import {
  OtpVerificationRequest,
  OtpSendResponse,
  OtpVerifyRequest,
  OtpVerifyResponse,
  OtpResendRequest,
  OtpResendResponse,
  OtpVerificationResult,
  MobileVerificationData,
  VerificationHistory,
  OtpStatus,
  VerificationType,
  OtpErrorType,
  TaiwanMobileValidator
} from '../models/verification.model';

/**
 * OTP會話狀態介面
 */
export interface OtpSessionState {
  /** 是否有活躍會話 */
  hasActiveSession: boolean;
  
  /** 驗證Token */
  verificationToken?: string;
  
  /** 手機號碼 */
  mobilePhone?: string;
  
  /** 驗證類型 */
  verificationType?: VerificationType;
  
  /** 發送時間 */
  sentAt?: Date;
  
  /** 過期時間 */
  expiresAt?: Date;
  
  /** 剩餘嘗試次數 */
  remainingAttempts: number;
  
  /** 剩餘重發次數 */
  remainingResends: number;
  
  /** 是否已鎖定 */
  isLocked: boolean;
  
  /** 鎖定到期時間 */
  lockedUntil?: Date;
  
  /** 當前狀態 */
  status: OtpStatus;
}

/**
 * 倒數計時狀態介面
 */
export interface CountdownState {
  /** 是否正在倒數 */
  isActive: boolean;
  
  /** 剩餘秒數 */
  remainingSeconds: number;
  
  /** 倒數類型 */
  type: 'EXPIRY' | 'RESEND' | 'UNLOCK';
  
  /** 倒數描述 */
  description: string;
}

/**
 * OTP驗證設定介面
 */
interface OtpServiceConfig {
  /** API基礎URL */
  baseUrl: string;
  
  /** 預設驗證碼長度 */
  defaultCodeLength: number;
  
  /** 預設有效期限（秒） */
  defaultExpiryDuration: number;
  
  /** 預設最大嘗試次數 */
  defaultMaxAttempts: number;
  
  /** 預設重發間隔（秒） */
  defaultResendInterval: number;
  
  /** 預設鎖定時間（秒） */
  defaultLockDuration: number;
}

/**
 * OTP驗證服務
 */
@Injectable({
  providedIn: 'root'
})
export class OtpService {
  private readonly config: OtpServiceConfig;
  
  // 狀態管理
  private sessionStateSubject!: BehaviorSubject<OtpSessionState>;
  public readonly sessionState$!: Observable<OtpSessionState>;
  
  // 倒數計時管理
  private countdownStateSubject!: BehaviorSubject<CountdownState>;
  public readonly countdownState$!: Observable<CountdownState>;
  
  // 載入狀態
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this.loadingSubject.asObservable();
  
  // 驗證歷史
  private verificationHistorySubject = new BehaviorSubject<VerificationHistory[]>([]);
  public readonly verificationHistory$ = this.verificationHistorySubject.asObservable();
  
  // 倒數計時器停止信號
  private stopCountdown$ = new BehaviorSubject<boolean>(false);

  constructor(
    private http: HttpClient
  ) {
    // 先設定 config
    this.config = {
      baseUrl: `${environment.apiUrl}/api/ibr/individual/verification/otp`,
      defaultCodeLength: 6,
      defaultExpiryDuration: 300, // 5分鐘
      defaultMaxAttempts: 3,
      defaultResendInterval: 60, // 1分鐘
      defaultLockDuration: 1800 // 30分鐘
    };
    
    // 再初始化 subjects
    this.sessionStateSubject = new BehaviorSubject<OtpSessionState>(this.getInitialSessionState());
    (this as any).sessionState$ = this.sessionStateSubject.asObservable();
    
    this.countdownStateSubject = new BehaviorSubject<CountdownState>(this.getInitialCountdownState());
    (this as any).countdownState$ = this.countdownStateSubject.asObservable();
    
    // 載入儲存的狀態
    this.loadSessionState();
  }

  /**
   * 取得初始會話狀態
   */
  private getInitialSessionState(): OtpSessionState {
    return {
      hasActiveSession: false,
      remainingAttempts: this.config.defaultMaxAttempts,
      remainingResends: 3,
      isLocked: false,
      status: OtpStatus.PENDING
    };
  }

  /**
   * 取得初始倒數狀態
   */
  private getInitialCountdownState(): CountdownState {
    return {
      isActive: false,
      remainingSeconds: 0,
      type: 'EXPIRY',
      description: ''
    };
  }

  /**
   * 載入會話狀態
   */
  private loadSessionState(): void {
    try {
      const savedState = sessionStorage.getItem('otp-session-state');
      if (savedState) {
        const state = JSON.parse(savedState);
        
        // 檢查是否過期
        if (state.expiresAt && new Date(state.expiresAt) > new Date()) {
          this.sessionStateSubject.next({
            ...this.getInitialSessionState(),
            ...state,
            sentAt: state.sentAt ? new Date(state.sentAt) : undefined,
            expiresAt: state.expiresAt ? new Date(state.expiresAt) : undefined,
            lockedUntil: state.lockedUntil ? new Date(state.lockedUntil) : undefined
          });
          
          // 啟動倒數計時
          this.startCountdown();
        } else {
          // 清除過期狀態
          this.clearSessionState();
        }
      }
    } catch (error) {
      console.warn('Failed to load OTP session state:', error);
      this.clearSessionState();
    }
  }

  /**
   * 儲存會話狀態
   */
  private saveSessionState(state: OtpSessionState): void {
    try {
      sessionStorage.setItem('otp-session-state', JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save OTP session state:', error);
    }
  }

  /**
   * 更新會話狀態
   */
  private updateSessionState(updates: Partial<OtpSessionState>): void {
    const currentState = this.sessionStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.sessionStateSubject.next(newState);
    this.saveSessionState(newState);
  }

  /**
   * 清除會話狀態
   */
  private clearSessionState(): void {
    this.sessionStateSubject.next(this.getInitialSessionState());
    sessionStorage.removeItem('otp-session-state');
    this.stopCountdown();
  }

  /**
   * 建立HTTP Headers
   */
  private createHeaders(sessionToken?: string): HttpHeaders {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    if (sessionToken) {
      headers = headers.set('Authorization', `Bearer ${sessionToken}`);
    }

    return headers;
  }

  /**
   * 設定載入狀態
   */
  private setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  // ==================== 核心OTP功能 ====================

  /**
   * 發送OTP驗證碼
   */
  sendOtpCode(request: OtpVerificationRequest): Observable<OtpSendResponse> {
    // 驗證手機號碼格式
    if (!TaiwanMobileValidator.validate(request.mobilePhone)) {
      return throwError({
        error: { 
          errorCode: OtpErrorType.INVALID_PHONE,
          message: '手機號碼格式不正確' 
        }
      });
    }

    // 檢查是否在鎖定期間
    const currentState = this.sessionStateSubject.value;
    if (currentState.isLocked && currentState.lockedUntil && new Date() < currentState.lockedUntil) {
      const remainingMinutes = Math.ceil((currentState.lockedUntil.getTime() - Date.now()) / 60000);
      return throwError({
        error: {
          errorCode: OtpErrorType.MAX_ATTEMPTS_EXCEEDED,
          message: `帳號已鎖定，請 ${remainingMinutes} 分鐘後再試`
        }
      });
    }

    this.setLoading(true);

    return this.http.post<OtpSendResponse>(
      `${this.config.baseUrl}/send`,
      request,
      { headers: this.createHeaders(request.sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateSessionState({
            hasActiveSession: true,
            verificationToken: response.verificationToken,
            mobilePhone: request.mobilePhone,
            verificationType: request.verificationType,
            sentAt: new Date(response.sentAt),
            expiresAt: new Date(response.expiresAt),
            remainingResends: response.remainingResends,
            status: OtpStatus.SENT,
            isLocked: false
          });
          
          // 啟動倒數計時
          this.startCountdown();
          
          // 添加到驗證歷史
          this.addToHistory({
            id: response.messageId,
            mobilePhone: request.mobilePhone,
            verificationType: request.verificationType,
            status: OtpStatus.SENT,
            sentAt: response.sentAt,
            attemptCount: 0,
            ipAddress: this.getClientIP(),
            userAgent: navigator.userAgent
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 驗證OTP驗證碼
   */
  verifyOtpCode(request: OtpVerifyRequest): Observable<OtpVerifyResponse> {
    const currentState = this.sessionStateSubject.value;
    
    // 檢查是否有活躍會話
    if (!currentState.hasActiveSession || !currentState.verificationToken) {
      return throwError({
        error: {
          errorCode: OtpErrorType.EXPIRED,
          message: '驗證會話已過期，請重新發送驗證碼'
        }
      });
    }

    // 檢查是否已過期
    if (currentState.expiresAt && new Date() > currentState.expiresAt) {
      this.updateSessionState({ status: OtpStatus.EXPIRED });
      return throwError({
        error: {
          errorCode: OtpErrorType.EXPIRED,
          message: '驗證碼已過期，請重新發送'
        }
      });
    }

    // 檢查是否已鎖定
    if (currentState.isLocked) {
      return throwError({
        error: {
          errorCode: OtpErrorType.MAX_ATTEMPTS_EXCEEDED,
          message: '超過最大嘗試次數，帳號已鎖定'
        }
      });
    }

    this.setLoading(true);

    return this.http.post<OtpVerifyResponse>(
      `${this.config.baseUrl}/verify`,
      {
        ...request,
        verificationToken: currentState.verificationToken
      },
      { headers: this.createHeaders(request.sessionToken) }
    ).pipe(
      tap(response => {
        const newAttemptCount = this.config.defaultMaxAttempts - (response.result?.remainingAttempts || 0);
        
        if (response.success && response.result.status === OtpStatus.VERIFIED) {
          // 驗證成功
          this.updateSessionState({
            status: OtpStatus.VERIFIED,
            remainingAttempts: response.result.remainingAttempts
          });
          this.stopCountdown();
          
          // 更新歷史記錄
          this.updateHistoryItem(currentState.mobilePhone!, {
            status: OtpStatus.VERIFIED,
            verifiedAt: new Date().toISOString(),
            attemptCount: newAttemptCount
          });
          
        } else {
          // 驗證失敗
          const remainingAttempts = response.result?.remainingAttempts || 0;
          const isLocked = remainingAttempts <= 0;
          
          this.updateSessionState({
            status: isLocked ? OtpStatus.LOCKED : OtpStatus.FAILED,
            remainingAttempts,
            isLocked,
            lockedUntil: isLocked ? new Date(Date.now() + this.config.defaultLockDuration * 1000) : undefined
          });
          
          // 更新歷史記錄
          this.updateHistoryItem(currentState.mobilePhone!, {
            status: isLocked ? OtpStatus.LOCKED : OtpStatus.FAILED,
            attemptCount: newAttemptCount
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 重發OTP驗證碼
   */
  resendOtpCode(reason: 'USER_REQUEST' | 'AUTO_RETRY' | 'TIMEOUT' = 'USER_REQUEST'): Observable<OtpResendResponse> {
    const currentState = this.sessionStateSubject.value;
    
    // 檢查是否可以重發
    if (!currentState.hasActiveSession || currentState.remainingResends <= 0) {
      return throwError({
        error: {
          errorCode: OtpErrorType.MAX_ATTEMPTS_EXCEEDED,
          message: '已達重發次數上限'
        }
      });
    }

    if (!currentState.verificationToken || !currentState.mobilePhone) {
      return throwError({
        error: {
          errorCode: OtpErrorType.SYSTEM_ERROR,
          message: '會話資訊不完整，請重新開始'
        }
      });
    }

    this.setLoading(true);

    const request: OtpResendRequest = {
      verificationToken: currentState.verificationToken,
      mobilePhone: currentState.mobilePhone,
      sessionToken: '', // 需要從外部傳入
      reason,
      timestamp: new Date().toISOString()
    };

    return this.http.post<OtpResendResponse>(
      `${this.config.baseUrl}/resend`,
      request,
      { headers: this.createHeaders(request.sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateSessionState({
            verificationToken: response.newVerificationToken,
            expiresAt: new Date(response.expiresAt),
            remainingResends: response.remainingResends,
            status: OtpStatus.SENT
          });
          
          // 重新啟動倒數計時
          this.startCountdown();
          
          // 添加重發記錄
          this.addToHistory({
            id: `resend-${Date.now()}`,
            mobilePhone: currentState.mobilePhone!,
            verificationType: currentState.verificationType!,
            status: OtpStatus.SENT,
            sentAt: new Date().toISOString(),
            attemptCount: 0,
            ipAddress: this.getClientIP(),
            userAgent: navigator.userAgent
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  // ==================== 倒數計時功能 ====================

  /**
   * 啟動倒數計時
   */
  private startCountdown(): void {
    const currentState = this.sessionStateSubject.value;
    
    if (!currentState.expiresAt) {
      return;
    }

    this.stopCountdown();
    
    const expiryTime = currentState.expiresAt.getTime();
    const now = Date.now();
    
    if (expiryTime <= now) {
      return;
    }

    // 開始倒數計時
    timer(0, 1000).pipe(
      takeUntil(this.stopCountdown$),
      map(() => {
        const remaining = Math.max(0, Math.ceil((expiryTime - Date.now()) / 1000));
        return remaining;
      }),
      tap(remainingSeconds => {
        if (remainingSeconds > 0) {
          this.countdownStateSubject.next({
            isActive: true,
            remainingSeconds,
            type: 'EXPIRY',
            description: `驗證碼將於 ${this.formatTime(remainingSeconds)} 後過期`
          });
        } else {
          // 倒數結束
          this.updateSessionState({ status: OtpStatus.EXPIRED });
          this.stopCountdown();
        }
      })
    ).subscribe();
  }

  /**
   * 停止倒數計時
   */
  private stopCountdown(): void {
    this.stopCountdown$.next(true);
    this.countdownStateSubject.next(this.getInitialCountdownState());
  }

  /**
   * 格式化時間顯示
   */
  private formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // ==================== 手機號碼驗證功能 ====================

  /**
   * 驗證手機號碼格式
   */
  validateMobilePhone(phoneNumber: string): Observable<MobileVerificationData> {
    const isValid = TaiwanMobileValidator.validate(phoneNumber);
    const formattedNumber = TaiwanMobileValidator.format(phoneNumber);
    const carrier = TaiwanMobileValidator.getCarrier(phoneNumber);

    const result: MobileVerificationData = {
      mobilePhone: phoneNumber,
      countryCode: '+886',
      formattedNumber,
      carrier,
      numberType: isValid ? 'MOBILE' : 'UNKNOWN',
      isValid
    };

    return new Observable(observer => {
      observer.next(result);
      observer.complete();
    });
  }

  /**
   * 格式化手機號碼顯示
   */
  formatMobileNumber(phoneNumber: string, maskSensitive = false): string {
    if (maskSensitive) {
      return TaiwanMobileValidator.mask(phoneNumber);
    }
    return TaiwanMobileValidator.format(phoneNumber);
  }

  // ==================== 驗證歷史管理 ====================

  /**
   * 添加驗證歷史記錄
   */
  private addToHistory(record: VerificationHistory): void {
    const currentHistory = this.verificationHistorySubject.value;
    const newHistory = [record, ...currentHistory].slice(0, 10); // 保留最近10筆
    this.verificationHistorySubject.next(newHistory);
  }

  /**
   * 更新歷史記錄項目
   */
  private updateHistoryItem(mobilePhone: string, updates: Partial<VerificationHistory>): void {
    const currentHistory = this.verificationHistorySubject.value;
    const updatedHistory = currentHistory.map(item => 
      item.mobilePhone === mobilePhone && !item.verifiedAt ? 
        { ...item, ...updates } : 
        item
    );
    this.verificationHistorySubject.next(updatedHistory);
  }

  /**
   * 取得驗證歷史
   */
  getVerificationHistory(): Observable<VerificationHistory[]> {
    return this.verificationHistory$;
  }

  // ==================== 簡化的OTP驗證介面 ====================

  /**
   * 簡化的發送OTP方法（與IndividualService整合使用）
   * 
   * @param idNumber 身份證號
   * @param phoneNumber 手機號碼
   * @param verificationType 驗證類型
   * @param sessionToken 會話Token
   * @returns OTP發送結果
   */
  sendOtp(
    idNumber: string, 
    phoneNumber: string, 
    verificationType: VerificationType = VerificationType.IDENTITY_VERIFICATION,
    sessionToken?: string
  ): Observable<OtpSendResponse> {
    const request: OtpVerificationRequest = {
      idNumber,
      mobilePhone: phoneNumber,
      verificationType,
      sessionToken: sessionToken || '',
      requestId: `otp-${Date.now()}`,
      timestamp: new Date().toISOString()
    };

    return this.sendOtpCode(request);
  }

  /**
   * 簡化的驗證OTP方法（與IndividualService整合使用）
   * 
   * @param verificationCode 驗證碼
   * @param sessionToken 會話Token
   * @returns OTP驗證結果
   */
  verifyOtp(
    verificationCode: string,
    sessionToken?: string
  ): Observable<OtpVerifyResponse> {
    const currentState = this.sessionStateSubject.value;
    
    if (!currentState.hasActiveSession) {
      return throwError({
        error: {
          errorCode: OtpErrorType.EXPIRED,
          message: '無活躍的OTP會話，請重新發送驗證碼'
        }
      });
    }

    const request: OtpVerifyRequest = {
      verificationCode,
      sessionToken: sessionToken || '',
      verificationToken: currentState.verificationToken || '',
      mobilePhone: currentState.mobilePhone || '',
      requestId: `verify-${Date.now()}`,
      timestamp: new Date().toISOString()
    };

    return this.verifyOtpCode(request);
  }

  /**
   * 檢查OTP狀態
   * 
   * @returns OTP狀態摘要
   */
  getOtpStatus(): {
    hasActiveSession: boolean;
    status: OtpStatus;
    remainingAttempts: number;
    remainingResends: number;
    isExpired: boolean;
    isLocked: boolean;
    canVerify: boolean;
    canResend: boolean;
    timeUntilExpiry?: number;
  } {
    const state = this.sessionStateSubject.value;
    const now = new Date();
    const isExpired = state.expiresAt ? now > state.expiresAt : false;
    const timeUntilExpiry = state.expiresAt ? 
      Math.max(0, Math.ceil((state.expiresAt.getTime() - now.getTime()) / 1000)) : 
      undefined;

    return {
      hasActiveSession: state.hasActiveSession,
      status: state.status,
      remainingAttempts: state.remainingAttempts,
      remainingResends: state.remainingResends,
      isExpired,
      isLocked: state.isLocked,
      canVerify: state.hasActiveSession && !isExpired && !state.isLocked && state.remainingAttempts > 0,
      canResend: state.hasActiveSession && state.remainingResends > 0 && !state.isLocked,
      timeUntilExpiry
    };
  }

  // ==================== 工具方法 ====================

  /**
   * 取得當前會話狀態
   */
  getCurrentSessionState(): OtpSessionState {
    return this.sessionStateSubject.value;
  }

  /**
   * 檢查是否有活躍會話
   */
  hasActiveSession(): boolean {
    const state = this.sessionStateSubject.value;
    return state.hasActiveSession && 
           state.status !== OtpStatus.EXPIRED && 
           state.status !== OtpStatus.VERIFIED &&
           (!state.expiresAt || new Date() < state.expiresAt);
  }

  /**
   * 檢查是否可以重發
   */
  canResend(): boolean {
    const state = this.sessionStateSubject.value;
    return state.hasActiveSession && 
           state.remainingResends > 0 && 
           !state.isLocked &&
           state.status !== OtpStatus.VERIFIED;
  }

  /**
   * 重置OTP會話
   */
  resetSession(): void {
    this.clearSessionState();
    this.verificationHistorySubject.next([]);
  }

  /**
   * 取得客戶端IP（模擬）
   */
  private getClientIP(): string {
    // 在實際應用中，這應該由後端提供
    return '127.0.0.1';
  }

  /**
   * 錯誤處理
   */
  private handleError = (error: any): Observable<never> => {
    console.error('OtpService error:', error);
    
    let errorMessage = '系統發生錯誤，請稍後再試';
    let errorCode = OtpErrorType.SYSTEM_ERROR;
    
    if (error.error?.errorCode) {
      errorCode = error.error.errorCode;
    }
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    // 根據錯誤類型更新狀態
    switch (errorCode) {
      case OtpErrorType.EXPIRED:
        this.updateSessionState({ status: OtpStatus.EXPIRED });
        break;
        
      case OtpErrorType.MAX_ATTEMPTS_EXCEEDED:
        this.updateSessionState({ 
          status: OtpStatus.LOCKED, 
          isLocked: true,
          lockedUntil: new Date(Date.now() + this.config.defaultLockDuration * 1000)
        });
        break;
        
      case OtpErrorType.INVALID_CODE:
        this.updateSessionState({ status: OtpStatus.FAILED });
        break;
    }
    
    return throwError({ 
      ...error, 
      userMessage: errorMessage,
      errorCode 
    });
  };
}