/**
 * 自然人解款模組 - 核心業務服務
 * 
 * @description 自然人解款流程的核心業務邏輯服務，負責協調各項服務與狀態管理
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap, finalize } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

// 導入新的API服務
import { IndividualApiService, IdentityVerificationRequest } from './individual-api.service';
import { OtpService } from './otp.service';

// Import all model interfaces
import {
  TermsAgreementData,
  TermsAgreementRequest,
  TermsAgreementResponse,
  TermsValidationResult
} from '../models/landing.model';

import {
  IdentitySelectionData,
  IdentityVerificationResponse,
  BankInfo
} from '../models/identity.model';

import {
  OtpVerificationRequest,
  OtpSendResponse,
  OtpVerifyRequest,
  OtpVerifyResponse,
  VerificationType
} from '../models/verification.model';

import {
  RemittanceSearchCriteria,
  RemittanceSearchResponse,
  RemittanceDetailRequest,
  RemittanceDetailResponse,
  RemittanceConfirmRequest,
  RemittanceConfirmResponse
} from '../models/remittance.model';

import {
  AmountCalculationRequest,
  AmountCalculationResponse,
  AmountConfirmationRequest,
  AmountConfirmationResponse,
  RateInquiryRequest,
  RateInquiryResponse,
  FeeEstimationRequest,
  FeeEstimationResponse
} from '../models/amount.model';

import {
  ApplicationConfirmationRequest,
  ApplicationConfirmationResponse,
  ApplicationStatusRequest,
  ApplicationStatusResponse
} from '../models/confirmation.model';

/**
 * 個人申請狀態介面
 */
export interface IndividualApplicationState {
  /** 條款同意狀態 */
  termsAgreed: boolean;
  
  /** 身份資料填寫完成 */
  identityDataCompleted: boolean;
  
  /** 身份驗證完成 */
  identityVerified: boolean;
  
  /** OTP驗證完成 */
  otpVerified: boolean;
  
  /** 匯款資料確認完成 */
  remittanceConfirmed: boolean;
  
  /** 金額確認完成 */
  amountConfirmed: boolean;
  
  /** 申請提交完成 */
  applicationSubmitted: boolean;
  
  /** 當前步驟 */
  currentStep: IndividualFlowStep;
  
  /** 會話Token */
  sessionToken?: string;
  
  /** 申請編號 */
  applicationNumber?: string;
}

/**
 * 流程步驟枚舉
 */
export enum IndividualFlowStep {
  TERMS_AGREEMENT = 'TERMS_AGREEMENT',
  IDENTITY_DATA = 'IDENTITY_DATA',
  IDENTITY_VERIFICATION = 'IDENTITY_VERIFICATION',
  OTP_VERIFICATION = 'OTP_VERIFICATION',
  REMITTANCE_SEARCH = 'REMITTANCE_SEARCH',
  REMITTANCE_CONFIRMATION = 'REMITTANCE_CONFIRMATION',
  AMOUNT_CALCULATION = 'AMOUNT_CALCULATION',
  AMOUNT_CONFIRMATION = 'AMOUNT_CONFIRMATION',
  APPLICATION_SUBMISSION = 'APPLICATION_SUBMISSION',
  COMPLETION = 'COMPLETION'
}

/**
 * 服務配置介面
 */
interface ServiceConfig {
  /** API基礎URL */
  baseUrl: string;
  
  /** 請求超時時間 */
  timeout: number;
  
  /** 重試次數 */
  retryAttempts: number;
  
  /** 快取時間 */
  cacheTimeout: number;
}

/**
 * 自然人解款核心服務
 */
@Injectable({
  providedIn: 'root'
})
export class IndividualService {
  private readonly baseUrl: string;
  private readonly config: ServiceConfig;
  
  // 狀態管理
  private applicationStateSubject!: BehaviorSubject<IndividualApplicationState>;
  public readonly applicationState$!: Observable<IndividualApplicationState>;
  
  // 載入狀態管理
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public readonly loading$ = this.loadingSubject.asObservable();
  
  // 資料快取
  private cache = new Map<string, any>();
  private cacheTimestamps = new Map<string, number>();
  
  constructor(
    private http: HttpClient,
    private individualApi: IndividualApiService,
    private otpService: OtpService
  ) {
    this.baseUrl = `${environment.apiUrl}/api/ibr/individual`;
    this.config = {
      baseUrl: this.baseUrl,
      timeout: 30000,
      retryAttempts: 3,
      cacheTimeout: 300000 // 5分鐘
    };
    
    // 初始化狀態管理
    this.applicationStateSubject = new BehaviorSubject<IndividualApplicationState>(this.getInitialState());
    (this as any).applicationState$ = this.applicationStateSubject.asObservable();
    
    // 初始化載入狀態
    this.loadState();
  }

  /**
   * 取得初始狀態
   */
  private getInitialState(): IndividualApplicationState {
    return {
      termsAgreed: false,
      identityDataCompleted: false,
      identityVerified: false,
      otpVerified: false,
      remittanceConfirmed: false,
      amountConfirmed: false,
      applicationSubmitted: false,
      currentStep: IndividualFlowStep.TERMS_AGREEMENT
    };
  }

  /**
   * 載入狀態（從localStorage或sessionStorage）
   */
  private loadState(): void {
    try {
      const savedState = sessionStorage.getItem('individual-application-state');
      if (savedState) {
        const state = JSON.parse(savedState);
        this.applicationStateSubject.next({
          ...this.getInitialState(),
          ...state
        });
      }
    } catch (error) {
      console.warn('Failed to load application state:', error);
    }
  }

  /**
   * 儲存狀態
   */
  private saveState(state: IndividualApplicationState): void {
    try {
      sessionStorage.setItem('individual-application-state', JSON.stringify(state));
    } catch (error) {
      console.warn('Failed to save application state:', error);
    }
  }

  /**
   * 更新申請狀態
   */
  private updateApplicationState(updates: Partial<IndividualApplicationState>): void {
    const currentState = this.applicationStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.applicationStateSubject.next(newState);
    this.saveState(newState);
  }

  /**
   * 設定載入狀態
   */
  private setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  /**
   * 建立HTTP Headers
   */
  private createHeaders(sessionToken?: string): HttpHeaders {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    if (sessionToken) {
      headers = headers.set('Authorization', `Bearer ${sessionToken}`);
    }

    return headers;
  }

  /**
   * 快取檢查
   */
  private getCachedData<T>(key: string): T | null {
    const timestamp = this.cacheTimestamps.get(key);
    if (timestamp && Date.now() - timestamp < this.config.cacheTimeout) {
      return this.cache.get(key) || null;
    }
    
    // 清除過期快取
    this.cache.delete(key);
    this.cacheTimestamps.delete(key);
    return null;
  }

  /**
   * 設定快取
   */
  private setCachedData(key: string, data: any): void {
    this.cache.set(key, data);
    this.cacheTimestamps.set(key, Date.now());
  }

  // ==================== 條款同意相關 ====================

  /**
   * 提交條款同意
   */
  submitTermsAgreement(request: TermsAgreementRequest): Observable<TermsAgreementResponse> {
    this.setLoading(true);
    
    return this.http.post<TermsAgreementResponse>(
      `${this.baseUrl}/terms/agreement`,
      request,
      { headers: this.createHeaders() }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateApplicationState({
            termsAgreed: true,
            sessionToken: response.nextRoute ? this.applicationStateSubject.value.sessionToken : undefined,
            currentStep: IndividualFlowStep.IDENTITY_DATA
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 驗證條款同意狀態
   */
  validateTermsAgreement(data: TermsAgreementData): Observable<TermsValidationResult> {
    return of({
      isValid: data.personalDataAgreed && data.digitalTermsAgreed,
      missingAgreements: [
        ...(data.personalDataAgreed ? [] : ['個資告知聲明']),
        ...(data.digitalTermsAgreed ? [] : ['數位解款條款'])
      ],
      errors: [],
      warnings: []
    });
  }

  // ==================== 身份驗證相關 ====================

  /**
   * 提交身份驗證資料
   */
  submitIdentityData(data: IdentitySelectionData): Observable<IdentityVerificationResponse> {
    this.setLoading(true);
    
    // 使用新的API服務，轉換為符合後端DTO的格式
    const verificationRequest: IdentityVerificationRequest = {
      taiwanId: data.idNumber,
      name: data.chineseName,
      birthDate: new Date(data.birthDate),
      phoneNumber: data.mobilePhone,
      verificationType: 'BASIC'
    };

    return this.individualApi.verifyIdentity(verificationRequest).pipe(
      map(apiResponse => {
        // 轉換API回應格式為現有的模型格式
        const response: IdentityVerificationResponse = {
          success: apiResponse.verified,
          message: apiResponse.verified ? '身份驗證成功' : (apiResponse.message || '身份驗證失敗'),
          sessionToken: this.applicationStateSubject.value.sessionToken || 'new-session-token',
          result: {
            identityStatus: apiResponse.verified ? 'VERIFIED' : 'FAILED',
            matchScore: apiResponse.details?.creditScore || 100,
            verificationDetails: {
              nameMatch: apiResponse.details?.nameMatched || false,
              idNumberMatch: apiResponse.details?.idNumberValid || false,
              phoneMatch: apiResponse.details?.phoneNumberValid || true,
              addressMatch: true // 假設地址比對成功
            },
            requiredNextStep: apiResponse.verified && (apiResponse.details?.creditScore || 100) < 80 ? 'OTP_VERIFICATION' : 'NONE'
          },
          nextAction: apiResponse.verified && (apiResponse.details?.creditScore || 100) < 80 ? 'OTP_VERIFICATION' : 'REMITTANCE_SEARCH',
          timestamp: new Date()
        };
        return response;
      }),
      tap(response => {
        if (response.success) {
          this.updateApplicationState({
            identityDataCompleted: true,
            identityVerified: response.result.identityStatus === 'VERIFIED',
            sessionToken: response.sessionToken,
            currentStep: response.nextAction === 'OTP_VERIFICATION' ? 
              IndividualFlowStep.OTP_VERIFICATION : 
              IndividualFlowStep.REMITTANCE_SEARCH
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 身份驗證 (verifyIdentity方法別名)
   */
  verifyIdentity(data: IdentitySelectionData): Observable<IdentityVerificationResponse> {
    return this.submitIdentityData(data);
  }

  /**
   * 取得銀行列表
   */
  getBankList(): Observable<BankInfo[]> {
    const cacheKey = 'bank-list';
    const cachedData = this.getCachedData<BankInfo[]>(cacheKey);
    
    if (cachedData) {
      return of(cachedData);
    }

    return this.http.get<BankInfo[]>(
      `${this.baseUrl}/banks`,
      { headers: this.createHeaders() }
    ).pipe(
      tap(banks => this.setCachedData(cacheKey, banks)),
      catchError(this.handleError)
    );
  }

  // ==================== OTP驗證相關 ====================

  /**
   * 發送OTP驗證碼
   */
  sendOtpCode(request: OtpVerificationRequest): Observable<OtpSendResponse> {
    this.setLoading(true);
    
    return this.otpService.sendOtpCode(request).pipe(
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 簡化的發送OTP方法
   */
  sendOtp(idNumber: string, phoneNumber: string): Observable<OtpSendResponse> {
    this.setLoading(true);
    
    return this.otpService.sendOtp(
      idNumber, 
      phoneNumber, 
      VerificationType.IDENTITY_VERIFICATION,
      this.applicationStateSubject.value.sessionToken
    ).pipe(
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 驗證OTP驗證碼
   */
  verifyOtpCode(request: OtpVerifyRequest): Observable<OtpVerifyResponse> {
    this.setLoading(true);
    
    return this.otpService.verifyOtpCode(request).pipe(
      tap(response => {
        if (response.success && response.result.status === 'VERIFIED') {
          this.updateApplicationState({
            otpVerified: true,
            identityVerified: true,
            sessionToken: response.newSessionToken || this.applicationStateSubject.value.sessionToken,
            currentStep: IndividualFlowStep.REMITTANCE_SEARCH
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 簡化的驗證OTP方法
   */
  verifyOtp(verificationCode: string): Observable<OtpVerifyResponse> {
    this.setLoading(true);
    
    return this.otpService.verifyOtp(
      verificationCode,
      this.applicationStateSubject.value.sessionToken
    ).pipe(
      tap(response => {
        if (response.success && response.result.status === 'VERIFIED') {
          this.updateApplicationState({
            otpVerified: true,
            identityVerified: true,
            sessionToken: response.newSessionToken || this.applicationStateSubject.value.sessionToken,
            currentStep: IndividualFlowStep.REMITTANCE_SEARCH
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 重發OTP驗證碼
   */
  resendOtp(): Observable<any> {
    this.setLoading(true);
    
    return this.otpService.resendOtpCode('USER_REQUEST').pipe(
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 取得OTP狀態
   */
  getOtpStatus() {
    return this.otpService.getOtpStatus();
  }

  /**
   * 取得OTP會話狀態
   */
  getOtpSessionState() {
    return this.otpService.sessionState$;
  }

  /**
   * 取得OTP倒數計時狀態
   */
  getOtpCountdownState() {
    return this.otpService.countdownState$;
  }

  // ==================== 匯款查詢相關 ====================

  /**
   * 搜尋匯款記錄
   */
  searchRemittances(criteria: RemittanceSearchCriteria): Observable<RemittanceSearchResponse> {
    this.setLoading(true);
    
    // 使用新的API服務
    const searchRequest = {
      idNumber: criteria.idNumber,
      beneficiaryName: criteria.beneficiaryName,
      remittanceType: criteria.remittanceType,
      dateRange: criteria.dateRange ? {
        startDate: new Date(criteria.dateRange.startDate),
        endDate: new Date(criteria.dateRange.endDate)
      } : undefined,
      pagination: { page: 1, pageSize: 20 }
    };

    return this.individualApi.searchRemittances(searchRequest).pipe(
      map(apiResponse => {
        // 轉換API回應格式為現有的模型格式
        const response: RemittanceSearchResponse = {
          success: true,
          message: '查詢成功',
          sessionToken: this.applicationStateSubject.value.sessionToken,
          result: {
            remittances: apiResponse.remittances.map(r => ({
              remittanceId: r.remittanceId,
              remittanceDate: new Date(r.remittanceDate),
              senderName: r.remitter?.nameEnglish || '',
              senderCountry: r.remitter?.country || '',
              beneficiaryName: r.beneficiary?.nameChinese || '',
              amount: r.amount,
              currency: r.currency,
              status: r.status.toString(),
              bankName: r.beneficiary?.bankName || '',
              purpose: r.purpose?.descriptionChinese || ''
            })),
            totalCount: apiResponse.totalCount,
            currentPage: apiResponse.currentPage,
            totalPages: apiResponse.totalPages,
            searchCriteria: criteria,
            hasMore: apiResponse.currentPage < apiResponse.totalPages
          },
          timestamp: new Date()
        };
        return response;
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 取得匯款詳情
   */
  getRemittanceDetail(remittanceId: string, idNumber: string): Observable<RemittanceDetailResponse> {
    this.setLoading(true);
    
    const request: RemittanceDetailRequest = {
      remittanceId,
      idNumber,
      sessionToken: this.applicationStateSubject.value.sessionToken || '',
      queryType: 'DETAILED'
    };

    return this.http.post<RemittanceDetailResponse>(
      `${this.baseUrl}/remittance/detail`,
      request,
      { headers: this.createHeaders(this.applicationStateSubject.value.sessionToken) }
    ).pipe(
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 確認匯款
   */
  confirmRemittance(request: RemittanceConfirmRequest): Observable<RemittanceConfirmResponse> {
    this.setLoading(true);
    
    return this.http.post<RemittanceConfirmResponse>(
      `${this.baseUrl}/remittance/confirm`,
      request,
      { headers: this.createHeaders(this.applicationStateSubject.value.sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateApplicationState({
            remittanceConfirmed: true,
            currentStep: IndividualFlowStep.AMOUNT_CALCULATION
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  // ==================== 金額計算相關 ====================

  /**
   * 計算金額
   */
  calculateAmount(request: AmountCalculationRequest): Observable<AmountCalculationResponse> {
    this.setLoading(true);
    
    return this.http.post<AmountCalculationResponse>(
      `${this.baseUrl}/amount/calculate`,
      request,
      { headers: this.createHeaders(this.applicationStateSubject.value.sessionToken) }
    ).pipe(
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 確認金額
   */
  confirmAmount(request: AmountConfirmationRequest): Observable<AmountConfirmationResponse> {
    this.setLoading(true);
    
    return this.http.post<AmountConfirmationResponse>(
      `${this.baseUrl}/amount/confirm`,
      request,
      { headers: this.createHeaders(this.applicationStateSubject.value.sessionToken) }
    ).pipe(
      tap(response => {
        if (response.success) {
          this.updateApplicationState({
            amountConfirmed: true,
            currentStep: IndividualFlowStep.APPLICATION_SUBMISSION
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 查詢匯率
   */
  inquireExchangeRate(request: RateInquiryRequest): Observable<RateInquiryResponse> {
    const cacheKey = `rate-${request.fromCurrency}-${request.toCurrency}`;
    const cachedData = this.getCachedData<RateInquiryResponse>(cacheKey);
    
    if (cachedData && request.inquiryType === 'INDICATIVE') {
      return of(cachedData);
    }

    return this.http.post<RateInquiryResponse>(
      `${this.baseUrl}/amount/rate-inquiry`,
      request,
      { headers: this.createHeaders() }
    ).pipe(
      tap(response => {
        if (request.inquiryType === 'INDICATIVE') {
          this.setCachedData(cacheKey, response);
        }
      }),
      catchError(this.handleError)
    );
  }

  /**
   * 試算手續費
   */
  estimateFees(request: FeeEstimationRequest): Observable<FeeEstimationResponse> {
    return this.http.post<FeeEstimationResponse>(
      `${this.baseUrl}/amount/fee-estimation`,
      request,
      { headers: this.createHeaders() }
    ).pipe(
      catchError(this.handleError)
    );
  }

  // ==================== 申請提交相關 ====================

  /**
   * 提交申請
   */
  submitApplication(request: ApplicationConfirmationRequest): Observable<ApplicationConfirmationResponse> {
    this.setLoading(true);
    
    // 使用新的API服務
    const submitRequest = {
      applicationData: request, // 直接使用 ApplicationConfirmationRequest 作為 ApplicationSubmission
      confirmations: {
        dataAccuracy: request.confirmations?.dataAccuracy || false,
        termsAgreed: request.confirmations?.termsAgreed || false,
        identityVerified: request.confirmations?.identityVerified || false,
        remittanceConfirmed: request.confirmations?.remittanceConfirmed || false
      },
      digitalSignature: typeof request.digitalSignature === 'string' ? request.digitalSignature : JSON.stringify(request.digitalSignature),
      submitTime: new Date()
    };

    return this.individualApi.submitApplication(submitRequest).pipe(
      map(apiResponse => {
        // 轉換API回應格式為現有的模型格式
        const response: ApplicationConfirmationResponse = {
          success: true,
          message: '申請提交成功',
          applicationNumber: apiResponse.applicationId,
          sessionToken: this.applicationStateSubject.value.sessionToken || 'updated-session-token',
          result: {
            applicationStatus: apiResponse.status,
            submitTime: apiResponse.submitTime,
            estimatedProcessTime: apiResponse.estimatedProcessTime.toISOString(),
            trackingCode: apiResponse.trackingNumber,
            nextSteps: apiResponse.nextSteps,
            contactInfo: {
              phone: apiResponse.contactInfo.phoneNumber,
              email: apiResponse.contactInfo.email,
              serviceHours: apiResponse.contactInfo.serviceHours
            },
            receiptUrl: apiResponse.receiptUrl
          },
          timestamp: new Date()
        };
        return response;
      }),
      tap(response => {
        if (response.success) {
          this.updateApplicationState({
            applicationSubmitted: true,
            applicationNumber: response.applicationNumber,
            currentStep: IndividualFlowStep.COMPLETION
          });
        }
      }),
      catchError(this.handleError),
      finalize(() => this.setLoading(false))
    );
  }

  /**
   * 查詢申請狀態
   */
  getApplicationStatus(applicationNumber: string, idNumber: string): Observable<ApplicationStatusResponse> {
    const request: ApplicationStatusRequest = {
      applicationNumber,
      idNumber,
      queryType: 'DETAILED',
      sessionToken: this.applicationStateSubject.value.sessionToken
    };

    return this.http.post<ApplicationStatusResponse>(
      `${this.baseUrl}/application/status`,
      request,
      { headers: this.createHeaders(this.applicationStateSubject.value.sessionToken) }
    ).pipe(
      catchError(this.handleError)
    );
  }

  // ==================== 工具方法 ====================

  /**
   * 重置申請狀態
   */
  resetApplicationState(): void {
    this.applicationStateSubject.next(this.getInitialState());
    sessionStorage.removeItem('individual-application-state');
    this.clearCache();
  }

  /**
   * 清除快取
   */
  clearCache(): void {
    this.cache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * 取得當前申請狀態
   */
  getCurrentState(): IndividualApplicationState {
    return this.applicationStateSubject.value;
  }

  /**
   * 檢查是否可以進入指定步驟
   */
  canEnterStep(step: IndividualFlowStep): boolean {
    const state = this.applicationStateSubject.value;
    
    switch (step) {
      case IndividualFlowStep.TERMS_AGREEMENT:
        return true;
        
      case IndividualFlowStep.IDENTITY_DATA:
        return state.termsAgreed;
        
      case IndividualFlowStep.IDENTITY_VERIFICATION:
        return state.termsAgreed && state.identityDataCompleted;
        
      case IndividualFlowStep.OTP_VERIFICATION:
        return state.identityDataCompleted && !state.identityVerified;
        
      case IndividualFlowStep.REMITTANCE_SEARCH:
        return state.identityVerified || state.otpVerified;
        
      case IndividualFlowStep.REMITTANCE_CONFIRMATION:
        return state.identityVerified && state.remittanceConfirmed;
        
      case IndividualFlowStep.AMOUNT_CALCULATION:
        return state.remittanceConfirmed;
        
      case IndividualFlowStep.AMOUNT_CONFIRMATION:
        return state.remittanceConfirmed;
        
      case IndividualFlowStep.APPLICATION_SUBMISSION:
        return state.amountConfirmed;
        
      case IndividualFlowStep.COMPLETION:
        return state.applicationSubmitted;
        
      default:
        return false;
    }
  }

  /**
   * 取得下一個步驟
   */
  getNextStep(currentStep: IndividualFlowStep): IndividualFlowStep | null {
    const steps = Object.values(IndividualFlowStep);
    const currentIndex = steps.indexOf(currentStep);
    
    if (currentIndex >= 0 && currentIndex < steps.length - 1) {
      return steps[currentIndex + 1];
    }
    
    return null;
  }

  /**
   * 取得上一個步驟
   */
  getPreviousStep(currentStep: IndividualFlowStep): IndividualFlowStep | null {
    const steps = Object.values(IndividualFlowStep);
    const currentIndex = steps.indexOf(currentStep);
    
    if (currentIndex > 0) {
      return steps[currentIndex - 1];
    }
    
    return null;
  }

  /**
   * 錯誤處理
   */
  private handleError = (error: any): Observable<never> => {
    console.error('IndividualService error:', error);
    
    let errorMessage = '系統發生錯誤，請稍後再試';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    // 如果是認證錯誤，重置狀態
    if (error.status === 401 || error.status === 403) {
      this.resetApplicationState();
      errorMessage = '認證失效，請重新開始';
    }
    
    return throwError({ ...error, userMessage: errorMessage });
  };
}