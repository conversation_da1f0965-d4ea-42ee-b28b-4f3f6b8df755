import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { IbrLandingComponent } from './components/landing/landing.component';
import { IIdentitySelectionComponent } from './components/i-identity-selection/i-identity-selection.component';
import { IIdentityVerificationComponent } from './components/i-identity-verification/i-identity-verification.component';
import { IRemittanceDetailComponent } from './components/i-remittance-detail/i-remittance-detail.component';
import { IAmountConfirmationComponent } from './components/i-amount-confirmation/i-amount-confirmation.component';
import { ITransactionConfirmationComponent } from './components/i-transaction-confirmation/i-transaction-confirmation.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'landing',
    pathMatch: 'full'
  },
  // Step 1 - 條款同意頁面
  {
    path: 'landing',
    component: IbrLandingComponent,
    data: { 
      title: '條款同意',
      step: 1,
      totalSteps: 6,
      description: '個資聲明與數位解款條款同意 (圖01)'
    }
  },
  // Step 2 - 選擇驗證方式
  {
    path: 'identity-selection',
    component: IIdentitySelectionComponent,
    data: { 
      title: '選擇驗證方式',
      step: 2,
      totalSteps: 6,
      description: '選擇 FIDO 或 OTP 驗證方式 (圖03)'
    }
  },
  // Step 3 - 身份驗證
  {
    path: 'identity-verification',
    component: IIdentityVerificationComponent,
    data: { 
      title: '身份驗證',
      step: 3,
      totalSteps: 6,
      description: '手機驗證碼與身份確認流程 (圖02-06)'
    }
  },
  // Step 4 - 匯款詳情確認
  {
    path: 'remittance-detail',
    component: IRemittanceDetailComponent,
    data: { 
      title: '匯款詳情確認',
      step: 4,
      totalSteps: 6,
      description: '確認匯款金額與性質 (圖07-09)'
    }
  },
  // Step 5 - 金額確認
  {
    path: 'amount-confirmation',
    component: IAmountConfirmationComponent,
    data: { 
      title: '最終確認',
      step: 5,
      totalSteps: 6,
      description: '完整資訊最終確認 (圖10)'
    }
  },
  // Step 6 - 申請完成
  {
    path: 'transaction-confirmation',
    component: ITransactionConfirmationComponent,
    data: { 
      title: '申請完成',
      step: 6,
      totalSteps: 6,
      description: '解款申請成功頁面 (圖11)'
    }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class IndividualRoutingModule { }