/**
 * 自然人解款模組 - 申請確認與完成相關模型
 * 
 * @description 定義申請確認、申請提交、申請完成所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 申請確認請求介面
 */
export interface ApplicationConfirmationRequest {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 個人資訊 */
  personalInfo?: {
    idNumber: string;
    name: string;
    phoneNumber: string;
    email: string;
    address: string;
  };
  
  /** 匯款資訊 */
  remittanceInfo?: {
    remittanceId: string;
    amount: number;
    currency: string;
    purpose: string;
  };
  
  /** 銀行資訊 */
  bankInfo?: {
    bankCode: string;
    bankName: string;
    accountNumber: string;
    accountName: string;
  };
  
  /** 確認項目 */
  confirmations?: {
    dataAccuracy: boolean;
    termsAgreed: boolean;
    identityVerified: boolean;
    remittanceConfirmed: boolean;
  };
  
  /** 數位簽章資訊 */
  digitalSignature: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 提交時間 */
  submissionTime: string;
}

/**
 * 確認申請資料介面
 */
export interface ConfirmationApplicationData {
  /** 申請人身份資訊 */
  applicantInfo: ApplicantInfo;
  
  /** 匯款詳細資訊 */
  remittanceInfo: ConfirmedRemittanceInfo;
  
  /** 金額確認資訊 */
  amountInfo: ConfirmedAmountInfo;
  
  /** 收款帳戶資訊 */
  receivingAccountInfo: ConfirmedAccountInfo;
  
  /** 聯絡資訊 */
  contactInfo: ContactInfo;
  
  /** 申請類型 */
  applicationType: 'STANDARD' | 'EXPEDITED' | 'SPECIAL';
  
  /** 申請備註 */
  applicationNotes?: string;
}

/**
 * 申請人身份資訊介面
 */
export interface ApplicantInfo {
  /** 身分證字號 */
  idNumber: string;
  
  /** 中文姓名 */
  chineseName: string;
  
  /** 英文姓名 */
  englishName: string;
  
  /** 出生日期 */
  birthDate: string;
  
  /** 國籍 */
  nationality: string;
  
  /** 居住地址 */
  residenceAddress: string;
  
  /** 身份驗證狀態 */
  verificationStatus: IdentityVerificationStatus;
  
  /** 身份驗證時間 */
  verificationTime: string;
}

/**
 * 確認匯款資訊介面
 */
export interface ConfirmedRemittanceInfo {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 匯款日期 */
  remittanceDate: string;
  
  /** 匯款人姓名 */
  remitterName: string;
  
  /** 匯款人國家 */
  remitterCountry: string;
  
  /** 匯款銀行 */
  remitterBank: string;
  
  /** 匯款金額 */
  remittanceAmount: number;
  
  /** 匯款幣別 */
  remittanceCurrency: string;
  
  /** 匯款性質 */
  remittancePurpose: ConfirmedPurpose;
  
  /** 匯款附言 */
  remittanceMessage?: string;
}

/**
 * 確認匯款性質介面
 */
export interface ConfirmedPurpose {
  /** 性質代碼 */
  purposeCode: string;
  
  /** 性質說明 */
  purposeDescription: string;
  
  /** 性質分類 */
  purposeCategory: string;
  
  /** 客戶確認 */
  customerConfirmed: boolean;
  
  /** 確認時間 */
  confirmationTime: string;
}

/**
 * 確認金額資訊介面
 */
export interface ConfirmedAmountInfo {
  /** 原幣金額 */
  originalAmount: number;
  
  /** 原幣幣別 */
  originalCurrency: string;
  
  /** 適用匯率 */
  exchangeRate: number;
  
  /** 台幣總額 */
  twdGrossAmount: number;
  
  /** 手續費總額 */
  totalFees: number;
  
  /** 實收金額 */
  netAmount: number;
  
  /** 金額確認編號 */
  amountConfirmationId: string;
  
  /** 金額確認時間 */
  amountConfirmationTime: string;
}

/**
 * 確認帳戶資訊介面
 */
export interface ConfirmedAccountInfo {
  /** 銀行代碼 */
  bankCode: string;
  
  /** 銀行名稱 */
  bankName: string;
  
  /** 分行代碼 */
  branchCode: string;
  
  /** 分行名稱 */
  branchName: string;
  
  /** 帳戶號碼 */
  accountNumber: string;
  
  /** 帳戶名稱 */
  accountName: string;
  
  /** 帳戶驗證狀態 */
  accountVerificationStatus: 'VERIFIED' | 'PENDING' | 'FAILED';
  
  /** 帳戶驗證時間 */
  accountVerificationTime?: string;
}

/**
 * 聯絡資訊介面
 */
export interface ContactInfo {
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 市內電話 */
  homePhone?: string;
  
  /** 電子郵件 */
  email: string;
  
  /** 通訊地址 */
  mailingAddress: string;
  
  /** 偏好聯絡方式 */
  preferredContactMethod: 'SMS' | 'EMAIL' | 'PHONE' | 'MAIL';
  
  /** 聯絡時間偏好 */
  contactTimePreference?: ContactTimePreference;
}

/**
 * 聯絡時間偏好介面
 */
export interface ContactTimePreference {
  /** 偏好時段 */
  preferredHours: string;
  
  /** 偏好日期 */
  preferredDays: string[];
  
  /** 時區 */
  timezone: string;
}

/**
 * 最終確認聲明介面
 */
export interface FinalConfirmationDeclaration {
  /** 資料正確性確認 */
  dataAccuracyConfirmed: boolean;
  
  /** 條款同意確認 */
  termsAgreedConfirmed: boolean;
  
  /** 手續費同意確認 */
  feesAgreedConfirmed: boolean;
  
  /** 風險告知同意 */
  riskDisclosureAgreed: boolean;
  
  /** 法律責任確認 */
  legalResponsibilityAcknowledged: boolean;
  
  /** 確認聲明文字 */
  confirmationStatement: string;
  
  /** 確認時間 */
  confirmationTimestamp: string;
  
  /** 確認IP位址 */
  confirmationIP: string;
  
  /** 確認設備資訊 */
  deviceInfo?: DeviceInfo;
}

/**
 * 設備資訊介面
 */
export interface DeviceInfo {
  /** 設備類型 */
  deviceType: 'DESKTOP' | 'MOBILE' | 'TABLET';
  
  /** 作業系統 */
  operatingSystem: string;
  
  /** 瀏覽器資訊 */
  browserInfo: string;
  
  /** 螢幕解析度 */
  screenResolution?: string;
  
  /** 設備指紋 */
  deviceFingerprint?: string;
}

/**
 * 數位簽章資訊介面
 */
export interface DigitalSignatureInfo {
  /** 簽章類型 */
  signatureType: 'ELECTRONIC' | 'DIGITAL_CERTIFICATE' | 'BIOMETRIC';
  
  /** 簽章資料 */
  signatureData: string;
  
  /** 簽章時間 */
  signatureTime: string;
  
  /** 簽章憑證資訊 */
  certificateInfo?: CertificateInfo;
  
  /** 簽章驗證狀態 */
  verificationStatus: 'VALID' | 'INVALID' | 'PENDING';
}

/**
 * 憑證資訊介面
 */
export interface CertificateInfo {
  /** 憑證序號 */
  serialNumber: string;
  
  /** 發行者 */
  issuer: string;
  
  /** 憑證主體 */
  subject: string;
  
  /** 有效期開始 */
  validFrom: string;
  
  /** 有效期結束 */
  validTo: string;
  
  /** 憑證指紋 */
  fingerprint: string;
}

/**
 * 申請確認回應介面
 */
export interface ApplicationConfirmationResponse {
  /** 確認是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 申請編號 */
  applicationNumber: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 申請結果 */
  result: {
    /** 申請狀態 */
    applicationStatus: string;
    
    /** 提交時間 */
    submitTime: Date;
    
    /** 預估處理時間 */
    estimatedProcessTime: string;
    
    /** 追蹤代碼 */
    trackingCode: string;
    
    /** 下一步驟 */
    nextSteps: string[];
    
    /** 聯絡資訊 */
    contactInfo: {
      phone: string;
      email: string;
      serviceHours: string;
    };
    
    /** 收據URL */
    receiptUrl?: string;
  };
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * 申請摘要介面
 */
export interface ApplicationSummary {
  /** 申請人姓名 */
  applicantName: string;
  
  /** 申請類型 */
  applicationType: string;
  
  /** 匯款金額摘要 */
  amountSummary: string;
  
  /** 申請時間 */
  applicationTime: string;
  
  /** 預計完成時間 */
  estimatedCompletionTime: string;
  
  /** 參考編號 */
  referenceNumbers: ReferenceNumbers;
  
  /** 重要提醒 */
  importantNotes: string[];
}

/**
 * 參考編號介面
 */
export interface ReferenceNumbers {
  /** 申請編號 */
  applicationNumber: string;
  
  /** 匯款編號 */
  remittanceNumber: string;
  
  /** 交易編號 */
  transactionNumber?: string;
  
  /** 客服案件編號 */
  serviceTicketNumber?: string;
}

/**
 * 處理時程介面
 */
export interface ProcessingSchedule {
  /** 當前階段 */
  currentStage: ProcessingStage;
  
  /** 處理階段列表 */
  stages: ProcessingStageInfo[];
  
  /** 總預估時間 */
  totalEstimatedTime: string;
  
  /** 關鍵里程碑 */
  keyMilestones: Milestone[];
  
  /** 營業時間說明 */
  businessHoursNote: string;
}

/**
 * 處理階段資訊介面
 */
export interface ProcessingStageInfo {
  /** 階段代碼 */
  stageCode: string;
  
  /** 階段名稱 */
  stageName: string;
  
  /** 階段狀態 */
  stageStatus: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'SKIPPED';
  
  /** 預估處理時間 */
  estimatedDuration: string;
  
  /** 實際開始時間 */
  actualStartTime?: string;
  
  /** 實際完成時間 */
  actualEndTime?: string;
  
  /** 階段說明 */
  stageDescription: string;
}

/**
 * 里程碑介面
 */
export interface Milestone {
  /** 里程碑名稱 */
  milestoneName: string;
  
  /** 預計達成時間 */
  expectedTime: string;
  
  /** 實際達成時間 */
  actualTime?: string;
  
  /** 里程碑狀態 */
  status: 'PENDING' | 'ACHIEVED' | 'DELAYED';
  
  /** 里程碑說明 */
  description: string;
}

/**
 * 通知設定介面
 */
export interface NotificationSettings {
  /** 簡訊通知設定 */
  smsNotifications: NotificationPreference;
  
  /** Email通知設定 */
  emailNotifications: NotificationPreference;
  
  /** 推播通知設定 */
  pushNotifications: NotificationPreference;
  
  /** 通知語言 */
  notificationLanguage: 'zh-TW' | 'en-US';
  
  /** 通知頻率 */
  notificationFrequency: 'IMMEDIATE' | 'DAILY_SUMMARY' | 'MILESTONE_ONLY';
}

/**
 * 通知偏好設定介面
 */
export interface NotificationPreference {
  /** 是否啟用 */
  enabled: boolean;
  
  /** 通知類型 */
  notificationTypes: NotificationType[];
  
  /** 通知時間 */
  notificationTime?: string;
  
  /** 聯絡資訊 */
  contactInfo: string;
}

/**
 * 後續動作指引介面
 */
export interface NextStepInstructions {
  /** 立即需要執行的動作 */
  immediateActions: ActionInstruction[];
  
  /** 待辦事項 */
  pendingActions: ActionInstruction[];
  
  /** 重要提醒 */
  importantReminders: string[];
  
  /** 客服聯絡資訊 */
  customerServiceInfo: CustomerServiceInfo;
  
  /** 常見問題連結 */
  faqLinks: FAQLink[];
}

/**
 * 動作指引介面
 */
export interface ActionInstruction {
  /** 動作代碼 */
  actionCode: string;
  
  /** 動作名稱 */
  actionName: string;
  
  /** 動作說明 */
  actionDescription: string;
  
  /** 執行期限 */
  deadline?: string;
  
  /** 優先等級 */
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  
  /** 相關連結 */
  relatedLinks?: string[];
}

/**
 * 客服資訊介面
 */
export interface CustomerServiceInfo {
  /** 客服電話 */
  phoneNumber: string;
  
  /** 客服Email */
  emailAddress: string;
  
  /** 線上客服連結 */
  liveChatUrl?: string;
  
  /** 服務時間 */
  serviceHours: string;
  
  /** 緊急聯絡方式 */
  emergencyContact?: string;
}

/**
 * 常見問題連結介面
 */
export interface FAQLink {
  /** 問題標題 */
  title: string;
  
  /** 問題描述 */
  description: string;
  
  /** 連結URL */
  url: string;
  
  /** 問題分類 */
  category: string;
}

/**
 * 申請狀態查詢請求介面
 */
export interface ApplicationStatusRequest {
  /** 申請編號 */
  applicationNumber: string;
  
  /** 身分證字號 */
  idNumber: string;
  
  /** 查詢類型 */
  queryType: 'BASIC' | 'DETAILED' | 'FULL_HISTORY';
  
  /** 會話Token */
  sessionToken?: string;
}

/**
 * 申請狀態查詢回應介面
 */
export interface ApplicationStatusResponse {
  /** 查詢成功 */
  success: boolean;
  
  /** 申請狀態 */
  currentStatus: ApplicationStatus;
  
  /** 狀態描述 */
  statusDescription: string;
  
  /** 最後更新時間 */
  lastUpdateTime: string;
  
  /** 處理進度 */
  processingProgress: ProcessingProgress;
  
  /** 狀態歷史 */
  statusHistory: StatusHistoryItem[];
  
  /** 預計完成時間 */
  estimatedCompletionTime?: string;
  
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 處理進度介面
 */
export interface ProcessingProgress {
  /** 完成百分比 */
  completionPercentage: number;
  
  /** 當前階段 */
  currentStage: string;
  
  /** 已完成階段數 */
  completedStages: number;
  
  /** 總階段數 */
  totalStages: number;
  
  /** 預估剩餘時間 */
  estimatedRemainingTime?: string;
}

/**
 * 狀態歷史項目介面
 */
export interface StatusHistoryItem {
  /** 狀態 */
  status: ApplicationStatus;
  
  /** 狀態變更時間 */
  changeTime: string;
  
  /** 變更原因 */
  changeReason?: string;
  
  /** 處理人員 */
  processor?: string;
  
  /** 備註 */
  notes?: string;
}

/**
 * 身份驗證狀態枚舉
 */
export enum IdentityVerificationStatus {
  /** 已驗證 */
  VERIFIED = 'VERIFIED',
  
  /** 驗證中 */
  VERIFYING = 'VERIFYING',
  
  /** 驗證失敗 */
  FAILED = 'FAILED',
  
  /** 需要補件 */
  REQUIRES_DOCUMENTS = 'REQUIRES_DOCUMENTS'
}

/**
 * 申請狀態枚舉
 */
export enum ApplicationStatus {
  /** 已提交 */
  SUBMITTED = 'SUBMITTED',
  
  /** 審核中 */
  UNDER_REVIEW = 'UNDER_REVIEW',
  
  /** 已核准 */
  APPROVED = 'APPROVED',
  
  /** 處理中 */
  PROCESSING = 'PROCESSING',
  
  /** 已完成 */
  COMPLETED = 'COMPLETED',
  
  /** 已拒絕 */
  REJECTED = 'REJECTED',
  
  /** 需要補件 */
  REQUIRES_SUPPLEMENTATION = 'REQUIRES_SUPPLEMENTATION',
  
  /** 暫停處理 */
  ON_HOLD = 'ON_HOLD',
  
  /** 已取消 */
  CANCELLED = 'CANCELLED'
}

/**
 * 處理階段枚舉
 */
export enum ProcessingStage {
  /** 申請接收 */
  APPLICATION_RECEIVED = 'APPLICATION_RECEIVED',
  
  /** 資料驗證 */
  DATA_VERIFICATION = 'DATA_VERIFICATION',
  
  /** 身份確認 */
  IDENTITY_CONFIRMATION = 'IDENTITY_CONFIRMATION',
  
  /** 合規檢查 */
  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',
  
  /** 風險評估 */
  RISK_ASSESSMENT = 'RISK_ASSESSMENT',
  
  /** 主管核准 */
  SUPERVISOR_APPROVAL = 'SUPERVISOR_APPROVAL',
  
  /** 匯款處理 */
  REMITTANCE_PROCESSING = 'REMITTANCE_PROCESSING',
  
  /** 款項撥付 */
  FUND_DISBURSEMENT = 'FUND_DISBURSEMENT',
  
  /** 完成確認 */
  COMPLETION_CONFIRMATION = 'COMPLETION_CONFIRMATION'
}

/**
 * 通知類型枚舉
 */
export enum NotificationType {
  /** 狀態更新 */
  STATUS_UPDATE = 'STATUS_UPDATE',
  
  /** 重要提醒 */
  IMPORTANT_REMINDER = 'IMPORTANT_REMINDER',
  
  /** 處理完成 */
  COMPLETION_NOTICE = 'COMPLETION_NOTICE',
  
  /** 異常通知 */
  EXCEPTION_ALERT = 'EXCEPTION_ALERT',
  
  /** 定期報告 */
  REGULAR_REPORT = 'REGULAR_REPORT'
}

/**
 * 申請確認工具類
 */
export class ApplicationConfirmationHelper {
  /**
   * 生成申請編號
   */
  static generateApplicationNumber(): string {
    const prefix = 'IBR';
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * 驗證申請資料完整性
   */
  static validateApplicationData(data: ConfirmationApplicationData): ValidationResult {
    const errors: string[] = [];
    
    if (!data.applicantInfo.idNumber) {
      errors.push('身分證字號為必填');
    }
    
    if (!data.applicantInfo.chineseName) {
      errors.push('中文姓名為必填');
    }
    
    if (!data.contactInfo.mobilePhone) {
      errors.push('手機號碼為必填');
    }
    
    if (!data.contactInfo.email) {
      errors.push('電子郵件為必填');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 格式化申請摘要
   */
  static formatApplicationSummary(data: ConfirmationApplicationData): string {
    const { remittanceInfo, amountInfo } = data;
    return `${remittanceInfo.remittanceCurrency} ${remittanceInfo.remittanceAmount.toLocaleString()} → TWD ${amountInfo.netAmount.toLocaleString()}`;
  }

  /**
   * 計算處理進度百分比
   */
  static calculateProgressPercentage(currentStage: ProcessingStage): number {
    const stageOrder = Object.values(ProcessingStage);
    const currentIndex = stageOrder.indexOf(currentStage);
    return Math.round((currentIndex + 1) / stageOrder.length * 100);
  }

  /**
   * 取得狀態顯示文字
   */
  static getStatusDisplayText(status: ApplicationStatus): string {
    const statusMap: Record<ApplicationStatus, string> = {
      [ApplicationStatus.SUBMITTED]: '已提交',
      [ApplicationStatus.UNDER_REVIEW]: '審核中',
      [ApplicationStatus.APPROVED]: '已核准',
      [ApplicationStatus.PROCESSING]: '處理中',
      [ApplicationStatus.COMPLETED]: '已完成',
      [ApplicationStatus.REJECTED]: '已拒絕',
      [ApplicationStatus.REQUIRES_SUPPLEMENTATION]: '需要補件',
      [ApplicationStatus.ON_HOLD]: '暫停處理',
      [ApplicationStatus.CANCELLED]: '已取消'
    };
    
    return statusMap[status] || status;
  }

  /**
   * 判斷是否為最終狀態
   */
  static isFinalStatus(status: ApplicationStatus): boolean {
    const finalStatuses = [
      ApplicationStatus.COMPLETED,
      ApplicationStatus.REJECTED,
      ApplicationStatus.CANCELLED
    ];
    
    return finalStatuses.includes(status);
  }

  /**
   * 計算預估完成時間
   */
  static calculateEstimatedCompletion(currentStage: ProcessingStage): Date {
    const now = new Date();
    const stageHours: Record<ProcessingStage, number> = {
      [ProcessingStage.APPLICATION_RECEIVED]: 1,
      [ProcessingStage.DATA_VERIFICATION]: 2,
      [ProcessingStage.IDENTITY_CONFIRMATION]: 4,
      [ProcessingStage.COMPLIANCE_CHECK]: 8,
      [ProcessingStage.RISK_ASSESSMENT]: 4,
      [ProcessingStage.SUPERVISOR_APPROVAL]: 24,
      [ProcessingStage.REMITTANCE_PROCESSING]: 12,
      [ProcessingStage.FUND_DISBURSEMENT]: 6,
      [ProcessingStage.COMPLETION_CONFIRMATION]: 1
    };
    
    const stageOrder = Object.values(ProcessingStage);
    const currentIndex = stageOrder.indexOf(currentStage);
    let remainingHours = 0;
    
    for (let i = currentIndex; i < stageOrder.length; i++) {
      remainingHours += stageHours[stageOrder[i]];
    }
    
    const estimatedCompletion = new Date(now.getTime() + remainingHours * 60 * 60 * 1000);
    return estimatedCompletion;
  }
}

/**
 * 驗證結果介面
 */
interface ValidationResult {
  /** 是否有效 */
  isValid: boolean;
  
  /** 錯誤訊息列表 */
  errors: string[];
}