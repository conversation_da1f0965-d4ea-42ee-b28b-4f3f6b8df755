/**
 * 自然人解款模組 - 條款同意頁面相關模型
 * 
 * @description 定義條款同意頁面所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 條款同意資料介面
 */
export interface TermsAgreementData {
  /** 個資告知聲明同意狀態 */
  personalDataAgreed: boolean;
  
  /** 數位解款條款同意狀態 */
  digitalTermsAgreed: boolean;
  
  /** 同意時間戳記 */
  agreementTimestamp: Date;
  
  /** 使用者IP位址 (可選) */
  ipAddress?: string;
  
  /** 會話ID */
  sessionId: string;
  
  /** 使用者身分證號 (可選，用於記錄) */
  idNumber?: string;
}

/**
 * 條款內容介面
 */
export interface TermsContent {
  /** 條款標題 */
  title: string;
  
  /** 條款內容HTML */
  content: string;
  
  /** 條款版本號 */
  version: string;
  
  /** 最後更新時間 */
  lastUpdated: Date;
  
  /** 是否為必要同意項目 */
  mandatory: boolean;
  
  /** 條款類型 */
  type: 'PERSONAL_DATA' | 'DIGITAL_REMITTANCE' | 'SERVICE_TERMS';
}

/**
 * 條款同意請求介面 (更新為符合後端DTO)
 */
export interface TermsAgreementRequest {
  /** 台灣身分證號 */
  taiwanId: string;
  
  /** 同意的條款版本 */
  termsVersion: string;
  
  /** 同意時間 */
  agreedAt: Date;
  
  /** 客戶端IP位址 */
  ipAddress?: string;
  
  /** 瀏覽器User-Agent */
  userAgent?: string;
  
  /** 同意方式 (WEB/MOBILE/API) */
  agreementMethod?: string;
  
  /** 裝置識別碼 */
  deviceId?: string;
  
  /** 瀏覽器指紋 */
  browserFingerprint?: string;
  
  /** 地理位置資訊 */
  geolocation?: {
    latitude?: number;
    longitude?: number;
    accuracy?: number;
    countryCode?: string;
    city?: string;
    timezone?: string;
  };
  
  /** 額外追蹤資訊 */
  additionalInfo?: {
    referrerUrl?: string;
    currentUrl?: string;
    screenResolution?: string;
    browserLanguage?: string;
    operatingSystem?: string;
    isMobile?: boolean;
    networkType?: string;
    sessionId?: string;
  };
  
  /** 個資同意狀態 (前端專用，不傳後端) */
  personalDataAgreed?: boolean;
  
  /** 數位條款同意狀態 (前端專用，不傳後端) */
  digitalTermsAgreed?: boolean;
}

/**
 * 條款同意回應介面
 */
export interface TermsAgreementResponse {
  /** 操作是否成功 */
  success: boolean;
  
  /** 同意記錄ID */
  agreementId: string;
  
  /** 回應訊息 */
  message: string;
  
  /** 下一步導向路由 */
  nextRoute?: string;
  
  /** 錯誤代碼 (如果有錯誤) */
  errorCode?: string;
}

/**
 * 條款查詢參數介面
 */
export interface TermsQueryParams {
  /** 條款類型 */
  type: 'PERSONAL_DATA' | 'DIGITAL_REMITTANCE';
  
  /** 語言代碼 */
  locale?: 'zh-TW' | 'en-US';
  
  /** 是否包含歷史版本 */
  includeHistory?: boolean;
}

/**
 * 條款驗證結果介面
 */
export interface TermsValidationResult {
  /** 驗證是否通過 */
  isValid: boolean;
  
  /** 缺少的必要同意項目 */
  missingAgreements: string[];
  
  /** 驗證錯誤訊息 */
  errors: string[];
  
  /** 警告訊息 */
  warnings: string[];
}

/**
 * 條款同意狀態枚舉
 */
export enum AgreementStatus {
  /** 未同意 */
  NOT_AGREED = 'NOT_AGREED',
  
  /** 已同意 */
  AGREED = 'AGREED',
  
  /** 已撤回 */
  REVOKED = 'REVOKED',
  
  /** 已過期 */
  EXPIRED = 'EXPIRED'
}

/**
 * 條款類型枚舉
 */
export enum TermsType {
  /** 個人資料保護聲明 */
  PERSONAL_DATA = 'PERSONAL_DATA',
  
  /** 數位解款服務條款 */
  DIGITAL_REMITTANCE = 'DIGITAL_REMITTANCE',
  
  /** 一般服務條款 */
  SERVICE_TERMS = 'SERVICE_TERMS'
}