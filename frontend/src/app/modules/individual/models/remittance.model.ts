/**
 * 自然人解款模組 - 匯款查詢與確認相關模型
 * 
 * @description 定義匯款搜尋、查詢結果、匯款詳情所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 匯款搜尋條件介面
 */
export interface RemittanceSearchCriteria {
  /** 身分證字號 */
  idNumber: string;
  
  /** 受益人姓名 */
  beneficiaryName: string;
  
  /** 匯款類型 */
  remittanceType?: string;
  
  /** 日期範圍 */
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  
  /** 幣別篩選 */
  currency?: string;
  
  /** 最小金額 */
  minAmount?: number;
  
  /** 最大金額 */
  maxAmount?: number;
  
  /** 匯款狀態篩選 */
  status?: RemittanceStatus[];
  
  /** 匯款人姓名 */
  remitterName?: string;
  
  /** 匯出國家 */
  remitterCountry?: string;
}

/**
 * 匯款搜尋請求介面
 */
export interface RemittanceSearchRequest {
  /** 搜尋條件 */
  criteria: RemittanceSearchCriteria;
  
  /** 分頁資訊 */
  pagination?: PaginationRequest;
  
  /** 排序條件 */
  sorting?: SortingRequest;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 搜尋類型 */
  searchType: 'EXACT' | 'FUZZY' | 'RANGE';
}

/**
 * 分頁請求介面
 */
export interface PaginationRequest {
  /** 頁碼 (從1開始) */
  page: number;
  
  /** 每頁筆數 */
  pageSize: number;
  
  /** 是否取得總筆數 */
  includeTotalCount?: boolean;
}

/**
 * 排序請求介面
 */
export interface SortingRequest {
  /** 排序欄位 */
  field: 'remittanceDate' | 'amount' | 'currency' | 'status' | 'remitterName';
  
  /** 排序方向 */
  direction: 'ASC' | 'DESC';
}

/**
 * 匯款搜尋回應介面
 */
export interface RemittanceSearchResponse {
  /** 搜尋是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 會話Token */
  sessionToken?: string;
  
  /** 搜尋結果 */
  result: {
    /** 匯款列表 */
    remittances: {
      remittanceId: string;
      remittanceDate: Date;
      senderName: string;
      senderCountry: string;
      beneficiaryName: string;
      amount: number;
      currency: string;
      status: string;
      bankName: string;
      purpose: string;
    }[];
    
    /** 總筆數 */
    totalCount: number;
    
    /** 當前頁數 */
    currentPage: number;
    
    /** 總頁數 */
    totalPages: number;
    
    /** 搜尋條件 */
    searchCriteria: RemittanceSearchCriteria;
    
    /** 是否有更多資料 */
    hasMore: boolean;
  };
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * 分頁回應介面
 */
export interface PaginationResponse {
  /** 當前頁碼 */
  currentPage: number;
  
  /** 每頁筆數 */
  pageSize: number;
  
  /** 總頁數 */
  totalPages: number;
  
  /** 總筆數 */
  totalItems: number;
  
  /** 是否有下一頁 */
  hasNext: boolean;
  
  /** 是否有上一頁 */
  hasPrevious: boolean;
}

/**
 * 搜尋統計介面
 */
export interface SearchStatistics {
  /** 匯款總筆數 */
  totalRemittances: number;
  
  /** 匯款總金額(原幣) */
  totalAmountOriginal: number;
  
  /** 匯款總金額(台幣) */
  totalAmountTWD: number;
  
  /** 涉及的幣別列表 */
  currencies: string[];
  
  /** 狀態分佈 */
  statusDistribution: StatusCount[];
  
  /** 搜尋執行時間(毫秒) */
  searchDuration: number;
}

/**
 * 狀態統計介面
 */
export interface StatusCount {
  /** 狀態 */
  status: RemittanceStatus;
  
  /** 該狀態的筆數 */
  count: number;
  
  /** 該狀態的總金額 */
  totalAmount: number;
}

/**
 * 匯款基本資訊介面
 */
export interface RemittanceInfo {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 匯款日期 */
  remittanceDate: string;
  
  /** 幣別 */
  currency: string;
  
  /** 匯款金額(原幣) */
  amount: number;
  
  /** 匯率 */
  exchangeRate: number;
  
  /** 台幣金額 */
  twdAmount: number;
  
  /** 匯款人資訊 */
  remitter: RemitterInfo;
  
  /** 受款人資訊 */
  beneficiary: BeneficiaryInfo;
  
  /** 匯款狀態 */
  status: RemittanceStatus;
  
  /** 匯款性質 */
  purpose: RemittancePurpose;
  
  /** 解款期限 */
  paymentDeadline: string;
  
  /** 手續費資訊 */
  fees: FeeInfo;
  
  /** 特殊標記 */
  flags: RemittanceFlag[];
}

/**
 * 匯款人資訊介面
 */
export interface RemitterInfo {
  /** 匯款人姓名(英文) */
  nameEnglish: string;
  
  /** 匯款人姓名(中文，如有) */
  nameChinese?: string;
  
  /** 匯款人國家 */
  country: string;
  
  /** 匯款人銀行 */
  bank: string;
  
  /** 匯款人銀行地址 */
  bankAddress?: string;
  
  /** SWIFT代碼 */
  swiftCode?: string;
  
  /** 匯款人地址 */
  address?: string;
  
  /** 聯絡電話 */
  phone?: string;
}

/**
 * 受款人資訊介面
 */
export interface BeneficiaryInfo {
  /** 受款人中文姓名 */
  nameChinese: string;
  
  /** 受款人英文姓名 */
  nameEnglish: string;
  
  /** 身分證字號 */
  idNumber: string;
  
  /** 銀行代碼 */
  bankCode: string;
  
  /** 銀行名稱 */
  bankName: string;
  
  /** 分行代碼 */
  branchCode: string;
  
  /** 分行名稱 */
  branchName: string;
  
  /** 帳戶號碼 */
  accountNumber: string;
  
  /** 聯絡電話 */
  phone?: string;
  
  /** 電子郵件 */
  email?: string;
  
  /** 地址 */
  address?: string;
}

/**
 * 匯款性質介面
 */
export interface RemittancePurpose {
  /** 性質代碼 */
  code: string;
  
  /** 性質說明(中文) */
  descriptionChinese: string;
  
  /** 性質說明(英文) */
  descriptionEnglish: string;
  
  /** 性質分類 */
  category: 'TRADE' | 'SERVICE' | 'INVESTMENT' | 'REMITTANCE' | 'OTHER';
  
  /** 是否需要額外文件 */
  requiresDocuments: boolean;
  
  /** 額外說明 */
  additionalNotes?: string;
}

/**
 * 手續費資訊介面
 */
export interface FeeInfo {
  /** 匯款手續費(原幣) */
  remittanceFee: number;
  
  /** 解款手續費(台幣) */
  paymentFee: number;
  
  /** 郵電費(台幣) */
  telegraphicFee: number;
  
  /** 其他費用(台幣) */
  otherFees: number;
  
  /** 總手續費(台幣) */
  totalFees: number;
  
  /** 實收金額(台幣) */
  netAmount: number;
  
  /** 費用明細 */
  feeDetails: FeeDetail[];
}

/**
 * 手續費明細介面
 */
export interface FeeDetail {
  /** 費用類型 */
  type: string;
  
  /** 費用名稱 */
  name: string;
  
  /** 費用金額 */
  amount: number;
  
  /** 費用幣別 */
  currency: string;
  
  /** 費用說明 */
  description?: string;
}

/**
 * 匯款詳情查詢請求介面
 */
export interface RemittanceDetailRequest {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 身分證字號 */
  idNumber: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 查詢類型 */
  queryType: 'BASIC' | 'DETAILED' | 'FULL';
}

/**
 * 匯款詳情回應介面
 */
export interface RemittanceDetailResponse {
  /** 查詢是否成功 */
  success: boolean;
  
  /** 匯款詳細資訊 */
  remittanceDetail: RemittanceDetail;
  
  /** 可執行的操作 */
  availableActions: AvailableAction[];
  
  /** 相關文件列表 */
  documents: DocumentInfo[];
  
  /** 處理歷史 */
  processHistory: ProcessHistoryItem[];
  
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 匯款詳情介面
 */
export interface RemittanceDetail extends RemittanceInfo {
  /** 匯款指示 */
  paymentInstructions: string;
  
  /** 匯款附言 */
  remittanceMessage?: string;
  
  /** 收費指示 */
  chargeInstruction: 'OUR' | 'BEN' | 'SHA';
  
  /** 中介銀行資訊 */
  intermediaryBank?: IntermediaryBankInfo;
  
  /** 覆核資訊 */
  reviewInfo?: ReviewInfo;
  
  /** 合規檢查結果 */
  complianceCheck?: ComplianceCheckResult;
  
  /** 預計到帳時間 */
  expectedArrivalTime?: string;
  
  /** 實際到帳時間 */
  actualArrivalTime?: string;
}

/**
 * 中介銀行資訊介面
 */
export interface IntermediaryBankInfo {
  /** 銀行名稱 */
  bankName: string;
  
  /** SWIFT代碼 */
  swiftCode: string;
  
  /** 銀行地址 */
  address: string;
  
  /** 帳戶號碼 */
  accountNumber?: string;
}

/**
 * 覆核資訊介面
 */
export interface ReviewInfo {
  /** 覆核狀態 */
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'REQUIRED_INFO';
  
  /** 覆核人員 */
  reviewer?: string;
  
  /** 覆核時間 */
  reviewTime?: string;
  
  /** 覆核意見 */
  comments?: string;
  
  /** 需補充資料 */
  requiredDocuments?: string[];
}

/**
 * 合規檢查結果介面
 */
export interface ComplianceCheckResult {
  /** 檢查狀態 */
  status: 'PASS' | 'FAIL' | 'PENDING' | 'MANUAL_REVIEW';
  
  /** AML檢查結果 */
  amlCheck: 'CLEAR' | 'HIT' | 'PENDING';
  
  /** 制裁名單檢查 */
  sanctionCheck: 'CLEAR' | 'HIT' | 'PENDING';
  
  /** 風險評分 */
  riskScore: number;
  
  /** 風險等級 */
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  
  /** 檢查時間 */
  checkTime: string;
  
  /** 檢查備註 */
  notes?: string;
}

/**
 * 可執行操作介面
 */
export interface AvailableAction {
  /** 操作代碼 */
  actionCode: string;
  
  /** 操作名稱 */
  actionName: string;
  
  /** 操作描述 */
  description: string;
  
  /** 是否可執行 */
  isEnabled: boolean;
  
  /** 不可執行原因 */
  disabledReason?: string;
  
  /** 操作類型 */
  actionType: 'PRIMARY' | 'SECONDARY' | 'DANGER';
}

/**
 * 文件資訊介面
 */
export interface DocumentInfo {
  /** 文件ID */
  documentId: string;
  
  /** 文件名稱 */
  fileName: string;
  
  /** 文件類型 */
  documentType: string;
  
  /** 文件大小(bytes) */
  fileSize: number;
  
  /** 上傳時間 */
  uploadTime: string;
  
  /** 文件狀態 */
  status: 'ACTIVE' | 'ARCHIVED' | 'DELETED';
  
  /** 下載URL */
  downloadUrl?: string;
}

/**
 * 處理歷史項目介面
 */
export interface ProcessHistoryItem {
  /** 處理時間 */
  processTime: string;
  
  /** 處理動作 */
  action: string;
  
  /** 處理狀態 */
  status: string;
  
  /** 處理人員 */
  processor?: string;
  
  /** 處理備註 */
  remarks?: string;
  
  /** 系統資訊 */
  systemInfo?: string;
}

/**
 * 匯款確認請求介面
 */
export interface RemittanceConfirmRequest {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 確認資料 */
  confirmationData: RemittanceConfirmationData;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 確認類型 */
  confirmationType: 'ACCEPT' | 'REJECT' | 'MODIFY';
}

/**
 * 匯款確認資料介面
 */
export interface RemittanceConfirmationData {
  /** 確認接受解款 */
  acceptRemittance: boolean;
  
  /** 受款人聯絡Email */
  contactEmail: string;
  
  /** 特殊指示 */
  specialInstructions?: string;
  
  /** 拒絕原因(如適用) */
  rejectionReason?: string;
  
  /** 修改要求(如適用) */
  modificationRequest?: ModificationRequest;
}

/**
 * 修改要求介面
 */
export interface ModificationRequest {
  /** 修改類型 */
  modificationType: 'BENEFICIARY_INFO' | 'BANK_ACCOUNT' | 'PURPOSE' | 'OTHER';
  
  /** 修改原因 */
  reason: string;
  
  /** 修改內容 */
  modificationDetails: any;
  
  /** 支持文件 */
  supportingDocuments?: string[];
}

/**
 * 匯款確認回應介面
 */
export interface RemittanceConfirmResponse {
  /** 確認是否成功 */
  success: boolean;
  
  /** 確認編號 */
  confirmationId: string;
  
  /** 處理狀態 */
  processingStatus: ProcessingStatus;
  
  /** 預計處理時間 */
  estimatedProcessingTime: string;
  
  /** 下一步指示 */
  nextStepInstructions: string;
  
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 匯款狀態枚舉
 */
export enum RemittanceStatus {
  /** 待處理 */
  PENDING = 'PENDING',
  
  /** 處理中 */
  PROCESSING = 'PROCESSING',
  
  /** 等待解款 */
  AWAITING_PAYMENT = 'AWAITING_PAYMENT',
  
  /** 已解款 */
  PAID = 'PAID',
  
  /** 已退匯 */
  RETURNED = 'RETURNED',
  
  /** 已取消 */
  CANCELLED = 'CANCELLED',
  
  /** 凍結中 */
  FROZEN = 'FROZEN',
  
  /** 需要補件 */
  REQUIRES_DOCUMENTS = 'REQUIRES_DOCUMENTS',
  
  /** 覆核中 */
  UNDER_REVIEW = 'UNDER_REVIEW'
}

/**
 * 處理狀態枚舉
 */
export enum ProcessingStatus {
  /** 已接受 */
  ACCEPTED = 'ACCEPTED',
  
  /** 已拒絕 */
  REJECTED = 'REJECTED',
  
  /** 修改中 */
  UNDER_MODIFICATION = 'UNDER_MODIFICATION',
  
  /** 等待審核 */
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  
  /** 處理完成 */
  COMPLETED = 'COMPLETED',
  
  /** 處理失敗 */
  FAILED = 'FAILED'
}

/**
 * 匯款標記枚舉
 */
export enum RemittanceFlag {
  /** 高風險 */
  HIGH_RISK = 'HIGH_RISK',
  
  /** 大額交易 */
  LARGE_AMOUNT = 'LARGE_AMOUNT',
  
  /** 急件 */
  URGENT = 'URGENT',
  
  /** VIP客戶 */
  VIP_CUSTOMER = 'VIP_CUSTOMER',
  
  /** 需人工處理 */
  MANUAL_PROCESSING = 'MANUAL_PROCESSING',
  
  /** 名單比對命中 */
  WATCHLIST_HIT = 'WATCHLIST_HIT',
  
  /** 補件中 */
  SUPPLEMENTING = 'SUPPLEMENTING'
}

/**
 * 匯款查詢工具類
 */
export class RemittanceQueryHelper {
  /**
   * 建立基本搜尋條件
   */
  static createBasicSearchCriteria(idNumber: string, name: string): RemittanceSearchCriteria {
    return {
      idNumber,
      beneficiaryName: name,
      dateRange: {
        startDate: this.getDateDaysAgo(365), // 預設搜尋一年內
        endDate: this.getTodayString()
      }
    };
  }

  /**
   * 格式化匯款金額顯示
   */
  static formatAmount(amount: number, currency: string): string {
    const formatter = new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    
    return formatter.format(amount);
  }

  /**
   * 計算匯款天數
   */
  static calculateDaysFromRemittance(remittanceDate: string): number {
    const today = new Date();
    const remitDate = new Date(remittanceDate);
    const diffTime = today.getTime() - remitDate.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  /**
   * 檢查是否接近解款期限
   */
  static isNearDeadline(deadlineDate: string, warningDays = 3): boolean {
    const today = new Date();
    const deadline = new Date(deadlineDate);
    const diffTime = deadline.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays <= warningDays && diffDays >= 0;
  }

  /**
   * 取得N天前的日期字串
   */
  private static getDateDaysAgo(days: number): string {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
  }

  /**
   * 取得今天的日期字串
   */
  private static getTodayString(): string {
    return new Date().toISOString().split('T')[0];
  }

  /**
   * 驗證匯款編號格式
   */
  static validateRemittanceId(remittanceId: string): boolean {
    // 匯款編號格式: 通常為字母+數字組合，長度10-20字元
    const pattern = /^[A-Z0-9]{10,20}$/;
    return pattern.test(remittanceId);
  }

  /**
   * 取得狀態顯示文字
   */
  static getStatusDisplayText(status: RemittanceStatus): string {
    const statusMap: Record<RemittanceStatus, string> = {
      [RemittanceStatus.PENDING]: '待處理',
      [RemittanceStatus.PROCESSING]: '處理中',
      [RemittanceStatus.AWAITING_PAYMENT]: '等待解款',
      [RemittanceStatus.PAID]: '已解款',
      [RemittanceStatus.RETURNED]: '已退匯',
      [RemittanceStatus.CANCELLED]: '已取消',
      [RemittanceStatus.FROZEN]: '凍結中',
      [RemittanceStatus.REQUIRES_DOCUMENTS]: '需要補件',
      [RemittanceStatus.UNDER_REVIEW]: '覆核中'
    };
    
    return statusMap[status] || status;
  }
}