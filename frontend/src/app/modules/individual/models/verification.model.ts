/**
 * 自然人解款模組 - OTP驗證相關模型
 * 
 * @description 定義OTP雙重驗證、手機驗證所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 驗證類型聯合類型（已移除，使用下方的枚舉定義）
 */
// export type VerificationType = 'IDENTITY_VERIFICATION' | 'REGISTRATION' | 'AUTHENTICATION' | 'TRANSACTION';

/**
 * OTP驗證請求介面
 */
export interface OtpVerificationRequest {
  /** 身份證號 (可選) */
  idNumber?: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 驗證類型 */
  verificationType: VerificationType;
  
  /** 請求ID */
  requestId: string;
  
  /** 時間戳記 */
  timestamp: string;
  
  /** 簡訊內容模板 */
  messageTemplate?: string;
  
  /** 驗證碼長度 */
  codeLength?: number;
  
  /** 有效期限（秒） */
  expiryDuration?: number;
}

/**
 * OTP驗證碼發送回應介面
 */
export interface OtpSendResponse {
  /** 發送是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 驗證Token */
  verificationToken: string;
  
  /** 訊息ID */
  messageId: string;
  
  /** 發送時間 */
  sentAt: string;
  
  /** 過期時間 */
  expiresAt: string;
  
  /** 剩餘重發次數 */
  remainingResends: number;
  
  /** 下次重發可用時間 */
  resendAvailableAt: string;
  
  /** 驗證結果詳情 */
  result: {
    status: OtpStatus;
    deliveryMethod: string;
    maskedTarget: string;
    codeLength: number;
    expiryDuration: number;
    attemptCount: number;
    maxAttempts: number;
  };
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * OTP驗證碼確認請求介面
 */
export interface OtpVerifyRequest {
  /** 驗證Token */
  verificationToken: string;
  
  /** 使用者輸入的驗證碼 */
  verificationCode: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 請求ID */
  requestId: string;
  
  /** 時間戳記 */
  timestamp: string;
}

/**
 * OTP驗證碼確認回應介面
 */
export interface OtpVerifyResponse {
  /** 驗證是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 新的會話Token */
  newSessionToken?: string;
  
  /** 驗證結果 */
  result: OtpVerificationResult;
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * OTP重發請求介面
 */
export interface OtpResendRequest {
  /** 驗證Token */
  verificationToken: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 重發原因 */
  reason: 'USER_REQUEST' | 'AUTO_RETRY' | 'TIMEOUT';
  
  /** 時間戳記 */
  timestamp: string;
}

/**
 * OTP重發回應介面
 */
export interface OtpResendResponse {
  /** 重發是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 新的驗證Token */
  newVerificationToken: string;
  
  /** 發送時間 */
  sentAt: string;
  
  /** 過期時間 */
  expiresAt: string;
  
  /** 剩餘重發次數 */
  remainingResends: number;
  
  /** 下次重發可用時間 */
  resendAvailableAt: string;
  
  /** 重發結果詳情 */
  result: {
    deliveryMethod: string;
    attemptCount: number;
    maxResends: number;
  };
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * OTP驗證結果介面
 */
export interface OtpVerificationResult {
  /** 驗證狀態 */
  status: OtpStatus;
  
  /** 驗證等級 */
  verificationLevel?: 'HIGH' | 'MEDIUM' | 'LOW' | 'NONE';
  
  /** 驗證時間 */
  verifiedAt?: string;
  
  /** 嘗試次數 */
  attemptCount: number;
  
  /** 剩餘嘗試次數 */
  remainingAttempts: number;
  
  /** 鎖定時間長度（秒） */
  lockoutDuration?: number;
}

/**
 * 手機驗證資料介面
 */
export interface MobileVerificationData {
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 國碼 */
  countryCode: string;
  
  /** 號碼格式化顯示 */
  formattedNumber: string;
  
  /** 電信業者 */
  carrier?: string;
  
  /** 號碼類型 */
  numberType: 'MOBILE' | 'LANDLINE' | 'UNKNOWN';
  
  /** 是否為有效號碼 */
  isValid: boolean;
  
  /** 上次驗證時間 */
  lastVerifiedAt?: string;
}

/**
 * OTP設定介面
 */
export interface OtpConfiguration {
  /** 驗證碼長度 */
  codeLength: number;
  
  /** 有效期限（秒） */
  expiryDuration: number;
  
  /** 最大嘗試次數 */
  maxAttempts: number;
  
  /** 鎖定時間（秒） */
  lockDuration: number;
  
  /** 最大重發次數 */
  maxResends: number;
  
  /** 重發間隔（秒） */
  resendInterval: number;
  
  /** 簡訊模板 */
  messageTemplate: string;
  
  /** 發送者ID */
  senderId: string;
}

/**
 * 驗證歷史記錄介面
 */
export interface VerificationHistory {
  /** 記錄ID */
  id: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 驗證類型 */
  verificationType: VerificationType;
  
  /** 驗證狀態 */
  status: OtpStatus;
  
  /** 發送時間 */
  sentAt: string;
  
  /** 驗證時間 */
  verifiedAt?: string;
  
  /** 嘗試次數 */
  attemptCount: number;
  
  /** IP位址 */
  ipAddress: string;
  
  /** 使用者代理 */
  userAgent?: string;
}

/**
 * 重發請求介面
 */
export interface OtpResendRequest {
  /** 驗證Token */
  verificationToken: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 重發原因 */
  reason: 'USER_REQUEST' | 'AUTO_RETRY' | 'TIMEOUT';
  
  /** 時間戳記 */
  timestamp: string;
}

/**
 * 重發回應介面
 */
export interface OtpResendResponse {
  /** 重發是否成功 */
  success: boolean;
  
  /** 新的驗證Token */
  newVerificationToken: string;
  
  /** 剩餘重發次數 */
  remainingResends: number;
  
  /** 下次可重發時間 */
  nextResendAt: string;
  
  /** 新的過期時間 */
  expiresAt: string;
  
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 批次驗證請求介面（用於同時驗證多個項目）
 */
export interface BatchVerificationRequest {
  /** 驗證項目列表 */
  verifications: SingleVerificationItem[];
  
  /** 批次會話Token */
  batchSessionToken: string;
  
  /** 批次驗證類型 */
  batchType: 'PARALLEL' | 'SEQUENTIAL';
  
  /** 時間戳記 */
  timestamp: string;
}

/**
 * 單一驗證項目介面
 */
export interface SingleVerificationItem {
  /** 項目ID */
  itemId: string;
  
  /** 驗證類型 */
  type: VerificationType;
  
  /** 驗證資料 */
  data: any;
  
  /** 優先順序 */
  priority: number;
}

/**
 * 批次驗證回應介面
 */
export interface BatchVerificationResponse {
  /** 批次是否成功 */
  batchSuccess: boolean;
  
  /** 各項驗證結果 */
  results: VerificationItemResult[];
  
  /** 總體驗證分數 */
  overallScore: number;
  
  /** 處理時間（毫秒） */
  processingTime: number;
  
  /** 失敗項目數量 */
  failedCount: number;
}

/**
 * 驗證項目結果介面
 */
export interface VerificationItemResult {
  /** 項目ID */
  itemId: string;
  
  /** 驗證是否成功 */
  success: boolean;
  
  /** 驗證狀態 */
  status: OtpStatus;
  
  /** 錯誤訊息 */
  errorMessage?: string;
  
  /** 處理時間（毫秒） */
  processingTime: number;
}

/**
 * 驗證統計資訊介面
 */
export interface VerificationStatistics {
  /** 總驗證次數 */
  totalVerifications: number;
  
  /** 成功次數 */
  successfulVerifications: number;
  
  /** 失敗次數 */
  failedVerifications: number;
  
  /** 成功率 */
  successRate: number;
  
  /** 平均驗證時間（秒） */
  averageVerificationTime: number;
  
  /** 最常使用的電信業者 */
  mostUsedCarrier?: string;
  
  /** 統計期間 */
  statisticsPeriod: {
    from: string;
    to: string;
  };
}

/**
 * OTP狀態枚舉
 */
export enum OtpStatus {
  /** 待發送 */
  PENDING = 'PENDING',
  
  /** 已發送 */
  SENT = 'SENT',
  
  /** 驗證成功 */
  VERIFIED = 'VERIFIED',
  
  /** 驗證失敗 */
  FAILED = 'FAILED',
  
  /** 已過期 */
  EXPIRED = 'EXPIRED',
  
  /** 已鎖定 */
  LOCKED = 'LOCKED',
  
  /** 已取消 */
  CANCELLED = 'CANCELLED'
}

/**
 * 驗證類型枚舉
 */
export enum VerificationType {
  /** 身份驗證 */
  IDENTITY_VERIFICATION = 'IDENTITY_VERIFICATION',
  
  /** 註冊驗證 */
  REGISTRATION = 'REGISTRATION',
  
  /** 登入驗證 */
  AUTHENTICATION = 'AUTHENTICATION',
  
  /** 交易驗證 */
  TRANSACTION = 'TRANSACTION',
  
  /** 身份確認 */
  IDENTITY_CONFIRMATION = 'IDENTITY_CONFIRMATION',
  
  /** 手機號碼更新 */
  PHONE_UPDATE = 'PHONE_UPDATE',
  
  /** 密碼重設 */
  PASSWORD_RESET = 'PASSWORD_RESET'
}

/**
 * 錯誤類型枚舉
 */
export enum OtpErrorType {
  /** 發送失敗 */
  SEND_FAILED = 'SEND_FAILED',
  
  /** 驗證碼錯誤 */
  INVALID_CODE = 'INVALID_CODE',
  
  /** 已過期 */
  EXPIRED = 'EXPIRED',
  
  /** 超過嘗試次數 */
  MAX_ATTEMPTS_EXCEEDED = 'MAX_ATTEMPTS_EXCEEDED',
  
  /** 號碼無效 */
  INVALID_PHONE = 'INVALID_PHONE',
  
  /** 系統錯誤 */
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  
  /** 網路錯誤 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  
  /** 服務暫時不可用 */
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

/**
 * 台灣手機號碼驗證工具類
 */
export class TaiwanMobileValidator {
  /**
   * 驗證台灣手機號碼格式
   */
  static validate(phoneNumber: string): boolean {
    if (!phoneNumber) {
      return false;
    }

    // 移除所有非數字字符
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    
    // 台灣手機號碼格式：09xxxxxxxx (10位數)
    const pattern = /^09\d{8}$/;
    return pattern.test(cleanNumber);
  }

  /**
   * 格式化手機號碼顯示
   */
  static format(phoneNumber: string): string {
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    if (cleanNumber.length === 10 && cleanNumber.startsWith('09')) {
      return `${cleanNumber.substring(0, 4)}-${cleanNumber.substring(4, 7)}-${cleanNumber.substring(7)}`;
    }
    return phoneNumber;
  }

  /**
   * 遮罩手機號碼（隱私保護）
   */
  static mask(phoneNumber: string): string {
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    if (cleanNumber.length === 10) {
      return `${cleanNumber.substring(0, 4)}***${cleanNumber.substring(7)}`;
    }
    return phoneNumber;
  }

  /**
   * 取得電信業者識別
   */
  static getCarrier(phoneNumber: string): string | null {
    const cleanNumber = phoneNumber.replace(/\D/g, '');
    if (!this.validate(phoneNumber)) {
      return null;
    }

    const prefix = cleanNumber.substring(0, 4);
    const carrierMap: Record<string, string> = {
      '0910': '中華電信', '0911': '中華電信', '0912': '中華電信', '0919': '中華電信',
      '0920': '台灣大哥大', '0921': '台灣大哥大', '0928': '台灣大哥大', '0929': '台灣大哥大',
      '0930': '遠傳電信', '0931': '遠傳電信', '0932': '遠傳電信', '0933': '遠傳電信',
      '0934': '遠傳電信', '0936': '遠傳電信', '0937': '遠傳電信', '0938': '遠傳電信',
      '0939': '遠傳電信',
      '0955': '台灣之星', '0956': '台灣之星', '0975': '台灣之星', '0976': '台灣之星',
      '0977': '台灣之星',
      '0905': '亞太電信', '0906': '亞太電信', '0907': '亞太電信'
    };

    return carrierMap[prefix] || '其他業者';
  }
}