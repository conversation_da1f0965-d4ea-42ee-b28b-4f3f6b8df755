/**
 * 自然人解款模組 - 金額計算與確認相關模型
 * 
 * @description 定義匯率計算、手續費計算、金額確認所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 金額計算請求介面
 */
export interface AmountCalculationRequest {
  /** 匯款編號 */
  remittanceId: string;
  
  /** 原幣金額 (可選) */
  amount?: number;
  
  /** 原幣金額 */
  originalAmount?: number;
  
  /** 原幣幣別 */
  originalCurrency?: string;
  
  /** 目標幣別 (通常為TWD) */
  targetCurrency: string;
  
  /** 計算類型 */
  calculationType: 'STANDARD' | 'PREFERENTIAL' | 'VIP';
  
  /** 受款人身分證號 */
  beneficiaryId?: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 計算日期 (預設為當日) */
  calculationDate?: string;
}

/**
 * 金額計算回應介面
 */
export interface AmountCalculationResponse {
  /** 計算是否成功 */
  success: boolean;
  
  /** 計算結果 */
  result?: AmountCalculationResult;
  
  /** 計算結果 (向後相容) */
  calculation?: AmountCalculationResult;
  
  /** 匯率資訊 */
  exchangeRateInfo?: ExchangeRateInfo;
  
  /** 手續費明細 */
  feeBreakdown?: FeeBreakdown;
  
  /** 優惠資訊 */
  promotions?: PromotionInfo[];
  
  /** 計算有效期限 */
  validUntil?: string;
  
  /** 原幣幣別 */
  originalCurrency?: string;
  
  /** 目標幣別 */
  targetCurrency?: string;
  
  /** 錯誤訊息 */
  message?: string;
  
  /** 錯誤訊息 (向後相容) */
  errorMessage?: string;
}

/**
 * 金額計算結果介面
 */
export interface AmountCalculationResult {
  /** 原幣金額 */
  originalAmount: number;
  
  /** 原幣幣別 */
  originalCurrency?: string;
  
  /** 原幣幣別 (向後相容) */
  fromCurrency?: string;
  
  /** 目標幣別 */
  toCurrency?: string;
  
  /** 適用匯率 */
  appliedExchangeRate?: number;
  
  /** 適用匯率 (向後相容) */
  exchangeRate?: number;
  
  /** 台幣總額（解款前） */
  twdGrossAmount?: number;
  
  /** 總手續費（台幣） */
  totalFees: number;
  
  /** 實收金額（台幣） */
  twdNetAmount?: number;
  
  /** 最終金額 */
  finalAmount?: number;
  
  /** 計算時間 */
  calculationTime?: string;
  
  /** 計算序號 */
  calculationId: string;
  
  /** 手續費明細 */
  feeBreakdown?: FeeStructure;
  
  /** 匯率保證期限 */
  rateGuaranteeExpiry?: string;
}

/**
 * 匯率資訊介面
 */
export interface ExchangeRateInfo {
  /** 幣別對 */
  currencyPair: string;
  
  /** 現鈔買入價 */
  cashBuyingRate: number;
  
  /** 現鈔賣出價 */
  cashSellingRate: number;
  
  /** 即期匯率 */
  spotRate: number;
  
  /** 適用匯率 */
  appliedRate: number;
  
  /** 匯率類型 */
  rateType: 'SPOT' | 'CASH' | 'PREFERENTIAL';
  
  /** 匯率更新時間 */
  rateUpdateTime: string;
  
  /** 匯率來源 */
  rateSource: string;
  
  /** 匯率保證期限 */
  guaranteePeriod?: number;
  
  /** 匯率備註 */
  rateNotes?: string;
}

/**
 * 費用結構介面 (與 FeeBreakdown 相同，用於向後相容)
 */
export interface FeeStructure {
  /** 解款手續費 */
  paymentFee: FeeItem;
  
  /** 匯款手續費 */
  remittanceFee: FeeItem;
  
  /** 郵電費 */
  telegraphicFee: FeeItem;
  
  /** 其他費用 */
  otherFees: FeeItem[];
  
  /** 折扣費用 */
  discounts: DiscountItem[];
  
  /** 總手續費 */
  totalFees: number;
  
  /** 費用幣別 */
  feeCurrency: string;
  
  /** 費用計算基準 */
  calculationBasis: 'AMOUNT_BASED' | 'FLAT_RATE' | 'TIERED';
}

/**
 * 金額明細介面
 */
export interface AmountBreakdown {
  /** 原始金額 */
  originalAmount: number;
  
  /** 原始幣別 */
  originalCurrency: string;
  
  /** 匯率 */
  exchangeRate: number;
  
  /** 台幣總額 */
  twdGrossAmount: number;
  
  /** 手續費 */
  fees: number;
  
  /** 台幣淨額 */
  twdNetAmount: number;
  
  /** 計算時間 */
  calculationTime: string;
}

/**
 * 手續費明細介面
 */
export interface FeeBreakdown {
  /** 解款手續費 */
  paymentFee: FeeItem;
  
  /** 匯款手續費 */
  remittanceFee: FeeItem;
  
  /** 郵電費 */
  telegraphicFee: FeeItem;
  
  /** 其他費用 */
  otherFees: FeeItem[];
  
  /** 折扣費用 */
  discounts: DiscountItem[];
  
  /** 總手續費 */
  totalFees: number;
  
  /** 費用幣別 */
  feeCurrency: string;
  
  /** 費用計算基準 */
  calculationBasis: 'AMOUNT_BASED' | 'FLAT_RATE' | 'TIERED';
}

/**
 * 手續費項目介面
 */
export interface FeeItem {
  /** 費用代碼 */
  feeCode: string;
  
  /** 費用名稱 */
  feeName: string;
  
  /** 費用金額 */
  amount: number;
  
  /** 費用幣別 */
  currency: string;
  
  /** 費率 (如適用) */
  rate?: number;
  
  /** 最低收費 */
  minimumFee?: number;
  
  /** 最高收費 */
  maximumFee?: number;
  
  /** 費用描述 */
  description?: string;
  
  /** 是否為必要費用 */
  isMandatory: boolean;
}

/**
 * 折扣項目介面
 */
export interface DiscountItem {
  /** 折扣代碼 */
  discountCode: string;
  
  /** 折扣名稱 */
  discountName: string;
  
  /** 折扣金額 */
  discountAmount: number;
  
  /** 折扣類型 */
  discountType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'WAIVED';
  
  /** 折扣原因 */
  reason: string;
  
  /** 適用條件 */
  conditions?: string;
}

/**
 * 優惠資訊介面
 */
export interface PromotionInfo {
  /** 優惠代碼 */
  promotionCode: string;
  
  /** 優惠名稱 */
  promotionName: string;
  
  /** 優惠類型 */
  promotionType: 'FEE_DISCOUNT' | 'RATE_BONUS' | 'CASHBACK' | 'POINTS';
  
  /** 優惠金額/比例 */
  benefitValue: number;
  
  /** 優惠描述 */
  description: string;
  
  /** 適用條件 */
  eligibilityCriteria: string;
  
  /** 優惠期限 */
  validUntil: string;
  
  /** 是否已套用 */
  isApplied: boolean;
}

/**
 * 金額確認請求介面
 */
export interface AmountConfirmationRequest {
  /** 計算序號 */
  calculationId: string;
  
  /** 匯款編號 */
  remittanceId: string;
  
  /** 確認金額 */
  confirmedAmount: number;
  
  /** 確認幣別 */
  confirmedCurrency: string;
  
  /** 確認匯率 */
  confirmedRate: number;
  
  /** 費用接受度 */
  feeAcceptance: any;
  
  /** 匯率接受度 */
  rateAcceptance: boolean;
  
  /** 最終確認 */
  finalConfirmation: boolean;
  
  /** 備註 */
  notes?: string;
  
  /** 確認的金額資訊 */
  confirmedAmountInfo?: ConfirmedAmountInfo;
  
  /** 付款方式 */
  paymentMethod?: PaymentMethod;
  
  /** 受款帳戶資訊 */
  receivingAccount?: ReceivingAccountInfo;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 確認時間 */
  confirmationTime: Date | string;
}

/**
 * 確認金額資訊介面
 */
export interface ConfirmedAmountInfo {
  /** 原幣金額 */
  originalAmount: number;
  
  /** 確認匯率 */
  confirmedRate: number;
  
  /** 台幣總額 */
  twdGrossAmount: number;
  
  /** 確認手續費 */
  confirmedFees: number;
  
  /** 最終實收金額 */
  finalNetAmount: number;
  
  /** 客戶確認同意 */
  customerAgreed: boolean;
  
  /** 確認備註 */
  confirmationNotes?: string;
}

/**
 * 付款方式介面
 */
export interface PaymentMethod {
  /** 付款方式代碼 */
  methodCode: string;
  
  /** 付款方式名稱 */
  methodName: string;
  
  /** 付款方式類型 */
  methodType: 'BANK_TRANSFER' | 'CASH' | 'CHECK' | 'ELECTRONIC';
  
  /** 是否需要額外手續費 */
  hasAdditionalFee: boolean;
  
  /** 額外手續費 */
  additionalFee?: number;
  
  /** 預計到帳時間 */
  expectedSettlementTime: string;
  
  /** 付款說明 */
  instructions?: string;
}

/**
 * 收款帳戶資訊介面
 */
export interface ReceivingAccountInfo {
  /** 銀行代碼 */
  bankCode: string;
  
  /** 銀行名稱 */
  bankName: string;
  
  /** 分行代碼 */
  branchCode: string;
  
  /** 分行名稱 */
  branchName: string;
  
  /** 帳戶號碼 */
  accountNumber: string;
  
  /** 帳戶名稱 */
  accountName: string;
  
  /** 帳戶類型 */
  accountType: 'SAVINGS' | 'CHECKING' | 'TIME_DEPOSIT';
  
  /** 帳戶狀態 */
  accountStatus: 'ACTIVE' | 'DORMANT' | 'FROZEN' | 'CLOSED';
  
  /** 是否為本人帳戶 */
  isOwnAccount: boolean;
}

/**
 * 金額確認回應介面
 */
export interface AmountConfirmationResponse {
  /** 確認是否成功 */
  success: boolean;
  
  /** 確認編號 */
  confirmationId?: string;
  
  /** 處理狀態 */
  processingStatus?: AmountProcessingStatus;
  
  /** 交易記錄 */
  transactionRecord?: TransactionRecord;
  
  /** 預計入帳時間 */
  expectedCreditTime?: string;
  
  /** 通知設定 */
  notificationSettings?: NotificationSettings;
  
  /** 訊息 */
  message?: string;
  
  /** 錯誤訊息 */
  errorMessage?: string;
}

/**
 * 交易記錄介面
 */
export interface TransactionRecord {
  /** 交易編號 */
  transactionId: string;
  
  /** 交易時間 */
  transactionTime: string;
  
  /** 交易狀態 */
  status: TransactionStatus;
  
  /** 交易金額 */
  transactionAmount: number;
  
  /** 交易幣別 */
  transactionCurrency: string;
  
  /** 交易類型 */
  transactionType: 'INWARD_REMITTANCE' | 'CURRENCY_EXCHANGE' | 'FEE_COLLECTION';
  
  /** 交易備註 */
  transactionNotes?: string;
  
  /** 審核資訊 */
  approvalInfo?: ApprovalInfo;
}

/**
 * 審核資訊介面
 */
export interface ApprovalInfo {
  /** 審核狀態 */
  approvalStatus: 'PENDING' | 'APPROVED' | 'REJECTED' | 'ESCALATED';
  
  /** 審核層級 */
  approvalLevel: number;
  
  /** 審核人員 */
  approver?: string;
  
  /** 審核時間 */
  approvalTime?: string;
  
  /** 審核備註 */
  approvalNotes?: string;
  
  /** 下一審核人 */
  nextApprover?: string;
}

/**
 * 通知設定介面
 */
export interface NotificationSettings {
  /** 簡訊通知 */
  smsNotification: boolean;
  
  /** Email通知 */
  emailNotification: boolean;
  
  /** 推播通知 */
  pushNotification: boolean;
  
  /** 通知手機號碼 */
  notificationPhone?: string;
  
  /** 通知Email */
  notificationEmail?: string;
  
  /** 通知語言 */
  notificationLanguage: 'zh-TW' | 'en-US';
}

/**
 * 費率查詢請求介面
 */
export interface RateInquiryRequest {
  /** 來源幣別 */
  fromCurrency: string;
  
  /** 目標幣別 */
  toCurrency: string;
  
  /** 查詢金額 */
  amount?: number;
  
  /** 客戶等級 */
  customerTier?: 'STANDARD' | 'PREMIUM' | 'VIP';
  
  /** 查詢類型 */
  inquiryType: 'INDICATIVE' | 'FIRM' | 'HISTORICAL' | 'REAL_TIME';
  
  /** 會話Token */
  sessionToken?: string;
  
  /** 查詢日期 */
  inquiryDate?: string;
}

/**
 * 費率查詢回應介面
 */
export interface RateInquiryResponse {
  /** 查詢成功 */
  success: boolean;
  
  /** 匯率結果 */
  result?: ExchangeRateInfo;
  
  /** 匯率資訊 */
  rates?: ExchangeRateInfo[];
  
  /** 歷史匯率(如適用) */
  historicalRates?: HistoricalRate[];
  
  /** 匯率走勢 */
  rateTrend?: RateTrend;
  
  /** 查詢時間 */
  inquiryTime?: string;
}

/**
 * 歷史匯率介面
 */
export interface HistoricalRate {
  /** 日期 */
  date: string;
  
  /** 匯率 */
  rate: number;
  
  /** 開盤價 */
  openRate?: number;
  
  /** 最高價 */
  highRate?: number;
  
  /** 最低價 */
  lowRate?: number;
  
  /** 收盤價 */
  closeRate?: number;
  
  /** 交易量 */
  volume?: number;
}

/**
 * 匯率走勢介面
 */
export interface RateTrend {
  /** 走勢方向 */
  direction: 'UP' | 'DOWN' | 'STABLE';
  
  /** 變動幅度 */
  changePercentage: number;
  
  /** 變動點數 */
  changePoints: number;
  
  /** 趨勢期間 */
  trendPeriod: string;
  
  /** 趨勢分析 */
  analysis?: string;
}

/**
 * 手續費試算請求介面
 */
export interface FeeEstimationRequest {
  /** 交易金額 */
  transactionAmount: number;
  
  /** 交易幣別 */
  transactionCurrency: string;
  
  /** 客戶類型 */
  customerType: 'INDIVIDUAL' | 'CORPORATE';
  
  /** 客戶等級 */
  customerTier: 'STANDARD' | 'PREMIUM' | 'VIP';
  
  /** 交易類型 */
  transactionType: string;
  
  /** 優惠代碼 */
  promotionCode?: string;
}

/**
 * 手續費試算回應介面
 */
export interface FeeEstimationResponse {
  /** 試算成功 */
  success: boolean;
  
  /** 手續費明細 */
  feeBreakdown: FeeBreakdown;
  
  /** 可用優惠 */
  availablePromotions: PromotionInfo[];
  
  /** 試算時間 */
  estimationTime: string;
}

/**
 * 金額處理狀態枚舉
 */
export enum AmountProcessingStatus {
  /** 處理中 */
  PROCESSING = 'PROCESSING',
  
  /** 等待審核 */
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  
  /** 已核准 */
  APPROVED = 'APPROVED',
  
  /** 已拒絕 */
  REJECTED = 'REJECTED',
  
  /** 已完成 */
  COMPLETED = 'COMPLETED',
  
  /** 已取消 */
  CANCELLED = 'CANCELLED',
  
  /** 處理失敗 */
  FAILED = 'FAILED'
}

/**
 * 交易狀態枚舉
 */
export enum TransactionStatus {
  /** 已提交 */
  SUBMITTED = 'SUBMITTED',
  
  /** 處理中 */
  PROCESSING = 'PROCESSING',
  
  /** 已清算 */
  SETTLED = 'SETTLED',
  
  /** 已完成 */
  COMPLETED = 'COMPLETED',
  
  /** 已退回 */
  RETURNED = 'RETURNED',
  
  /** 已取消 */
  CANCELLED = 'CANCELLED',
  
  /** 失敗 */
  FAILED = 'FAILED'
}

/**
 * 金額計算工具類
 */
export class AmountCalculationHelper {
  /**
   * 計算台幣金額
   */
  static calculateTWDAmount(originalAmount: number, exchangeRate: number): number {
    return Math.round(originalAmount * exchangeRate * 100) / 100;
  }

  /**
   * 計算實收金額
   */
  static calculateNetAmount(grossAmount: number, totalFees: number): number {
    return Math.max(0, grossAmount - totalFees);
  }

  /**
   * 格式化金額顯示
   */
  static formatAmount(amount: number, currency = 'TWD'): string {
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * 計算匯率變動百分比
   */
  static calculateRateChange(currentRate: number, previousRate: number): number {
    if (previousRate === 0) return 0;
    return ((currentRate - previousRate) / previousRate) * 100;
  }

  /**
   * 驗證金額範圍
   */
  static validateAmountRange(amount: number, minAmount = 0, maxAmount = Infinity): boolean {
    return amount >= minAmount && amount <= maxAmount;
  }

  /**
   * 計算手續費率
   */
  static calculateFeeRate(feeAmount: number, principalAmount: number): number {
    if (principalAmount === 0) return 0;
    return (feeAmount / principalAmount) * 100;
  }

  /**
   * 四捨五入至指定小數位
   */
  static roundToDecimalPlaces(value: number, decimalPlaces = 2): number {
    const factor = Math.pow(10, decimalPlaces);
    return Math.round(value * factor) / factor;
  }

  /**
   * 比較兩個金額是否相等（考慮浮點數誤差）
   */
  static isAmountEqual(amount1: number, amount2: number, tolerance = 0.01): boolean {
    return Math.abs(amount1 - amount2) <= tolerance;
  }

  /**
   * 取得匯率保證期限
   */
  static getRateGuaranteeExpiry(guaranteePeriodMinutes = 30): string {
    const now = new Date();
    now.setMinutes(now.getMinutes() + guaranteePeriodMinutes);
    return now.toISOString();
  }

  /**
   * 檢查匯率是否在有效期內
   */
  static isRateValid(guaranteeExpiry: string): boolean {
    const now = new Date();
    const expiry = new Date(guaranteeExpiry);
    return now <= expiry;
  }

  /**
   * 計算總手續費
   */
  static calculateTotalFees(feeItems: FeeItem[]): number {
    return feeItems.reduce((total, fee) => total + fee.amount, 0);
  }

  /**
   * 取得幣別符號
   */
  static getCurrencySymbol(currency: string): string {
    const symbolMap: Record<string, string> = {
      'TWD': 'NT$',
      'USD': '$',
      'EUR': '€',
      'JPY': '¥',
      'GBP': '£',
      'CNY': '¥',
      'HKD': 'HK$',
      'SGD': 'S$',
      'AUD': 'A$',
      'CAD': 'C$'
    };
    
    return symbolMap[currency] || currency;
  }
}