/**
 * 自然人解款模組 - 身份選擇與驗證相關模型
 * 
 * @description 定義身份資料填寫和驗證所需的資料結構和介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

/**
 * 身份選擇資料介面
 */
export interface IdentitySelectionData {
  /** 中文姓名 */
  chineseName: string;
  
  /** 英文姓名 */
  englishName: string;
  
  /** 確認英文姓名無誤 */
  confirmEnglishName: boolean;
  
  /** 身分證字號 */
  idNumber: string;
  
  /** 出生日期 (YYYY-MM-DD) */
  birthDate: string;
  
  /** 銀行代碼 */
  bankCode: string;
  
  /** 分行代碼 */
  branchCode: string;
  
  /** 銀行帳號 */
  accountNumber: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 電子郵件 (可選) */
  email?: string;
  
  /** 聯絡地址 (可選) */
  address?: string;
}

/**
 * 銀行資訊介面
 */
export interface BankInfo {
  /** 銀行代碼 */
  bankCode: string;
  
  /** 銀行名稱 */
  bankName: string;
  
  /** 銀行英文名稱 */
  bankNameEn?: string;
  
  /** 分行列表 */
  branches: BranchInfo[];
  
  /** 是否支援線上驗證 */
  supportsOnlineVerification: boolean;
  
  /** 銀行Logo URL */
  logoUrl?: string;
}

/**
 * 分行資訊介面
 */
export interface BranchInfo {
  /** 分行代碼 */
  branchCode: string;
  
  /** 分行名稱 */
  branchName: string;
  
  /** 分行地址 */
  address?: string;
  
  /** 分行電話 */
  phone?: string;
  
  /** 是否為主要分行 */
  isPrimary?: boolean;
}

/**
 * 身份驗證請求介面
 */
export interface IdentityVerificationRequest {
  /** 身分證字號 */
  idNumber: string;
  
  /** 中文姓名 */
  chineseName: string;
  
  /** 出生日期 */
  birthDate: string;
  
  /** 手機號碼 */
  mobilePhone: string;
  
  /** 銀行帳號 */
  accountNumber: string;
  
  /** 銀行代碼 */
  bankCode: string;
  
  /** 驗證類型 */
  verificationType: 'BASIC' | 'ENHANCED';
}

/**
 * 身份驗證回應介面
 */
export interface IdentityVerificationResponse {
  /** 驗證是否成功 */
  success: boolean;
  
  /** 回應訊息 */
  message: string;
  
  /** 會話Token */
  sessionToken: string;
  
  /** 驗證結果 */
  result: {
    /** 身份驗證狀態 */
    identityStatus: 'VERIFIED' | 'FAILED' | 'PENDING';
    
    /** 信心分數 (0-100) */
    matchScore: number;
    
    /** 驗證詳細資訊 */
    verificationDetails: {
      /** 姓名比對 */
      nameMatch: boolean;
      /** 身份證號比對 */
      idNumberMatch: boolean;
      /** 電話號碼比對 */
      phoneMatch: boolean;
      /** 地址比對 */
      addressMatch: boolean;
    };
    
    /** 下一步要求 */
    requiredNextStep: 'OTP_VERIFICATION' | 'NONE';
  };
  
  /** 下一步動作 */
  nextAction: 'OTP_VERIFICATION' | 'REMITTANCE_SEARCH';
  
  /** 時間戳記 */
  timestamp: Date;
}

/**
 * 驗證結果介面
 */
export interface VerificationResult {
  /** 身份驗證狀態 */
  identityStatus: IdentityStatus;
  
  /** 帳戶驗證狀態 */
  accountStatus: AccountStatus;
  
  /** 驗證分數 (0-100) */
  verificationScore: number;
  
  /** 風險等級 */
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  
  /** 驗證詳細資訊 */
  details: VerificationDetail[];
}

/**
 * 驗證詳細資訊介面
 */
export interface VerificationDetail {
  /** 驗證項目 */
  item: string;
  
  /** 驗證狀態 */
  status: 'PASS' | 'FAIL' | 'WARNING';
  
  /** 驗證訊息 */
  message: string;
  
  /** 信心度 (0-1) */
  confidence?: number;
}

/**
 * 搜尋條件介面
 */
export interface SearchCriteria {
  /** 身分證字號 */
  idNumber: string;
  
  /** 姓名 */
  name: string;
  
  /** 搜尋開始日期 */
  dateFrom?: string;
  
  /** 搜尋結束日期 */
  dateTo?: string;
  
  /** 幣別篩選 */
  currency?: string;
  
  /** 最小金額 */
  minAmount?: number;
  
  /** 最大金額 */
  maxAmount?: number;
}

/**
 * 表單驗證錯誤介面
 */
export interface FormValidationError {
  /** 欄位名稱 */
  fieldName: string;
  
  /** 錯誤類型 */
  errorType: 'REQUIRED' | 'FORMAT' | 'LENGTH' | 'INVALID';
  
  /** 錯誤訊息 */
  message: string;
  
  /** 建議修正方式 */
  suggestion?: string;
}

/**
 * 身份狀態枚舉
 */
export enum IdentityStatus {
  /** 未驗證 */
  NOT_VERIFIED = 'NOT_VERIFIED',
  
  /** 驗證中 */
  VERIFYING = 'VERIFYING',
  
  /** 驗證通過 */
  VERIFIED = 'VERIFIED',
  
  /** 驗證失敗 */
  FAILED = 'FAILED',
  
  /** 需要額外驗證 */
  REQUIRES_ADDITIONAL = 'REQUIRES_ADDITIONAL'
}

/**
 * 帳戶狀態枚舉
 */
export enum AccountStatus {
  /** 帳戶正常 */
  ACTIVE = 'ACTIVE',
  
  /** 帳戶凍結 */
  FROZEN = 'FROZEN',
  
  /** 帳戶關閉 */
  CLOSED = 'CLOSED',
  
  /** 帳戶不存在 */
  NOT_FOUND = 'NOT_FOUND',
  
  /** 帳戶限制 */
  RESTRICTED = 'RESTRICTED'
}

/**
 * 台灣身分證驗證工具類
 */
export class TaiwanIdValidator {
  /**
   * 驗證台灣身分證號格式和檢查碼
   */
  static validate(idNumber: string): boolean {
    if (!idNumber || idNumber.length !== 10) {
      return false;
    }

    const pattern = /^[A-Z][12]\d{8}$/;
    if (!pattern.test(idNumber)) {
      return false;
    }

    // 字母對應數字映射
    const letterMap: Record<string, number> = {
      'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16,
      'H': 17, 'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
      'O': 35, 'P': 23, 'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28,
      'V': 29, 'W': 32, 'X': 30, 'Y': 31, 'Z': 33
    };

    const firstLetter = idNumber.charAt(0);
    const letterValue = letterMap[firstLetter];
    
    if (!letterValue) {
      return false;
    }

    // 計算檢查碼
    let sum = Math.floor(letterValue / 10) + (letterValue % 10) * 9;
    
    for (let i = 1; i < 9; i++) {
      sum += parseInt(idNumber.charAt(i)) * (9 - i);
    }
    
    sum += parseInt(idNumber.charAt(9));
    
    return sum % 10 === 0;
  }

  /**
   * 取得身分證號對應的發證地區
   */
  static getIssueLocation(idNumber: string): string | null {
    if (!idNumber || idNumber.length === 0) {
      return null;
    }

    const locationMap: Record<string, string> = {
      'A': '台北市', 'B': '台中市', 'C': '基隆市', 'D': '台南市', 'E': '高雄市',
      'F': '新北市', 'G': '宜蘭縣', 'H': '桃園市', 'I': '嘉義市', 'J': '新竹縣',
      'K': '苗栗縣', 'L': '台中縣', 'M': '南投縣', 'N': '彰化縣', 'O': '新竹市',
      'P': '雲林縣', 'Q': '嘉義縣', 'R': '台南縣', 'S': '高雄縣', 'T': '屏東縣',
      'U': '花蓮縣', 'V': '台東縣', 'W': '金門縣', 'X': '澎湖縣', 'Y': '陽明山',
      'Z': '連江縣'
    };

    return locationMap[idNumber.charAt(0)] || null;
  }
}