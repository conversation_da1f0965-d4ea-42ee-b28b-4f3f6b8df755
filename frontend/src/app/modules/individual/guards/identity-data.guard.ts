/**
 * 自然人解款模組 - 身份資料守衛
 * 
 * @description 檢查用戶是否已完成身份資料填寫，未完成則導向身份資料頁面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { IndividualService, IndividualFlowStep } from '../services/individual.service';

/**
 * 身份資料完成路由守衛
 */
@Injectable({
  providedIn: 'root'
})
export class IdentityDataGuard implements CanActivate, CanActivateChild {

  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟動路由
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkIdentityDataCompletion(state.url);
  }

  /**
   * 檢查是否可以啟動子路由
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkIdentityDataCompletion(state.url);
  }

  /**
   * 檢查身份資料完成狀態
   */
  private checkIdentityDataCompletion(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 如果正在前往條款頁面或身份資料頁面，允許通過
        if (url.includes('/landing') || 
            url.includes('/terms') || 
            url.includes('/identity-selection') ||
            url.includes('/identity-data')) {
          return true;
        }

        // 檢查前置條件：必須先同意條款
        if (!state.termsAgreed) {
          console.warn('Terms not agreed, redirecting to landing page');
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查是否已完成身份資料填寫
        if (state.identityDataCompleted) {
          return true;
        }

        // 未完成身份資料填寫，導向身份資料頁面
        console.warn('Identity data not completed, redirecting to identity selection page');
        this.router.navigate(['/ibr/individual/identity-selection'], {
          queryParams: { returnUrl: url }
        });
        
        return false;
      })
    );
  }
}