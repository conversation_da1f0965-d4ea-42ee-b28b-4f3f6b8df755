# Individual Guards - 自然人解款路由守衛

![Guards](https://img.shields.io/badge/Guards-4-blue) ![Route Protection](https://img.shields.io/badge/Route-Protection-green) ![Flow Control](https://img.shields.io/badge/Flow-Control-orange) ![State Management](https://img.shields.io/badge/State-Management-red)

## 🎯 守衛概述

Individual Guards 包含自然人解款模組的所有路由守衛，確保用戶按照正確的流程步驟進行解款申請。這些守衛提供路由層級的權限控制、流程驗證和狀態檢查，防止用戶跳過必要步驟或在未完成前置條件時訪問後續頁面。

## 📊 守衛統計

- **總守衛數**: 4個核心路由守衛
- **保護層級**: 路由級別權限控制
- **流程驗證**: 14步驟完整流程保護
- **狀態追蹤**: 即時狀態檢查與導向

## 🗂️ 守衛架構

```
guards/
├── terms-agreed.guard.ts        # 條款同意守衛
├── identity-data.guard.ts       # 身份資料完成守衛
├── identity-verified.guard.ts   # 身份驗證完成守衛
├── remittance-confirmed.guard.ts# 匯款確認守衛
└── index.ts                     # 統一匯出檔案
```

## 🛡️ 核心守衛詳解

### 1. [Terms Agreed Guard](terms-agreed.guard.ts) - 條款同意守衛

**功能**: 檢查用戶是否已同意使用條款

**保護範圍**: 除了落地頁和條款頁面外的所有頁面

**守衛邏輯**:
```typescript
@Injectable({
  providedIn: 'root'
})
export class TermsAgreedGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkTermsAgreement(state.url);
  }

  private checkTermsAgreement(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許訪問的頁面 (不需要條款同意)
        const allowedPaths = ['/landing', '/terms'];
        
        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查是否已同意條款
        if (!state.termsAgreed) {
          console.warn('Terms not agreed, redirecting to landing page');
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        return true;
      })
    );
  }
}
```

**使用範例**:
```typescript
// 在路由配置中使用
const routes: Routes = [
  {
    path: 'identity-selection',
    component: IdentitySelectionComponent,
    canActivate: [TermsAgreedGuard]
  }
];
```

### 2. [Identity Data Guard](identity-data.guard.ts) - 身份資料完成守衛

**功能**: 檢查用戶是否已完成身份資料填寫

**保護範圍**: 身份資料填寫後的所有頁面

**守衛邏輯**:
```typescript
@Injectable({
  providedIn: 'root'
})
export class IdentityDataGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  private checkIdentityDataCompletion(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許訪問的頁面 (不需要身份資料完成)
        const allowedPaths = [
          '/landing',
          '/terms', 
          '/identity-selection',
          '/identity-data'
        ];
        
        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查前置條件：必須先同意條款
        if (!state.termsAgreed) {
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查身份資料是否完成
        if (!state.identityDataCompleted) {
          console.warn('Identity data not completed, redirecting to identity selection');
          this.router.navigate(['/ibr/individual/identity-selection'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        return true;
      })
    );
  }
}
```

**驗證項目**:
- 條款是否已同意
- 身份資料是否已填寫完成
- 必要欄位是否都已填寫

### 3. [Identity Verified Guard](identity-verified.guard.ts) - 身份驗證完成守衛

**功能**: 檢查用戶是否已完成身份驗證 (FIDO + OTP)

**保護範圍**: 身份驗證後的所有頁面

**守衛邏輯**:
```typescript
@Injectable({
  providedIn: 'root'
})
export class IdentityVerifiedGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  private checkIdentityVerification(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許通過的頁面
        const allowedPaths = [
          '/landing',
          '/terms',
          '/identity-selection', 
          '/identity-data',
          '/identity-verification',
          '/otp-verification'
        ];

        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查前置條件層層驗證
        if (!state.termsAgreed) {
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        if (!state.identityDataCompleted) {
          this.router.navigate(['/ibr/individual/identity-selection'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查身份驗證狀態
        if (state.identityVerified) {
          return true;
        }

        // 根據當前狀態決定導向哪個驗證頁面
        if (!state.otpVerified && !state.identityVerified) {
          if (state.currentStep === IndividualFlowStep.OTP_VERIFICATION) {
            this.router.navigate(['/ibr/individual/otp-verification'], {
              queryParams: { returnUrl: url }
            });
          } else {
            this.router.navigate(['/ibr/individual/identity-verification'], {
              queryParams: { returnUrl: url }
            });
          }
          return false;
        }

        return true;
      })
    );
  }
}
```

**驗證項目**:
- 條款同意狀態
- 身份資料完成狀態
- FIDO生物辨識驗證狀態
- OTP簡訊驗證狀態
- 整體身份驗證完成狀態

### 4. [Remittance Confirmed Guard](remittance-confirmed.guard.ts) - 匯款確認守衛

**功能**: 檢查用戶是否已完成匯款選擇和確認

**保護範圍**: 最終確認和提交相關頁面

**守衛邏輯**:
```typescript
@Injectable({
  providedIn: 'root'
})
export class RemittanceConfirmedGuard implements CanActivate, CanActivateChild {
  
  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  private checkRemittanceConfirmation(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許訪問的頁面 (不需要匯款確認)
        const allowedPaths = [
          '/landing',
          '/terms',
          '/identity-selection',
          '/identity-data', 
          '/identity-verification',
          '/otp-verification',
          '/remittance-search',
          '/remittance-detail',
          '/amount-confirmation'
        ];

        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查完整的前置條件鏈
        if (!state.termsAgreed) {
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        if (!state.identityDataCompleted) {
          this.router.navigate(['/ibr/individual/identity-selection'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        if (!state.identityVerified) {
          this.router.navigate(['/ibr/individual/identity-verification'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查匯款確認狀態
        if (!state.remittanceConfirmed) {
          console.warn('Remittance not confirmed, redirecting to search page');
          this.router.navigate(['/ibr/individual/remittance-search'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        return true;
      })
    );
  }
}
```

**驗證項目**:
- 所有前置條件 (條款、身份資料、身份驗證)
- 匯款搜尋是否已完成
- 匯款選擇是否已確認
- 金額計算是否已完成

## 🔄 守衛協作與流程控制

### 守衛依賴關係
```mermaid
graph TD
    A[TermsAgreedGuard] --> B[IdentityDataGuard]
    B --> C[IdentityVerifiedGuard] 
    C --> D[RemittanceConfirmedGuard]
    
    E[Landing Page] --> A
    F[Identity Selection] --> B
    G[Identity Verification] --> C
    H[Final Confirmation] --> D
    
    I[Route Protection] --> A
    I --> B
    I --> C
    I --> D
```

### 完整流程保護
```typescript
// 完整的路由配置範例
const individualRoutes: Routes = [
  {
    path: '',
    component: IndividualLayoutComponent,
    children: [
      // 第1步：落地頁 (無守衛)
      {
        path: 'landing',
        component: LandingComponent
      },
      
      // 第2步：條款同意 (無守衛)
      {
        path: 'terms',
        component: TermsComponent
      },
      
      // 第3步：身份選擇 (需要條款同意)
      {
        path: 'identity-selection',
        component: IdentitySelectionComponent,
        canActivate: [TermsAgreedGuard]
      },
      
      // 第4步：身份資料 (需要條款同意)
      {
        path: 'identity-data',
        component: IdentityDataComponent,
        canActivate: [TermsAgreedGuard]
      },
      
      // 第5步：身份驗證 (需要身份資料完成)
      {
        path: 'identity-verification',
        component: IdentityVerificationComponent,
        canActivate: [IdentityDataGuard]
      },
      
      // 第6步：OTP驗證 (需要身份資料完成)
      {
        path: 'otp-verification',
        component: OtpVerificationComponent,
        canActivate: [IdentityDataGuard]
      },
      
      // 第7步：匯款搜尋 (需要身份驗證完成)
      {
        path: 'remittance-search',
        component: RemittanceSearchComponent,
        canActivate: [IdentityVerifiedGuard]
      },
      
      // 第8步：匯款詳情 (需要身份驗證完成)
      {
        path: 'remittance-detail',
        component: RemittanceDetailComponent,
        canActivate: [IdentityVerifiedGuard]
      },
      
      // 第9步：金額確認 (需要身份驗證完成)
      {
        path: 'amount-confirmation',
        component: AmountConfirmationComponent,
        canActivate: [IdentityVerifiedGuard]
      },
      
      // 第10步：最終確認 (需要匯款確認完成)
      {
        path: 'final-confirmation',
        component: FinalConfirmationComponent,
        canActivate: [RemittanceConfirmedGuard]
      },
      
      // 第11步：提交完成 (需要匯款確認完成)
      {
        path: 'completion',
        component: CompletionComponent,
        canActivate: [RemittanceConfirmedGuard]
      }
    ]
  }
];
```

## 🛡️ 狀態檢查邏輯

### 應用程式狀態介面
```typescript
interface IndividualApplicationState {
  // 條款相關
  termsAgreed: boolean;                  // 是否已同意條款
  termsVersion: string;                  // 條款版本
  termsAgreedAt: Date;                   // 同意時間
  
  // 身份資料相關
  identityDataCompleted: boolean;        // 身份資料是否完成
  identityData: IdentitySelectionData;   // 身份資料內容
  
  // 身份驗證相關
  fidoRegistered: boolean;               // FIDO是否已註冊
  fidoVerified: boolean;                 // FIDO是否已驗證
  otpSent: boolean;                      // OTP是否已發送
  otpVerified: boolean;                  // OTP是否已驗證
  identityVerified: boolean;             // 整體身份驗證是否完成
  
  // 匯款相關
  remittanceSelected: boolean;           // 匯款是否已選擇
  remittanceConfirmed: boolean;          // 匯款是否已確認
  amountCalculated: boolean;             // 金額是否已計算
  
  // 流程相關
  currentStep: IndividualFlowStep;       // 當前步驟
  completedSteps: IndividualFlowStep[];  // 已完成步驟
  
  // 時間戳記
  lastUpdated: Date;                     // 最後更新時間
  sessionStarted: Date;                  // 會話開始時間
}
```

### 守衛決策樹
```typescript
export class GuardDecisionEngine {
  static determineRedirectPath(
    targetUrl: string, 
    currentState: IndividualApplicationState
  ): string | null {
    
    // 1. 檢查條款同意
    if (!currentState.termsAgreed) {
      return '/ibr/individual/landing';
    }
    
    // 2. 檢查身份資料完成
    if (!currentState.identityDataCompleted) {
      return '/ibr/individual/identity-selection';
    }
    
    // 3. 檢查身份驗證完成
    if (!currentState.identityVerified) {
      // 根據當前驗證狀態決定導向
      if (!currentState.fidoRegistered) {
        return '/ibr/individual/identity-verification';
      }
      if (!currentState.otpVerified) {
        return '/ibr/individual/otp-verification';
      }
      return '/ibr/individual/identity-verification';
    }
    
    // 4. 檢查匯款確認完成 (僅限最終確認相關頁面)
    const finalConfirmationPaths = [
      '/final-confirmation',
      '/completion',
      '/transaction-confirmation'
    ];
    
    if (finalConfirmationPaths.some(path => targetUrl.includes(path))) {
      if (!currentState.remittanceConfirmed) {
        return '/ibr/individual/remittance-search';
      }
    }
    
    // 所有檢查通過，允許訪問
    return null;
  }
}
```

## 🔧 守衛工具與輔助函數

### URL匹配工具
```typescript
export class UrlMatcher {
  static isAllowedPath(currentUrl: string, allowedPaths: string[]): boolean {
    return allowedPaths.some(path => currentUrl.includes(path));
  }
  
  static extractReturnUrl(route: ActivatedRouteSnapshot): string | null {
    return route.queryParams['returnUrl'] || null;
  }
  
  static buildRedirectOptions(returnUrl: string) {
    return {
      queryParams: { returnUrl },
      queryParamsHandling: 'merge'
    };
  }
}
```

### 狀態驗證工具
```typescript
export class StateValidator {
  static validateMinimumRequirements(
    state: IndividualApplicationState,
    requiredLevel: 'TERMS' | 'IDENTITY_DATA' | 'IDENTITY_VERIFIED' | 'REMITTANCE_CONFIRMED'
  ): boolean {
    
    switch (requiredLevel) {
      case 'TERMS':
        return state.termsAgreed;
        
      case 'IDENTITY_DATA':
        return state.termsAgreed && state.identityDataCompleted;
        
      case 'IDENTITY_VERIFIED':
        return state.termsAgreed && 
               state.identityDataCompleted && 
               state.identityVerified;
               
      case 'REMITTANCE_CONFIRMED':
        return state.termsAgreed && 
               state.identityDataCompleted && 
               state.identityVerified && 
               state.remittanceConfirmed;
               
      default:
        return false;
    }
  }
}
```

## 🧪 測試策略

### 守衛單元測試
```typescript
describe('IdentityVerifiedGuard', () => {
  let guard: IdentityVerifiedGuard;
  let mockIndividualService: jasmine.SpyObj<IndividualService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(() => {
    const individualServiceSpy = jasmine.createSpyObj('IndividualService', [], {
      applicationState$: of({
        termsAgreed: true,
        identityDataCompleted: true,
        identityVerified: false
      })
    });

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        IdentityVerifiedGuard,
        { provide: IndividualService, useValue: individualServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    });

    guard = TestBed.inject(IdentityVerifiedGuard);
    mockIndividualService = TestBed.inject(IndividualService) as jasmine.SpyObj<IndividualService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should allow access to allowed paths', (done) => {
    const route = new ActivatedRouteSnapshot();
    const state = { url: '/ibr/individual/identity-verification' } as RouterStateSnapshot;

    guard.canActivate(route, state).subscribe(result => {
      expect(result).toBeTruthy();
      done();
    });
  });

  it('should redirect when identity not verified', (done) => {
    const route = new ActivatedRouteSnapshot();
    const state = { url: '/ibr/individual/remittance-search' } as RouterStateSnapshot;

    guard.canActivate(route, state).subscribe(result => {
      expect(result).toBeFalsy();
      expect(mockRouter.navigate).toHaveBeenCalledWith(
        ['/ibr/individual/identity-verification'],
        { queryParams: { returnUrl: '/ibr/individual/remittance-search' } }
      );
      done();
    });
  });
});
```

### 整合測試
```typescript
describe('Individual Guards Integration', () => {
  it('should enforce complete flow progression', async () => {
    // 測試完整的流程保護
    const testSequence = [
      { path: '/identity-selection', shouldAllow: false, redirectTo: '/landing' },
      { path: '/identity-verification', shouldAllow: false, redirectTo: '/identity-selection' },
      { path: '/remittance-search', shouldAllow: false, redirectTo: '/identity-verification' },
      { path: '/final-confirmation', shouldAllow: false, redirectTo: '/remittance-search' }
    ];

    for (const test of testSequence) {
      const result = await TestBed.runInInjectionContext(() => 
        guard.canActivate(createRoute(test.path))
      );
      
      expect(result).toBe(test.shouldAllow);
      if (!test.shouldAllow) {
        expect(mockRouter.navigate).toHaveBeenCalledWith([test.redirectTo]);
      }
    }
  });
});
```

## 📋 最佳實踐

### 守衛設計原則
1. **漸進式驗證**: 每個守衛只檢查當前步驟需要的最小條件
2. **明確的錯誤處理**: 提供清楚的重定向路徑和原因
3. **狀態一致性**: 確保守衛檢查與服務狀態同步
4. **效能考量**: 使用Observable避免重複的狀態查詢

### 守衛配置建議
```typescript
// 推薦的守衛配置模式
const routeConfig = {
  path: 'protected-route',
  component: ProtectedComponent,
  canActivate: [
    // 按照依賴順序排列守衛
    TermsAgreedGuard,           // 最基本的條件
    IdentityDataGuard,          // 中等條件 
    IdentityVerifiedGuard       // 高級條件
  ],
  data: {
    expectedStep: IndividualFlowStep.REMITTANCE_SEARCH,
    title: '匯款搜尋'
  }
};
```

## 🔗 相關連結

### 模組文檔
- [Individual Module README](../README.md) - 自然人模組總覽
- [Individual Components](../components/README.md) - 自然人元件文檔
- [Individual Services](../services/README.md) - 自然人服務文檔

### 相關守衛
- [Corporate Guards](../../corporate/guards/README.md) - 法人模組路由守衛
- [Supplement Guards](../../supplement/guards/README.md) - 補件模組路由守衛

### Angular文檔
- [Angular Route Guards](https://angular.io/guide/router#guards) - Angular官方守衛文檔
- [Angular Router](https://angular.io/guide/router) - Angular路由指南

---

**🎯 守衛完成度**: 4/4 完成 | **🛡️ 保護層級**: 路由級別 | **🔄 流程控制**: 完整 | **📊 狀態檢查**: 即時

*Individual Guards 提供完整的路由保護機制，確保用戶按照正確的14步流程進行自然人解款申請，防止跳過必要步驟或在未完成前置條件時訪問後續頁面。*