/**
 * 自然人解款模組 - 身份驗證守衛
 * 
 * @description 檢查用戶是否已完成身份驗證，未完成則導向相應驗證頁面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { IndividualService, IndividualFlowStep } from '../services/individual.service';

/**
 * 身份驗證完成路由守衛
 */
@Injectable({
  providedIn: 'root'
})
export class IdentityVerifiedGuard implements CanActivate, CanActivateChild {

  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟動路由
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkIdentityVerification(state.url);
  }

  /**
   * 檢查是否可以啟動子路由
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkIdentityVerification(state.url);
  }

  /**
   * 檢查身份驗證完成狀態
   */
  private checkIdentityVerification(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許通過的頁面
        const allowedPaths = [
          '/landing',
          '/terms',
          '/identity-selection',
          '/identity-data',
          '/identity-verification',
          '/otp-verification'
        ];

        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查前置條件：必須先同意條款
        if (!state.termsAgreed) {
          console.warn('Terms not agreed, redirecting to landing page');
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查前置條件：必須已完成身份資料填寫
        if (!state.identityDataCompleted) {
          console.warn('Identity data not completed, redirecting to identity selection page');
          this.router.navigate(['/ibr/individual/identity-selection'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查身份驗證狀態
        if (state.identityVerified) {
          return true;
        }

        // 如果需要OTP驗證但尚未完成
        if (!state.otpVerified && !state.identityVerified) {
          console.warn('Identity not verified, redirecting to verification page');
          
          // 根據當前狀態決定導向哪個驗證頁面
          if (state.currentStep === IndividualFlowStep.OTP_VERIFICATION) {
            this.router.navigate(['/ibr/individual/otp-verification'], {
              queryParams: { returnUrl: url }
            });
          } else {
            this.router.navigate(['/ibr/individual/identity-verification'], {
              queryParams: { returnUrl: url }
            });
          }
          
          return false;
        }

        return true;
      })
    );
  }
}