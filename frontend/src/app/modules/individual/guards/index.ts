/**
 * 自然人解款模組 - 路由守衛統一導出
 * 
 * @description 提供所有Individual模組路由守衛的統一導出介面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

export { TermsAgreedGuard } from './terms-agreed.guard';
export { IdentityDataGuard } from './identity-data.guard';
export { IdentityVerifiedGuard } from './identity-verified.guard';
export { RemittanceConfirmedGuard } from './remittance-confirmed.guard';

/**
 * 所有Individual模組守衛的陣列
 */
export const INDIVIDUAL_GUARDS = [
  TermsAgreedGuard,
  IdentityDataGuard,
  IdentityVerifiedGuard,
  RemittanceConfirmedGuard
];

/**
 * 守衛應用順序說明
 * 
 * 1. TermsAgreedGuard - 檢查條款同意狀態
 * 2. IdentityDataGuard - 檢查身份資料完成狀態
 * 3. IdentityVerifiedGuard - 檢查身份驗證完成狀態
 * 4. RemittanceConfirmedGuard - 檢查匯款確認完成狀態
 * 
 * 使用方式：
 * ```typescript
 * {
 *   path: 'amount-calculation',
 *   component: AmountCalculationComponent,
 *   canActivate: [
 *     TermsAgreedGuard,
 *     IdentityDataGuard,
 *     IdentityVerifiedGuard,
 *     RemittanceConfirmedGuard
 *   ]
 * }
 * ```
 */