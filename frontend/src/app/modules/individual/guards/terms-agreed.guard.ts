/**
 * 自然人解款模組 - 條款同意守衛
 * 
 * @description 檢查用戶是否已同意條款，未同意則導向條款頁面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';

import { IndividualService, IndividualFlowStep } from '../services/individual.service';

/**
 * 條款同意路由守衛
 */
@Injectable({
  providedIn: 'root'
})
export class TermsAgreedGuard implements CanActivate, CanActivateChild {

  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟動路由
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkTermsAgreement(state.url);
  }

  /**
   * 檢查是否可以啟動子路由
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkTermsAgreement(state.url);
  }

  /**
   * 檢查條款同意狀態
   */
  private checkTermsAgreement(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 如果正在前往條款頁面，允許通過
        if (url.includes('/landing') || url.includes('/terms')) {
          return true;
        }

        // 檢查是否已同意條款
        if (state.termsAgreed) {
          return true;
        }

        // 未同意條款，導向條款頁面
        console.warn('Terms not agreed, redirecting to landing page');
        this.router.navigate(['/ibr/individual/landing'], {
          queryParams: { returnUrl: url }
        });
        
        return false;
      })
    );
  }
}