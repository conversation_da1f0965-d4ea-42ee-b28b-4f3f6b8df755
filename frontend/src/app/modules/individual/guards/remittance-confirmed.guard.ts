/**
 * 自然人解款模組 - 匯款確認守衛
 * 
 * @description 檢查用戶是否已完成匯款確認，未完成則導向匯款相關頁面
 * <AUTHOR> Code
 * @date 2025/06/01
 */

import { Injectable } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { IndividualService, IndividualFlowStep } from '../services/individual.service';

/**
 * 匯款確認完成路由守衛
 */
@Injectable({
  providedIn: 'root'
})
export class RemittanceConfirmedGuard implements CanActivate, CanActivateChild {

  constructor(
    private individualService: IndividualService,
    private router: Router
  ) {}

  /**
   * 檢查是否可以啟動路由
   */
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkRemittanceConfirmation(state.url);
  }

  /**
   * 檢查是否可以啟動子路由
   */
  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    return this.checkRemittanceConfirmation(state.url);
  }

  /**
   * 檢查匯款確認完成狀態
   */
  private checkRemittanceConfirmation(url: string): Observable<boolean> {
    return this.individualService.applicationState$.pipe(
      map(state => {
        // 允許通過的頁面（前面的流程頁面 + 匯款相關頁面）
        const allowedPaths = [
          '/landing',
          '/identity-selection',
          '/identity-verification',
          '/remittance-detail',
          '/amount-confirmation',
          '/transaction-confirmation'
        ];

        if (allowedPaths.some(path => url.includes(path))) {
          return true;
        }

        // 檢查前置條件：必須先同意條款
        if (!state.termsAgreed) {
          console.warn('Terms not agreed, redirecting to landing page');
          this.router.navigate(['/ibr/individual/landing'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查前置條件：必須已完成身份資料填寫
        if (!state.identityDataCompleted) {
          console.warn('Identity data not completed, redirecting to identity selection page');
          this.router.navigate(['/ibr/individual/identity-selection'], {
            queryParams: { returnUrl: url }
          });
          return false;
        }

        // 檢查前置條件：必須已完成身份驗證
        if (!state.identityVerified && !state.otpVerified) {
          console.warn('Identity not verified, redirecting to verification page');
          
          if (state.currentStep === IndividualFlowStep.OTP_VERIFICATION) {
            this.router.navigate(['/ibr/individual/otp-verification'], {
              queryParams: { returnUrl: url }
            });
          } else {
            this.router.navigate(['/ibr/individual/identity-verification'], {
              queryParams: { returnUrl: url }
            });
          }
          
          return false;
        }

        // 檢查匯款確認狀態
        if (state.remittanceConfirmed) {
          return true;
        }

        // 未完成匯款確認，導向匯款詳情頁面
        console.warn('Remittance not confirmed, redirecting to remittance detail page');
        this.router.navigate(['/ibr/individual/remittance-detail'], {
          queryParams: { returnUrl: url }
        });
        
        return false;
      })
    );
  }
}