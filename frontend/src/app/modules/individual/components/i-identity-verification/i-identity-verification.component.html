<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container">
      
      <!-- 內容區塊 -->
      <div class="content-section">
        <!-- 標題區 -->
        <div class="title-section">
          <h1 class="main-title">線上外匯解款</h1>
          <p class="subtitle">Online Foreign Exchange Remittance</p>
        </div>

        <!-- 步驟指示器 -->
        <div class="step-indicator">
          <div class="step-info">
            <div class="step-current">第 1 步 驗證身份</div>
            <div class="step-total">共 3 步</div>
          </div>
          <div class="step-subtitle">
            <div class="step-current-en">Step 1 Verify Identity</div>
            <div class="step-total-en">3 Steps</div>
          </div>
          <div class="step-progress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="33.33"></div>
            </div>
          </div>
        </div>

        <!-- 完整 OTP 元件 -->
        <app-g-otp
          [error]="hasError"
          [errorMessage]="errorMessage"
          [disabled]="isLoading"
          [sentTime]="sentTime"
          [resendCountdown]="resendCountdown"
          [maskedPhoneNumber]="maskedPhoneNumber"
          [showPrefix]="true"
          (otpComplete)="onOtpComplete($event)"
          (otpChange)="onOtpChange($event)"
          (resend)="resendOtp()"
        ></app-g-otp>

        <!-- 操作按鈕 -->
        <div class="action-section">
          <button 
            class="btn-primary btn-next" 
            [disabled]="!canProceed || isLoading"
            (click)="proceedToNext()"
          >
            <div *ngIf="isLoading" class="loading-spinner"></div>
            <div *ngIf="!isLoading">
              <span class="button-text-zh">{{ isVerified ? '下一步' : '驗證中...' }}</span>
              <span class="button-text-en">{{ isVerified ? 'Next' : 'Verifying...' }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- 測試資訊 (開發時使用) -->
      <div class="test-info" *ngIf="isDevelopment">
        <h4>🧪 Individual Module - 第1頁 (身份驗證)</h4>
        <p>頁面: 身份驗證</p>
        <p>狀態: {{ currentStatus }}</p>
        <p>驗證碼: {{ verificationCode }}</p>
        <p>倒數: {{ countdown }}秒</p>
      </div>
      
    </div>
  </main>
</div>
