# 身份驗證組件 (Identity Verification Component)

## 概述
這是線上外匯解款系統中的身份驗證頁面，對應自然人解款流程的第1步。

## 功能特色

### 🎨 UI 設計
- **標題區域**: 顯示「線上外匯解款」中英文標題
- **步驟指示器**: 顯示「第1步 驗證身份」和進度條 (33.33%)
- **文件圖示**: SVG 繪製的文件與對話框圖示
- **驗證碼顯示**: 顯示部分遮罩的驗證碼 (如: XYZ-)
- **狀態訊息**: 顯示發送時間和有效期限
- **重新發送**: 帶倒數計時的重新發送按鈕
- **發送目標**: 顯示遮罩手機號碼

### 🔧 技術功能
- **自動倒數**: 5分鐘 (300秒) 倒數計時
- **時間格式化**: mm:ss 格式顯示
- **自動驗證**: 模擬在18秒後自動通過驗證
- **響應式設計**: 支援手機、平板、桌機版本

### 📱 響應式設計
- **手機版**: 單欄布局，移除圓角和陰影
- **平板版**: 中等寬度容器 (500px)
- **桌機版**: 2/3 寬度布局 (600-900px)

## 使用方式

### 路由配置
```typescript
{
  path: 'identity-verification',
  component: IIdentityVerificationComponent,
  data: { 
    title: '身份驗證',
    step: 1,
    totalSteps: 3,
    description: '手機驗證碼與身份確認流程'
  }
}
```

### 導航流程
1. **前一頁**: `/ibr/individual/landing` (條款同意)
2. **當前頁**: `/ibr/individual/identity-verification` (身份驗證)
3. **下一頁**: `/ibr/individual/remittance-detail` (匯款詳情)

## 組件屬性

### 主要屬性
- `verificationCode`: 完整驗證碼 (XYZ-123456)
- `displayCode`: 顯示的部分驗證碼 (XYZ-)
- `countdown`: 倒數計時秒數
- `maskedPhoneNumber`: 遮罩手機號碼 (0953***416)
- `sentTime`: 發送時間 (HH:mm 格式)

### 方法
- `startCountdown()`: 開始5分鐘倒數
- `formatTime(seconds)`: 格式化時間為 mm:ss
- `resendOtp()`: 重新發送驗證碼
- `proceedToNext()`: 前往下一步

## 樣式特色

### 色彩配置
- **主色**: #0044ad (凱基藍)
- **背景**: #f8f9fa (淺灰)
- **文字**: #041c43 (深藍)
- **次要文字**: #666666 (中灰)

### 字體配置
- **中文**: "Noto Sans TC"
- **英文**: "Montserrat"
- **驗證碼**: "Montserrat" (等寬效果)

## 開發注意事項

### 測試功能
- 開發模式下會顯示測試資訊
- 18秒後自動啟用「下一步」按鈕
- 重新發送會隨機更換驗證碼前綴

### 實際整合
- 需要整合簡訊發送服務
- 需要實際的驗證碼驗證邏輯
- 需要整合客服系統

## 更新記錄
- 2024-05-31: 根據 UI 設計圖調整為新的身份驗證介面
- 移除 OTP 輸入框，改為顯示驗證碼
- 新增步驟指示器和進度條
- 優化響應式設計 