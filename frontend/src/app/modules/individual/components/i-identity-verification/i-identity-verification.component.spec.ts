// /**
//  * 自然人解款模組 - 身份驗證元件單元測試
//  *
//  * @description 測試身份驗證元件的功能，包括OTP驗證、表單驗證、狀態管理等
//  * <AUTHOR> Code
//  * @date 2025/06/01
//  */
//
// import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
// import { Router } from '@angular/router';
// import { CommonModule } from '@angular/common';
// import { FormsModule } from '@angular/forms';
// import { IIdentityVerificationComponent } from './i-identity-verification.component';
// import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
// import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
//
// describe('IIdentityVerificationComponent', () => {
//   let component: IIdentityVerificationComponent;
//   let fixture: ComponentFixture<IIdentityVerificationComponent>;
//   let mockRouter: jasmine.SpyObj<Router>;
//   let mockStateService: jasmine.SpyObj<IbrStateService>;
//
//   beforeEach(async () => {
//     // 建立模擬對象
//     mockRouter = jasmine.createSpyObj('Router', ['navigate']);
//     mockStateService = jasmine.createSpyObj('IbrStateService', [
//       'getCurrentState',
//       'getApplicationData',
//       'updateApplicationStatus'
//     ]);
//
//     // 設定模擬回傳值
//     mockStateService.getCurrentState.and.returnValue({
//       userType: 'individual',
//       status: ApplicationStatus.IDENTITY_VERIFYING,
//       stepTitle: '身份驗證',
//       currentStep: 1,
//       totalSteps: 3,
//       progress: 33
//     });
//
//     mockStateService.getApplicationData.and.returnValue({
//       verificationMethod: 'otp'
//     });
//
//     await TestBed.configureTestingModule({
//       imports: [
//         CommonModule,
//         FormsModule,
//         IbrSharedModule,
//         IIdentityVerificationComponent
//       ],
//       providers: [
//         { provide: Router, useValue: mockRouter },
//         { provide: IbrStateService, useValue: mockStateService }
//       ]
//     }).compileComponents();
//
//     fixture = TestBed.createComponent(IIdentityVerificationComponent);
//     component = fixture.componentInstance;
//   });
//
//   afterEach(() => {
//     // 清理定時器
//     if (component['countdownTimer']) {
//       clearInterval(component['countdownTimer']);
//     }
//     if (component['resendTimer']) {
//       clearInterval(component['resendTimer']);
//     }
//   });
//
//   describe('元件初始化', () => {
//     it('應該成功建立元件', () => {
//       expect(component).toBeTruthy();
//     });
//
//     it('應該在初始化時設定正確的初始狀態', () => {
//       component.ngOnInit();
//
//       expect(component.currentStatus).toBe(ApplicationStatus.IDENTITY_VERIFYING);
//       expect(component.verificationMethod).toBe('otp');
//       expect(component.otpDigits).toEqual(['', '', '', '', '', '']);
//       expect(component.hasError).toBeFalse();
//       expect(component.countdown).toBe(300);
//     });
//
//     it('應該呼叫狀態服務更新應用程式狀態', () => {
//       component.ngOnInit();
//
//       expect(mockStateService.updateApplicationStatus).toHaveBeenCalledWith({
//         status: ApplicationStatus.IDENTITY_VERIFYING,
//         stepTitle: '身份驗證',
//         currentStep: 1
//       });
//     });
//
//     it('應該啟動倒數計時器', fakeAsync(() => {
//       component.ngOnInit();
//       tick(1000);
//
//       expect(component.countdown).toBe(299);
//     }));
//   });
//
//   describe('OTP輸入處理', () => {
//     beforeEach(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//     });
//
//     it('應該正確處理數字輸入', () => {
//       const mockEvent = {
//         target: { value: '1' }
//       };
//
//       component.onOtpInput(mockEvent, 0);
//
//       expect(component.otpDigits[0]).toBe('1');
//       expect(component.hasError).toBeFalse();
//     });
//
//     it('應該拒絕非數字輸入', () => {
//       const mockEvent = {
//         target: { value: 'a' }
//       };
//
//       component.onOtpInput(mockEvent, 0);
//
//       expect(mockEvent.target.value).toBe('');
//       expect(component.otpDigits[0]).toBe('');
//     });
//
//     it('應該在輸入時清除錯誤狀態', () => {
//       component.hasError = true;
//       const mockEvent = {
//         target: { value: '1' }
//       };
//
//       component.onOtpInput(mockEvent, 0);
//
//       expect(component.hasError).toBeFalse();
//     });
//
//     it('應該在填滿6位數時自動驗證', () => {
//       spyOn((component as typeof component & { verifyOTP: () => void }), 'verifyOTP');
//
//       // 模擬填滿6位數
//       for (let i = 0; i < 6; i++) {
//         component.otpDigits[i] = (i + 1).toString();
//       }
//
//       const mockEvent = {
//         target: { value: '6' }
//       };
//
//       component.onOtpInput(mockEvent, 5);
//
//       expect(component['verifyOTP']).toHaveBeenCalled();
//     });
//   });
//
//   describe('鍵盤事件處理', () => {
//     beforeEach(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//     });
//
//     it('應該處理Backspace鍵', () => {
//       const mockEvent = new KeyboardEvent('keydown', { key: 'Backspace' });
//       spyOn(mockEvent, 'preventDefault');
//
//       component.onOtpKeydown(mockEvent, 1);
//
//       expect(mockEvent.preventDefault).toHaveBeenCalled();
//     });
//
//     it('應該處理左右箭頭鍵', () => {
//       const mockEvent = new KeyboardEvent('keydown', { key: 'ArrowLeft' });
//       spyOn(mockEvent, 'preventDefault');
//
//       component.onOtpKeydown(mockEvent, 1);
//
//       expect(mockEvent.preventDefault).toHaveBeenCalled();
//     });
//   });
//
//   describe('貼上功能', () => {
//     beforeEach(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//     });
//
//     it('應該正確處理6位數字貼上', () => {
//       const mockEvent = {
//         preventDefault: jasmine.createSpy('preventDefault'),
//         clipboardData: {
//           getData: jasmine.createSpy('getData').and.returnValue('123456')
//         }
//       } as unknown as ClipboardEvent;
//
//       spyOn((component as typeof component & { verifyOTP: () => void }), 'verifyOTP');
//
//       component.onPaste(mockEvent);
//
//       expect(mockEvent.preventDefault).toHaveBeenCalled();
//       expect(component.otpDigits).toEqual(['1', '2', '3', '4', '5', '6']);
//       expect(component['verifyOTP']).toHaveBeenCalled();
//     });
//
//     it('應該過濾非數字字符', () => {
//       const mockEvent = {
//         preventDefault: jasmine.createSpy('preventDefault'),
//         clipboardData: {
//           getData: jasmine.createSpy('getData').and.returnValue('12a3b4c')
//         }
//       } as unknown as ClipboardEvent;
//
//       component.onPaste(mockEvent);
//
//       expect(component.otpDigits).toEqual(['1', '2', '3', '4', '', '']);
//     });
//   });
//
//   describe('OTP驗證', () => {
//     beforeEach(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//     });
//
//     it('應該在驗證碼正確時驗證成功', () => {
//       // 設定開發模式以使用預設驗證碼
//       component.isDevelopment = true;
//       component.otpDigits = ['1', '2', '3', '4', '5', '6'];
//
//       component['verifyOTP']();
//
//       expect(component.hasError).toBeFalse();
//     });
//
//     it('應該在驗證碼錯誤時顯示錯誤', () => {
//       component.isDevelopment = true;
//       component.otpDigits = ['1', '2', '3', '4', '5', '7'];
//       spyOn((component as typeof component & { shakeInputs: () => void }), 'shakeInputs');
//
//       component['verifyOTP']();
//
//       expect(component.hasError).toBeTrue();
//       expect(component['shakeInputs']).toHaveBeenCalled();
//     });
//   });
//
//   describe('重新發送功能', () => {
//     beforeEach(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//     });
//
//     it('應該在倒數期間禁止重新發送', () => {
//       component.resendCountdown = 30;
//       const initialCountdown = component.countdown;
//
//       component.resendOtp();
//
//       expect(component.countdown).toBe(initialCountdown);
//     });
//
//     it('應該在可以重新發送時執行重新發送', fakeAsync(() => {
//       component.resendCountdown = 0;
//       spyOn((component as typeof component & { updateSentTime: () => void }), 'updateSentTime');
//
//       component.resendOtp();
//       tick(1000);
//
//       expect(component['updateSentTime']).toHaveBeenCalled();
//       expect(component.countdown).toBe(299);
//       expect(component.resendCountdown).toBe(59);
//     }));
//
//     it('應該在重新發送時清空輸入', () => {
//       component.resendCountdown = 0;
//       component.otpDigits = ['1', '2', '3', '4', '5', '6'];
//       component.hasError = true;
//
//       component.resendOtp();
//
//       expect(component.otpDigits).toEqual(['', '', '', '', '', '']);
//       expect(component.hasError).toBeFalse();
//     });
//   });
//
//   describe('導航功能', () => {
//     it('應該在返回時導航到landing頁面', () => {
//       component.goBack();
//
//       expect(mockRouter.navigate).toHaveBeenCalledWith(['/ibr/individual/landing']);
//     });
//
//     it('應該在繼續時導航到匯款詳情頁面', () => {
//       component.isDevelopment = true;
//       component.isVerified = true;
//
//       component.proceedToNext();
//
//       expect(mockRouter.navigate).toHaveBeenCalledWith(['/ibr/individual/remittance-detail']);
//     });
//
//     it('應該在驗證碼錯誤時不允許繼續', () => {
//       component.isDevelopment = true;
//       component.isVerified = false;
//
//       component.proceedToNext();
//
//       expect(component.hasError).toBeTrue();
//       expect(mockRouter.navigate).not.toHaveBeenCalled();
//     });
//   });
//
//   describe('時間格式化', () => {
//     it('應該正確格式化秒數為mm:ss格式', () => {
//       expect(component.formatTime(90)).toBe('01:30');
//       expect(component.formatTime(0)).toBe('00:00');
//       expect(component.formatTime(300)).toBe('05:00');
//       expect(component.formatTime(65)).toBe('01:05');
//     });
//   });
//
//   describe('canProceed getter', () => {
//     it('應該在OTP完整且正確時返回true', () => {
//       component.isDevelopment = true;
//       component.isVerified = true;
//
//       expect(component.canProceed).toBeTrue();
//     });
//
//     it('應該在OTP不完整時返回false', () => {
//       component.isDevelopment = true;
//       component.isVerified = false;
//
//       expect(component.canProceed).toBeFalse();
//     });
//
//     it('應該在OTP錯誤時返回false', () => {
//       component.isDevelopment = true;
//       component.isVerified = false;
//
//       expect(component.canProceed).toBeFalse();
//     });
//   });
//
//   describe('客服功能', () => {
//     it('應該呼叫開啟客服視窗', () => {
//       spyOn(console, 'log');
//
//       component.openCustomerService();
//
//       expect(console.log).toHaveBeenCalledWith('開啟客服視窗');
//     });
//   });
//
//   describe('元件銷毀', () => {
//     it('應該在銷毀時清理定時器', () => {
//       component.ngOnInit();
//       const countdownTimerSpy = spyOn(window, 'clearInterval');
//
//       component.ngOnDestroy();
//
//       expect(countdownTimerSpy).toHaveBeenCalled();
//     });
//   });
//
//   describe('FIDO驗證模式', () => {
//     it('應該在FIDO模式下初始化不同的驗證流程', () => {
//       // 重新設定mockStateService返回FIDO驗證方式
//       mockStateService.getApplicationData.and.returnValue({
//         verificationMethod: 'fido'
//       });
//
//       spyOn(console, 'log');
//
//       component.ngOnInit();
//
//       expect(console.log).toHaveBeenCalledWith('初始化 FIDO 生物辨識驗證');
//       expect(component.verificationMethod).toBe('fido');
//     });
//   });
//
//   describe('響應式行為', () => {
//     it('應該在手機版模式下正確調整UI', () => {
//       // 這個測試需要根據實際的響應式邏輯來實作
//       expect(component).toBeTruthy();
//     });
//   });
//
//   describe('錯誤處理', () => {
//     it('應該正確處理系統錯誤', () => {
//       // 模擬系統錯誤情況
//       component.hasError = true;
//
//       expect(component.hasError).toBeTrue();
//     });
//   });
//
//   describe('狀態持續性', () => {
//     it('應該在頁面重新載入後保持狀態', () => {
//       // 這個測試需要模擬localStorage或sessionStorage
//       expect(component).toBeTruthy();
//     });
//   });
//
//   describe('無障礙功能', () => {
//     it('應該支援鍵盤導航', () => {
//       component.ngOnInit();
//       fixture.detectChanges();
//
//       const inputs = fixture.debugElement.nativeElement.querySelectorAll('.otp-input');
//       expect(inputs.length).toBe(6);
//     });
//
//     it('應該提供適當的ARIA標籤', () => {
//       component.ngOnInit();
//       fixture.detectChanges();
//
//       // 檢查是否有適當的無障礙標記
//       expect(component).toBeTruthy();
//     });
//   });
//
//   describe('整合測試', () => {
//     it('應該完整執行OTP驗證流程', fakeAsync(() => {
//       component.ngOnInit();
//       fixture.detectChanges();
//
//       // 模擬輸入正確的OTP
//       component.isDevelopment = true;
//       component.isVerified = true;
//       for (let i = 0; i < 6; i++) {
//         const mockEvent = {
//           target: { value: (i + 1).toString() }
//         };
//         component.onOtpInput(mockEvent, i);
//       }
//
//       tick(100);
//
//       // 點擊繼續按鈕
//       component.proceedToNext();
//
//       expect(mockRouter.navigate).toHaveBeenCalledWith(['/ibr/individual/remittance-detail']);
//     }));
//   });
// });
