import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { IbrLandingComponent } from './landing.component';
import { SlideDialogService, DialogRef } from '../../../../@core/shared/service/slide-dialog.service';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { IndividualApiService } from '../../services/individual-api.service';
import { PersonalDataDialogComponent } from '../../../../@core/shared-2/components/personal-data-dialog/personal-data-dialog.component';

describe('IbrLandingComponent - 個資告知聲明對話框測試', () => {
  let component: IbrLandingComponent;
  let fixture: ComponentFixture<IbrLandingComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockDialogService: jasmine.SpyObj<SlideDialogService>;
  let mockStateService: jasmine.SpyObj<IbrStateService>;
  let mockIndividualApi: jasmine.SpyObj<IndividualApiService>;
  let mockDialogRef: jasmine.SpyObj<DialogRef<PersonalDataDialogComponent>>;

  beforeEach(async () => {
    // 創建 mock 服務
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockDialogService = jasmine.createSpyObj('SlideDialogService', ['customOpen']);
    mockStateService = jasmine.createSpyObj('IbrStateService', [
      'updateApplicationStatus',
      'updateApplicationData',
      'getApplicationData'
    ]);
    mockIndividualApi = jasmine.createSpyObj('IndividualApiService', [
      'agreeToTerms',
      'getTermsContent'
    ]);
    mockDialogRef = jasmine.createSpyObj('DialogRef', ['afterClosed']);

    await TestBed.configureTestingModule({
      imports: [IbrLandingComponent],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: SlideDialogService, useValue: mockDialogService },
        { provide: IbrStateService, useValue: mockStateService },
        { provide: IndividualApiService, useValue: mockIndividualApi }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IbrLandingComponent);
    component = fixture.componentInstance;
    
    // 設定預設的 mock 返回值
    mockStateService.getApplicationData.and.returnValue({
      identityData: { taiwanId: 'A123456789' }
    });
    mockDialogRef.afterClosed.and.returnValue(of(true));
    mockDialogService.customOpen.and.returnValue(mockDialogRef);
  });

  describe('組件初始化', () => {
    it('應該創建組件', () => {
      expect(component).toBeTruthy();
    });

    it('應該在 ngOnInit 時更新申請狀態', () => {
      component.ngOnInit();
      
      expect(mockStateService.updateApplicationStatus).toHaveBeenCalledWith({
        status: ApplicationStatus.TERMS_AGREEING,
        stepTitle: '條款同意',
        currentStep: 1
      });
    });
  });

  describe('個資告知聲明對話框', () => {
    it('應該使用正確的配置開啟對話框', () => {
      // 設定條款內容
      component.personalDataContent = {
        title: '個人資料保護告知聲明',
        content: '測試內容',
        version: '1.0',
        lastUpdated: new Date(),
        mandatory: true,
        type: 'PERSONAL_DATA'
      };

      // 呼叫開啟對話框方法
      component.openPersonalDataStatement();

      // 驗證 customOpen 被呼叫
      expect(mockDialogService.customOpen).toHaveBeenCalledWith(
        PersonalDataDialogComponent,
        jasmine.objectContaining({
          style: 'modal',
          width: '800px',
          maxWidth: '90vw',
          height: 'auto',
          maxHeight: '80vh',
          hasCloseBtn: false,
          disableClose: false,
          panelClass: 'personal-data-modal',
          backdropClass: 'personal-data-backdrop',
          data: {
            content: '測試內容',
            title: '個人資料保護告知聲明'
          }
        })
      );
    });

    it('應該處理對話框關閉事件', () => {
      spyOn(console, 'log');
      
      component.openPersonalDataStatement();
      
      // 驗證訂閱 afterClosed
      expect(mockDialogRef.afterClosed).toHaveBeenCalled();
      
      // 觸發關閉事件
      const afterClosedCallback = mockDialogRef.afterClosed.calls.mostRecent().returnValue;
      afterClosedCallback.subscribe((result: boolean) => {
        if (result === true) {
          expect(console.log).toHaveBeenCalledWith('使用者已確認個資告知聲明');
        }
      });
    });

    it('當沒有條款內容時應該顯示載入中', () => {
      // 清空條款內容
      component.personalDataContent = null;

      component.openPersonalDataStatement();

      expect(mockDialogService.customOpen).toHaveBeenCalledWith(
        PersonalDataDialogComponent,
        jasmine.objectContaining({
          data: {
            content: '載入中...',
            title: '個人資料保護告知聲明'
          }
        })
      );
    });
  });

  describe('條款同意流程', () => {
    beforeEach(() => {
      component.personalDataAgreed = true;
      component.digitalTermsAgreed = true;
    });

    it('兩個條款都勾選後才能繼續', () => {
      component.personalDataAgreed = false;
      component.digitalTermsAgreed = true;
      expect(component.canProceed).toBeFalsy();

      component.personalDataAgreed = true;
      component.digitalTermsAgreed = false;
      expect(component.canProceed).toBeFalsy();

      component.personalDataAgreed = true;
      component.digitalTermsAgreed = true;
      expect(component.canProceed).toBeTruthy();
    });

    it('載入中時不能繼續', () => {
      component.personalDataAgreed = true;
      component.digitalTermsAgreed = true;
      component.isLoading = true;
      
      expect(component.canProceed).toBeFalsy();
    });
  });

  describe('錯誤處理', () => {
    it('載入條款內容失敗時應該顯示錯誤訊息', async () => {
      spyOn(console, 'error');
      
      // 模擬載入失敗
      const loadTermsContent = component['loadTermsContent'].bind(component);
      
      // 由於目前使用模擬資料，這裡只測試錯誤處理邏輯
      try {
        await loadTermsContent();
      } catch (error) {
        expect(component.errorMessage).toBe('載入條款內容失敗，請稍後再試');
        expect(console.error).toHaveBeenCalled();
      }
    });
  });

  describe('輔助方法測試', () => {
    it('應該正確生成會話ID', () => {
      const sessionId = component['generateSessionId']();
      
      expect(sessionId).toMatch(/^SES\d+[A-Z0-9]{6}$/);
      expect(sessionId.length).toBeGreaterThan(10);
    });

    it('應該正確偵測作業系統', () => {
      // 模擬不同的 user agent
      const originalUserAgent = navigator.userAgent;
      
      // 測試 Windows
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        configurable: true
      });
      expect(component['detectOperatingSystem']()).toBe('Windows');
      
      // 測試 macOS
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        configurable: true
      });
      expect(component['detectOperatingSystem']()).toBe('macOS');
      
      // 還原
      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        configurable: true
      });
    });

    it('應該正確判斷是否為行動裝置', () => {
      const originalUserAgent = navigator.userAgent;
      
      // 測試手機
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
        configurable: true
      });
      expect(component['isMobileDevice']()).toBeTruthy();
      
      // 測試桌面
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        configurable: true
      });
      expect(component['isMobileDevice']()).toBeFalsy();
      
      // 還原
      Object.defineProperty(navigator, 'userAgent', {
        value: originalUserAgent,
        configurable: true
      });
    });
  });
});