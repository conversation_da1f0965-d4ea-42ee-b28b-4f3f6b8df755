import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';
import { PersonalDataDialogComponent } from '../../../../@core/shared-2/components/personal-data-dialog/personal-data-dialog.component';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { IndividualApiService } from '../../services/individual-api.service';
import { TermsAgreementRequest, TermsAgreementResponse, TermsContent } from '../../models/landing.model';
import { SharedTestDataService, UnifiedTestData, RemittanceDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 自然人條款同意頁面組件 (Step 1)
 * 
 * === 頁面功能說明 ===
 * 此頁面處理用戶對個資告知聲明和數位解款條款的同意
 * 包含條款內容展示、用戶同意確認、狀態記錄等功能
 * 
 * === 前後端整合驗證結果 ===
 * ✅ 前端模型: TermsAgreementRequest - 已更新符合後端DTO
 *    新格式: { taiwanId, termsVersion, agreedAt, ipAddress, userAgent, geolocation, additionalInfo }
 *    包含完整的追蹤和指紋資訊
 * 
 * ✅ 後端API對應: TermsController已實作且可用
 *    API端點: 
 *    - GET /api/ibr/terms/content - 取得條款內容 ✅
 *    - POST /api/ibr/terms/agree - 記錄用戶同意 ✅
 *    - GET /api/ibr/terms/status - 查詢條款同意狀態 ✅
 *    - GET /api/ibr/terms/latest-version - 取得最新條款版本 ✅
 * 
 * ✅ URL路徑一致性: 前端API服務已修正為 /api/ibr/terms/*
 *    原路徑: /api/ibr/individual/terms/* → 新路徑: /api/ibr/terms/*
 * 
 * ✅ 前端API方法: individual-api.service.ts已新增
 *    - agreeToTerms() - 條款同意
 *    - checkTermsStatus() - 查詢狀態 
 *    - getTermsContent() - 取得內容
 *    - getLatestTermsVersion() - 取得版本
 * 
 * === 狀態管理 ===
 * ✅ 更新申請狀態為 TERMS_AGREEING -> IDENTITY_SELECTING
 * ✅ 記錄條款同意資料到狀態服務
 * ✅ 條款同意完成後導航到身份驗證選擇頁
 * 
 * === 導航流程 ===
 * 條款同意頁 (Step 1) → 身份驗證選擇頁 (Step 2): /ibr/individual/identity-selection
 * 
 * 對應UI設計: 01.jpg
 */
@Component({
  selector: 'app-ibr-landing',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IbrSharedModule
  ],
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.scss']
})
export class IbrLandingComponent implements OnInit {
  
  // 同意狀態
  personalDataAgreed = false;
  digitalTermsAgreed = false;
  
  // 載入狀態
  isLoading = false;
  
  // 條款內容
  personalDataContent: TermsContent | null = null;
  digitalTermsContent: TermsContent | null = null;
  
  // 錯誤訊息
  errorMessage = '';
  
  // Email 連結參數
  isFromEmail = false;
  emailCaseNo: string | null = null;
  emailToken: string | null = null;
  
  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private dialogService: SlideDialogService,
    private stateService: IbrStateService,
    private individualApi: IndividualApiService,
    private sharedTestDataService: SharedTestDataService
  ) { }

  ngOnInit(): void {
    // 檢查是否從 Email 連結進入
    this.checkEmailParameters();
    
    // 更新申請狀態為條款同意進行中
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.TERMS_AGREEING,
      stepTitle: '條款同意',
      currentStep: 1
    });
    
    // 載入條款內容
    this.loadTermsContent();
  }

  /**
   * 檢查 Email 連結參數
   */
  private async checkEmailParameters(): Promise<void> {
    const source = this.route.snapshot.queryParams['source'];
    const caseNo = this.route.snapshot.queryParams['caseNo'];
    const token = this.route.snapshot.queryParams['token'];

    if (source === 'email' && caseNo && token) {
      this.isFromEmail = true;
      this.emailCaseNo = caseNo;
      this.emailToken = token;
      
      try {
        // 驗證 token 並載入案件資料
        await this.loadRemittanceDataFromEmail(caseNo, token);
      } catch (error) {
        console.error('Email 連結驗證失敗:', error);
        this.errorMessage = 'Email 連結已失效或無效，請重新申請';
      }
    }
  }

  /**
   * 從 Email 連結載入匯款資料
   */
  private async loadRemittanceDataFromEmail(caseNo: string, token: string): Promise<void> {
    try {
      // TODO: 呼叫後端 API 驗證 token 並取得匯款資料
      // const response = await this.individualApi.verifyEmailToken(token, caseNo).toPromise();
      
      // 使用統一測試資料
      let testData = this.sharedTestDataService.getCurrentTestData();
      if (!testData) {
        testData = this.sharedTestDataService.getDefaultTestData();
        this.sharedTestDataService.setTestData(testData);
      }
      
      // 確保案件編號一致
      if (testData.RemitRefNo !== caseNo) {
        testData.RemitRefNo = caseNo;
        this.sharedTestDataService.setTestData(testData);
      }
      
      const remittanceInfo = this.sharedTestDataService.toRemittanceDisplayInfo(testData);
      const mockData = {
        caseNo: caseNo,
        senderName: remittanceInfo.senderName,
        senderCountry: this.getCountryDisplayName(remittanceInfo.country),
        currency: remittanceInfo.currency,
        amount: parseFloat(remittanceInfo.amount),
        twdAmount: Math.round(parseFloat(remittanceInfo.amount) * 32.35),
        notificationDate: remittanceInfo.notificationDate
      };
      
      // 存入匯款資料到狀態服務
      this.stateService.updateApplicationData({
        remittanceInfo: mockData,
        source: 'email'
      });
      
      console.log('Landing 已載入統一測試資料:', mockData);
      
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * 取得國家顯示名稱
   */
  private getCountryDisplayName(countryCode: string): string {
    const countryMap: { [key: string]: string } = {
      'US': '美國 (USA)',
      'UK': '英國 (UK)',
      'JP': '日本 (Japan)',
      'SG': '新加坡 (Singapore)',
      'CN': '中國 (China)',
      'HK': '香港 (Hong Kong)'
    };
    return countryMap[countryCode] || countryCode;
  }

  // 檢查是否可以繼續
  get canProceed(): boolean {
    return this.personalDataAgreed && this.digitalTermsAgreed && !this.isLoading;
  }

  /**
   * 載入條款內容
   */
  private async loadTermsContent(): Promise<void> {
    try {
      this.isLoading = true;
      this.errorMessage = '';
      
      // TODO: 實作API呼叫載入條款內容
      // 注意: 需要建立前端API方法，目前後端已有 GET /api/ibr/terms/content
      // const personalData = await this.individualApi.getTermsContent('PERSONAL_DATA').toPromise();
      // const digitalTerms = await this.individualApi.getTermsContent('DIGITAL_REMITTANCE').toPromise();
      
      // 目前使用模擬資料
      this.personalDataContent = {
        title: '個人資料保護告知聲明',
        content: '凱基銀行個人資料保護告知聲明內容...',
        version: '1.0',
        lastUpdated: new Date(),
        mandatory: true,
        type: 'PERSONAL_DATA'
      };
      
      this.digitalTermsContent = {
        title: '數位解款服務條款',
        content: '數位解款服務條款內容...',
        version: '1.0', 
        lastUpdated: new Date(),
        mandatory: true,
        type: 'DIGITAL_REMITTANCE'
      };
      
    } catch (error) {
      console.error('載入條款內容失敗:', error);
      this.errorMessage = '載入條款內容失敗，請稍後再試';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 點擊我同意按鈕
   */
  async onAgreeClick(): Promise<void> {
    if (!this.canProceed) {
      return;
    }

    try {
      this.isLoading = true;
      this.errorMessage = '';

      // 準備條款同意請求資料 (符合後端DTO格式)
      const agreementRequest: TermsAgreementRequest = {
        taiwanId: this.stateService.getApplicationData()?.identityData?.taiwanId || '', // 從狀態服務取得
        termsVersion: '1.0', // 可從getLatestTermsVersion API取得
        agreedAt: new Date(),
        ipAddress: await this.getClientIpAddress(), // 取得客戶端IP
        userAgent: navigator.userAgent,
        agreementMethod: 'WEB',
        deviceId: this.generateDeviceId(),
        browserFingerprint: this.generateBrowserFingerprint(),
        additionalInfo: {
          currentUrl: window.location.href,
          referrerUrl: document.referrer,
          screenResolution: `${screen.width}x${screen.height}`,
          browserLanguage: navigator.language,
          operatingSystem: this.detectOperatingSystem(),
          isMobile: this.isMobileDevice(),
          networkType: this.getNetworkType(),
          sessionId: this.generateSessionId()
        },
        // 前端專用欄位
        personalDataAgreed: this.personalDataAgreed,
        digitalTermsAgreed: this.digitalTermsAgreed
      };

      // 呼叫後端API記錄條款同意
      // TODO: 實作API呼叫 - 後端已有 POST /api/ibr/terms/agree
      // 需要將前端API服務的URL從 /individual/terms/agree 改為 /terms/agree
      // const response = await this.individualApi.agreeToTerms(agreementRequest).toPromise();
      
      // 模擬API回應
      const response: TermsAgreementResponse = {
        success: true,
        agreementId: 'AGR' + Date.now(),
        message: '條款同意記錄成功',
        nextRoute: '/ibr/individual/identity-selection'
      };

      if (response.success) {
        // 更新狀態服務
        this.stateService.updateApplicationStatus({
          status: ApplicationStatus.IDENTITY_SELECTING,
          stepTitle: '選擇驗證方式',
          currentStep: 2
        });

        // 記錄條款同意資料
        this.stateService.updateApplicationData({
          termsAgreement: {
            personalDataAgreed: this.personalDataAgreed,
            digitalTermsAgreed: this.digitalTermsAgreed,
            agreementId: response.agreementId,
            agreementTimestamp: new Date()
          }
        });

        // 導航到下一頁
        this.router.navigate([response.nextRoute || '/ibr/individual/identity-selection']);
        
      } else {
        this.errorMessage = response.message || '條款同意處理失敗';
      }

    } catch (error) {
      console.error('條款同意處理失敗:', error);
      this.errorMessage = '系統錯誤，請稍後再試';
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 開啟個資告知聲明
   */
  openPersonalDataStatement(): void {
    const dialogRef = this.dialogService.customOpen(PersonalDataDialogComponent, {
      style: 'modal',  // 改為 modal 而非 offcanvas
      width: '800px',  // 設定合理的寬度
      maxWidth: '90vw', // 最大寬度不超過視窗的 90%
      height: 'auto',  // 高度自動調整
      maxHeight: '80vh', // 最大高度不超過視窗的 80%
      hasCloseBtn: false, // 設為 false，因為 PersonalDataDialogComponent 已經有自己的 UI
      disableClose: false,
      panelClass: 'personal-data-modal', // 加入自定義樣式類別
      backdropClass: 'personal-data-backdrop', // 使用自定義背景樣式
      data: {
        content: this.personalDataContent?.content || '載入中...',
        title: this.personalDataContent?.title || '個人資料保護告知聲明'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        // 使用者已閱讀完整內容並確認
        console.log('使用者已確認個資告知聲明');
      }
    });
  }

  /**
   * 開啟數位解款條款
   */
  openDigitalTerms(): void {
    const termsContent = this.digitalTermsContent?.content || `數位解款條款

歡迎使用凱基銀行數位解款服務，以下為服務條款：

第一條 服務內容
本服務提供線上外匯匯入款項解款功能

第二條 使用者責任
使用者應確保所提供資料之真實性與正確性

第三條 交易限額
個人單筆解款限額為新台幣50萬元

第四條 手續費用
依本行公告之費率收取相關手續費

第五條 服務時間
本服務提供時間為營業日上午9:00至下午3:30

（此為模擬內容，實際內容請參考銀行正式條款）`;

    alert(termsContent);
  }

  /**
   * 開啟客服
   */
  openCustomerService(): void {
    alert('客服服務\n\n如需協助請撥打客服專線：\n0800-588-111\n\n服務時間：\n週一至週五 9:00-18:00');
  }

  /**
   * 生成會話ID
   */
  private generateSessionId(): string {
    return 'SES' + Date.now() + Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  /**
   * 取得客戶端IP位址
   */
  private async getClientIpAddress(): Promise<string> {
    try {
      // 在實際環境中可以呼叫API取得客戶端IP
      // 目前返回空字串，讓後端從請求標頭取得
      return '';
    } catch (error) {
      console.warn('無法取得客戶端IP位址:', error);
      return '';
    }
  }

  /**
   * 生成裝置ID
   */
  private generateDeviceId(): string {
    // 簡化版裝置指紋
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }
    const fingerprint = canvas.toDataURL();
    return btoa(fingerprint).substring(0, 32);
  }

  /**
   * 生成瀏覽器指紋
   */
  private generateBrowserFingerprint(): string {
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      navigator.platform
    ].join('|');
    
    return btoa(fingerprint).substring(0, 64);
  }

  /**
   * 偵測作業系統
   */
  private detectOperatingSystem(): string {
    const userAgent = navigator.userAgent;
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'macOS';
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  /**
   * 檢查是否為行動裝置
   */
  private isMobileDevice(): boolean {
    return /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * 取得網路類型
   */
  private getNetworkType(): string {
    // 使用Navigator API如果可用
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    if (connection) {
      return connection.effectiveType || connection.type || 'unknown';
    }
    return 'unknown';
  }

  /**
   * 返回上一頁
   */
  goBack(): void {
    // 可以返回到外部入口頁或首頁
    this.router.navigate(['/ibr/individual/external-entry']);
  }
}
