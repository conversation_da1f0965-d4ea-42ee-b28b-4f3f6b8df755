# Individual Remittance Detail 組件功能說明

## 新增功能

### 1. 收款人資料垂直排列
- **修改**: 將收款人資料從水平排列改為垂直排列
- **排列方式**: 由上到下顯示姓名、帳號、銀行資訊
- **樣式**: 每個欄位佔一行，標籤在上，值在下

### 2. 匯款性質選擇對話框
- **檔案**: `remittance-nature-dialog.component.ts`
- **功能**: 點擊匯款性質區塊時，會彈出對話框顯示可選擇的匯款性質選項
- **選項包含**:
  - 19D - 專業技術及事務收入
  - 250 - 收回國外存款  
  - 262 - 收回投資國外股權證券
  - 280 - 收回對外融資
  - 410 - 薪資款匯入
  - 510 - 贍家匯款收入

### 3. 同意條款 Checkbox
- **位置**: 匯款資訊卡片下方，手續費資訊上方
- **包含兩個選項**:
  1. 同意透過財金公司跨行通匯系統，進行新台幣資金撥轉至本次驗證帳號
  2. 同意凱基銀行依約定匯款性質進行《自動解款服務》

### 4. 按鈕狀態控制
- **條件**: 只有當兩個 checkbox 都勾選時，「下一步」按鈕才會啟用
- **視覺回饋**: 未勾選時按鈕呈現禁用狀態（灰色）

## 技術實現

### 組件結構
```
i-remittance-detail/
├── i-remittance-detail.component.ts     # 主組件
├── i-remittance-detail.component.html   # 主模板
├── i-remittance-detail.component.scss   # 主樣式
├── remittance-nature-dialog.component.ts # 匯款性質選擇對話框
└── README.md                            # 說明文件
```

### 依賴服務
- `SlideDialogService`: 用於開啟匯款性質選擇對話框
- `IbrStateService`: 管理應用程式狀態
- `Router`: 頁面導航

### 狀態管理
```typescript
// Checkbox 狀態
checkboxStates = {
  agreeFinancialService: false,    // 同意財金公司服務
  agreeAutoRemittance: false       // 同意自動解款服務
};

// 匯款性質資料
remittanceData.nature = {
  code: string,
  description: string,
  descriptionEn: string
};
```

### 樣式特色
- 響應式設計，支援手機、平板、桌機
- 自訂 checkbox 樣式，符合設計規範
- 匯款性質區塊具有 hover 效果
- 按鈕禁用狀態視覺回饋

## 使用方式

1. **選擇匯款性質**: 點擊匯款性質區塊，從對話框中選擇適當的性質
2. **勾選同意項目**: 勾選兩個必要的同意條款
3. **繼續流程**: 點擊「下一步」按鈕進入下一個步驟

## 注意事項

- 匯款性質選擇為可選功能，不影響流程進行
- 兩個 checkbox 為必選項目，未勾選無法繼續
- 對話框支援鍵盤 ESC 鍵關閉
- 所有文字內容支援中英文雙語顯示 