import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { SlideDialogService } from '../../../../@core/shared/service/slide-dialog.service';
import { RemittanceNatureDialogComponent, RemittanceNature } from './remittance-nature-dialog.component';
import { AutoRemittanceDialogComponent } from './auto-remittance-dialog.component';
import { SharedTestDataService, UnifiedTestData, RemittanceDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 自然人匯款詳情確認頁面組件 (Step 3)
 * 
 * === 頁面功能說明 ===
 * 此頁面顯示匯款詳細資訊，包含匯款人、受款人、金額、匯率、性質等
 * 用戶需要確認資訊正確並同意相關條款後進入下一步
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 匯款詳情展示: currency, amount, exchangeRate, twdAmount
 *    - 參與方資訊: remitter(匯款人), beneficiary(受款人)
 *    - 業務邏輯: nature(匯款性質選擇), remarks(備註)
 *    - 手續費計算: feeAmount, netAmount 自動計算
 *    - 同意確認: agreeFinancialService, agreeAutoRemittance
 *    - 匯款性質對話框: RemittanceNatureDialogComponent 整合
 * 
 * 🔶 後端API狀態分析:
 *    - 目前只有管理員API，缺乏一般用戶API
 *    - 需要新增一般用戶匯款詳情API:
 *      * GET /api/ibr/individual/remittance/{id}/detail - 取得匯款詳情
 *      * GET /api/ibr/individual/exchange-rate/current - 取得當前匯率  
 *      * GET /api/ibr/individual/remittance/natures - 匯款性質選項
 *      * POST /api/ibr/individual/fees/calculate - 手續費計算
 *      * POST /api/ibr/individual/remittance/detail/confirm - 確認詳情
 * 
 * ✅ 資料模型對應:
 *    - 前端 remittanceData 與後端 IndividualRemittance 實體對應良好
 *    - currency ↔ currency, amount ↔ amount, twdAmount ↔ twdAmount
 *    - exchangeRate ↔ exchangeRate, remarks ↔ customerNote
 *    - 手續費計算邏輯與後端 fee, getNetAmount() 方法對應
 * 
 * === 狀態管理 ===
 * ✅ 更新申請狀態: REMITTANCE_CONFIRMING -> AMOUNT_CONFIRMING
 * ✅ 記錄匯款詳情確認資料到狀態服務
 * ✅ 導航: /ibr/individual/amount-confirmation
 * 
 * === 導航流程 ===
 * 匯款詳情頁 (Step 3) → 金額確認頁 (Step 4): /ibr/individual/amount-confirmation
 * 
 * 對應UI設計: 07-09.jpg
 */
@Component({
  selector: 'app-i-remittance-detail',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IbrSharedModule
  ],
  templateUrl: './i-remittance-detail.component.html',
  styleUrls: ['./i-remittance-detail.component.scss']
})
export class IRemittanceDetailComponent implements OnInit {
  currentStatus: ApplicationStatus = ApplicationStatus.REMITTANCE_CONFIRMING;
  
  // 開發模式標記
  isDevelopment = true;
  
  // 匯款資料（從統一測試資料取得）
  remittanceData = {
    currency: 'USD',
    amount: 0,
    exchangeRate: '32.35',
    twdAmount: 0,
    remitter: {
      name: '',
      account: '',
      bank: '',
      country: ''
    },
    beneficiary: {
      name: '',
      account: '',
      bank: '',
      idNumber: '',
      phone: '',
      email: ''
    },
    nature: {
      code: '',
      description: '',
      descriptionEn: ''
    },
    remarks: ''
  };
  
  // 手續費計算
  feeAmount = 200;
  
  // 實收金額
  get netAmount(): number {
    return this.remittanceData.twdAmount - this.feeAmount;
  }

  // 聯絡人資訊（共用欄位）
  contactInfo = '';
  contactInfoError = '';
  contactInfoType: 'email' | 'phone' | '' = ''; // 記錄輸入類型

  // Checkbox 狀態
  checkboxStates = {
    agreeFinancialService: false,
    agreeAutoRemittance: false
  };

  constructor(
    private router: Router,
    private stateService: IbrStateService,
    private slideDialogService: SlideDialogService,
    private sharedTestDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    // 頁面初始化邏輯
    // 在實際環境中，這裡應該從服務獲取匯款資料
    this.loadRemittanceData();
  }
  
  /**
   * 載入匯款資料
   */
  private loadRemittanceData(): void {
    console.log('載入匯款資料...');
    
    // 檢查是否有統一測試資料
    let testData = this.sharedTestDataService.getCurrentTestData();
    if (!testData) {
      // 如果沒有測試資料，使用預設資料
      testData = this.sharedTestDataService.getDefaultTestData();
      this.sharedTestDataService.setTestData(testData);
    }
    
    // 從統一測試資料載入匯款資訊
    this.loadFromTestData(testData);
    
    console.log('已載入統一測試資料:', this.remittanceData);
  }
  
  /**
   * 從統一測試資料載入
   */
  private loadFromTestData(testData: UnifiedTestData): void {
    const amount = parseFloat(testData.Amount);
    const exchangeRate = 32.35; // 模擬匯率
    const twdAmount = Math.round(amount * exchangeRate);
    
    // 匯款性質對應表
    const natureMap: { [key: string]: any } = {
      '410': {
        code: '410',
        description: '薪資所得',
        descriptionEn: 'Salary Income'
      },
      '001': {
        code: '001',
        description: '貨物貿易',
        descriptionEn: 'Trade in Goods'
      },
      '002': {
        code: '002',
        description: '服務貿易',
        descriptionEn: 'Trade in Services'
      },
      '': {
        code: '410',
        description: '薪資所得',
        descriptionEn: 'Salary Income'
      }
    };
    
    this.remittanceData = {
      currency: testData.Currency,
      amount: amount,
      exchangeRate: exchangeRate.toString(),
      twdAmount: twdAmount,
      remitter: {
        name: testData.PayerName,
        account: '****' + testData.RemitRefNo.slice(-4),
        bank: this.getRemitterBankName(testData.PayerCountry),
        country: testData.PayerCountry
      },
      beneficiary: {
        name: testData.PayeeName,
        account: '****' + testData.PayeeAccount.slice(-4),
        bank: this.getBeneficiaryBankName(testData.PayeeBankCode),
        idNumber: testData.PayeeID,
        phone: testData.PayeeTel,
        email: testData.PayeeMail
      },
      nature: natureMap[testData.SourceOfFund] || natureMap['410'],
      remarks: testData.Memo
    };
    
    // 計算手續費
    this.calculateFees(amount);
  }
  
  /**
   * 取得匯款人銀行名稱
   */
  private getRemitterBankName(country: string): string {
    const bankMap: { [key: string]: string } = {
      'US': 'WELLS FARGO BANK',
      'UK': 'HSBC BANK PLC',
      'JP': 'BANK OF TOKYO-MITSUBISHI UFJ',
      'SG': 'DBS BANK LTD'
    };
    return bankMap[country] || 'OVERSEAS BANK';
  }
  
  /**
   * 取得受款人銀行名稱
   */
  private getBeneficiaryBankName(bankCode: string): string {
    // 根據銀行代碼前3碼取得銀行名稱
    const bankCodePrefix = bankCode ? bankCode.substring(0, 3) : '';
    const bankMap: { [key: string]: string } = {
      '004': '臺灣銀行 (004)',
      '008': '華南商業銀行 (008)',
      '009': '彰化商業銀行 (009)',
      '012': '臺灣中小企業銀行 (012)',
      '013': '國泰世華商業銀行 (013)',
      '017': '兆豐國際商業銀行 (017)',
      '050': '臺灣企銀 (050)',
      '803': '聯邦商業銀行 (803)',
      '809': '凱基銀行 (809)',
      '812': '台新國際商業銀行 (812)',
      '822': '中國信託商業銀行 (822)'
    };
    return bankMap[bankCodePrefix] || `凱基銀行 (809)`;
  }
  
  /**
   * 計算手續費
   */
  private calculateFees(amount: number): void {
    // 簡化的手續費計算邏輯
    const baseRate = 0.001; // 0.1%
    const minFee = 200; // 最低手續費
    const maxFee = 800; // 最高手續費
    
    let calculatedFee = amount * 32.35 * baseRate; // 以台幣計算
    calculatedFee = Math.max(minFee, Math.min(maxFee, calculatedFee));
    
    this.feeAmount = Math.round(calculatedFee);
  }
  
  /**
   * 客服按鈕點擊處理
   */
  openCustomerService(): void {
    console.log('開啟客服視窗');
    // 實際環境中應開啟客服對話或撥打電話
  }

  /**
   * 開啟匯款性質選擇對話框
   */
  openRemittanceNatureDialog(): void {
    console.log('點擊匯款性質，準備開啟對話框');
    
    try {
      const dialogRef = this.slideDialogService.customOpen(RemittanceNatureDialogComponent, {
        style: 'dialog',
        title: '匯款性質選擇',
        width: '600px',
        hasCloseBtn: true,
        centerTitle: true,
        data: {
          currentNature: this.remittanceData.nature
        }
      });

      console.log('對話框已開啟:', dialogRef);

      dialogRef.afterClosed().subscribe((selectedNature: RemittanceNature) => {
        console.log('對話框關閉，選擇的性質:', selectedNature);
        if (selectedNature) {
          this.remittanceData.nature = selectedNature;
          console.log('更新匯款性質:', selectedNature);
        }
      });
    } catch (error) {
      console.error('開啟對話框時發生錯誤:', error);
    }
  }

  /**
   * 驗證聯絡資訊（Email 或手機號碼）
   */
  validateContactInfo(): void {
    if (!this.contactInfo) {
      this.contactInfoError = '請輸入 Email 或手機號碼';
      this.contactInfoType = '';
      return;
    }
    
    // 移除空白
    const trimmedInfo = this.contactInfo.trim();
    
    // 檢查是否為 Email 格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(trimmedInfo)) {
      this.contactInfoError = '';
      this.contactInfoType = 'email';
      return;
    }
    
    // 檢查是否為手機號碼格式（移除破折號後檢查）
    const phoneNumber = trimmedInfo.replace(/-/g, '');
    const phoneRegex = /^09\d{8}$/;
    if (phoneRegex.test(phoneNumber)) {
      this.contactInfoError = '';
      this.contactInfoType = 'phone';
      return;
    }
    
    // 都不符合
    this.contactInfoError = '請輸入有效的 Email 或手機號碼（09開頭的10位數字）';
    this.contactInfoType = '';
  }

  /**
   * 檢查是否可以繼續下一步
   */
  canProceed(): boolean {
    // 檢查聯絡人資訊是否填寫且格式正確
    if (!this.contactInfo || this.contactInfoError || !this.contactInfoType) {
      return false;
    }
    
    return this.checkboxStates.agreeFinancialService && 
           this.checkboxStates.agreeAutoRemittance;
  }

  /**
   * 確認並繼續到下一步
   */
  proceedToNext(): void {
    // 先驗證聯絡人資訊
    this.validateContactInfo();
    
    if (!this.canProceed()) {
      if (!this.contactInfo || this.contactInfoError || !this.contactInfoType) {
        alert('請先填寫聯絡資訊（Email 或手機號碼）');
      } else {
        alert('請先勾選所有必要的同意項目');
      }
      return;
    }

    console.log('確認匯款資訊，前往下一步');
    console.log('聯絡資訊:', this.contactInfo);
    console.log('資訊類型:', this.contactInfoType);
    
    // 更新狀態
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.AMOUNT_CONFIRMING,
      stepTitle: '金額確認',
      currentStep: 3
    });
    
    // 導航到金額確認頁面 (第3步)
    this.router.navigate(['/ibr/individual/amount-confirmation']);
  }

  /**
   * 開啟自動解款服務條款對話框
   */
  openAutoRemittanceDialog(): void {
    console.log('開啟自動解款服務條款對話框');
    
    try {
      const dialogRef = this.slideDialogService.customOpen(AutoRemittanceDialogComponent, {
        style: 'dialog',
        title: '自動解款服務條款',
        width: '700px',
        hasCloseBtn: true,
        centerTitle: true
      });

      console.log('自動解款服務條款對話框已開啟');

      dialogRef.afterClosed().subscribe(() => {
        console.log('自動解款服務條款對話框已關閉');
      });
    } catch (error) {
      console.error('開啟自動解款服務條款對話框時發生錯誤:', error);
    }
  }
}
