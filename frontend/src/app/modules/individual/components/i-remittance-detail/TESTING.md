# 匯款性質對話框功能測試說明

## 測試步驟

### 1. 啟動應用程式
```bash
cd frontend
ng serve --port 4200
```

### 2. 導航到測試頁面
- 開啟瀏覽器訪問: `http://localhost:4200`
- 導航到 Individual Remittance Detail 頁面
- 路徑: `/ibr/individual/remittance-detail`

### 2.1 驗證收款人資料排列
1. **檢查收款人資料區塊**
   - 找到「收款人資料 Beneficiary Information」區塊
   - 確認資料是垂直排列（由上到下）：
     - 姓名 Name: 王小明
     - 帳號 Account: ****6789
     - 銀行 Bank: 凱基銀行 (809)
   - 每個欄位應該佔一行，標籤在上，值在下

### 3. 測試匯款性質對話框
1. **找到匯款性質區塊**
   - 在頁面中找到「匯款性質 Remittance Nature」區塊
   - 應該顯示當前的匯款性質：`262 - 收回投資國外股權證券`
   - 區塊右側應該有下拉箭頭 `▼`

2. **點擊匯款性質區塊**
   - 點擊整個匯款性質區塊
   - 應該會彈出對話框顯示匯款性質選項

3. **驗證對話框內容**
   - 對話框標題：「匯款性質選擇」
   - 應該顯示 6 個選項：
     - 19D - 專業技術及事務收入
     - 250 - 收回國外存款
     - 262 - 收回投資國外股權證券
     - 280 - 收回對外融資
     - 410 - 薪資款匯入
     - 510 - 贍家匯款收入

4. **測試選擇功能**
   - 點擊任一選項
   - 對話框應該關閉
   - 匯款性質區塊應該更新為選擇的新選項

### 4. 測試 Checkbox 功能
1. **找到同意條款區塊**
   - 在匯款資訊卡片下方應該有「同意條款區塊」
   - 包含兩個 checkbox 選項

2. **測試 Checkbox 狀態**
   - 初始狀態：兩個 checkbox 都未勾選
   - 「下一步」按鈕應該是禁用狀態（灰色）

3. **勾選第一個 Checkbox**
   - 勾選「同意透過財金公司跨行通匯系統...」
   - 「下一步」按鈕仍應該是禁用狀態

4. **勾選第二個 Checkbox**
   - 勾選「若收款人同時勾選...」
   - 「下一步」按鈕應該變為啟用狀態（藍色）

5. **取消勾選測試**
   - 取消任一 checkbox
   - 「下一步」按鈕應該重新變為禁用狀態

## 預期行為

### 匯款性質對話框
- ✅ 點擊匯款性質區塊會開啟對話框
- ✅ 對話框顯示 6 個匯款性質選項
- ✅ 點擊選項會關閉對話框並更新顯示
- ✅ 點擊關閉按鈕或按 ESC 鍵可關閉對話框

### Checkbox 功能
- ✅ 兩個 checkbox 都未勾選時，按鈕禁用
- ✅ 只勾選一個 checkbox 時，按鈕仍禁用
- ✅ 兩個 checkbox 都勾選時，按鈕啟用
- ✅ 按鈕狀態有視覺回饋（顏色變化）

## 故障排除

### 如果對話框沒有彈出
1. 檢查瀏覽器控制台是否有錯誤
2. 確認點擊的是整個匯款性質區塊，不只是文字
3. 檢查 `openRemittanceNatureDialog()` 方法是否被調用

### 如果 Checkbox 不工作
1. 檢查 `[(ngModel)]` 綁定是否正確
2. 確認 `FormsModule` 已正確導入
3. 檢查 `canProceed()` 方法邏輯

### 常見錯誤
- **對話框服務錯誤**: 確認 `SlideDialogService` 正確注入
- **樣式問題**: 檢查 CSS 類名是否正確
- **數據綁定錯誤**: 確認組件屬性名稱正確

## 開發者工具檢查

### 控制台日誌
點擊匯款性質時應該看到：
```
點擊匯款性質，準備開啟對話框
對話框已開啟: [DialogRef object]
```

選擇匯款性質時應該看到：
```
對話框關閉，選擇的性質: [RemittanceNature object]
更新匯款性質: [RemittanceNature object]
```

### 網路請求
- 此功能不涉及 API 調用，所有數據都是本地模擬數據

### DOM 檢查
- 匯款性質區塊應該有 `clickable` CSS 類
- Checkbox 應該有正確的 `checked` 狀態
- 按鈕應該有 `disabled` 屬性（當未勾選時） 