/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 步驟指示器 === */
.step-indicator {
  margin-bottom: 40px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-current {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
}

.step-total {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.step-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-current-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-total-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0044ad;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* === 匯款資訊卡片 === */
.remittance-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

/* === 匯款金額區塊 === */
.amount-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid #e8e8e8;
}

.amount-header {
  margin-bottom: 16px;
}

.amount-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
}

.amount-title-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 13px;
  font-weight: 400;
  margin: 0;
}

.amount-content {
  text-align: center;
}

.amount-main {
  margin-bottom: 16px;
}

.currency {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin-right: 8px;
}

.amount {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 32px;
  font-weight: 600;
  letter-spacing: -0.5px;
}

.exchange-info {
  display: flex;
  justify-content: space-around;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.exchange-rate,
.twd-amount {
  text-align: center;
}

.rate-label,
.twd-label {
  display: block;
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  margin-bottom: 4px;
}

.rate-value,
.twd-value {
  display: block;
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

/* === 資訊區塊 === */
.info-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #e8e8e8;
}

.section-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 12px 0;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.info-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
}

.info-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: right;
}

/* === 匯款性質 === */
.nature-content {
  padding: 12px;
  background: #f0f7ff;
  border-radius: 6px;
  text-align: center;
}

.nature-code {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.nature-desc {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  margin-bottom: 4px;
}

.nature-desc-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  line-height: 140%;
}

/* === 附言 === */
.remarks-content {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.remarks-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  margin: 0;
  font-style: italic;
}

/* === 手續費資訊 === */
.fee-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 32px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.fee-item:not(:last-child) {
  border-bottom: 1px dashed #ffeaa7;
}

.fee-item.total {
  padding-top: 12px;
  
  .fee-label {
    font-weight: 500;
  }
  
  .fee-value {
    font-size: 18px;
    font-weight: 600;
    color: #856404;
  }
}

.fee-label {
  color: #856404;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
}

.fee-value {
  color: #856404;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  justify-content: center;
}

.btn-primary {
  min-width: 200px;
  height: 56px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  padding: 0 32px;
  background: #0044ad;
}

.btn-next {
  width: 100%;
}

.btn-primary:hover:not(:disabled) {
  background: #003390;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.3);
}

.btn-primary:disabled {
  background: #e2e2e2;
  cursor: not-allowed;
  
  .button-text-zh,
  .button-text-en {
    color: #a6a6a6;
  }
}

.button-text-zh {
  color: #ffffff;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
}

.button-text-en {
  color: #ffffff;
  font-family: "Montserrat", sans-serif;
  font-size: 18px;
  line-height: 100%;
  font-weight: 500;
}

/* === 測試資訊 === */
.test-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  margin: 20px;
}

.test-info h4 {
  margin: 0 0 10px 0;
  color: #0044ad;
}

.test-info p {
  margin: 5px 0;
  font-size: 0.9em;
  color: #666;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 500px;
  }
  
  .content-section {
    padding: 48px;
  }
  
  .main-title {
    font-size: 26px;
  }
  
  .subtitle {
    font-size: 15px;
  }
  
  .step-current {
    font-size: 20px;
  }
  
  .amount {
    font-size: 36px;
  }
  
  .remittance-card {
    padding: 32px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
  
  .content-section {
    padding: 60px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }
  
  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }
  
  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }
  
  .content-section {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .step-current {
    font-size: 16px;
  }
  
  .step-total {
    font-size: 14px;
  }
  
  .amount {
    font-size: 28px;
  }
  
  .btn-primary {
    height: 48px;
    min-width: 100%;
  }
  
  .button-text-zh,
  .button-text-en {
    font-size: 16px;
  }
  
  .remittance-card {
    padding: 16px;
  }
  
  .exchange-info {
    flex-direction: column;
    gap: 12px;
  }
}

/* === 匯款性質點擊樣式 === */
.nature-content.clickable {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #ffffff;
  transition: all 0.2s ease;
}

.nature-content.clickable:hover {
  border-color: #0044ad;
  background: #f8f9fa;
}

.nature-display {
  flex: 1;
}

.dropdown-icon {
  margin-left: 12px;
  color: #666666;
  font-size: 12px;
  transition: transform 0.2s ease;
}

.nature-content.clickable:hover .dropdown-icon {
  color: #0044ad;
}

/* === 聯絡人資訊 === */
.section-subtitle {
  font-size: 14px;
  color: #666666;
  margin-top: 4px;
  margin-bottom: 16px;
}

.contact-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.input-wrapper {
  flex: 1;
}

.input-label {
  display: block;
  font-size: 14px;
  color: #333333;
  margin-bottom: 8px;
  font-weight: 500;
}

.contact-input {
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  color: #041c43;
  background: #ffffff;
  transition: all 0.2s ease;
  
  &::placeholder {
    color: #9ca3af;
    font-size: 14px;
  }
  
  &:focus {
    outline: none;
    border-color: #0044ad;
    box-shadow: 0 0 0 3px rgba(0, 68, 173, 0.1);
  }
  
  &:hover:not(:focus) {
    border-color: #6b7280;
  }
  
  &.error {
    border-color: #dc2626;
    
    &:focus {
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  }
}

.error-message {
  color: #dc2626;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  margin-top: 4px;
}

/* === 輸入提示 === */
.input-hint {
  color: #6b7280;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.input-format-hint {
  margin-top: 4px;
}

.format-valid {
  color: #10b981;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* === 同意條款區塊 === */
.agreement-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.checkbox-item {
  margin-bottom: 20px;
}

.checkbox-item:last-child {
  margin-bottom: 0;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  gap: 12px;
  margin: 0;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: #ffffff;
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: #0044ad;
  border-color: #0044ad;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  flex: 1;
}

.checkbox-text-zh {
  color: #333333;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  line-height: 150%;
  font-weight: 400;
  margin-bottom: 4px;
}

.checkbox-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  line-height: 140%;
  font-weight: 400;
}

/* === 可點擊條款連結 === */
.clickable-link {
  color: #0044ad;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
  
  &:hover {
    color: #003390;
    text-decoration: none;
    background-color: rgba(0, 68, 173, 0.05);
    padding: 0 2px;
    border-radius: 2px;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

/* === 按鈕禁用狀態 === */
.btn-primary.disabled {
  background: #e2e2e2 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.btn-primary.disabled .button-text-zh,
.btn-primary.disabled .button-text-en {
  color: #a6a6a6 !important;
}

/* === 收款人資訊垂直排列 === */
.beneficiary-info-vertical {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.beneficiary-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.beneficiary-label {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 13px;
  font-weight: 400;
  line-height: 140%;
}

.beneficiary-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 150%;
}

/* === 響應式調整 === */
@media (max-width: 767px) {
  .agreement-section {
    padding: 16px;
  }
  
  .checkbox-text-zh {
    font-size: 13px;
  }
  
  .checkbox-text-en {
    font-size: 11px;
  }
  
  .checkbox-custom {
    width: 18px;
    height: 18px;
  }
  
  .checkbox-input:checked + .checkbox-custom::after {
    font-size: 10px;
  }
  
  .beneficiary-value {
    font-size: 14px;
  }
  
  .beneficiary-label {
    font-size: 12px;
  }
}