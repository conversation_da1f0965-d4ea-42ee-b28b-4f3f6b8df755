import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';

/**
 * 自動解款服務條款對話框組件
 * 顯示自動解款服務的詳細條款內容
 */
@Component({
  selector: 'app-auto-remittance-dialog',
  standalone: true,
  imports: [
    CommonModule,
    IbrSharedModule
  ],
  template: `
    <div class="auto-remittance-dialog">
      <!-- 對話框標題 -->
      <div class="dialog-header">
        <h2 class="dialog-title">自動解款服務條款</h2>
        <h3 class="dialog-subtitle">Automatic Remittance Service Terms</h3>
      </div>

      <!-- 對話框內容 -->
      <div class="dialog-content">
        <!-- 服務說明 -->
        <section class="content-section">
          <h3 class="section-title">一、服務說明 Service Description</h3>
          <div class="content-text">
            <p>本行「自動解款服務」係指當您同意本服務後，往後來自同一匯款人匯入同一收款帳號之款項，將由本行系統自動進行解款作業，無需您再次進行手動操作。</p>
            <p class="english-text">The "Automatic Remittance Service" means that after you agree to this service, future remittances from the same remitter to the same beneficiary account will be automatically processed by our bank's system without requiring manual operation.</p>
          </div>
        </section>

        <!-- 適用範圍 -->
        <section class="content-section">
          <h3 class="section-title">二、適用範圍 Scope of Application</h3>
          <div class="content-text">
            <p>1. 本服務僅適用於相同匯款人、相同收款帳號、相同匯款性質之匯入款項。</p>
            <p>2. 自動解款將依據您首次設定之匯款性質進行處理。</p>
            <p>3. 若匯款資料有異動或不符，系統將通知您進行人工確認。</p>
            <p class="english-text">
              1. This service only applies to remittances from the same remitter to the same beneficiary account with the same remittance nature.<br>
              2. Automatic processing will be based on the remittance nature you initially set.<br>
              3. If there are any changes or discrepancies in the remittance data, the system will notify you for manual confirmation.
            </p>
          </div>
        </section>

        <!-- 服務期限 -->
        <section class="content-section">
          <h3 class="section-title">三、服務期限 Service Period</h3>
          <div class="content-text">
            <p>本服務自您同意啟用之日起生效，您可隨時透過本行網路銀行或臨櫃申請終止本服務。</p>
            <p class="english-text">This service takes effect from the date you agree to activate it. You may terminate this service at any time through our online banking or by visiting our branch.</p>
          </div>
        </section>

        <!-- 注意事項 -->
        <section class="content-section">
          <h3 class="section-title">四、注意事項 Important Notes</h3>
          <div class="content-text">
            <p>1. 自動解款服務將依據主管機關相關法令規定執行。</p>
            <p>2. 若匯款涉及洗錢防制或其他法令規定需進行特別審查時，本行保留暫停自動解款並要求人工確認之權利。</p>
            <p>3. 本行將於每次自動解款完成後，發送通知至您指定之聯絡方式。</p>
            <p class="english-text">
              1. The automatic remittance service will be executed in accordance with relevant regulations.<br>
              2. If the remittance involves anti-money laundering or other regulatory requirements requiring special review, the bank reserves the right to suspend automatic processing and require manual confirmation.<br>
              3. The bank will send notifications to your designated contact method after each automatic remittance is completed.
            </p>
          </div>
        </section>

        <!-- 責任與義務 -->
        <section class="content-section">
          <h3 class="section-title">五、責任與義務 Responsibilities and Obligations</h3>
          <div class="content-text">
            <p>1. 您應確保所提供之資料正確無誤。</p>
            <p>2. 如因您提供錯誤資料導致之損失，本行不負賠償責任。</p>
            <p>3. 您同意配合本行進行必要之身分驗證及資料更新。</p>
            <p class="english-text">
              1. You shall ensure that the information provided is accurate.<br>
              2. The bank shall not be liable for any losses caused by incorrect information provided by you.<br>
              3. You agree to cooperate with the bank for necessary identity verification and data updates.
            </p>
          </div>
        </section>
      </div>

      <!-- 對話框底部 -->
      <div class="dialog-footer">
        <p class="footer-note">
          本條款如有修改，將於本行網站公告，不另行個別通知。<br>
          <span class="english-text">Any modifications to these terms will be announced on our bank's website without individual notification.</span>
        </p>
      </div>
    </div>
  `,
  styles: [`
    .auto-remittance-dialog {
      padding: 0;
      max-height: 80vh;
      overflow-y: auto;
    }

    .dialog-header {
      background: #f8f9fa;
      padding: 24px;
      border-bottom: 1px solid #e8e8e8;
      text-align: center;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .dialog-title {
      color: #041c43;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 22px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .dialog-subtitle {
      color: #666666;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
      font-weight: 400;
      margin: 0;
    }

    .dialog-content {
      padding: 32px;
    }

    .content-section {
      margin-bottom: 32px;
    }

    .content-section:last-child {
      margin-bottom: 0;
    }

    .section-title {
      color: #0044ad;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e8e8e8;
    }

    .content-text {
      color: #333333;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 15px;
      line-height: 1.8;
    }

    .content-text p {
      margin: 0 0 12px 0;
    }

    .content-text p:last-child {
      margin-bottom: 0;
    }

    .english-text {
      color: #666666;
      font-family: "Montserrat", sans-serif;
      font-size: 14px;
      font-style: italic;
      margin-top: 8px;
    }

    .dialog-footer {
      background: #f8f9fa;
      padding: 20px 32px;
      border-top: 1px solid #e8e8e8;
      margin-top: 32px;
    }

    .footer-note {
      color: #666666;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 13px;
      line-height: 1.6;
      margin: 0;
      text-align: center;
    }

    /* 捲軸樣式 */
    .auto-remittance-dialog::-webkit-scrollbar {
      width: 8px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-thumb {
      background: #cccccc;
      border-radius: 4px;
    }

    .auto-remittance-dialog::-webkit-scrollbar-thumb:hover {
      background: #999999;
    }

    /* 響應式設計 */
    @media (max-width: 767px) {
      .dialog-header {
        padding: 20px;
      }

      .dialog-title {
        font-size: 20px;
      }

      .dialog-subtitle {
        font-size: 14px;
      }

      .dialog-content {
        padding: 20px;
      }

      .section-title {
        font-size: 16px;
      }

      .content-text {
        font-size: 14px;
      }

      .english-text {
        font-size: 13px;
      }

      .dialog-footer {
        padding: 16px 20px;
      }

      .footer-note {
        font-size: 12px;
      }
    }
  `]
})
export class AutoRemittanceDialogComponent {
  constructor() {}
}