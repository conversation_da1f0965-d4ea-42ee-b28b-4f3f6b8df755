import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

import { IIdentitySelectionComponent } from './i-identity-selection.component';
import { IbrStateService } from '../../../../@core/shared-2/services/ibr-state.service';

describe('IIdentitySelectionComponent', () => {
  let component: IIdentitySelectionComponent;
  let fixture: ComponentFixture<IIdentitySelectionComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockStateService: jasmine.SpyObj<IbrStateService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const stateServiceSpy = jasmine.createSpyObj('IbrStateService', ['updateApplicationStatus', 'getApplicationData', 'updateApplicationData']);

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        IIdentitySelectionComponent
      ],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: IbrStateService, useValue: stateServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(IIdentitySelectionComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockStateService = TestBed.inject(IbrStateService) as jasmine.SpyObj<IbrStateService>;
    
    // Mock the getApplicationData method to return an observable
    mockStateService.getApplicationData.and.returnValue(new Promise(resolve => resolve({})));
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with details collapsed', () => {
    expect(component.isDetailsExpanded).toBeFalse();
  });

  it('should toggle details expansion when toggleDetails is called', () => {
    // Initially collapsed
    expect(component.isDetailsExpanded).toBeFalse();
    
    // Toggle to expand
    component.toggleDetails();
    expect(component.isDetailsExpanded).toBeTrue();
    
    // Toggle to collapse
    component.toggleDetails();
    expect(component.isDetailsExpanded).toBeFalse();
  });

  it('should display remittance information', () => {
    expect(component.remittanceInfo.senderName).toBe('Shiang Ru');
    expect(component.remittanceInfo.country).toBe('US');
    expect(component.remittanceInfo.notificationDate).toBe('2023/06/05');
    expect(component.remittanceInfo.daysRemaining).toBe(2);
    expect(component.remittanceInfo.remittanceType).toBe('410 薪資款匯入');
    expect(component.remittanceInfo.message).toBe('Monthly Salary');
  });

  it('should display expanded details in correct layout', () => {
    // Expand details
    component.toggleDetails();
    fixture.detectChanges();
    
    const expandedDetails = fixture.nativeElement.querySelector('.expanded-details');
    expect(expandedDetails).toBeTruthy();
    
    const detailRows = fixture.nativeElement.querySelectorAll('.detail-row');
    expect(detailRows.length).toBe(2);
    
    // Check if labels and values are properly structured
    const firstRow = detailRows[0];
    const labels = firstRow.querySelector('.detail-labels');
    const value = firstRow.querySelector('.detail-value');
    
    expect(labels).toBeTruthy();
    expect(value).toBeTruthy();
    expect(value.textContent.trim()).toBe('410 薪資款匯入');
  });

  it('should validate form fields correctly', () => {
    // Test Chinese name validation
    component.formData.chineseName = '';
    expect(component['validateChineseName']()).toBeFalse();
    expect(component.formErrors.chineseName).toBe('請輸入中文姓名');

    component.formData.chineseName = '王小明';
    expect(component['validateChineseName']()).toBeTrue();
    expect(component.formErrors.chineseName).toBe('');

    // Test ID number validation
    component.formData.idNumber = '';
    expect(component['validateIdNumber']()).toBeFalse();
    expect(component.formErrors.idNumber).toBe('請輸入身分證字號/居留證號碼');

    component.formData.idNumber = 'A123456789';
    expect(component['validateIdNumber']()).toBeTrue();
    expect(component.formErrors.idNumber).toBe('');
  });

  it('should handle responsive design correctly', () => {
    // Test mobile detection
    spyOnProperty(window, 'innerWidth').and.returnValue(500);
    component['checkScreenSize']();
    expect(component.isMobile).toBeTrue();
    expect(component.isTablet).toBeFalse();
    expect(component.isDesktop).toBeFalse();

    // Test desktop detection
    spyOnProperty(window, 'innerWidth').and.returnValue(1200);
    component['checkScreenSize']();
    expect(component.isMobile).toBeFalse();
    expect(component.isTablet).toBeFalse();
    expect(component.isDesktop).toBeTrue();
  });
}); 
