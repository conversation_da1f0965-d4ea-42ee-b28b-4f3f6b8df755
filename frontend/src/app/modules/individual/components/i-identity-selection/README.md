# I-Identity-Selection 組件

## 概述
這是自然人線上外匯解款流程中的身份驗證資料填寫頁面，用於收集受款人的基本資料以進行身份驗證。

## 功能特色

### 🎨 響應式設計
- **手機版**: 全螢幕顯示，適合觸控操作
- **平板版**: 中等寬度顯示，保持良好的可讀性
- **桌機版**: 佔螢幕 2/3 寬度，最大寬度 800px，居中顯示

### 📝 表單驗證
- **即時驗證**: 欄位失焦時立即驗證
- **完整驗證**: 提交前進行完整表單驗證
- **錯誤提示**: 清楚的錯誤訊息顯示

### 🔽 展開詳情功能
- **動態展開**: 點擊「展開詳情 More」可顯示更多匯款資訊
- **平滑動畫**: 展開/收合時有流暢的滑動動畫效果
- **圖示旋轉**: 下拉箭頭會根據展開狀態旋轉
- **詳細資訊**: 顯示匯款類型、附言等額外資訊
- **左右佈局**: 標題在左側，內容在右側並排顯示
- **響應式設計**: 手機版自動切換為上下佈局

### 🔍 驗證規則
- **中文姓名**: 至少2個字，僅允許中文字符
- **英文姓名**: 僅允許英文字母、空格、逗號、句號、連字號
- **身分證字號**: 台灣身分證字號格式 (A123456789)
- **生日**: 年齡必須在18-120歲之間
- **分行代碼**: 4位數字
- **銀行帳號**: 10-16位數字
- **手機號碼**: 台灣手機號碼格式 (09xxxxxxxx)

## 使用方式

### 基本使用
```html
<app-i-identity-selection></app-i-identity-selection>
```

### 路由配置
```typescript
{
  path: 'identity-selection',
  component: IIdentitySelectionComponent
}
```

## 組件結構

### 主要區塊
1. **統一 Header**: 使用 `<app-ibr-header>` 統一 IBR 頁面標頭
2. **頁面標題**: 線上外匯解款標題
3. **步驟指示器**: 顯示當前步驟 (第1步/共3步)
4. **匯款資訊**: 顯示匯款人資訊和時限提醒
5. **表單區域**: 受款資料填寫表單
6. **操作按鈕**: 下一步按鈕

### 表單欄位
- 中文姓名/銀行帳戶名稱
- 英文姓名 + 確認勾選
- 身分證字號/居留證號碼
- 生日
- 銀行代碼及分行代碼
- 銀行帳號
- 手機號碼

## 樣式特色

### 設計系統
- **主色調**: KGI 藍 (#0044ad)
- **字體**: Noto Sans TC (中文), Montserrat (英文)
- **圓角**: 8px (表單元素), 12px (卡片)
- **陰影**: 柔和的陰影效果

### 互動效果
- **懸停效果**: 按鈕和可點擊元素的懸停狀態
- **焦點狀態**: 表單元素的焦點邊框
- **錯誤狀態**: 紅色邊框和錯誤訊息

## 技術實作

### 依賴項目
- Angular 17+
- RxJS
- FormsModule (雙向綁定)
- IbrSharedModule (統一 IBR 組件)

### 狀態管理
- 使用 `IbrStateService` 管理應用狀態
- 表單資料自動儲存到狀態服務

### 響應式實作
- 使用 CSS Media Queries
- `@HostListener` 監聽視窗大小變化
- 動態 CSS 類別綁定

## 開發注意事項

### 圖片資源
確保以下 SVG 圖片存在於 `assets/image/icon/` 目錄：
- `basic-time.svg` - 時間圖示
- `center.svg` - 展開圖示
- `pull.svg` - 下拉圖示
- `basic-date.svg` - 日期圖示
- `basic-costumer-service.svg` - 客服圖示

### 統一 Header
- 使用 `<app-ibr-header>` 組件提供統一的頁面標頭
- 支援客服功能 `(customerServiceClick)` 事件
- 自動處理響應式設計和品牌一致性

### 表單驗證
- 所有驗證邏輯都在組件內部實作
- 支援即時驗證和提交前驗證
- 錯誤訊息會自動顯示在對應欄位下方

### 導航流程
- 成功提交後導航到 `/ibr/individual/identity-verification`
- 可透過 `goBack()` 方法返回上一頁

## 更新日誌

### v2.1.1 (2024-01-01)
- **佈局優化**: 調整展開詳情為左右並排佈局
- 標題顯示在左側，內容顯示在右側
- 手機版自動切換為上下佈局
- 改善視覺層次和可讀性

### v2.1.0 (2024-01-01)
- **新功能**: 實作展開詳情功能
- 添加匯款類型和附言顯示
- 實作平滑的展開/收合動畫
- 添加圖示旋轉效果
- 完善響應式設計支援

### v2.0.0 (2024-01-01)
- **重大更新**: 採用統一 IBR Header 設計
- 完全重構 HTML 結構，提升可維護性
- 優化響應式設計，改善用戶體驗
- 簡化 CSS 類別命名，提高可讀性
- 移除冗餘的導航欄，使用統一 Header

### v1.0.0 (2024-01-01)
- 初始版本
- 實作響應式設計
- 添加完整表單驗證
- 支援即時錯誤提示

## 測試建議

### 功能測試
1. 測試所有表單欄位的驗證規則
2. 測試響應式設計在不同螢幕尺寸下的表現
3. 測試表單提交和導航功能
4. 測試錯誤訊息的顯示和清除
5. 測試統一 Header 的客服功能

### 視覺測試
1. 確認在不同裝置上的視覺一致性
2. 測試互動效果 (懸停、焦點、錯誤狀態)
3. 確認圖片資源正確載入
4. 驗證與其他 IBR 頁面的設計一致性

## 架構優勢

### 統一設計系統
- 與其他 IBR 頁面保持一致的視覺風格
- 統一的 Header 組件減少重複代碼
- 標準化的響應式設計模式

### 可維護性
- 清晰的 HTML 結構，易於理解和修改
- 模組化的 CSS 樣式，便於重用
- 統一的錯誤處理和驗證邏輯

### 用戶體驗
- 流暢的響應式設計
- 即時的表單驗證回饋
- 一致的導航和互動模式 