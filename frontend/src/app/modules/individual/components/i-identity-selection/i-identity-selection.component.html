<div class="ibr-page-container">
  <!-- IBR 統一 Header -->
  <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

  <!-- 主要內容區域 -->
  <main class="main-content-wrapper">
    <div class="content-container" [ngClass]="getResponsiveClass()">
      <div class="content-section">
        <!-- 頁面標題 -->
        <div class="title-section">
          <h1 class="main-title">線上外匯解款</h1>
          <p class="subtitle">Online Foreign Exchange Remittance</p>
        </div>

        <!-- 步驟指示器 -->
        <div class="step-indicator">
          <div class="step-info">
            <div class="step-current">第 1 步 驗證身份</div>
            <div class="step-total">共 3 步</div>
          </div>
          <div class="step-subtitle">
            <div class="step-current-en">Step 1 Verify Identity</div>
            <div class="step-total-en">3 Steps</div>
          </div>
          <div class="step-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 33.33%;"></div>
            </div>
          </div>
        </div>

        <!-- 匯款資訊區域 -->
        <div class="remittance-info-section">
          <div class="remittance-header">
            <div class="remittance-title">
              <span class="title-text">你有一筆來自</span>
              <span class="sender-name">{{ remittanceInfo.senderName }}</span>
              <span class="title-text">的匯款</span>
            </div>
            <div class="remittance-title-en">
              <span class="title-text-en">You have received a remittance from </span>
              <span class="sender-name-en">{{ remittanceInfo.senderName }}</span>
              <span class="title-text-en">.</span>
            </div>
          </div>

          <div class="time-reminder">
            <div class="time-icon">
              <img src="../../../../../assets/image/icon/basic-time.svg" alt="時間圖示" />
            </div>
            <div class="time-text">
              <div class="reminder-text">
                請於 {{ remittanceInfo.daysRemaining }} 天內完成解匯，若未完成需至臨櫃辦理
              </div>
              <div class="reminder-text-en">
                Complete the remittance within {{ remittanceInfo.daysRemaining }} days, or visit a branch to process it
              </div>
            </div>
          </div>

          <div class="remittance-details">
            <div class="detail-item">
              <div class="detail-label">匯款國別</div>
              <div class="detail-label-en">Remittance Country</div>
              <div class="detail-value">{{ remittanceInfo.country }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">通知日期</div>
              <div class="detail-label-en">Notification Date</div>
              <div class="detail-value">{{ remittanceInfo.notificationDate }}</div>
            </div>
          </div>

          <!-- 展開的詳細資訊 -->
          <div class="expanded-details" *ngIf="isDetailsExpanded">
            <div class="detail-row">
              <div class="detail-labels">
                <div class="detail-label">匯款類型</div>
                <div class="detail-label-en">Remittance Type</div>
              </div>
              <div class="detail-value">{{ remittanceInfo.remittanceType }}</div>
            </div>
            <div class="detail-row">
              <div class="detail-labels">
                <div class="detail-label">附言</div>
                <div class="detail-label-en">Message</div>
              </div>
              <div class="detail-value">{{ remittanceInfo.message }}</div>
            </div>
          </div>

          <div class="expand-toggle" (click)="toggleDetails()">
            <img src="../../../../../assets/image/icon/center.svg" alt="展開圖示" />
            <div class="toggle-text">
              <span class="toggle-zh">{{ isDetailsExpanded ? '收合資料' : '展開詳情' }}</span>
              <span class="toggle-en">{{ isDetailsExpanded ? 'Less' : 'More' }}</span>
            </div>
            <img 
              src="../../../../../assets/image/icon/pull.svg"
              alt="下拉圖示" 
              [class.rotated]="isDetailsExpanded"
            />
          </div>
        </div>

        <!-- 表單區域 -->
        <div class="form-section">
          <div class="form-header">
            <h2 class="form-title">填寫你的受款資料</h2>
            <p class="form-subtitle">Fill In Your Payment Details</p>
            <p class="form-description">
              請輸入您當時提供予匯款人之帳號，以供凱基銀行確認身份 Please fill in the account number you provided to the remitter for KGI Bank to verify your identity.
            </p>
          </div>

          <form class="identity-form">
            <!-- 中文姓名 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">中文姓名/銀行帳戶名稱</div>
                <div class="label-en">Full Name / Bank Account Name</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="formErrors.chineseName"
                  placeholder="輸入中文姓名" 
                  [(ngModel)]="formData.chineseName"
                  name="chineseName"
                  (blur)="onFieldBlur('chineseName')"
                />
                <div class="error-message" *ngIf="formErrors.chineseName">
                  {{ formErrors.chineseName }}
                </div>
              </div>
            </div>

            <!-- 英文姓名與確認 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">英文姓名</div>
                <div class="label-en">English Name</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="formErrors.englishName"
                  value="Qing Lan, Wang" 
                  [(ngModel)]="formData.englishName"
                  name="englishName"
                  (blur)="onFieldBlur('englishName')"
                />
                <div class="error-message" *ngIf="formErrors.englishName">
                  {{ formErrors.englishName }}
                </div>
              </div>
              
              <div class="checkbox-field">
                <div class="checkbox-wrapper">
                  <input 
                    type="checkbox" 
                    id="confirmEnglishName"
                    class="checkbox-input"
                    [(ngModel)]="formData.confirmEnglishName"
                    name="confirmEnglishName"
                    (change)="onFieldBlur('confirmEnglishName')"
                  />
                  <label for="confirmEnglishName" class="checkbox-label"></label>
                </div>
                <div class="checkbox-content">
                  <div class="checkbox-text">確認收款人英文姓名無誤</div>
                  <div class="checkbox-text-en">Confirm the recipient's English name is correct.</div>
                  <div class="error-message" *ngIf="formErrors.confirmEnglishName">
                    {{ formErrors.confirmEnglishName }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 身分證字號 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">身分證字號/居留證號碼</div>
                <div class="label-en">ID Number / Resident Certificate Number</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  [class.error]="formErrors.idNumber"
                  placeholder="輸入身分證字號/居留證號碼" 
                  [(ngModel)]="formData.idNumber"
                  name="idNumber"
                  (blur)="onFieldBlur('idNumber')"
                />
                <div class="error-message" *ngIf="formErrors.idNumber">
                  {{ formErrors.idNumber }}
                </div>
              </div>
            </div>

            <!-- 生日 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">生日</div>
                <div class="label-en">Date of Birth</div>
              </div>
              <div class="field-input">
                <div class="date-input-wrapper" [class.error]="formErrors.birthDate">
                  <input 
                    type="date" 
                    class="date-input"
                    placeholder="選擇出生年月日" 
                    [(ngModel)]="formData.birthDate"
                    name="birthDate"
                    (blur)="onFieldBlur('birthDate')"
                  />
                  <img class="date-icon" src="../../../../../assets/image/icon/basic-date.svg" alt="日期圖示" />
                </div>
                <div class="error-message" *ngIf="formErrors.birthDate">
                  {{ formErrors.birthDate }}
                </div>
              </div>
            </div>

            <!-- 銀行代碼 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">銀行代碼及分行代碼 (共7碼)</div>
                <div class="label-en">Bank Code</div>
              </div>
              <div class="field-input">
                <input 
                  type="text" 
                  class="text-input"
                  value="中國信託商業銀行" 
                  [(ngModel)]="formData.bankName"
                  name="bankName"
                  readonly
                />
                <div class="bank-code-input" [class.error]="formErrors.branchCode">
                  <div class="bank-code-prefix">
                    <span class="code-number">822</span>
                    <div class="divider"></div>
                  </div>
                  <input 
                    type="text" 
                    class="branch-code-input"
                    placeholder="輸入分行代碼" 
                    [(ngModel)]="formData.branchCode"
                    name="branchCode"
                    (blur)="onFieldBlur('branchCode')"
                  />
                </div>
                <div class="error-message" *ngIf="formErrors.branchCode">
                  {{ formErrors.branchCode }}
                </div>
              </div>
            </div>

            <!-- 銀行帳號 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">銀行帳號</div>
                <div class="label-en">Bank Account No.</div>
              </div>
              <div class="field-input">
                <div class="account-input-wrapper" [class.error]="formErrors.accountNumber">
                  <input 
                    type="text" 
                    class="account-input"
                    placeholder="輸入銀行帳號" 
                    [(ngModel)]="formData.accountNumber"
                    name="accountNumber"
                    (blur)="onFieldBlur('accountNumber')"
                  />
                  <div class="account-suffix">
                    <div class="divider"></div>
                    <span class="suffix-number">345</span>
                  </div>
                </div>
                <div class="error-message" *ngIf="formErrors.accountNumber">
                  {{ formErrors.accountNumber }}
                </div>
              </div>
            </div>

            <!-- 手機號碼 -->
            <div class="form-field">
              <div class="field-label">
                <div class="label-zh">手機號碼</div>
                <div class="label-en">Mobile Phone No.</div>
              </div>
              <div class="field-input">
                <input 
                  type="tel" 
                  class="text-input"
                  [class.error]="formErrors.mobilePhone"
                  placeholder="輸入手機號碼" 
                  [(ngModel)]="formData.mobilePhone"
                  name="mobilePhone"
                  (blur)="onFieldBlur('mobilePhone')"
                />
                <div class="field-note">
                  請填寫留存於上述銀行之號碼 Please fill in the number registered with the above bank.
                </div>
                <div class="error-message" *ngIf="formErrors.mobilePhone">
                  {{ formErrors.mobilePhone }}
                </div>
              </div>
            </div>
          </form>

          <!-- 操作按鈕 -->
          <div class="action-section">
            <button 
              type="button" 
              class="btn-primary btn-next"
              (click)="proceedToNext()"
            >
              <span class="button-text-zh">下一步</span>
              <span class="button-text-en">Next</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
