/* === 基礎重置 === */
a,
button,
input,
select,
h1,
h2,
h3,
h4,
h5,
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  text-decoration: none;
  background: none;
  -webkit-font-smoothing: antialiased;
}

menu, ol, ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

/* === 頁面容器設計 === */
.ibr-page-container {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* === 主要內容區域 === */
.main-content-wrapper {
  flex: 1;
  width: 100%;
  padding: 0 20px 20px;
  display: flex;
  justify-content: center;
  background: #f8f9fa;
  position: relative;
  top: -1px;
}

.content-container {
  width: 100%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* === 內容區塊 === */
.content-section {
  padding: 40px;
}

/* === 標題區 === */
.title-section {
  text-align: center;
  margin-bottom: 32px;
}

.main-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 24px;
  line-height: 150%;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 140%;
  font-weight: 400;
  margin: 0;
}

/* === 步驟指示器 === */
.step-indicator {
  margin-bottom: 40px;
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.step-current {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 18px;
  font-weight: 500;
}

.step-total {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.step-subtitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-current-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-total-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.step-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e8e8e8;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #0044ad;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* === 匯款資訊區域 === */
.remittance-info-section {
  background: #ffffff;
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  margin-bottom: 32px;
}

.remittance-header {
  margin-bottom: 20px;
}

.remittance-title {
  margin-bottom: 8px;
}

.title-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.sender-name {
  color: #0044ad;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.remittance-title-en {
  margin-bottom: 0;
}

.title-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.sender-name-en {
  color: #0044ad;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.time-reminder {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fff3e0;
  border-radius: 8px;
  margin-bottom: 20px;
}

.time-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.time-icon img {
  width: 24px;
  height: 24px;
}

.time-text {
  flex: 1;
}

.reminder-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  margin-bottom: 4px;
}

.reminder-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
}

.remittance-details {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.detail-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 500;
}

.detail-label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.detail-value {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.expand-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f5f5;
  }
}

.expand-toggle img {
  width: 16px;
  height: 16px;
  transition: transform 0.3s ease;
}

.expand-toggle img.rotated {
  transform: rotate(180deg);
}

.toggle-text {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.toggle-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.toggle-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
}

/* === 展開詳細資訊 === */
.expanded-details {
  padding: 20px 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: slideDown 0.3s ease-out;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.detail-labels {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
  }
  to {
    opacity: 1;
    max-height: 200px;
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

/* === 表單區域 === */
.form-section {
  background: #f8f9fa;
  padding: 32px 24px;
  border-radius: 12px;
  margin-bottom: 32px;
}

.form-header {
  margin-bottom: 32px;
}

.form-title {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 20px;
  font-weight: 500;
  line-height: 150%;
  margin: 0 0 8px 0;
}

.form-subtitle {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 140%;
  margin: 0 0 16px 0;
}

.form-description {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 150%;
  margin: 0;
}

.identity-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-label {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label-zh {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.label-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.field-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #ffffff;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
  
  &::placeholder {
    color: #999999;
  }
  
  &:focus {
    border-color: #0044ad;
    box-shadow: 0 0 0 2px rgba(0, 68, 173, 0.1);
  }
  
  &.error {
    border-color: #dc3545;
    
    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
    }
  }
  
  &[readonly] {
    background: #f8f9fa;
    color: #666666;
    cursor: not-allowed;
  }
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #ffffff;
  padding: 12px 16px;
  transition: border-color 0.2s, box-shadow 0.2s;
  
  &:focus-within {
    border-color: #0044ad;
    box-shadow: 0 0 0 2px rgba(0, 68, 173, 0.1);
  }
  
  &.error {
    border-color: #dc3545;
    
    &:focus-within {
      border-color: #dc3545;
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
    }
  }
}

.date-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  font-weight: 400;
}

.date-icon {
  width: 20px;
  height: 20px;
  margin-left: 12px;
  flex-shrink: 0;
}

.bank-code-input {
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #ffffff;
  overflow: hidden;
  transition: border-color 0.2s, box-shadow 0.2s;
  
  &:focus-within {
    border-color: #0044ad;
    box-shadow: 0 0 0 2px rgba(0, 68, 173, 0.1);
  }
  
  &.error {
    border-color: #dc3545;
    
    &:focus-within {
      border-color: #dc3545;
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
    }
  }
}

.bank-code-prefix {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-right: 1px solid #e8e8e8;
}

.code-number {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.divider {
  width: 1px;
  height: 20px;
  background: #e8e8e8;
  margin: 0 8px;
}

.branch-code-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  background: transparent;
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 400;
  
  &::placeholder {
    color: #999999;
  }
}

.account-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #ffffff;
  overflow: hidden;
  transition: border-color 0.2s, box-shadow 0.2s;
  
  &:focus-within {
    border-color: #0044ad;
    box-shadow: 0 0 0 2px rgba(0, 68, 173, 0.1);
  }
  
  &.error {
    border-color: #dc3545;
    
    &:focus-within {
      border-color: #dc3545;
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
    }
  }
}

.account-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  background: transparent;
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 400;
  
  &::placeholder {
    color: #999999;
  }
}

.account-suffix {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-left: 1px solid #e8e8e8;
}

.suffix-number {
  color: #041c43;
  font-family: "Montserrat", sans-serif;
  font-size: 16px;
  font-weight: 500;
}

.field-note {
  color: #666666;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  margin-top: 4px;
}

.checkbox-field {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-top: 16px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.checkbox-input {
  width: 20px;
  height: 20px;
  border: 2px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  appearance: none;
  
  &:checked {
    background: #0044ad;
    border-color: #0044ad;
    
    &::after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: white;
      font-size: 12px;
      font-weight: bold;
    }
  }
}

.checkbox-label {
  cursor: pointer;
}

.checkbox-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.checkbox-text {
  color: #041c43;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 14px;
  font-weight: 400;
}

.checkbox-text-en {
  color: #666666;
  font-family: "Montserrat", sans-serif;
  font-size: 12px;
  font-weight: 400;
}

/* === 錯誤訊息樣式 === */
.error-message {
  color: #dc3545;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  margin-top: 4px;
  padding-left: 4px;
}

/* === 操作按鈕 === */
.action-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.btn-primary {
  min-width: 200px;
  height: 56px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  padding: 0 32px;
  background: #0044ad;
}

.btn-next {
  width: 100%;
}

.btn-primary:hover:not(:disabled) {
  background: #003390;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 68, 173, 0.3);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 68, 173, 0.3);
}

.btn-primary:disabled {
  background: #e2e2e2;
  cursor: not-allowed;
  
  .button-text-zh,
  .button-text-en {
    color: #a6a6a6;
  }
}

.button-text-zh {
  color: #ffffff;
  font-family: "Noto Sans TC", sans-serif;
  font-size: 16px;
  line-height: 100%;
  font-weight: 500;
  margin-right: 8px;
}

.button-text-en {
  color: #ffffff;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  line-height: 100%;
  font-weight: 400;
}

/* === 響應式設計 === */

/* 平板和小螢幕桌機 */
@media (min-width: 768px) {
  .content-container {
    max-width: 600px;
  }
  
  .content-section {
    padding: 48px;
  }
  
  .main-title {
    font-size: 26px;
  }
  
  .subtitle {
    font-size: 15px;
  }
  
  .step-current {
    font-size: 20px;
  }
  
     .remittance-info-section {
     padding: 32px;
   }
   
   .expanded-details {
     padding: 24px 0;
     gap: 20px;
   }
   
   .detail-row {
     gap: 24px;
   }
  
  .form-section {
    padding: 40px 32px;
  }
  
  .form-title {
    font-size: 24px;
  }
  
  .form-subtitle {
    font-size: 16px;
  }
  
  .label-zh {
    font-size: 18px;
  }
  
  .label-en {
    font-size: 16px;
  }
  
  .text-input,
  .date-input,
  .branch-code-input,
  .account-input {
    font-size: 18px;
    padding: 16px 20px;
  }
  
  .date-input-wrapper,
  .bank-code-input,
  .account-input-wrapper {
    padding: 16px 20px;
  }
  
  .bank-code-prefix,
  .account-suffix {
    padding: 16px 20px;
  }
  
  .checkbox-input {
    width: 24px;
    height: 24px;
    
    &:checked::after {
      font-size: 14px;
    }
  }
  
  .button-text-zh {
    font-size: 18px;
  }
  
  .button-text-en {
    font-size: 16px;
  }
}

/* 桌機版本 */
@media (min-width: 1024px) {
  .main-content-wrapper {
    padding: 0 20px 40px;
  }
  
  .content-container {
    width: 66.666%;
    min-width: 600px;
    max-width: 800px;
  }
  
  .content-section {
    padding: 60px;
  }
  
  .form-section {
    padding: 48px 40px;
  }
}

/* 大螢幕桌機 */
@media (min-width: 1440px) {
  .content-container {
    max-width: 900px;
  }
  
  .main-content-wrapper {
    padding: 0 40px 60px;
  }
}

/* 手機版調整 */
@media (max-width: 767px) {
  .main-content-wrapper {
    padding: 0 15px 15px;
  }
  
  .content-container {
    max-width: 100%;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
  }
  
  .content-section {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 20px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .step-current {
    font-size: 16px;
  }
  
  .step-total {
    font-size: 14px;
  }
  
     .remittance-info-section {
     padding: 20px;
   }
   
   .expanded-details {
     padding: 16px 0;
     gap: 12px;
   }
   
   .detail-row {
     gap: 16px;
     flex-direction: column;
   }
   
   .detail-value {
     text-align: left;
     margin-top: 8px;
   }
  
  .form-section {
    padding: 24px 20px;
  }
  
  .form-title {
    font-size: 18px;
  }
  
  .form-subtitle {
    font-size: 12px;
  }
  
  .label-zh {
    font-size: 14px;
  }
  
  .label-en {
    font-size: 12px;
  }
  
  .text-input,
  .date-input,
  .branch-code-input,
  .account-input {
    font-size: 14px;
  }
  
  .btn-primary {
    height: 48px;
    min-width: 100%;
  }
  
  .button-text-zh,
  .button-text-en {
    font-size: 14px;
  }
}