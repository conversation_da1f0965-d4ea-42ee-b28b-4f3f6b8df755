import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IbrSharedModule } from '../../../../@core/shared-2/ibr-shared.module';
import { IbrStateService, ApplicationStatus } from '../../../../@core/shared-2/services/ibr-state.service';
import { SharedTestDataService, UnifiedTestData } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 自然人申請完成頁面組件 (Step 5)
 * 
 * === 頁面功能說明 ===
 * 此頁面顯示申請完成狀態、交易摘要、通知設定等
 * 提供後續操作引導，如查詢記錄、客服聯繫等
 * 
 * === 前後端整合驗證 (已完成驗證) ===
 * ✅ 前端功能結構完整:
 *    - 交易摘要: confirmationData(匯款、受款人、金額)
 *    - 通知設定: sms/email通知偏好, emailAddress驗證
 *    - 最終確認: finalConfirmed 多重驗證機制
 *    - 表單驗證: email格式驗證、條件式必填驗證
 *    - 完成流程: 提交申請和狀態更新
 * 
 * 🔶 後端API狀態分析:
 *    - 需要新增申請提交相關API:
 *      * POST /api/ibr/individual/application/submit - 最終提交申請
 *      * GET /api/ibr/individual/application/{id}/status - 申請狀態查詢
 *      * POST /api/ibr/individual/notification/preferences - 通知偏好設定
 *    - 與管理員API呼叫對應的用戶版本API
 * 
 * ✅ 資料模型對應:
 *    - confirmationData 與後端 IndividualRemittance 實體完整對應
 *    - notificationSettings 模型清晰，適合後端處理
 *    - 提交流程與後端狀態管理機制匹配
 * 
 * === 狀態管理 ===
 * ✅ 更新申請狀態: COMPLETED
 * ✅ 最終提交驗證與錯誤處理
 * ✅ 導航: 完成流程或查詢記錄
 * 
 * === 導航流程 ===
 * 申請完成頁 (Step 5) → 結束流程或查詢記錄
 * 
 * 對應UI設計: 11.jpg
 */
@Component({
  selector: 'app-i-transaction-confirmation',
  standalone: true,
  imports: [CommonModule, FormsModule, IbrSharedModule],
  templateUrl: './i-transaction-confirmation.component.html',
  styleUrls: ['./i-transaction-confirmation.component.scss']
})
export class ITransactionConfirmationComponent implements OnInit {
  currentStatus: ApplicationStatus = ApplicationStatus.COMPLETED;
  
  // 開發模式標記
  isDevelopment = true;
  
  // 確認資料
  confirmationData = {
    // 匯款資訊
    currency: 'USD',
    amount: 2500.00,
    exchangeRate: '32.35',
    twdAmount: 80875,
    
    // 受款人資訊
    beneficiaryName: '王小明',
    beneficiaryId: 'A123***789',
    beneficiaryPhone: '0912-345-678',
    beneficiaryBank: '凱基銀行 (809)',
    beneficiaryAccount: '****-****-****-1234'
  };
  
  // 手續費
  feeAmount = 200;
  
  // 實收金額
  get netAmount(): number {
    return this.confirmationData.twdAmount - this.feeAmount;
  }
  
  // 通知設定
  notificationSettings = {
    sms: true,
    email: false,
    emailAddress: ''
  };
  
  // Email 錯誤訊息
  emailError = '';
  
  // 最終確認
  finalConfirmed = false;
  
  // 新增的屬性用於模板
  senderName = 'Shiang Ru';
  senderBank = 'WELLS FARGO BANK, N.A.';
  transactionNumber = 'DAHRI2329524026-036';
  remittanceFee = 30;
  remittanceNature = '410 薪資款匯入';
  recipientEnglishName = 'Qing Lan, Wang';
  contactEmail = '<EMAIL>';
  contactPhone = '0912-345-678';
  
  // 是否可以提交
  get canSubmit(): boolean {
    // 必須勾選最終確認
    if (!this.finalConfirmed) {
      return false;
    }
    
    // 如果選擇 email 通知，必須填寫有效的 email
    if (this.notificationSettings.email) {
      return this.notificationSettings.emailAddress !== '' && !this.emailError;
    }
    
    return true;
  }

  constructor(
    private router: Router,
    private stateService: IbrStateService,
    private sharedTestDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    this.initializePage();
    this.loadTestData();
  }

  /**
   * 初始化頁面
   */
  private initializePage(): void {
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.COMPLETED,
      stepTitle: '申請完成',
      currentStep: 3
    });
  }

  /**
   * 載入測試資料
   */
  private loadTestData(): void {
    // 從 SharedTestDataService 載入測試資料
    let testData = this.sharedTestDataService.getCurrentTestData();
    if (!testData) {
      testData = this.sharedTestDataService.getDefaultTestData();
      this.sharedTestDataService.setTestData(testData);
    }

    // 更新確認資料
    const amount = parseFloat(testData.Amount);
    const exchangeRate = 32.35; // 模擬匯率
    
    this.confirmationData = {
      currency: testData.Currency,
      amount: amount,
      exchangeRate: exchangeRate.toString(),
      twdAmount: Math.round(amount * exchangeRate),
      
      // 受款人資訊
      beneficiaryName: testData.PayeeName,
      beneficiaryId: this.maskIdNumber(testData.PayeeID),
      beneficiaryPhone: this.formatPhoneNumber(testData.PayeeTel),
      beneficiaryBank: this.getBankName(testData.PayeeBankCode),
      beneficiaryAccount: this.maskAccountNumber(testData.PayeeAccount)
    };
    
    // 更新其他屬性
    this.senderName = testData.PayerName;
    this.senderBank = this.getRemitterBankName(testData.PayerCountry);
    this.transactionNumber = testData.RemitRefNo;
    this.recipientEnglishName = testData.PayeeEngName;
    this.contactEmail = testData.PayeeMail;
    
    // 根據匯款性質設定
    const sourceOfFundMap: { [key: string]: string } = {
      '410': '410 薪資款匯入',
      '001': '001 貨物貿易',
      '002': '002 服務貿易', 
      '320': '320 投資收益'
    };
    this.remittanceNature = sourceOfFundMap[testData.SourceOfFund] || '410 薪資款匯入';
    
    console.log('已載入統一測試資料到申請完成頁面:', {
      currency: this.confirmationData.currency,
      amount: this.confirmationData.amount,
      beneficiaryName: this.confirmationData.beneficiaryName,
      beneficiaryBank: this.confirmationData.beneficiaryBank,
      senderName: this.senderName,
      transactionNumber: this.transactionNumber
    });
  }

  /**
   * 取得銀行名稱
   */
  private getBankName(bankCode: string): string {
    const bankCodePrefix = bankCode ? bankCode.substring(0, 3) : '';
    const bankMap: { [key: string]: string } = {
      '008': '華南商業銀行 (008)',
      '809': '凱基銀行 (809)',
      '822': '中國信託商業銀行 (822)'
    };
    return bankMap[bankCodePrefix] || '凱基銀行 (809)';
  }

  /**
   * 遮罩身分證號
   */
  private maskIdNumber(idNumber: string): string {
    if (!idNumber || idNumber.length < 10) return idNumber;
    return `${idNumber.substring(0, 4)}***${idNumber.substring(7)}`;
  }

  /**
   * 遮罩帳號
   */
  private maskAccountNumber(account: string): string {
    if (!account || account.length < 8) return account;
    const lastFour = account.slice(-4);
    const maskedPart = '****-****-****-';
    return maskedPart + lastFour;
  }

  /**
   * 格式化電話號碼
   */
  private formatPhoneNumber(phone: string): string {
    if (!phone || phone.length !== 10) return phone;
    return `${phone.substring(0, 4)}-${phone.substring(4, 7)}-${phone.substring(7)}`;
  }
  
  /**
   * 取得匯款人銀行名稱
   */
  private getRemitterBankName(country: string): string {
    const bankMap: { [key: string]: string } = {
      'US': 'WELLS FARGO BANK, N.A.',
      'UK': 'HSBC BANK PLC', 
      'JP': 'BANK OF TOKYO-MITSUBISHI UFJ',
      'SG': 'DBS BANK LTD'
    };
    return bankMap[country] || 'OVERSEAS BANK';
  }

  /**
   * Email 開關切換處理
   */
  onEmailToggle(): void {
    if (!this.notificationSettings.email) {
      this.notificationSettings.emailAddress = '';
      this.emailError = '';
    }
  }

  /**
   * 驗證 Email
   */
  validateEmail(): void {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!this.notificationSettings.emailAddress) {
      this.emailError = '';
    } else if (!emailPattern.test(this.notificationSettings.emailAddress)) {
      this.emailError = '請輸入正確的電子郵件格式';
    } else {
      this.emailError = '';
    }
  }

  /**
   * 最終確認勾選處理
   */
  onFinalConfirmation(): void {
    // 可以在這裡加入額外的驗證邏輯
    console.log('最終確認狀態:', this.finalConfirmed);
  }

  /**
   * 確認送出
   */
  async proceedToNext(): Promise<void> {
    if (!this.canSubmit) {
      this.showValidationError();
      return;
    }

    try {
      // 模擬提交過程
      console.log('提交申請資料...');
      await this.simulateSubmission();
      
      // 更新狀態
      this.stateService.updateApplicationStatus({
        status: ApplicationStatus.COMPLETED,
        stepTitle: '申請完成',
        currentStep: 3
      });
      
      // 導航到交易確認頁面
      this.router.navigate(['/ibr/individual/transaction-confirmation']);
      
    } catch (error) {
      alert('提交失敗，請稍後再試');
    }
  }

  /**
   * 模擬提交過程
   */
  private simulateSubmission(): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 95% 成功率的模擬
        if (Math.random() > 0.05) {
          resolve();
        } else {
          reject(new Error('模擬提交失敗'));
        }
      }, 2000);
    });
  }

  /**
   * 顯示驗證錯誤
   */
  private showValidationError(): void {
    let errorMessage = '請完成以下項目：\n\n';
    
    if (!this.finalConfirmed) {
      errorMessage += '• 請勾選「我已確認以上資料正確無誤」\n';
    }
    if (this.notificationSettings.email && this.emailError) {
      errorMessage += '• 請輸入正確的電子郵件地址\n';
    }
    
    alert(errorMessage);
  }

  /**
   * 開啟客服服務
   */
  openCustomerService(): void {
    console.log('開啟客服視窗');
    alert('客服服務\n\n（模擬功能）\n\n如需協助請撥打客服專線：\n0800-588-111');
  }

  /**
   * 返回上一頁
   */
  goBack(): void {
    this.router.navigate(['/ibr/individual/remittance-detail']);
  }

  /**
   * 前往凱基銀行官網
   */
  goToKgiWebsite(): void {
    console.log('前往凱基銀行官網');
    window.open('https://www.kgibank.com.tw', '_blank');
  }

  /**
   * 查詢解款紀錄
   */
  checkRemittanceHistory(): void {
    console.log('查詢解款紀錄');
    this.router.navigate(['/ibr/individual/remittance-history']);
  }
}
