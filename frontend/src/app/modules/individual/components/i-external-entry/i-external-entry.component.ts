import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IbrStateService } from '../../../../@core/shared-2/services/ibr-state.service';
import { SharedTestDataService, RemittanceDisplayInfo } from '../../../../@core/shared-2/services/shared-test-data.service';

/**
 * 自然人外部入口頁面組件 (Step 0)
 * 
 * === 頁面功能說明 ===
 * 此頁面是外部系統調用API後，顯示給用戶的第一個畫面
 * 用戶點擊「線上解款平台」按鈕後，會進入自然人的Step 1 (landing)頁面
 * 
 * === 前後端整合驗證 ===
 * ✅ 前端模型: remittanceData (匯款資料)
 *    - senderName: string (匯款人姓名)
 *    - senderCountry: string (匯款國家)
 *    - currency: string (幣別)
 *    - amount: number (金額)
 *    - twdAmount: number (台幣金額)
 *    - notificationDate: string (通知日期)
 * 
 * ⚠️ 後端API對應: 需要創建外部通知接收API
 *    建議API: POST /api/ibr/notifications/inbound-remittance
 *    回應格式應與前端 remittanceData 格式一致
 * 
 * === 狀態管理 ===
 * ✅ 使用 IbrStateService 管理申請狀態
 * ✅ 調用 startNewApplication('individual') 初始化個人申請
 * ✅ 將匯款資料存入狀態服務: updateApplicationData({ remittanceInfo })
 * 
 * === 導航流程 ===
 * 外部入口頁 (Step 0) → 條款同意頁 (Step 1): /ibr/individual/landing
 * 
 * 對應UI設計: 00.jpg
 */
@Component({
  selector: 'app-i-external-entry',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="email-notification-container">
      <!-- Email 通知內容 -->
      <div class="email-content">
        <!-- Header -->
        <div class="email-header">
          <div class="bank-logo-section">
            <img src="/assets/image/icon/kgi_icon.svg" alt="凱基銀行" class="kgi-logo">
            <span class="bank-name">凱基銀行</span>
            <span class="kgi-bank-eng">KGI BANK</span>
          </div>
          <div class="header-banner"></div>
        </div>

        <!-- 標題區 -->
        <div class="notification-title-section">
          <h1 class="chinese-title">匯入匯款通知</h1>
          <h2 class="english-title">Transaction Notification of Release<br>FCY Inward Remittance</h2>
        </div>

        <!-- 主要內容 -->
        <div class="notification-body">
          <div class="greeting">
            <p>親愛的客戶，您好</p>
            <p class="english">Dear Customer,</p>
          </div>

          <div class="notification-message">
            <p class="chinese-text">
              通知您有一筆國外匯入匯款，交易編號2401382-712，請於營業<br>
              時間 9:00-XX:XX 進入線上解款平台查看。
            </p>
            <p class="english-text">
              You have a foreign currency inward remittance. Please click the<br>
              link below during business hours 9:00-XX:XX to check the<br>
              remittance.
            </p>
          </div>

          <div class="link-instruction">
            <p class="chinese-text">請至以下連結進行匯入匯款收款流程：</p>
            <p class="english-text">Please click the link below:</p>
          </div>

          <!-- 線上解款平台連結 -->
          <div class="platform-link">
            <a [href]="getPlatformLink()" class="link-text">
              線上解款平台連結 Online Remittance Release Platform
            </a>
          </div>

          <div class="inquiry-section">
            <p class="chinese-text">若要查詢過往交易資料，請至以下連結查詢解款紀錄：</p>
            <p class="english-text">To find past transaction details, please click the link below;</p>
          </div>

          <!-- 解款紀錄查詢連結 -->
          <div class="inquiry-link">
            <a [href]="getInquiryLink()" class="link-text">
              解款紀錄查詢連結 Inward Remittance Inquiry
            </a>
          </div>

          <div class="contact-info">
            <p>*若有疑感與建議，請致電客服專線 (02)8023-9088，由專人為您<br>
            服務</p>
            <p class="english">*If you have any questions, please call our service hotline<br>
            (02)8023-9088 or click contact me.</p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="email-footer">
        <div class="footer-links">
          <a href="#">服務據點資訊</a>
          <a href="#">推薦商品</a>
          <a href="#" class="phone-link">
            <span class="phone-icon">📞</span>(02)8023-9088
          </a>
          <a href="#">聯絡我們</a>
        </div>
        <div class="social-media">
          <a href="#" class="social-icon">f</a>
          <a href="#" class="social-icon">📷</a>
          <a href="#" class="social-icon">▶</a>
          <a href="#" class="social-icon">▶</a>
        </div>
        <div class="copyright-section">
          <p class="copyright">凱基證券股份有限公司2023 by KGI Bank Co.,Ltd.</p>
          <p class="disclaimer">本網站內容享有著作權，禁止侵害，違者必究。</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .email-notification-container {
      max-width: 650px;
      margin: 0 auto;
      background: white;
      font-family: 'Noto Sans TC', 'Microsoft JhengHei', sans-serif;
    }

    .email-content {
      position: relative;
      background: white;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }

    /* Header Styles */
    .email-header {
      position: relative;
      background: white;
      overflow: hidden;
    }

    .bank-logo-section {
      display: flex;
      align-items: center;
      padding: 20px 40px;
      gap: 10px;
    }

    .kgi-logo {
      width: 40px;
      height: 40px;
    }

    .bank-name {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .kgi-bank-eng {
      font-size: 14px;
      color: #666;
      margin-left: 5px;
    }

    .header-banner {
      height: 80px;
      background: linear-gradient(135deg, #2c5aa0 0%, #f47920 100%);
      position: relative;
      overflow: hidden;
    }

    .header-banner::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -10%;
      width: 300px;
      height: 300px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
    }

    /* 標題區 */
    .notification-title-section {
      text-align: center;
      padding: 30px 40px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
    }

    .chinese-title {
      font-size: 28px;
      font-weight: 600;
      color: #2c5aa0;
      margin: 0 0 10px 0;
      letter-spacing: 2px;
    }

    .english-title {
      font-size: 20px;
      font-weight: 500;
      color: #333;
      margin: 0;
      line-height: 1.4;
    }

    /* 主要內容 */
    .notification-body {
      padding: 40px;
      background: white;
    }

    .greeting {
      margin-bottom: 30px;
    }

    .greeting p {
      margin: 5px 0;
      font-size: 16px;
      color: #333;
    }

    .greeting .english {
      color: #666;
      font-size: 14px;
    }

    .notification-message {
      background: #f8f9fa;
      border-left: 4px solid #2c5aa0;
      padding: 20px;
      margin-bottom: 30px;
    }

    .notification-message p {
      margin: 0 0 15px 0;
      line-height: 1.8;
    }

    .chinese-text {
      font-size: 15px;
      color: #333;
    }

    .english-text {
      font-size: 14px;
      color: #666;
      margin-top: 10px;
    }

    .link-instruction {
      margin: 30px 0 20px 0;
    }

    .link-instruction p {
      margin: 5px 0;
      font-size: 15px;
    }

    /* 連結樣式 */
    .platform-link,
    .inquiry-link {
      margin: 20px 0;
      padding: 15px;
      background: #f0f6ff;
      border-radius: 8px;
      text-align: center;
    }

    .link-text {
      color: #2c5aa0;
      font-size: 16px;
      font-weight: 500;
      text-decoration: underline;
      cursor: pointer;
      display: inline-block;
      transition: all 0.3s ease;
    }

    .link-text:hover {
      color: #1e3d6f;
      text-decoration: none;
      transform: translateY(-1px);
    }

    .inquiry-section {
      margin-top: 40px;
      padding-top: 30px;
      border-top: 1px solid #e9ecef;
    }

    .inquiry-section p {
      margin: 5px 0;
      font-size: 15px;
    }

    .contact-info {
      margin-top: 40px;
      padding: 20px;
      background: #fff9e6;
      border-radius: 8px;
      border: 1px solid #ffd666;
    }

    .contact-info p {
      margin: 5px 0;
      font-size: 14px;
      line-height: 1.8;
      color: #666;
    }

    /* 文字內容僅示意按鈕 */
    .content-toggle-button {
      position: absolute;
      top: 20px;
      right: 20px;
      background: #2c5aa0;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(44, 90, 160, 0.3);
    }

    /* Footer */
    .email-footer {
      background: #2c5aa0;
      color: white;
      padding: 30px 40px;
      text-align: center;
    }

    .footer-links {
      display: flex;
      justify-content: center;
      gap: 30px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .footer-links a {
      color: white;
      text-decoration: none;
      font-size: 14px;
      transition: opacity 0.3s;
    }

    .footer-links a:hover {
      opacity: 0.8;
    }

    .phone-link {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .phone-icon {
      font-size: 16px;
    }

    .social-media {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 20px 0;
    }

    .social-icon {
      width: 35px;
      height: 35px;
      background: white;
      color: #2c5aa0;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      font-size: 18px;
      transition: all 0.3s;
    }

    .social-icon:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }

    .copyright-section {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .copyright,
    .disclaimer {
      margin: 5px 0;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.9);
    }

    /* 響應式設計 */
    @media (max-width: 768px) {
      .email-content {
        margin: 0;
      }

      .notification-body {
        padding: 20px;
      }

      .footer-links {
        flex-direction: column;
        gap: 15px;
      }

      .chinese-title {
        font-size: 24px;
      }

      .english-title {
        font-size: 18px;
      }

      .content-toggle-button {
        position: static;
        margin: 20px auto;
        display: block;
        width: fit-content;
      }
    }


  `]
})
export class IExternalEntryComponent implements OnInit {
  // 從 URL 參數取得的案件資訊
  caseNo: string = '';
  token: string = '';
  
  // 匯款資訊（從統一測試資料取得）
  remittanceInfo: RemittanceDisplayInfo | null = null;

  constructor(
    private router: Router,
    private stateService: IbrStateService,
    private sharedTestDataService: SharedTestDataService
  ) {}

  ngOnInit(): void {
    // 從 URL 查詢參數取得案件編號和 token
    const urlParams = new URLSearchParams(window.location.search);
    this.caseNo = urlParams.get('caseNo') || 'IBR2401382-712';
    this.token = urlParams.get('token') || '';
    
    // 載入統一測試資料
    this.loadTestData();
  }
  
  /**
   * 載入統一測試資料
   */
  private loadTestData(): void {
    // 檢查是否有統一測試資料
    let testData = this.sharedTestDataService.getCurrentTestData();
    if (!testData) {
      // 如果沒有測試資料，不要使用預設資料，而是顯示錯誤
      console.error('沒有找到測試資料！請從 test-notification 頁面進入');
      alert('錯誤：沒有找到測試資料！請從測試通知頁面進入。');
      this.router.navigate(['/ibr/test/test-notification']);
      return;
    }
    
    // 載入匯款資訊
    this.remittanceInfo = this.sharedTestDataService.toRemittanceDisplayInfo(testData);
    
    // 更新案件編號為測試資料中的值
    if (testData.RemitRefNo) {
      this.caseNo = testData.RemitRefNo;
    }
    
    console.log('External Entry 已載入統一測試資料:', {
      caseNo: this.caseNo,
      payeeID: testData.PayeeID,
      payeeIDLength: testData.PayeeID ? testData.PayeeID.length : 'null',
      whileFlag: testData.WhileFlag,
      remittanceInfo: this.remittanceInfo,
      fullTestData: testData
    });
  }

  /**
   * 取得線上解款平台連結
   * 根據 PayeeID 判斷資料類型，返回對應的 URL
   */
  getPlatformLink(): string {
    // 取得當前測試資料
    const testData = this.sharedTestDataService.getCurrentTestData();
    
    if (!testData) {
      console.error('無法取得測試資料');
      return '#';
    }
    
    // 除錯：顯示判斷資訊
    console.log('=== 判斷資料類型 (getPlatformLink) ===');
    console.log('PayeeID:', testData.PayeeID);
    console.log('PayeeID 長度:', testData.PayeeID ? testData.PayeeID.length : 'null');
    console.log('WhileFlag:', testData.WhileFlag);
    
    // 建立基本查詢參數
    const baseUrl = window.location.origin;
    const queryParams = new URLSearchParams({
      remitrefno: testData.RemitRefNo || '',
      theirrefno: testData.TheirRefNo || '',
      source: 'email'
    });
    
    // 根據 PayeeID 判斷資料類型
    if (testData.WhileFlag === 'S') {
      // 補件通知
      console.log('判斷結果: 補件流程');
      queryParams.append('type', 'supplement');
      return `${baseUrl}/ibr/supplement/notification?${queryParams.toString()}`;
    } else if (testData.PayeeID && testData.PayeeID.length === 8) {
      // 法人通知 (統一編號8碼)
      console.log('判斷結果: 法人流程');
      queryParams.append('type', 'corporate');
      return `${baseUrl}/ibr/corporate/landing?${queryParams.toString()}`;
    } else if (testData.PayeeID && testData.PayeeID.length === 10) {
      // 個人通知 (身分證號10碼)
      console.log('判斷結果: 個人流程');
      queryParams.append('type', 'individual');
      return `${baseUrl}/ibr/individual/landing?${queryParams.toString()}`;
    } else {
      // 預設為個人流程
      console.log('判斷結果: 預設個人流程 (PayeeID長度異常)');
      queryParams.append('type', 'individual');
      return `${baseUrl}/ibr/individual/landing?${queryParams.toString()}`;
    }
  }

  /**
   * 取得解款紀錄查詢連結
   */
  getInquiryLink(): string {
    const baseUrl = window.location.origin;
    const queryParams = new URLSearchParams({
      type: 'Q',
      case: this.caseNo || ''
    });
    return `${baseUrl}/ibr/query?${queryParams.toString()}`;
  }
}
