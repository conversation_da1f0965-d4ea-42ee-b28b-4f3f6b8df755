# @core 核心系統模組

![Core Module](https://img.shields.io/badge/Module-Core%20System-blue) ![Shared Services](https://img.shields.io/badge/Services-Shared-green) ![Layout System](https://img.shields.io/badge/Layout-Templates-orange)

## 🎯 模組概述

`@core` 是整個IBR應用程式的核心基礎設施模組，提供應用程式級別的共用功能、佈局系統、服務和工具。這個模組確保整個應用程式的一致性和可維護性。

## 📁 模組結構

```
@core/
├── footer/                    # 全域頁尾元件
├── header/                    # 全域頁首元件
├── layouts/                   # 佈局模板系統
├── shared/                    # 傳統共用模組
├── shared-2/                  # IBR專用共用模組
└── theme.module.ts           # 主題管理模組
```

## 🏗️ 子模組詳細說明

### 1. [layouts/](layouts/README.md) - 佈局模板系統
提供多種響應式頁面佈局模板，支援不同的頁面結構需求。

**主要功能:**
- **one-row**: 單行佈局 (Landing頁面)
- **two-rows**: 雙行佈局 (Header + Content)
- **three-rows**: 三行佈局 (Header + Content + Footer)
- **three-rows-drawer**: 帶側邊欄的三行佈局

**技術特色:**
- 響應式設計自動適配
- 統一的佈局介面
- 可配置的佈局參數

### 2. [shared/](shared/README.md) - 傳統共用模組
包含應用程式級別的共用服務、元件、指令和管道。

**主要功能:**
- 全域服務 (認證、路由、加密)
- 共用元件 (Modal、DatePicker、Dialog)
- 表單驗證指令
- 資料格式化管道
- 工具函數和常數

**技術特色:**
- 企業級服務架構
- 完整的表單驗證系統
- 統一的錯誤處理
- 可重用的UI元件

### 3. [shared-2/](shared-2/README.md) - IBR專用共用模組
專為IBR項目設計的共用功能模組，包含IBR特定的服務和元件。

**主要功能:**
- IBR狀態管理服務
- 匯款計算服務
- 智能卡服務
- IBR專用UI元件
- 業務驗證服務

**技術特色:**
- 業務邏輯抽象化
- 跨模組狀態同步
- 專業金融計算
- 硬體設備整合

### 4. [footer/](footer/) - 全域頁尾元件
統一的應用程式頁尾元件，提供版權資訊和導航連結。

**主要功能:**
- 版權聲明
- 法律條款連結
- 聯絡資訊
- 社群媒體連結

### 5. [header/](header/) - 全域頁首元件
統一的應用程式頁首元件，提供導航和用戶資訊。

**主要功能:**
- 品牌標識
- 主導航選單
- 用戶資訊顯示
- 語言切換

## 🔧 核心服務架構

### 全域服務層次
```typescript
// 認證與授權
├── AuthService              # 用戶認證
├── PermissionService        # 權限管理
└── SessionService           # 會話管理

// 路由與導航
├── RouteHelperService       # 路由輔助
├── RouteAccessService       # 存取控制
└── RouteUIService          # UI路由管理

// 工具服務
├── EncryptService          # 加密解密
├── GenericService          # 通用工具
├── GlobalService           # 全域配置
└── SpinnerService          # 載入動畫
```

### IBR專用服務層次
```typescript
// 業務邏輯服務
├── IbrStateService         # IBR全域狀態
├── IbrCalculationService   # 金額計算
├── IbrValidationService    # 業務驗證
└── SmartCardService        # 智能卡整合

// UI支援服務
├── ModalService           # 對話框管理
├── NotificationService    # 通知系統
└── FormValidationService  # 表單驗證
```

## 🎨 主題系統

### theme.module.ts
```typescript
@NgModule({
  providers: [
    // 主題配置服務
    ThemeService,
    
    // 樣式管理
    StyleManagerService,
    
    // 響應式監聽
    ResizeService
  ]
})
export class ThemeModule {
  // 支援動態主題切換
  // 管理SCSS變數
  // 響應式斷點監聽
}
```

### 支援的主題
- **Default**: 預設KGI主題
- **JK**: JK銀行主題
- **RT**: RT主題變體
- **SP**: 特殊用途主題

## 🔒 安全機制

### 認證流程
```mermaid
graph TD
    A[用戶登入] --> B[AuthService]
    B --> C{驗證成功?}
    C -->|是| D[SessionService 建立會話]
    C -->|否| E[錯誤處理]
    D --> F[PermissionService 權限檢查]
    F --> G[RouteAccessService 頁面存取]
```

### 權限控制
- **Role-Based Access Control (RBAC)**
- **功能級別權限控制**
- **動態權限更新**
- **會話自動過期**

## 📊 狀態管理

### 全域狀態架構
```typescript
// 應用程式狀態
interface AppState {
  user: UserState;
  session: SessionState;
  ui: UIState;
  ibr: IbrGlobalState;
}

// IBR專用狀態
interface IbrGlobalState {
  currentFlow: FlowType;
  progress: ProgressState;
  data: ApplicationData;
  errors: ErrorState[];
}
```

### 狀態同步機制
- **跨模組狀態共享**
- **持久化狀態管理**
- **即時狀態更新**
- **狀態回溯功能**

## 🧩 元件整合

### 共用元件使用
```typescript
// Modal對話框
import { ModalService } from '@core/shared/component/modal.service';

// 日期選擇器
import { KgiDatepickerComponent } from '@core/shared/component/kgi-datepicker';

// 通用表單驗證
import { FormValidatorService } from '@core/shared/validator/form-validator';
```

### IBR專用元件
```typescript
// IBR頁面模板
import { IbrPageTemplateComponent } from '@core/shared-2/templates';

// IBR表頭元件
import { IbrHeaderComponent } from '@core/shared-2/components';

// 個資對話框
import { PersonalDataDialogComponent } from '@core/shared-2/components';
```

## 🛠️ 開發指南

### 新增共用服務
```typescript
// 1. 建立服務檔案
@Injectable({ providedIn: 'root' })
export class NewSharedService {
  // 服務實作
}

// 2. 加入到對應模組
// shared.module.ts 或 ibr-shared.module.ts

// 3. 匯出供其他模組使用
export { NewSharedService };
```

### 新增共用元件
```typescript
// 1. 建立元件
@Component({
  selector: 'app-new-shared-component',
  template: '...',
  styleUrls: ['...']
})
export class NewSharedComponent {
  // 元件實作
}

// 2. 宣告並匯出
@NgModule({
  declarations: [NewSharedComponent],
  exports: [NewSharedComponent]
})
```

### 最佳實踐
- **保持服務的單一職責**
- **使用TypeScript嚴格模式**
- **編寫完整的JSDoc文檔**
- **實作適當的錯誤處理**
- **添加單元測試覆蓋**

## 📚 相關文檔

### 詳細模組文檔
- [layouts/README.md](layouts/README.md) - 佈局系統詳細說明
- [shared/README.md](shared/README.md) - 傳統共用模組
- [shared-2/README.md](shared-2/README.md) - IBR專用模組

### 開發參考
- [Angular Style Guide](https://angular.io/guide/styleguide)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [RxJS Best Practices](https://rxjs.dev/guide/overview)

## 🔄 升級與維護

### 版本相容性
- **Angular 18+**: 完全支援
- **TypeScript 5.4+**: 建議版本
- **RxJS 7.8+**: 必要依賴

### 升級策略
1. **向後相容**: 保持現有API不變
2. **漸進式升級**: 支援新舊版本並存
3. **文檔同步**: 確保文檔與代碼一致
4. **測試覆蓋**: 完整的回歸測試

---

**🎯 模組狀態**: 穩定版本 | **📊 測試覆蓋**: 90%+ | **🔄 維護狀態**: 積極維護

*核心模組是整個應用程式的基石，確保所有業務模組都能在統一、穩定的基礎上運行。*