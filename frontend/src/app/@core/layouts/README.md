# Layouts - 佈局模板系統

![Layout System](https://img.shields.io/badge/Layout-Templates-blue) ![Responsive](https://img.shields.io/badge/Responsive-Design-green) ![Kendo UI](https://img.shields.io/badge/Kendo-Drawer-orange) ![Components](https://img.shields.io/badge/Components-4-red)

## 🎯 模組概述

Layouts模組提供IBR應用程式的標準化頁面佈局模板系統。包含四種不同的佈局模式，支援響應式設計，確保所有頁面都有一致的視覺結構和用戶體驗。每個佈局模板都經過精心設計，適應不同的頁面需求和用戶場景。

## 📊 模組統計

- **佈局模板數**: 4個
- **響應式斷點**: 3個 (Desktop, Tablet, Mobile)
- **支援元件**: Header, Footer, Drawer
- **主題支援**: 4個主題完全相容

## 🏗️ 佈局結構

```
layouts/
├── index.ts                      # 模組匯出檔案
├── one-row/                      # 單行佈局
│   ├── one-row.layout.html       # HTML模板
│   ├── one-row.layout.scss       # 樣式檔案
│   └── one-row.layout.ts         # TypeScript元件
├── two-rows/                     # 雙行佈局
│   ├── two-rows.layout.html      # HTML模板
│   ├── two-rows.layout.scss      # 樣式檔案
│   └── two-rows.layout.ts        # TypeScript元件
├── three-rows/                   # 三行佈局
│   ├── three-rows.layout.html    # HTML模板
│   ├── three-rows.layout.scss    # 樣式檔案
│   └── three-rows.layout.ts      # TypeScript元件
└── three-rows-drawer/            # 三行側邊欄佈局
    ├── three-rows-drawer.layout.html  # HTML模板
    ├── three-rows-drawer.layout.scss  # 樣式檔案
    └── three-rows-drawer.layout.ts    # TypeScript元件
```

## 📱 佈局類型詳解

### 1. One-Row Layout - 單行佈局
**選擇器**: `<kgi-one-row-layout>`  
**用途**: 全屏展示頁面，如Landing頁面、歡迎頁面

**結構**:
```html
<div class="container">
    <router-outlet></router-outlet>
</div>
```

**特色**:
- 🎯 **極簡設計**: 純內容展示，無導航干擾
- 📱 **全響應式**: 完美適配各種螢幕尺寸
- ⚡ **載入快速**: 最少的DOM結構，最快的渲染速度
- 🎨 **自由度高**: 完全由內容元件控制版面

**適用場景**:
- 系統首頁/歡迎頁面
- 登入/註冊頁面
- 錯誤頁面 (404, 500)
- 獨立的功能頁面

### 2. Two-Rows Layout - 雙行佈局
**選擇器**: `<kgi-two-rows-layout>`  
**用途**: 帶標題列的簡潔頁面

**結構**:
```html
<div class="container">
    <kgi-header-component></kgi-header-component>
    <router-outlet></router-outlet>
</div>
```

**特色**:
- 🧭 **簡潔導航**: 提供基本的導航功能
- 📏 **空間最大化**: 最大化內容顯示區域
- 🔧 **靈活配置**: Header可根據需要配置
- 📱 **移動友善**: 移動設備下Header自動簡化

**適用場景**:
- 表單填寫頁面
- 資料展示頁面
- 簡單的操作介面
- 中間步驟頁面

### 3. Three-Rows Layout - 三行佈局
**選擇器**: `<kgi-three-rows-layout>`  
**用途**: 標準的應用程式頁面佈局

**結構**:
```html
<div>
    <kgi-header-component></kgi-header-component>
    <router-outlet></router-outlet>
    <kgi-footer></kgi-footer>
</div>
```

**特色**:
- 🏛️ **標準佈局**: 經典的Header-Content-Footer結構
- ℹ️ **資訊完整**: 提供完整的導航和資訊
- 🎨 **品牌一致**: 統一的品牌展示區域
- 📋 **資訊豐富**: Footer提供額外的連結和資訊

**適用場景**:
- 主要業務頁面
- 資訊展示頁面
- 完整的操作流程
- 需要Footer資訊的頁面

### 4. Three-Rows-Drawer Layout - 三行側邊欄佈局
**選擇器**: `<kgi-three-rows-drawer-layout>`  
**用途**: 複雜的管理介面和多功能頁面

**結構**:
```html
<div>
    <kgi-header-full-component></kgi-header-full-component>
    <kendo-drawer-container>
        <kendo-drawer>
            <!-- 側邊欄選單 -->
        </kendo-drawer>
        <kendo-drawer-content>
            <router-outlet></router-outlet>
        </kendo-drawer-content>
    </kendo-drawer-container>
    <kgi-footer></kgi-footer>
</div>
```

**特色**:
- 🗂️ **多級選單**: 支援多層級的導航結構
- 📱 **自適應**: 移動設備自動收納側邊欄
- ⚙️ **功能豐富**: 整合Kendo UI Drawer組件
- 🎛️ **管理友善**: 適合複雜的管理介面

**側邊欄功能**:
- **可收納**: 支援展開/收納切換
- **響應式**: 不同螢幕尺寸自適應
- **圖示支援**: 豐富的圖示系統
- **多層選單**: 支援樹狀結構選單

**適用場景**:
- 後台管理介面
- 複雜的業務系統
- 多功能整合頁面
- 需要豐富導航的應用

## 🎨 響應式設計

### 斷點設計
```scss
// 響應式斷點
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1200px;

// 移動設備 (< 768px)
@media (max-width: $breakpoint-mobile - 1px) {
  // 移動端樣式
}

// 平板設備 (768px - 1023px)
@media (min-width: $breakpoint-mobile) and (max-width: $breakpoint-tablet - 1px) {
  // 平板端樣式
}

// 桌面設備 (>= 1024px)
@media (min-width: $breakpoint-tablet) {
  // 桌面端樣式
}
```

### 適配策略
- **Mobile First**: 優先設計移動端體驗
- **漸進增強**: 大螢幕逐步增加功能
- **彈性佈局**: 使用Flexbox和Grid
- **自適應字型**: 響應式字型大小

## 🔧 使用指南

### 基本使用
```typescript
// 1. 在路由配置中指定佈局
{
  path: 'my-page',
  component: MyPageComponent,
  data: { 
    layout: 'three-rows' // 指定使用三行佈局
  }
}

// 2. 在元件中使用
@Component({
  template: `
    <kgi-three-rows-layout>
      <!-- 頁面內容 -->
    </kgi-three-rows-layout>
  `
})
export class MyPageComponent { }
```

### 佈局選擇指南
```mermaid
graph TD
    A[選擇佈局] --> B{需要導航?}
    B -->|不需要| C[One-Row Layout]
    B -->|需要| D{需要Footer?}
    D -->|不需要| E[Two-Rows Layout]
    D -->|需要| F{需要側邊欄?}
    F -->|不需要| G[Three-Rows Layout]
    F -->|需要| H[Three-Rows-Drawer Layout]
```

### 動態佈局切換
```typescript
// 動態切換佈局的服務
@Injectable()
export class LayoutService {
  private currentLayout = new BehaviorSubject<string>('three-rows');
  
  setLayout(layout: string) {
    this.currentLayout.next(layout);
  }
  
  getCurrentLayout(): Observable<string> {
    return this.currentLayout.asObservable();
  }
}
```

## 🧩 元件整合

### Header 元件
- **kgi-header-component**: 標準Header
- **kgi-header-full-component**: 完整功能Header

**功能**:
- 品牌標識顯示
- 主導航選單
- 用戶資訊顯示
- 語言切換功能

### Footer 元件
- **kgi-footer**: 標準Footer

**功能**:
- 版權聲明
- 重要連結
- 聯絡資訊
- 法律聲明

### Drawer 元件
- **kendo-drawer**: Kendo UI 側邊欄
- **kendo-drawer-container**: 容器元件

**功能**:
- 多層級選單
- 展開/收納
- 響應式適配
- 圖示系統

## 🎛️ 配置選項

### Drawer 配置
```typescript
// three-rows-drawer.layout.ts
export class ThreeRowsDrawerLayoutComponent {
  // 抽屜配置
  mode: DrawerMode = 'overlay';
  mini: boolean = false;
  miniWidth: number = 50;
  width: number = 200;
  drawerExpanded: boolean = true;
  
  // 選單項目
  drawerItems: DrawerItem[] = [
    {
      text: '首頁',
      icon: 'k-icon k-i-home',
      selected: true,
      path: '/home'
    },
    {
      text: 'IBR系統',
      icon: 'k-icon k-i-globe',
      children: [
        { text: '自然人解款', path: '/ibr/individual' },
        { text: '法人解款', path: '/ibr/corporate' },
        { text: '補件處理', path: '/ibr/supplement' }
      ]
    }
  ];
}
```

### 響應式配置
```typescript
// 響應式監聽
@HostListener('window:resize', ['$event'])
onResize(event: any) {
  const width = event.target.innerWidth;
  
  if (width < 768) {
    this.mode = 'overlay';
    this.drawerExpanded = false;
  } else if (width < 1024) {
    this.mode = 'push';
    this.mini = true;
  } else {
    this.mode = 'push';
    this.mini = false;
    this.drawerExpanded = true;
  }
}
```

## 📱 行動端最佳化

### 觸控優化
- **大按鈕**: 最小44px點擊區域
- **適當間距**: 8px以上元素間距
- **滑動手勢**: 支援滑動展開側邊欄
- **快速點擊**: 防止雙擊縮放

### 效能優化
- **延遲載入**: 非關鍵元件延遲載入
- **圖片優化**: 響應式圖片載入
- **動畫優化**: 硬體加速動畫
- **記憶體管理**: 適時釋放資源

## 🎨 主題支援

### 主題切換
```scss
// 支援的主題
.theme-default {
  --primary-color: #2196F3;
  --secondary-color: #FF9800;
}

.theme-jk {
  --primary-color: #E91E63;
  --secondary-color: #9C27B0;
}

.theme-rt {
  --primary-color: #4CAF50;
  --secondary-color: #8BC34A;
}

.theme-sp {
  --primary-color: #FF5722;
  --secondary-color: #795548;
}
```

### 動態主題
```typescript
// 主題切換服務
@Injectable()
export class ThemeService {
  setTheme(theme: string) {
    document.body.className = `theme-${theme}`;
  }
}
```

## 📚 開發指南

### 新增佈局
```bash
# 1. 建立佈局目錄
mkdir src/app/@core/layouts/new-layout

# 2. 建立檔案
touch new-layout/new-layout.layout.ts
touch new-layout/new-layout.layout.html
touch new-layout/new-layout.layout.scss

# 3. 實作佈局元件
# 4. 加入到 index.ts
# 5. 更新模組宣告
```

### 最佳實踐
- **保持簡潔**: 佈局只負責結構，不包含業務邏輯
- **一致性**: 所有佈局使用相同的命名慣例
- **可測試**: 每個佈局都要有對應的測試
- **文檔完整**: 詳細記錄使用場景和配置

---

**🎯 佈局完成度**: 100% | **📱 響應式**: 完全支援 | **🎨 主題相容**: 4個主題 | **⚡ 效能**: 優化完成

*Layouts模組是IBR應用程式的視覺基礎，提供標準化、響應式、可維護的頁面佈局系統，確保整個應用程式的視覺一致性和用戶體驗。*