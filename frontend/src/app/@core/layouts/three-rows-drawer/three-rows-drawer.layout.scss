.container, .content {
  display: flex;
  flex-direction: column;
}
@media all and (min-width: 768px) {
  .container {
    //flex-direction: row;
    flex-wrap: wrap;
  }
  header,
  footer {
    width: 100%;
  }
  main {
    flex: 2;
    order: 2;
    min-height: 80vh;
  }

  .left-sidebar {
    order: 1;
    flex: 1;
  }
  .right-sidebar {
    flex: 1;
    order: 3;
  }
  footer {
    order: 4;
  }

}

.k-level-1 {
  padding-left: 5px;
}

kendo-drawer-container {
  position: fixed;
  width: 100%;
  height: 100%;
}
