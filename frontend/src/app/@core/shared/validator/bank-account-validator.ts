import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * 台灣銀行帳號格式驗證器
 * 支援台灣主要銀行的帳號格式驗證，包含檢查碼驗證
 */

// 銀行代碼與名稱對應表
export const TAIWAN_BANK_CODES: Record<string, string> = {
  '004': '台灣銀行',
  '005': '台灣土地銀行',
  '006': '合作金庫商業銀行',
  '007': '第一商業銀行',
  '008': '華南商業銀行',
  '009': '彰化商業銀行',
  '011': '上海商業儲蓄銀行',
  '012': '台北富邦銀行',
  '013': '國泰世華商業銀行',
  '016': '高雄銀行',
  '017': '兆豐國際商業銀行',
  '018': '全國農業金庫',
  '021': '花旗（台灣）商業銀行',
  '022': '美國美國銀行',
  '025': '首都銀行',
  '039': '澳盛（台灣）商業銀行',
  '040': '中華開發工業銀行',
  '050': '台灣企銀',
  '052': '渣打國際商業銀行',
  '053': '台中商業銀行',
  '054': '京城商業銀行',
  '081': '匯豐（台灣）商業銀行',
  '101': '台北101銀行',
  '102': '華泰商業銀行',
  '103': '台灣新光商業銀行',
  '104': '台北商業銀行',
  '108': '陽信商業銀行',
  '114': '基隆第一信用合作社',
  '115': '基隆第二信用合作社',
  '118': '板橋信用合作社',
  '119': '淡水信用合作社',
  '120': '宜蘭信用合作社',
  '124': '花蓮信用合作社',
  '127': '三重市農會信用部',
  '158': '彰化縣鹿港信用合作社',
  '204': '遠東國際商業銀行',
  '215': '日盛國際商業銀行',
  '224': '安泰商業銀行',
  '803': '聯邦商業銀行',
  '805': '遠東國際商業銀行',
  '806': '元大商業銀行',
  '807': '永豐商業銀行',
  '808': '玉山商業銀行',
  '809': '凱基商業銀行',
  '810': '星展（台灣）商業銀行',
  '812': '台新國際商業銀行',
  '814': '大眾商業銀行',
  '815': '日盛國際商業銀行',
  '816': '安泰商業銀行',
  '822': '中國信託商業銀行',
  '824': '聯邦商業銀行',
  '826': '華南永昌綜合證券',
  '700': '中華郵政',
  // 信用合作社
  '910': '財團法人農漁會聯合資訊中心',
  '951': '農業金庫'
};

// 各銀行特殊帳號格式規則
interface BankRule {
  bankCode: string;
  accountLengths: number[];
  pattern?: RegExp;
  hasCheckDigit?: boolean;
  checkDigitValidator?: (account: string) => boolean;
}

const BANK_RULES: BankRule[] = [
  // 台灣銀行 (004)
  {
    bankCode: '004',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateTaiwanBankCheckDigit
  },
  // 台灣土地銀行 (005)
  {
    bankCode: '005',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateLandBankCheckDigit
  },
  // 合作金庫 (006)
  {
    bankCode: '006',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateCoopBankCheckDigit
  },
  // 第一銀行 (007)
  {
    bankCode: '007',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateFirstBankCheckDigit
  },
  // 華南銀行 (008)
  {
    bankCode: '008',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateHuaNanBankCheckDigit
  },
  // 彰化銀行 (009)
  {
    bankCode: '009',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateChangHwaBankCheckDigit
  },
  // 台北富邦銀行 (012)
  {
    bankCode: '012',
    accountLengths: [14],
    pattern: /^\d{14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateFubonBankCheckDigit
  },
  // 國泰世華銀行 (013)
  {
    bankCode: '013',
    accountLengths: [14],
    pattern: /^\d{14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateCathayBankCheckDigit
  },
  // 兆豐銀行 (017)
  {
    bankCode: '017',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateMegaBankCheckDigit
  },
  // 花旗銀行 (021)
  {
    bankCode: '021',
    accountLengths: [8, 10, 12],
    pattern: /^\d{8,12}$/,
    hasCheckDigit: false
  },
  // 渣打銀行 (052)
  {
    bankCode: '052',
    accountLengths: [10, 12],
    pattern: /^\d{10,12}$/,
    hasCheckDigit: false
  },
  // 匯豐銀行 (081)
  {
    bankCode: '081',
    accountLengths: [9, 12],
    pattern: /^\d{9,12}$/,
    hasCheckDigit: false
  },
  // 台新銀行 (812)
  {
    bankCode: '812',
    accountLengths: [14],
    pattern: /^\d{14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateTaishinBankCheckDigit
  },
  // 中國信託 (822)
  {
    bankCode: '822',
    accountLengths: [14],
    pattern: /^\d{14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateCTBCBankCheckDigit
  },
  // 玉山銀行 (808)
  {
    bankCode: '808',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateEsunBankCheckDigit
  },
  // 永豐銀行 (807)
  {
    bankCode: '807',
    accountLengths: [14],
    pattern: /^\d{14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateSinoPacBankCheckDigit
  },
  // 元大銀行 (806)
  {
    bankCode: '806',
    accountLengths: [12, 14],
    pattern: /^\d{12,14}$/,
    hasCheckDigit: true,
    checkDigitValidator: validateYuantaBankCheckDigit
  },
  // 中華郵政 (700)
  {
    bankCode: '700',
    accountLengths: [14],
    pattern: /^0\d{13}$/,
    hasCheckDigit: true,
    checkDigitValidator: validatePostOfficeCheckDigit
  }
];

// 檢查碼驗證函數

/**
 * 台灣銀行檢查碼驗證
 */
function validateTaiwanBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    const digit = parseInt(account[i]) * weights[i];
    sum += Math.floor(digit / 10) + (digit % 10);
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(account[11]);
}

/**
 * 土地銀行檢查碼驗證
 */
function validateLandBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [2, 8, 7, 6, 5, 4, 3, 2, 8, 7, 6];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 合作金庫檢查碼驗證
 */
function validateCoopBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    const digit = parseInt(account[i]) * weights[i];
    sum += Math.floor(digit / 10) + (digit % 10);
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(account[11]);
}

/**
 * 第一銀行檢查碼驗證
 */
function validateFirstBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [2, 8, 7, 6, 5, 4, 3, 2, 8, 7, 6];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const remainder = sum % 11;
  const checkDigit = remainder === 0 ? 0 : remainder === 1 ? 0 : 11 - remainder;
  
  return checkDigit === parseInt(account[11]);
}

/**
 * 華南銀行檢查碼驗證
 */
function validateHuaNanBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [2, 3, 4, 5, 6, 7, 2, 3, 4, 5, 6];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 彰化銀行檢查碼驗證
 */
function validateChangHwaBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [8, 9, 2, 3, 4, 5, 6, 7, 8, 9, 2];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit === 11 ? 1 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 富邦銀行檢查碼驗證
 */
function validateFubonBankCheckDigit(account: string): boolean {
  if (account.length !== 14) return false;
  
  const weights = [2, 3, 4, 5, 6, 7, 2, 3, 4, 5, 6, 7, 2];
  let sum = 0;
  
  for (let i = 0; i < 13; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[13]);
}

/**
 * 國泰世華銀行檢查碼驗證
 */
function validateCathayBankCheckDigit(account: string): boolean {
  if (account.length !== 14) return false;
  
  const weights = [7, 6, 5, 4, 3, 2, 7, 6, 5, 4, 3, 2, 7];
  let sum = 0;
  
  for (let i = 0; i < 13; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit >= 10 ? checkDigit - 10 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[13]);
}

/**
 * 兆豐銀行檢查碼驗證
 */
function validateMegaBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [3, 7, 6, 1, 9, 4, 5, 2, 8, 6, 3];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit >= 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 台新銀行檢查碼驗證
 */
function validateTaishinBankCheckDigit(account: string): boolean {
  if (account.length !== 14) return false;
  
  const weights = [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1];
  let sum = 0;
  
  for (let i = 0; i < 13; i++) {
    const digit = parseInt(account[i]) * weights[i];
    sum += Math.floor(digit / 10) + (digit % 10);
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(account[13]);
}

/**
 * 中國信託檢查碼驗證
 */
function validateCTBCBankCheckDigit(account: string): boolean {
  if (account.length !== 14) return false;
  
  const weights = [6, 3, 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7];
  let sum = 0;
  
  for (let i = 0; i < 13; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit === 11 ? 1 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[13]);
}

/**
 * 玉山銀行檢查碼驗證
 */
function validateEsunBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [3, 7, 6, 1, 9, 4, 5, 2, 8, 6, 3];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = (11 - (sum % 11)) % 11;
  const expectedCheckDigit = checkDigit >= 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 永豐銀行檢查碼驗證
 */
function validateSinoPacBankCheckDigit(account: string): boolean {
  if (account.length !== 14) return false;
  
  const weights = [1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1];
  let sum = 0;
  
  for (let i = 0; i < 13; i++) {
    const digit = parseInt(account[i]) * weights[i];
    sum += Math.floor(digit / 10) + (digit % 10);
  }
  
  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit === parseInt(account[13]);
}

/**
 * 元大銀行檢查碼驗證
 */
function validateYuantaBankCheckDigit(account: string): boolean {
  if (account.length < 12) return false;
  
  const weights = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11];
  let sum = 0;
  
  for (let i = 0; i < 11; i++) {
    sum += parseInt(account[i]) * weights[i];
  }
  
  const checkDigit = sum % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[11]);
}

/**
 * 中華郵政檢查碼驗證
 */
function validatePostOfficeCheckDigit(account: string): boolean {
  if (account.length !== 14 || !account.startsWith('0')) return false;
  
  const weights = [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1];
  let sum = 0;
  
  for (let i = 1; i < 13; i++) {
    sum += parseInt(account[i]);
  }
  
  const checkDigit = sum % 11;
  const expectedCheckDigit = checkDigit === 10 ? 0 : checkDigit;
  
  return expectedCheckDigit === parseInt(account[13]);
}

/**
 * 銀行帳號驗證結果介面
 */
export interface BankAccountValidationResult {
  isValid: boolean;
  bankCode?: string;
  bankName?: string;
  accountNumber?: string;
  errors: string[];
}

/**
 * 驗證銀行代碼是否存在
 */
export function validateBankCode(bankCode: string): boolean {
  return Object.prototype.hasOwnProperty.call(TAIWAN_BANK_CODES, bankCode);
}

/**
 * 取得銀行名稱
 */
export function getBankName(bankCode: string): string | null {
  return TAIWAN_BANK_CODES[bankCode] || null;
}

/**
 * 驗證完整銀行帳號（銀行代碼 + 帳號）
 */
export function validateFullBankAccount(fullAccount: string): BankAccountValidationResult {
  const result: BankAccountValidationResult = {
    isValid: false,
    errors: []
  };

  // 移除空格和特殊字符
  const cleanAccount = fullAccount.replace(/\s|-/g, '');
  
  // 檢查基本格式
  if (!cleanAccount || !/^\d+$/.test(cleanAccount)) {
    result.errors.push('帳號格式不正確，只能包含數字');
    return result;
  }

  // 檢查長度（最少7位，包含銀行代碼3位）
  if (cleanAccount.length < 7) {
    result.errors.push('帳號長度不足');
    return result;
  }

  // 提取銀行代碼（前3位）
  const bankCode = cleanAccount.substring(0, 3);
  const accountNumber = cleanAccount.substring(3);

  // 驗證銀行代碼
  if (!validateBankCode(bankCode)) {
    result.errors.push('銀行代碼不存在');
    return result;
  }

  result.bankCode = bankCode;
  result.bankName = getBankName(bankCode);
  result.accountNumber = accountNumber;

  // 查找對應的銀行規則
  const bankRule = BANK_RULES.find(rule => rule.bankCode === bankCode);
  
  if (bankRule) {
    // 檢查帳號長度
    if (!bankRule.accountLengths.includes(accountNumber.length)) {
      result.errors.push(`${result.bankName}帳號長度應為 ${bankRule.accountLengths.join(' 或 ')} 位數`);
      return result;
    }

    // 檢查格式
    if (bankRule.pattern && !bankRule.pattern.test(accountNumber)) {
      result.errors.push('帳號格式不符合銀行規範');
      return result;
    }

    // 檢查檢查碼
    if (bankRule.hasCheckDigit && bankRule.checkDigitValidator) {
      if (!bankRule.checkDigitValidator(accountNumber)) {
        result.errors.push('帳號檢查碼驗證失敗');
        return result;
      }
    }
  } else {
    // 沒有特定規則的銀行，進行基本驗證
    if (accountNumber.length < 4 || accountNumber.length > 20) {
      result.errors.push('帳號長度應在4-20位數之間');
      return result;
    }
  }

  result.isValid = true;
  return result;
}

/**
 * 驗證銀行帳號（不含銀行代碼）
 */
export function validateBankAccount(accountNumber: string, bankCode: string): BankAccountValidationResult {
  const result: BankAccountValidationResult = {
    isValid: false,
    errors: []
  };

  // 移除空格和特殊字符
  const cleanAccount = accountNumber.replace(/\s|-/g, '');
  
  // 檢查基本格式
  if (!cleanAccount || !/^\d+$/.test(cleanAccount)) {
    result.errors.push('帳號格式不正確，只能包含數字');
    return result;
  }

  // 驗證銀行代碼
  if (!validateBankCode(bankCode)) {
    result.errors.push('銀行代碼不存在');
    return result;
  }

  result.bankCode = bankCode;
  result.bankName = getBankName(bankCode);
  result.accountNumber = cleanAccount;

  // 查找對應的銀行規則
  const bankRule = BANK_RULES.find(rule => rule.bankCode === bankCode);
  
  if (bankRule) {
    // 檢查帳號長度
    if (!bankRule.accountLengths.includes(cleanAccount.length)) {
      result.errors.push(`${result.bankName}帳號長度應為 ${bankRule.accountLengths.join(' 或 ')} 位數`);
      return result;
    }

    // 檢查格式
    if (bankRule.pattern && !bankRule.pattern.test(cleanAccount)) {
      result.errors.push('帳號格式不符合銀行規範');
      return result;
    }

    // 檢查檢查碼
    if (bankRule.hasCheckDigit && bankRule.checkDigitValidator) {
      if (!bankRule.checkDigitValidator(cleanAccount)) {
        result.errors.push('帳號檢查碼驗證失敗');
        return result;
      }
    }
  } else {
    // 沒有特定規則的銀行，進行基本驗證
    if (cleanAccount.length < 4 || cleanAccount.length > 20) {
      result.errors.push('帳號長度應在4-20位數之間');
      return result;
    }
  }

  result.isValid = true;
  return result;
}

/**
 * Angular 表單驗證器 - 完整帳號驗證（銀行代碼 + 帳號）
 */
export function bankAccountValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }

    const result = validateFullBankAccount(control.value);
    
    if (!result.isValid) {
      return {
        bankAccount: {
          message: result.errors.join('；'),
          errors: result.errors
        }
      };
    }

    return null;
  };
}

/**
 * Angular 表單驗證器 - 帳號驗證（需提供銀行代碼）
 */
export function bankAccountNumberValidator(bankCode: string): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }

    const result = validateBankAccount(control.value, bankCode);
    
    if (!result.isValid) {
      return {
        bankAccountNumber: {
          message: result.errors.join('；'),
          errors: result.errors,
          bankCode: bankCode,
          bankName: getBankName(bankCode)
        }
      };
    }

    return null;
  };
}

/**
 * Angular 表單驗證器 - 銀行代碼驗證
 */
export function bankCodeValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) {
      return null;
    }

    const bankCode = control.value.toString().trim();
    
    if (!validateBankCode(bankCode)) {
      return {
        bankCode: {
          message: '銀行代碼不存在',
          code: bankCode
        }
      };
    }

    return null;
  };
}

/**
 * 格式化銀行帳號顯示（加入分隔符）
 */
export function formatBankAccount(accountNumber: string, separator = '-'): string {
  const cleanAccount = accountNumber.replace(/\s|-/g, '');
  
  if (cleanAccount.length <= 4) {
    return cleanAccount;
  }
  
  // 每4位數加一個分隔符
  return cleanAccount.replace(/(\d{4})(?=\d)/g, `$1${separator}`);
}

/**
 * 取得所有支援的銀行清單
 */
export function getSupportedBanks(): {code: string, name: string}[] {
  return Object.keys(TAIWAN_BANK_CODES).map(code => ({
    code,
    name: TAIWAN_BANK_CODES[code]
  })).sort((a, b) => a.name.localeCompare(b.name));
}