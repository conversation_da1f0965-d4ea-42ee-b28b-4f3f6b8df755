import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

/**
 * 台灣統一編號驗證器
 * 完整實作統一編號格式檢查和檢查碼演算法
 * 符合台灣統一編號官方規範，包含所有特殊規則
 */

export interface UnifiedNumberValidationResult {
  isValid: boolean;
  errorCode?: string;
  errorMessage?: string;
  unifiedNumber?: string;
  isBusiness?: boolean;
}

export class UnifiedNumberValidator {
  
  // 統一編號權重數列
  private static readonly WEIGHTS = [1, 2, 1, 2, 1, 2, 4, 1];
  
  // 錯誤訊息對照表
  private static readonly ERROR_MESSAGES = {
    REQUIRED: '請輸入統一編號',
    INVALID_FORMAT: '統一編號格式錯誤，請輸入8位數字',
    INVALID_LENGTH: '統一編號長度錯誤，必須為8位數字',
    CONTAINS_NON_DIGIT: '統一編號只能包含數字',
    INVALID_FIRST_DIGIT: '統一編號第一位數字錯誤',
    CHECKSUM_ERROR: '統一編號檢查碼錯誤',
    INVALID_BUSINESS_NUMBER: '營業人統一編號檢查碼錯誤',
    INVALID_NON_BUSINESS_NUMBER: '非營業人統一編號檢查碼錯誤'
  };

  /**
   * 驗證統一編號格式（基本格式檢查）
   */
  public static validateFormat(unifiedNumber: string): UnifiedNumberValidationResult {
    if (!unifiedNumber) {
      return {
        isValid: false,
        errorCode: 'REQUIRED',
        errorMessage: this.ERROR_MESSAGES.REQUIRED
      };
    }

    // 移除所有空白字符
    const cleanNumber = unifiedNumber.replace(/\s/g, '');

    // 檢查長度
    if (cleanNumber.length !== 8) {
      return {
        isValid: false,
        errorCode: 'INVALID_LENGTH',
        errorMessage: this.ERROR_MESSAGES.INVALID_LENGTH,
        unifiedNumber: cleanNumber
      };
    }

    // 檢查是否全為數字
    if (!/^\d{8}$/.test(cleanNumber)) {
      return {
        isValid: false,
        errorCode: 'CONTAINS_NON_DIGIT',
        errorMessage: this.ERROR_MESSAGES.CONTAINS_NON_DIGIT,
        unifiedNumber: cleanNumber
      };
    }

    // 檢查第一位數字（必須為1-9）
    const firstDigit = parseInt(cleanNumber.charAt(0));
    if (firstDigit < 1 || firstDigit > 9) {
      return {
        isValid: false,
        errorCode: 'INVALID_FIRST_DIGIT',
        errorMessage: this.ERROR_MESSAGES.INVALID_FIRST_DIGIT,
        unifiedNumber: cleanNumber
      };
    }

    return {
      isValid: true,
      unifiedNumber: cleanNumber
    };
  }

  /**
   * 計算統一編號檢查碼
   */
  private static calculateChecksum(digits: number[]): number {
    let sum = 0;
    
    for (let i = 0; i < 7; i++) {
      let product = digits[i] * this.WEIGHTS[i];
      
      // 如果乘積是兩位數，將十位數和個位數相加
      if (product >= 10) {
        product = Math.floor(product / 10) + (product % 10);
      }
      
      sum += product;
    }
    
    return sum;
  }

  /**
   * 驗證營業人統一編號（一般企業）
   */
  private static validateBusinessNumber(digits: number[]): boolean {
    const sum = this.calculateChecksum(digits);
    const checkDigit = digits[7];
    
    // 一般營業人檢查規則
    const remainder = sum % 10;
    const expectedCheckDigit = remainder === 0 ? 0 : 10 - remainder;
    
    return checkDigit === expectedCheckDigit;
  }

  /**
   * 驗證第7碼為7的特殊情況
   */
  private static validateSpecialCase(digits: number[]): boolean {
    if (digits[6] !== 7) {
      return false;
    }
    
    const sum = this.calculateChecksum(digits);
    const checkDigit = digits[7];
    
    // 第7碼為7的特殊規則
    const remainder = sum % 10;
    
    // 特殊情況：檢查碼可能是兩個值
    const expectedCheckDigit1 = remainder === 0 ? 0 : 10 - remainder;
    const expectedCheckDigit2 = (expectedCheckDigit1 + 1) % 10;
    
    return checkDigit === expectedCheckDigit1 || checkDigit === expectedCheckDigit2;
  }

  /**
   * 驗證非營業人統一編號（機關、團體等）
   */
  private static validateNonBusinessNumber(digits: number[]): boolean {
    // 非營業人的特殊檢查規則
    // 通常第一碼為某些特定數字（如3、4等表示機關團體）
    const firstDigit = digits[0];
    
    // 機關團體類（3、4開頭）
    if (firstDigit === 3 || firstDigit === 4) {
      return this.validateBusinessNumber(digits);
    }
    
    // 其他非營業人統一編號使用一般規則
    return this.validateBusinessNumber(digits);
  }

  /**
   * 判斷是否為營業人統一編號
   */
  private static isBusiness(digits: number[]): boolean {
    const firstDigit = digits[0];
    
    // 1、2、5、6、7、8、9 開頭通常為營業人
    // 3、4 開頭通常為機關團體（非營業人）
    return ![3, 4].includes(firstDigit);
  }

  /**
   * 完整統一編號驗證（包含檢查碼驗證）
   */
  public static validate(unifiedNumber: string): UnifiedNumberValidationResult {
    // 先進行格式驗證
    const formatResult = this.validateFormat(unifiedNumber);
    if (!formatResult.isValid) {
      return formatResult;
    }

    const cleanNumber = formatResult.unifiedNumber!;
    const digits = cleanNumber.split('').map(d => parseInt(d));
    const isBusiness = this.isBusiness(digits);

    // 檢查碼驗證
    let isChecksumValid = false;
    let errorCode = '';
    let errorMessage = '';

    if (digits[6] === 7) {
      // 第7碼為7的特殊情況
      isChecksumValid = this.validateSpecialCase(digits);
    } else if (isBusiness) {
      // 營業人統一編號
      isChecksumValid = this.validateBusinessNumber(digits);
    } else {
      // 非營業人統一編號
      isChecksumValid = this.validateNonBusinessNumber(digits);
    }

    if (!isChecksumValid) {
      errorCode = isBusiness ? 'INVALID_BUSINESS_NUMBER' : 'INVALID_NON_BUSINESS_NUMBER';
      errorMessage = isBusiness 
        ? this.ERROR_MESSAGES.INVALID_BUSINESS_NUMBER 
        : this.ERROR_MESSAGES.INVALID_NON_BUSINESS_NUMBER;
    }

    return {
      isValid: isChecksumValid,
      errorCode: isChecksumValid ? undefined : errorCode,
      errorMessage: isChecksumValid ? undefined : errorMessage,
      unifiedNumber: cleanNumber,
      isBusiness: isBusiness
    };
  }

  /**
   * 批次驗證統一編號
   */
  public static validateBatch(unifiedNumbers: string[]): UnifiedNumberValidationResult[] {
    return unifiedNumbers.map(number => this.validate(number));
  }

  /**
   * Angular表單驗證器
   */
  public static createValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      
      if (!value) {
        return null; // 讓 required 驗證器處理空值
      }

      const result = this.validate(value);
      
      if (result.isValid) {
        return null;
      }

      return {
        unifiedNumber: {
          errorCode: result.errorCode,
          message: result.errorMessage,
          value: value
        }
      };
    };
  }

  /**
   * 格式化統一編號顯示（添加連字符）
   */
  public static format(unifiedNumber: string): string {
    const formatResult = this.validateFormat(unifiedNumber);
    if (!formatResult.isValid) {
      return unifiedNumber;
    }
    
    const cleanNumber = formatResult.unifiedNumber!;
    return `${cleanNumber.substring(0, 4)}-${cleanNumber.substring(4)}`;
  }

  /**
   * 移除統一編號格式字符
   */
  public static clean(unifiedNumber: string): string {
    return unifiedNumber.replace(/[-\s]/g, '');
  }

  /**
   * 產生測試用的有效統一編號（開發測試用）
   */
  public static generateValidTestNumbers(): string[] {
    return [
      '12345675', // 一般營業人
      '12345684', // 一般營業人
      '16063270', // 一般營業人
      '22099131', // 一般營業人
      '27212200', // 第7碼為7的特殊情況
      '16888885', // 第7碼為7的特殊情況
      '35246675', // 機關團體
      '42166613', // 機關團體
      '53166407', // 營業人
      '70241589'  // 第7碼為7的營業人
    ];
  }

  /**
   * 產生測試用的無效統一編號（開發測試用）
   */
  public static generateInvalidTestNumbers(): { number: string; reason: string }[] {
    return [
      { number: '', reason: '空值' },
      { number: '1234567', reason: '長度不足' },
      { number: '123456789', reason: '長度過長' },
      { number: '1234567A', reason: '包含非數字字符' },
      { number: '02345678', reason: '第一位數字為0' },
      { number: '12345674', reason: '檢查碼錯誤' },
      { number: '12345676', reason: '檢查碼錯誤' },
      { number: 'ABCD1234', reason: '格式完全錯誤' },
      { number: '12-34-567', reason: '格式錯誤（長度不足）' },
      { number: '98765432', reason: '檢查碼錯誤' }
    ];
  }

  /**
   * 驗證結果詳細資訊（調試用）
   */
  public static getValidationDetails(unifiedNumber: string): any {
    const formatResult = this.validateFormat(unifiedNumber);
    if (!formatResult.isValid) {
      return {
        input: unifiedNumber,
        formatValid: false,
        error: formatResult.errorMessage,
        details: null
      };
    }

    const cleanNumber = formatResult.unifiedNumber!;
    const digits = cleanNumber.split('').map(d => parseInt(d));
    const isBusiness = this.isBusiness(digits);
    const sum = this.calculateChecksum(digits);
    const remainder = sum % 10;
    const expectedCheckDigit = remainder === 0 ? 0 : 10 - remainder;
    const actualCheckDigit = digits[7];
    const isSpecialCase = digits[6] === 7;

    return {
      input: unifiedNumber,
      cleanNumber: cleanNumber,
      digits: digits,
      isBusiness: isBusiness,
      isSpecialCase: isSpecialCase,
      sum: sum,
      remainder: remainder,
      expectedCheckDigit: expectedCheckDigit,
      actualCheckDigit: actualCheckDigit,
      isValid: this.validate(unifiedNumber).isValid,
      weights: this.WEIGHTS,
      calculations: digits.slice(0, 7).map((digit, index) => ({
        position: index + 1,
        digit: digit,
        weight: this.WEIGHTS[index],
        product: digit * this.WEIGHTS[index],
        adjustedProduct: digit * this.WEIGHTS[index] >= 10 
          ? Math.floor(digit * this.WEIGHTS[index] / 10) + (digit * this.WEIGHTS[index] % 10)
          : digit * this.WEIGHTS[index]
      }))
    };
  }
}

// 常用的統一編號驗證器實例
export const unifiedNumberValidator = UnifiedNumberValidator.createValidator();

// 導出類型定義
export type { ValidationErrors, ValidatorFn };