/**
 * 台灣身分證號驗證器
 * 支援：本國人身分證、外籍人士統一證號、居留證號
 */

export interface TaiwanIdValidationResult {
  isValid: boolean;
  errorMessage?: string;
  idType?: 'NATIONAL' | 'FOREIGN' | 'RESIDENT';
  gender?: 'MALE' | 'FEMALE';
  cityCode?: string;
  cityName?: string;
}

export class TaiwanIdValidator {
  
  // 縣市代碼對應表
  private static readonly CITY_CODES: Record<string, string> = {
    'A': '台北市',
    'B': '台中市',
    'C': '基隆市',
    'D': '台南市',
    'E': '高雄市',
    'F': '新北市',
    'G': '宜蘭縣',
    'H': '桃園市',
    'I': '嘉義市',
    'J': '新竹縣',
    'K': '苗栗縣',
    'L': '台中縣',
    'M': '南投縣',
    'N': '彰化縣',
    'O': '新竹市',
    'P': '雲林縣',
    'Q': '嘉義縣',
    'R': '台南縣',
    'S': '高雄縣',
    'T': '屏東縣',
    'U': '花蓮縣',
    'V': '台東縣',
    'W': '金門縣',
    'X': '澎湖縣',
    'Y': '陽明山',
    'Z': '連江縣'
  };

  // 縣市代碼數值對應表（用於檢查碼計算）
  private static readonly CITY_CODE_VALUES: Record<string, number> = {
    'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17,
    'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22, 'O': 35, 'P': 23,
    'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28, 'V': 29, 'W': 32, 'X': 30,
    'Y': 31, 'Z': 33
  };

  // 外籍人士第一碼對應表
  private static readonly FOREIGN_FIRST_CODES = ['A', 'B', 'C', 'D'];

  // 居留證第一碼對應表
  private static readonly RESIDENT_FIRST_CODES = ['A', 'B', 'C', 'D'];

  /**
   * 驗證台灣身分證號
   */
  static validate(id: string): TaiwanIdValidationResult {
    if (!id) {
      return {
        isValid: false,
        errorMessage: '身分證號不能為空'
      };
    }

    // 移除空白並轉為大寫
    const cleanId = id.trim().toUpperCase();

    // 基本格式檢查
    if (!/^[A-Z][0-9A-Z]\d{8}$/.test(cleanId)) {
      return {
        isValid: false,
        errorMessage: '身分證號格式不正確，應為1個英文字母 + 1個數字或英文字母 + 8個數字'
      };
    }

    const firstChar = cleanId.charAt(0);
    const secondChar = cleanId.charAt(1);

    // 檢查縣市代碼
    if (!this.CITY_CODES[firstChar]) {
      return {
        isValid: false,
        errorMessage: '身分證號縣市代碼不正確'
      };
    }

    // 判斷身分證類型並驗證
    if (/^\d$/.test(secondChar)) {
      // 本國人身分證
      return this.validateNationalId(cleanId);
    } else if (/^[A-Z]$/.test(secondChar)) {
      // 外籍人士或居留證
      return this.validateForeignOrResidentId(cleanId);
    } else {
      return {
        isValid: false,
        errorMessage: '身分證號第二碼格式不正確'
      };
    }
  }

  /**
   * 驗證本國人身分證
   */
  private static validateNationalId(id: string): TaiwanIdValidationResult {
    const firstChar = id.charAt(0);
    const genderCode = parseInt(id.charAt(1));
    
    // 性別代碼驗證
    if (genderCode !== 1 && genderCode !== 2) {
      return {
        isValid: false,
        errorMessage: '身分證號性別代碼不正確，應為1（男性）或2（女性）'
      };
    }

    // 檢查碼驗證
    if (!this.validateNationalChecksum(id)) {
      return {
        isValid: false,
        errorMessage: '身分證號檢查碼錯誤'
      };
    }

    return {
      isValid: true,
      idType: 'NATIONAL',
      gender: genderCode === 1 ? 'MALE' : 'FEMALE',
      cityCode: firstChar,
      cityName: this.CITY_CODES[firstChar]
    };
  }

  /**
   * 驗證外籍人士統一證號或居留證號
   */
  private static validateForeignOrResidentId(id: string): TaiwanIdValidationResult {
    const firstChar = id.charAt(0);
    const secondChar = id.charAt(1);
    const genderCode = parseInt(id.charAt(2));

    // 性別代碼驗證
    if (genderCode !== 8 && genderCode !== 9) {
      return {
        isValid: false,
        errorMessage: '外籍人士證號性別代碼不正確，應為8（男性）或9（女性）'
      };
    }

    // 檢查碼驗證
    if (!this.validateForeignChecksum(id)) {
      return {
        isValid: false,
        errorMessage: '外籍人士證號檢查碼錯誤'
      };
    }

    return {
      isValid: true,
      idType: 'FOREIGN',
      gender: genderCode === 8 ? 'MALE' : 'FEMALE',
      cityCode: firstChar,
      cityName: this.CITY_CODES[firstChar]
    };
  }

  /**
   * 驗證本國人身分證檢查碼
   */
  private static validateNationalChecksum(id: string): boolean {
    const firstChar = id.charAt(0);
    const cityValue = this.CITY_CODE_VALUES[firstChar];
    
    // 將縣市代碼拆解為兩位數
    const cityTens = Math.floor(cityValue / 10);
    const cityOnes = cityValue % 10;
    
    // 計算檢查碼
    let sum = cityTens * 1 + cityOnes * 9;
    
    for (let i = 1; i < 9; i++) {
      sum += parseInt(id.charAt(i)) * (9 - i);
    }
    
    const remainder = sum % 10;
    const checkDigit = remainder === 0 ? 0 : 10 - remainder;
    
    return checkDigit === parseInt(id.charAt(9));
  }

  /**
   * 驗證外籍人士統一證號檢查碼
   */
  private static validateForeignChecksum(id: string): boolean {
    const firstChar = id.charAt(0);
    const secondChar = id.charAt(1);
    
    const firstValue = this.CITY_CODE_VALUES[firstChar];
    const secondValue = this.getSecondCharValue(secondChar);
    
    // 將第一碼拆解為兩位數
    const firstTens = Math.floor(firstValue / 10);
    const firstOnes = firstValue % 10;
    
    // 將第二碼拆解為兩位數
    const secondTens = Math.floor(secondValue / 10);
    const secondOnes = secondValue % 10;
    
    // 計算檢查碼
    let sum = firstTens * 1 + firstOnes * 9 + secondTens * 8 + secondOnes * 7;
    
    for (let i = 2; i < 9; i++) {
      sum += parseInt(id.charAt(i)) * (9 - i);
    }
    
    const remainder = sum % 10;
    const checkDigit = remainder === 0 ? 0 : 10 - remainder;
    
    return checkDigit === parseInt(id.charAt(9));
  }

  /**
   * 取得第二個字元的數值
   */
  private static getSecondCharValue(char: string): number {
    return char.charCodeAt(0) - 'A'.charCodeAt(0) + 10;
  }

  /**
   * 快速驗證身分證號格式（不包含詳細檢查）
   */
  static isValidFormat(id: string): boolean {
    if (!id) return false;
    const cleanId = id.trim().toUpperCase();
    return /^[A-Z][0-9A-Z]\d{8}$/.test(cleanId);
  }

  /**
   * 檢查是否為本國人身分證
   */
  static isNationalId(id: string): boolean {
    if (!this.isValidFormat(id)) return false;
    const cleanId = id.trim().toUpperCase();
    return /^\d$/.test(cleanId.charAt(1));
  }

  /**
   * 檢查是否為外籍人士統一證號
   */
  static isForeignId(id: string): boolean {
    if (!this.isValidFormat(id)) return false;
    const cleanId = id.trim().toUpperCase();
    return /^[A-Z]$/.test(cleanId.charAt(1));
  }

  /**
   * 取得性別
   */
  static getGender(id: string): 'MALE' | 'FEMALE' | null {
    const result = this.validate(id);
    return result.isValid ? result.gender! : null;
  }

  /**
   * 取得縣市名稱
   */
  static getCityName(id: string): string | null {
    const result = this.validate(id);
    return result.isValid ? result.cityName! : null;
  }

  /**
   * 取得身分證類型
   */
  static getIdType(id: string): 'NATIONAL' | 'FOREIGN' | 'RESIDENT' | null {
    const result = this.validate(id);
    return result.isValid ? result.idType! : null;
  }
}

/**
 * Angular Validator 函數
 */
export function taiwanIdValidator() {
  return (control: any) => {
    if (!control.value) {
      return null;
    }

    const result = TaiwanIdValidator.validate(control.value);
    
    if (result.isValid) {
      return null;
    } else {
      return {
        taiwanId: {
          message: result.errorMessage
        }
      };
    }
  };
}

/**
 * 測試資料
 */
export const TAIWAN_ID_TEST_DATA = {
  // 有效的本國人身分證
  validNationalIds: [
    'A123456789', // 台北市男性
    'A223456789', // 台北市女性
    'B123456789', // 台中市男性
    'F223456789', // 新北市女性
    'H123456789', // 桃園市男性
    'N223456789', // 彰化縣女性
    'K123456789', // 苗栗縣男性
    'T223456789'  // 屏東縣女性
  ],

  // 有效的外籍人士統一證號
  validForeignIds: [
    'AA88123456', // 台北市男性外籍人士
    'AB99123456', // 台北市女性外籍人士
    'BA88234567', // 台中市男性外籍人士
    'BC99345678'  // 台中市女性外籍人士
  ],

  // 無效的身分證號
  invalidIds: [
    '',           // 空字串
    'A12345678',  // 長度不足
    'A1234567890', // 長度過長
    'a123456789', // 小寫字母
    'A023456789', // 性別代碼錯誤（0）
    'A323456789', // 性別代碼錯誤（3）
    'A123456788', // 檢查碼錯誤
    'Z123456789', // 縣市代碼錯誤
    'A12345678A', // 最後一碼非數字
    'AA78123456', // 外籍性別代碼錯誤（7）
    'AA80123456'  // 外籍性別代碼錯誤（0）
  ],

  // 縣市代碼測試
  cityTests: [
    { id: 'A123456789', expectedCity: '台北市' },
    { id: 'B123456789', expectedCity: '台中市' },
    { id: 'F223456789', expectedCity: '新北市' },
    { id: 'H123456789', expectedCity: '桃園市' },
    { id: 'X223456789', expectedCity: '澎湖縣' },
    { id: 'W123456789', expectedCity: '金門縣' }
  ],

  // 性別測試
  genderTests: [
    { id: 'A123456789', expectedGender: 'MALE' },
    { id: 'A223456789', expectedGender: 'FEMALE' },
    { id: 'AA88123456', expectedGender: 'MALE' },
    { id: 'AB99123456', expectedGender: 'FEMALE' }
  ]
};