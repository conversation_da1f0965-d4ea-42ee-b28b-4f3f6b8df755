import {ValidatorFn, Validators} from "@angular/forms";
import {FormValidatorBuilder} from "../validator/form-validator";
import {GET_BASEMODEL_FIELDNAME} from "../app.constants";
import {TermsModel} from "./term.model";

export class StockModel{


    //是否重新開案, 或是斷點接續
    forceNewApply: string;

    // 產品類別
    productId: string;

    // 唯一標識
    uniqId: string;

    // 異業代號
    entry: string;

    // 異業資料
    entryOther: string;

    // 01: 體驗 02: 申請 03: 立約 04: 專人聯絡 05: 信用卡
    uniqType: string;

    // 試算資料
    calMain: string;

    // 試算按號
    expCaseNo: string;

    // 身分證
    idno: string;

    // 生日
    birthday: string;

    // 前端分成 3 格，各為 4, 2, 2 位數字
    inputYear: string;
    inputMonth: string;
    inputDate: string;

    // 手機
    phone: string;

    // 手機
    mobile: string;

    // 業務員代號
    agentNo: string;

    // 通道
    channelId: string;

    // 本人聲明未持有美國籍且非為其他地區稅務居民身份
    ch7: boolean;

    // 本人已閱讀並同意下列條款及告知事項
    ch1: boolean;

    // 同意使用與貴行往來之資料申辦貸款
    ch2: boolean;

    /**
     *  Fatch 條款
     *  屬於上線後加入欄位，因為正式區無法回補資料，故無法使用boolean
     *  null 為 未填寫
     *  0: false
     *  1: true
     */
    termsFatca: string;

    tempTermsFatca: boolean;

    // 轉介單位
    referralUnit: string;

    // 轉介員編
    referralEditor: string;

    ipAddress: string;

    productType: string;

    // 對 workflow 起哪個流程的變數
    PProductType: string;

    shortUrl: string;

    routingType: string;

    ksCifToken: string;
    // 以下為只存在 ApplyStartVO，此處尚無對應之欄位
    // private String forceNewApply;
    // private String browser ;
    // private String platform ;
    // private String os ;
    // private String clToken ;


    //single_D3 promoChannelID 有值 代表從凱證入口: KS 或 凱銀入口: KB
    promoChannelID: string;
    // 轉介單位
    promoDepart: string;
    // 轉介員編
    promoMember: string;

    // 條款
    termsList: TermsModel[];

    public getValidator( name: string): ValidatorFn | ValidatorFn[] | null {
        switch (name) {
            case 'idno':
                return [Validators.required, FormValidatorBuilder.idCardValidator("身分證字號")];
            // case 'inputYear':
            //     return [Validators.required, FormValidatorBuilder.birthYearValidator(this.inputYear, "出生年份")];
            // case 'inputMonth':
            //     return [Validators.required, FormValidatorBuilder.birthMonthValidator(this.inputMonth, "出生月份")];
            // case 'inputDate':
            //     return [Validators.required, FormValidatorBuilder.birthDateValidator(this.inputDate, "出生日期")];
            case 'inputYear':
                return [Validators.nullValidator];
            case 'inputMonth':
                return [Validators.nullValidator];
            case 'inputDate':
                return [Validators.nullValidator];
            case 'birthday':
                return [Validators.required, FormValidatorBuilder.dateValidator( '出生日期')];
            case 'phone':
                return [Validators.required, FormValidatorBuilder.loginMobileValidator("手機號碼")];
            case 'referralUnit':
                return [Validators.nullValidator];
            case 'referralEditor':
                return [Validators.nullValidator];
            case 'ch1':
                return [FormValidatorBuilder.required("請勾選同意條款", "同意條款")];
            case 'ch2':
                return [Validators.nullValidator];
            case 'ch7':
                return [Validators.nullValidator];
            case 'termsFatca':
                return [Validators.nullValidator];
            case 'tempTermsFatca':
                return [Validators.nullValidator];
            case 'PProductType':
                return [Validators.required];
            case 'productId':
                return [Validators.required];
            case 'entry':
                return [Validators.nullValidator];
            case 'entryOther':
                return [Validators.nullValidator];
            case 'uniqType':
                return [Validators.required];
            case 'agentNo':
                return [Validators.nullValidator];
            case 'channelId':
                return [Validators.nullValidator];
            case 'shortUrl':
                return [Validators.nullValidator];
            case 'ksCifToken':
                return [Validators.nullValidator];
            case 'promoChannelID':
                return [Validators.nullValidator];
            case 'promoDepart':
                return [Validators.nullValidator];
            case 'promoMember':
                return [Validators.nullValidator];
            case 'termsList':
                return [Validators.nullValidator];
        }
        return null;
    }

    public getValidators(): any {
        return {
            idno: [this.idno, this.getValidator(GET_BASEMODEL_FIELDNAME(this).idno)],

            inputYear: [this.inputYear, this.getValidator(GET_BASEMODEL_FIELDNAME(this).inputYear)],
            inputMonth: [this.inputMonth, this.getValidator(GET_BASEMODEL_FIELDNAME(this).inputMonth)],
            inputDate: [this.inputDate, this.getValidator(GET_BASEMODEL_FIELDNAME(this).inputDate)],

            birthday: [this.birthday, this.getValidator(GET_BASEMODEL_FIELDNAME(this).birthday)],

            phone: [this.phone, this.getValidator(GET_BASEMODEL_FIELDNAME(this).phone)],

            referralUnit: [this.referralUnit, this.getValidator(GET_BASEMODEL_FIELDNAME(this).referralUnit)],
            referralEditor: [this.referralEditor, this.getValidator(GET_BASEMODEL_FIELDNAME(this).referralUnit)],

            ch1: [this.ch1, this.getValidator(GET_BASEMODEL_FIELDNAME(this).ch1)],
            ch2: [this.ch2, this.getValidator(GET_BASEMODEL_FIELDNAME(this).ch2)],
            ch7: [this.ch7, this.getValidator(GET_BASEMODEL_FIELDNAME(this).ch7)],
            termsFatca: [this.termsFatca, this.getValidator(GET_BASEMODEL_FIELDNAME(this).termsFatca)],
            tempTermsFatca: [this.tempTermsFatca, this.getValidator(GET_BASEMODEL_FIELDNAME(this).tempTermsFatca)],
            PProductType: [this.PProductType, this.getValidator(GET_BASEMODEL_FIELDNAME(this).PProductType)],

            productId: [this.productId, this.getValidator(GET_BASEMODEL_FIELDNAME(this).productId)],

            entry: [this.entry, this.getValidator(GET_BASEMODEL_FIELDNAME(this).entry)],
            entryOther: [this.entryOther, this.getValidator(GET_BASEMODEL_FIELDNAME(this).entryOther)],
            uniqType: [this.uniqType, this.getValidator(GET_BASEMODEL_FIELDNAME(this).uniqType)],

            agentNo: [this.agentNo, this.getValidator(GET_BASEMODEL_FIELDNAME(this).agentNo)],
            channelId: [this.channelId, this.getValidator(GET_BASEMODEL_FIELDNAME(this).channelId)],
            shortUrl: [this.shortUrl, this.getValidator(GET_BASEMODEL_FIELDNAME(this).shortUrl)],
            ksCifToken: [this.ksCifToken, this.getValidator(GET_BASEMODEL_FIELDNAME(this).ksCifToken)],
            promoChannelID: [this.promoChannelID, this.getValidator(GET_BASEMODEL_FIELDNAME(this).promoChannelID)],
            promoDepart: [this.promoDepart, this.getValidator(GET_BASEMODEL_FIELDNAME(this).promoDepart)],
            promoMember: [this.promoMember, this.getValidator(GET_BASEMODEL_FIELDNAME(this).promoMember)],
            termsList: [this.termsList, this.getValidator(GET_BASEMODEL_FIELDNAME(this).termsList)],
        };
    }

}