import {ValidatorFn, Validators} from "@angular/forms";
import { FormValidator, FormValidatorBuilder } from "../validator/form-validator";
import {GET_BASEMODEL_FIELDNAME} from "../app.constants";
import { BaseModel } from '../base/base.model';
import {TermsModel} from "./term.model";

export class MidVerifyModel extends BaseModel implements FormValidator<any> {


    // 客戶電話號碼
    phone: string;

    // 電信業者代碼
    operator: string;

    // 授權條款
    midVerifyTerms: boolean;

    // 條款
    termsList: TermsModel[];

    //回到上一頁
    backAction: boolean;

    public getValidator( name: string): ValidatorFn | ValidatorFn[] | null {
        switch (name) {
            case 'phone':
                return [FormValidatorBuilder.required("請填寫行動電話", "行動電話"), FormValidatorBuilder.mobileValidator('行動電話')];
            case 'operator':
                return [FormValidatorBuilder.required("請選擇電信業者代碼", "電信業者代碼")];
            case 'midVerifyTerms':
                return [FormValidatorBuilder.required("請勾選使用授權條款", "使用授權條款")];
            case 'termsList':
                return [Validators.nullValidator];
        }
        return null;
    }

    public getValidators(): any {
        return {
            phone: [this.phone, this.getValidator(GET_BASEMODEL_FIELDNAME(this).phone)],
            operator: [this.operator, this.getValidator(GET_BASEMODEL_FIELDNAME(this).operator)],
            midVerifyTerms: [this.midVerifyTerms, this.getValidator(GET_BASEMODEL_FIELDNAME(this).midVerifyTerms)],
            termsList: [this.termsList, this.getValidator(GET_BASEMODEL_FIELDNAME(this).termsList)],
            backAction: [this.backAction, this.getValidator(GET_BASEMODEL_FIELDNAME(this).backAction)],
        };
    }

}
