export interface PhotoView {
    uniqId: string; // 暫時先給
    idno: string;   // 暫時先給
    stype: string;
    base64Image: string | ArrayBuffer;
    subSerial?: number;
    online: number;
    prodType: string;
    unitId: string;
    secondIdType?: string;
}
export interface IDCardResponse {
    waterMarkImage: string;
    name: string;
    idCard_Issue_DT: string;
    idCard_Issue_Type: string;
    idCard_Issue_City: string;
    householdAddr1: string;
    householdAddr2: string;
    householdAddr3: string;
    householdAddr4: string;
    householdZipCode: string;
    marriage: string;
    subSerial: number;
    birthplace: string;
    secCardNumber: string;
    secCardType: string;
}
export interface OtherDocumentResponse {
    waterMarkImage: string;
    subSerial: number;
    stype?: string;
}
export interface DocumentResult {
    name: string;
    image: string | ArrayBuffer;
    title: string;
    css?: any;
    serial?: number;
}
export interface UserPhotoData {
    uniqId: string;
    stype: string;
    status: string;
}


export interface UserPhotoCheck {
    status: string;
    types: Array<number>;
}

export interface UserPhotoVO {
    uniqId: string;
    stype: number;
    prodType: string;
    subSerial?: number;
    imageBig?: string | ArrayBuffer;
    imageSmall?: string | ArrayBuffer;
    productId: string;
    online: number;

}
