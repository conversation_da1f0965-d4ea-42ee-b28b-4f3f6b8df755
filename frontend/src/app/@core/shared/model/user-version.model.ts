import { BaseModel } from '../base/base.model';
import { FormValidator, FormValidatorBuilder } from '../validator/form-validator';
import { ValidatorFn, Validators } from '@angular/forms';
import { GET_BASEMODEL_FIELDNAME } from '../app.constants';

export class UserVersionModel extends BaseModel implements FormValidator<UserVersionModel> {

    //使用者的瀏覽器名稱
    browserName: string;
    //使用者的瀏覽器版本
    browserVersion: string;
    //使用者的系統名稱
    mobileDeviceName: string;
    //使用者的系統版本
    mobileDeviceVersion: string;
    //使用者是否使用手機端
    isMobile: boolean;

    public getValidator( name: string): ValidatorFn | ValidatorFn[] | null {
        switch (name) {
            case 'browserName':
                return [Validators.nullValidator];
            case 'browserVersion':
                return [Validators.nullValidator];
            case 'mobileDeviceName':
                return [Validators.nullValidator];
            case 'mobileDeviceVersion':
                return [Validators.nullValidator];
            case 'isMobile':
                return [Validators.nullValidator];
        }
        return null;
    }

    public getValidators(): any {
        return {
            browserName: [this.browserName, this.getValidator(GET_BASEMODEL_FIELDNAME(this).browserName)],
            browserVersion: [this.browserVersion, this.getValidator(GET_BASEMODEL_FIELDNAME(this).browserVersion)],
            mobileDeviceName: [this.mobileDeviceName, this.getValidator(GET_BASEMODEL_FIELDNAME(this).mobileDeviceName)],
            mobileDeviceVersion: [this.mobileDeviceVersion, this.getValidator(GET_BASEMODEL_FIELDNAME(this).mobileDeviceVersion)],
            isMobile: [this.isMobile, this.getValidator(GET_BASEMODEL_FIELDNAME(this).isMobile)]
        };
    }

}
