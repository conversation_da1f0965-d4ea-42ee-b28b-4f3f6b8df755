
/**
 * ValidReq給Api參數
 */
export class ValidReq {
    key: string = '';
    name: string = '';
    errorMessage: string = '';
    errorMessage2: string = '';
    errorMessage3: string = '';
    value: ValidParams | string  ;
    valid: string[] = [''];
    lowerLimit: number = 0;
    upperLimit: number = 0;
    type:string = '';
    start:number = 0;
    end:number = 0;
    uniqId:string = '';
    pageId:string = '';
}

/**
 * InvalidMessageDto後端回傳的物件內容
 */
export class InvalidMessageDto {
    error: string = '';
    message: string = '';
    name: string = '';
    validtion: string = '';
}


/**
 * 後端檢核回傳物件名稱
 */
export class ValidResp {
    invalidMessageDto: InvalidMessageDto = new InvalidMessageDto();
}

/**
 * ValidParams參數
 */
export class ValidParams {
    validArray: Array<string> = [''];
    validName: string = '';
    validErrorMessage: string = '';
    validErrorMessage2: string = '';
    validErrorMessage3: string = '';
    checkCellPhone: boolean = false;
    lowerLimit: number = 0;
    upperLimit: number = 0;
    type:string = '';
    start:number = 0;
    end:number = 0;
}
