export class RoutingModel<T> {
    // public id: string;
    public sourceId: string;
    public targetId: string;
    // public token: string;
    public module: string;
    public data: T;

    public currentStep: string;
    public totalStep: string;
    public frontEndBack: boolean = false;

    constructor(id?: string, module?: string, targetId?: string, data?: any) {
        this.sourceId = id;
        this.module = module;
        this.targetId = targetId;
        this.data = data;
    }

}
