import { ValidatorFn } from '@angular/forms';
import { FormValidator, FormValidatorBuilder } from '../../@core/shared/validator/form-validator';
import { BaseModel } from 'src/app/@core/shared/base/base.model';
import { GET_BASEMODEL_FIELDNAME } from '../../@core/shared/app.constants';

export class AIOCaseDataVO extends BaseModel implements FormValidator<AIOCaseDataVO> {

    /**
     * 案件編號(數位信貸) (caseNo)   範例: KGIBYYYYMMDD000001
     */
    uniqId: string = '';

    /**
     * 身分證字號或護照號碼
     */
    idno: string = '';

    /**
     * 生日
     */
    birthday: string = '';

    /**
     * 23. 客戶填入之行動電話
     */
    phone: string = '';

    /**
     * 他行帳戶驗身的手機
     */
    pcode2566Phone: string = '';

    /**
     * 產品別:個人貸款
     */
    productId_pl: string = '';

    /**
     * 產品別:循環貸款
     */
    productId_rpl: string = '';

    /**
     * 產品別:信用卡
     */
    productId_cc: string = '';

    /**
     * 產品別:數位帳戶
     */
    productId_d3: string = '';

    /**
     * 分期型貸款期間
     */
    peroid_PL: string = '';

    /**
     * 額度型貸款期間
     */
    peroid_RPL: string = '';

    /**
     * 分期型貸款金額    (單位：萬元)
     */
    apyAmount_PL: string = '';

    /**
     * 貸款類型-分期型
     */
    loanType: string = '';


    /**
     * 貸款類型-額度型
     */
    quotaType: string = '';

    /**
     * 額度型貸款產品名稱
     */
    quotaProductId: string = '';

    /**
     * 額度型貸款金額
     */
    apyAmount_RPL: string = '';


    /**
     * 額度型貸款用途
     */
    quotaPurpose: string = '';

    /**
     * 貸款類型-分期型+額度型
     */
    installmentQuotaType: string = '';

    /** 首刷禮  **/
    firstPresent: string = '';

    /**
     * 原信用卡專案 使用的欄位 updateEmailAndName
     */
    chtName: string = '';

    /**
     * 原信用卡專案 使用的欄位
     */
    engName: string = '';

    ccEngName: string = '';
    /**
     * 姓名    以下欄位皆為對應 WEB API 欄位
     */
//       customerName: string = '';

//    /**
//     * 英文姓名
//     */
//       englishName: string = '';

    /**
     * E-mail 網址
     */
    emailAddress: string = '';

    isEmailVerify: string = '';

    /**
     * 居住地址相關 (住家)
     */
    homeAddrZipCode: string = '';
    homeAddr: string = '';
    /**  居住電話區碼  */
    homeTelArea: string = '';
    /**  居住縣市區域  */
    homeAddrArea: string = '';

    homeTel: string = '';

    /**
     * 通訊地址相關
     */
    commAddrZipCode: string = '';
    commAddr: string = '';
    commAddrArea: string = '';

    /**
     * 戶藉地址相關
     */
    resTelArea: string = '';
    resAddress: string = '';
    ResAddrZipCode: string = '';
    resAddr: string = '';
    resTel: string = '';


    /**
     * 案件編號(APS)    APS 的案件編號 (ADD_NEW_CASE 產的)
     */
    caseNoWeb: string = '';


    /**
     * 建立新的案例編號
     */
    createNewCaseCaseNo: string = '';

    /**
     * 申請代碼
     */
    applyCode: string = '';

    /** APS 真正的案件編號    APS 起案之後會來新 */

    /**
     * APS 真正的案件編號    APS 起案之後會來新
     */

    caseNoAps: string = '';

    /**
     * 現金卡系統案件編號
     */
    gmCardNo: string = '';


    /**
     * 是否預核    1:預核 2:非預核
     */
    isPreAudit: string = '';

    /**
     * 產品名稱 改為多種 productId 搭配 uniqType 判斷
     * uniqType:01-貸款，02-卡加貸，03-立約，05-信用卡，06-aio-cc，07-aio-loan，08-aio-d3，09-aio-loan-d3，10-aio-loan-cc，11-aio-cc-d3，12-aio-loan-cc-d3，13-d3
     */
    productId: string = '';

    /**
     * 業務員部門
     */
    agentDepart: string = '';

    // /**
    //  * 發動起案時間(產生 decisionCaseNo 的時間)
    //  */
    // addNewCaseTime: Date = new Date();

    /**
     * 業務員代號
     */
    agentNo: string = '';

    /**
     * 決策平台編號 透過 api/KGI/ADD_LOAN_CASE 取得的編號，下列決策平台 API都需要用這個編號做後續發查用
     */
    decisionCaseNo: string = '';

    /**
     * 有上傳ID
     */
    hasUploadId: string = '';

    /**
     * 是否填寫完申請書
     */
    hasApplication: string = '';

    /**
     * 非約定轉帳功能
     */
    nonAgreeTransfer: boolean = false;

    /**
     * 跨國交易功能
     */
    internationalTrade: boolean = false;

    // 貸款-個人頁, 數位帳戶 - 個人頁


    /**
     * 性別    1:男 2:女
     */
    gender: string = '';


    // 貸款-工作頁, 數位帳戶 - 工作頁
    /**
     * 公司名稱
     */
    corpName: string = '';

    /**
     * 公司名稱（文字欄位）
     */
    corpNameText: string = '';

    /**
     * 公司統編
     */
    corpTaxNum: string = '';

    /**
     * 職業類別
     */
    occupation: string = '';

    /**
     * 職稱
     */
//       workTitle: string = '';

    /**
     * 到職年
     */
    year: string = '';

    /**
     * 到職月
     */
    month: string = '';

    /**
     * 到職日期
     */
    onBoardDate: string = '';

    /**
     * 公司電話區碼
     */
    corpTelArea: string = '';
    corpAddrZipCode: string = '';
    corpAddr: string = '';
    /** 公司地址區域  */
    corpAddrArea: string = '';

    /**
     * 公司縣市
     */
    companyCounties: string = '';

    /**
     * 公司地址
     */
    companyAddress: string = '';


    /**    公司分機      **/
    corpTelExten: string = '';

    /**
     * 公司電話號碼
     */
    corpTel: string = '';

    corpDepart: string = '';

    /**
     * 職稱
     */
    jobTitle: string = '';

    /**
     * 職稱  (D3/SAL 其他選項時填寫)
     */
    jobTitleText: string = '';


    // 貸款-其他頁, 數位帳戶 - 其他頁

    /**
     * 教育程度
     */
    education: string = '';

//    /**
//     * 畢業國小
//     */
//       graduatedElemSch: string = '';

    /**
     * 婚姻狀況
     */
    marriage: string = '';

    /**
     * 不動產狀態
     */
    estateType: string = '';

    /**
     * 是否有重大傷病
     */
    injury: string = '';

    // 貸款-驗身

    /**
     * 驗身-信用卡銀行別
     */
    deposit: string = '';

    /**
     * 驗身-信用卡卡號
     */
    creditCard: string = '';

    /**
     * 驗身-存款帳戶銀行別
     */
    depositBank: string = '';

    /**
     * 驗身-銀行帳號
     */
    bankAccount: string = '';

    /**
     * 驗身-留存銀行電話號碼
     */
    bankPhone: string = '';

    /**
     * 驗身-信用卡卡號
     */
    creditCardNumber: string = '';

    /**
     * 驗身-信用卡有效期日期
     */
    validityPeriod: string = '';

    /**
     * 身份證換發日期
     */
    idCardDate: string = '';

    /**
     * 身份證換發地點
     */
    idCardLocation: string = '';

    idCardRecord: string = '';

    /**
     * 身份證縣市
     */
    resAddrZipCode: string = '';

    /**
     * 身份證發證地點
     */
    idCardTownship: string = '';

    /**
     * 身份證出生地
     */
    birthplace: string = '';


    /**
     * 婚姻狀況
     */
    idCardMarriage: string = '';

    // 貸款-財力證明上傳

    /**
     * 財力證明種類
     */
    stype: string = '';

    /**
     * 財力證明圖片
     */
    financialBase64Image: string = '';

    /**
     * 財力編號
     */
    subSerial: string = '';

    /**
     * 副狀態 (LOAN)
     */
    substatus: string = '';


    // 貸款-其他設定關係人

    /**
     * 關係人1-配偶姓名
     */
    spouseName: string = '';

    /**
     * 關係人1-配偶身分證字號
     */
    spouseIdno: string = '';

    /**
     * 若您或您的配偶搭任「企業負責人」
     */
    ceo: string = '';

    /**
     * 關係人2-二親等以內血親
     */
    twoBloodRelatives: string = '';

    /**
     * 關係人2-二親等以內血親姓名
     */
    twoBloodRelativesName: string = '';

    /**
     * 關係人2-二親等以內血親身分證字號
     */
    twoBloodRelativesIdno: string = '';

    //數位帳戶-申請書

    /**
     * 本次開立帳戶-信託帳戶
     */
    trustAccount: string = '';

    /**
     * 銀行分行選擇-縣市
     */
    bankCountiesCities: string = '';

    /**
     * 銀行分行選擇-區域
     */
    bankArea: string = '';

    /**
     * 銀行分行選擇-分行名稱
     */
    bankBranchName: string = '';

    /**
     * 銀行分行選擇-開戶目的
     */
    purpose_D3: string = '';

    /**
     * 證券項目-交割戶
     */
    setSecuritiesDelivery: string = '';

    /** 證券分行代碼  */
    securitiesCode: string = '';

    /**
     * 證券項目-複委託
     */
    setSubBrokerage: string = '';

    /**
     * 證券分公司選擇-縣市
     */
    securitiesCountiesCities: string = '';

    /**
     * 證券分公司選擇-區域
     */
    securitiesArea: string = '';

    /** 證券分行名稱  */
    securitiesName: string = '';

    /**  證券分行地址 */
    securitiesAddr: string = '';

    /**  證券分行地址區碼*/
    securitiesAddrZipCode: string = '';



    //數位帳戶-身分驗證

    /**
     * 轉介單位
     */
    referralUnit: string = '';

    /**
     * 轉介員編
     */
    referralEditor: string = '';

    //數位帳戶-其他設定

    /**
     * 設定網路銀行-使用者代號
     */
    userID: string = '';

    /**
     * 設定網路銀行-網路銀行密碼
     */
    interneBankPwd: string = '';

    /**
     * 設定網路銀行-確認網路銀行密碼
     */
    confirmInterneBankPwd: string = '';

    /**
     * 設定電話銀行密碼-電話銀行密碼
     */
    phoneBankPwd: string = '';

    /**
     * 設定電話銀行密碼-確認電話銀行密碼
     */
    confirmPhoneBankPwd: string = '';

    //信用卡-申請書
    /**
     * 申辦卡別
     *
     * 未來需要拿掉 應該舊使用productId 即可
     */
    applyForCard: string = '';

    /**
     * 獨享優惠
     */
    exclusiveOffer: string;

    //條款
    /** 我已閱讀並同意下列條款及告知事項 */
    ch1: boolean = false;

    /** 同意使用與貴行往來之資料申辦貸款 */
    ch2: boolean = false;

    /** 我已閱讀並同意貴行查詢聯徵信用資訊 */
    ch3: boolean = false;

    /**
     * 金控共銷條款
     */
    ch4: boolean = false;

    /**
     * 第三方共銷條款
     */
    ch5: boolean = false;

    /** 申請凱基人壽保費12期分期0利率
     * 新戶首年可同享1%刷卡金回饋 */
    ch6: boolean = false;

    /**  本人聲明未持有美國籍且非為其他地區稅務居民身份 */
    ch7: boolean = false;

    ch8: boolean = false;


    /**
     * 主動推介(Agreement)
     */
    ch9: boolean = false;

    /**
     * 蒐集、處理及利用個人資料告知書
     */
    ch12: boolean = false;

    //------------------------------


    /**
     * 設定網路銀行-使用者代號
     */
    ipAddress: string = '';


    /**
     * 專案代號
     */
    prjCode: string = '';

    /**
     * PL專案代號
     */
    prjCodePl: string = '';

    /**
     * RPL專案代號
     */
    prjCodeRpl: string = '';

    /**
     * 預審案件編號
     */
    caseNoEst: string = '';

    /**
     * 客戶勾選與我聯絡    1:不勾選 2:勾選
     */
    ContactMe: string = '';

    /**
     * 是否送給 TM    0:還沒 1:已送出
     */
    ContactMeFlag: string = '';

    /**
     * 圖檔是否上傳影像    0:還沒 1:已送出
     */
    addPhotoFlag: string = '';

    /**
     * 職業類別 Aps 檢核結果
     */
    occupatioFlag: string = '';

    /**
     * 是否為重要客戶    Y:重要客戶 N:非重要客戶
     */
    importantCust: string = '';

    /**
     * AML 回傳結果 (By AmlResult)
     */
//       aMLFlag: string = '';


    /**
     * 使用者類別    0: 新戶 1:信用卡戶 2:存款戶 3:純貸款戶
     */
    userType: string = '';


    /**
     * 24. DATA_TYPE 客戶類型(1.信用卡2.現金卡3.存款4.貸款) 目的是給apply verify 簡訊驗證顯示用戶於本行有效行動電話之服務名稱
     */
    stempDataType: string = '';


    // /**
    //  * 詳細寄送時間
    //  */
    // sendCustInfoTime: Date = new Date();


    // /**
    //  * D3 詳細寄送時間 (ED3_CaseData SendedTime)
    //  */
    // d3SentTime: Date = new Date();


    /**
     * 有qryAml資料  可能還要找清楚他的定義
     */
    hasQryAml: string = '';


    /**
     * 是否為薪轉戶 判斷客戶是否為薪轉戶(徵審) Y 薪轉戶 N 非薪轉戶
     */
    checkSalaryAccount: string = '';

    /**
     * 是否為信用卡戶 Y/N
     */
    checkCreditcardAccount: string = '';

    /**
     * 是否為現金卡戶 Y/N
     */
    checkCashcardAccount: string = '';

    /**
     * 是否為存款戶 Y/N
     */
    checkKgibankAccount: string = '';

    /**
     * 是否為貸款戶 Y/N
     */
    checkLoanAccount: string = '';


    /**
     * LoanPersonalDataResult (form AIOConfirm.java NO. 272)
     */
    loanPersonalDataResult: string = '';

    /**
     * 行內是否有舊ID 00:無, 01:有
     */
    hasOldIdImg: string = '';

    /**
     * 7. 舊ID影編代號列表
     */
    oldIdImgNoList: string = '';

    /**
     * 行內是否有舊財力 00:無, 01:有
     */
    hasOldFinaImg: string = '';

    /**
     * 4. 舊財力影編代號列表
     */
    oldFinaImgNoList: string = '';

    /**
     * 舊ID可否使用 00:不可, 01:可
     */
    canUseOldIdImg: string = '';


    /**
     * 是否已通過戶役政 00:否, 01:是
     */
    hasQryIdInfo: string = '';

    /**
     * 是否已通過Z07 00:否, 01:是
     */
    hasQryZ07: string = '';

    /**
     * 是否已產生KYC表 00:否, 01:是
     */
    hasFinishKyc: string = '';

    /**
     * 是否已通過檢核利害關係人 00:否, 01:是
     */
    hasNoRelp: string = '';

    /**
     * 是否為免財力證明 00:否, 01:是
     */
    applyWithoutFina: string = '';

    /**
     * 理債平台的前一案件編號
     */
    caseNoApsPrev: string = '';


    /**
     * http 碼
     */
    httpCode: string = '';

    /**
     * http 訊息
     */
    httpMessage: string = '';

    /**
     * rdCode:rdMessage => RS2007:資料不存在: string = '';
     * RS3811:連結機關作業管理資料未建: string = '';
     * RS7007:網路傳送錯誤: string = ''; RS7009:查詢作業完成
     */
    rdCode: string = '';

    /**
     *
     */
    rdMessage: string = '';

    /**
     * 查驗總結果
     */
    responseData: string = '';

    /**
     *
     */
    dataType: string = '';


    /**
     *
     */
    checkIdCardApply: string = '';


    /**
     * 01    案件資料不存在
     * 02    發證日期資料錯誤
     * 03    發證地點資料錯誤
     * 04    領補換類別資料錯誤
     * 99      非預期性系統異常
     */
    checkCode: string = '';

    /**
     * (2)    ERR_MSG：
     * A.    當系統發生非預期性異常時，CHECK_CODE=99，此參數存放錯誤說明(空白代表沒有錯誤)
     */
    errMsg: string = '';

    corpClass: string = '';

    corpType: string = '';

    title: string = '';

    /**
     * 年收入
     */
    yearlyIncome: string = '';

    // 原loan_personal_data_Time
    // loanPersonalDataTime: Date = new Date();

    /**
     * 驗證狀態
     */
    verifyStatus: string = '';

    /**
     * 驗證類型
     */
    verifyType: string = '';

    /**
     * 信用卡卡號
     */
    cardNo: string = '';

    /**
     * 信用卡時間
     */
    cardTime: Date = new Date();

    /**
     * 銀行編號
     */
    bankId: string = '';

    /**
     * 戶口號碼
     */
    accountNo: string = '';

    /**
     * 預訂分行 ID
     */
    bookingBranchId: string = '';

    /**
     * 預定日期
     */
    bookingDate: Date = new Date();

    // /**
    //  * 預訂時間
    //  */
    // bookingTime: Date = new Date();

    /**
     * 錯誤計數
     */
    errorCount: number = 0;

    /**
     * 錯誤計數
     */
    ncccErrorCount: number = 0;

    /**
     * pcode2566ErrorCount
     */
    pcode2566ErrorCount: number = 0;

    /**
     * 錯誤消息
     */
    errorMsg: string = '';

    /**
     * 我的卡
     */
    myCard: string = '';


    /**
     * 原信用卡專案 使用的欄位
     */
    promoDepart1: string = '';

    /**
     * 原信用卡專案 使用的欄位
     */
    promoMember1: string = '';

    /**
     * 原信用卡專案 使用的欄位
     */
    promoDepart2: string = '';

    /**
     * 原信用卡專案 使用的欄位
     */
    promoMember2: string = '';


    /**
     * 原信用卡專案 使用的欄位
     */
    creditNoAps: string = '';

    /**
     * 原信用卡專案 使用的欄位
     */
    channelId: string = '';


    /**
     * 原信用卡專案 使用的欄位
     */
    deviceId: string = '';


    /**
     * 原信用卡專案 使用的欄位
     */
    pdfContent: string = '';

//        /**
//         * 原信用卡專案 使用的欄位
//         * */
//           authType: string = '';

    /**
     * 畢業國小
     */
    elementarySchoolName: string = '';

    /**
     * 客戶身分證字號
     */
    customerId: string = '';

    /**
     * 數位案件編號
     */
    dgtPlatformNo: string = '';

    /**
     * 同一關係人親屬資料
     */
    dATA_LIST: string = '';

    /**
     * 同一關係人企業資料
     */
    dATA_COMP_LIST: string = '';


    /**    是否為高風險   **/
    AmlResult: string = '';

    // amlTime: Date = new Date();

    hasUploadFina: string = '';

    jobStatus: string = '';

    /**
     * 數三
     **/
    accountType: string = '';

    subCategory: string = '';

    cLToken: string = '';

    sendType: string = '';

    sendTypeD3: string = '';

    purpose: string = '';

    /**
     * uniqType:01-貸款，02-卡加貸，03-立約，05-信用卡，06-aio-cc，07-aio-loan，08-aio-d3，09-aio-loan-d3，10-aio-loan-cc，11-aio-cc-d3，12-aio-loan-cc-d3，13-d3
     */
    uniqType: string = '';

    promoDepart: string = '';

    promoMember: string = '';

    /**
     * 利率風險
     */
    cddRateRisk: string = '';

    /**
     *
     */
    z07Html: string = '';

    /**
     *
     */
    z07Result: string = '';

    /**
     *
     */
    // z07Time: Date = new Date();

    /**
     * 檢查結果
     */
    checkRelpResult: string = '';

    // /**
    //  * 檢查回复時間
    //  */
    // checkRelpTime: Date = new Date();

    /**
     * 信息源_CDE
     */
    infosourceCde: string = '';

    // /**
    //  * 圖片上傳系統完成時間
    //  */
    // imgSysCaseCompleteTime: Date = new Date();

    /**
     * 圖片上傳系統完成
     */
    imgSysCaseComplete: string = '';

    /**
     * 帳單地址區碼
     */
    billAddrZipCode: string = '';

    billAddr: string = '';

    /**
     *
     */
    sendCardAddrZipCode: string = '';

    /**
     *
     */
    sendCardAddr: string = '';


    homeType: string = '';

    homeOwner: string = '';


    //FIXME:modify 還未加  start
    /**
     * MBC驗證是否通過
     */
    mbcValid: boolean = false;

    mBCCheckCode: string = '';

    mBCMessage: string = '';

    chtAgree: boolean = false;

    chtAgreeTime: Date = new Date();

    isfinal: string = '';

    transDepart: string = '';

    transMember: string = '';


    //FIXME:modify 還未加  end


    itemId: string = '';

    comment: string = '';

    /**
     * 拒貸90天內案件註記
     */
    rejectFlag: string = '';

    /**
     * 90天內是否改動過手機號碼註記
     */
    isChanged: string = '';

    /**
     * 分期型貸款用途
     */
    purpose_PL: string = '';

    /**
     * 分期型貸款用途 如果其他要再填寫原因
     */
    purposePLOtherText: string = '';

    /**
     * 資金用途其他
     */
    pPurposeName: string = '';

    /**
     * 專案代號
     */
    PJ: string = '';

    /**
     * 電文 CHECK_NO_FINPF 的 MEMO 欄位值
     */
    checkNoFinpfMemo: string = '';

    /**
     * 電文 CHECK_NO_FINPF 的 YEARLY_INCOME 欄位值
     */
    noFinpfYearlyIncome: string = '';

    /**
     * 電文 CHECK_NO_FINPF 的 RESULT_CODE 欄位值
     */
    noFinpfResultCode: string = '';

    creditLimit: string = '';

    /**
     * 客戶身分證字號
     */
    CUSTOMER_ID: string = '';


    eOpenIDType: string = '';

    authorizeToallCorp: string = '';

    /**
     * 第三人共銷
     */
    authorizeOther: string = '';

    hasFin: string = '';

    /**
     * 第三人共銷
     */
    dataUse: string = '';

    /**
     * 第三方共銷
     */
    otherDataUse: string = '';

    /**
     * OCR 身份證字號
     */
    ocrFrontIdno: string = '';
    /**
     * 身份證字號
     */
    ocrFrontBypassId: string = '';
    /**
     *
     */
    ocrFrontRspCode: string = '';
    /**
     * 姓名
     */
    ocrFrontName: string = '';


    /**
     * 發證日期eeeMMdd
     */
    ocrFrontIdcardIssueDt: string = '';
    /**
     * 發證類型
     */
    ocrFrontIdcardIssueType: string = '';
    /**
     * 發證縣市
     */
    ocrFrontIdCardIssueCity: string = '';

    /**
     * 婚姻:未婚/已婚
     */
    ocrBackMarriage: string = '';

    //TODO:先補的欄位 未來移除 START
    /**
     * 先補的欄位 未來移除
     */
    stagLoanAmount: string = '';

    /**  流程(臨櫃/數三)  */
    process: string = '';


    /**
     * 薪資轉帳
     */
    productId_sal: string = '';
    /**
     * 個人貸款狀態
     */
    status_pl: string = '';

    /**
     * 循環貸款狀態
     */
    status_rpl: string = '';

    /**
     * 信用卡狀態
     */
    status_cc: string = '';

    /**
     * 數位帳戶狀態
     */
    status_d3: string = '';

    /**
     * 薪資轉帳狀態
     */
    status_sal: string = '';

    /**
     * 申辦流程名稱
     */
    pProductType: string = '';

    /**
     * 申辦流程名稱(這個目前才取得到資料)
     */
    pproductType: string = '';

    /**
     * 發查 取得案件申請評分等..資訊
     */
    dgtcaseResult: string = '';

    // /**
    //  * 發查 取得案件申請評分等..資訊 的時間
    //  */
    // dgtCaseTime: string = '';

    /**
     * 發查 取得案件申請評分等..資訊
     */
    isCaseIntegrated: string = '';

    /**
     * 發查 取得案件申請評分等..資訊
     */
    cAbnormalReason: string = '';

    /**
     * 發查 取得案件申請評分等..資訊
     */
    subcaseType: string = '';

    /**
     * 發查聯徵成功 Y/N
     */
    jcicOk: string = '';

    // /**
    //  * 發查聯徵的時間
    //  */
    // jcicTime: Date = new Date();

    // /** 更新時間 */
    // updateTime: Date = new Date();
    //
    // /** 新增時間  */
    // createTime: Date = new Date();


    /**
     * D3凱基人壽用
     */
    isPayer: string = '';

    /**
     * 案件狀態
     */
    status: string = '';

    /**
     * 案件狀態(CC)
     */
    loanStatus: string = '';

    /**
     * 案件狀態(CC)
     */
    ccStatus: string = '';

    /**
     * 案件狀態(D3)
     */
    d3Status: string = '';

    /**
     * 案件狀態(SAL)
     */
    salStatus: string = '';

    /**
     * APS 傳來的案件狀態 (CC/LOAN會互打)
     */
    apsStatus: string = '';

    /**
     * APS 傳來的案件狀態 (LOAN)
     */
    LoanApsStatus: string = '';

    /**
     * APS 傳來的案件狀態 (CC)
     */
    CcApsStatus: string = '';

    /**
     * 舊財力可否使用 00:不可, 01:可
     */
    canUseOldFinaImg: string = '';

    //FIXME: CreditCaseData 裡面有的欄位 start
    isCLCreditCard: string = '';

    isCheckCLInstallment: string = '';

    installmentMonth: number = 0;

    installmentAgreeTime: string = '';
    //FIXME: CreditCaseData 裡面有的欄位 end


    //FIXME: ED3_CaseData 裡面有的欄位 start
    isTwTaxResident: string = '';

    promoChannelID: string = '';

    /** 戶籍地址區域 / 身份證鄉鎮市區 */
    resAddrArea: string = '';

    branchID: string = '';

    autVer: string = '';

    breakPointPage: string = '';

    // sendedTime: Date = new Date();

    /**  結果時間 */
    endTime: Date = new Date();

    routeID: string = '';

    channelStartPoint: Date = new Date();

    /**  是否申請信託 */
    isTrust: string = '';

    /** 是否申請外幣 */
    isForeign: string = '';

    /**
     * 帳戶資金來源
     */
    incomeSource: string = '';

    eleSchool: string = '';

    tradeProduct: string= '';

    /** 預計與本行往來之業務(存代號)  */
    preTradeProduct: string= '';

    autOthVer: string= '';

    /** D3 複委託幣別  */
    subBroBinKind: string= '';

    /** D3 是否同意查詢存款帳戶資料 */
    isQryBalance: string = '';

    //會組合成-> homeAddrArea (tempHomeCountry + tempHomeArea)
    // 住家城市 前端篩選使用
    tempHomeCountry: string = '';
    tempHomeArea: string = '';

    //會組合成-> commAddrArea  (tempCommCountry + tempCommArea)
    // 通訊城市 前端篩選使用
    tempCommCountry: string = '';
    tempCommArea: string = '';

    //會組合成-> corpAddrArea  (tempCorpCountry + tempCorpArea)
    // 通訊城市 前端篩選使用
    tempCorpCountry: string = '';
    tempCorpArea: string = '';

    // 住家電話號碼, 02-22151122
    tempHomeTel: string= '';
    // 戶籍電話號碼, 02-22151122
    tempResTel: string= '';
    // 公司電話號碼, 02-22151122
    tempCorpTel: string = '';

    //sal 台幣存摺 電子:N/紙本:Y
    passBookTw: string= '';
    //sal 外幣存摺 電子:N/紙本:Y
    passBookFore: string= '';

    termsFincShrRisk: boolean = false; // 金融控股公司及參與資料共享公司間為辨識風險

    termsFincShrAdv: boolean = false; // 金融控股公司及參與資料共享公司間為提升客戶便利性

    // 判斷臨櫃出示
    proCabinet: boolean = false;

    /**
     * 產品別:房屋貸款
     */
    productId_hl:string = '';
    /**
     * 房屋貸款金額
     */
    apyAmount_HL:string = '';
    /**
     * 房屋貸款期間
     */
    peroid_HL:string = '';
    /**
     * 房屋貸款期間
     */
    peroid_grace:string = '';
    /**
     * 房屋貸款用途
     */
    purpose_HL:string = '';
    /**
     * 房屋貸款用途-選擇其他要再填寫原因
     */
    purposeHLOtherText:string = '';
    /**
     * 房屋使用狀況
     */
    useStatus_HL:string = '';

    /**
     * 房屋貸款狀態
     */
    status_hl: string

    chooseProduct:string = '';
    /**
     * 房屋貸款類型
     */
    relationshipWithApplicant:string = '';
    /**
     * 通訊電話號碼
     */
    commTel:string = '';
    /**
     * 通訊電話區碼
     */
    commTelArea:string = '';
    /**
     * 借款人案編
     */
    borrowUniqId:string = '';
    /**
     * 房屋地址縣市
     */
    addressCity_HL:string = '';
    /**
     * 房屋地址
     */
    address_HL:string = '';
    /**
     * 房屋地址鄉鎮區
     */
    addressArea_HL:string = '';
    /**
     * 房屋地址完整地址
     */
    addressWhole_HL:string = '';
    /**
     * 房屋地址區碼
     */
    houseAddrZipCode:string = '';
    /**
     * 房屋所有權人
     */
    ownership_HL:string = '';
    /**
     * 要往來服務據點
     */
    serviceBase:string = '';

    /**
     * 房屋使用狀況其他
     */
    useStatusText_HL:string = '';
    /**
     * 房屋貸款-保證人
     */
    guarantor_HL:string = '';
    /**
     * 房屋貸款-還款方式
     */
    repayment_HL:string = '';
    /**
     * 房屋貸款-貸款用途其他文字
     */
    purpose_HLText:string = '';

    tempCommTel:string = '';

    // 投資理財
    invest: string = '';

    /**
     * ******** WT20240416003 Maggie
     * 新增個人資產總額欄位
     */
    totalPersonAssets: string = '';

    /**
     * Email驗證註記
     */
     mailVerifyed:string = '';
    /**
     * Email失聯註記
     */
    uncfEmail:string = '';

    // 信用卡群組
    cardGroup: string= '';

    // 台幣帳戶
    accountTw: string= '';

    // 台幣帳戶清單
    accountTwList: string= '';

    // 外幣帳戶
    accountFore: string= '';

    // 外幣帳戶清單
    accountForeList: string= '';

    // 扣繳金額 (1:應繳總金額, 2:最低應繳金額)
    withholdingAmount: string= '';

    // 同意變更原授扣設定
    awChangeAgreement: boolean;

    redirectUrl: string;

    public static getValidator( name: string): ValidatorFn | ValidatorFn[] | null {
        switch (name) {
            case 'chtName':
                return [FormValidatorBuilder.required("請輸入您身分證上的完整姓名", "姓名")];
            case 'onBoardDate':
                return [FormValidatorBuilder.onBoardDateValidator('到職日請勿超過現在時間', "到職年月")];
            case 'engName':
                return [FormValidatorBuilder.required("請輸入英文姓名", "英文姓名"),FormValidatorBuilder.checkEngName("請輸入英文姓名", "英文姓名")];
            case 'ccEngName':
                return [FormValidatorBuilder.checkEngName("請輸入英文姓名", "英文姓名")];
            case 'emailAddress':
                return [FormValidatorBuilder.emailValidator("請輸入電子信箱", "電子信箱")];
            case 'tempHomeCountry':
                return [FormValidatorBuilder.required("請選擇住家縣市", "住家縣市")];
            case 'tempHomeArea':
                return [FormValidatorBuilder.required("請選擇住家區域", "住家區域")];
            case 'tempHomeTel':
                return  [FormValidatorBuilder.required("請輸入住家電話", "住家電話")];
            case 'homeAddrZipCode':
                return [];
            case 'homeAddrArea':
                return [FormValidatorBuilder.required("請選擇鄉鎮市區", "住家鄉鎮市區")];
            case 'homeAddr':
                return  [FormValidatorBuilder.required("請輸入住家地址", "住家詳細地址")];
            case 'homeTelArea':
                return  [];
            case 'homeTel':
                return  [];
            case 'tempCommCountry':
                return [FormValidatorBuilder.required("請選擇通訊縣市", " 通訊縣市")];
            case 'tempCommArea':
                return [FormValidatorBuilder.required("請選擇通訊區域", " 通訊區域")];
            case 'commAddrArea':
                return [FormValidatorBuilder.required("請選擇鄉鎮市區", "通訊鄉鎮市區")];
            case 'commAddr':
                return [FormValidatorBuilder.required("請輸入通訊地址", "通訊詳細地址")];
            case 'commAddrZipCode':
                return [];
            case 'tempResTel':
                return [FormValidatorBuilder.required("請輸入戶籍電話", "戶籍電話")];
            case 'resTelArea':
                return  [];
            case 'resTel':
                return  [];
            case 'occupation':
                return [FormValidatorBuilder.required("請選擇行業類別", "行業類別")];
            case 'year':
                return [FormValidatorBuilder.required("請選擇到職年", "到職年")];
            case 'month':
                return [FormValidatorBuilder.required("請選擇到職月", "到職月")];
            case 'yearlyIncome':
                return [FormValidatorBuilder.yearIncomeValidator("年收入", 20, 9999)];
            case 'tempCorpCountry':
                return [FormValidatorBuilder.required("請選擇公司縣市", "公司縣市")];
            case 'tempCorpArea':
                return [FormValidatorBuilder.required("請選擇公司區域", "公司區域")];
            case 'tempCorpTel':
                return [FormValidatorBuilder.validatorTelAreaCodeSeparate("公司電話", "公司電話",false)];
            case 'corpAddr':
                return [FormValidatorBuilder.required("請輸入公司地址", "公司地址")];
            case 'corpAddrZipCode':
                return [];
            case 'corpTelArea':
                return [];
            case 'corpTel':
                return [];
            case 'corpTelExten':
                return [];
            case 'corpName':
                return [FormValidatorBuilder.required("請輸入公司名稱", "公司名稱")];
            case 'corpNameText':
                return [];
            case 'corpTaxNum':
                return [];
            case 'education':
                return [FormValidatorBuilder.required("請選擇", "教育程度")];
            case 'elementarySchoolName':
                return [FormValidatorBuilder.required("請輸入畢業國小", "畢業國小")];
            case 'marriage':
                return [FormValidatorBuilder.required("請選擇", "婚姻")];
            case 'estateType':
                return [FormValidatorBuilder.required("請選擇", " 不動產狀態")];
            case 'incomeSource':
                return [FormValidatorBuilder.required("請選擇", " 帳戶資金來源")];
            case 'preTradeProduct':
                return [FormValidatorBuilder.required("請選擇", "預計與本行往來之業務")];
            case 'sendType':
                return [FormValidatorBuilder.required("請選擇", "帳單類型")];
            case 'sendTypeD3':
                return [FormValidatorBuilder.required("請選擇", "數位帳戶對帳單類型")];
            case 'injury':
                return  [FormValidatorBuilder.required("請選擇", "是否領有全民健康保險重大傷病證明")];
            case 'jobTitle':
                return [FormValidatorBuilder.required("請選擇職稱", "職稱")];
            case 'jobTitleText':
                return [FormValidatorBuilder.required("請輸入職稱", "職稱")];
            case 'corpAddrArea':
                return [];
            case 'passBookTw':
                return  [FormValidatorBuilder.required("請選擇臺幣存摺類型", "臺幣存摺")];
            case 'passBookFore':
                return  [FormValidatorBuilder.required("請選擇外幣存摺類型", "外幣存摺")];
            case 'termsFincShrRisk':
                return  [];
            case 'termsFincShrAdv':
                return  [];
            case 'purposePLOtherText':
                return  [];
            case 'proCabinet':
                return [];
            case 'invest':
                return  [];
            case 'accountTw':
                return  [FormValidatorBuilder.required("請選擇凱基臺幣存款帳號", "凱基臺幣存款帳號")];
            case 'accountFore':
                return  [FormValidatorBuilder.required("請選擇凱基外幣存款帳號", "凱基外幣存款帳號")];
            case 'withholdingAmount':
                return  [FormValidatorBuilder.required("請選擇授權扣繳金額", "授權扣繳金額")];
            case 'totalPersonAssets':
                return [];
        }
        return null;
    }

    public getValidators(): any {
        return {
            chtName: [this.chtName, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).chtName)],
            engName: [this.engName, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).engName)],
            ccEngName: [this.ccEngName, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).ccEngName)],
            emailAddress: [this.emailAddress, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).emailAddress)],
            onBoardDate: [this.onBoardDate, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).onBoardDate)],
            tempHomeCountry: [this.tempHomeCountry, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempHomeCountry)],
            tempHomeArea: [this.tempHomeArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempHomeArea)],
            tempHomeTel: [this.tempHomeTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempHomeTel)],
            homeAddrZipCode: [this.homeAddrZipCode, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).homeAddrZipCode)],
            homeAddrArea: [this.homeAddrArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).homeAddrArea)],
            homeAddr: [this.homeAddr, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).homeAddr)],
            homeTelArea: [this.homeTelArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).homeTelArea)],
            homeTel: [this.homeTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).homeTel)],

            tempCommCountry: [this.tempCommCountry, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempCommCountry)],
            tempCommArea: [this.tempCommArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempCommArea)],
            tempResTel: [this.tempResTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempResTel)],

            commAddrArea: [this.commAddrArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).commAddrArea)],
            commAddr: [this.commAddr, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).commAddr)],
            commAddrZipCode: [this.commAddrZipCode, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).commAddrZipCode)],
            resTelArea: [this.resTelArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).resTelArea)],
            resTel: [this.resTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).resTel)],

            occupation: [this.occupation, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).occupation)],
            year: [this.year, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).year)],
            month: [this.month, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).month)],

            yearlyIncome: [this.yearlyIncome, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).yearlyIncome)],

            tempCorpCountry: [this.tempCorpCountry, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempCorpCountry)],
            tempCorpArea: [this.tempCorpArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempCorpArea)],
            tempCorpTel: [this.tempCorpTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).tempCorpTel)],
            corpAddrArea: [this.corpAddrArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpAddrArea)],
            corpAddr: [this.corpAddr, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpAddr)],
            corpAddrZipCode: [this.corpAddrZipCode, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpAddrZipCode)],
            corpTelArea: [this.corpTelArea, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpTelArea)],
            corpTel: [this.corpTel, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpTel)],
            corpTelExten: [this.corpTelExten, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpTelExten)],
            corpName: [this.corpName, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpName)],
            corpNameText: [this.corpNameText, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpNameText)],
            corpTaxNum: [this.corpTaxNum, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).corpTaxNum)],
            education: [this.education, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).education)],
            elementarySchoolName: [this.elementarySchoolName, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).elementarySchoolName)],
            marriage: [this.marriage, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).marriage)],
            estateType: [this.estateType, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).estateType)],
            incomeSource: [this.incomeSource, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).incomeSource)],
            preTradeProduct: [this.preTradeProduct, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).preTradeProduct)],
            sendType: [this.sendType, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).sendType)],
            sendTypeD3: [this.sendTypeD3, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).sendTypeD3)],
            injury: [this.injury, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).injury)],
            jobTitle: [this.jobTitle, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).jobTitle)],
            jobTitleText: [this.jobTitleText, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).jobTitleText)],
            ch3: [this.ch3],
            ch4: [this.ch4, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).ch4)],
            ch5: [this.ch5],
            ch9: [this.ch9],
            birthday: [this.birthday],
            passBookTw: [this.passBookTw, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).passBookTw)],
            passBookFore: [this.passBookFore, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).passBookFore)],
            termsFincShrRisk: [this.termsFincShrRisk, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).termsFincShrRisk)],
            termsFincShrAdv: [this.termsFincShrAdv, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).termsFincShrAdv)],
            productId_pl: [this.productId_pl],
            productId_rpl: [this.productId_rpl],
            productId_cc: [this.productId_cc],
            productId_d3: [this.productId_d3],
            purposePLOtherText: [this.purposePLOtherText, AIOCaseDataVO.getValidator(GET_BASEMODEL_FIELDNAME(this).purposePLOtherText)],
            productId_hl: [this.productId_hl],
            invest: [this.invest],
            accountTw: [this.accountTw], // 台幣帳戶(雙幣卡，一般授扣用)
            accountFore: [this.accountFore], // 外幣帳戶(雙幣卡，一般授扣用)
            withholdingAmount: [this.withholdingAmount], // 應繳總額or最低(雙幣卡，一般授扣用)
            totalPersonAssets: [this.totalPersonAssets, this.getValidator(GET_BASEMODEL_FIELDNAME(this).totalPersonAssets)],
        };
    }
}
