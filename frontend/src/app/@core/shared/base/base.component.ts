import { RoutingModel } from '../model/routing.model';
import { KIG_OFFICIAL_WEBSITE, MODE_ENUM, RETURN_CODE, SERVER_URL } from '../app.constants';
import { AfterContentInit, AfterViewInit, Directive, EventEmitter, HostListener, Inject, Input, OnDestroy, OnInit } from '@angular/core';
import { RouteUiService } from '../service/route-ui.service';
import { GlobalDataService } from '../service/global.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { BaseModel } from './base.model';
import { DialogComponent } from '../component/dialog/dialog.component';
import { DIALOG_TYPE, DialogConfig, SlideDialogService } from '../service/slide-dialog.service';
import { ResizeService, SCREEN_SIZE } from '../service/resize.service';
import { ComponentType } from '@angular/cdk/overlay';
import { GenericDialogComponent } from './dialog/info/generic-dialog.component';
import { ConfirmDialogComponent } from './dialog/confirm/confirm-dialog.component';
import { SpinnerRef, SpinnerService } from '../service/spinner.service';
import { interval, Observable, of, Subject, Subscription } from 'rxjs';
import { BrowserLog } from '../model/browser-log.model';
import { environment } from '../../../../environments/environment';
import { AIO_VIEW_TYPE, COMPONENT_AIO_FLOW_TYPE } from '../routing/aio.path';
import { CustomerServiceComponent } from '../../../mono/customer-service/customer-service.component';
import { CustomerServiceDialogComponent } from '../../shared/base/dialog/customer-service-dialog/customer-service-dialog.component';
import { TimeoutComponent } from './dialog/timeout/timeout.component';
import { delay, finalize, map, retryWhen, take, takeWhile, tap } from 'rxjs/operators';
import { TermsModel } from "../model/term.model";
import { ResponseVO } from '../model/responseVO';
import { LoanTrialNewsletterDialogComponent } from './dialog/loan-trial-newsletter-dialog/loan-trial-newsletter-dialog.component';
import { COMPONENT_D3 } from "../routing/d3.path";
import { PRODUCT } from "../../../mono/progress-query/progress.constants";
import { COMPONENT_D2 } from "../routing/d2.path";
import { ValidParams, ValidReq, ValidResp } from "../../../generic/model/validParams.model";
import { VerificationDialogComponent } from "./dialog/verification/verification-dialog.component";
import { FormBaseComponent } from "./form-base.component";
import { HttpClient, HttpParams } from "@angular/common/http";
import { PhotoView } from "../../../generic/model/photoView.model";
import { DocumentService } from "../service/document.service";

@Directive()
// tslint:disable-next-line:directive-class-suffix
export abstract class BaseComponent<T extends BaseModel> extends FormBaseComponent<BaseModel> implements OnInit, AfterViewInit, AfterContentInit, OnDestroy { // implements OnInit, OnDestroy{

    dataInitialization = false;

    subscription = new Subscription();

    valueChange = new EventEmitter<any>();

    @Input()
    mode: MODE_ENUM = MODE_ENUM.UPDATE; // = MODE_ENUM.READ;

    @Input()
    fillPersonMode: MODE_ENUM = MODE_ENUM.READ;

    @Input()
    fillWorkMode: MODE_ENUM = MODE_ENUM.READ;

    @Input()
    fillOtherMode: MODE_ENUM = MODE_ENUM.READ;

    componentTitle: string;

    // 控制下一步的按鈕 是否能夠開啟
    nextButtonDisable = false;

    // 用於 confirmation page 下一步顯示
    nextButtonDisplay = true;

    isIEOrEdge = /msie\s|trident\/|edge\//i.test(window.navigator.userAgent);

    innerWidth: number; //螢幕解析度 width
    screenType: SCREEN_SIZE = SCREEN_SIZE.MD;

    dialogType: DIALOG_TYPE = DIALOG_TYPE.DIALOG;
    spinner: SpinnerRef<any>;

    goNextSwitch: boolean = true;// 鎖住按鈕被連點多次下一步
    applyIdentifiedCheckFlag: boolean = false;  // 給貸款非同步使用 true=>非同步不檢核跳過 false=>需檢核

    eChatOpen: boolean = false;

    isBackendValid: boolean = true; //是否開啟後端檢核 開啟:true 關閉:false


    @HostListener('window:resize', ['$event'])
    private onResize() {
        this.innerWidth = window.innerWidth;

        if (this.innerWidth < 575) {
            this.screenType = SCREEN_SIZE.XS;
            this.dialogType = DIALOG_TYPE.OFFCANVAS;
        } else if (this.innerWidth < 768) {
            this.screenType = SCREEN_SIZE.SM;
            this.dialogType = DIALOG_TYPE.OFFCANVAS;
        } else if (this.innerWidth < 992) {
            this.screenType = SCREEN_SIZE.MD;
            this.dialogType = DIALOG_TYPE.DIALOG;
        } else if (this.innerWidth < 1200) {
            this.screenType = SCREEN_SIZE.LG;
            this.dialogType = DIALOG_TYPE.DIALOG;
        } else {
            this.screenType = SCREEN_SIZE.XL;
            this.dialogType = DIALOG_TYPE.DIALOG;
        }

        this.detectScreenSize(this.screenType);
    }

    //每頁基本資料
    baseModel: T;
    // abstract baseModel: new() => T;

    //每頁基本路由資訊
    abstract routingModel: RoutingModel<any>;

    myFormGroup: FormGroup;

    //是否為第一頁(startBpm)
    firstPage: boolean = false;

    //是否為加辦第一頁(startBpm)
    firstAddition: boolean = false;

    //從上頁但進來本頁的資料, refresh 就會消失, 必須從 backend重查 or read from sessionStorage
    data: any;

    newable: new () => T;

    urlPrefix: string;

    componentName: string = this.constructor.toString().match(/\w+/g)[1];

    AmericanTermsResult: TermsModel = new TermsModel();

    productViewType = AIO_VIEW_TYPE;

    appTake: boolean = true; // 免登用的 避免一直不斷重複subscribe

    validReqArray: Array<ValidReq> = []
    validParamsMap: Map<string, ValidParams>;
    validReq: ValidReq = new ValidReq();
    alive = true;
    protected documentService: DocumentService = Inject(DocumentService);
    protected constructor(protected activatedRoute: ActivatedRoute,
                          protected routingService: RouteUiService,
                          protected myDialogService: SlideDialogService,
                          protected spinnerService: SpinnerService,
                          protected resizeService: ResizeService,
                          protected router: Router,
                          protected fb: FormBuilder,
                          protected gd: GlobalDataService,
                          protected http: HttpClient,) {

        super();
        this.innerWidth = window.innerWidth;

        this.disableBackNavigation();

        const flowType = localStorage.getItem('_aioRoutingMockupType');

        this.activatedRoute.data.subscribe((data: { module: string, sourceId: string, type: string }) => {
            console.log('Basic router data: ', data);

            if (data.sourceId && data.module) {
                if (!this.routingModel)
                    this.routingModel = new RoutingModel(data.sourceId, data.module);
                this.routingModel.sourceId = data.sourceId;
                this.routingModel.module = data.module;
                this.gd.type = data.module;
            }
        });
        console.log("this.router.getCurrentNavigation(): " , this.router.getCurrentNavigation())
        if (this.router.getCurrentNavigation().extras.state) {
            const routeState = this.router.getCurrentNavigation().extras.state;
            if (routeState) {
                // console.log(this.getComponentName() + 'routeState: ', routeState);
                // this.data.frontEnd = routeState.frontEnd ? JSON.parse(routeState.frontEnd) : '';
                this.data = routeState.data ? routeState.data : '';

                this.gd.data = this.data;
                // console.log(this.getComponentName() + ' has init data in routeState: ', this.data);
            } else {
                console.log(this.getComponentName() + ' no data in routeState, maybe you refreshed the page? ');
                // this.gd.data = undefined;

            }
        } else {

        }

        if (!this.data) {
            this.data = this.gd.getData(BaseModel);
        }


        this.myFormGroup = new FormGroup({});
        // this.subscription.add( this.ngAfterContentInit())

    }

    dataType: new () => T;

    /**
     * 要求指定 dataType
     */
    abstract initData(): Observable<Object>;

    /**
     * 更新 formGroup controls
     */
    abstract resetFormControl(value?: any): void;


    // 整個流程的stage  操作formController
    onAfterFormControlInited(): void {
        // 這裏 是在 form init   需要讓 formController  來使用

    }

    // assignDataType<T>(T): void {
    //     this.dataType = T;
    //     // console.log('dataType: ', this.dataType);
    //     if(this.gd.getData(this.dataType))
    //         this.baseModel = this.gd.getData(this.dataType);
    // }

    ngOnInit(): void {
        console.log('[BaseComponent] ngOnInit');
        console.log('[BaseComponent] baseModel: ', this.baseModel);
        // 美國籍內文設定
        this.termsAmericanSetting();


    }

    /**
     * 當 Angular 把外部內容投影進元件檢視或指令所在的檢視之後呼叫
     * 第一次 ngDoCheck() 之後呼叫，只調用一次
     */
    ngAfterContentInit(): void {
        console.log('[BaseComponent] ngAfterContentInit(module): ', this.routingModel);

        if(this.routingModel) {
            this.checkMobileType();

            this.activatedRoute.data.subscribe((data: { module: string, sourceId: string, type: string }) => {
                console.log('[ngAfterContentInit] Basic router data: ', data);

                if (data.sourceId && data.module) {
                    this.routingModel.sourceId = data.sourceId;
                    this.routingModel.module = data.module;

                    //設定本次服務申辦型態
                    this.gd.type = data.module;
                }

                switch (this.routingModel.module) {
                    case COMPONENT_AIO_FLOW_TYPE.D3:
                        this.componentTitle = '數位帳戶';
                        break;
                    case COMPONENT_AIO_FLOW_TYPE.SAL:
                        this.componentTitle = '薪轉帳戶';
                        break;
                    case COMPONENT_AIO_FLOW_TYPE.APPT:
                        this.componentTitle = '分行開戶預填';
                        break;
                    case COMPONENT_AIO_FLOW_TYPE.LOAN:
                        this.componentTitle = '信用貸款';
                        break;
                    case COMPONENT_AIO_FLOW_TYPE.CC:
                        this.componentTitle = '信用卡';
                        break;
                }
            });

            console.log('[ngAfterContentInit] routingModel: ', this.routingModel);

            // writeBrowserLog GET
            this.writeBrowserLog('GET');

            this.initData().subscribe((result: boolean) => {
                // console.log('initData result: ', result);

                this.appendFormValidator(result);
            });


            if (this.baseModel) {
                this.appendFormValidator(this.dataInitialization);
            }

            this.urlPrefix = this.routingModel.module;
        }

    }

    appendFormValidator(dataInitialization: boolean) {
        this.dataInitialization = dataInitialization;

        if (this.dataInitialization) {
            let formValidator = this.prepareFormValidator();

            console.log('appendFormValidator.... :', formValidator);

            if (formValidator) {
                this.myFormGroup = this.fb.group(formValidator);
            } else {
                this.myFormGroup = new FormGroup({}); //no validators
            }

            //需調用否則 prepareFormValidator時的 data 不會進去
            if (this.myFormGroup && this.baseModel) {
                this.myFormGroup.patchValue(this.baseModel);
            }

            this.resetFormControl();

            this.onAfterFormControlInited();
        }
    }


    ngAfterViewInit(): void {
        // console.log('ngAfterViewInit: ', this.baseModel);
        //scroll to top
        // this.scrollToTop();
        // document.getElementById("top").scrollIntoView({ behavior: "smooth", block: "start", inline: "start" });
        // 換膚
        this.changeTheme();
    }

    /**
     * scroll回到頂部
     */
    scrollToTop(){
        setTimeout(() => {
            window.scrollTo(0,0);
            document.body.scrollTo(0,0);
        }, 1000);
        // window.scrollTo(0, 0);
    }

    ngOnDestroy() {
        this.hideSpinner();
        this.appTake = false;
    }

    private detectScreenSize(width: SCREEN_SIZE) {
        this.resizeService.onResize(width);
    }

    getComponentName(): string {
        return '[' + this.componentName + ']';
    }

    // abstract ngOnInit(): void;
    // abstract ngOnDestroy(): void;

    // @Output()
    // onNextChange: EventEmitter<any> = new EventEmitter<any>();
    //
    // @Output()
    // onBackChange: EventEmitter<any> = new EventEmitter<any>();

    private init(type: { new(): T }): T {
        return new type();
    }

    prepareFormValidator() { //-> appendFormValidator()
        // console.log(this.getComponentName() + ' FormValidator: ', this.baseModel);

        if (this.baseModel) {
            return this.baseModel.getValidators();
        }
        return undefined;
    }

    /**
     * 取得上一步該前往哪個 component info
     */

    // abstract onBack(routingModel: RoutingModel<any>): void;

    /**
     * 檢查資料格式正確性, 若驗證錯誤,回傳 false
     */
    abstract validateBeforeRoute(routingModel: RoutingModel<any>, disableFormGroup?: boolean): boolean;



    public showConfirmDialog<T>(data: string) {
        console.log('showConfirmDialog type: ', this.dialogType);
        //console.log('showConfirmDialog screenType: ', this.screenType);

        const dialog = this.myDialogService.open(ConfirmDialogComponent, {
            width: '400px',
            height: '200px',
            title: '',
            data: data,
            style: this.dialogType
        });

        //從 dialog 取回資料
        dialog.afterClosed().subscribe((result) => {
            console.log('Result data: ', result);

            // this.result = JSON.stringify(result);
        });

    }

    public showDialogMenu(style?: string) {
        const dialog = this.myDialogService.open(DialogComponent, {
            width: '573px',
            height: '200px',
            title: 'My Title',
            style: DIALOG_TYPE.DIALOG
        });

    }

    public showOffcanvasMenu(style?: string) {
        const dialog = this.myDialogService.open(DialogComponent, {
            width: '400px',
            height: '200px',
            title: 'My Title',
            style: DIALOG_TYPE.OFFCANVAS
        });

    }

    public showErrorDialog<T>(data: any, component?: ComponentType<T>) {
        let contentComponent: any = GenericDialogComponent;
        if (component) {
            contentComponent = component;
        }

        const dialog = this.myDialogService.open(contentComponent, {
            width: '400px',
            height: '200px',
            title: '',
            data: data,
            style: this.dialogType
        });

        //從 dialog 取回資料
        dialog.afterClosed().subscribe((result) => {
            console.log('Result data: ', result);
            this.hideSpinner();
        });
    }

    /**
     * 起案專用
     * @param data
     * @param component
     */
    public showLoanErrorDialog<T>(data: any, component?: ComponentType<T>) {
        let contentComponent: any = GenericDialogComponent;
        if (component) {
            contentComponent = component;
        }

        const dialog = this.myDialogService.open(contentComponent, {
            width: '400px',
            height: '200px',
            title: '',
            data: data,
            style: this.dialogType
        });

        //從 dialog 取回資料
        setTimeout(() => {
            this.routingService.checkApplyIdentifiedBreakpoint(this.routingModel).subscribe(resp => {
                this.applyIdentifiedCheckFlag = true;
                this.hideSpinner();
                // 若非確認頁(既有戶) or OTP2(新戶) 就透過goNext去導到正確的頁面
                if(!(this.routingModel.sourceId.includes("confirmation_form") || this.routingModel.sourceId.includes("otp2") )){
                    this.goNext();
                }
            });
        }, 500);
    }

    // 目前給網行銀設定使用 因為若他是不需要設定網行銀密碼 但又跑進來 就跟user說無需設定 並在按下確定時到finish
    public showSuccessDialog<T>(data: any,targetPath: string , component?: ComponentType<T>) {
        let contentComponent: any = GenericDialogComponent;
        if (component) {
            contentComponent = component;
        }

        const dialog = this.myDialogService.open(contentComponent, {
            width: '400px',
            height: '200px',
            title: '',
            data: data,
            style: this.dialogType
        });

        //從 dialog 取回資料
        dialog.afterClosed().subscribe((result) => {
            this.router.navigate([targetPath], {
                state: {
                    data: this.routingModel.data
                }, queryParams: {}, skipLocationChange: false
            });
        });
    }

    public showConfirmation(width?: string, height?: string) {
        // data?: any;
        // header?: string;
        // footer?: string;
        // width?: string;
        // height?: string;
        // closeOnEscape?: boolean;
        // baseZIndex?: number;
        // autoZIndex?: boolean;
        // dismissableMask?: boolean;
        // rtl?: boolean;
        // style?: any;
        // contentStyle?: any;
        // styleClass?: string;
        // transitionOptions?: string;
        // closable?: boolean;
        // showHeader?: boolean;
        // modal?: boolean;

        // this.refDialog = this.dialogService.open(OnlyConfirmDialogComponent, {
        //     header: '',
        //     showHeader: true,
        //     height: '50%',
        //     width: '50%',
        //     data: 'data',
        //     transitionOptions: '.12s cubic-bezier(0, 0, 0.2, 1)',
        //     closable: true,
        //     closeOnEscape: true,
        //     contentStyle: {"max-height": "500px", "overflow": "auto"},
        //     // footer: 'hey',
        //     baseZIndex: 10000,
        //     modal: true,
        //     style: {'top': '50%', 'transition': 'all 0.5s'}
        // });

        // this.refDialog.onClose.subscribe((baseModel: T) =>{
        //     if (baseModel) {
        //        // this.messageService.add({severity:'info', summary: 'Product Selected', detail: product.name});
        //     }
        // });
    }

    showSpinner(message?: string, backdrop?: string) {
        if (!this.spinner && !message) {
            this.spinner = this.spinnerService.show({message: '請稍候...'});
        } else if (!this.spinner && message && backdrop) {
            this.spinner = this.spinnerService.show({message: message, backdrop: backdrop});
        } else if (!this.spinner && message) {
            this.spinner = this.spinnerService.show({message: message});
        }
    }

    hideSpinner() {

        this.spinner = undefined;
        this.spinnerService.hide();
    }

    public checkForUploadDocument(url: string): boolean {
        const regex = /upload_document/gi;
        return regex.test(url);
    }

    public updateUploadTime(uniqId:string, operate: string): Observable<any> {
        let param = new HttpParams();
        param = param.set('uniqId', uniqId);
        param = param.set('operate', operate);
        console.log('param',param)
        return this.http.post<any>(SERVER_URL + 'publicApi/updateUploadTime', param);

    }

    goNext(disableFormGroup?: boolean, routingModel?: RoutingModel<any>): void {
        // 有碰到workflow有兩次request請求 導致流程異常 在這鎖button 看能否解決此問題
        console.warn("goNext ", this.goNextSwitch);
        if (this.goNextSwitch) {
            // 關閉貸款非同步flag
            if(this.applyIdentifiedCheckFlag){
                console.log("關閉貸款非同步flag!");
                this.applyIdentifiedCheckFlag = false;
            }
            of("").pipe(tap(value => {
                console.log("下一步關閉中!");
                this.goNextSwitch = false;
            }), delay(3000)).subscribe(res => {
                console.log("下一步開啟中!");
                this.goNextSwitch = true;
            });

            console.log('this.getValidators: ', this.baseModel.getValidators());
            console.log('this.routingModel: ', this.routingModel);

            if (this.baseModel && this.baseModel.getValidators()) {
                //如果沒有使用 ngModel, 傳送前需同步最新資料
                console.log('this.baseModel.getValidators(): ', this.baseModel.getValidators());
            }

            const routTo: RoutingModel<any> = routingModel ? Object.assign({}, routingModel) : Object.assign({}, this.routingModel);

            if (disableFormGroup) { //關閉 forGroup 而使用 ngModel 資料
                console.log('Using model Value:', this.baseModel);
                if (!routingModel) {
                    routTo.data = Object.assign({}, this.baseModel);
                } //assign data
            } else {
                console.log('Using FormGroup Value:', this.myFormGroup.getRawValue());
                if (!routingModel) {
                    routTo.data = Object.assign({}, this.myFormGroup.getRawValue());
                } //assign data
            }


            console.log(this.getComponentName() + ' [goNext]: ', routTo);

            if (routTo && !this.validateBeforeRoute(routTo.data, disableFormGroup)) {
                console.log(this.getComponentName() + ' validate data fail.');
                return;
            }

            this.showSpinner();

            this.startRouting(routTo);
        }else{
            console.log("稍等 別急!")
        }
    }

    private startRouting(routTo: RoutingModel<any>){
        this.routingService.getNextRouting(routTo, this.getComponentName(), this.firstPage, this.firstAddition).subscribe((responseVO) => {
            console.log(this.getComponentName() + ' responseVO: ', responseVO);
            console.warn("routTo.sourceId = ",routTo.sourceId.includes('other_setting'));

            this.startRouterResponse(responseVO, routTo);
        }, () => {} , ()=>{
            // 假如當前頁面為 confirmation goNext完成後 再次打開
            this.nextButtonDisplay = true;
            this.goNextSwitch = true;
            console.log("下一步先開啟 微服務已回覆response");
        });
    }
    goBack(routingModel?: RoutingModel<any>): void {
        console.log(this.getComponentName() + 'goBack 尚未實作');
        // this.onBackChange.emit(undefined);
        return;
    }

    writeBrowserLog(type: string): void {
        if (environment.useMockupRouting) {
            console.log('useMockupRouting is true, skip writeBrowserLog.... ');
            return;
        }
        // console.log('type: ', type);

        // 需要寫browser log get
        // 取出gd 的案件編號 有案件編號再去存 aiobrowserlog
        const browserLog: BrowserLog = new BrowserLog();
        browserLog.page = this.router.url.split('?')[0];   // 取問號左邊
        browserLog.action = type;

        try {
            if(this.gd.token && (this.gd.aioRoutingType !== COMPONENT_AIO_FLOW_TYPE.LOAN_CONTRACT)){
                this.routingService.saveBrowserLogGetType(browserLog).subscribe((res) => {
                    console.log('save saveBrowserLogGetType');
                });
            }else {
                // 目前給立約使用
                browserLog.uniqId = this.gd.loanContractUniqId;
                browserLog.uniqType = "01";
                this.routingService.saveBrowseRecordForNoToken(browserLog).subscribe((res) => {
                    console.log('save saveBrowseRecordForNoToken');
                });
            }
        }catch (e){
            console.log("browser log write error");
        }


    }

    private disableBackNavigation() {
        if (window.location.href.indexOf('trial') < 0) {
            history.pushState(null, null, window.location.href);
        //     // this.location.onPopState(() => {
        //     //     history.pushState(null, null, window.location.href);
        //     // });
        //     window.onpopstate = function (e) {
        //         window.history.go(1);
        //     }
        }
    }

    // 客製化 input regex Replace
    inputReplaceChange(event: any, formName: string, regex: string, flag: string): void {
        console.log(event.target.value)
        console.log(formName)
        console.log(regex);
        const target = event.target.value;
        const regexp = new RegExp(regex, flag);
        console.log(regexp);
        this.updateFormControlValue(formName, target.replace(regexp, ''))
    }

    // 左右補參數  use padStart or padEnd
    padSetting(event: any, name: string, length: number, target: string, direction: string) {
        console.log('padSetting_padSetting');
        //direction start 左邊補 、 end 右邊補
        let value = event.target.value;
        if (value !==null && value !=='' && direction === 'start') {
            console.log('value.padStart(length, target)', value.padStart(length, target));
            this.updateFormControlValue(name, value.padStart(length, target));
        } else if (value !==null && value !=='' && direction === 'end') {
            console.log('value.padEnd(length, target)', value.padEnd(length, target));
            this.updateFormControlValue(name, value.padEnd(length, target));
        }
    }


    checkMobileType() {
        console.warn('確認是否為mobile');
        if (this.gd.startFromMobile) {
            console.warn(' app 端 倒數計時');
            if (this.appTake){
                interval(5 * 60 * 1000).pipe(takeWhile(()=> {
                    return true;
                })).subscribe(() => {
                    // do window post
                    // =0 網站沒有逾時 需要繼續呼叫  =1 網站已逾時 不須呼叫
                    if (this.gd.obd_timeout === '0') {
                        if (this.gd.app_token && this.gd.app_idno) {
                            console.warn('this.gd.obd_app_timeout_counter:', this.gd.obd_app_timeout_thrice);

                            if (!this.gd.obd_app_timeout_thrice) {

                                // 2022/11/17  vickey app免登需求;
                                // 1. Obd timeout 不呼叫
                                // 2.累計三次450後不呼叫
                                // 3.onBoarding 未timeOut時，每5min 發updatesessionToken
                                // 1 timeout 0 正常
                                this.routingService.updateAppSession(this.gd.app_idno, this.gd.app_token).subscribe((res) => {
                                    if (res.rtnCode === '9802'){
                                        // 後端三次450 過期 不再呼叫
                                        this.gd.obd_app_timeout_thrice = true;
                                    }
                                });
                            }
                        }
                    }

                });
            }
            // setInterval(() => {
            //     // do window post
            //     // =0 網站沒有逾時 需要繼續呼叫  =1 網站已逾時 不須呼叫
            //     if (this.gd.obd_timeout === '0') {
            //         if (this.gd.app_token && this.gd.app_idno) {
            //             console.warn('this.gd.obd_app_timeout_counter:', this.gd.obd_app_timeout_thrice);
            //
            //             if (!this.gd.obd_app_timeout_thrice) {
            //
            //                 // 2022/11/17  vickey app免登需求;
            //                 // 1. Obd timeout 不呼叫
            //                 // 2.累計三次450後不呼叫
            //                 // 3.onBoarding 未timeOut時，每5min 發updatesessionToken
            //                 // 1 timeout 0 正常
            //                 this.routingService.updateAppSession(this.gd.app_idno, this.gd.app_token).subscribe((res) => {
            //                     if (res.rtnCode === '9802'){
            //                         // 後端三次450 過期 不再呼叫
            //                         this.gd.obd_app_timeout_thrice = true;
            //                     }
            //                 });
            //             }
            //         }
            //     }
            //
            // }, 5  * 1000);
            // environment.production ? (5 * 60 * 1000) : 10 * 1000
        }

        if (this.getUserIsMobile()) {
            this.dialogType = DIALOG_TYPE.OFFCANVAS;
        }

    }

    mobileClose() {
        parent.postMessage("maib:closeBrowser", "*");
        // 如果app沒有 token  且timeout 要導去 官網
        const kgiIndex = KIG_OFFICIAL_WEBSITE;
        window.location.replace(kgiIndex);
        window.location.href = kgiIndex;
    }

    mobileCloseForCreditLimit() {
        parent.postMessage("maib:closeBrowser", "*");
    }

    customerServiceDialog() {
        let result = this.getUserIsMobile();
        console.log("result:" , result);
        if(result){
            const config:DialogConfig = {
                width: "400px",
                height: "300px",
                title: "提示訊息",
                style: DIALOG_TYPE.OFFCANVAS,
            }
            return this.myDialogService.openTerms(CustomerServiceDialogComponent,config)
        }else{
            const dialog = this.myDialogService.openCustomerService(CustomerServiceComponent, {
                title: "",
                style: DIALOG_TYPE.DIALOG,
            });

            dialog.afterClosed().subscribe(data => {
                if (data === 'YES') {
                    console.log('是');
                } else {
                    console.log('否');
                }
            });
        }
    }

    /**
     * 智能客服 顯示/關閉
     * @param open
     */
    showEChatDialog(open) {
        console.log('showECHAT');
        console.log(open);
        this.eChatOpen = open
        this.scrollToTop();
    }


    getUserIsMobile():boolean{
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ||
            (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.platform))) {

            return true;
            // ...
        }
        return false;

    }

    // 驗身失敗
    /**
     * 20240131 Bruce
     * 單號: WT20231122012b
     * 修改內容: 手機90天內曾異動只有 AIO選繼續驗證才要往下一步
     */
    /**
     * sourceid: credit or deposit
     * type: 1: 他行驗身失敗  3: 本行驗身失敗
     * r2-3960 手機90天內曾異動跳不同錯誤訊息
     * @param sourceid
     * @param type
     */
    public validate(sourceid: string, type: string, errPCode?: string, isAio?: boolean) {
        let res = this.validateDialog(sourceid, type, errPCode);
        res.afterClosed().subscribe((res) => {
            console.log('res 驗身失敗 = ', res);
            if (res === 'YES') {
                console.log('繼續驗證');
            } else {
                console.log('離開');
                this.backToHomePage();
            }
            this.hideSpinner();
        });
    }

    /**
     * 20240416 WT20240412003 Maggie <br>
     * 90天手機異動，需求修正，單一D3 若為 000001Y 則導回登入頁 其餘則繼續申辦
     * @param sourceid
     * @param type
     * @param errPCode
     * @param isAio
     */
    public verifyFailD3(sourceid: string, type: string, errPCode?: string, isAio?: boolean) {
        let phone90D = errPCode === '4';

        // 90天手機異動者，僅確認按鈕可選擇
        if(phone90D){
            const dialog = this.myDialogService.open(GenericDialogComponent, {
                width: '400px',
                height: '300px',
                title: '',
                data: type,
                style: this.dialogType
            });
            // 客戶按下確認後離開
            dialog.afterClosed().subscribe((result) => {
                this.backToHomePage();
            });
            this.hideSpinner();
            // 其他驗證失敗原因則走原本流程
        }else{
            let res = this.validateDialog(sourceid, type, errPCode);
            res.afterClosed().subscribe((res) => {
                console.log('res 驗身失敗 = ', res);
                if (res === 'YES') {
                    console.log('繼續驗證');
                } else {
                    console.log('離開');
                    this.backToHomePage();
                }
                this.hideSpinner();
            });
        }
    }

    /**
     * 驗身失敗 dialog
     * r2-3960 手機90天內曾異動跳不同錯誤訊息
     * @param sourceid
     * @param type
     * @param errPCode
     */
    public validateDialog(sourceid: string, type: string, errPCode: string) {
        let title = '';
        let data =  '';
        let phone90D = errPCode === '4';

        if(sourceid === 'credit'){
            title = "信用卡驗證失敗";
            data = type === '1' ? '線上驗身未能驗證成功，請確認是否要繼續驗身？' : '因您的信用卡持卡未滿六個月，請更換一張信用卡驗證。'
        }else{
            title = "存款帳戶驗證失敗";
            let errMsg = "提醒您，無法使用數位帳戶驗證，如有問題請洽客服。";
            data = phone90D ? type : type == errMsg ? errMsg : "";
        }
        let result = this.myDialogService.open(ConfirmDialogComponent, {
            width: "400px",
            height: "300px",
            title,
            data,
            style: DIALOG_TYPE.DIALOG,
            confirmContent: "繼續驗證",
            cancelContent: "離開",
        });
        return result;
    }

    // 3次錯誤
    public validateError(uniqId: string) {
        let res = this.validateErrorDialog();
        res.afterClosed().subscribe((res) => {
            console.log('res 驗身失敗回登入頁 = ', res);

            //撥打客服與我知道了點擊後把錯誤次數歸0
            this.routingService.setValidateErrorZero(uniqId).subscribe((result: any) => {
                console.log('result = ', result);
            }, (error) =>{
                console.log('error = ', error);
            });

            if (res === 'YES') {
                console.log('我知道了');
                this.backToHomePage();
            } else {
                console.log('撥打客服');
                this.customerServiceDialog();
            }
            this.hideSpinner();
        });
    }

    // 3次錯誤 Dialog
    public validateErrorDialog() {
        let IsMobile = this.getUserIsMobile();
        console.log("IsMobile:", IsMobile);
        let cancelContent = '';
        if (IsMobile) {
            cancelContent = '撥打客服';
        }
        console.log("cancelContent = ", cancelContent);
        let result = this.myDialogService.open(TimeoutComponent, {
            width: "573px",
            height: "200px",
            title: "聯繫我們",
            centerTitle: true,
            leftTitle: false,
            confirmContent: "我知道了",
            cancelContent: cancelContent,
            data: '<p class="d-flex justify-content-center">申辦遇到問題嗎?<p><p class="d-flex justify-content-center">隨時使用右上的客服功能與我們聯繫<p>',
            style: DIALOG_TYPE.DIALOG
        });
        return result;
    }

    //線上驗身失敗回登入頁
    backToHomePage(){
        console.log("window.location.href:", window.location);
        const tempUrl = this.gd.timeoutRedirectUrl;
        console.log("tempUrl: " , tempUrl);
        if (this.router.url!== tempUrl) {
            if (tempUrl !== '') {
                this.sessionTimeout();
                // 如果是登入(ex 多合一就需要用 window.locan.href = route 島頁過去
                if(tempUrl.indexOf("?")!=-1){
                    // console.log('url : ', `${window.location.protocol}//${window.location.host}${window.location.pathname}#${tempUrl}`);
                    this.routingService.changeRef(tempUrl)
                    // window.location.href = `${window.location.protocol}//${window.location.host}${window.location.pathname}#${tempUrl}`;
                    this.routingService.changeRef(tempUrl)

                }else{
                    // 其他單一
                    this.router.navigate(['/'+ tempUrl], {replaceUrl: true});
                }
            }
        }
    }

    /*
    * 參考 stp 程式
    *  @see apply-header.component.ts
    * */
    sessionTimeout() {
        // FIXME
        // 這裡應該要把相關的申辦資料刪除 或是 要手動將案件編號的狀態改成 進入斷點等等...
        sessionStorage.clear()
    }

    /**
     *  美國籍 顯示title、content
     */
    termsAmericanSetting() {
        this.AmericanTermsResult.name = 'AmericanTerms'
        this.AmericanTermsResult.title = '具有美國/或其他地區稅務居住者身分'
        this.AmericanTermsResult.content = '若你持有美國國籍或其他地區稅務居民身份或出生地為美國，則無法於上線上開立數位帳戶，請您於臨櫃進行開戶申請。'
    }



    /**
     * 非同步檢查起按流程是否結束
     */
    public applyIdentifiedCheck(responseVO?: ResponseVO<any>): void {
        console.warn('this.applyIdentifiedCheck()!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
        const loading = document.getElementsByClassName('loading')[0];
        const uniqId = this.gd.caseData.uniqId;
        const idno = this.gd.caseData.idno;
        let applyIdentifiedCheck = true;
        let timeSchedule = 0;


        if (loading) {
            loading.innerHTML = "案件處理中，請稍後...";
        } else {
            this.showSpinner("案件處理中，請稍後...");
        }

        this.routingService.applyIdentifiedCheck(uniqId, idno).pipe(map(resp => {
                console.log('applyIdentifiedCheck resp = ', resp);

                if (resp.rtnCode === RETURN_CODE.SUCCESSFUL) {
                    // resp.result['processStatus'] === '0' 表示起案中
                    const result = JSON.parse(resp['rtnObj']);
                    const processStatus = result['processStatus'];
                    const processMessage = result['processMessage'];

                    if (processStatus && processStatus === '0') {
                        sessionStorage.setItem('Shopee.PrjCode', '');
                        const loading =  document.getElementsByClassName('loading')[0];
                        if(loading){
                            // loading.innerHTML = processMessage;
                            if(applyIdentifiedCheck){
                                applyIdentifiedCheck = false;
                                interval(1000).pipe(take(10)).subscribe(()=>{
                                    console.warn("applyIdentifiedCheck time = " ,timeSchedule);
                                    loading.innerHTML = '案件處理中，請稍後...' + '(' + timeSchedule + '%)';
                                    timeSchedule = timeSchedule + 10;
                                });
                            }

                        }
                        throw resp;
                    } else if (processStatus && (processStatus === '2' || processStatus === '3')) {
                        resp.rtnCode = RETURN_CODE.ERROR;
                        resp.rtnMessage = processMessage;
                        const loading = document.getElementsByClassName('loading')[0];
                        if(loading){
                            loading.innerHTML = processMessage;
                        }
                        return resp;
                    } else {
                        console.log('@@@@@@@@@@ last processMessage=' + processMessage);
                        const lastMsgArray = processMessage.split('|');
                        const loading =  document.getElementsByClassName('loading')[0];
                        if(loading){
                            loading.innerHTML = lastMsgArray[0];
                        }else{

                        }
                        // console.log('@@@@@@@@@@ lastMsgArray[1]=' + lastMsgArray[1]);
                        // sessionStorage.setItem('Shopee.PrjCode', lastMsgArray[1]);
                    }
                }
                return resp;
            }),
            retryWhen((errors: Observable<ResponseVO<any>>) => {
                console.log('applyIdentifiedCheck errors = ', errors);
                return interval(10000);
            }), finalize(() => {
                console.log('this.applyIdentifiedCheck() finalize!!!!!!!!!!!');
                console.log("APPLY_IDENTIFIED_CHECK responseVO = ", responseVO);
                // this.hideSpinner();
            })).subscribe((resp: ResponseVO<any>)=>{
            console.log("load~~~~~",responseVO );

            if (resp.rtnCode === RETURN_CODE.ERROR && !responseVO) {
                // 退回畫面!!!!!!!
                console.warn('斷點進入 退回畫面!!!!!!');
                this.showLoanErrorDialog(resp.rtnMessage);
                return;
            }else if(resp.rtnCode === RETURN_CODE.ERROR && responseVO){
                console.warn('顯示系統異常之類的錯誤!!!!!!');
                this.showLoanErrorDialog(resp.rtnMessage);
                return;
            }

            if(responseVO) {
                this.routingModel.module = responseVO.rtnObj.module;
                this.routingModel.data = responseVO.rtnObj.data;   // 上一步的結果   可以拿出來到
                this.routingModel.sourceId = responseVO.rtnObj.sourceId;
                this.routingModel.targetId = responseVO.rtnObj.targetId;

                const targetPath = '/' + this.routingModel.module + '/' + this.routingModel.targetId;
                console.log('APPLY_IDENTIFIED_CHECK targetPath = ' + targetPath);
                this.router.navigate([targetPath], {
                    state: {
                        data: this.routingModel.data
                    }, queryParams: {}, skipLocationChange: false
                });
            } else {
                console.log('沒有 applyIdentifiedCheck responseVO', this.routingModel);
                if ((this.routingModel.sourceId.indexOf('upload_document') < 1) &&
                    (this.routingModel.sourceId.indexOf('financial_statement') < 1)) {
                    console.log('不是上傳頁面');
                    this.goNext();
                } else {
                    this.hideSpinner();
                }
            }
        });
    }

    /** 包含 loan 產品 */
    loanProductViewType(viewType: number) {
        return (viewType === this.productViewType.LOAN_CC_D3 || viewType === this.productViewType.LOAN_D3 ||
            viewType === this.productViewType.LOAN_CC || viewType === this.productViewType.AIO_LOAN || viewType === this.productViewType.LOAN ||
            viewType === this.productViewType.AIO_LOAN_DC_D3 || viewType === this.productViewType.AIO_LOAN_DC);
    }

    /** 包含 cc 產品 */
    ccProductViewType(viewType: number) {
        return (viewType === this.productViewType.LOAN_CC_D3 || viewType === this.productViewType.LOAN_CC ||
            viewType === this.productViewType.CC_D3 || viewType === this.productViewType.AIO_CC || viewType === this.productViewType.CC ||
            viewType === this.productViewType.AIO_DC || viewType === this.productViewType.AIO_LOAN_DC ||
            viewType === this.productViewType.AIO_LOAN_DC_D3 || viewType === this.productViewType.AIO_DC_D3);
    }

    /** 包含 D3 產品 */
    d3ProductViewType(viewType: number) {
        return (viewType === this.productViewType.LOAN_CC_D3 || viewType === this.productViewType.LOAN_D3 ||
            viewType === this.productViewType.CC_D3 || viewType === this.productViewType.AIO_D3 || viewType === this.productViewType.D3 ||
            viewType === this.productViewType.AIO_LOAN_DC_D3 || viewType === this.productViewType.AIO_DC_D3);
    }

    // 貸款試算簡訊dialog
    loanTrialDialog(data: any){
        const config:DialogConfig = {
            width: "400px",
            height: "300px",
            title: "提示訊息",
            style: DIALOG_TYPE.OFFCANVAS,
            data: data
        }
        return this.myDialogService.openTerms(LoanTrialNewsletterDialogComponent,config)
    }

    /**
     *  換膚
     */
    public changeTheme() {
        console.log('changeTheme');
        if(this.gd.themePath && this.gd.themePath != '') {
            console.log('changeTheme');
            if(document.body.classList && document.body.classList.length　> 0) {
                // console.log("xxxx changeTheme");
                let temp = document.body.classList ;
                for (let i = 0; i < temp.length; i++) {
                    document.body.classList.remove(temp[i]);
                    i -=1;
                }
                document.body.classList.toggle(`${this.gd.themePath}-theme`);

            }

        }
    }

    /**
     * 組合電話  區碼+電話
     * ex: 住家電話、戶籍電話、公司電話
     * @param area
     * @param tel
     */
    combinationTel(area : string, tel: string): string {
        const telArea = area && area !== '' ? area : null;
        const TelNum = tel && tel !== '' ? tel : null;
        const tempTel = telArea && TelNum ? `${telArea}-${TelNum}` : !telArea  && !TelNum ? '' : !telArea && TelNum ? TelNum :  telArea && !TelNum ? `${telArea}-` : '';
        return tempTel;
    }

    /**
     * 來檢測formGroup是否有檢核不過的
     */
    formCheck():void{
        Object.keys(this.myFormGroup.controls).forEach((key) => {
            console.warn(key +"  = ", this.myFormGroup.controls[key]);
            console.warn(key +"  = " + this.myFormGroup.controls[key].valid);
        });
    }


    /**
     * ******** WT********002 Maggie <br>
     * 修改內容：<br>
     * * 90天手機異動 CR 若 rtn 為空，則需跳彈窗可選擇繼續申辦或換其他帳戶
     * @param responseVO
     * @param routTo
     */
    startRouterResponse(responseVO: ResponseVO<RoutingModel<any>>, routTo :RoutingModel<any>){
        if (responseVO && responseVO.rtnMessage) {
            if (responseVO.rtnCode === RETURN_CODE.SUCCESSFUL || responseVO.rtnCode === RETURN_CODE.UNNECESSARY_SETTING) {

                if (!this.routingModel) {
                    this.routingModel = new RoutingModel();
                }

                // console.log("Routing path: ", responseVO.rtnObj);
                console.log(this.getComponentName() + ' Routing module: ', responseVO.rtnObj.module);
                console.log(this.getComponentName() + ' Routing data: ', responseVO.rtnObj.data);

                this.routingModel.module = responseVO.rtnObj.module;

                this.routingModel.data = responseVO.rtnObj.data;   // 上一步的結果   可以拿出來到
                this.routingModel.sourceId = responseVO.rtnObj.sourceId;
                this.routingModel.targetId = responseVO.rtnObj.targetId;

                const targetPath = '/' + this.routingModel.module + '/' + this.routingModel.targetId;
                // const targetPath = this.routingModel.targetId;
                console.log(this.getComponentName() + 'navigate: ', targetPath);

                this.hideSpinner();
                console.log('end_end');
                console.log(this.getComponentName() + 'reloading...', targetPath);
                // 離開時寫browserLog post
                this.writeBrowserLog('POST');

                // 3960 數存除外 若碰到Y只需提示即可不擋流程
                if (responseVO.rtnMessage && (responseVO.rtnMessage.split(':').length > 2)) {
                    let sourceid = '';
                    if (this.myFormGroup.get('creditCardNumber').value !== null && this.myFormGroup.get('creditCardNumber').value !== '') {
                        sourceid = 'credit';
                    } else {
                        sourceid = 'deposit';
                    }
                    let content = sourceid === 'credit' ? '線上驗身未能驗證成功，請確認是否要繼續驗身？' : responseVO.rtnMessage.split(':')[2];
                    console.log('errMSg : ', content);
                    this.showErrorDialog(content);
                }

                // 網行銀若發查不需設定但又進來設定頁面 需彈出視窗告訴user
                if (this.componentName === 'GOtherSettingComponent') {
                    console.warn("GOtherSettingComponent!!!!!!");
                    this.routingService.getCaseProperties('User.NotSettingWebBank').subscribe((resp) => {
                        console.warn('GOtherSettingComponent resp = ', resp);
                        if (resp.rtnObj['propertyValue'] === 'Y') {
                            this.showSuccessDialog(resp.rtnObj['propertyDesc'], targetPath);
                        } else {
                            this.router.navigate([targetPath], {
                                state: {
                                    data: this.routingModel.data
                                }, queryParams: {}, skipLocationChange: false
                            });
                        }
                    });
                } else {
                    this.router.navigate([targetPath], {
                        state: {
                            data: this.routingModel.data
                        }, queryParams: {}, skipLocationChange: false
                    });
                }

            } else if (responseVO.rtnCode === '99' && responseVO.rtnMessage.search('_BACK') >= 0) {

                // this.showErrorDialog(responseVO.rtnMessage);
                const targetPath = '/' + this.routingModel.module + '/' + responseVO.rtnObj.sourceId;

                console.log(this.getComponentName() + 'navigate: ', targetPath);

                this.hideSpinner();

                console.log(this.getComponentName() + 'reloading...', targetPath);
                // 離開時寫browserLog post
                this.writeBrowserLog('POST');

                this.router.navigate([targetPath], {
                    state: {
                        data: this.routingModel.data
                    }, queryParams: {}, skipLocationChange: false
                });
            } else if (responseVO.rtnCode === RETURN_CODE.RETRY) {
                this.startRouting(routTo);
                return;
            } else if (responseVO.rtnCode === RETURN_CODE.ERROR_WORKFLOW_ERROR && routTo.sourceId.includes('other_setting')) {
                // ********有碰到客戶不耐煩就按reflash，這裡是防止他重整又按下一步，找不到任務導致異常(可能已經完成了)，這步通常最後一步，直接讓畫面導頁到完成頁
                this.writeBrowserLog('POST');
                console.warn('ERROR_WORKFLOW_ERROR!!!!!!!!!!!!!!');
                const data ={
                    routTo,
                    'getComponentName': this.getComponentName(),
                    'firstPage': this.firstPage,
                    'firstAddition': this.firstAddition,
                    responseVO,
                    'rtnMessage':responseVO.rtnMessage
                }
                this.routingService.saveBackendProblemLog(data).subscribe(()=>{
                })

                if (routTo.module === 'appt') {
                    this.router.navigate([routTo.module + '/' + 'Appt_finish'], {
                        state: {
                            data: this.routingModel.data
                        }, queryParams: {}, skipLocationChange: false
                    });
                } else if(routTo.module === 'aio'){
                    this.router.navigate([routTo.module + '/' + 'A_finish'], {
                        state: {
                            data: this.routingModel.data
                        }, queryParams: {}, skipLocationChange: false
                    });
                }
                return;
            } else if (responseVO.rtnCode === RETURN_CODE.APPLY_IDENTIFIED_CHECK) {
                // 貸款起案 rtnCode若是多執行續非同步請求 就走這裡輪詢是否做完
                this.applyIdentifiedCheck(responseVO);
                return;
            } else if (responseVO.rtnCode === RETURN_CODE.ERROR_WORKFLOW_ERROR) {
                console.warn("getCurrentNavigation",  this.router.getCurrentNavigation());
                this.router.navigate([responseVO.rtnObj],{});

                return;
            }else {
                // this.onNextChange.emit(this.routingModel);
                // this.validateBeforeRoute(this.routingModel);
                let sourceid = '';
                this.writeBrowserLog('POST');
                console.log('validateError : ', responseVO.rtnMessage.includes("validateError"));
                if(responseVO.rtnMessage.includes("validateError")){

                    console.log("creditCardNumber : ", this.myFormGroup.get('creditCardNumber').value);

                    if(this.myFormGroup.get('creditCardNumber').value !== null && this.myFormGroup.get('creditCardNumber').value !== ''){
                        sourceid = 'credit';
                    }else{
                        sourceid = 'deposit';
                    }

                    console.log('rtnMessage : ', responseVO.rtnMessage.split(','));
                    console.log('errorcount : ', Number(responseVO.rtnMessage.split(':')[1]));

                    if(this.gd.aioRoutingType === COMPONENT_AIO_FLOW_TYPE.D2 && responseVO.rtnMessage.split(':').length >= 3 && responseVO.rtnMessage.split(':')[2] === '3009'){
                        this.showErrorDialog('提醒您，請確認您的信用卡效期及須持卡滿6個月以上，如有問題請洽客服。');
                    } else if(Number(responseVO.rtnMessage.split(':')[1]) >= 3){
                        console.log('uniqId : ', responseVO.rtnObj.data.uniqId);
                        this.validateError(responseVO.rtnObj.data.uniqId);
                    }else{
                        console.log('sourceid : ', sourceid);
                        console.log('rtnMessage.length : ', responseVO.rtnMessage.split(':').length);
                        const type = responseVO.rtnMessage.split(':')[2];
                        const errPCode = responseVO.rtnMessage.split(':')[3];
                        console.log("type = ",type);
                        console.log("errPCode = ",errPCode);
                        console.log("this.gd.aioRoutingType = ",this.gd.aioRoutingType);

                        /** D3、sal 使用不同顯示方式 不需要 **/
                        // 若 errPCode = 4 則為單一D3 90天手機異動註記，去跳彈窗回登入頁
                        if((this.gd.caseData.productId_d3 !== "X" || this.gd.caseData.productId_sal !== 'X')
                            && this.gd.caseData.productId_pl === "X"  && this.gd.caseData.productId_rpl === "X" && this.gd.caseData.productId_cc === "X") {
                            this.verifyFailD3(sourceid, type, errPCode);
                        } else if(this.gd.aioRoutingType === 'aio-dc-d3') { // 雙幣卡 dc_d3 也需踢退
                            this.verifyFailD3(sourceid, type, errPCode);
                        } else if(responseVO.rtnMessage.split(':').length > 2) {
                            this.dialogType = DIALOG_TYPE.DIALOG;
                            let content = sourceid === 'credit' ? '線上驗身未能驗證成功，請確認是否要繼續驗身？' : responseVO.rtnMessage.split(':')[2];
                            console.log('errMSg : ', content);
                            this.showErrorDialog(content);
                        }
                    }
                }else{
                    // routTo, this.getComponentName(), this.firstPage, this.firstAddition,responseVO && responseVO.rtnMessage
                    // 寫 aio api log 到後端
                    this.routingService.saveBackendProblemLog(responseVO).subscribe(()=>{
                    })
                    this.showErrorDialog(responseVO.rtnMessage);
                }
                //TODO: 是否要回首頁?
                return;
            }
        } else {

            // routTo, this.getComponentName(), this.firstPage, this.firstAddition,responseVO && responseVO.rtnMessage
            // 寫 aio api log 到後端
            const data ={
                routTo,
                'getComponentName': this.getComponentName(),
                'firstPage': this.firstPage,
                'firstAddition': this.firstAddition,
                responseVO,
                'rtnMessage':responseVO.rtnMessage
            }
            this.routingService.saveBackendProblemLog(data).subscribe(()=>{
            })
            this.showErrorDialog('系統錯誤 請洽客服');
        }
    }

    /**
     * 下一步按鈕加上:針對必填欄位直接做長度是否為0的檢查(簡單JS判斷)FUNCTION，來避免Validator異常的問題。
     * 常常發生於Safari瀏覽器
     */
    invalidCheck(): boolean{
        let invalid = false;
        Object.keys(this.myFormGroup.controls).forEach((key) => {
            if(!this.myFormGroup.controls[key].value && this.myFormGroup.controls[key].validator){
                console.warn("key  ", key );
                console.warn("this.myFormGroup.controls[key].value  ", !this.myFormGroup.controls[key].value );
                console.warn("this.myFormGroup.controls[key].validator ", this.myFormGroup.controls[key].validator);
                invalid = true;
                return;
            }
        });
        return invalid;
    }

    /**
     * 前台應檢核合理的Onboardate及職業類別
     */
    checkSelect(selectType: string, option: Array<any>, checkOption: string): string {
        // 工作
        if (selectType === 'job') {
            for (let i = 0; i < option.length; i++) {
                if(option[i].dataKey === checkOption){
                    return checkOption;
                }
            }
        }

        // 到職年
        if (selectType === 'year') {
            for (let i = 0; i < option.length; i++) {
                if(option[i].key === checkOption){
                    return checkOption;
                }
            }
        }

        // 到職月
        if (selectType === 'month') {
            for (let i = 0; i < option.length; i++) {
                if(option[i].key === checkOption){
                    return checkOption;
                }
            }
        }
        return "";
    }

    /**
     * 給未使用workflow的申辦流程紀錄browserLog
     * @param type
     */
    writeBrowserNoToken(type: string, uniqType: string):void{
        const browserLog: BrowserLog = new BrowserLog();
        browserLog.page = this.router.url.split('?')[0];   // 取問號左邊
        browserLog.action = type;
        browserLog.uniqId = this.gd.loanContractUniqId;
        browserLog.uniqType = uniqType;

        this.routingService.saveBrowseRecordForNoToken(browserLog).subscribe((res) => {
            console.log('save saveBrowseRecordForNoToken');
        });
    }

    /**
     * 判斷申辦產品
     * @param product
     */
    checkSelectProduct(product: string) {
        return (product !== undefined) && (product !== null) && (product !== '') && (product !== 'X');
    }

    /**
     * 確認使用 D2 、 D3
     * 根據Type 判斷回傳
     * @param d3path
     * @param d2path
     * @param type
     */
    digitalModelCheck(d3path:string , d2path: string, type: string) : any {
        const path = this.gd.type === PRODUCT.D3 ?　d3path : d2path;
        const module = this.gd.type === PRODUCT.D3 ?　COMPONENT_D3.MODULE : COMPONENT_D2.MODULE;
        switch (type) {
            case 'Routing':
                return new RoutingModel(path, module);
            case 'Path':
                return `${module}/${path}`;
        }
    }

    /** 確認url 是否有D3，判斷走哪一個 path  */
    digitalUrlCheck(d3path:string ,dpath: string) : string {
        console.log(this.router.url);
        return this.router.url.indexOf('d3_') > -1 ? d3path : dpath
    }

    /**
     * ******** WT20231227004 Momo
     * 若無法 mapping，需把值清掉，前端才能正確檢核
     *
     * r2-4246
     * r2-4586 既有戶資料有問題，更改 mapping 下拉寫法
     * 確認value是否能mapping到
     */
    checkDropDownDataValue(name: any, dropDownDataList: any) {
        let dropDownData = dropDownDataList !== undefined ? dropDownDataList.find(e => e.dataKey === this.getFormControlValue(name)) : '';
        if (dropDownData !== undefined && dropDownData !== '') {
            return true;
        } else {
            this.updateFormControlValue(name, '');
            return false;
        }
    }

    /**
     * 後端檢核 針對Model處理要帶去檢核的資料
     */
    validParamsFun(): Array<ValidReq>{
        let value = '';
        this.validReqArray = [];
        this.validParamsMap.forEach((value,key) => {
            this.validReq = new ValidReq();
            this.validReq.key = key; //欄位名稱
            console.warn("key = ",  key);
            if(this.myFormGroup.controls[key]){
                value = this.myFormGroup.controls[key].value;
            }else {
                // 關係人頁 FormArray
                if(key.includes('twoBlood')){
                    let index = Number(key.substring(key.length - 1, key.length)) - 1;
                    const formArray = this.myFormGroup.get('addSecondDegreeData') as FormArray;
                    if(formArray) {
                        value = formArray.at(index).get(key.substring(0, key.length - 1)).value;
                    }
                }else {
                    let index = Number(key.substring(key.length - 1, key.length)) - 1;
                    const formArray = this.myFormGroup.get('addCeoData') as FormArray;
                    if(formArray){
                        value = formArray.at(index).get(key.substring(0, key.length - 1)).value;
                    }
                }
            }

            this.validReq.name = this.validParamsMap.get(key).validName; //欄位名稱
            this.validReq.errorMessage = this.validParamsMap.get(key).validErrorMessage; //欄位名稱
            this.validReq.errorMessage2 = this.validParamsMap.get(key).validErrorMessage2; //欄位名稱
            this.validReq.errorMessage3 = this.validParamsMap.get(key).validErrorMessage3; //欄位名稱
            this.validReq.value = (value == null ? value : typeof value === 'boolean' ? String(this.myFormGroup.controls[key].value) : typeof value === 'object' ? JSON.stringify(value) : value ) ;  // 欄位得值
            this.validReq.valid = this.validParamsMap.get(key).validArray; // 欄位的檢核
            this.validReq.lowerLimit = this.validParamsMap.get(key).lowerLimit; // 欄位的檢核
            this.validReq.upperLimit = this.validParamsMap.get(key).upperLimit; // 欄位的檢核
            this.validReq.valid = this.validParamsMap.get(key).validArray; // 欄位的檢核
            this.validReq.type = this.validParamsMap.get(key).type; // 欄位的檢核
            this.validReq.start = this.validParamsMap.get(key).start; // 欄位的檢核
            this.validReq.end = this.validParamsMap.get(key).end; // 欄位的檢核
            this.validReq.uniqId = this.gd.caseData !== null && this.gd.caseData?.uniqId !== undefined ? this.gd.caseData?.uniqId : ''; //案件編號(登入頁沒有案編)
            this.validReq.pageId = this.routingModel.sourceId; //當前頁面
            this.validReqArray.push(this.validReq) // 整個頁面要檢核的欄位陣列
        });

        return this.validReqArray;

    }

    findInvalidControlsFromBackEnd(invalidMessageArray: Array<ValidResp>) {
        const invalid = [];
        for (let i=0;i<invalidMessageArray.length;i++) {
            if (invalidMessageArray[i].invalidMessageDto) {
                const errorsInfo = {};
                errorsInfo['name'] = invalidMessageArray[i].invalidMessageDto.name;
                errorsInfo['message'] = invalidMessageArray[i].invalidMessageDto.message;
                invalid.push(errorsInfo);
            }
        }
        console.warn("invalid = ", invalid)
        return invalid;
    };

    // 補件 UniqId,idno 是null 導回補件登錄頁
    public redirectDialog<T>(data?: string) {
        console.log('showConfirmDialog type: ', this.dialogType);
        console.log("data",data)
        data = data == undefined ? "很抱歉，因操作異常，煩請重新補件，謝謝！" : data;

        const dialog = this.myDialogService.open(GenericDialogComponent, {
            width: '400px',
            height: '300px',
            title: '',
            data: data,
            style: this.dialogType
        });

        //從 dialog 取回資料
        dialog.afterClosed().subscribe((result) => {
            console.log('Result data: ', result);
            this.router.navigate(['/addPhoto/login/addPhoto'], {replaceUrl: true});
        });
    }

    /**
     * ******** WT********002 Maggie
     * 若為空白需要不同 dialog 按鈕供選擇
     * @param content
     * @param leftBtn
     * @param rightBtn
     */
    confirmDialog(content: string, leftBtn?: string, rightBtn?: string){
        const dialog = this.myDialogService.open(ConfirmDialogComponent, {
            width: '400px',
            height: '200px',
            title: '',
            data: content,
            style: this.dialogType,
            confirmContent: rightBtn ? rightBtn : "確認",
            cancelContent: leftBtn ? leftBtn : "取消",
        });
        return dialog;
    }

    /**
     * 檢查哪一個欄位檢核無效 並把錯誤訊息填入
     */
    findInvalidControls() {
        const invalid = [];
        const controls = this.myFormGroup.controls;
        for (const name in controls) {
            if (controls[name].invalid) {
                const errorsInfo = {};
                errorsInfo['name'] = this.myFormGroup.controls[name]?.errors?.name;
                errorsInfo['message'] = this.myFormGroup.controls[name]?.errors?.message;
                invalid.push(errorsInfo);
            }
        }
        return invalid;
    };

    public showVerificationDialog<T>(data: any, component?: ComponentType<T>) {
        let contentComponent: any = VerificationDialogComponent;

        if (component) {
            contentComponent = component;
        }

        const dialog = this.myDialogService.open(contentComponent, {
            width: '573px',
            height: '200px',
            leftTitle: false,
            centerTitle: true,
            title: '請確認以下欄位：',
            data: data,
            style:  DIALOG_TYPE.DIALOG,
        });

        //從 dialog 取回資料
        dialog.afterClosed().subscribe((result) => {
            console.log('Result data: ', result);
        });
    }

    startRouterMyDataResponse(responseVO: ResponseVO<RoutingModel<any>>, routTo :RoutingModel<any>) {
        if (responseVO && responseVO.rtnMessage) {
            if (responseVO.rtnCode === RETURN_CODE.SUCCESSFUL || responseVO.rtnCode === RETURN_CODE.UNNECESSARY_SETTING) {
                if (!this.routingModel) {
                    this.routingModel = new RoutingModel();
                }

                this.hideSpinner();

            } else {

                // routTo, this.getComponentName(), this.firstPage, this.firstAddition,responseVO && responseVO.rtnMessage
                // 寫 aio api log 到後端
                const data = {
                    routTo,
                    'getComponentName': this.getComponentName(),
                    'firstPage': this.firstPage,
                    'firstAddition': this.firstAddition,
                    responseVO,
                    'rtnMessage': responseVO.rtnMessage
                }
                this.routingService.saveBackendProblemLog(data).subscribe(() => {
                })
                this.showErrorDialog('系統錯誤 請洽客服');
            }
        }
    }

    /**
     * WT20241022004 在最後一次source$next後延遲檢查 respCount 如果有成功一次就回應正常 若任一件大於10Mb則直接回傳false
     * 上傳財力
     * @param photoViewList
     * @param uploadedDocument
     */

    uploadFinancialDocument(photoViewList: PhotoView[], uploadedDocument: Array<string>): Observable<string> {
        return new Observable<string>((observer) => {
            let respCount = 0;
            const source$ = new Subject<PhotoView>();

            source$.subscribe({
                next: (photoView: PhotoView) => {
                    this.documentService.uploadFinancial(photoView)
                        .pipe(takeWhile(() => this.alive))
                        .subscribe((data) => {
                            // if(respCount === 11){
                            //     data.rtnCode = RETURN_CODE.ERROR_PIC;
                            // }
                            if (data.rtnCode === RETURN_CODE.SUCCESSFUL) {
                                respCount++;
                                console.warn("成功上傳財力資料第", respCount, "筆");
                            } else if (data.rtnCode === RETURN_CODE.ERROR_PIC) {
                                source$.error(RETURN_CODE.ERROR_PIC); // source回傳錯誤 close
                                observer.error(RETURN_CODE.ERROR_PIC); // uploadFinancialDocument回傳錯誤 close
                                console.warn("RETURN_CODE.ERROR_PIC第", respCount, "筆");
                            }

                            if (photoViewList.length === respCount) {
                                console.warn("最後一筆回來，進入微服務！！！！ ", respCount);
                                source$.complete(); // 完成 source
                            }
                        }, error => {
                            console.error("Upload error:", error);
                        });
                },
                complete: () => {
                    console.warn("complete: ", RETURN_CODE.SUCCESSFUL);
                    observer.next(RETURN_CODE.SUCCESSFUL); // 回傳成功
                    observer.complete(); // Observable close
                }
            });

            if (photoViewList.length > 0) {
                this.documentService.getFineSubSerial(photoViewList).subscribe(res => {
                    if (res.rtnCode === RETURN_CODE.SUCCESSFUL) {
                        const photoListRes: Array<PhotoView> = res.rtnObj;

                        for (let i = 0; i < photoViewList.length; i++) {
                            photoListRes[i].base64Image = uploadedDocument[i];
                            source$.next(photoListRes[i]);
                        }

                        setTimeout(() => {
                            if (respCount > 0) {
                                this.updateUploadTime(this.gd.caseData.uniqId, `Frontend-financialUpload-hasErrMsg-` + respCount)
                                    .subscribe(() => {
                                        source$.complete();
                                        observer.complete();
                                        console.warn("setTimeout respCount > 0 ", respCount, "筆");
                                    });
                            } else {
                                console.warn("setTimeout respCount < 0 ", respCount, "筆");
                                observer.error(RETURN_CODE.ERROR); // 報告錯誤
                            }
                        }, 60000);
                    } else {
                        console.warn("res.rtnCode !== RETURN_CODE.SUCCESSFUL ", respCount, "筆");
                        observer.error(RETURN_CODE.ERROR); // 報告錯誤
                    }
                });
            } else {
                console.warn("photoViewList.length < 0 ");
                observer.next(RETURN_CODE.SUCCESSFUL); // 回傳成功
                observer.complete(); // 完成 Observable
            }
        });
    }

}
