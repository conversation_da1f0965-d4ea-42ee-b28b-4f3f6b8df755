<div class="pt-4">
    <div class="web-ch-h4 mobile-ch-h4 text-center">{{title}}</div>
    <div class="d-flex px-sm-default px-md-6 pt-3 pt-md-0 m-auto mobile-ch-h6">
        <div *ngIf="content === 'F099-系統異常，請洽'"  class="d-flex justify-content-center" style="min-height: 100px">
            {{ content }}<a href="">客服中心</a>。
        </div>
        <div *ngIf="content !== 'F099-系統異常，請洽'" class="d-flex justify-content-center" style="margin: 0 auto;">
            {{ content }}
        </div>
    </div>
    <div class="pb-6"></div>

    <div *ngIf="confirmBtn1 !=='' && confirmBtn2 !=='' && link1 ==='' && link2 !==''" class="btnListBorder d-flex text-center">
        <button class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col"  (click)="close()"> {{ confirmBtn1 }} </button>
        <a class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col"  href="{{link2}}" (click)="close()"> {{ confirmBtn2 }} </a>
    </div>

    <div *ngIf="confirmBtn1 !=='' && confirmBtn2 ==='' && link1 ==='' && link2 ===''" class="btnListBorder d-flex text-center">
        <button class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col" (click)="close()"> {{ confirmBtn1 }} </button>
    </div>

    <div *ngIf="confirmBtn1 !=='' && confirmBtn2 !=='' && link1 !=='' && link2 !==''" class="btnListBorder d-flex text-center">
        <a class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col" href="{{link1}}" (click)="close()"> {{ confirmBtn1 }} </a>
        <a class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col"  href="{{link2}}" (click)="close()"> {{ confirmBtn2 }} </a>
    </div>

    <div *ngIf="confirmBtn1 !=='' && link1 !== '' && confirmBtn2 ==='' && link2 === ''" class="btnListBorder d-flex text-center">
        <a class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col"  href="{{link1}}" (click)="close()"> {{ confirmBtn1 }} </a>
    </div>
</div>
