import { Component, Inject, OnInit } from '@angular/core';
import { DIALOG_DATA, DialogRef } from "../../../service/slide-dialog.service";

@Component({
  selector: 'app-customer-dach-dialog',
  templateUrl: './customer-dach-dialog.component.html',
  styleUrls: ['./customer-dach-dialog.component.scss']
})
export class CustomerDACHDialogComponent implements OnInit {

  public content = ''; // dialog內容
  public confirmBtn1 = ''; // 確認文字
  public confirmBtn2 = ''; // 確認文字
  public link1 = '/entry';
  public link2 = '/entry'
  public title = ''; // 有填才顯示

  constructor(private dialogRef: DialogRef<any>,
              @Inject(DIALOG_DATA) public data: any) {

    //從 SlideDialogService 讀取傳入 data
    if(this.data ==="F099-系統異常，請洽客服中心。"){
      this.content ="F099-系統異常，請洽";
    }else {
      this.content = data.msg;
      this.confirmBtn1 = data.confirmCBtn1;
      this.confirmBtn2 = data.confirmCBtn2;
      this.link1 = data.link1;
      this.link2 = data.link2;
      this.title = data.title;
    }
  }

  ngOnInit(): void {
  }

  // dialog close function
  public close(result?: any) {
    console.log(`GenericDialogComponent result: ${result}`);
    this.dialogRef.close(result);
  }
}
