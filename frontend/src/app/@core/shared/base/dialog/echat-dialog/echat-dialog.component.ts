import { Component, EventEmitter, HostListener, Input, OnInit, Output, OnChanges } from '@angular/core';
import { Observable } from "rxjs";
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import { COMPONENT_AIO_FLOW_TYPE, OBD_E_CHAT_Q } from "../../../routing/aio.path";
import { environment } from "../../../../../../environments/environment";

@Component({
  selector: 'app-echat-dialog',
  templateUrl: './echat-dialog.component.html',
  styleUrls: ['./echat-dialog.component.scss']
})
export class EchatDialogComponent implements OnInit, OnChanges {

  constructor(private san: DomSanitizer) {}

  url = '';

  @Input()
  chatDialog = false;
  @Input()
  productType = '';
  @Input()
  token = '';

  @Output()
  returnChat: EventEmitter<boolean> = new EventEmitter<boolean>();

  iframeUrl: SafeResourceUrl;

  ngOnInit(): void {
    this.iframeUrl = this.san.bypassSecurityTrustResourceUrl("");
    console.log(this.chatDialog);
    this.eChatDialog(this.chatDialog);
  }

  ngOnChanges() {
    this.eChatDialog(this.chatDialog);

    console.log(this.url);
  }

  @HostListener('window:message', ['$event'])
  onMessage(event: MessageEvent) {
    // 智能客服 關閉通知
    if(event && event.data && event.data.func && event.data.func === 'closeEChatDialog') {
      this.eChatDialog(false);
    }
  }

  /**
   * 開關 Dialog
   * @param show
   */
  eChatDialog(show: boolean) {
    if (show){
      document.getElementById("SpiritAI").style.display = "block";
      document.getElementById("kgi-obd").style.overflow = "hidden";

      this.url = this.urlSwitch(); // 取網址
      this.url = this.url.replace( '{productType}' , this.productType ? this.productTypeSwitchQ(this.productType) : '')
      // this.url = this.url.replace( '{token}' , this.token ? this.token : '')
      console.log(this.url);
      this.iframeUrl =  this.san.bypassSecurityTrustResourceUrl(this.url);
      console.log(this.iframeUrl);
    }else{
      document.getElementById("SpiritAI").style.display = "none";
      document.getElementById("kgi-obd").style.overflow = "";
      this.iframeUrl = this.san.bypassSecurityTrustResourceUrl("");
    }
    this.returnChat.emit(show);
  }

  productTypeSwitchQ(type: string): string {
    switch (type) {
      case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN:
      case COMPONENT_AIO_FLOW_TYPE.AIO_D3:
      case COMPONENT_AIO_FLOW_TYPE.AIO_CC:
      case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_CC_D3:
      case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_D3:
      case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_CC:
      case COMPONENT_AIO_FLOW_TYPE.AIO_CC_D3:
        return OBD_E_CHAT_Q.AIO;
      case COMPONENT_AIO_FLOW_TYPE.RPL:
        return OBD_E_CHAT_Q.RPL;
      case COMPONENT_AIO_FLOW_TYPE.PL:
        return OBD_E_CHAT_Q.PL;
      case COMPONENT_AIO_FLOW_TYPE.FLEX_CARD:
        return OBD_E_CHAT_Q.FLEX_CARD;
      case COMPONENT_AIO_FLOW_TYPE.D3:
        return OBD_E_CHAT_Q.D3;
      case COMPONENT_AIO_FLOW_TYPE.CC:
        return OBD_E_CHAT_Q.CC;
      default:
        return '';
    }
  }

  /**
   * 2024/03/21 xavier
   * WT20240311004
   * environment.production 改用網址判斷 如果錯誤就預設為正式區參數
   *
   * 正式環境判斷 **/
  urlSwitch(): string {
    // return 'https://cbottweb.kgibank.com/Webhook?eservice=onboarding&q={productType}&token={token}';
    // return 'https://cbottweb.kgibank.com/Webhook?eservice=onboarding&q={productType}';

    let production = true;

    try {
      production =  !!window.location.href.match(/kgibank\.com/)
    } catch (ex) {
      console.warn('網址錯誤 改為預設正式區', ex);
    }

    if(production) {
      return 'https://cbotpweb.kgibank.com/Webhook?eservice=onboarding&q={productType}';
    } else {
      return 'https://cbottweb.kgibank.com/Webhook?eservice=onboarding&q={productType}';
    }
    // if(environment.production) {
    //   return 'https://cbotpweb.kgibank.com/Webhook?eservice=onboarding&q={productType}&token={token}';
    // } else {
    //   return 'https://cbottweb.kgibank.com/Webhook?eservice=onboarding&q={productType}&token={token}';
    // }
  }

}
