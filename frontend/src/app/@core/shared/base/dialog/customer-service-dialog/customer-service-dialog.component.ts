import { Component, Inject, Input, OnInit } from '@angular/core';
import { DIALOG_DATA, DIALOG_TYPE, DialogRef, SlideDialogService } from '../../../service/slide-dialog.service';

@Component({
    selector: 'app-customer-service-dialog',
    templateUrl: './customer-service-dialog.component.html',
    styleUrls: ['./customer-service-dialog.component.scss']
})
export class CustomerServiceDialogComponent implements OnInit {

    @Input()
    public contact = '專人與我們連絡';
    @Input()
    public callCustomerService = '直撥客服中心';

    constructor(private slideDialogService: SlideDialogService,
                private dialogRef: DialogRef<any>,
                @Inject(DIALOG_DATA) public data: any) {
    }

    ngOnInit(): void {
    }

    public close() {
        this.dialogRef.close();
    }



    /**
     * 手機版使用
     * 點擊專人與我們聯絡，彈出以下新分頁
     */
    contactUs(){
        window.open('https://www.kgibank.com.tw/zh-tw/others/contact-us');
    }

}
