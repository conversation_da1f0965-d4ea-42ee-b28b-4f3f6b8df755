import {Component, Inject, Input, OnInit} from '@angular/core';
import {DIALOG_DATA, DIALOG_REDIRECT, DIALOG_URL, DialogRef} from '../../../service/slide-dialog.service';
import { Router } from "@angular/router";

@Component({
    selector: 'app-generic-dialog',
    templateUrl: './generic-dialog.component.html',
    styleUrls: ['./generic-dialog.component.scss']
})
export class GenericDialogComponent implements OnInit {

    @Input()
    public contentText = ''; // dialog內容
    @Input()
    public confirmContent = '確認'; // 確認文字
    @Input()
    public redirectPath = ''; // dialog內容
    @Input()
    public urlPath = '';



    constructor(private dialogRef: DialogRef<any>,
                @Inject(DIALOG_DATA) public data: any,
                @Inject(DIALOG_REDIRECT) public redirectUrl: any,
                @Inject(DIALOG_URL) public url: any) {

        //從 SlideDialogService 讀取傳入 data
        if(this.data ==="F099-系統異常，請洽客服中心。"){
            this.contentText ="F099-系統異常，請洽";
        }else{
            this.contentText = data;
        }
        this.redirectPath = redirectUrl
        this.urlPath = url;
    }

    ngOnInit(): void {
    }

    // dialog close function
    public close(result?: any) {
        console.log(`GenericDialogComponent result: ${result}`);

        this.dialogRef.close(result);

        /**
         * 有redirectPath 才導頁
         */
        if(this.redirectPath && this.redirectPath !== '') {
            console.log(this.redirectPath);
            window.location.href = this.redirectPath;
        }
    }

}
