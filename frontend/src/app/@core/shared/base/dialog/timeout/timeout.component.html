<div>
    <div class="d-flex justify-content-center ">

        <div class="d-flex justify-content-center" *ngIf="contentText.indexOf('上傳') > -1 ; else ELSE">
            <p>
                {{ contentText }}
            </p>
        </div>
        <ng-template #ELSE>
            <div class="flex-column px-5" [innerHTML]="contentText">
            </div>
        </ng-template>

    </div>
    <div class="pb-5"></div>
    <div class="btnListBorder d-flex justify-content-center ">

        <button *ngIf="cancelContent !== ''" class="cancelBtn btn btn-outline-secondary web-ch-h4 mobile-ch-h6 col"><a
                style=" color: #888888;" href="tel:02-80239088">{{ cancelContent }}</a></button>
        <div *ngIf="cancelContent !== ''" class="btnListBorderLine"></div>
        <!-- r2-3297 KS 無短網址、有TOKEN的 需跳訊息不用按鈕 -->
        <button *ngIf="confirmContent !== ''" class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h4 mobile-ch-h6 col"
                (click)="close('YES')">{{ confirmContent }}</button>
    </div>
</div>
