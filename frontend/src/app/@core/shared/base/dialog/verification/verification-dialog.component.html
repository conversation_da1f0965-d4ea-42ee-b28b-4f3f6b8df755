<div class="pt-3">
    <div class="px-sm-default px-md-8">
        <p class="web-ch-h6 mobile-ch-h6" *ngFor="let data of contentText">
            {{data.name}}：<span>{{data.message}}</span>
        </p>
    </div>
    <div class="pb-3"></div>
    <div class="btnListBorder d-flex text-center">
        <button class="confirmBtn btn btn-outline-secondary k-button-secondary web-ch-h6 mobile-ch-h6 col"
                (click)="close()"> {{ confirmContent }} </button>
    </div>
</div>
