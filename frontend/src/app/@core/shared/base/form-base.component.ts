import {AfterContentInit, AfterViewInit, Directive, On<PERSON><PERSON>roy, OnInit} from "@angular/core";
import {BaseModel} from "./base.model";
import {AbstractControl, FormArray, FormControl, FormGroup, ValidatorFn} from "@angular/forms";
import {ValidParams, ValidReq} from "../../../generic/model/validParams.model";

@Directive()
// tslint:disable-next-line:directive-class-suffix
export class FormBaseComponent<T extends BaseModel> {
    //每頁基本資料
    baseModel: T;
    myFormGroup: FormGroup;
    validReqArray: Array<ValidReq> = []
    validParamsMap: Map<string, ValidParams>;
    validReq: ValidReq = new ValidReq();

    enableFormControl(controlName: string) {
        console.log('enable control: ' + controlName);

        // this.myFormGroup.get(controlName).enabled;
        this.myFormGroup?.get(controlName).setValidators(this.baseModel?.getValidator(controlName));
        // this.myFormGroup.get(controlName).updateValueAndValidity({onlySelf:true});
        this.myFormGroup.get(controlName).updateValueAndValidity();
    }



    updateFormControlValue(controlName: string, value: any) {
        // console.log('value', value);
        if (this.myFormGroup?.get(controlName)) {
            this.myFormGroup.get(controlName).setValue(value);
        } else {
            console.log('control: ', this.myFormGroup?.get(controlName));
        }
    }

    getFormControlValue(controlName: string) {
        return this.myFormGroup?.get(controlName)?.value;
    }

    disableFormControl(controlName: string) {
        console.log('disable control: ' + controlName);
        console.log('disable validParamsMap: ', this.validParamsMap);
        // this.myFormGroup.get(controlName).disabled;
        if (this.myFormGroup.get(controlName)) {
            if(this.validParamsMap){
                // 配合後端檢核邏輯 有給valid 就存到validMap 下一步時就會去檢核
                if (this.validParamsMap.get(controlName)) {
                    this.validParamsMap.get(controlName).validArray = [''];
                    this.validParamsMap.set(controlName, this.validParamsMap.get(controlName));
                }
            }

            this.myFormGroup.get(controlName).setValidators(null);
            this.myFormGroup.get(controlName).updateValueAndValidity({ onlySelf: true });
        }
    }


    clearFormControl(controlName: string) {
        if (this.myFormGroup.get(controlName)) {
            // 配合後端檢核邏輯 有給valid 就存到validMap 下一步時就會去檢核
            this.validParamsMap.get(controlName).validArray = null;
            this.validParamsMap.set(controlName, this.validParamsMap.get(controlName));

            this.myFormGroup.get(controlName).clearAsyncValidators();
            this.myFormGroup.get(controlName).updateValueAndValidity({ onlySelf: true })
        }
    }


    updateFormControlValidators(controlName: string, validators: ValidatorFn|ValidatorFn[], backEndValid?: Array<string>, validParamMap?: Map<string, string>) {
        if (this.myFormGroup.get(controlName)) {
            // 配合後端檢核邏輯 有給valid 就存到validMap 下一步時就會去檢核
            if (backEndValid) {
                this.validParamsMap.get(controlName).validArray = backEndValid;
                if(validParamMap && validParamMap.get('validErrorMessage')){
                    this.validParamsMap.get(controlName).validErrorMessage = validParamMap.get('validErrorMessage');
                }
                if(validParamMap && validParamMap.get('validName')){
                    this.validParamsMap.get(controlName).validName = validParamMap.get('validName');
                }
                // 登入頁驗證生日是否符合申辦產品條件 就會需要在這加上參數
                if(validParamMap && validParamMap.get('type')){
                    this.validParamsMap.get(controlName).type = validParamMap.get('type');
                }
                // 若碰到有像年收入 有需要變動金額 就會需要在這加上參數
                if(validParamMap && validParamMap.get('lowerLimit')){
                    this.validParamsMap.get(controlName).lowerLimit = Number.parseInt(validParamMap.get('lowerLimit'));
                }

                if(validParamMap && validParamMap.get('upperLimit')){
                    this.validParamsMap.get(controlName).upperLimit = Number.parseInt(validParamMap.get('upperLimit'));
                }
                // 登入頁驗證生日是否符合申辦產品條件 就會需要在這加上參數
                if(validParamMap && validParamMap.get('start')){
                    this.validParamsMap.get(controlName).start = Number.parseInt(validParamMap.get('start'));
                }
                // 登入頁驗證生日是否符合申辦產品條件 就會需要在這加上參數
                if(validParamMap && validParamMap.get('end')){
                    this.validParamsMap.get(controlName).end = Number.parseInt(validParamMap.get('end'));
                }
                this.validParamsMap.set(controlName, this.validParamsMap.get(controlName));
            }
            this.myFormGroup.get(controlName).setValidators(validators);
            this.myFormGroup.get(controlName).updateValueAndValidity({ onlySelf: true })
        }
    }


    enableFormArray(arrayName: string, controlName: string, index: number) {
        // console.log('enable arrayName: ' + arrayName);
        // console.log('enable control: ' + controlName);
        const formArray = this.myFormGroup.get(arrayName) as FormArray;
        // this.myFormGroup.get(controlName).enabled;
        formArray.at(index).get(controlName).setValidators(this.baseModel.getValidator(controlName));
        // this.myFormGroup.get(controlName).updateValueAndValidity({onlySelf:true});
        formArray.at(index).get(controlName).updateValueAndValidity({ onlySelf: true });
    }

    disableFormArray(arrayName: string, controlName: string, index: number) {
        // console.log('disable arrayName: ' + arrayName);
        // console.log('disable control: ' + controlName);
        // this.myFormGroup.get(controlName).disabled;
        const formArray = this.myFormGroup.get(arrayName) as FormArray;
        formArray.at(index).get(controlName).setValidators(null);
        formArray.at(index).get(controlName).updateValueAndValidity({ onlySelf: true });
    }


    updateFormArrayValidators(arrayName: string, controlName: string, index: number, validators: ValidatorFn|ValidatorFn[], backEndValid?: Array<string>, validParamMap?: Map<string, string>) {
        const formArray = this.myFormGroup.get(arrayName) as FormArray;

        if (formArray) {
            // 配合後端檢核邏輯 有給valid 就存到validMap 下一步時就會去檢核
            if (backEndValid) {
                this.validParamsMap.get(controlName + (index)).validArray = backEndValid;
                this.validParamsMap.set(controlName + index, this.validParamsMap.get(controlName));
            }
            formArray.at(index).get(controlName).setValidators(validators);
            formArray.at(index).get(controlName).updateValueAndValidity({ onlySelf: true })
        }
    }

    /**
     * 取得FormControl物件 想做其他操作可以省略呼叫的長度
     */
    getFormControl(name: string): AbstractControl{
        return this.myFormGroup.get(name) as FormControl;
    }

    /**
     * 取得FormControl物件檢核 有效
     */
    getFormControlValid(name: string): boolean{
        return this.myFormGroup.get(name).valid;
    }

    /**
     * 取得FormControl物件檢核 無效
     */
    getFormControlInValid(name: string): boolean{
        return this.myFormGroup.get(name).invalid;
    }

    /**
     * 取得欄位名稱
     * @param obj
     * @param type
     */
    getFieldName = (obj) => {
        return new Proxy(obj, {
            get(_, key) {
                return key;
            }
        });
    };

}
