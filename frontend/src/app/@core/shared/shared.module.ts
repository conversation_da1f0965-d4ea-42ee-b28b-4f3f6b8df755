import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { DialogComponent } from './component/dialog/dialog.component';
import {CommonModule, DatePipe} from '@angular/common';
import { GenericDialogComponent } from './base/dialog/info/generic-dialog.component';
import { ConfirmDialogComponent } from './base/dialog/confirm/confirm-dialog.component';
import { CustomDialogComponent } from './base/dialog/custom/custom-dialog.component';
import { HtmlDialogComponent } from './base/dialog/html/html-dialog.component';
import { NumberOnlyDirective } from './directive/number-only.directive';

import { PhoneMaskDirective } from './directive/phone-validation.directive';
import { MobileMaskDirective } from './directive/mobile-validation.directive';
import { LandlineMaskDirective } from './directive/landline-validation.directive';
import { TargetSelectAllDirective } from './directive/targetSelectAll.directive';
import { GApplyConfirmationPipe } from './pipes/g-apply-confirmation.pipe';
import { EducationPipe } from './pipes/education.pipe';
import { MarriagePipe } from './pipes/marriage.pipe';
import { EstatePipe } from './pipes/estate.pipe';
import { SourceOfAccountFundsPipe } from './pipes/sourceOfAccountFunds.pipe';
import { BusinessPipe } from './pipes/business.pipe';
import { SendTypePipe } from './pipes/sendType.pipe';
import { EoppurposePipe } from './pipes/eoppurpose.pipe';
import { PreTradeProductPipe } from './pipes/preTradeProduct.pipe';
import {
    CustomerServiceDialogComponent
} from './base/dialog/customer-service-dialog/customer-service-dialog.component';
import { RouterModule } from '@angular/router';
import { JobTitlePipe } from './pipes/job-title.pipe';
import { YesOrNoPipe } from './pipes/yesOrNo.pipe';
import { EnglishOnlyDirective } from './directive/english-only.directive';
import { ChtAndEngValidationDirective } from './directive/chtAndEng-validation.directive';
import { EditDialogComponent } from './base/dialog/edit-dialog/edit-dialog.component';
import { CustomSimpleDialogComponent } from './base/dialog/custom-simple/custom-simple-dialog.component';
import { ChtAndEngAndNumValidationDirective } from './directive/chtAndEngAndNum-validation.directive';
import { engAndNumValidationDirective } from './directive/engAndNum-validation.directive';
import { LoginPhoneMaskDirective } from './directive/login-phone-validation.directive';
import { TimeoutComponent } from './base/dialog/timeout/timeout.component';

import { PassbookPipe } from './pipes/passbook.pipe';
import { BankdataPipe } from './pipes/bankdata.pipe';
import { EmailValidationDirective } from './directive/email-validation.directive';
import { HtmlPipe } from './pipes/html.pipe';
import { EdittypePipe } from './pipes/edittype.pipe';
import { SendTypeD3Pipe } from './pipes/send-type-d3.pipe';
import { AddressValidationDirective } from './directive/address-validation.directive';
import { PhoneMask2Directive } from './directive/phone-validation2.directive';
import { ModalComponent } from './component/modal.component';
import { EchatDialogComponent } from './base/dialog/echat-dialog/echat-dialog.component';
import { CustomerDACHDialogComponent } from './base/dialog/customer-dach-dialog/customer-dach-dialog.component';
import { KgiDatepickerComponent } from './component/kgi-datepicker/kgi-datepicker.component';
import { CalendarModule } from 'primeng/calendar';
import { RelationshipWithApplicantPipe } from './pipes/relationshipWithApplicant.pipe';
import {withholdingAmountPipe} from './pipes/withholdingAmount.pipe';
import {SendTypeDcPipe} from "./pipes/sendTypeDc.pipe"; // WT20240514001 Tim 20240515 雙幣卡易用性測試 電子帳單-> 電子對帳單
import {SendTypeDcD3Pipe} from "./pipes/send-type-dc-d3.pipe";
import { NumberOnlyV2Directive } from './directive/number-only_v2.directive';
import { NumberOnlyV3Directive } from './directive/number-only_v3.directive';


@NgModule({
    imports: [
    ],
    declarations: [
        // HasAnyAuthorityDirective,

        //Dialog
        GenericDialogComponent,
        ConfirmDialogComponent,
        CustomDialogComponent,
        DialogComponent, //deprecated

        CustomSimpleDialogComponent, //confirm??

        ModalComponent,

        HtmlDialogComponent,

        GApplyConfirmationPipe,
        EducationPipe,
        MarriagePipe,
        EstatePipe,
        SourceOfAccountFundsPipe,
        BusinessPipe,
        SendTypePipe,
        SendTypeDcPipe,
        EoppurposePipe,
        PreTradeProductPipe,
        YesOrNoPipe,
        PassbookPipe,
        withholdingAmountPipe,
        RelationshipWithApplicantPipe,

        NumberOnlyDirective,
        NumberOnlyV2Directive,
        NumberOnlyV3Directive,
        LandlineMaskDirective,
        MobileMaskDirective,
        EnglishOnlyDirective,
        ChtAndEngValidationDirective,
        EmailValidationDirective,
        ChtAndEngAndNumValidationDirective,
        engAndNumValidationDirective,
        PhoneMaskDirective,
        PhoneMask2Directive,
        LoginPhoneMaskDirective,
        TargetSelectAllDirective,
        CustomerServiceDialogComponent,

        JobTitlePipe,
        EditDialogComponent,
        TimeoutComponent,
        BankdataPipe,

        HtmlPipe,
        EdittypePipe,

        SendTypeD3Pipe,
        SendTypeDcD3Pipe,
        AddressValidationDirective,

        EchatDialogComponent,
        CustomerDACHDialogComponent,
        KgiDatepickerComponent,
    ],
    exports: [
        DialogComponent,
        SendTypePipe,
        SendTypeDcPipe,
        SendTypeD3Pipe,
        SendTypeDcD3Pipe,
        EoppurposePipe,
        PreTradeProductPipe,

        GApplyConfirmationPipe,
        EducationPipe,
        MarriagePipe,
        EstatePipe,
        SourceOfAccountFundsPipe,
        SourceOfAccountFundsPipe,
        BusinessPipe,
        YesOrNoPipe,
        withholdingAmountPipe,
        HtmlPipe,

        PhoneMaskDirective,
        PhoneMask2Directive,
        LoginPhoneMaskDirective,
        MobileMaskDirective,
        EnglishOnlyDirective,
        ChtAndEngValidationDirective,
        ChtAndEngAndNumValidationDirective,
        engAndNumValidationDirective,
        EmailValidationDirective,
        LandlineMaskDirective,
        NumberOnlyDirective,
        NumberOnlyV2Directive,
        NumberOnlyV3Directive,
        TargetSelectAllDirective,
        AddressValidationDirective,
        JobTitlePipe,
        PassbookPipe,
        BankdataPipe,
        EdittypePipe,
        HtmlPipe,

        EchatDialogComponent,
        KgiDatepickerComponent,
        RelationshipWithApplicantPipe,
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    providers: [DatePipe]
})
export class SharedModule {}
