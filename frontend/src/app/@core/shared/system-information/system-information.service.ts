import { Injectable } from '@angular/core';
import { TimeoutComponent } from "../base/dialog/timeout/timeout.component";
import { DIALOG_TYPE, SlideDialogService } from "../service/slide-dialog.service";
import { KIG_OFFICIAL_WEBSITE } from "../app.constants";
import { GenericService } from "../service/generic.service";
import { UserVersionModel } from "../../../generic/model/user-version.model";

@Injectable({
    providedIn: 'root'
})
export class SystemInformationService {

    private mobileDeviceName: string;
    private mobileDeviceVersion: string;
    private browserName: string;
    private userVersionData = new UserVersionModel();

    constructor(private genericService: GenericService,
                private myDialogService: SlideDialogService,
    ) {
    }


    getSystemInformation() {
        let os = navigator.platform;
        let userAgent = navigator.userAgent;
        let info = "";
        let tempArray: any = "";
        //判斷作業系統
        if (os.indexOf("Win") > -1) {
            if (userAgent.indexOf("Windows NT 5.0") > -1) {
                info += "Win2000";
            } else if (userAgent.indexOf("Windows NT 5.1") > -1) {
                info += "WinXP";
            } else if (userAgent.indexOf("Windows NT 5.2") > -1) {
                info += "Win2003";
            } else if (userAgent.indexOf("Windows NT 6.0") > -1) {
                info += "WindowsVista";
            } else if (userAgent.indexOf("Windows NT 6.1") > -1 || userAgent.indexOf("Windows 7") > -1) {
                info += "Win7";
            } else if (userAgent.indexOf("Windows NT 6.2") > -1 || userAgent.indexOf("Windows 8") > -1) {
                info += "Win8";
            } else if (userAgent.indexOf("Windows NT 6.3") > -1 || userAgent.indexOf("Windows 8.1") > -1) {
                info += "Win8.1";
            } else if (userAgent.indexOf("Windows NT 10.0") > -1 || userAgent.indexOf("Windows 10") > -1) {
                info += "Win10";
            } else {
                info += "Other";
            }
        } else if (os.indexOf("Mac") > -1) {
            info += "Mac";
        } else if (os.indexOf("X11") > -1) {
            info += "Unix";
        } else if (os.indexOf("Linux") > -1) {
            info += "Linux";
        } else {
            info += "Other";
        }
        info += "/";


        //判斷瀏覽器版本
        let isOpera = userAgent.indexOf("Opera") > -1; //判斷是否Opera瀏覽器
        let isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判斷是否IE瀏覽器
        let isEdge = userAgent.toLowerCase().indexOf("edge") > -1 && !isIE; //判斷是否IE的Edge瀏覽器
        let isIE11 = (userAgent.toLowerCase().indexOf("trident") > -1 && userAgent.indexOf("rv") > -1);

        if (/[Ff]irefox(\/\d+\.\d+)/.test(userAgent)) {
            tempArray = /([Ff]irefox)\/(\d+\.\d+)/.exec(userAgent);
            info += tempArray[1] + tempArray[2];
        } else if (isIE) {

            let version = "";
            let reIE = new RegExp("MSIE (\\d+\\.\\d+);");
            reIE.test(userAgent);
            let fIEVersion = parseFloat(RegExp["$1"]);
            if (fIEVersion == 7) {
                version = "IE7";
            } else if (fIEVersion == 8) {
                version = "IE8";
            } else if (fIEVersion == 9) {
                version = "IE9";
            } else if (fIEVersion == 10) {
                version = "IE10";
            } else {
                version = "0"
            }

            info += version;

        } else if (isEdge) {
            info += "Edge";
        } else if (isIE11) {
            info += "IE11";
        } else if (/[Cc]hrome\/\d+/.test(userAgent)) {
            tempArray = /([Cc]hrome)\/(\d+)/.exec(userAgent);
            info += tempArray[1] + tempArray[2];
        } else if (/[Vv]ersion\/\d+\.\d+\.\d+(\.\d)* *[Ss]afari/.test(userAgent)) {
            tempArray = /[Vv]ersion\/(\d+\.\d+\.\d+)(\.\d)* *([Ss]afari)/.exec(userAgent);
            info += tempArray[3] + tempArray[1];
        } else if (/[Oo]pera.+[Vv]ersion\/\d+\.\d+/.test(userAgent)) {
            tempArray = /([Oo]pera).+[Vv]ersion\/(\d+)\.\d+/.exec(userAgent);
            info += tempArray[1] + tempArray[2];
        } else if (navigator.userAgent.match("MSIE")) {
            info += "IE"
        } else if (navigator.userAgent.match("Safari")) {
            info += "Safari"
        } else {
            info += "unknown";
        }

        info += "/";

        if (/Android/i.test(navigator.userAgent)) {
            info += "Android"
        } else if (/webOS/i.test(navigator.userAgent)) {
            info += "webOS"
        } else if (/iPhone/i.test(navigator.userAgent)) {
            info += "iPhone"
        } else if (/iPad/i.test(navigator.userAgent)) {
            info += "iPad"
        } else if (/iPod/i.test(navigator.userAgent)) {
            info += "iPod"
        } else if (/BlackBerry/i.test(navigator.userAgent)) {
            info += "BlackBerry"
        } else if (/Windows/i.test(navigator.userAgent)) {
            info += "Windows"
        }


        return info;

    };

    getUserIsMobile(): boolean {
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent) ||
            (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.platform))) {

            return true;
            // ...
        }
        return false;

    }

    getOS(): string {
        let os = navigator.platform;
        let userAgent = navigator.userAgent;
        let info = "";
        let tempArray: any = "";
        //判斷作業系統
        if (os.indexOf("Win") > -1) {
            info = 'Windows';
        } else if (os.indexOf("Mac") > -1) {
            info += "Mac";
        } else if (os.indexOf("X11") > -1) {
            info += "Unix";
        } else if (os.indexOf("Linux") > -1) {
            info += "Linux";
        } else {
            info += "Other";
        }
        return info;

    }


    // getIpAddress() {
    //   return this.http
    //     .get('https://api.ipify.org/?format=json')
    //     .pipe(
    //       catchError(this.handleError)
    //     );
    // }

    // private handleError(error: HttpErrorResponse) {
    //   if (error.error instanceof ErrorEvent) {
    //     // A client-side or network error occurred. Handle it accordingly.
    //     console.error('An error occurred:', error.error.message);
    //   } else {
    //     // The backend returned an unsuccessful response code.
    //     // The response body may contain clues as to what went wrong,
    //     console.error(
    //       `Backend returned code ${error.status}, ` +
    //       `body was: ${error.error}`);
    //   }
    //   // return an observable with a user-facing error message
    //   return throwError(
    //     'Something bad happened; please try again later.');
    // }

    /**
     *  WT20250314001 myData自驗優化移至login後檢查瀏覽器版本
     *  2023/11/29 Richie
     *  單號:WT20231122002b
     *  修改內容:
     *      v2:改用物件丟給後端
     *      v4:修改顯示的畫面 有點醜
     */
    checkVersion() {
        const isMobile = this.isMobile();
        console.log("是否為手機端:", isMobile);
        if (isMobile) {
            this.getUserDevice();
        }
        // 計算api時間
        const startTime = Date.now();
        let endTime: number;

        const browserVersionData = this.getBrowserVersion();
        //取得版號
        const version = browserVersionData.toString().replace(/[^\d.]/g, "");
        this.userVersionData.browserName = this.browserName;
        this.userVersionData.browserVersion = version;
        this.userVersionData.mobileDeviceName = this.mobileDeviceName;
        this.userVersionData.mobileDeviceVersion = this.mobileDeviceVersion;
        this.userVersionData.isMobile = isMobile;

        this.genericService.checkVersion(this.userVersionData).subscribe(result => {
            endTime = Date.now();
            console.log(`login3 checkVersion API請求耗時: ${endTime - startTime}ms`);
            if ("1" === String(result) || "2" === String(result)) {
                const dialogRef = this.myDialogService.open(TimeoutComponent, {
                    width: "573px",
                    height: "200px",
                    hasCloseBtn: false,
                    centerTitle: true,
                    leftTitle: false,
                    confirmContent: "我知道了",
                    data: ("1" === String(result)) ? '您使用的作業系統版本非最新版本，為避免影響後續申請流程，請將作業系統更新至最新版本後再進行申辦，謝謝！' :
                        '您使用的瀏覽器非本行可支援瀏覽器，建議優請先使用 Chrome 瀏覽器進行申請，謝謝!',
                    style: DIALOG_TYPE.DIALOG
                })
                // 等待使用者關閉彈窗後，如果驗證不符再導轉到KGI網站
                dialogRef.afterClosed().subscribe(() => {
                    if ("2" === String(result)) {
                        // 先清瀏覽記錄
                        sessionStorage.clear();
                        window.location.replace(KIG_OFFICIAL_WEBSITE);
                        return;
                    }
                });
            }
        });
    }

    /**
     *  WT20250314001 myData自驗優化移至login後檢查瀏覽器版本
     *  2023/11/29 Richie
     *  單號:WT20231122002b
     *  修改內容:
     *      v3:取得手機系統名稱與版本 補上註解
     */
    getUserDevice() {
        const userAgentString = navigator.userAgent;
        const androidVersion = userAgentString.match(/Android (\d+\.?.*)/);
        const iOSVersion = userAgentString.match(/OS (\d+\.?\d+\.?.*) like Mac OS X/);

        if (androidVersion) {
            this.mobileDeviceName = 'android';
            this.mobileDeviceVersion = androidVersion[1];
        }

        if (iOSVersion) {
            this.mobileDeviceName = 'ios';
            this.mobileDeviceVersion = iOSVersion[1];
        }
    }

    /**
     *  WT20250314001 myData自驗優化移至login後檢查瀏覽器版本
     *  2023/11/29 Richie
     *  單號:WT20231122002b
     *  修改內容:
     *      v3:是否為手機端 補上註解
     */
    isMobile(): boolean {
        if (navigator.userAgent.match(/Mobi/i) ||
            navigator.userAgent.match(/Android/i) ||
            navigator.userAgent.match(/iPhone/i)) {
            return true;
        }
        return false;
    }

    /**
     *  WT20250314001 myData自驗優化移至login後檢查瀏覽器版本
     *  2023/11/29 Richie
     *  單號:WT20231122002b
     *  修改內容:
     *      v3:取得瀏覽器名稱與版本號 補上註解
     */
    getBrowserVersion() {
        var agent = navigator.userAgent.toLowerCase();
        var regStr_edge = /edg\/[\d.]+/gi;
        var regStr_ff = /(firefox|fxios)\/[\d.]+/gi;
        var regStr_chrome = /(chrome|chromium|crios)\/[\d.]+/gi;
        var regStr_saf = /safari\/[\d.]+/gi;

        //Edge 一定要先判斷Edge 因為userAgent有些不必要的資訊
        if (agent.indexOf("edg") > 0) {
            this.browserName = 'edge';
            return agent.match(regStr_edge);
        }
        //Firefox
        if (agent.indexOf("firefox") > 0 || agent.indexOf("fxios") > 0) {
            this.browserName = 'firefox';
            return agent.match(regStr_ff);
        }

        //Safari
        if (agent.indexOf("safari") > 0 && agent.indexOf("chrome") < 0 && agent.indexOf("chromium") < 0 && agent.indexOf("crios") < 0) {
            this.browserName = 'safari';
            return agent.match(regStr_saf);
        }

        //Chrome
        if (agent.indexOf("chrome") > 0 || agent.indexOf("chromium") > 0 || agent.indexOf("crios") > 0) {
            this.browserName = 'chrome';
            return agent.match(regStr_chrome);
        }
    }
}
