import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {SERVER_URL} from '../app.constants';

@Injectable({
  providedIn: 'root'
})
export class BackToTheHomepageService {

  constructor(private http: HttpClient) { }

  
  backToTheHomepage(uniqId: any): Observable<any> {
    let params = new HttpParams();
    params = params.set('uniqId', uniqId);
    return this.http.post<any>(SERVER_URL + 'publicApi/backToTheHomepage', params);
  }

}

