import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Injectable({
    providedIn: 'root'
})
export class GlobalDataService {
    private _currentPageType: number;
    public _currentPageTypeSubject = new BehaviorSubject<number>(0); // 即時監聽



    private _currentStationName: string;

    constructor() {
    }

    private _data: any;


    /**
     * type 是 產品大分類
     * 就是在每支前端產品的 .path.ts 檔案中的 MODULE
     *
     * url/obd/產品大分類/前端頁面名?參數們
     * 例如：
     * http://172.31.1.117/obd/aio/A_g_identity?PProductType=aio-loan-cc-d3&shortUrl=bpvn4dnl&productId=loan:2-3,cc:ALLCARD,d3:6&entry=KBOTE0009
     * type 就是 aio
     */
    // 申辦型態
    set type(type: string) {
        console.log('設定本次服務申辦型態: ' + type);
        sessionStorage.setItem('_type', type);
    }

    get type() {
        // console.log('取得本次服務申辦型態: '+ sessionStorage.getItem('_type'));
        return sessionStorage.getItem('_type');
    }

    set token(token: string) {
        console.log('set token via type: ' + this.type);
        if (this.type) {
            sessionStorage.setItem(this.type + '_token', JSON.stringify(token));
        } else {
            sessionStorage.setItem('_token', JSON.stringify(token));
        }
    }

    get token() {
        console.log('get token type: ' + this.type);
        if (this.type) {console.log('get token via type: ' + this.type); }
        if (this.type) {
            return JSON.parse(sessionStorage.getItem(this.type + '_token'));
        } else {
            return JSON.parse(sessionStorage.getItem('_token'));
        }
    }


    set data(data: any) {
        // set data(data: new() => T) {
        if (data) {
            if (this.type) {
                // aio_data
                sessionStorage.setItem(this.type + '_data', JSON.stringify(data));
            } else {
                sessionStorage.setItem('_data', JSON.stringify(data));
            }
            this._data = data;
        }
    }

    // get data(): new() => T {
    get data(): any {
        if (this.type) {

            // let clientWithType = Object.assign(new CIFInfoModel(),  JSON.parse(sessionStorage.getItem(this.type+'_data')))
            // // return clientWithType;
            return JSON.parse(sessionStorage.getItem(this.type + '_data'));
        } else {
            return JSON.parse(sessionStorage.getItem('_data'));
        }
    }

    setData(data: any) {
        if (data) {
            if (this.type) {
                sessionStorage.setItem(this.type + '_data', JSON.stringify(data));
            } else {
                sessionStorage.setItem('_data', JSON.stringify(data));
            }
            this._data = data;
        }
    }

    getData<T>(T): T {
        // getData<T>(factory: Factory<T>): T {
        const userFactory = new Factory<T>(T);
        // const userFactory = new Factory<CIFInfoModel>(CIFInfoModel);
        return Object.assign(userFactory.getNew(), JSON.parse(sessionStorage.getItem(this.type + '_data')));
    }

    /**
     * aioRoutingType 是 PProductType
     * 理論上 可看出 申辦產品的完整組合是啥
     * 例如：http://172.31.1.117/obd/aio/A_g_identity?PProductType=aio-loan-cc-d3&shortUrl=bpvn4dnl&productId=loan:2-3,cc:ALLCARD,d3:6&entry=KBOTE0009
     * PProductType=aio-loan-cc-d3
     * aioRoutingType 就是 aio-loan-cc-d3
     */
    get aioRoutingType(): string {
        if (environment.useMockupRouting) {
            return localStorage.getItem('_aioRoutingMockupType');
        }

        return sessionStorage.getItem('_aioRoutingType');
    }

    set aioRoutingType(type: string) {
        if (environment.useMockupRouting) {
            localStorage.setItem('_aioRoutingMockupType', type);
        }

        sessionStorage.setItem('_aioRoutingType', type);
    }

    get aioDepartId(): string {
        return sessionStorage.getItem('_aioDepartId');
    }

    set aioDepartId(id: string) {
        sessionStorage.setItem('_aioDepartId', id);
    }

    get aioProductId(): string {
        return sessionStorage.getItem('_aioProductId');
    }

    set aioProductId(id: string) {
        sessionStorage.setItem('_aioProductId', id);
    }

    get aioShortUrl(): string {
        return sessionStorage.getItem('_aioShortUrl');
    }

    set aioShortUrl(url: string) {
        sessionStorage.setItem('_aioShortUrl', url);
    }

    get aioMember(): string {
        return sessionStorage.getItem('_aioMember');
    }

    set aioMember(member: string) {
        sessionStorage.setItem('_aioMember', member);
    }

    get aioChannelid(): string {
        return sessionStorage.getItem('_aioChannelid');
    }

    set aioChannelid(Channelid: string) {
        sessionStorage.setItem('_aioChannelid', Channelid);
    }

    // get aioPrjCode(): string {
    //     return sessionStorage.getItem('_aioPrjCode');
    // }
    //
    // set aioPrjCode(PrjCode: string) {
    //     sessionStorage.setItem('_aioPrjCode', PrjCode);
    // }

    get entryOther(): string {
        return sessionStorage.getItem('_entryOther');
    }

    set entryOther(entryOther: string) {
        sessionStorage.setItem('_entryOther', entryOther);
    }

    /**
     * r2-3348
     * 證券A 新增 Sourcetype
     */
    get aioSourceType(): string {
        return sessionStorage.getItem('_sourceType');
    }

    set aioSourceType(sourceType: string) {
        sessionStorage.setItem('_sourceType', sourceType);
    }


    /**
     * 補件使用
     *  1: 信用卡
     *  2: 貸款
     *  3: 薪轉
     *  4: 數三
     *  5: 多合一
     */
    set applyProduct(applyProduct: string) {
        sessionStorage.setItem('applyProduct', applyProduct);
    }
    /**
     * 補件使用
     */
    get applyProduct(): string {
        return sessionStorage.getItem('applyProduct');
    }

    /**
     * 補件使用 線上或紙本
     */
    set additionalIsBill(isBill: string) {
        sessionStorage.setItem('isBill', isBill);
    }
    /**
     * 補件使用 線上或紙本
     */
    get additionalIsBill(): string {
        return sessionStorage.getItem('isBill');
    }

    /**
     * 補件使用
     *  app.constants -> ADD_MENU
     *  01: 信用卡、貸款、多合一沒有D3 (身分證 + 財力證明 + 其他文件)
     *  02: 多合一有D3 (身分證 + 第二證件 + 財力證明 + 其他文件)
     *  03: 數三、薪轉 (身分證)
     *  04: 數三、薪轉 (第二證件)
     *  05: 數三、薪轉 (身分證 + 第二證件)
     */
    set menuMode(menuMode: string) {
        sessionStorage.setItem('menuMode', menuMode);
    }
    /**
     * 補件使用
     */
    get menuMode(): string {
        return sessionStorage.getItem('menuMode');
    }
    /**
     * 補件使用 繳件單位
     */
    set additionalUnitId(unitId: string) {
        sessionStorage.setItem('additionalUnitId', unitId);
    }
    get additionalUnitId(): string {
        return sessionStorage.getItem('additionalUnitId');
    }
    /**
     * 補件使用 線下件 產品種類
     *  信用卡：COMPONENT_AIO_FLOW_TYPE.CC
     *  貸款：COMPONENT_AIO_FLOW_TYPE.LOAN
     */
    set offlineProductType(offlineProductType: string) {
        sessionStorage.setItem('offlineProductType', offlineProductType);
    }
    get offlineProductType(): string {
        return sessionStorage.getItem('offlineProductType');
    }
    /**
     * 補件使用
     */
    set storageRoute(storageRoute: string) {
        sessionStorage.setItem('storageRoute', storageRoute);
    }
    get storageRoute(): string {
        return sessionStorage.getItem('storageRoute');
    }
    /**
     * 補件使用
     */
    set additionalIdNo(idNo: string) {
        sessionStorage.setItem('additionalIdNo', idNo);
    }
    get additionalIdNo(): string {
        return sessionStorage.getItem('additionalIdNo');
    }
    /**
     * 補件使用 案編或是身分證號
     */
    set additionalUniqId(uniqId: string) {
        sessionStorage.setItem('additionalUniqId', uniqId);
    }
    get additionalUniqId(): string {
        return sessionStorage.getItem('additionalUniqId');
    }
    /**
     * 補件使用
     * <!-- LOAN='01', LOAN_CC='02', CC='05', AIO_CC='06', AIO_LOAN='07', AIO_D3='08', AIO_LOAN_D3='09', AIO_LOAN_CC='10', AIO_CC_D3='11', AIO_LOAN_CC_D3='15', SAL='12', D3='13' -->
     */
    set additionalUniqType(uniqType: string) {
        sessionStorage.setItem('additionalUniqType', uniqType);
    }
    get additionalUniqType(): string {
        return sessionStorage.getItem('additionalUniqType');
    }
    /**
     * 補件使用
     */
    set additionalDocStatus(additionalDocStatus: any) {
        sessionStorage.setItem('additionalDocStatus', JSON.stringify(additionalDocStatus));
    }
    get additionalDocStatus(): any {
        return JSON.parse(sessionStorage.getItem('additionalDocStatus'));
    }
    /**
     * 補件使用
     */
    set idDocStatus(idDocStatus: string) {
        sessionStorage.setItem('idDocStatus', idDocStatus);
    }
    get idDocStatus(): string {
        return sessionStorage.getItem('idDocStatus');
    }
    /**
     * 補件使用
     */
    set financialDocStatus(financialDocStatus: string) {
        sessionStorage.setItem('financialDocStatus', financialDocStatus);
    }
    get financialDocStatus(): string {
        return sessionStorage.getItem('financialDocStatus');
    }
    /**
     * 補件使用
     */
    set secondDocStatus(secondDocStatus: string) {
        sessionStorage.setItem('secondDocStatus', secondDocStatus);
    }
    get secondDocStatus(): string {
        return sessionStorage.getItem('secondDocStatus');
    }
    /**
     * 補件使用
     */
    set otherDocStatus(otherDocStatus: string) {
        sessionStorage.setItem('otherDocStatus', otherDocStatus);
    }
    get otherDocStatus(): string {
        return sessionStorage.getItem('otherDocStatus');
    }
    /**
     * LoanIdentityVerifyComponent
     */
    set loanItemType(loanItemType: string) {
        sessionStorage.setItem('loanItemType', loanItemType);
    }
    get loanItemType(): string {
        return sessionStorage.getItem('loanItemType');
    }
    /**
     * 補件使用
     * 於財力證明 正確顯示需要使用
     */
    set additionalProductId(additionalProductId: string) {
        sessionStorage.setItem('additionalProductId', additionalProductId);
    }
    get additionalProductId(): string {
        return sessionStorage.getItem('additionalProductId');
    }
    /**
     * 補件使用
     * 於財力證明 是否出現
     */
    get additionalIsLonaApplyMoney(): string {
        return sessionStorage.getItem('additionalIsLonaApplyMoney');
    }
    set additionalIsLonaApplyMoney(additionalIsLonaApplyMoney: string) {
        sessionStorage.setItem('additionalIsLonaApplyMoney', additionalIsLonaApplyMoney);
    }
    /**
     * 補件使用
     * 選擇 C3 時
     * 判斷此次登入是否已驗身過
     */
    get additionalVerificationCanUse(): string {
        return sessionStorage.getItem('additionalVerificationCanUse');
    }
    set additionalVerificationCanUse(additionalVerificationCanUse: string) {
        sessionStorage.setItem('additionalVerificationCanUse', additionalVerificationCanUse);
    }
    /**
     * 補件使用
     * 產品類型是否有包含貸款
     */
    get additionalIsSelectLoan(): boolean {
        return  JSON.parse(sessionStorage.getItem('additionalIsSelectLoan'));
    }
    set additionalIsSelectLoan(additionalIsSelectLoan: boolean) {
        sessionStorage.setItem('additionalIsSelectLoan', JSON.stringify(additionalIsSelectLoan));
    }
    /**
     * 補件使用
     * 可補件的 AioCaseData -> Phone
     */
    get additionalPhone(): string {
        return sessionStorage.getItem('additionalPhone');
    }
    set additionalPhone(additionalPhone: string) {
        sessionStorage.setItem('additionalPhone', additionalPhone);
    }
    /**
     * 補件使用
     * 判斷 OTP
     */
    set additionalFirstVisitKey(additionalFirstVisitKey: boolean) {
        sessionStorage.setItem('additionalFirstVisitKey', JSON.stringify(additionalFirstVisitKey));
    }
    get additionalFirstVisitKey(): boolean {
        return  JSON.parse(sessionStorage.getItem('additionalFirstVisitKey'));
    }

    /**
     * 信用卡調額使用
     * 案件編號
     */
    set creditLimitUniqId(creditLimitUniqId: string) {
        sessionStorage.setItem('creditLimitUniqId', creditLimitUniqId);
    }
    get creditLimitUniqId(): string {
        return sessionStorage.getItem('creditLimitUniqId');
    }
    /**
     * 信用卡調額使用
     * 產品類型
     */
    set creditLimitActionType(creditLimitActionType: string) {
        sessionStorage.setItem('creditLimitActionType', creditLimitActionType);
    }
    get creditLimitActionType(): string {
        return sessionStorage.getItem('creditLimitActionType');
    }

    /**
     * 財力證明使用
     * 圖檔名稱 Array
     */
    set financialDialogData(financialDialogData: any) {
        sessionStorage.setItem('financialDialogData', JSON.stringify(financialDialogData));
    }
    get financialDialogData(): any {
        return JSON.parse(sessionStorage.getItem('financialDialogData'));
    }
    /**
     * 財力證明使用
     * 圖檔名稱
     */
    set financialDialogDataName(financialDialogDataName: any) {
        sessionStorage.setItem('financialDialogDataName', JSON.stringify(financialDialogDataName));
    }
    get financialDialogDataName(): any {
        return JSON.parse(sessionStorage.getItem('financialDialogDataName'));
    }
    /**
     * 財力證明使用
     * 圖檔類型 Array
     */
    set financialDialogDataType(financialDialogData: any) {
        sessionStorage.setItem('financialDialogDataType', JSON.stringify(financialDialogData));
    }
    get financialDialogDataType(): any {
        return JSON.parse(sessionStorage.getItem('financialDialogDataType'));
    }
    /**
     * 手機嵌入web 免登流程使用
     */
    set startFromMobile(startFromMobile: string) {
        sessionStorage.setItem('startFromMobile', startFromMobile);
    }
    get startFromMobile(): string {
        return sessionStorage.getItem('startFromMobile');
    }
    /**
     * 多合一申辦 和 單一申辦都會需要entry
     * 多合一申辦的 entry 資料會是 qrcode 那邊給的
     */
    get entry(): string {
        return sessionStorage.getItem('_entry');
    }
    set entry(entry: string) {
        sessionStorage.setItem('_entry', entry);
    }

    set ksCifToken(ksCifToken: string) {
        sessionStorage.setItem('_ksCifToken', ksCifToken);
    }
    get ksCifToken(): string {
        return sessionStorage.getItem('_ksCifToken');
    }

    set csToken(CS_Token: string) {
        sessionStorage.setItem('CS_Token', CS_Token);
    }
    get csToken(): string {
        return sessionStorage.getItem('CS_Token');
    }

    set csUniqId(CS_UniqId: string) {
        sessionStorage.setItem('CS_UniqId', CS_UniqId);
    }
    get csUniqId(): string {
        return sessionStorage.getItem('CS_UniqId');
    }

    set themePath(themePath: string) {
        console.log('themePath:' , themePath);
        sessionStorage.setItem('_themePath', themePath);
    }
    get themePath(): string {
        return sessionStorage.getItem('_themePath');
    }

    set MGM(mgm: string) {
        console.log('_mgm:' , mgm);
        sessionStorage.setItem('_MGM', mgm);
    }
    get MGM(): string {
        return sessionStorage.getItem('_MGM');
    }

    clearData(type?: string) {
        console.log('clear data in storage!!!');
        console.log('clear data in storage!!!');
        console.log('clear data in storage!!!');
        console.log('clear data in storage!!!');
        console.log('clear data in storage!!!');

        if (type && type === 'appToken') {
            sessionStorage.removeItem(type + '_token');
            sessionStorage.removeItem(type + '_case_data');
            // sessionStorage.removeItem(type + '_additionalData')
            // sessionStorage.removeItem('_type');
        } else {
            console.log('clear all data in session');
            sessionStorage.clear();
        }
        console.log('type: ', type);
    }

    /**
     * 進件查詢使用
     * */
    setKgiAndAio(data: any) {

        if (data) {
            if (this.kgiAndAio) {
                sessionStorage.setItem('_kgiAndAio', JSON.stringify(data));
            } else {
                sessionStorage.setItem('_kgiAndAio', JSON.stringify(data));
            }
            this.kgiAndAio = data;
        }
    }

    set kgiAndAio(progress: object) {

        sessionStorage.setItem('_kgiAndAio', JSON.stringify(progress));
    }
    get kgiAndAio() {
        return JSON.parse(sessionStorage.getItem('_kgiAndAio'));
    }

    set timeoutRedirectUrl(url: string) {
        sessionStorage.setItem('_timeoutRedirectUrl', url);
    }
    get timeoutRedirectUrl() {
        return sessionStorage.getItem('_timeoutRedirectUrl');
    }

    set npsUrl(url: string) {
        sessionStorage.setItem('_npsUrl', url);
    }
    get npsUrl() {
        return sessionStorage.getItem('_npsUrl');
    }

    set trustAppToken(appToken: string) {
        sessionStorage.setItem('trust_token', appToken);
    }
    get trustAppToken() {
        return sessionStorage.getItem('trust_token');
    }

    removeTrustAppToken() {
        sessionStorage.removeItem('trust_token');
    }

    set freeLoginToken(appToken: string) {
        sessionStorage.setItem('_freeLoginToken', appToken);
    }
    get freeLoginToken() {
        return sessionStorage.getItem('_freeLoginToken');
    }

    get errorMsg(): string {
        return sessionStorage.getItem('_errorMsg');
    }
    set errorMsg(errorMsg: string) {
        sessionStorage.setItem('_errorMsg', errorMsg);
    }

    // 2022/11/17  vickey app免登需求;
    // 1. Obd timeout 不呼叫
    // 2.累計三次450後不呼叫
    // 3.onBoarding 未timeOut時，每5min 發updatesessionToken
    // 1 timeout 0 正常
    set obd_timeout(timeoutStatus: string) {
        sessionStorage.setItem('obd_timeout', timeoutStatus);
    }
    get obd_timeout() {
        return sessionStorage.getItem('obd_timeout');
    }

    // app time out
    set obd_app_timeout_thrice(status: boolean) {
        sessionStorage.setItem('obd_app_timeout_thrice', JSON.stringify(status));
    }
    get obd_app_timeout_thrice(): boolean {
        return  JSON.parse(sessionStorage.getItem('obd_app_timeout_thrice'));
    }

    set app_token(appToken: string){
        sessionStorage.setItem('app_token', appToken);
    }
    get app_token(){
        return sessionStorage.getItem('app_token');
    }

    set app_idno(idno: string){
        sessionStorage.setItem('app_idno', idno);
    }
    get app_idno(){
        return sessionStorage.getItem('app_idno');
    }

    /**
     * 信用卡群組
     * @param cardGroup
     */
    set cardGroup(cardGroup: string){
        sessionStorage.setItem('app_iccGroupdno', cardGroup);
    }
    get cardGroup(){
        return sessionStorage.getItem('cardGroup');
    }

    /**
     * 20240709 Momo WT20240708003
     * 因為 init 執行時有可能失敗，所以用 isDeviceId 來記錄init時是否有成功，是否需要在下一頁時再執行一次
     * 成功： Y | 失敗： N
     */
    set isDeviceId(isDeviceId: string){
        sessionStorage.setItem('isDeviceId', isDeviceId);
    }
    get isDeviceId(): string{
        return sessionStorage.getItem('isDeviceId');
    }


    //---begin 預約開戶_網行銀資料_預約分行日期時段

    set otherSetting(data: any) {
        if (data) {
            if (this.type) {
                sessionStorage.setItem(this.type + '_otherSetting', JSON.stringify(data));
            } else {
                sessionStorage.setItem('_otherSetting', JSON.stringify(data));
            }
        }
    }

    get otherSetting(): any {
        if (this.type) {
            return JSON.parse(sessionStorage.getItem(this.type + '_otherSetting'));
        } else {
            return JSON.parse(sessionStorage.getItem('_otherSetting'));
        }
    }

    //---end 預約開戶_網行銀資料_預約分行日期時段


    // ------------------------ otp 頁使用 ---------------------------
    /**
     * otp 識別碼
     */
    set dynacPwd(dynacPwd: string){
        sessionStorage.setItem('dynacPwd', dynacPwd);
    }
    get dynacPwd(){
        return sessionStorage.getItem('dynacPwd');
    }

    /**
     * otp 發送計時
     */
    set sendTimeText(sendTimeText: string){
        sessionStorage.setItem('sendTimeText', sendTimeText);
    }
    get sendTimeText(){
        return sessionStorage.getItem('sendTimeText');
    }

    /**
     * otp 手機號碼
     */
    set displayPhone(displayPhone: string){
        sessionStorage.setItem('displayPhone', displayPhone);
    }
    get displayPhone(){
        return sessionStorage.getItem('displayPhone');
    }

    /**
     * otp 結束時間
     */
    set endTime(endTime: string){
        sessionStorage.setItem('endTime', endTime);
    }
    get endTime(): string{
        return sessionStorage.getItem('endTime');
    }

    /**
     * sk
     */
    set sk(sk: string){
        sessionStorage.setItem('sk', sk);
    }
    get sk(): string{
        return sessionStorage.getItem('sk');
    }

    /**
     * txnId
     */
    set txnId(txnId: string){
        sessionStorage.setItem('txnId', txnId);
    }
    get txnId(): string{
        return sessionStorage.getItem('txnId');
    }

    /**
     * txnDate
     */
    set txnDate(txnDate: string){
        sessionStorage.setItem('txnDate', txnDate);
    }
    get txnDate(): string {
        return sessionStorage.getItem('txnDate');
    }

    /**
     * 是否已於 init 執行過
     */
    set firstVisitKey(firstVisitKey: string){
        sessionStorage.setItem('firstVisitKey', firstVisitKey);
    }
    get firstVisitKey(): string{
        return sessionStorage.getItem('firstVisitKey');
    }
    // ------------------------ otp 頁使用 ---------------------------

    /**
     * ******** WT2024050300
     * 證券A 新增 Sourcetype
     */
    get existKS(): string {
        return sessionStorage.getItem('_existKS');
    }
    set existKS(existKS: string) {
        sessionStorage.setItem('_existKS', existKS);
    }
    /**
     * ******** WT20240621004 Maggie <br>
     * 儲存 PCode2566 是以本行還他行驗身 <br>
     * 本行： Y | 他行： N
     */
    set isKgiBank(isKgiBank: string){
        sessionStorage.setItem('isKgiBank', isKgiBank);
    }
    get isKgiBank(): string{
        return sessionStorage.getItem('isKgiBank');
    }

    /**
     * Case data for AIO flow
     */
    set caseData(caseData: any) {
        if (this.type) {
            sessionStorage.setItem(this.type + '_case_data', JSON.stringify(caseData));
        } else {
            sessionStorage.setItem('_case_data', JSON.stringify(caseData));
        }
    }
    
    get caseData(): any {
        if (this.type) {
            return JSON.parse(sessionStorage.getItem(this.type + '_case_data'));
        } else {
            return JSON.parse(sessionStorage.getItem('_case_data'));
        }
    }

}

export class Factory<T> {
    constructor(private type: new () => T) { }

    getNew(): T {
        return new this.type();
    }
}
