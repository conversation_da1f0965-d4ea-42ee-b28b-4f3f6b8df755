import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, ReplaySubject } from 'rxjs';
import { Account } from '../model/account.model';
import { UserModel } from '../../../demo/demo-identity-verify/user.model';
import { ApplyStartVO } from '../../../generic/model/apply-start.model';
import { SERVER_URL } from '../app.constants';
import { tap } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AccountService {

    // prefix : string = '/api/v1/login';
    // prefix : string = '/demo/flow/start';
    prefix : string = 'demo/login';

    private userIdentity: Account | null = null;
    private authenticationState = new ReplaySubject<Account | null>(1);
    private accountCache$?: Observable<Account | null>;

    constructor(
        private http: HttpClient,

    ) {}

    login(account: Account): Observable<any> {
        // this.authenticationState.next(this.userIdentity);
        return this.http.post<Object>(SERVER_URL + this.prefix, account);
    }

    loginDemo(userModel: UserModel): Observable<any> {
        // this.authenticationState.next(this.userIdentity);
        console.log('login: ' + this.prefix);
        return this.http.post<Object>(SERVER_URL + 'demo/login', userModel);
    }

    // AIO LOAN 登入
    login3(applyStartVO: ApplyStartVO): Observable<any> {
        // this.authenticationState.next(this.userIdentity);
        // console.log('login: ' + '/aio/login');
        let startTime = Date.now();

        return this.http.post<Object>(SERVER_URL + 'aioApi/login', applyStartVO)
            .pipe(
                tap(response => {
                    const endTime = Date.now();
                    const executionTime = endTime - startTime;
                    console.log(`API 'aioApi/login' 處理時間: ${executionTime}ms (${executionTime/1000}秒)`);

                    // 將執行時間添加到響應對象中，而不改變其原始結構
                    response._executionTime = executionTime;
                })
            );
    }

    // LOAN 登入
    loginLoan(applyStartVO: ApplyStartVO): Observable<any> {
        // this.authenticationState.next(this.userIdentity);
        // console.log('login: ' + '/aio/login');
        return this.http.post<Object>(SERVER_URL + 'loanApi/login', applyStartVO);
    }

    loginD3(applyStartVO: ApplyStartVO): Observable<any> {
        // this.authenticationState.next(this.userIdentity);
        // console.log('login: ' + '/d3/login');
        return this.http.post<Object>(SERVER_URL + 'd3Api/login', applyStartVO);
    }

    // authenticate(identity: Account): void {
    //     this.userIdentity = identity;
    //     this.authenticationState.next(this.userIdentity);
    // }

    hasAnyAuthority(authorities: string[] | string): boolean {
        if (!this.userIdentity) {
            return false;
        }
        if (!Array.isArray(authorities)) {
            authorities = [authorities];
        }
        //return this.userIdentity.authorities.some((authority: string) => authorities.includes(authority));
        return true;
    }

    // identity(force?: boolean): Observable<Account | null> {
    //     if (!this.accountCache$ || force || !this.isAuthenticated()) {
    //         this.accountCache$ = this.fetch().pipe(
    //             catchError(() => of(null)),
    //             tap((account: Account | null) => {
    //                 this.authenticate(account);
    //
    //                 if (account) {
    //                     this.navigateToStoredUrl();
    //                 }
    //             }),
    //             shareReplay()
    //         );
    //     }
    //     return this.accountCache$;
    // }
    //
    // isAuthenticated(): boolean {
    //     return this.userIdentity !== null;
    // }

    getAuthenticationState(): Observable<Account | null> {
        return this.authenticationState.asObservable();
    }

    // private fetch(): Observable<Account> {
    //     return this.http.get<Account>(this.prefix + '/account');
    // }
    //
    // private navigateToStoredUrl(): void {
    //     // previousState can be set in the authExpiredInterceptor and in the userRouteAccessService
    //     // if login is successful, go to stored previousState and clear previousState
    //     const previousUrl = this.stateStorageService.getUrl();
    //     if (previousUrl) {
    //         this.stateStorageService.clearUrl();
    //         this.router.navigateByUrl(previousUrl);
    //     }
    // }
}
