import {Injectable} from '@angular/core';
import {HttpClient, HttpErrorResponse, HttpHeaders, HttpParams} from '@angular/common/http';

import {ResponseVO} from '../model/responseVO';
import {Observable, throwError} from 'rxjs';
import {SERVER_URL} from '../app.constants';
import {EncryptService} from "./encrypt.service";

@Injectable({
  providedIn: 'root'
})
export class UserDataService {
  routingType = "";

  constructor(
      private http: HttpClient,
      private encryptService: EncryptService
  ) {}

  public httpOptions = {
    headers: new HttpHeaders({
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Max-Age': '86400'
    })
  };


  getBookingDate() {
    return this.http.get<ResponseVO<any>>(SERVER_URL + 'aioApi/getBookingDate');
  }

  getPrjCodeRPL() {
    return this.http.get<ResponseVO<any>>(SERVER_URL + 'aioApi/getPrjCodeRPL');
  }

  caseNonHouseLoanSetting() {
    return this.http.get<any>(SERVER_URL + 'aioApi/caseNonHouseLoanSetting');
  }

  caseTimeOutSendMail(tempUrl: string) {
    console.log('caseTimeOutSendMail');
    let params = new HttpParams();
    params = params.set('tempUrl', tempUrl);
    return this.http.get<any>(SERVER_URL + 'aioApi/caseTimeOutSendMail', {params:params});
  }

  /** SAL 取得 eop_shortUrl 內是否有設定 帳單類型 **/
  getEOPUrlPassBookTW() {
    return this.http.get<any>(SERVER_URL + 'aioApi/getEOPUrlPassBookTW');
  }

  /** 判斷是否 可開立信託帳戶 **/
  getTrustAcc(): Observable<boolean> {
    return this.http.get<boolean>(SERVER_URL + 'publicApi/queryTrustAcc');
  }

  /** 判斷是否 可開立外幣帳戶 **/
  getForeignAcc() {
    return this.http.get<any>(SERVER_URL + 'publicApi/queryForeignAcc');
  }

  /** 儲存DeviceId **/
  setDeviceId(uniqId, deviceId, type) {
    let params = new HttpParams();
    params = params.set('uniqId', uniqId);
    params = params.set('deviceId', deviceId);
    params = params.set('type', type);
    return this.http.get<any>(SERVER_URL + 'publicApi/deviceId', {params: params});
  }
  
  /** 儲存DeviceId with start and end Time **/
  setDeviceIdWithTime(uniqId, deviceId, type, startTime, endTime) {
    let params = new HttpParams();
    params = params.set('uniqId', uniqId);
    params = params.set('deviceId', deviceId);
    params = params.set('type', type);
	params = params.set('inStartTime', startTime);
	params = params.set('inEndTime', endTime);
    return this.http.get<any>(SERVER_URL + 'publicApi/saveDeviceIdWithTime', {params: params});
  }


  /**
   * 取JSON
   * return any
   */
  private getJSON(key: string): any {
    return sessionStorage.getItem(key)
      ? JSON.parse(sessionStorage.getItem(key))
      : undefined;
  }

  private handleError(error: HttpErrorResponse) {
    if (error.error instanceof ErrorEvent) {
      // A client-side or network error occurred. Handle it accordingly.
      console.error('An error occurred:', error.error.message);
    } else {
      // The backend returned an unsuccessful response code.
      // The response body may contain clues as to what went wrong,
      console.error(
          `Backend returned code ${error.status}, ` +
          `body was: ${error.error}`);
    }
    // return an observable with a user-facing error message
    return throwError(
        'Something bad happened; please try again later.');
  }

  //取得擔保品門牌
  getCollateral(idno:string): Observable<any>{
    let param = new HttpParams();
    param = param.set('idno', idno);
    return this.http.post<any>(SERVER_URL + 'hlApi/getCollateral', param);
  }


  updateUploadTime(uniqId: string, operate: string): Observable<any> {
    let param = new HttpParams();
    param = param.set('uniqId', uniqId);
    param = param.set('operate', operate);
    console.log('param', param)
    return this.http.post<any>(SERVER_URL + 'publicApi/updateUploadTime', param);

  }

  getPersonData(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getPersonData');
  }

}
