import { Injectable } from '@angular/core';
import {Base64} from "js-base64";

declare let CGJSCrypt: any;
declare let UTIL: any;

@Injectable({
    providedIn: 'root'
})
export class EncryptService {
    constructor() {}

    encrypt(publicKey: string, val: string) {
        // 日期格式YYYY-MM-DD HH24:MI:SS
        // 範例:2014-07-10 15:37:00
        const now = new Date();
        const str_year = now.getFullYear();
        const str_month =
            ('' + (now.getMonth() + 1)).length === 1
                ? '0' + (now.getMonth() + 1)
                : '' + (now.getMonth() + 1);
        const str_date =
            ('' + now.getDate()).length === 1
                ? '0' + now.getDate()
                : '' + now.getDate();
        const str_hours =
            ('' + now.getHours()).length === 1
                ? '0' + now.getHours()
                : '' + now.getHours();
        const str_minutes =
            ('' + now.getMinutes()).length === 1
                ? '0' + now.getMinutes()
                : '' + now.getMinutes();
        const str_seconds =
            ('' + now.getSeconds()).length === 1
                ? '0' + now.getSeconds()
                : '' + now.getSeconds();
        const str_yyyymmdd_hh24miss =
            str_year +
            '-' +
            str_month +
            '-' +
            str_date +
            ' ' +
            str_hours +
            ':' +
            str_minutes +
            ':' +
            str_seconds;
        // 網銀登入-使用者代碼加密
        const base64_usno = CGJSCrypt.CertEncrypt(
            publicKey,
            val + str_yyyymmdd_hh24miss,
            0
        );
        const obj_usno = CGJSCrypt.DecodeBase64(base64_usno);
        return UTIL.bytesToHex(obj_usno);
    }


    /** base64 decode 後轉 Json 
     *  範例: const data :AIOCaseDataVO = this.encryptService.decodeBase64ToJson(rtnObj)
     */
    decodeBase64ToJson<T>(data: any): T {
        try {
            // const decodedData = atob(data); // 解码为二进制数据
            // // 将字符串转换为 Uint8Array
            // const byteArray = new Uint8Array(decodedData.length);
            // for (let i = 0; i < decodedData.length; i++) {
            //     byteArray[i] = decodedData.charCodeAt(i);
            // }
            // const respVO = new TextDecoder("utf-8").decode(byteArray); // 将二进制数据转换为文本
            const respVO = Base64.decode(data);
            const parsedData: T = JSON.parse(respVO);
            return parsedData;
        } catch (error) {
            console.error('Failed to parse JSON:', error);
            throw new Error();
        }
    }
}
