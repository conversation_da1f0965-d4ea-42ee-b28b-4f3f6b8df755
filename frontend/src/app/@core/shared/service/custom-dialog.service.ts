import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CustomDialogService {

  constructor() { }

  alert(message: string): { afterClosed: () => Observable<any> } {
    // Simple alert implementation
    return {
      afterClosed: () => of(true)
    };
  }

  commonTermsDialogSpinner2(title: string, content: string, showButtons: boolean): void {
    // Terms dialog implementation
    console.log('Terms dialog:', title, content);
  }

}