import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

/**
 * 臨時的 Terms Service
 * TODO: 待 loan 模組建立後移除此檔案
 */
@Injectable({
  providedIn: 'root'
})
export class TermsService {
  
  constructor() { }
  
  getTermsWithTermsName(termsName: string): Observable<any> {
    // 暫時返回空資料
    return of({
      title: termsName,
      content: '',
      version: '1.0.0'
    });
  }
  
  getTerms(termId: string): Observable<any> {
    return of({
      id: termId,
      content: '',
      version: '1.0.0'
    });
  }
}