import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ResponseVO } from '../model/responseVO';
import { RoutingModel } from '../model/routing.model';
import { MOMENT_TYPE, MOMENT_UNIT, SERVER_URL, PinyinEnum } from '../app.constants';
import { GlobalDataService } from "./global.service";
import { map } from "rxjs/operators";
import { EncryptService } from "./encrypt.service";
import moment from "moment/moment";

@Injectable({
  providedIn: 'root'
})
export class GenericService {

  constructor(private http: HttpClient,
              protected gd: GlobalDataService,
              private encryptService: EncryptService
  ) { }

  getDropDownByNames(names: string[]): Observable<any> {
    return this.http.post<any>(SERVER_URL + 'publicApi/getDropDownList', names);
  }

  getDropDataByEoppurpose(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getDropDataByEoppurpose');
  }

  getUserPhotoSerial(uniqId:string) {
    return this.http.get<ResponseVO<number>>(SERVER_URL + 'publicApi/userPhoto/' + uniqId);
  }
  // 取得最近分行
  getNearestBranchBankInfo(latitude: string,longitude: string): Observable<any> {
    const formdata = new FormData();
    formdata.append('latitude', latitude);
    formdata.append('longitude', longitude);
    return this.http.post<any>(SERVER_URL + 'publicApi/getNearestBranchInfo', formdata);
  }

  // 取得分行別的縣市去掉重複
  getBranchBankInfo(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getBranchAddrCity');
  }

  // 取得分行別的縣市去掉重複
  getBranchBankInfoHasPosition(latitude: string,longitude: string): Observable<any> {
    const params = new URLSearchParams();
    params.append('latitude', latitude);
    params.append('longitude', longitude);
    return this.http.get<any>(SERVER_URL + 'publicApi/getBranchAddrCity?' + params.toString());
  }

  // publicApi/getBranchAddrCity/{台北市}
  getBranchAddrDist(city:string):Observable<any>{
    console.log("city:",city);
    return  this.http.get<any>(SERVER_URL + 'publicApi/getBranchAddrCity/'+city);
  }

  getBranchName(city:string,area:string):Observable<any>{
    console.log("city:",city);

     console.log("area:",area);
    return  this.http.get<Observable<any>>(SERVER_URL + 'publicApi/getBranchAddrCity/'+city+"/"+area)
  }

  // 新增分行預約
  addReservation(branchId: string, queryDate: string, queryTime: string){
    const formdata = new FormData();
    formdata.append('branchId', branchId);
    formdata.append('queryDate', queryDate);
    formdata.append('queryTime', queryTime);
    return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/branchAppointment', formdata);
  }

  // 取得當天可預約時段
  getAvailableTime(branchId: string,queryDate: string): Promise<any> {
    const formdata = new FormData();
    formdata.append('branchId', branchId);
    formdata.append('queryDate', queryDate);
    return this.http.post<any>(SERVER_URL + 'publicApi/getAvailableBookingDate', formdata).toPromise();
  }

  checkAdditionalDocument(): Observable<ResponseVO<any>> {
    return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/checkAdditionalDocument', {});
  }

  // for test
  sendEmailSupplementLater2(uniqId: any): Observable<any> {
    let params = new HttpParams();
    params = params.set('uniqId', uniqId);
    return this.http.post<any>(SERVER_URL + 'publicApi/sendEmailSupplementLater2', params);
  }

  // 薪轉判斷是否需要判斷 checkList
  getShortUrlCheck(shortUrl: string): Observable<boolean> {
    let params = new HttpParams();
    params = params.set('shortUrl', shortUrl);
    return this.http.get<boolean>(SERVER_URL + 'publicApi2/getShortUrlCheck', { params })
  }

  // 取得是否是預核名單&金額利率
  getPreAuditData(token: string): Observable<ResponseVO<any>>  {
    let params = new HttpParams();
    params = params.set('token', token);
    return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/getPreAuditData', params);
  }

  // 取得網路銀行畫面文字
  getModifyBank(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getModifyBank');
  }

  // 取得電話銀行畫面文字
  getModifyPhone(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getModifyPhone');
  }

  // 取得約定帳號銀行清單
  geteATMBank(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/geteATMBank');
  }

  // 取得是否已經經過確認頁起案 如果有則前端不該出現重新申請按鈕
  /** WT20240621001 Tim ******** 傳cardGroup，讓斷點可以判斷是dc還是非dc */
  getVerifyType(idno: string, pProductType: string, cardGroup: string): Observable<ResponseVO<boolean>> {
    const queryParams = new HttpParams();
    queryParams.set("idno",idno);
    queryParams.set("pProductType",pProductType);
    queryParams.set("cardGroup",cardGroup);

    return this.http.get<ResponseVO<boolean>>(SERVER_URL + 'publicApi/getVerifyType?'+'idno='+idno+'&pProductType='+pProductType);
  }


  // 房貸保證人身分證檢核
  checkAssurerIdnoAndUniqId(uniqId: string): Observable<ResponseVO<boolean>> {

    let params = new HttpParams();
    // params = params.set('idno', idno);
    params = params.set('uniqId', uniqId);
    return this.http.get<ResponseVO<boolean>>(SERVER_URL + 'publicApi2/checkAssurerIdnoAndUniqId', {params: params});
  }

  /**
   * 主案件驗身方式
   */
  getMainVerifyType():Observable<ResponseVO<string>>{
    return this.http.get<ResponseVO<string>>(SERVER_URL + 'publicApi/getMainVerifyType');
  }

  /**
   * 取得業務員代號
   */
  public getAgentNo(shortUrl: string) : Observable<ResponseVO<string>>{
    return this.http.post<ResponseVO<string>>(SERVER_URL +'publicApi/qrshorturl/'+ shortUrl, {});
  }

  public getTrustContactusUrl(): Observable<any> {

    return this.http.get<any>(SERVER_URL + 'publicApi2/getTrustContactusUrl');
  }

  public queryAccInfo(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/queryAccInfo')
  }

  public queryBindingAccInfo(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/queryBindingAccInfo')
  }

  /**
   * 確認客戶是否申辦無限卡
   */
  isInfinityCard(uniqId: string) : Observable<ResponseVO<boolean>> {
    const params = new FormData();
    params.set('uniqId', uniqId);
    return this.http.post<ResponseVO<boolean>>(SERVER_URL + 'publicApi/isInfinityCard', params);
  }

  /**
   * 幣勝卡是否顯示優惠訊息
   * @param isD3Qulification
   */
  getBeWinCardDisCount(isD3Qulification: boolean): Observable<any> {
    const params = new FormData();
    params.set('isD3Qulification', String(isD3Qulification));
    return this.http.post<any>(SERVER_URL + 'publicApi/getBeWinCardDisCount', params);
  }

  public getQrcodeShortUrl(uniqId:string) {
    return this.http.get<ResponseVO<any>>(SERVER_URL + `publicApi/getQrcodeShortUrl/${uniqId}`);
  }

  public getContractSP(uniqId:string, caseNoDgt:string) {
    return this.http.get<ResponseVO<any>>(SERVER_URL + `publicApi/getContractSP/${uniqId}/${caseNoDgt}`);
  }

  public getApplyLoanSP(uniqId:string, caseNoDgt:string) {
    return this.http.get<ResponseVO<any>>(SERVER_URL + `publicApi/getApplyLoanSP/${uniqId}`);
  }

    // public getMyDataSwitchContent() {
    //     return this.http.get<ResponseVO<any>>(SERVER_URL + `publicApi/getMyDataSwitchContent`);
    // }

    public checkVersion(userData: any) {
        return this.http.post<any>(SERVER_URL + 'publicApi/checkVersion', userData);
    }


    /**
     * 確認ShortUrl是否是TM的
     * @param shortUrl
     */
    public shortUrlCheckForTM(shortUrl: string): Observable<ResponseVO<any>> {
        console.log("--------------shortUrlCheck------------------");
        let params = new HttpParams();
        params = params.set('shortUrl', shortUrl);
        return this.http.post<any>(SERVER_URL + 'publicApi2/shortUrlCheckForTM', params);
    }

    /**
     * 確認是否是TM的案件
     * @constructor
     */
    public TMCaseCheck(): Observable<ResponseVO<any>> {
        console.log("--------------TMCaseCheck------------------");
        return this.http.get<any>(SERVER_URL + 'tmApi/TMCaseCheck');
    }

    public getToIDPortal() {
        return this.http.get<any>(SERVER_URL + 'publicApi/getToIDPortal')
    }

    public getToIDPortalForC3() {
        return this.http.get<any>(SERVER_URL + 'publicApi/getToIDPortalForC3')
    }


    public getQrPProductTypeList(pproductType: string) {
        return this.http.post<any>(SERVER_URL + `publicApi/getQrPProductTypeList`, pproductType);
    }

    public getHLShortUrl(uniqId: string) {
        let params = new HttpParams();
        params = params.set('uniqId', uniqId);
        console.log("params:", params);
        return this.http.post<ResponseVO<any>>(SERVER_URL + `publicApi/getHLShortUrl`, params);
    }

  getServiceBaseList():Observable<string[]>{
    return this.http.get<any>(SERVER_URL + 'publicApi/getServiceBaseList');
  }
  public getAddPhotoUrl() {
    return this.http.get<ResponseVO<any>>(SERVER_URL + `publicApi/getAddPhotoUrl`);
  }

  public additionCheckAge(age: string, type: string, unit: MOMENT_UNIT, start: number, end?: number) {

    const year = age.substr(0, 4);
    const month = age.substr(4, 2);
    const day = age.substr(6, 2);
    const date = moment(`${year}-${month}-${day}`);
    const startAge = start ? moment(date).add(start, unit) : null;
    const endAge = end ? moment(date).add(end, unit) : null;
    const now = moment(new Date());

    let result = true;
    switch (type) {
      case MOMENT_TYPE.BEFORE:
        result = now.isBefore(startAge);
        break;
      case MOMENT_TYPE.AFTER:
        result = now.isAfter(startAge);
        break;
      case MOMENT_TYPE.BETWEEN:
        result = now.isBetween(startAge, endAge);
        break;
    }

    return result;
  }

  /**
   * 20240102 Maggie
   * 單號： WT20231228001b 數存STP需求
   * 修改內容: 取得高風險 List
   */
  public getHighRiskOccupation() {
    return this.http.get<any>(SERVER_URL + 'publicApi/getHighRiskOccupation');
  }

  /**
   * 20240102 Maggie
   * 單號： WT20231228001b 數存STP需求
   * 修改內容: 員編比對 AOIM
   * */
  public checkAOIM(agentno: string) {
    const params = new HttpParams().set('member', agentno);
    console.log('params: '+params);
    return this.http.post<any>(SERVER_URL + 'publicApi/checkAOIM',params);
  }

  /**
   * 20240110 Maggie
   * 單號： WT20231228001b 數存STP需求
   * 修改內容: 若為高風險、至後端寫入一筆高風險註記
   */
  public highRiskMark(req) {
    console.log('genericService publicApi/highRiskMark');
    return this.http.post<any>(SERVER_URL + 'publicApi/highRiskMark',req);
  }

  /**
   * 20240110 Maggie
   * 單號： WT20231228001b 數存STP需求
   * 修改內容: 高風險後端檢核，檢核通過才 goNext()
   */
  public highRiskBackendValid(req) {
    return this.http.post<any>(SERVER_URL + 'publicApi/highRiskBackendValid',req);
  }

  /**
   * 20240111 Maggie
   * 單號： WT20231228001b 數存STP需求
   * 修改內容: CC 完成頁去後端確認此 UniqId 是否為高風險，如果是就註記並回前端
   */
  public finishPageCheckHighRisk(uniq) {
    console.log('genericService publicApi/finishPageCheckHighRisk');
    const params = new HttpParams().set('uniqId', uniq);
    console.log('params: ' + params);
    return this.http.post<any>(SERVER_URL + 'publicApi/finishPageCheckHighRisk', params);
  }

  /**
   * 20240306 Maggie WT20240109003b 證件上傳填完資料按下確認要去後端比對輸入的與 DB 是否相同
   * 不同 > 需落註記 | 檢核條件不同 (消金)
   */
  public afterConfirmDataCheckAndMark(data) {
    console.log('genericService afterConfirmDataCheckAndMark data: '+ JSON.stringify(data));
    return this.http.post<any>(SERVER_URL + 'publicApi/afterConfirmDataCheckAndMark',data);
  }

  /**
   * 20240306 Maggie WT20240109003b 證件上傳填完資料按下確認要去後端比對輸入的與 DB 是否相同
   * 不同 > 須註記後回前端跳視窗 | 檢核條件不同 (數存)
   */
  public afterConfirmDataCheck(data) {
    console.log('genericService afterConfirmDataCheck data: '+ JSON.stringify(data));
    return this.http.post<any>(SERVER_URL + 'publicApi/afterConfirmDataCheck',data);
  }

  /**
   * 20240429 Maggie WT20240422003
   * OCR辨識使用本圖專用註記
   */
  public markErrIdCard(data) {
    console.log('genericService markErrIdCard data: '+ JSON.stringify(data));
    return this.http.post<any>(SERVER_URL + 'publicApi/markErrIdCard',data);
  }

  /**
   * 20240517 WT20240503007 Maggie <br>
   * 從 DB 取得視訊服務網址
   */
  public getVideoServiceUrl() {
    console.log('genericService publicApi/getVideoServiceUrl');
    return this.http.get<any>(SERVER_URL + 'publicApi/getVideoServiceUrl');
  }

  // 預約開戶_取得分行搬遷資訊
  getBranchChange(): Observable<any> {
    return this.http.get<any>(SERVER_URL + 'publicApi/getBranchChange');
  }

    /**
   * 20240730 WT20240729001 Maggie
   * 取得電信業者下拉選單
   */
  getOperatorList(){
    return this.http.get<any>(SERVER_URL + 'publicApi/getOperatorList');
  }

  /**
   * 20240827 WT20240815002 Maggie <br>
   * 將 token 帶去後端傳給凱證並導頁至凱證
   */
  public securitiesRedirect(req) {
    console.log('genericService publicApi/securitiesRedirect ', req);
    return this.http.post<any>(SERVER_URL + 'publicApi/securitiesRedirect', req);
  }

  /**
   * 20240927 WT20240815002 Maggie <br>
   * 若是直接在完成頁點選證券按鈕 <br>
   * 不會帶 tk 在網址上，要另外抓
   */
  public getUrlKey(req) {
    console.log('genericService publicApi/getUrlKey ', req);
    return this.http.post<any>(SERVER_URL + 'publicApi/getUrlKey', req);
  }

  public checkTkValidityPeriod(tk){
    console.log('genericService publicApi/checkTkValidityPeriod ', tk);
    return this.http.post<any>(SERVER_URL + 'publicApi/checkTkValidityPeriod', tk);
  }

  /**
   * 20241128 WT20241028002 Maggie <br>
   * 從後端 aioCaseData 取得是否為異業 csCaseType
   */
  public getCsCaseType(uniq){
    console.log('genericService publicApi/getCsCaseType ', uniq);
    return this.http.post<any>(SERVER_URL + 'publicApi/getCsCaseType', uniq);
  }

  /**
   * 20241218 WT20241218001 Maggie <br>
   * 確認頁去後端取得條款同意狀態
   */
  public selectTermsFromKSCif(uniq) {
    return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/selectTermsFromKSCif', uniq);
  }

  /**
   * 202412210 WT20241125002 Maggie <br>
   * 取得信用卡調額申請原因下拉選單
   */
  getApplyReason(): Observable<any> {
    console.log("GenericService getApplyReason");
    return this.http.get<any>(SERVER_URL + 'publicApi2/getApplyReason');
  }

  /**
   * 202412210 WT20241125002<br>
   * 取得信用卡調額注意事項
   */
  getNotifyText(): Observable<any> {
    console.log("GenericService getNotifyText");
    return this.http.get<any>(SERVER_URL + "publicApi2/getNotifyText");
  }

  /**
   * WT20241125002 #6181 調額完成頁上傳照片至CS
   */
  getUploadPhotoToCS(uniqId): Observable<any> {
    console.log("GenericService uploadPhotoToCS");
    return this.http.post<any>(SERVER_URL + "publicApi2/uploadPhotoToCS", uniqId);
  }

  /**
   * WT20250331001 20250408 Beck
   * 打翻譯API
   *
   * @param input
   * @private
   */
  chineseToPinyin(text: string, type: PinyinEnum) {
    const params = new HttpParams()
        .set('text', text)
        .set('type', type);
    const headers = new HttpHeaders({ 'Content-Type': 'application/json;charset=UTF-8' });
    return this.http.get<ResponseVO<string[]>>(SERVER_URL + "publicApi/chineseToPinyin", { params, headers });
  }

}
