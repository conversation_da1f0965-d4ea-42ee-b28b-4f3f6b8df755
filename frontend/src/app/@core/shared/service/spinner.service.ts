import { Injectable } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';

export interface SpinnerRef {
  id?: string;
  show(): void;
  hide(): void;
}

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {

  constructor(private spinner: NgxSpinnerService) { }

  show(options?: any): SpinnerRef {
    if (typeof options === 'string') {
      this.spinner.show(options);
    } else if (options?.message) {
      this.spinner.show();
    } else {
      this.spinner.show();
    }
    return {} as SpinnerRef;
  }

  hide(name?: string) {
    this.spinner.hide(name);
  }

}