import {Observable} from 'rxjs';
import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {ResponseVO} from 'src/app/@core/shared/model/responseVO';
import {SERVER_URL} from 'src/app/@core/shared/app.constants';
import {RoutingModel} from '../model/routing.model';
import {BrowserLog} from '../model/browser-log.model';
import {GlobalDataService} from './global.service';
import {COMPONENT_AIO} from '../routing/aio.path';
import {COMPONENT_LOAN} from '../routing/loan.path';
import {map} from "rxjs/operators";
import {EncryptService} from "./encrypt.service";
import { COMPONENT_DC } from '../routing/dc.path';
import { COMPONENT_HL } from '../routing/hl.path';
import { COMPONENT_APPT } from '../routing/appt.path';

@Injectable({providedIn: 'root'})
export class RouteUiService {

    private time: Date = new Date();
    private resourceUrl = '/flow/routing';
    private browserLogUrl = 'publicApi/log/saveBrowseRecord';
    private browserLogForNoTokenUrl = 'publicApi/log/saveBrowseRecordForNoToken';

    constructor(private http: HttpClient,private gd:GlobalDataService,
                private encryptService: EncryptService) {
    }

    startRouting(dataModel: RoutingModel<any>, srcCompName?: string): Observable<ResponseVO<RoutingModel<any>>> {
        console.log(srcCompName + ' first page, startRouting: ', dataModel);
        console.log('啟動wf');
        let modulePath = '';
        // 因aio path都改為aioApi 避免有地方用module判斷 所以統一在這改
        switch (dataModel.module){
            case  'aio':
                modulePath = 'aioApi';
                break;
            case  'appt':
                modulePath = 'apptApi';
                break;
            case 'loan':
                modulePath = 'loanApi';
                break;
            case 'd3':
                modulePath = 'd3Api';
                break;
            case 'd2':
                modulePath = 'd2Api';
                break;
            case 'cc':
                modulePath = 'ccApi';
                break;
            case 'dc':
                modulePath = 'dcApi';
                break;
            case 'sal':
                modulePath ='salApi'
                break;
            case 'hl':
                modulePath ='hlApi'
                break;
            default:
                modulePath = dataModel.module;
        }
        console.log(SERVER_URL + modulePath + '/start');

        return this.http.post<ResponseVO<RoutingModel<any>>>(SERVER_URL + modulePath + '/start', dataModel, {responseType: 'json'})
            .pipe(map((resp: ResponseVO<RoutingModel<any>>) => {
                try {
                    if (resp && resp.rtnObj) {
                        // 加辦Start 回傳資料轉換
                        const caseData :RoutingModel<any> = this.encryptService.decodeBase64ToJson(resp.rtnObj);
                        resp.rtnObj = caseData;
                    }
                }catch(e){
                    console.error("startRouting 異常!!!!!!!!!");
                }

                return resp;
            }));
    }

    getNextRouting(dataModel: RoutingModel<any>, srcCompName?: string, isFirstPage?: boolean, isAddition?: boolean): Observable<ResponseVO<RoutingModel<any>>> {
        console.log(srcCompName + ' getNextRouting: ', dataModel);
        console.log('isFirstPage:' , isFirstPage);
        console.log('isAddition:' , isAddition);
        let modulePath = '';
        switch (dataModel.module){
            case  'aio':
                modulePath = 'aioApi';
                break;
            case  'appt':
                modulePath = 'apptApi';
                break;
            case 'loan':
                modulePath = 'loanApi';
                break;
            case 'd3':
                modulePath = 'd3Api';
                break;
            case 'd2':
                modulePath = 'd2Api';
                break;
            case 'cc':
                modulePath = 'ccApi';
                break;
            case 'dc':
                modulePath = 'dcApi';
                break;
            case 'sal':
                modulePath = 'salApi';
                break;
            case 'hl':
                modulePath ='hlApi'
                break;
            default:
                modulePath = dataModel.module;
        }

        // 判斷是否為第一步
        if (isFirstPage && !isAddition) {
            return this.startRouting(dataModel, srcCompName);
        }
        if (isAddition){
            // 加辦流程
            return this.startAdditionRouting(dataModel, srcCompName);
        }

        return this.http.post<ResponseVO<RoutingModel<any>>>(SERVER_URL + modulePath + this.resourceUrl, dataModel, {responseType: 'json'})
            .pipe(map((resp: ResponseVO<RoutingModel<any>>) => {
                try {
                    if (resp && resp.rtnObj) {
                        // 加辦Start 回傳資料轉換
                        resp.rtnObj = this.encryptService.decodeBase64ToJson(resp.rtnObj);
                        console.log(resp);
                    }
                } catch (e) {
                    console.error("getNextRouting 異常!!!!!!!!!");
                }


                return resp;
            }));
    }

    startAdditionRouting(dataModel: RoutingModel<any>, srcCompName?: string): Observable<ResponseVO<RoutingModel<any>>> {
        console.log(srcCompName + ' first page, startRouting: ', dataModel);
        let modulePath = '';
        switch (dataModel.module){
            case  'aio':
                modulePath = 'aioApi';
                break;
            case  'appt':
                modulePath = 'apptApi';
                break;
            case 'loan':
                modulePath = 'loanApi';
                break;
            case 'd3':
                modulePath = 'd3Api';
                break;
            case 'd2':
                modulePath = 'd2Api';
                break;
            case 'cc':
                modulePath = 'ccApi';
                break;
            case 'dc':
                modulePath = 'dcApi';
                break;
            case 'sal':
                modulePath = 'salApi';
                break;
            default:
                modulePath = dataModel.module;
        }
        return this.http.post<ResponseVO<RoutingModel<any>>>(SERVER_URL + modulePath + '/startAddition', dataModel, {responseType: 'json'})
            .pipe(map((resp: ResponseVO<RoutingModel<any>>) => {
                try {
                    if (resp && resp.rtnObj) {
                        // 加辦Start 回傳資料轉換
                        resp.rtnObj = this.encryptService.decodeBase64ToJson(resp.rtnObj);
                    }
                } catch (e) {
                    console.error("startAdditionRouting 異常!!!!!!!!!");
                }

                return resp;
            }));
    }

    // 2022-01-11 browser log  進入時要寫一次
    saveBrowserLogGetType(browserLog: BrowserLog): Observable<any> {
        return this.http.post<any>(SERVER_URL + this.browserLogUrl, browserLog);
    }

    // 2022-01-11 browser log  進入時要寫一次
    saveBrowseRecordForNoToken(browserLog: BrowserLog): Observable<any> {
        return this.http.post<any>(SERVER_URL + this.browserLogForNoTokenUrl, browserLog);
    }


    // 進入頁面時 需要將申辦的入口 記錄起來
    saveRedirectUrl(url: string): void{
        console.log('來存url : ' , url );
        // 有值 且與當前url 相同 不取代
        if (this.getRedirectUrl() && this.gd.timeoutRedirectUrl === url){
            // 已經有值不取代
        }else{
            this.gd.timeoutRedirectUrl = url;
        }
    }

    getRedirectUrl(): string{
        return this.gd.timeoutRedirectUrl;
    }

    // 線上驗身錯誤次數歸零
    setValidateErrorZero(uniqId: string): Observable<any> {
        console.log('setValidateErrorZero uniqId : ', uniqId);
        let params = new HttpParams();
        params = params.set('uniqId', uniqId);
        return this.http.get<any>(SERVER_URL + 'publicApi/setValidateErrorZero', {params});
    }

    // 寫 aio api log 到後端
    saveBackendProblemLog(data): Observable<any> {
        console.log('saveBackendProblemLog uniqId : ', data);

        return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/saveBackendProblemLog' , data, {responseType: 'json'});
    }


    applyIdentifiedCheck(uniqId: string, idno: string): Observable<ResponseVO<any>> {
        let params = new HttpParams();
        params = params.set('uniqId', uniqId);
        params = params.set('idno', idno);
        return this.http.get<ResponseVO<any>>(SERVER_URL + 'publicApi/applyIdentifiedCheck', {headers: {'Cache-Control': 'no-cache, no-store, must-revalidate, post-check=0, pre-check=0'}, params});
    }

    isCaseProperties(): Observable<ResponseVO<any>> {
        return this.http.post<ResponseVO<any>>(SERVER_URL + 'publicApi/isCaseProperties', {}, {headers: {'Cache-Control': 'no-cache, no-store, must-revalidate, post-check=0, pre-check=0'}});
    }

    doRedirectUrl(path: string) {
        window.open(this.getCurrentUrl() + path);
    }

    changeRef(path: string) {
        if (!path) {
            console.error('changeRef: path is null or undefined');
            return;
        }
        window.location.href = this.getCurrentUrl() + path;
    }

    getCurrentUrl(path?: string): string{
        console.log('window.href: ' , window.location.href);

        const url = window.location.href;
        let host = '';
        if (url.indexOf('obd') > -1){
            // production
            host = `${window.location.protocol}//${window.location.host}/obd`;
        }else{
            // develop
            host = `${window.location.protocol}//${window.location.host}`;
        }
        return path ? host + path : host;
    }

    updateAppSession(idno: string, token: string) {
        return this.http.post<ResponseVO<string>>('publicApi/updateAppSession', {
            idno, token
        });
    }
}
