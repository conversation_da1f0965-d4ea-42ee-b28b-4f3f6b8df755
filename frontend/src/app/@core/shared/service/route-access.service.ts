import {Injectable} from '@angular/core';
import {ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree} from '@angular/router';
import {Observable} from 'rxjs';
import {AccountService} from './account.service';
import {environment} from '../../../../environments/environment';
import {RouteHelperService} from './route-helper.service';
import {GlobalDataService} from './global.service';


@Injectable({
    providedIn: 'root'
})
export class RouteAccessService implements CanActivate {
    constructor(
        private router: Router,
        private accountService: AccountService,
        private gd: GlobalDataService,

        private routerHelperService: RouteHelperService
    ) {
    }

    /**
     * 2024/03/21 xavier
     * WT20240311004
     * environment.production 改用網址判斷 如果錯誤就預設為正式區參數
     *
     * 這支不知道有沒有用到，但還是先改上去避免後續有問題
     *
     **/
    canActivate(
        next: ActivatedRouteSnapshot,
        state: RouterStateSnapshot
    ):
        | Observable<boolean | UrlTree>
        | Promise<boolean | UrlTree>
        | boolean
        | UrlTree {

        let production = true;

        try {
            production =  !!window.location.href.match(/kgibank\.com/)
        } catch (ex) {
            console.warn('網址錯誤 改為預設正式區', ex);
        }
        if (!production) {
            console.log('RouteAccessService !production');
            return true;
        } else if (this.gd.token) {
            console.log('RouteAccessService this.gd.token');
            return true;
        } else {
            console.log('RouteAccessService else');
            this.routerHelperService.navigate(['error']);
            return false;
        }
    }

}
