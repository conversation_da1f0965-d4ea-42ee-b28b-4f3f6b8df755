import {Directive, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from '@angular/forms';

//TODO: 尚未完成
@Directive({
    selector: '[formControlName][mobileValidator]',
})
export class MobileMaskDirective {

    constructor(public ngControl: NgControl) {
    }

    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //
    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //   this.onInputChange(event, false);
    // }

    // @HostListener('keydown.backspace', ['$event'])
    // keydownBackspace(event) {
    //   this.onInputChange(event.target.value, true);
    // }

    @HostListener('keyup', ['$event'])
    keyup(event) {
        this.onInputChange(event.target.value, true);
    }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
    }


    // export const MASK_MOBILE = [/[0]/, /[9]/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/, /\d/, /\d/]; // 行動電話
    onInputChange(event, backspace) {
        if(!event) return;
        let rawValue = event.replace(/[\D\s-]/g, ''); //移除 非數字/空白
        rawValue = rawValue.replace(/^([^0])/g, ''); //移除非 0 開頭的
        rawValue = rawValue.replace(/^([0][^9])/g, '0'); //移除非 09 開頭的

        // console.log('rawValue (start):', rawValue);
        // console.log('rawValue (length):', rawValue.length);
        // console.log('backspace: ' + backspace + ' / rawValue: ' + rawValue)

        if (rawValue.length === 0) {
            rawValue = '';
        } else if (rawValue.length < 10) {
            // console.log('1');
            rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1$2');
        } else {
            // console.log('2');
            rawValue = rawValue.substring(0, 10);
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1$2');
        }

        this.ngControl.valueAccessor.writeValue(rawValue);
        const control: AbstractControl = this.ngControl.control;
        control.setValue(rawValue);
    }
}
