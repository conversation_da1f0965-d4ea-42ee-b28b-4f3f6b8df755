import {Directive, ElementRef, HostListener} from '@angular/core';
import {AbstractControl, NgControl} from "@angular/forms";

@Directive({
    selector: '[formControlName][englishOnly]'
})
export class EnglishOnlyDirective {



    constructor(public ngControl: NgControl) {
    }

    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //     this.onInputChange(event, false);
    // }

    // @HostListener('keydown.backspace', ['$event'])
    // keydownBackspace(event) {
    //     this.onInputChange(event.target.value, true);
    // }
    //
    @HostListener('input', ['$event'])
    keyup(event) {
        this.onInputChange(event.target.value, true);
    }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
    }

    onInputChange(event, backspace) {
        const control: AbstractControl = this.ngControl.control;
        if(!event) return;
        const rawValue = event.replace(/[^a-zA-Z\s]/g,''); // 限定英文字
        control.setValue(rawValue);
        // this.ngControl.valueAccessor.writeValue(rawValue);
    }

}
