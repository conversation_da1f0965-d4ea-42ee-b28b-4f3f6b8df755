import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';
// TODO: AccountService暫不存在，IBR模組中暫不使用權限控制
// import { AccountService } from '../service/account.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

/**
 * @whatItDoes Conditionally includes an HTML element if current user has any
 * of the authorities passed as the `expression`.
 *
 * @howToUse
 * ```
 *     <some-element *wtHasAnyAuthority="'ROLE_ADMIN'">...</some-element>
 *
 *     <some-element *wtHasAnyAuthority="['ROLE_ADMIN', 'ROLE_USER']">...</some-element>
 * ```
 */
@Directive({
    selector: '[wtHasAnyAuthority]'
})
export class HasAnyAuthorityDirective {

    private authorities!: string | string[];

    private readonly destroy$ = new Subject<void>();

    constructor(private templateRef: TemplateRef<any>, private viewContainerRef: ViewContainerRef) {
        // TODO: AccountService暫時移除，IBR模組中預設顯示所有內容
    }

    @Input()
    set wtHasAnyAuthority(value: string|string[]) {
        this.authorities = value;
        this.updateView();
        // TODO: 暫時移除AccountService依賴，IBR模組中所有用戶都有權限
    }

    private updateView(): void {
        // TODO: 暫時移除AccountService依賴，IBR模組中所有用戶都有權限
        const hasAnyAuthority = true; // 暫時預設所有用戶都有權限
        this.viewContainerRef.clear();
        if (hasAnyAuthority) {
            this.viewContainerRef.createEmbeddedView(this.templateRef);
        }
    }
}
