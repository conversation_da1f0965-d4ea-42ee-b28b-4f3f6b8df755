import { Directive, HostListener, Input } from '@angular/core';
import {AbstractControl, NgControl} from "@angular/forms";
import { lengthLimit } from '../utils/common-utils';

@Directive({
  selector: '[formControlName][addressValidation]'
})
export class AddressValidationDirective {
  @Input() bitLimit: number;

  constructor(public ngControl: NgControl) {
  }

  @HostListener('focusout', ['$event'])
  focusout(event) {
    this.onInputChange(event.target.value, true);
    if(this.bitLimit)
      lengthLimit(event.target.value, this.bitLimit, this.ngControl.control);
  }

  onInputChange(event, backspace) {
    const control: AbstractControl = this.ngControl.control;
    if(!event) return;
    const rawValue = event.replace(/[^\-\\a-zA-Z0-9\u4E00-\u9FA5]/g,''); // 限定中英數字
    control.setValue(rawValue);
  }

}
