import {Directive, ElementRef, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from "@angular/forms";

@Directive({
  selector: '[numbersOnly_v3]'
})
export class NumberOnlyV3Directive {

  constructor(public ngControl: NgControl) {
  }

  /**
   * 為了顯示千分位而新增的版本
   * 為了顯示千分位而新增的版本
   * 為了顯示千分位而新增的版本
   */


  // 需確認是否與 呼叫 function 為一致 ，否則 會導致 重複setValue
  @HostListener('input', ['$event'])
  ngModelChange(event: Event): void {
    const inputValue = (event.target as any).value;
    this.onInputChange(inputValue);
  }

  onInputChange(value: string): void {
    if (!value) {
      const control: AbstractControl = this.ngControl.control;
      control.setValue('');
      return;
    }

    try {
      // 允許千分位逗號，但移除其他非數字字符
      const rawValue = value.replace(/[^0-9,]/g, '');

      // 移除所有逗號以獲取純數字值
      let pureNumber = rawValue.replace(/,/g, '');

      // 如果以 0 開頭，限制只能輸入單個 0
      if (pureNumber.startsWith('0') && pureNumber.length > 1) {
        pureNumber = '0';
      }

      const control: AbstractControl = this.ngControl.control;
      control.setValue(pureNumber, { emitEvent: false }); // 避免觸發無限循環
    } catch (exception) {
      console.warn('onInputChange fail!!!!!', exception);
    }
  }

}
