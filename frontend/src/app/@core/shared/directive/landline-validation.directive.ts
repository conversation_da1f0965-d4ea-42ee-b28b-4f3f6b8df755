import {Directive, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from '@angular/forms';

/**
 * 市內電話 Validator
 */
@Directive({
    selector: '[formControlName][landlineValidator]',
})
export class LandlineMaskDirective {

    constructor(public ngControl: NgControl) {
    }
    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //
    @HostListener('keyup', ['$event'])
    keyup(event) {
        this.onInputChange(event.target.value, true);
    }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
    }
    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //     this.onInputChange(event, false);
    // }

    /**
     * 20240102 Maggie
     * 單號： WT20231228001b 數存STP需求
     * 修改內容: 電話號碼長度判別，區碼新增
     * @param event
     * @param backspace
     */
    //02, 04, 049 十碼, 其他九碼
    onInputChange(event, backspace) {
        if(!event) return;
        let rawValue = event.replace(/[\D\s-]/g, ''); //移除 非數字/空白
        rawValue = rawValue.replace(/^([^0])/g, ''); //移除非 0 開頭的
        if (rawValue.length <= 2) {
            rawValue = rawValue.replace(/[0][(0|1|9)]/g, '0'); //移除 00,01 開頭
        }
        // console.log('rawValue (start):', rawValue);
        // console.log('rawValue (length):', rawValue.length);
        // console.log('backspace: ' + backspace + ' / rawValue: ' + rawValue)

        if (backspace && rawValue.length === 2) {
            rawValue = rawValue.substring(0, rawValue.length);
        }

        if (rawValue.length === 0) {
            rawValue = '';
        } else if (rawValue.length <= 1) {
            console.log('1');
            rawValue = rawValue.replace(/^(\d{0,2})/, '$1');
        } else if (rawValue.length === 2) {
            rawValue = rawValue.replace(/^(\d{0,2})(\d{0,2})/, '$1$2');


        } else if (rawValue.length === 3) {
            console.log('2');

            if((rawValue.startsWith('037') || rawValue.startsWith('049')  || rawValue.startsWith('089')|| rawValue.startsWith('082'))) {
                rawValue = rawValue.replace(/^(\d{0,3})(\d{0,6})/, '$1$2');
            }else {
                rawValue = rawValue.replace(/^(\d{0,2})(\d{0,8})/, '$1-$2');
            }

            rawValue = this.nonStart(rawValue)

        } else if (rawValue.length < 10) {
            console.log('3');
            if((rawValue.startsWith('037') || rawValue.startsWith('049')  || rawValue.startsWith('089')|| (rawValue.startsWith('082')&& !rawValue.startsWith('0826')))) {
                rawValue = rawValue.replace(/^(\d{0,3})(\d{0,6})/, '$1-$2');
            } else if (rawValue.startsWith('0836') || rawValue.startsWith('0826')) {
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1-$2');
            }else{
                rawValue = rawValue.replace(/^(\d{0,2})(\d{0,8})/, '$1-$2');
            }

            rawValue = this.nonStart(rawValue)

        } else {
            console.log('4');
            //rawValue = rawValue.substring(0, 10);
            //02, 04, 049 十碼, 其他九碼

            if((!rawValue.startsWith('02') && !rawValue.startsWith('04') && !rawValue.startsWith('049'))) {
                rawValue = rawValue.substring(0, 9);
            }

            if((rawValue.startsWith('037') || rawValue.startsWith('049')  || rawValue.startsWith('089')|| (rawValue.startsWith('082')&& !rawValue.startsWith('0826')))) {
                rawValue = rawValue.replace(/^(\d{0,3})(\d{0,3})/, '$1-$2');
            } else if (rawValue.startsWith('0836') || rawValue.startsWith('0826')) {
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1-$2');
            }else{
                rawValue = rawValue.replace(/^(\d{0,2})(\d{0,4})/, '$1-$2');
            }


            // r2-3973  市話不會有 09、00
            if(rawValue.startsWith('09') || rawValue.startsWith('00')) { //截掉字元
                rawValue = "";
            }
        }
        
        this.ngControl.valueAccessor.writeValue(rawValue);
        const control: AbstractControl = this.ngControl.control;
        control.setValue(rawValue);
    }

    // r2-3973  市話不會有 09、00
    nonStart(rawValue :string): string {
        if(rawValue.startsWith('09') || rawValue.startsWith('00') || rawValue.startsWith('01')) { //截掉字元
            rawValue = rawValue.substring(0, 1);
        }
        return rawValue;
    }

}
