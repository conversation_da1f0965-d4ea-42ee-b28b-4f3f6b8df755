import {Directive, ElementRef, HostBinding, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from "@angular/forms";

@Directive({
    selector: '[formControlName][emailValidation]'
})
export class EmailValidationDirective {

    constructor(public ngControl: NgControl) {
    }

    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //

    // @HostBinding('value') emailValue: string;

    @HostListener('window:load', ['$event'])
    load() {
        let result = '';
        const control: AbstractControl = this.ngControl.control;
        const value = control.value ? control.value : "";

        result = this.transform(value)
        control.setValue(result);
    }

    /**
     * 有張單需求全形轉半形 因會衝突 導致打一個字重複很多個字 所以先關閉
     * @param event
     */
    // @HostListener('input', ['$event'])
    // focusout(event) {
    //     let result = '';
    //     let eventValue = event.target.value ? event.target.value : "";
    //
    //     this.onInputChange(result, true);
    // }

    @HostListener('change', ['$event'])
    change(event) {
        let result = '';
        const control: AbstractControl = this.ngControl.control;
        const eventValue = control.value ? control.value : "";
        console.warn("!!!!!!!change!!!!!!");
        result = this.transform(eventValue)
        control.setValue(result);
        this.onInputChange(result, true);
    }

    onInputChange(event, backspace) {

        // 目前會如果輸入完電子信箱 再切換中文輸入發輸入注音時會刪除原有電子信箱的問題
        // if(!event) return;
        // let rawValue = event.replace(/[^a-zA-Z\u4E00-\u9FA5]/g,''); // 限定中英字
        // this.ngControl.valueAccessor.writeValue(rawValue);

        const control: AbstractControl = this.ngControl.control;
        if(!event) {
            control.setValue('');
            return;
        }

        console.warn("event ", event.charCodeAt(0));
        const rawValue = event.replace(/[^A-Za-z0-9@._-]/g,''); // 信箱
        console.warn("replace ", rawValue);

        control.setValue(rawValue);
    }

    transform(value: string): string {
        let result = '';
        let code = 0;

        for (let i = 0; i < value.length; i++) {
            code = value.charCodeAt(i);//獲取當前字元的unicode編碼
            if (value.charCodeAt(i) > 65280 && value.charCodeAt(i) < 65375)//在這個unicode編碼範圍中的是所有的英文字母已經各種字元
            {
                result += String.fromCharCode(value.charCodeAt(i) - 65248);//把全形字元的unicode編碼轉換為對應半形字元的unicode碼
            } else if (value.charCodeAt(i) == 12288)//空格
            {
                result += String.fromCharCode(value.charCodeAt(i)-12256);
                continue;
            } else if (value.charCodeAt(i) == 12290)//.
            {
                result += String.fromCharCode(value.charCodeAt(i)-12244);
                continue;
            } else {
                console.warn("!!!---", value.charCodeAt(1));
                result += String.fromCharCode(value.charCodeAt(i));
            }
        }

        return result !== "undefined" ? result : "";
    }
}
