import { Directive, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { AbstractControl, NgControl } from "@angular/forms";
import { lengthLimit } from '../utils/common-utils';

@Directive({
    selector: '[formControlName][chtAndEngValidation]'
})
export class ChtAndEngValidationDirective {
    @Input() bitLimit: number;
    @Output() errorMessage: EventEmitter<string> = new EventEmitter<string>();

    constructor(public ngControl: NgControl) {
    }

    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //
    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //     this.onInputChange(event, false);
    // }

    // @HostListener('keydown.backspace', ['$event'])
    // keydownBackspace(event) {
    //     this.onInputChange(event.target.value, true);
    // }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
        if (this.bitLimit) {
            const errorMessage = lengthLimit(event.target.value, this.bitLimit, this.ngControl.control);
            if (errorMessage !== '' && errorMessage !== undefined) {
                this.errorMessage.emit(errorMessage);
            }
        }
    }

    // @HostListener('input', ['$event'])
    // keyup(event) {
    //     this.onInputChange(event.target.value, true);
    // }

    onInputChange(event, backspace) {
        // if(!event) return;
        // let rawValue = event.replace(/[^a-zA-Z\u4E00-\u9FA5]/g,''); // 限定中英字
        // this.ngControl.valueAccessor.writeValue(rawValue);

        const control: AbstractControl = this.ngControl.control;
        if(!event) return;
        const rawValue = event.replace(/[^a-zA-Z\u4E00-\u9FA5]/g,''); // 限定中英字
        control.setValue(rawValue);
    }



}
