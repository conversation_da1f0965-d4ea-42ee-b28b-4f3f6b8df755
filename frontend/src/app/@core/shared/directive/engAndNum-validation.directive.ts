import {Directive, ElementRef, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from "@angular/forms";

@Directive({
    selector: '[formControlName][engAndNumValidation]'
})
export class engAndNumValidationDirective {

    constructor(public ngControl: NgControl) {
    }

    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //
    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //     this.onInputChange(event, false);
    // }

    // @HostListener('keydown.backspace', ['$event'])
    // keydownBackspace(event) {
    //     this.onInputChange(event.target.value, true);
    // }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
    }

    // @HostListener('keyup', ['$event'])
    // keyup(event) {
    //     this.onInputChange(event.target.value, true);
    // }

    onInputChange(event, backspace) {
        const control: AbstractControl = this.ngControl.control;
        if (!event) return;
        const rawValue = event.replace(/[^a-zA-Z0-9]/g, ''); // 限定英數字
        control.setValue(rawValue);
    }

}