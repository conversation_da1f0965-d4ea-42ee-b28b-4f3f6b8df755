import {Directive, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from '@angular/forms';

/**
 * 市內電話 + 手機號碼 Validator
 */
@Directive({
    selector: '[formControlName][loginPhoneValidator]',
})
export class LoginPhoneMaskDirective {

    constructor(public ngControl: NgControl) {
    }

    // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
    //
    // @HostListener('ngModelChange', ['$event'])
    // onModelChange(event) {
    //   this.onInputChange(event, false);
    // }

    // @HostListener('keydown.backspace', ['$event'])
    // keydownBackspace(event) {
    //   this.onInputChange(event.target.value, true);
    // }

    // @HostListener('keyup', ['$event'])
    // keyup(event) {
    //     this.onInputChange(event.target.value, true);
    // }

    @HostListener('focusout', ['$event'])
    focusout(event) {
        this.onInputChange(event.target.value, true);
    }


    // 登入的手機專用
    onInputChange(event, backspace) {
        if(!event) return;
        let rawValue = event.replace(/[\D\s-]/g, ''); //移除 非數字/空白
        rawValue = rawValue.replace(/^([^0])/g, ''); //移除非 0 開頭的
        if (rawValue.length <= 2) {
            rawValue = rawValue.replace(/[0][(0|1)]/g, '0'); //移除 00,01 開頭
        }

        // console.log('rawValue (start):', rawValue);
        // console.log('rawValue (length):', rawValue.length);
        // console.log('backspace: ' + backspace + ' / rawValue: ' + rawValue)

        if (backspace && rawValue.length === 2) {
            rawValue = rawValue.substring(0, rawValue.length);
        }

        if (rawValue.length === 0) {
            rawValue = '';
        } else if (rawValue.length <= 1) {
            // console.log('1');
            rawValue = rawValue.replace(/^(\d{0,2})/, '$1');
        } else if (rawValue.length === 2) {
            rawValue = rawValue.replace(/^(\d{0,2})(\d{0,2})/, '$1$2');
        } else if (rawValue.length === 3) {
            // console.log('2');

            if(rawValue.startsWith('09')) {
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1$2');
            }
        } else if (rawValue.length < 10) {
            // console.log('3');
            if(rawValue.startsWith('09')) {
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1$2');
            }
        } else {
            // console.log('4');
            //rawValue = rawValue.substring(0, 10);
            //02, 04, 049 十碼, 其他九碼
            // console.log(rawValue.startsWith('02'))

            //截掉超過字元
            if(rawValue.startsWith('09')) { //截掉超過字元
                rawValue = rawValue.substring(0, 10);
            }

            if(rawValue.startsWith('09')) {
                rawValue = rawValue.replace(/^(\d{0,4})(\d{0,6})/, '$1$2');
            }
        }

        this.ngControl.valueAccessor.writeValue(rawValue);
        const control: AbstractControl = this.ngControl.control;
        control.setValue(rawValue);
    }

}
