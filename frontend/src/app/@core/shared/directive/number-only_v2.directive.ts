import {Directive, ElementRef, HostListener} from '@angular/core';
import { AbstractControl, NgControl } from "@angular/forms";

@Directive({
  selector: '[numbersOnly_v2]'
})
export class NumberOnlyV2Directive {
  private isComposing = false;
  constructor(public ngControl: NgControl) {
  }
  // 寫太多 hostListener  會造成 瀏覽器 ERROR RangeError: Maximum call stack size exceeded  所以將兩個註解起來
  //
  // @HostListener('ngModelChange', ['$event'])
  // onModelChange(event) {
  //   this.onInputChange(event, false);
  // }

  // @HostListener('keydown.backspace', ['$event'])
  // keydownBackspace(event) {
  //   this.onInputChange(event.target.value, true);
  // }

  // @HostListener('keyup', ['$event'])
  // keyup(event) {
  //   this.onInputChange(event.target.value, true);
  // }
  //
  // @HostListener('focusout', ['$event'])
  // focusout(event) {
  //   this.onInputChange(event.target.value, true);
  // }
  // 中文輸入開始時標記 isComposing
  @HostListener('compositionstart')
  onCompositionStart() {
    this.isComposing = true;
  }

  // 中文輸入結束時，執行輸入驗證
  @HostListener('compositionend', ['$event'])
  onCompositionEnd(event: any) {
    this.isComposing = false;
    this.onInputChange(event.target.value,true);
  }

  // 需確認是否與 呼叫 function 為一致 ，否則 會導致 重複setValue
  @HostListener('input', ['$event'])
  ngModelChange(event) {
    if (!this.isComposing) {
      this.onInputChange(event.target.value, true);
    }
  }

  onInputChange(event,backspace) {
    if(!event) return;
    try {
      let rawValue = event.replace(/[^0-9]*/g,''); // 限定數字
      if(rawValue.startsWith('0')){
        rawValue = rawValue.substring(0, 1); // 0 開頭則後面不能補其他數字
      }
      const control: AbstractControl = this.ngControl.control;
      // control.setValue(rawValue);

      // 也可以使用以下方法
      control.patchValue(rawValue, { emitEvent: false });

    }catch (Exception){
      console.warn("onInputChange fail!!!!!");
    }
  }

}
