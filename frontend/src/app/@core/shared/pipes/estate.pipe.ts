import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'estate'
})
export class EstatePipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 不動產狀態
    if (value === '1') {
      return '本人所有'
    }
    if (value === '2') {
      return '配偶所有'
    }
    if (value === '3') {
      return '家族所有'
    }
    if (value === '4') {
      return '無'
    }
  }

}
