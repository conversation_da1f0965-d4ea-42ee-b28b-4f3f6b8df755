import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'relationshipWithApplicant'
})
export class RelationshipWithApplicantPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 婚姻
    if (value === '0') {
      return '配偶';
    }
    if (value === '1') {
      return '父母';
    }
    if (value === '2') {
      return '子女';
    }
    if (value === '3') {
      return '朋友';
    }
    if (value === '4') {
      return '其他';
    }

    return '請輸入';
  }

}
