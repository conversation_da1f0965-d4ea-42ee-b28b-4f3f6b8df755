import { Pipe, PipeTransform } from '@angular/core';
import { SEND_TYPE_D3 } from "../app.constants";

@Pipe({
  name: 'sendTypeD3'
})
export class SendTypeD3Pipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 繳款方式
    if (value === SEND_TYPE_D3.PAPER) {
      return '紙本帳單';
    }
    if (value === SEND_TYPE_D3.EBILLING) {
      return '電子帳單';
    }
  }

}
