import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'incomeSource'
})
export class SourceOfAccountFundsPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 帳戶資金來源
    if (value === '1') {
      return '薪資'
    }
    if (value === '2') {
      return '執業所得'
    }
    if (value === '3') {
      return '投資'
    }
    if (value === '4') {
      return '退休/保險年金'
    }
    if (value === '5') {
      return '租賃所得'
    }
    if (value === '6') {
      return '其他'
    }
  }

}
