import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'gApplyConfirmation'
})
export class GApplyConfirmationPipe implements PipeTransform {

    transform(value: unknown, ...args: unknown[]): unknown {
        // 行業類別
        if (value === "01") {
            return "政府機關（含軍、公、警、消）、公營機構";
        }
        if (value === "013399") {
            return "製造業";
        }
        if (value === "014290") {
            return "營造/土木工程";
        }
        if (value === "014832") {
            return "通訊/科技業";
        }
        if (value === "014990") {
            return "運輸業";
        }
        if (value === "016419") {
            return "虛擬貨幣";
        }
        if (value === "016496") {
            return "典當業";
        }
        if (value === "016499") {
            return "金融保險業";
        }
        if (value === "016812") {
            return "不動產經紀業";
        }
        if (value === "016911") {
            return "法律服務業";
        }
        if (value === "016920") {
            return "會計服務業";
        }
        if (value === "018610") {
            return "醫療院所";
        }
        if (value === "019200") {
            return "博弈業";
        }
        if (value === "019323") {
            return "特殊行業(視廳歌唱業、理髮業、三溫暖業、舞廳業、酒吧業、特種咖啡茶室及電子遊藝業)";
        }
        if (value === "02") {
            return "國際組織及外國機構";
        }
        if (value === "03") {
            return "非營利組織團體(如人民、宗教、社福慈善團體)";
        }
        if (value === "030100") {
            return "軍公教";
        }
        if (value === "04") {
            return "醫藥業（含醫院、獸醫、診所、藥局）";
        }
        if (value === "05") {
            return "貿易業";
        }
        if (value === "06") {
            return "金融服務業";
        }
        if (value === "07") {
            return "虛擬貨幣業";
        }
        if (value === "08") {
            return "法律及會計服務業";
        }
        if (value === "09") {
            return "地政士（含事務所）";
        }
        if (value === "1_014745") {
            return "藝術品或骨董等專拍賣";
        }
        if (value === "1_019690") {
            return "服務業";
        }
        if (value === "10") {
            return "公證人（含事務所）";
        }
        if (value === "11") {
            return "記帳士暨記帳及報稅代理人（含事務所）";
        }
        if (value === "12") {
            return "不動產經紀業";
        }
        if (value === "13") {
            return "住宿餐飲業";
        }
        if (value === "14") {
            return "文教暨大眾傳播出版業";
        }
        if (value === "15") {
            return "珠寶銀樓業";
        }
        if (value === "16") {
            return "典當拍賣業（排除公營當鋪）";
        }
        if (value === "17") {
            return "博弈業/彩券行";
        }
        if (value === "18") {
            return "娛樂休閒業";
        }
        if (value === "19") {
            return "特殊娛樂事業（酒吧、舞廳、電子遊藝等）";
        }
        if (value === "2_014745") {
            return "珠寶、銀樓業";
        }
        if (value === "2_019690") {
            return "家管";
        }
        if (value === "20") {
            return "軍火業";
        }
        if (value === "21") {
            return "農林漁牧";
        }
        if (value === "22") {
            return "製造業";
        }
        if (value === "23") {
            return "能源及污染整治業";
        }
        if (value === "24") {
            return "營建不動產業";
        }
        if (value === "25") {
            return "批發零售業";
        }
        if (value === "26") {
            return "運輸倉儲業";
        }
        if (value === "27") {
            return "電信業";
        }
        if (value === "28") {
            return "電子資訊業";
        }
        if (value === "29") {
            return "自由業/家管/學生";
        }
        if (value === "30") {
            return "無業/退休人士";
        }
        if (value === "31") {
            return "其他";
        }
        if (value === "32") {
            return "外勞人力仲介業";
        }
        if (value === "33") {
            return "現金服務業";
        }
        if (value === "34") {
            return "第三方支付服務業";
        }
        if (value === "35") {
            return "線上遊戲事業";
        }
        if (value === "999690") {
            return "軍火業";
        }

    }
}
