import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'preTradeProduct'
})
export class PreTradeProductPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 預計與本行往來之業務
    if (value === '1') {
      return '股票'
    }
    if (value === '2') {
      return '定存'
    }
    if (value === '3') {
      return '基金'
    }
    if (value === '4') {
      return '保險'
    }
    if (value === '5') {
      return '外幣'
    }
    if (value === '6') {
      return '房地產'
    }
    if (value === '7') {
      return '其他'
    }
  }

}
