import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'eoppurpose'
})
export class EoppurposePipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 開戶目的
    if (value === '01') {
      return '薪轉'
    }
    if (value === '02') {
      return '證券'
    }
    if (value === '03') {
      return '存款儲蓄'
    }
    if (value === '04') {
      return '代扣繳'
    }
    if (value === '05') {
      return '投資理財'
    }
    if (value === '07') {
      return '資金調撥'
    }
    if (value === '08') {
      return '貸款需要'
    }
    if (value === '12') {
      return '其他'
    }
  }

}
