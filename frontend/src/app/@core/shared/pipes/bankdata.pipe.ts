import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'bankdata'
})
export class BankdataPipe implements PipeTransform {

  transform(items: any[], searchText: string): unknown {
    // 若陣列沒有任何東西回傳空陣列
    if (!items) { return []; }
    // 若沒有搜尋字 回傳陣列
    if (!searchText) { return items; }
    searchText = searchText.toLowerCase();
    // 若有搜尋字 回傳有關鍵字的相關資料
    return items.filter(it => {
      return ((it.bankId + it.mainbankName)).toLowerCase().includes(searchText);
    });
  }

}
