import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'jobTitle'
})
export class JobTitlePipe implements PipeTransform {

    transform(value: unknown, ...args: unknown[]): unknown {
        // 職稱
        switch (value) {
            case '1':
                return '董監事/股東';
            case '10':
                return '其他';
            case '2':
                return '負責人';
            case '3':
                return '高階管理職務人員';
            case '4':
                return '一般主管';
            case '5':
                return '工程師';
            case '6':
                return '業務人員';
            case '7':
                return '一般職員';
            case '8':
                return '約聘人員';
            case '9':
                return '老師';
            default:
                return value;
        }
    }

}
