import { Pipe, PipeTransform } from '@angular/core';
import { SEND_TYPE } from '../app.constants';

@Pipe({
  name: 'sendTypeDc'
})
/**
 * WT20240514001 Tim 20240515 雙幣卡易用性測試 電子帳單-> 電子對帳單
 */
export class SendTypeDcPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 繳款方式
    if (value === SEND_TYPE.PAPER) {
      return '紙本帳單';
    }
    if (value === SEND_TYPE.EBILLING) {
      return '信用卡電子帳單';
    }
  }

}
