import { Pipe, PipeTransform } from '@angular/core';
import { SEND_TYPE_D3 } from "../app.constants";

@Pipe({
  name: 'sendTypeDcD3'
})
/**
 * WT20240514001 Tim 20240515 雙幣卡易用性測試 電子帳單-> 電子對帳單
 */
export class SendTypeDcD3Pipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 繳款方式
    if (value === SEND_TYPE_D3.PAPER) {
      return '紙本帳單';
    }
    if (value === SEND_TYPE_D3.EBILLING) {
      return '電子對帳單';
    }
  }

}
