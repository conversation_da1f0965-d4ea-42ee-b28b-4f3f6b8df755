import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'education'
})
export class EducationPipe implements PipeTransform {

  transform(value: unknown, ...args: unknown[]): unknown {
    // 教育程度
    if (value === '1') {
      return '博士'
    }
    if (value === '2') {
      return '碩士'
    }
    if (value === '3') {
      return '大學'
    }
    if (value === '4') {
      return '大專'
    }
    if (value === '5') {
      return '高中職'
    }
    if (value === '6') {
      return '國中'
    }
    if (value === '7') {
      return '其他'
    }
    if (value === '8') {
      return '國小'
    }
  }

}
