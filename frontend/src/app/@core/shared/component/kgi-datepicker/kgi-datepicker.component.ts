import { Component, EventEmitter, Input, Output, forwardRef, OnInit, } from '@angular/core';
import moment from 'moment'; // 导入moment
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { PrimeNGConfig } from 'primeng/api';
@Component({
  selector: 'kgi-datepicker',
  templateUrl: './kgi-datepicker.component.html',
  styleUrls: ['./kgi-datepicker.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => KgiDatepickerComponent),
      multi: true
    }
  ]
})
export class KgiDatepickerComponent implements OnInit, ControlValueAccessor {
  // UI上選擇的日期
  selectedDate: any;
  // 最小可選日期
  @Input() minDate: Date;
  // 最大可選日期
  @Input() maxDate: Date;
  // 禁止選取的日期陣列
  @Input() disabledDates: Date[];
  // 日期格式
  @Input() customDateFormat = 'yy-mm-dd';
  // output的資料類型: Date|String
  @Input() dataType = "date";
  // 若output的資料類行為string, output的資料格式
  @Input() dataFormat = 'YYYYMMDD';
  // 是否開放選擇(需先選擇分行)
  @Input() isDisable = true;

  @Output() dateSelect = new EventEmitter<any>();

  ngOnInit() {
    // initialize
    const es= {
      "weekHeader": "周",
      "firstDayOfWeek": 0,
      "dayNames": [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六"
      ],
      "dayNamesShort": [
        "周日",
        "周一",
        "周二",
        "周三",
        "周四",
        "周五",
        "周六"
      ],
      "dayNamesMin": [
        "日",
        "一",
        "二",
        "三",
        "四",
        "五",
        "六"
      ],
      "monthNames": [
        "一月",
        "二月",
        "三月",
        "四月",
        "五月",
        "六月",
        "七月",
        "八月",
        "九月",
        "十月",
        "十一月",
        "十二月"
      ],
      "monthNamesShort": [
        "1 月",
        "2 月",
        "3 月",
        "4 月",
        "5 月",
        "6 月",
        "7 月",
        "8 月",
        "9 月",
        "10 月",
        "11 月",
        "12 月"
      ],
      "chooseYear": "選擇年份",
      "chooseMonth": "選擇月份",
      "chooseDate": "選擇日期",
      "prevDecade": "上一個十年",
      "nextDecade": "下一個十年",
      "prevYear": "上一年",
      "nextYear": "下一年",
      "prevMonth": "上一個月",
      "nextMonth": "下一個月",
      "prevHour": "上一個小時",
      "nextHour": "下一個小時",
      "prevMinute": "上一分鐘",
      "nextMinute": "下一分鐘",
      "prevSecond": "上一秒",
      "nextSecond": "下一秒",
      "am": "上午",
      "pm": "下午",
      "today": "今日",
      "now": "現在",
      "clear": "清除"
    };
    this.primengConfig.setTranslation(es);
  }

  constructor(private primengConfig: PrimeNGConfig) { }


  onChange: any = () => { };
  onTouched: any = () => { };

  writeValue(value: any): void {
    console.log('writeValue Called');
    console.log(value);
    if (value) {
      // 根據dataType來決定如何處理傳入的值
      this.selectedDate = this.dataType.toLowerCase() === 'string' ? moment(value, this.dataFormat).toDate() : value;
    } else {
      this.selectedDate = null;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }



  onDateChange(event: any): void {
    // 假设event对象包含新选择的日期值
    const newDateValue = event; // 根據實際情況，你可能需要調整屬性名

    // 更新selectedDate
    this.selectedDate = newDateValue;

    // 确定输出值的格式
    const value = this.dataType.toLowerCase() === 'string' && newDateValue ? moment(newDateValue).format(this.dataFormat) : newDateValue;

    // 調用onChange以更新錶單控件的值
    this.onChange(value);

    // 發出自定義的onSelect事件
    this.dateSelect.emit(value);
  }






}
