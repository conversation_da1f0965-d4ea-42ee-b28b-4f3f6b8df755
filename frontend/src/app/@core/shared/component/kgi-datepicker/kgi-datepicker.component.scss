::ng-deep .p-datepicker-header {
    /*添加月份的樣式*/
    background-color: #1A43A7 !important;
    color: #f6f8fa !important;
    border: none;
}

::ng-deep .p-datepicker-calendar>thead {
    /*添加星期日到星期六的樣式*/
    background-color: #DAE3F3 !important;
    border: none;
}

::ng-deep .p-inputtext {
    width: 100% !important;
    border: none;
}

::ng-deep .custom-dropdown-style .p-inputtext {
    /* 將滑鼠遊標改為指針，模仿下拉列表的行為 */
    cursor: pointer;

    -webkit-appearance: none;
    /* 移除預設的iOS和Chrome的樣式 */
    -moz-appearance: none;
    /* 移除Firefox的預設樣式 */
    appearance: none;
    /* 移除預設樣式 */
    // background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="16" height="16"><path fill="currentColor" d="M840.4 300H183.6c-13.8 0-21.1 16.8-11.2 26.8l328.4 328.8c6.2 6.2 16.4 6.2 22.6 0l328.4-328.8c9.8-10 2.6-26.8-11.4-26.8z"></path></svg>');
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="16" height="16"><path fill="none" stroke="currentColor" stroke-width="2" d="M4 6l4 4 4-4"/></svg>');

    /* 添加嚮下箭頭 */
    background-repeat: no-repeat;
    background-position: right 0.7em top 50%;
    /* 調整箭頭位置 */
    padding-right: 1.5em;
    /* 確保文本不會覆蓋箭頭 */

}

::ng-deep .custom-dropdown-style .p-calendar .p-button {
    display: none;
    /* 隱藏日曆圖示 */
}