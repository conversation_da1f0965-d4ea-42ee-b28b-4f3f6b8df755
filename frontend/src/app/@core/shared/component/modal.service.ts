import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ModalComponent } from './modal.component';
// TODO: 以下組件在IBR模組中暫不使用，待後續建立
// import { ModalInputComponent } from './modal-input/modal-input.component';
// import { ModalTermsComponent } from './modal-terms/modal-terms.component';
import { TermsService } from '../service/terms.service';
// import { ModalQrcodeComponent } from './modal-qrcode/modal-qrcode.component';
// import { ModalBookingTimepickerComponent } from './modal-booking-timepicker/modal-booking-timepicker.component';
// import { ModalBankInfoComponent } from './modal-bank-info/modal-bank-info.component';
// import { BranchBankInfo } from './bank-list/bank-list.interface';

// 暫時的占位符組件
const ModalTermsComponent = ModalComponent;
const ModalQrcodeComponent = ModalComponent;
const ModalInputComponent = ModalComponent;
const ModalBookingTimepickerComponent = ModalComponent;
const ModalBankInfoComponent = ModalComponent;

// 暫時的介面定義
interface BranchBankInfo {
  branchId?: string;
  branchName?: string;
  address?: string;
  [key: string]: any;
}


@Injectable({
    providedIn: 'root'
})
export class ModalService {
    clickBtnImgType = "";

    constructor(private modalService: NgbModal,
                private http: HttpClient,private termsService: TermsService) {
    }

    openTermsModalWithTermsTable(title: string, termsName:string) {
        this.termsService.getTermsWithTermsName(termsName).subscribe((res)=>{
            console.log(res);

            const modalRef = this.modalService.open(ModalTermsComponent,{scrollable: true, size:'lg',centered: true});
            modalRef.componentInstance.title = title;
            modalRef.componentInstance.terms = res.content.toString();
            return modalRef.result;
        })
    }

    public openQrCodeModal(qrImage:string,shortUrl:string){
        const modalRef = this.modalService.open(ModalQrcodeComponent);
        modalRef.componentInstance.qrImage = qrImage;
        modalRef.componentInstance.shortUrl = shortUrl;
        return modalRef.result;
    }

    openModal(title = '預設標題',
              context?: string,
              cancelButtonText = '取消',
              confirmButtonText = '確定',
              scrollable = false) {
        const modalRef = this.modalService.open(ModalComponent, {scrollable: true, centered: true});
        modalRef.componentInstance.title = title;
        modalRef.componentInstance.context = context;
        modalRef.componentInstance.cancelButtonText = cancelButtonText;
        modalRef.componentInstance.confirmButtonText = confirmButtonText;
        modalRef.componentInstance.scrollable = scrollable;
        return modalRef.result;
    }


    openTermsModal(title: string, terms:string ) {
        const modalRef = this.modalService.open(ModalTermsComponent,{scrollable: true, size:'lg',centered: true});
        modalRef.componentInstance.title = title;
        modalRef.componentInstance.terms = terms;
        return modalRef.result;
    }

    public openModalInput(text: string, value: string) {

        const modalRef = this.modalService.open(ModalInputComponent);
        modalRef.componentInstance.text = text;
        modalRef.componentInstance.value = value;
        return modalRef.result;
    }

    public openBookingTimeModal(
        title: string,
        value: any,
        bookingTimeArray: any
      ) {
        const modalRef = this.modalService.open(ModalBookingTimepickerComponent, {
          scrollable: true,
          centered: true,
          size: "xs",
        });
        if (title) {
          modalRef.componentInstance.title = title;
        }
        if (value) {
          modalRef.componentInstance.selected = value;
        }
        if(bookingTimeArray){
          modalRef.componentInstance.bookingTimeArray = bookingTimeArray;
        }
    
        return modalRef.result;
      }
    
      //點選鄰近分行
      openBankInfoModal(branchId: string) {
        const modalRef = this.modalService.open(ModalBankInfoComponent, {
          scrollable: true,
          centered: true,
          size: "xs",
        });
        if (branchId) {
          modalRef.componentInstance.branchId = branchId;
        }
        return modalRef.result;
      }
      //點選地址
      openBankInfoByData(branchInfo: BranchBankInfo){
        const modalRef = this.modalService.open(ModalBankInfoComponent, {
          scrollable: true,
          centered: true,
          size: "xs",
        });
        if (branchInfo) {
          modalRef.componentInstance.branchInfo = branchInfo;
        }  
        return modalRef.result;
      }
    

}
