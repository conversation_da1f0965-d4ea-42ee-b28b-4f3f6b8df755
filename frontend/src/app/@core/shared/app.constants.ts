// DO NOT EDIT THIS FILE, EDIT THE WEBPACK COMMON CONFIG INSTEAD, WHICH WILL MODIFY THIS FILE

import { COMPONENT_AIO_FLOW_TYPE } from './routing/aio.path';
import { environment } from '../../../environments/environment';

const _VERSION = '0.0.0'; // This value will be overwritten by webpack
const _DEBUG_INFO_ENABLED = true; // This value will be overwritten by webpack
export const VERSION = _VERSION;
export const DEBUG_INFO_ENABLED = _DEBUG_INFO_ENABLED;

/**
 * 凱銀官網
 */
export const KIG_OFFICIAL_WEBSITE = 'https://www.kgibank.com.tw/zh-tw/';

/**
 * 取得欄位名稱
 * @param obj
 * @param type
 */
export function GET_BASEMODEL_FIELDNAME<T> (obj: any) {
    return new Proxy(obj, {
        get(_, key: string) {
            return key;
        }
    });
}

export const MASK_MOBILE = [/[0]/,  /[9]/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/, /\d/, /\d/]; // 行動電話

export const MASK_ID = [/[A-Z]/, /[1-2]/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/]; // 身分證號

export const MASK_VAT = [/\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/, /\d/]; // 統編

export const MASK_TEL = (rawValue: string) => {
    // https://github.com/text-mask/text-mask/blob/master/addons/src/createNumberMask.js
    // console.log('telMask[: ', rawValue);

    // (03) 36789..
    if (rawValue && (rawValue.startsWith('(03) 7') || rawValue.startsWith('(037)'))) {// 苗栗縣 037
        return ['(', /[0]/, /[3]/, /[7]/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/];
    } else if (rawValue && (rawValue.startsWith('(04) 9') || rawValue.startsWith('(049)'))) {// 南投縣 049
        return ['(', /[0]/, /[4]/, /[9]/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/];
    } else if (rawValue && (rawValue.startsWith('(08) 9') || rawValue.startsWith('(089)'))) {// 台東縣 089
        return ['(', /[0]/, /[8]/, /[9]/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/];
    } else {
        return ['(', /[0]/, /[2-8]/, ')', ' ', /\d/, /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/];
    }
};

export const MASK_BIRTH = (rawValue) => {
    // console.log('birthMask: ', rawValue);
    // 2000-01-01
    if (rawValue && rawValue[5] === 0) {// 月份第一位數為零
        if (rawValue[8] === 0) { // 日期第一位數為0
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[0-1]/, /[1-9]/, '-', /[0-3]/, /[1-9]/]; // 不會有 00月00日
        } else if (rawValue[8] === 3) { // 日期第一位數為3
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[0-1]/, /[1-9]/, '-', /[0-3]/, /[0-1]/]; // 不會有 32日
        } else {
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[0-1]/, /[1-9]/, '-', /[0-3]/, /\d/]; // 不會有 00月份
        }
    } else if (rawValue && rawValue[5] === 1) {// 月份第一位數為1
        if (rawValue[8] === 0) { // 日期第一位數為0
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[1]/, /[0-2]/, '-', /[0-3]/, /[1-9]/]; // 只有10~12月, 不會有 00日
        } else if (rawValue[8] === 3) { // 日期第一位數為3
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[0-1]/, /[1-9]/, '-', /[0-3]/, /[0-1]/]; // 不會有 32日
        } else {
            return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[1]/, /[0-2]/, '-', /[0-3]/, /\d/]; // 不會有 00月份
        }
    } else {
        return [/[1-2]/, /\d/ , /\d/, /\d/, '-', /[0-1]/, /\d/, '-', /[0-3]/, /\d/];
    }
};

export enum MODE_ENUM {
    MAIN = 0, // 清單首頁
    ADD = 1, // 新增
    UPDATE = 2, // 更新
    READ = 3  // 唯讀
}

export enum RETURN_CODE {
    SUCCESSFUL = '000', // 執行成功
    SUCCESS_NO_DATA = '001', // 執行成功，無資料
    SUCCESS_VERIFICATION_MESSAGE = '004', // 執行成功，無資料

    ERROR_PIC = '999', // r2-3141 圖片超過10MB
    ERROR_Ed3BreakPoint_ACCOUNT = '3008', // r2-3143 身分證 申辦過舊d3 未完成
    UNNECESSARY_SETTING = '3100', // 您無需設定電話銀行及網／行銀密碼
    ERROR_OTHER_SETTING_PROCESS = '5003', // 網行銀設定處理中
    ERROR_OTHER_SETTING_FAIL = '5004', // 網行銀設定失敗
    SUCCESS_BREAK_POINT_DETECTED = '9000', // 執行成功. 有斷點 有起案
    ERROR_NOT_ALLOW_FOR_BUSINESS = '9001', // 執行成功. 產品皆無法申辦
    ALLOW_FOR_BUSINESS = '9002', // 執行成功. 可申辦產品為
    SUCCESS_BREAK_POINT_NOAPS = '9005', // 執行成功. 有斷點 未起案
    SUCCESS_BREAK_POINT_CREDITCARD_TIMEOUT = '9006', // 執行成功. 有斷點 信用卡不在時效內
    SUCCESS_BREAK_POINT_CREDITCARD = '9007', // 執行成功. 有斷點 信用卡在時效內
    ERROR_NOT_ALLOW_FOR_BUSINESS_CREATE_DGT_LOAN = '9009', // 案件起案發生異常，請洽服務專員(A001)
    ERROR_NOT_ALLOW_FOR_BUSINESS_REPEAT_APPLY = '9010', // 近期已申請過此產品，請洽服務專員(A002)
    ERROR_NOT_KS_URL = '9012', // 請使用業務專員提供網址重新由凱證官網進入
    ERROR_EXISTED_PHONE = '3006', // 執行成功. 有辦數三，但手機號碼重複
    ERROR_NOT_ALLOW_FOR_ALREADY_APPLY = '5001', // 預約過，預約時間還沒到
    ERROR_NOT_ALLOW_FOR_PHONE = '5002', // 執行成功. 85081 手機號碼與登入頁手機號碼不同
    ERROR_WORKFLOW_ERROR = '9994', // workflow activity notfound
    ERROR = '9999', // 執行失敗
    RETRY = '1000',
    APPLY_IDENTIFIED_CHECK = '1001',
    ERROR_APPLY_D2_NO_EMAIL = 'F039',
    ERROR_VALUE = '9997',
    ERROR_EXISTED_DUAL_CURRENCY = '2005',
    ERROR_APPLY_WARNING_ACCOUNT = '3110',
    ERROR_APPLY_WARNING_ACCOUNT_DC = '8823', // 雙幣卡: 警示、告誡戶 不可申辦
    // ERROR_APPLY_WARNING_ACCOUNT_CC = '8823_cc', // 一般卡: 警示、告誡戶 不可授扣，Tim todo: 改成跟數存一樣用 ERROR_APPLY_WARNING_ACCOUNT，之後可刪除
    ERROR_APPLY_WARNING_ACCOUNT_CC_CANCEL = '8823_cc_cancel', // 一般卡警示戶、告誡戶取消授扣
    USE_TMCASE_CHECK = '9014',
    ERROR_OCR_ID_FRONT_EMPTY = '9811', // 您上傳的照片可能模糊不清或上傳不正確的證件，建議重新上傳。
    ERROR_OCR_ID_FRONT_EMPTY_FORE_TIMES = '9812', // 您拍攝的照片可能模糊不清或證件種類不符，請調整角度與光線，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。
    ERROR_OCR_ID_FRONT_NOT_SAME = '9813', // 您上傳的身分證與申請人資料不同，請重新提供本人最新版身分證！
    ERROR_OCR_ID_FRONT_OVER_FOUR_TIMES = '9814', // 您上傳的身分證與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。
    ERROR_OCR_BIRTH_PLACE = '9815', // 很抱歉，您的出生地非臺灣，無法提供您線上開立本行數位帳戶，請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶
    ERROR_OCR_HOUSE_PLACE = '9816', // 您提供證件之戶籍地址為公所或戶政事務所，無法提供您線上開立本行數位帳戶，請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶
    ERROR_OCR_SEC_HEALTH_VS_ERR = '9817', // 您上傳的第二證件與身分證資料不同，請重新提供！
    ERROR_OCR_SEC_DRIVE_VS_ERR = '9818', // 您上傳的駕照與身分證資料不同，請重新提供！
    ERROR_OCR_SEC_PASSPORT_VS_ERR = '9819', // 您上傳的護照與身分證資料不同，請重新提供！
    ERROR_OCR_SEC_HEALTH_VS_FOUR_ERR = '9820', // 您上傳的健保卡與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。
    ERROR_OCR_SEC_DRIVE_VS_FOUR_ERR = '9821', // 您上傳的駕照與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。
    ERROR_OCR_SEC_PASSPORT_VS_FOUR_ERR = '9822', // 您上傳的護照與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。
    ERROR_OCR_SEC_PASSPORT_IS_OLD_ERR = '9823', // 您的證件效期已屆滿或過期，請提供其他第二證件。
    ERROR_OCR_COLOR_ERR = '117', // 您提供的影像模糊不清，建議您將證件橫放後直立擺設，且須四角含證件膠框都需於拍攝範圍內，並確認證件上無任何反光，無誤後按下對焦後拍攝，麻煩重新拍攝或上傳。
    ERROR_OCR_SIZE_ERR = '119', // 您提供的影像模糊不清，建議您將證件橫放後直立擺設，且須四角含證件膠框都需於拍攝範圍內，並確認證件上無任何反光，無誤後按下對焦後拍攝，麻煩重新拍攝或上傳。
    ERROR_OCR_MARKED = '9824', // 已於後端註記，前端流程繼續
    ERROR_OCR_ERR = '101', // 辨識失敗。
    ERROR_OCR_ID_FRONT_INPUT_CHECK = '請確認修正內容是否正確?',
    ERROR_OCR_ID_SECCARD_CHECK = '請確認證件是否為本人證件，且四角完整拍攝清楚。',
    ERROR_APPLY_DC_NO_ACC = '3107', // 申辦雙幣哩程卡須開立凱基臺幣、外幣存款帳戶設定消費自動扣繳。你未持符合設定自動扣繳的凱基存款帳戶，請洽凱基銀行分行辦理開戶！
    ERROR_EXISTED_ACCOUNT = '3003', // 本功能僅提供首次開戶之客戶使用，您已持有本行存款帳戶，若您有其他開戶需求，歡迎蒞臨全省各分行辦理。
    ERROR_APPLY_DC_NO_FOREIGN_ACC = '3106', // 申辦雙幣哩程卡須開立凱基臺、外幣存款帳戶並設定為自動扣款帳戶。你未持凱基外幣存款帳戶，請先線上申請或洽凱基銀行分行辦理外幣帳戶開戶！
    ERROR_APPLY_DC_NO_TW_ACC = '3111', // 申辦雙幣哩程卡須開立凱基臺、外幣存款帳戶並設定為自動扣款帳戶。你未持凱基臺幣存款帳戶，請先線上申請或洽凱基銀行分行辦理臺幣帳戶開戶！

    /**
     * 信用卡調額
     */
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF = '9601 呼叫 /OnboardingCif/CustInfo 時發生錯誤，請詢問行銀', // 呼叫 /OnboardingCif/CustInfo 時發生錯誤（OnboardingCif/CustInfo）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF_DATAERROR = '9602 行銀回覆資料的身分證或信用卡號或永久調額客服電子單單號欄位不正確，請查看 AioApiLog CREDIT_LIMIT/OnboardingCif/CustInfo', // 免登發現身分證或信用卡欄位不正確 透過 token 跟行銀取得 CIF 失敗（OnboardingCif/CustInfo DATA 不對）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF_OBD = '9603 OBD未知錯誤，請查看 OBD ServerLog', // 透過 token 跟行銀取得 CIF 失敗（OnboardingCif/CustInfo OBD 問題？！）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720 = '9701 呼叫 信用卡電文額度NCCME472000Q時，發生錯誤，請詢問信用卡', // 透過 id 查詢信用卡電文額度 失敗（NCCME472000Q）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720_DATAERROR = '9702 呼叫信用卡電文額度NCCME472000Q時，發生錯誤，請查看 AioApiLog CREDIT_LIMIT/NCCME472000Q', // 透過 id 查詢信用卡電文額度 失敗（NCCME472000Q DATA 不對）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720_OBD = '9703 查詢信用卡電文，OBD未知錯誤，請查看 OBD ServerLog', // 透過 id 查詢信用卡電文額度 失敗（NCCME472000Q OBD 問題？！）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETTOKEN = '9801 OBD token 失敗，請查看 OBD ServerLog', // 取得 OBD token 失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_DATAERROR_SAVE = '9802 OBD 案編，取得 CaseData 資料 失敗，請查看 OBD ServerLog', // 透過 OBD 案編，取得 CaseData 資料 失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_DATAERROR_GET = '9803 OBD 案編，取得 CaseData 資料 失敗，請查看 OBD ServerLog', // 透過 OBD 案編，取得 CaseData 資料 失敗

    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SEND = '9901 呼叫 CS041 申請失敗，請詢問客服電子單，並查看 AioApiLog CREDIT_LIMIT/CS041Send', // 通知要新增客服電子單CS041申請額度調整 失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SEND_CATCH = '9902 OBD 呼叫 CS041 申請失敗，請查看 OBD ServerLog', // 通知要新增客服電子單CS041申請額度調整 OBD失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SUPPLY = '9903 呼叫 CS041 補件失敗，請詢問客服電子單，並查看 AioApiLog CREDIT_LIMIT/CS041Supply', // 通知要補件客服電子單CS041申請額度調整 失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SUPPLY_CATCH = '9904 OBD 呼叫 CS041 補件失敗，請查看 OBD ServerLog', // 通知要補件客服電子單CS041申請額度調整 OBD失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_MESSAGE = '目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088',
}

export enum UNIQ_TYPE {
    /**
     * 單一申辦 貸款
     */
    LOAN = '01',

    /**
     * 卡加貸 (舊系統邏輯)
     */
    LOAN_CC = '02',

    /**
     * 立約 contract
     */
    CONTRACT = '03',

    /**
     * 單一申辦 信用卡
     */
    CC = '05',

    /**
     * aio-cc
     */
    AIO_CC = '06',

    /**
     * aio-loan
     */
    AIO_LOAN = '07',

    /**
     * aio-d3
     */
    AIO_D3 = '08',

    /**
     * aio-loan-d3
     */
    AIO_LOAN_D3 = '09',

    /**
     * aio-loan-cc
     */
    AIO_LOAN_CC = '10',

    /**
     * aio-cc-d3
     */
    AIO_CC_D3 = '11',

    /**
     * aio-loan-cc-d3
     */
    AIO_LOAN_CC_D3 = '15',

    /**
     * d3
     */
    D3 = '13',

    /**
     * sal
     */
    SAL = '12',

    /**
     * appointment 與 單一d3 共用
     */
    APPOINTMENT = '14',

    /**
     * D2
     */
    D2 = '16',

    /**
     * 房貸
     */
    HL = '17',

    /**
     * 信用卡調額
     */
    CASE_UNINQTYPE_CREDIT_LIMIT = '18'

}

export enum VERIFICATION_TYPE {
    FIRST ='FIRST',
    SECOND = 'SECOND',
    THIRD = 'THIRD',
}

export enum PRODUCT_TYPE {
    LOAN = 'loan',
    CC = 'cc',
    D3 = 'd3',
    SAL = 'sal',
    APPT = 'appt',
    D2 = 'd2',
    HL = 'hl',
}

export enum LOAN_QUOTA {
    PL_1 = 1,
    PL_2 = 2,
    RPL_FAST = 3, // 速還金
    RPL_CASH = 4, // 靈活卡(現金卡)
    HL //房屋貸款
}

export enum LOAN_TYPE {
    LOAN_INSTALLMENT = 'ra1', //分期型
    LOAN_QUOTA = 'ra2', //額度型
    LOAN_BOTH = 'ra3', //分期型+額度型
}

// export const SERVER_URL = '/obd/Appt_landing/';
export const SERVER_URL = environment.productApiPrefix;


/**
 * 預設日期格式
 */
export const DEFAULT_DATE_PATTERN = 'yyyy-MM-DD';

/**
 * 檔案上傳限制大小
 */
export const UPLOAD_LIMITED_SIZE: number = 1024 * 1024 * 10;

export enum TYPE_PRODUCT {
    ACCOUNT = '1', // 帳戶
    ACCOUNT_AND_CARD = '2' // 帳戶+信用卡
}

export function getEnumKeyByValue(myEnum: any, enumValue: number | string): string {
    const keys = Object.keys(myEnum).filter((x) => myEnum[x] === enumValue);

    return keys.length > 0 ? keys[0] : '';
}

export function uniqTypeForm(value: string): string {
    switch(value) {
        case COMPONENT_AIO_FLOW_TYPE.CC: //05
            return UNIQ_TYPE.CC;
        case COMPONENT_AIO_FLOW_TYPE.AIO_DC: // 雙幣信用卡
        case COMPONENT_AIO_FLOW_TYPE.AIO_CC: //06
            return UNIQ_TYPE.AIO_CC;
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN: //07
            return  UNIQ_TYPE.AIO_LOAN;
        case COMPONENT_AIO_FLOW_TYPE.AIO_D3: //08
            return  UNIQ_TYPE.AIO_D3;
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_D3: //09
            return  UNIQ_TYPE.AIO_LOAN_D3;
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_DC: // 貸款+雙幣信用卡
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_CC: //10
            return  UNIQ_TYPE.AIO_LOAN_CC;
        case COMPONENT_AIO_FLOW_TYPE.AIO_DC_D3: // 雙幣信用卡+數三
        case COMPONENT_AIO_FLOW_TYPE.AIO_CC_D3: //11
            return  UNIQ_TYPE.AIO_CC_D3;
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_DC_D3: // 貸款+雙幣信用卡+數三
        case COMPONENT_AIO_FLOW_TYPE.AIO_LOAN_CC_D3: //15
            return  UNIQ_TYPE.AIO_LOAN_CC_D3;
        case COMPONENT_AIO_FLOW_TYPE.D3: //13
            return  UNIQ_TYPE.D3;
        case COMPONENT_AIO_FLOW_TYPE.SAL: //12
            return  UNIQ_TYPE.SAL;
        case COMPONENT_AIO_FLOW_TYPE.APPT: //13
            return  UNIQ_TYPE.APPOINTMENT;
        case COMPONENT_AIO_FLOW_TYPE.HL: //17
            return  UNIQ_TYPE.HL;
        case COMPONENT_AIO_FLOW_TYPE.HLASSURER: //17
            return  UNIQ_TYPE.HL;
        default:
            return  UNIQ_TYPE.LOAN; //預設貸款
    }
}

export enum TYPE_EID {
    IDC = '0', // 一般身分證
    EID = '1' // 新式身分證
}

// 0 or undefined:即時, 1:日, 2:月, 3: 季, 4:年
export enum QUERY_TYPE {
    REALTIME = 0, // 即時
    DAY = 1, // 日
    MONTH = 2, // 月
    SEASON = 3, // 季
    YEAR = 4 // 年
}

export enum PRODUCT_NAME {
    PL = '貸款-分期型',
    RPL = '貸款-額度型',
    D3 = '數位帳戶',
    CC = '信用卡',
    LOAN_PL = '個人信貸',
    LOAN_RPL = '額度型貸款',
    LOAN_GM = '靈活卡',
    LOAN_HL = '房屋貸款',
    SAL = '薪轉帳戶',
    APPT = '分行開戶預填',
    D2 = '數位帳戶-簡化',
}

/**
 * #6181
 * 信用卡調額 使用
 */
export enum CREDIT_LIMIT_CONSTANTS {
    PRODUCT_TYPE_CREDIT_LIMIT = 'CREDIT_LIMIT',
    ACTION_CREDIT_LIMIT_A = 'A', // 信用卡調額 申請
    ACTION_CREDIT_LIMIT_S = 'S', // 信用卡調額 補件
    TITLE_CREDIT_LIMIT_A = '永久額度調升申請',
    TITLE_CREDIT_LIMIT_S = '財力證明文件補件',
    FINISH_CREDIT_LIMIT_A = '完成額度調整申請',
    FINISH_CREDIT_LIMIT_S = '完成財力證明文件補件',
}

export enum PRODUCT_SYMBOL {
    UnableApply = 'X', // 不能辦被踢產品
    UnableApplyForPhone = 'XX', // 辦D3但手機號碼重複
}

// 條款變數
export enum TERMS{

    PERSONAL_DATA_AGREEMENT = 'qr_personal_data_agreement', // 個資料款
}

export enum SEND_TYPE{
    // 紙本
    PAPER = '1',
    // 電子帳單
    EBILLING =  '2',

}

export enum SEND_TYPE_D3{
    // 紙本
    PAPER = '3',
    // 電子帳單
    EBILLING =  '2',

}

export enum PROCESS_TYPE{
    /** Process預約分行流程 */
    CASEDATA_PROCESS_BOOKBRANCH = '1',
    /** Process 數位帳戶流程  */
    CASEDATA_PROCESS_DIGIT =  '2',

}

/** 複委託選取狀態 **/
export enum SUB_BRO_BIN_KIND {
    T = '0',
    F = '1',
    TF = '2',
}

/** KS 凱證 API 欄位eoaKind回傳資料判斷  **/
export enum EOA_KIND {
    Y = 'YY',
    N = 'YN'
}

/** 判斷進入方式  **/
export enum CROSS {
    KSD = 'KSD',
    KS = 'KS',
    KB = 'KB'
}

/** 上傳圖片線上線下狀態  **/
export enum PHOTO_ONLINE {
    PHOTO_ONLINE_STATUS_ONLINE = 1, // 線上
    PHOTO_ONLINE_STATUS_OFFLINE = 2 // 線下
}

/** 上傳圖片產品別種類  **/
export enum PHOTO_PRODUCTTYPE {
    PHOTO_PRODUCTTYPE_CREDIT = '1', // 線上信用卡
    PHOTO_PRODUCTTYPE_LOAN = '2', // 線上貸款
    PHOTO_PRODUCTTYPE_SAL = '3', // 線上薪轉
    PHOTO_PRODUCTTYPE_D3 = '4', // 線上數三
    PHOTO_PRODUCTTYPE_AIO = '5', // 線上多合一
    PHOTO_PRODUCTTYPE_APPT = '6', // 線上預約開戶
    PHOTO_PRODUCTTYPE_Flex_Card = '7', // 靈活卡
    PHOTO_PRODUCTTYPE_Addition = '8', // 加辦
    PHOTO_PRODUCTTYPE_LOAN_APPLYMONEY = '9', // 貸款額度調整
    PHOTO_PRODUCTTYPE_D2 = '10', // 線上數二
    PHOTO_PRODUCTTYPE_HL = '11', //線上房貸
    PHOTO_PRODUCTTYPE_CREDIT_LINIT = '12', // 信用卡額度調整
}

export enum ADD_MENU {
    ID_FIN_OTHER = '01', // 信用卡、貸款、多合一沒有D3 (身分證 + 財力證明 + 其他文件)
    ID_SEC_FIN_OTHER = '02', // 多合一有D3 (身分證 + 第二證件 + 財力證明 + 其他文件)
    ID = '03', // 數三、薪轉 (身分證)
    SEC = '04', // 數三、薪轉 (第二證件)
    ID_SEC = '05', // 數三、薪轉 (身分證 + 第二證件)
    FIN = '06'
}

/**
 * ref: https://momentjscom.readthedocs.io/en/latest/moment/03-manipulating/01-add/
 */
export enum MOMENT_UNIT {
    YEARS = 'y',
    QUARTERS = 'Q',
    MONTHS = 'M',
    WEEKS = 'w',
    DAYS = 'd',
    HOURS = 'h',
    MINUTES	= 'm',
    SECONDS	= 's',
    MILLISECONDS = 'ms'
}

export enum MOMENT_TYPE {
    AFTER = 'after',
    BEFORE = 'before',
    BETWEEN = 'between'
}


export enum DEVICE_ID_TYPE {
    DEVICE_ID_FIRST = 'device.id.first',
    DEVICE_ID_SECOND = 'device.id.second',
}

// Dialog 按鈕文字
/**
 * WT20240219002 Maggie
 * 根據不同情境，Dialog 需有不同按鈕
 */
export enum DIALOG_BUTTON {
    NEXT = '接續其他業務申辦',
    CHANGE_ACCOUNT = '更換其他帳戶'
}

// Dialog 按鈕文字
export enum DIALOG_BTN {
    GO_APPT ='立即至分行開戶預填',
    RE_UPLOAD = '重新上傳',
    GO_IDENTITY = '重新申辦',
    CHECK_NOT_ERR = '確認無誤',
    CORRECT = '立即更正',
    CONFIRM = '確定',
    USE_PHOTO = '使用本圖',
}

export enum OCR_STYPE {
    ID_CARD_FRONT = '1',
    ID_CARD_BACK = '2',
    SEC_CARD = '3',
}

// OCR 使用卡片種類
export enum CARD_TYPE {
    FRONT ='front',
    BACK = 'back',
    SEC = 'sec',
    ID_CARD = 'IDCard',
}

// OCR 開關
export enum OCR_SWITCH {
    ON = 'Y',
    OFF = 'N',
}

/**
 * 自動扣款用
 */
export enum DEDUCTION{
    DEDUCTION_ON_WAY = '9', // 自扣申請途中
    KG_BANK_NO = '809', // 凱基銀行代碼
}

export enum DROP_MONEY_PURPOSE {
    INVERT_CODE = '10'
}
/**
 * ******** Maggie WT********003b 新增顯示 otpPhone 彈窗
 */
export enum DIALOG_MSG {
    OTP_PHONE = '若要變更OTP行動電話請至臨櫃或網路銀行插入晶片金融卡進行變更',
}

/**
 * 信用卡常數
 */
export enum CREDIT_CARD {
    CHINA_LIFE = '513001', // 中壽悠遊VISA御璽
    DC_CARD_USD = '531001', // 美元雙幣卡
    DC_CARD_JPY = '532001', // 日元雙幣卡
    DC_CARD_EUR = '533001', // 歐元雙幣卡
    CARD_GROUP_DC = 'dc', // 雙幣卡
    ESILT_CARD_A = '523', // 誠品聯名 A 卡
    ESILT_CARD_B = '524', // 誠品聯名 B 卡
    BEWIN_CARD = '666', // 幣享卡

}

/**
 * ******** WT20240219004 Maggie <br>
 * 數二 67050 Email 失聯註記回前端跳彈窗提示文字
 */
export enum D2_UNCF_EMAIL {
    TEXT = '您留存在本行的Email非有效，請您至本行網行銀或客服進行更新。未更新前，本行交付綜合對帳單會郵寄至您留存於本行通訊地址。'
}

/**
 * ******** WT20240503008 Maggie <br>
 * 凱證既有戶 tag
 */
export enum STOCK_EXIST {
    EXIST_KS = 'existKS'
}

/**
 * ******** WT20240503008 Maggie <br>
 * 凱證既有戶 tag
 */
export enum DEPOSIT_BANK {
    KGI_BANK = '809'
}

/**
 * product-header 特別 title
 */
export enum PRODUCT_HEADER_TITLE {
    // 視訊服務特別 title
    VIDEO_TITLE = '數位帳戶非約定轉帳額度提升'
}

/**
 * 各畫面 icon
 */
export enum ICON {
    // 自行上傳圖示 icon
    UPLOAD_SELF = 'assets/image/icon/uploadSelf.svg',
    UPLOAD_PDF = 'assets/image/icon/pdf.png'
}

/**
 * 上傳檔案類型
 */
export enum UPLOAD_FILE_TYPE {
    PDF = 'application/pdf',
    JPG = 'image/jpg',
    PNG = 'image/png',
    JPEG = 'image/jpeg',
}

/**
 * 條款名稱
 */
export enum TERMS_NAME {
    ESLITE_CARD_TERMS = 'esliteCardTerms', // 誠品聯名卡特別條款
    ESLITE_CARD_TERMS_CHE = '誠品聯名卡特別條款',
    BEWIN_CARD_TERMS = 'bewin_card', // 幣享卡特別約定條款
    CAPITAL_TERMS = 'capital', // 凱基人壽卡條款
    AUTOMATIC_WITH_HOLDING_AGREEMENT_TERMS = 'AutomaticWithholdingAgreement', // 委託轉帳代繳授權約定條款
    DC_CARD_TERMS = 'dualCurrencyCardTerms' // 雙幣卡條款
}


/**
 * WT20250331001 20250423 Beck
 * 中文轉拼音
 *
 */
export enum PinyinEnum {
    WG = 'WG',
    CHINESE = 'CHINESE',
    // UNIVERSAL = 'UNIVERSAL',

}
