import {
    Http<PERSON>lient,
    HttpErrorResponse,
    HttpEvent,
    HttpHandler,
    HttpInterceptor,
    HttpRequest,
    HttpResponse
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError, timer, of } from 'rxjs';
import { 
    tap, 
    catchError, 
    retry, 
    timeout, 
    finalize, 
    switchMap,
    delay
} from 'rxjs/operators';
import { GlobalDataService } from '../service/global.service';
import { EncryptService } from '../service/encrypt.service';
import { SpinnerService } from '../service/spinner.service';
import moment from 'moment';
import { SERVER_URL } from '../app.constants';

/**
 * IBR系統HTTP請求/回應介面定義
 */
export interface IBRApiRequest {
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: any;
    timestamp: string;
    requestId: string;
}

export interface IBRApiResponse {
    status: number;
    statusText: string;
    body?: any;
    timestamp: string;
    responseTime: number;
    requestId: string;
}

export interface IBRApiError {
    error: HttpErrorResponse;
    requestId: string;
    retryCount: number;
    timestamp: string;
}

export interface IBRInterceptorConfig {
    enableEncryption: boolean;
    enableLogging: boolean;
    enableRetry: boolean;
    maxRetryAttempts: number;
    retryDelay: number;
    timeoutMs: number;
    apiVersion: string;
    enableSpinner: boolean;
}

/**
 * IBR系統統一HTTP攔截器
 * 提供認證、加密、記錄、錯誤處理、重試機制等核心功能
 */
@Injectable({
    providedIn: 'root'
})
export class IBRAuthInterceptor implements HttpInterceptor {

    private readonly defaultConfig: IBRInterceptorConfig = {
        enableEncryption: true,
        enableLogging: true,
        enableRetry: true,
        maxRetryAttempts: 3,
        retryDelay: 1000,
        timeoutMs: 30000,
        apiVersion: 'v1',
        enableSpinner: true
    };

    private activeRequests = new Set<string>();
    private requestIdCounter = 0;

    constructor(
        private router: Router,
        private globalDataService: GlobalDataService,
        private encryptService: EncryptService,
        private spinnerService: SpinnerService,
        private http: HttpClient
    ) {}

    intercept(
        request: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {
        
        const requestId = this.generateRequestId();
        const startTime = Date.now();
        const config = this.getRequestConfig(request);
        
        // 開始請求記錄
        if (config.enableLogging) {
            this.logRequest(request, requestId);
        }

        // 顯示載入狀態
        if (config.enableSpinner && !this.hasActiveRequests()) {
            this.spinnerService.show('IBR_API_SPINNER');
        }

        this.activeRequests.add(requestId);

        // 建立修改後的請求
        const modifiedRequest = this.buildRequest(request, config, requestId);

        return next.handle(modifiedRequest).pipe(
            // 超時處理
            timeout(config.timeoutMs),
            
            // 重試機制
            retry({
                count: config.enableRetry ? config.maxRetryAttempts : 0,
                delay: (error: HttpErrorResponse, retryCount: number) => {
                    if (this.shouldRetry(error, retryCount, config)) {
                        this.logRetryAttempt(requestId, retryCount, error);
                        return timer(config.retryDelay * Math.pow(2, retryCount - 1));
                    }
                    return throwError(() => error);
                }
            }),

            // 處理成功回應
            tap((event: HttpEvent<any>) => {
                if (event instanceof HttpResponse) {
                    const responseTime = Date.now() - startTime;
                    
                    if (config.enableLogging) {
                        this.logResponse(event, requestId, responseTime);
                    }

                    // 解密回應資料
                    if (config.enableEncryption && event.body) {
                        event = event.clone({
                            body: this.decryptResponse(event.body)
                        });
                    }
                }
            }),

            // 錯誤處理
            catchError((error: HttpErrorResponse) => {
                const responseTime = Date.now() - startTime;
                
                if (config.enableLogging) {
                    this.logError(error, requestId, responseTime);
                }

                return this.handleError(error, requestId);
            }),

            // 完成處理
            finalize(() => {
                this.activeRequests.delete(requestId);
                
                // 隱藏載入狀態
                if (config.enableSpinner && !this.hasActiveRequests()) {
                    this.spinnerService.hide('IBR_API_SPINNER');
                }
            })
        );
    }

    /**
     * 建立修改後的HTTP請求
     */
    private buildRequest(
        request: HttpRequest<any>, 
        config: IBRInterceptorConfig, 
        requestId: string
    ): HttpRequest<any> {
        
        const timestamp = moment().format('YYYY/MM/DD HH:mm:ss:SSS');
        const token = this.globalDataService.token;
        
        let headers = request.headers
            .set('KGI', 'KGI')
            .set('X-Request-ID', requestId)
            .set('X-API-Version', config.apiVersion)
            .set('X-Timestamp', timestamp);

        // 添加認證Token
        if (token) {
            headers = headers.set('Authorization', `Bearer ${token}`);
        }

        // API時間標記（用於nginx追蹤）
        headers = headers.set('ApiTime', timestamp);

        // 處理請求體加密
        let body = request.body;
        if (config.enableEncryption && body && this.shouldEncryptRequest(request)) {
            body = this.encryptRequest(body);
            headers = headers.set('Content-Encrypted', 'true');
        }

        return request.clone({
            headers,
            body
        });
    }

    /**
     * 獲取請求配置
     */
    private getRequestConfig(request: HttpRequest<any>): IBRInterceptorConfig {
        // 可以根據URL或headers來自訂配置
        const config = { ...this.defaultConfig };
        
        // 例如：某些API不需要加密
        if (request.url.includes('/public/') || request.url.includes('/health')) {
            config.enableEncryption = false;
            config.enableSpinner = false;
        }

        // 上傳檔案時不需要加密
        if (request.headers.has('Content-Type') && 
            request.headers.get('Content-Type')?.includes('multipart/form-data')) {
            config.enableEncryption = false;
        }

        return config;
    }

    /**
     * 加密請求資料
     */
    private encryptRequest(data: any): any {
        try {
            if (typeof data === 'object') {
                // 這裡可以實作具體的加密邏輯
                // 例如：將敏感欄位進行加密
                return this.encryptSensitiveFields(data);
            }
            return data;
        } catch (error) {
            console.error('Request encryption failed:', error);
            return data;
        }
    }

    /**
     * 解密回應資料
     */
    private decryptResponse(data: any): any {
        try {
            // 如果回應是base64編碼的JSON
            if (typeof data === 'string' && this.isBase64(data)) {
                return this.encryptService.decodeBase64ToJson(data);
            }
            return data;
        } catch (error) {
            console.error('Response decryption failed:', error);
            return data;
        }
    }

    /**
     * 加密敏感欄位
     */
    private encryptSensitiveFields(data: any): any {
        const sensitiveFields = ['password', 'idNumber', 'phoneNumber', 'accountNumber'];
        const encrypted = { ...data };
        
        sensitiveFields.forEach(field => {
            if (encrypted[field]) {
                // 這裡需要實際的公鑰來加密
                // encrypted[field] = this.encryptService.encrypt(publicKey, encrypted[field]);
            }
        });
        
        return encrypted;
    }

    /**
     * 判斷是否需要加密請求
     */
    private shouldEncryptRequest(request: HttpRequest<any>): boolean {
        // 只對POST, PUT, PATCH請求進行加密
        const encryptMethods = ['POST', 'PUT', 'PATCH'];
        return encryptMethods.includes(request.method.toUpperCase());
    }

    /**
     * 判斷是否為Base64字串
     */
    private isBase64(str: string): boolean {
        try {
            return btoa(atob(str)) === str;
        } catch (err) {
            return false;
        }
    }

    /**
     * 錯誤處理
     */
    private handleError(error: HttpErrorResponse, requestId: string): Observable<never> {
        switch (error.status) {
            case 401:
                this.handleUnauthorized();
                break;
            case 403:
                this.handleForbidden();
                break;
            case 500:
                this.handleServerError(error);
                break;
            case 0:
                this.handleNetworkError();
                break;
            default:
                this.handleGenericError(error);
        }
        
        return throwError(() => error);
    }

    /**
     * 處理未授權錯誤
     */
    private handleUnauthorized(): void {
        console.warn('Unauthorized access - redirecting to login');
        // TODO: clearToken方法暫不存在，IBR模組中暫時使用localStorage清除
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
        this.router.navigate(['/login']);
    }

    /**
     * 處理禁止存取錯誤
     */
    private handleForbidden(): void {
        console.warn('Access forbidden');
        // 可以顯示權限不足的訊息
    }

    /**
     * 處理伺服器錯誤
     */
    private handleServerError(error: HttpErrorResponse): void {
        console.error('Server error:', error);
        // 可以顯示伺服器錯誤訊息
    }

    /**
     * 處理網路錯誤
     */
    private handleNetworkError(): void {
        console.error('Network error - check connection');
        // 可以顯示網路連接錯誤訊息
    }

    /**
     * 處理一般錯誤
     */
    private handleGenericError(error: HttpErrorResponse): void {
        console.error('HTTP error:', error);
    }

    /**
     * 判斷是否應該重試
     */
    private shouldRetry(
        error: HttpErrorResponse, 
        retryCount: number, 
        config: IBRInterceptorConfig
    ): boolean {
        // 不重試的狀態碼
        const nonRetryableStatuses = [400, 401, 403, 404, 422];
        
        return retryCount < config.maxRetryAttempts && 
               !nonRetryableStatuses.includes(error.status);
    }

    /**
     * 記錄請求
     */
    private logRequest(request: HttpRequest<any>, requestId: string): void {
        const logData: IBRApiRequest = {
            url: request.url,
            method: request.method,
            headers: this.sanitizeHeaders(request.headers.keys().reduce((acc, key) => {
                acc[key] = request.headers.get(key) || '';
                return acc;
            }, {} as Record<string, string>)),
            body: this.sanitizeRequestBody(request.body),
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss.SSS'),
            requestId
        };
        
        console.group(`🚀 IBR API Request [${requestId}]`);
        console.log('URL:', logData.url);
        console.log('Method:', logData.method);
        console.log('Headers:', logData.headers);
        console.log('Body:', logData.body);
        console.log('Timestamp:', logData.timestamp);
        console.groupEnd();
    }

    /**
     * 記錄回應
     */
    private logResponse(response: HttpResponse<any>, requestId: string, responseTime: number): void {
        const logData: IBRApiResponse = {
            status: response.status,
            statusText: response.statusText,
            body: this.sanitizeResponseBody(response.body),
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss.SSS'),
            responseTime,
            requestId
        };
        
        console.group(`✅ IBR API Response [${requestId}]`);
        console.log('Status:', `${logData.status} ${logData.statusText}`);
        console.log('Response Time:', `${responseTime}ms`);
        console.log('Body:', logData.body);
        console.log('Timestamp:', logData.timestamp);
        console.groupEnd();
    }

    /**
     * 記錄錯誤
     */
    private logError(error: HttpErrorResponse, requestId: string, responseTime: number): void {
        const logData: IBRApiError = {
            error,
            requestId,
            retryCount: 0,
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss.SSS')
        };
        
        console.group(`❌ IBR API Error [${requestId}]`);
        console.error('Status:', `${error.status} ${error.statusText}`);
        console.error('URL:', error.url);
        console.error('Response Time:', `${responseTime}ms`);
        console.error('Error:', error.error);
        console.error('Timestamp:', logData.timestamp);
        console.groupEnd();
    }

    /**
     * 記錄重試嘗試
     */
    private logRetryAttempt(requestId: string, retryCount: number, error: HttpErrorResponse): void {
        console.warn(`🔄 IBR API Retry [${requestId}] - Attempt ${retryCount}`, {
            status: error.status,
            url: error.url,
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss.SSS')
        });
    }

    /**
     * 清理敏感的headers資訊
     */
    private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
        const sensitiveHeaders = ['authorization', 'x-api-key'];
        const sanitized = { ...headers };
        
        sensitiveHeaders.forEach(header => {
            if (sanitized[header]) {
                sanitized[header] = '***MASKED***';
            }
        });
        
        return sanitized;
    }

    /**
     * 清理敏感的請求資料
     */
    private sanitizeRequestBody(body: any): any {
        if (!body) return body;
        
        const sensitiveFields = ['password', 'idNumber', 'phoneNumber', 'accountNumber'];
        const sanitized = JSON.parse(JSON.stringify(body));
        
        const maskSensitiveData = (obj: any): any => {
            if (typeof obj !== 'object' || obj === null) return obj;
            
            Object.keys(obj).forEach(key => {
                if (sensitiveFields.some(field => 
                    key.toLowerCase().includes(field.toLowerCase()))) {
                    obj[key] = '***MASKED***';
                } else if (typeof obj[key] === 'object') {
                    obj[key] = maskSensitiveData(obj[key]);
                }
            });
            
            return obj;
        };
        
        return maskSensitiveData(sanitized);
    }

    /**
     * 清理敏感的回應資料
     */
    private sanitizeResponseBody(body: any): any {
        // 對於回應，通常不需要遮罩，但可以限制記錄的大小
        if (!body) return body;
        
        const bodyStr = JSON.stringify(body);
        if (bodyStr.length > 1000) {
            return `${bodyStr.substring(0, 1000)}... [TRUNCATED]`;
        }
        
        return body;
    }

    /**
     * 生成請求ID
     */
    private generateRequestId(): string {
        return `IBR-${Date.now()}-${++this.requestIdCounter}`;
    }

    /**
     * 檢查是否有活躍的請求
     */
    private hasActiveRequests(): boolean {
        return this.activeRequests.size > 0;
    }
}