import {
    HttpClient,
    HttpErrorResponse,
    HttpEvent,
    HttpHandler,
    HttpInterceptor, HttpParams,
    HttpRequest,
    HttpResponse
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { GlobalDataService } from '../service/global.service';
import moment from 'moment';
import {SERVER_URL} from "../app.constants";

@Injectable({
    providedIn: 'root'
})
export class KgiAuthInterceptor implements HttpInterceptor {

    constructor(public router: Router,
                protected gd: GlobalDataService,
                protected http: HttpClient
    ) {
    }

    intercept(
        req: HttpRequest<any>,
        next: HttpHandler
    ): Observable<HttpEvent<any>> {

        const headerTime = moment(new Date()).format('YYYY/MM/DD HH:mm:ss:SSS');
        const token = this.gd.token;


        const clonedRequest = req.clone({
            headers: req.headers.set('KGI', 'KGI')
        });

        if (token) {
            req = clonedRequest.clone({
                setHeaders: {
                    Authorization: 'Bearer' + token,
                    ApiTime: headerTime
                },
                body: req.body
            });
        }else{
            // 沒有token 也要補時間上去 以利 nginx 追蹤
            req = req.clone({
                setHeaders: {
                    ApiTime: headerTime
                },
                body: req.body
            });
        }

        return next.handle(req).pipe(tap((event) => {
        }, (err) => {
            if (err instanceof HttpErrorResponse && err.status === 401) {
                console.log(err);
            }
        }));
    }
}
