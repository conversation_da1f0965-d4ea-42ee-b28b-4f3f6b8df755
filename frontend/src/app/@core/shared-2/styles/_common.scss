// IBR 共用樣式變數與混合器

// 顏色變數
$primary-color: #0044ad;
$secondary-color: #1565c0;
$success-color: #28a745;
$warning-color: #ffc107;
$danger-color: #dc3545;
$info-color: #17a2b8;

// 灰階
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// 間距
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-xxl: 3rem;

// 圓角
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;

// 陰影
$box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);

// 字型大小
$font-size-xs: 0.75rem;
$font-size-sm: 0.875rem;
$font-size-base: 1rem;
$font-size-lg: 1.125rem;
$font-size-xl: 1.25rem;
$font-size-xxl: 1.5rem;

// 斷點
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;

// 混合器 - 按鈕樣式
@mixin button-base {
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: all 0.3s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: white;
  
  &:hover:not(:disabled) {
    background-color: darken($primary-color, 10%);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: white;
  color: $primary-color;
  border: 2px solid $primary-color;
  
  &:hover:not(:disabled) {
    background-color: $gray-100;
  }
}

// 混合器 - 卡片樣式
@mixin card {
  background-color: white;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $box-shadow-sm;
}

// 混合器 - 表單元素
@mixin form-control {
  width: 100%;
  padding: $spacing-md;
  border: 2px solid $gray-300;
  border-radius: $border-radius-md;
  font-size: $font-size-base;
  transition: border-color 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
  }
  
  &:disabled {
    background-color: $gray-100;
    cursor: not-allowed;
  }
}

// 混合器 - 響應式
@mixin mobile {
  @media (max-width: $breakpoint-md) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// 通用類別
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: $spacing-sm; }
.mt-2 { margin-top: $spacing-md; }
.mt-3 { margin-top: $spacing-lg; }
.mt-4 { margin-top: $spacing-xl; }

.mb-1 { margin-bottom: $spacing-sm; }
.mb-2 { margin-bottom: $spacing-md; }
.mb-3 { margin-bottom: $spacing-lg; }
.mb-4 { margin-bottom: $spacing-xl; }

.text-primary { color: $primary-color; }
.text-success { color: $success-color; }
.text-warning { color: $warning-color; }
.text-danger { color: $danger-color; }
.text-muted { color: $gray-600; }