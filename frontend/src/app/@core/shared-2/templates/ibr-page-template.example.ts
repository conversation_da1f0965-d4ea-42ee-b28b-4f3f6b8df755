import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { IbrStateService, ApplicationStatus } from '../services/ibr-state.service';

/**
 * IBR 頁面標準模板
 * 
 * 使用說明：
 * 1. 複製此模板創建新的 IBR 頁面
 * 2. 更新 selector、class name 和相關方法
 * 3. 確保所有頁面都遵循統一的設計規範
 * 
 * 設計規範：
 * - 使用 <app-ibr-header> 統一 header
 * - 主要內容區域採用 2/3 寬度響應式設計
 * - 統一的客服功能處理
 */
@Component({
  selector: 'app-example-page',
  template: `
    <div class="ibr-page-container">
      <!-- IBR 統一 Header (自動 2/3 寬度) -->
      <app-ibr-header (customerServiceClick)="openCustomerService()"></app-ibr-header>

      <!-- 主要內容區域 (2/3 寬度) -->
      <main class="main-content-wrapper">
        <div class="content-container">
          
          <!-- 進度指示器 (可選) -->
          <div class="progress-section" *ngIf="showProgress">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="progressPercent"></div>
            </div>
            <div class="step-info">
              <span class="step-current">步驟 {{ currentStep }}</span>
              <span class="step-total">/ {{ totalSteps }}</span>
            </div>
          </div>

          <!-- 頁面標題 -->
          <div class="page-title-section">
            <h1 class="page-title">{{ pageTitle }}</h1>
            <p class="page-subtitle" *ngIf="pageSubtitle">{{ pageSubtitle }}</p>
          </div>

          <!-- 頁面主要內容 -->
          <div class="page-content">
            <!-- 在這裡添加頁面特定內容 -->
            <div class="content-placeholder">
              <p>頁面內容區域</p>
            </div>
          </div>

          <!-- 操作按鈕區域 -->
          <div class="action-section">
            <button 
              class="btn-secondary" 
              *ngIf="showBackButton"
              (click)="goBack()"
            >
              上一步
            </button>
            <button 
              class="btn-primary" 
              [disabled]="!canProceed"
              (click)="proceedToNext()"
            >
              {{ nextButtonText }}
            </button>
          </div>

          <!-- 測試資訊 (開發時使用) -->
          <div class="test-info" *ngIf="showTestInfo">
            <h4>🧪 {{ moduleInfo }}</h4>
            <p>當前狀態: {{ currentStatus }}</p>
            <p>頁面: {{ pageDescription }}</p>
          </div>
          
        </div>
      </main>
    </div>
  `,
  styles: [`
    /* === 頁面容器設計 === */
    .ibr-page-container {
      min-height: 100vh;
      background: #ffffff;
      display: flex;
      flex-direction: column;
    }

    /* === 主要內容區域 (2/3 寬度) === */
    .main-content-wrapper {
      flex: 1;
      width: 100%;
      padding: 20px;
      display: flex;
      justify-content: center;
      background: #f8f9fa;
    }

    .content-container {
      width: 100%;
      max-width: 400px;
      display: flex;
      flex-direction: column;
      gap: 24px;
      background: #ffffff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    /* === 進度指示器 === */
    .progress-section {
      margin-bottom: 20px;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e2e2e2;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .progress-fill {
      height: 100%;
      background: #0044ad;
      transition: width 0.3s ease;
    }

    .step-info {
      display: flex;
      align-items: center;
      gap: 4px;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 14px;
      color: #666;
    }

    .step-current {
      font-weight: 600;
      color: #0044ad;
    }

    /* === 頁面標題 === */
    .page-title-section {
      text-align: center;
      margin-bottom: 20px;
    }

    .page-title {
      color: #041c43;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 24px;
      line-height: 150%;
      font-weight: 400;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #041c43;
      font-family: "Montserrat", sans-serif;
      font-size: 16px;
      line-height: 140%;
      font-weight: 600;
      margin: 0;
    }

    /* === 頁面內容 === */
    .page-content {
      flex: 1;
      margin: 20px 0;
    }

    .content-placeholder {
      text-align: center;
      padding: 40px 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 2px dashed #dee2e6;
    }

    /* === 操作按鈕 === */
    .action-section {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      padding: 20px 0;
    }

    .btn-primary, .btn-secondary {
      flex: 1;
      height: 48px;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;
    }

    .btn-primary {
      background: #0044ad;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #003399;
    }

    .btn-primary:disabled {
      background: #e2e2e2;
      color: #a6a6a6;
      cursor: not-allowed;
    }

    .btn-secondary {
      background: white;
      color: #0044ad;
      border: 2px solid #0044ad;
    }

    .btn-secondary:hover {
      background: #0044ad;
      color: white;
    }

    /* === 測試資訊 === */
    .test-info {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border: 1px solid #dee2e6;
      margin-top: 20px;
    }

    .test-info h4 {
      margin: 0 0 10px 0;
      color: #0044ad;
    }

    .test-info p {
      margin: 5px 0;
      font-size: 0.9em;
      color: #666;
    }

    /* === 響應式設計 === */
    
    /* 平板和小螢幕桌機 */
    @media (min-width: 768px) {
      .content-container {
        max-width: 500px;
        padding: 32px;
      }
      
      .page-title {
        font-size: 26px;
      }
    }

    /* 桌機版本 - 2/3 寬度 */
    @media (min-width: 1024px) {
      .main-content-wrapper {
        padding: 40px 20px;
      }
      
      .content-container {
        width: 66.666%;
        min-width: 600px;
        max-width: 800px;
        padding: 40px;
      }
      
      .page-title {
        font-size: 28px;
      }
    }

    /* 大螢幕桌機 */
    @media (min-width: 1440px) {
      .content-container {
        max-width: 900px;
        padding: 48px;
      }
      
      .main-content-wrapper {
        padding: 60px 40px;
      }
    }

    /* 手機版調整 */
    @media (max-width: 767px) {
      .main-content-wrapper {
        padding: 15px;
      }
      
      .content-container {
        max-width: 100%;
        gap: 20px;
        padding: 20px;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
      }
      
      .page-title {
        font-size: 22px;
      }
      
      .page-subtitle {
        font-size: 15px;
      }
      
      .action-section {
        flex-direction: column;
      }
      
      .btn-primary, .btn-secondary {
        width: 100%;
      }
    }
  `]
})
export class ExamplePageComponent implements OnInit {

  // 頁面基本資訊
  pageTitle = '頁面標題';
  pageSubtitle = 'Page Subtitle';
  moduleInfo = 'Module Name - 第X頁';
  pageDescription = '頁面描述 (X個頁面中的第X個)';
  
  // 進度相關
  showProgress = true;
  currentStep = 1;
  totalSteps = 3;
  progressPercent = 33;
  
  // 按鈕控制
  showBackButton = true;
  nextButtonText = '下一步';
  canProceed = false;
  
  // 狀態管理
  currentStatus = 'NOT_STARTED';
  
  // 開發模式
  showTestInfo = true; // 生產環境設為 false

  constructor(
    private router: Router,
    private stateService: IbrStateService
  ) {}

  ngOnInit(): void {
    // 初始化頁面狀態
    this.initializePage();
  }

  /**
   * 初始化頁面
   */
  private initializePage(): void {
    // 更新應用程式狀態
    this.stateService.updateApplicationStatus({
      status: ApplicationStatus.PROCESSING, // 根據頁面調整
      stepTitle: this.pageTitle,
      currentStep: this.currentStep
    });
  }

  /**
   * 開啟客服服務
   */
  openCustomerService(): void {
    alert('客服服務\n\n（模擬功能）\n\n如需協助請撥打客服專線：\n0800-588-111');
  }

  /**
   * 返回上一頁
   */
  goBack(): void {
    // 根據業務邏輯調整導航
    this.router.navigate(['/ibr/test']);
  }

  /**
   * 進行到下一步
   */
  proceedToNext(): void {
    if (!this.canProceed) {
      alert('請完成必要欄位填寫');
      return;
    }

    // 更新狀態並導航
    this.stateService.moveToNextStep('下一頁標題', ApplicationStatus.PROCESSING);
    
    // 模擬導航
    alert(`${this.pageTitle} 完成！\n\n下一步：下一頁\n（頁面尚未建立，將返回測試首頁）`);
    this.router.navigate(['/ibr/test']);
  }
}