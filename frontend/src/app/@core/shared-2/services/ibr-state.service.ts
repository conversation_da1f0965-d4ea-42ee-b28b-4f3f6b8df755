import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * 申請狀態枚舉
 */
export enum ApplicationStatus {
  NOT_STARTED = 'not_started',           // 未開始
  TERMS_AGREEING = 'terms_agreeing',     // 條款同意中
  IDENTITY_SELECTING = 'identity_selecting', // 選擇驗證方式中
  IDENTITY_VERIFYING = 'identity_verifying', // 身份驗證中
  IDENTITY_VERIFIED = 'identity_verified', // 身份驗證完成
  ID_VERIFYING = 'id_verifying',         // 身分證驗證中
  PHONE_VERIFYING = 'phone_verifying',   // 手機驗證中
  FIDO_REGISTERING = 'fido_registering', // FIDO 註冊中
  OTP_SENDING = 'otp_sending',           // OTP 發送中
  OTP_VERIFYING = 'otp_verifying',       // OTP 驗證中
  PERSONAL_INFO = 'personal_info',       // 個人資料填寫中
  REMITTANCE_INFO = 'remittance_info',   // 匯款資料填寫中
  REMITTANCE_CONFIRMING = 'remittance_confirming', // 匯款確認中
  BANK_INFO = 'bank_info',               // 銀行資料填寫中
  CARD_CONNECTING = 'card_connecting',   // 讀卡機連接中
  CERT_VERIFYING = 'cert_verifying',     // 憑證驗證中
  CERTIFICATE_VERIFYING = 'certificate_verifying', // 工商憑證驗證中
  CERTIFICATE_VERIFIED = 'certificate_verified', // 工商憑證驗證完成
  FORM_FILLING = 'form_filling',         // 表單填寫中
  AMOUNT_CONFIRMING = 'amount_confirming', // 金額確認中
  FINAL_CONFIRMING = 'final_confirming', // 最終確認中
  TRANSACTION_CONFIRMING = 'transaction_confirming', // 交易確認中
  APPLICATION_SUBMITTED = 'application_submitted', // 申請已提交
  PROCESSING = 'processing',             // 處理中
  COMPLETED = 'completed',               // 已完成
  FAILED = 'failed',                     // 失敗
  SUPPLEMENT_NEEDED = 'supplement_needed', // 需要補件
  SUPPLEMENT_REQUIRED = 'supplement_required', // 補件通知
  SUPPLEMENT_SUBMITTED = 'supplement_submitted' // 補件已提交
}

/**
 * 用戶類型
 */
export type UserType = 'individual' | 'corporate';

/**
 * 申請狀態資料
 */
export interface ApplicationState {
  id?: string;                          // 申請編號
  userType: UserType;                   // 用戶類型
  status: ApplicationStatus;            // 當前狀態
  currentStep: number;                  // 當前步驟
  totalSteps: number;                   // 總步驟數
  stepTitle: string;                    // 步驟標題
  progress: number;                     // 進度百分比
  startTime?: Date;                     // 開始時間
  lastUpdateTime?: Date;                // 最後更新時間
  completedTime?: Date;                 // 完成時間
  errorMessage?: string;                // 錯誤訊息
  data?: any;                          // 申請資料
}

/**
 * IBR 狀態管理服務
 * 
 * 管理整個申請流程的狀態，提供狀態追蹤和進度管理
 * 支援個人和企業兩種不同的申請流程
 */
@Injectable({
  providedIn: 'root'
})
export class IbrStateService {

  // 狀態主題
  private applicationStateSubject!: BehaviorSubject<ApplicationState>;
  public applicationState$!: Observable<ApplicationState>;

  // 載入狀態
  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  // 錯誤狀態
  private errorSubject = new BehaviorSubject<string | null>(null);
  public error$ = this.errorSubject.asObservable();

  constructor() {
    // 初始化狀態主題
    this.applicationStateSubject = new BehaviorSubject<ApplicationState>(this.getInitialState());
    (this as any).applicationState$ = this.applicationStateSubject.asObservable();
    
    this.initializeFromStorage();
  }

  /**
   * 取得初始狀態
   */
  private getInitialState(): ApplicationState {
    return {
      userType: 'individual',
      status: ApplicationStatus.NOT_STARTED,
      currentStep: 0,
      totalSteps: 3,
      stepTitle: '準備開始',
      progress: 0,
      startTime: new Date()
    };
  }

  /**
   * 從本地儲存初始化狀態
   */
  private initializeFromStorage(): void {
    try {
      const savedState = sessionStorage.getItem('ibr_application_state');
      if (savedState) {
        const state = JSON.parse(savedState);
        // 還原日期物件
        if (state.startTime) state.startTime = new Date(state.startTime);
        if (state.lastUpdateTime) state.lastUpdateTime = new Date(state.lastUpdateTime);
        if (state.completedTime) state.completedTime = new Date(state.completedTime);
        
        this.applicationStateSubject.next(state);
      }
    } catch (error) {
      console.warn('無法從本地儲存載入狀態:', error);
    }
  }

  /**
   * 儲存狀態到本地儲存
   */
  private saveToStorage(state: ApplicationState): void {
    try {
      sessionStorage.setItem('ibr_application_state', JSON.stringify(state));
    } catch (error) {
      console.warn('無法儲存狀態到本地儲存:', error);
    }
  }

  /**
   * 開始新申請
   * @param userType 用戶類型
   */
  startNewApplication(userType: UserType): void {
    const totalSteps = userType === 'individual' ? 3 : 3; // 兩種類型都是3大步驟
    
    const newState: ApplicationState = {
      id: this.generateApplicationId(),
      userType: userType,
      status: ApplicationStatus.TERMS_AGREEING,
      currentStep: 1,
      totalSteps: totalSteps,
      stepTitle: userType === 'individual' ? '條款同意' : '企業條款同意',
      progress: Math.round((1 / totalSteps) * 100),
      startTime: new Date(),
      lastUpdateTime: new Date(),
      data: {}
    };

    this.updateState(newState);
  }

  /**
   * 更新申請狀態
   * @param updates 狀態更新
   */
  updateApplicationStatus(updates: Partial<ApplicationState> & Record<string, any>): void {
    const currentState = this.applicationStateSubject.value;
    const newState = {
      ...currentState,
      ...updates,
      lastUpdateTime: new Date()
    };

    // 自動計算進度
    if (updates.currentStep !== undefined) {
      newState.progress = Math.round((updates.currentStep / newState.totalSteps) * 100);
    }

    // 如果有額外的資料，儲存到 data 欄位
    if (updates.verificationMethod) {
      newState.data = {
        ...newState.data,
        verificationMethod: updates.verificationMethod
      };
    }

    this.updateState(newState);
  }

  /**
   * 前進到下一步
   * @param stepTitle 步驟標題
   * @param status 新狀態
   */
  moveToNextStep(stepTitle: string, status?: ApplicationStatus): void {
    const currentState = this.applicationStateSubject.value;
    const nextStep = currentState.currentStep + 1;
    
    this.updateApplicationStatus({
      currentStep: nextStep,
      stepTitle: stepTitle,
      status: status || currentState.status,
      progress: Math.min(Math.round((nextStep / currentState.totalSteps) * 100), 100)
    });
  }

  /**
   * 返回上一步
   * @param stepTitle 步驟標題
   * @param status 新狀態
   */
  moveToPreviousStep(stepTitle: string, status?: ApplicationStatus): void {
    const currentState = this.applicationStateSubject.value;
    const previousStep = Math.max(currentState.currentStep - 1, 1);
    
    this.updateApplicationStatus({
      currentStep: previousStep,
      stepTitle: stepTitle,
      status: status || currentState.status,
      progress: Math.round((previousStep / currentState.totalSteps) * 100)
    });
  }

  /**
   * 完成申請
   * @param completionData 完成資料
   */
  completeApplication(completionData?: any): void {
    this.updateApplicationStatus({
      status: ApplicationStatus.COMPLETED,
      currentStep: this.applicationStateSubject.value.totalSteps,
      stepTitle: '申請完成',
      progress: 100,
      completedTime: new Date(),
      data: { ...this.applicationStateSubject.value.data, ...completionData }
    });
  }

  /**
   * 設定申請失敗
   * @param errorMessage 錯誤訊息
   */
  setApplicationFailed(errorMessage: string): void {
    this.updateApplicationStatus({
      status: ApplicationStatus.FAILED,
      errorMessage: errorMessage
    });
    this.setError(errorMessage);
  }

  /**
   * 設定需要補件
   */
  setSupplementNeeded(): void {
    this.updateApplicationStatus({
      status: ApplicationStatus.SUPPLEMENT_NEEDED,
      stepTitle: '需要補件'
    });
  }

  /**
   * 更新申請資料
   * @param data 資料
   */
  updateApplicationData(data: any): void {
    const currentState = this.applicationStateSubject.value;
    this.updateApplicationStatus({
      data: { ...currentState.data, ...data }
    });
  }

  /**
   * 設定載入狀態
   * @param loading 是否載入中
   */
  setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  /**
   * 設定錯誤
   * @param error 錯誤訊息
   */
  setError(error: string | null): void {
    this.errorSubject.next(error);
  }

  /**
   * 清除錯誤
   */
  clearError(): void {
    this.errorSubject.next(null);
  }

  /**
   * 重設申請狀態
   */
  resetApplication(): void {
    const initialState = this.getInitialState();
    this.updateState(initialState);
    this.clearError();
    sessionStorage.removeItem('ibr_application_state');
  }

  /**
   * 取得當前狀態
   */
  getCurrentState(): ApplicationState {
    return this.applicationStateSubject.value;
  }

  /**
   * 取得申請資料
   */
  getApplicationData(): any {
    return this.applicationStateSubject.value.data || {};
  }

  /**
   * 檢查是否可以進行下一步
   */
  canProceedToNext(): boolean {
    const state = this.applicationStateSubject.value;
    return state.status !== ApplicationStatus.FAILED && 
           state.status !== ApplicationStatus.PROCESSING &&
           state.currentStep < state.totalSteps;
  }

  /**
   * 檢查是否可以返回上一步
   */
  canGoBack(): boolean {
    const state = this.applicationStateSubject.value;
    return state.currentStep > 1 && 
           state.status !== ApplicationStatus.PROCESSING &&
           state.status !== ApplicationStatus.COMPLETED;
  }

  /**
   * 產生申請編號
   */
  private generateApplicationId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `IBR${timestamp}${random.toUpperCase()}`;
  }

  /**
   * 更新狀態
   */
  private updateState(state: ApplicationState): void {
    this.applicationStateSubject.next(state);
    this.saveToStorage(state);
  }

  /**
   * 取得步驟資訊
   * @param userType 用戶類型
   * @returns 步驟資訊陣列
   */
  getStepInfo(userType: UserType): {step: number, title: string, description: string}[] {
    if (userType === 'individual') {
      return [
        { step: 1, title: '身份驗證', description: '確認身份與同意條款' },
        { step: 2, title: '確認金額', description: '填寫資料與確認金額' },
        { step: 3, title: '完成申請', description: '確認交易並完成申請' }
      ];
    } else {
      return [
        { step: 1, title: '企業驗證', description: '企業資料與工商憑證' },
        { step: 2, title: '確認金額', description: '填寫資料與確認金額' },
        { step: 3, title: '完成申請', description: '確認交易並完成申請' }
      ];
    }
  }
}