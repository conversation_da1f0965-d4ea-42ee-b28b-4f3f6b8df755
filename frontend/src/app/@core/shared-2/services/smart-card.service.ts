import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject, throwError, interval } from 'rxjs';
import { map, catchError, takeWhile, switchMap } from 'rxjs/operators';

/**
 * 讀卡機資訊
 */
export interface CardReaderInfo {
  id: string;
  name: string;
  manufacturer: string;
  model: string;
  status: 'available' | 'busy' | 'error' | 'disconnected';
  isDefault: boolean;
  supportedCards: string[];
}

/**
 * 智慧卡資訊
 */
export interface SmartCardInfo {
  atr: string;           // Answer to Reset
  type: 'unknown' | 'business_certificate' | 'health_card' | 'citizen_card';
  inserted: boolean;
  readerName: string;
}

/**
 * 工商憑證資訊
 */
export interface BusinessCertificate {
  companyName: string;          // 企業名稱
  businessId: string;           // 統一編號
  serialNumber: string;         // 憑證序號
  issuer: string;              // 發行機構
  subject: string;             // 憑證主體
  validFrom: Date;             // 有效期開始
  validTo: Date;               // 有效期結束
  keyUsage: string[];          // 金鑰用途
  isValid: boolean;            // 是否有效
  publicKey?: string;          // 公開金鑰
}

/**
 * 驅動程式資訊
 */
export interface DriverInfo {
  name: string;
  version: string;
  platform: 'windows' | 'mac' | 'linux';
  downloadUrl: string;
  fileSize: string;
  checksum: string;
  isInstalled: boolean;
  isLatest: boolean;
}

/**
 * PC/SC 智慧卡服務
 * 
 * 提供讀卡機連接、工商憑證讀取等功能
 * 支援多種讀卡機和智慧卡類型
 */
@Injectable({
  providedIn: 'root'
})
export class SmartCardService {

  // 讀卡機狀態
  private readersSubject = new BehaviorSubject<CardReaderInfo[]>([]);
  public readers$ = this.readersSubject.asObservable();

  // 智慧卡狀態
  private cardStatusSubject = new BehaviorSubject<SmartCardInfo | null>(null);
  public cardStatus$ = this.cardStatusSubject.asObservable();

  // 連接狀態
  private connectionStatusSubject = new BehaviorSubject<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  // 模擬的 PC/SC 接口 (實際使用時需要原生插件)
  private pcscInterface: any = null;
  private selectedReader: CardReaderInfo | null = null;
  private monitoringEnabled = false;

  constructor() {
    this.initializePcsc();
  }

  /**
   * 初始化 PC/SC 系統
   */
  private async initializePcsc(): Promise<void> {
    try {
      // 檢查是否支援 PC/SC
      if (this.isPcscSupported()) {
        this.connectionStatusSubject.next('connecting');
        
        // 模擬 PC/SC 初始化
        await this.simulatePcscInit();
        
        this.connectionStatusSubject.next('connected');
        
        // 開始監控讀卡機
        this.startReaderMonitoring();
      } else {
        throw new Error('系統不支援 PC/SC 或未安裝驅動程式');
      }
    } catch (error) {
      console.error('PC/SC 初始化失敗:', error);
      this.connectionStatusSubject.next('error');
    }
  }

  /**
   * 檢查是否支援 PC/SC
   */
  private isPcscSupported(): boolean {
    // 實際實作需要檢查原生插件或 PC/SC 驅動
    // 這裡模擬檢查邏輯
    const userAgent = navigator.userAgent;
    
    // Windows 平台檢查
    if (userAgent.includes('Windows')) {
      // 檢查是否有 PC/SC 服務
      return this.checkWindowsPcsc();
    }
    
    // macOS 平台檢查
    if (userAgent.includes('Mac')) {
      // macOS 內建 PC/SC 支援
      return true;
    }
    
    // Linux 平台檢查
    if (userAgent.includes('Linux')) {
      // 檢查是否安裝 pcscd
      return this.checkLinuxPcsc();
    }
    
    return false;
  }

  /**
   * 模擬 PC/SC 初始化
   */
  private async simulatePcscInit(): Promise<void> {
    return new Promise((resolve) => {
      // 模擬初始化延遲
      setTimeout(() => {
        this.pcscInterface = {
          // 模擬的 PC/SC 接口
          connected: true,
          version: '1.0.0'
        };
        resolve();
      }, 2000);
    });
  }

  /**
   * 檢查 Windows PC/SC 服務
   */
  private checkWindowsPcsc(): boolean {
    // 實際實作需要檢查 SCard* API
    // 這裡返回模擬結果
    return true;
  }

  /**
   * 檢查 Linux PC/SC 服務
   */
  private checkLinuxPcsc(): boolean {
    // 實際實作需要檢查 pcscd 服務
    // 這裡返回模擬結果
    return true;
  }

  /**
   * 掃描可用讀卡機
   */
  async scanReaders(): Promise<CardReaderInfo[]> {
    try {
      this.connectionStatusSubject.next('connecting');
      
      // 模擬掃描讀卡機
      const readers = await this.simulateReaderScan();
      
      this.readersSubject.next(readers);
      this.connectionStatusSubject.next('connected');
      
      return readers;
    } catch (error) {
      console.error('掃描讀卡機失敗:', error);
      this.connectionStatusSubject.next('error');
      throw error;
    }
  }

  /**
   * 模擬讀卡機掃描
   */
  private async simulateReaderScan(): Promise<CardReaderInfo[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockReaders: CardReaderInfo[] = [
          {
            id: 'reader_001',
            name: 'ACS ACR122U USB NFC Reader',
            manufacturer: 'Advanced Card Systems Ltd.',
            model: 'ACR122U',
            status: 'available',
            isDefault: true,
            supportedCards: ['business_certificate', 'citizen_card']
          },
          {
            id: 'reader_002', 
            name: 'Gemalto PC Twin Reader',
            manufacturer: 'Gemalto',
            model: 'PC Twin Reader',
            status: 'available',
            isDefault: false,
            supportedCards: ['business_certificate', 'health_card']
          }
        ];
        resolve(mockReaders);
      }, 1500);
    });
  }

  /**
   * 連接指定讀卡機
   */
  async connectToReader(readerId: string): Promise<boolean> {
    try {
      const readers = this.readersSubject.value;
      const reader = readers.find(r => r.id === readerId);
      
      if (!reader) {
        throw new Error('找不到指定的讀卡機');
      }

      this.connectionStatusSubject.next('connecting');
      
      // 模擬連接過程
      await this.simulateReaderConnection(reader);
      
      this.selectedReader = reader;
      this.connectionStatusSubject.next('connected');
      
      // 開始監控卡片狀態
      this.startCardMonitoring();
      
      return true;
    } catch (error) {
      console.error('連接讀卡機失敗:', error);
      this.connectionStatusSubject.next('error');
      throw error;
    }
  }

  /**
   * 模擬讀卡機連接
   */
  private async simulateReaderConnection(reader: CardReaderInfo): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        reader.status = 'busy';
        resolve();
      }, 1000);
    });
  }

  /**
   * 開始監控讀卡機
   */
  private startReaderMonitoring(): void {
    if (this.monitoringEnabled) return;
    
    this.monitoringEnabled = true;
    
    // 每5秒檢查讀卡機狀態
    interval(5000).pipe(
      takeWhile(() => this.monitoringEnabled),
      switchMap(() => this.scanReaders())
    ).subscribe({
      next: (readers) => {
        // 更新讀卡機狀態
      },
      error: (error) => {
        console.error('讀卡機監控錯誤:', error);
      }
    });
  }

  /**
   * 開始監控卡片狀態
   */
  private startCardMonitoring(): void {
    if (!this.selectedReader) return;

    // 每1秒檢查卡片狀態
    interval(1000).pipe(
      takeWhile(() => this.selectedReader !== null),
      map(() => this.checkCardStatus())
    ).subscribe({
      next: (cardInfo) => {
        this.cardStatusSubject.next(cardInfo);
      },
      error: (error) => {
        console.error('卡片監控錯誤:', error);
      }
    });
  }

  /**
   * 檢查卡片狀態
   */
  private checkCardStatus(): SmartCardInfo | null {
    if (!this.selectedReader) return null;

    // 模擬卡片檢測
    const isCardPresent = Math.random() > 0.7; // 30% 機率有卡片
    
    if (isCardPresent) {
      return {
        atr: '3B8F8001804F0CA000000306030001000000006A',
        type: 'business_certificate',
        inserted: true,
        readerName: this.selectedReader.name
      };
    }
    
    return null;
  }

  /**
   * 讀取工商憑證
   */
  async readBusinessCertificate(pin: string): Promise<BusinessCertificate> {
    try {
      const cardInfo = this.cardStatusSubject.value;
      
      if (!cardInfo || !cardInfo.inserted) {
        throw new Error('請插入工商憑證卡');
      }

      if (cardInfo.type !== 'business_certificate') {
        throw new Error('插入的卡片不是工商憑證');
      }

      // 驗證 PIN 碼
      if (!this.validatePin(pin)) {
        throw new Error('PIN 碼錯誤');
      }

      // 模擬讀取憑證
      const certificate = await this.simulateCertificateRead();
      
      return certificate;
    } catch (error) {
      console.error('讀取工商憑證失敗:', error);
      throw error;
    }
  }

  /**
   * 驗證 PIN 碼
   */
  private validatePin(pin: string): boolean {
    // 實際實作需要與智慧卡通訊驗證
    // 這裡模擬驗證邏輯
    return pin.length >= 4 && pin.length <= 12;
  }

  /**
   * 模擬憑證讀取
   */
  private async simulateCertificateRead(): Promise<BusinessCertificate> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockCertificate: BusinessCertificate = {
          companyName: 'OOXX科技股份有限公司',
          businessId: '12345678',
          serialNumber: 'C123456789',
          issuer: 'MOICA',
          subject: 'CN=OOXX科技股份有限公司,O=MOICA,C=TW',
          validFrom: new Date('2023-01-01'),
          validTo: new Date('2025-12-31'),
          keyUsage: ['digitalSignature', 'keyEncipherment'],
          isValid: true,
          publicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...'
        };
        resolve(mockCertificate);
      }, 2000);
    });
  }

  /**
   * 取得可用驅動程式
   */
  async getAvailableDrivers(): Promise<DriverInfo[]> {
    const platform = this.detectPlatform();
    
    const drivers: DriverInfo[] = [
      {
        name: 'PC/SC Smart Card Driver',
        version: '6.1.7600.16385',
        platform: 'windows',
        downloadUrl: 'https://example.com/drivers/pcsc-windows.msi',
        fileSize: '2.5 MB',
        checksum: 'sha256:abc123...',
        isInstalled: platform === 'windows',
        isLatest: true
      },
      {
        name: 'Smart Card Services',
        version: '10.15.7',
        platform: 'mac',
        downloadUrl: 'https://example.com/drivers/pcsc-mac.pkg',
        fileSize: '1.8 MB',
        checksum: 'sha256:def456...',
        isInstalled: platform === 'mac',
        isLatest: true
      },
      {
        name: 'pcscd',
        version: '1.9.5',
        platform: 'linux',
        downloadUrl: 'https://example.com/drivers/pcscd-linux.deb',
        fileSize: '1.2 MB',
        checksum: 'sha256:ghi789...',
        isInstalled: platform === 'linux',
        isLatest: true
      }
    ];

    return drivers.filter(driver => driver.platform === platform);
  }

  /**
   * 偵測作業系統平台
   */
  private detectPlatform(): 'windows' | 'mac' | 'linux' {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('windows')) return 'windows';
    if (userAgent.includes('mac')) return 'mac';
    if (userAgent.includes('linux')) return 'linux';
    
    return 'windows'; // 預設
  }

  /**
   * 中斷連接
   */
  disconnect(): void {
    this.monitoringEnabled = false;
    this.selectedReader = null;
    this.pcscInterface = null;
    this.connectionStatusSubject.next('disconnected');
    this.cardStatusSubject.next(null);
    this.readersSubject.next([]);
  }

  /**
   * 重設連接
   */
  async reset(): Promise<void> {
    this.disconnect();
    await this.initializePcsc();
  }

  /**
   * 取得系統相容性資訊
   */
  getSystemCompatibility(): {
    platform: string;
    supported: boolean;
    requirements: string[];
    recommendations: string[];
  } {
    const platform = this.detectPlatform();
    
    const compatibility = {
      windows: {
        platform: 'Windows',
        supported: true,
        requirements: [
          'Windows 7 或更新版本',
          'PC/SC Smart Card Resource Manager 服務',
          'USB 連接埠'
        ],
        recommendations: [
          '建議使用 Windows 10 或更新版本',
          '安裝最新的讀卡機驅動程式',
          '確保 Smart Card 服務正常運行'
        ]
      },
      mac: {
        platform: 'macOS',
        supported: true,
        requirements: [
          'macOS 10.12 或更新版本',
          '內建 Smart Card Services',
          'USB 連接埠'
        ],
        recommendations: [
          '建議使用 macOS 11 或更新版本',
          '允許瀏覽器存取 USB 裝置',
          '安裝 CryptoTokenKit 框架'
        ]
      },
      linux: {
        platform: 'Linux',
        supported: true,
        requirements: [
          'Ubuntu 18.04 或相容發行版',
          'pcscd 套件',
          'libpcsclite 函式庫'
        ],
        recommendations: [
          '安裝 pcsc-tools 進行偵錯',
          '確保使用者有 scard 群組權限',
          '安裝對應的讀卡機驅動'
        ]
      }
    };

    return compatibility[platform] || compatibility.windows;
  }
}