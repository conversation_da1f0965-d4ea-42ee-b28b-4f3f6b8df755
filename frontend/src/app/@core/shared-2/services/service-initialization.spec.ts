/**
 * 服務初始化測試
 * 測試修復後的服務是否能正常初始化
 */

import { TestBed } from '@angular/core/testing';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

import { IbrStateService } from './ibr-state.service';
import { OtpService } from '../../modules/individual/services/otp.service';
import { IndividualService } from '../../modules/individual/services/individual.service';

describe('服務初始化測試', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        IbrStateService,
        OtpService,
        IndividualService
      ]
    });
  });

  it('IbrStateService 應該能正常初始化', () => {
    expect(() => {
      const service = TestBed.inject(IbrStateService);
      expect(service).toBeTruthy();
      
      // 驗證狀態主題已正確初始化
      const currentState = service.getCurrentState();
      expect(currentState).toBeDefined();
      expect(currentState.userType).toBe('individual');
    }).not.toThrow();
  });

  it('OtpService 應該能正常初始化', () => {
    expect(() => {
      const service = TestBed.inject(OtpService);
      expect(service).toBeTruthy();
      
      // 驗證會話狀態已正確初始化
      const sessionState = service.getCurrentSessionState();
      expect(sessionState).toBeDefined();
      expect(sessionState.hasActiveSession).toBe(false);
      expect(sessionState.remainingAttempts).toBe(3);
    }).not.toThrow();
  });

  it('IndividualService 應該能正常初始化', () => {
    expect(() => {
      const service = TestBed.inject(IndividualService);
      expect(service).toBeTruthy();
      
      // 驗證應用狀態已正確初始化
      const currentState = service.getCurrentState();
      expect(currentState).toBeDefined();
      expect(currentState.termsAgreed).toBe(false);
    }).not.toThrow();
  });

  it('服務之間的依賴注入應該正常工作', () => {
    expect(() => {
      const individualService = TestBed.inject(IndividualService);
      const otpService = TestBed.inject(OtpService);
      const ibrStateService = TestBed.inject(IbrStateService);

      // 所有服務都應該正常初始化
      expect(individualService).toBeTruthy();
      expect(otpService).toBeTruthy();
      expect(ibrStateService).toBeTruthy();

      // 驗證服務可以正常訂閱
      individualService.applicationState$.subscribe(state => {
        expect(state).toBeDefined();
      });

      otpService.sessionState$.subscribe(state => {
        expect(state).toBeDefined();
      });

      ibrStateService.applicationState$.subscribe(state => {
        expect(state).toBeDefined();
      });
    }).not.toThrow();
  });

  it('不應該出現 defaultMaxAttempts 未定義錯誤', () => {
    expect(() => {
      const otpService = TestBed.inject(OtpService);
      
      // 這個操作之前會觸發 defaultMaxAttempts 錯誤
      const status = otpService.getOtpStatus();
      expect(status).toBeDefined();
      expect(status.remainingAttempts).toBe(3);
    }).not.toThrow();
  });

  it('所有 Observable 都應該有初始值', (done) => {
    const individualService = TestBed.inject(IndividualService);
    const otpService = TestBed.inject(OtpService);
    const ibrStateService = TestBed.inject(IbrStateService);

    let completedCount = 0;
    const totalChecks = 3;

    const checkComplete = () => {
      completedCount++;
      if (completedCount === totalChecks) {
        done();
      }
    };

    // 每個 Observable 都應該立即發出初始值
    individualService.applicationState$.subscribe(state => {
      expect(state).toBeDefined();
      checkComplete();
    });

    otpService.sessionState$.subscribe(state => {
      expect(state).toBeDefined();
      checkComplete();
    });

    ibrStateService.applicationState$.subscribe(state => {
      expect(state).toBeDefined();
      checkComplete();
    });
  });
});