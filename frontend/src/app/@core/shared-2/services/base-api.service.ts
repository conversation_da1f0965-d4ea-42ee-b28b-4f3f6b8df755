import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { catchError, map, timeout, retry } from 'rxjs/operators';
import { ApiConfigService } from './api-config.service';
import { environment } from '../../../../environments/environment';

/**
 * 基礎 API 服務
 * 所有 API 服務都應該繼承此類
 * 提供統一的 mock/real 切換機制
 */
export abstract class BaseApiService {
  
  protected readonly timeout = environment.ibrConfig?.requestTimeout || 30000;
  protected readonly retryCount = environment.ibrConfig?.retryCount || 2;
  
  constructor(
    protected http: HttpClient,
    protected apiConfig: ApiConfigService
  ) {}
  
  /**
   * 發送 GET 請求
   */
  protected get<T>(module: string, endpoint: string, params?: any): Observable<T> {
    if (this.apiConfig.isMockMode) {
      return this.handleMockRequest<T>(module, endpoint, params);
    }
    
    const url = this.apiConfig.getApiUrl(module, endpoint);
    if (!url) {
      return throwError(() => new Error(`Invalid API endpoint: ${module}/${endpoint}`));
    }
    
    // 自動加入客戶類型參數（如果是 individual 或 corporate 模組）
    const requestParams = this.addCustomerTypeToParams(module, params);
    
    return this.http.get<T>(url, { params: requestParams })
      .pipe(
        timeout(this.timeout),
        retry(this.retryCount),
        catchError(this.handleError),
        map(response => this.processResponse(response))
      );
  }
  
  /**
   * 發送 POST 請求
   */
  protected post<T>(module: string, endpoint: string, body: any, options?: any): Observable<T> {
    if (this.apiConfig.isMockMode) {
      return this.handleMockRequest<T>(module, endpoint, body);
    }
    
    const url = this.apiConfig.getApiUrl(module, endpoint);
    if (!url) {
      return throwError(() => new Error(`Invalid API endpoint: ${module}/${endpoint}`));
    }
    
    // 自動加入客戶類型參數（如果是 individual 或 corporate 模組）
    const requestBody = this.addCustomerTypeIfNeeded(module, body);
    
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      ...options?.headers
    });
    
    return this.http.post<T>(url, requestBody, { headers, ...options })
      .pipe(
        timeout(this.timeout),
        retry(this.retryCount),
        catchError(this.handleError),
        map(response => this.processResponse(response))
      );
  }
  
  /**
   * 發送 PUT 請求
   */
  protected put<T>(module: string, endpoint: string, body: any, options?: any): Observable<T> {
    if (this.apiConfig.isMockMode) {
      return this.handleMockRequest<T>(module, endpoint, body);
    }
    
    const url = this.apiConfig.getApiUrl(module, endpoint);
    if (!url) {
      return throwError(() => new Error(`Invalid API endpoint: ${module}/${endpoint}`));
    }
    
    return this.http.put<T>(url, body, options)
      .pipe(
        timeout(this.timeout),
        retry(this.retryCount),
        catchError(this.handleError),
        map(response => this.processResponse(response))
      );
  }
  
  /**
   * 發送 DELETE 請求
   */
  protected delete<T>(module: string, endpoint: string, options?: any): Observable<T> {
    if (this.apiConfig.isMockMode) {
      return this.handleMockRequest<T>(module, endpoint, null);
    }
    
    const url = this.apiConfig.getApiUrl(module, endpoint);
    if (!url) {
      return throwError(() => new Error(`Invalid API endpoint: ${module}/${endpoint}`));
    }
    
    return this.http.delete<T>(url, options)
      .pipe(
        timeout(this.timeout),
        retry(this.retryCount),
        catchError(this.handleError),
        map(response => this.processResponse(response))
      );
  }
  
  /**
   * 處理 Mock 請求
   */
  private handleMockRequest<T>(module: string, endpoint: string, params?: any): Observable<T> {
    const mockDataPromise = this.apiConfig.getMockData(module, endpoint, params);
    
    if (mockDataPromise instanceof Promise) {
      return new Observable<T>(subscriber => {
        mockDataPromise
          .then(data => {
            console.log(`[Mock Response] ${module}/${endpoint}:`, data);
            subscriber.next(data as T);
            subscriber.complete();
          })
          .catch(error => {
            subscriber.error(error);
          });
      });
    }
    
    return of(mockDataPromise as T);
  }
  
  /**
   * 處理錯誤
   */
  protected handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // 客戶端錯誤
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // 伺服器端錯誤
      errorMessage = `Server Error: ${error.status} - ${error.message}`;
      
      // 處理特定的錯誤狀態碼
      switch (error.status) {
        case 400:
          errorMessage = '請求參數錯誤';
          break;
        case 401:
          errorMessage = '未授權，請重新登入';
          break;
        case 403:
          errorMessage = '無權限訪問此資源';
          break;
        case 404:
          errorMessage = '請求的資源不存在';
          break;
        case 500:
          errorMessage = '伺服器內部錯誤';
          break;
        case 503:
          errorMessage = '服務暫時無法使用';
          break;
      }
    }
    
    console.error('API Error:', errorMessage, error);
    return throwError(() => new Error(errorMessage));
  }
  
  /**
   * 處理回應（可在子類中覆寫）
   */
  protected processResponse(response: any): any {
    // 預設直接返回回應
    return response;
  }
  
  /**
   * 檢查是否為 Mock 模式
   */
  get isMockMode(): boolean {
    return this.apiConfig.isMockMode;
  }
  
  /**
   * 自動加入客戶類型參數 (POST/PUT 請求)
   * 後端統一 Controller 需要透過參數判斷客戶類型
   */
  private addCustomerTypeIfNeeded(module: string, body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }
    
    // 複製 body 以避免修改原始物件
    const requestBody = { ...body };
    
    // 如果是 individual 或 corporate 模組，且沒有 customerType，則自動加入
    if (module === 'individual' && !requestBody.customerType) {
      requestBody.customerType = 'INDIVIDUAL';
    } else if (module === 'corporate' && !requestBody.customerType) {
      requestBody.customerType = 'CORPORATE';
    }
    
    return requestBody;
  }
  
  /**
   * 自動加入客戶類型參數 (GET 請求)
   */
  private addCustomerTypeToParams(module: string, params?: any): any {
    // 確保 params 是物件
    const requestParams = params || {};
    
    // 如果是 individual 或 corporate 模組，且沒有 customerType，則自動加入
    if (module === 'individual' && !requestParams.customerType) {
      requestParams.customerType = 'INDIVIDUAL';
    } else if (module === 'corporate' && !requestParams.customerType) {
      requestParams.customerType = 'CORPORATE';
    }
    
    return requestParams;
  }
}