import { TestBed } from '@angular/core/testing';
import { IbrStateService, ApplicationStatus, UserType } from './ibr-state.service';

describe('IbrStateService', () => {
  let service: IbrStateService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(IbrStateService);
    
    // 清理本地儲存
    sessionStorage.clear();
  });

  afterEach(() => {
    sessionStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('初始狀態', () => {
    it('應該有正確的初始狀態', () => {
      const initialState = service.getCurrentState();
      
      expect(initialState.userType).toBe('individual');
      expect(initialState.status).toBe(ApplicationStatus.NOT_STARTED);
      expect(initialState.currentStep).toBe(0);
      expect(initialState.totalSteps).toBe(3);
      expect(initialState.progress).toBe(0);
    });

    it('應該訂閱狀態變化', (done) => {
      service.applicationState$.subscribe(state => {
        expect(state).toBeDefined();
        expect(state.userType).toBe('individual');
        done();
      });
    });
  });

  describe('開始新申請', () => {
    it('應該正確初始化個人申請', () => {
      service.startNewApplication('individual');
      const state = service.getCurrentState();
      
      expect(state.userType).toBe('individual');
      expect(state.status).toBe(ApplicationStatus.TERMS_AGREEING);
      expect(state.currentStep).toBe(1);
      expect(state.totalSteps).toBe(3);
      expect(state.stepTitle).toBe('條款同意');
      expect(state.progress).toBeCloseTo(33.33, 1);
      expect(state.startTime).toBeDefined();
    });

    it('應該正確初始化企業申請', () => {
      service.startNewApplication('corporate');
      const state = service.getCurrentState();
      
      expect(state.userType).toBe('corporate');
      expect(state.status).toBe(ApplicationStatus.TERMS_AGREEING);
      expect(state.stepTitle).toBe('企業條款同意');
    });

    it('應該生成唯一的申請ID', () => {
      service.startNewApplication('individual');
      const id1 = service.getCurrentState().id;
      
      service.startNewApplication('individual');
      const id2 = service.getCurrentState().id;
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
    });
  });

  describe('狀態更新', () => {
    beforeEach(() => {
      service.startNewApplication('individual');
    });

    it('應該正確更新申請狀態', () => {
      const updateData = {
        status: ApplicationStatus.IDENTITY_VERIFYING,
        stepTitle: '身份驗證中'
      };
      
      service.updateApplicationStatus(updateData);
      const state = service.getCurrentState();
      
      expect(state.status).toBe(ApplicationStatus.IDENTITY_VERIFYING);
      expect(state.stepTitle).toBe('身份驗證中');
      expect(state.lastUpdateTime).toBeDefined();
    });

    it('應該正確更新申請資料', () => {
      const testData = {
        personalInfo: {
          name: '測試用戶',
          phone: '0912345678'
        },
        remittanceInfo: {
          amount: 10000,
          currency: 'USD'
        }
      };
      
      service.updateApplicationData(testData);
      const state = service.getCurrentState();
      
      expect(state.data).toBeDefined();
      expect(state.data.personalInfo.name).toBe('測試用戶');
      expect(state.data.remittanceInfo.amount).toBe(10000);
    });

    it('應該合併現有資料', () => {
      // 第一次更新
      service.updateApplicationData({ 
        personalInfo: { name: '測試用戶' }
      });
      
      // 第二次更新
      service.updateApplicationData({ 
        remittanceInfo: { amount: 10000 }
      });
      
      const state = service.getCurrentState();
      expect(state.data.personalInfo.name).toBe('測試用戶');
      expect(state.data.remittanceInfo.amount).toBe(10000);
    });
  });

  describe('步驟導航', () => {
    beforeEach(() => {
      service.startNewApplication('individual');
    });

    it('應該正確前進到下一步', () => {
      service.moveToNextStep('身份驗證', ApplicationStatus.IDENTITY_VERIFYING);
      const state = service.getCurrentState();
      
      expect(state.currentStep).toBe(2);
      expect(state.stepTitle).toBe('身份驗證');
      expect(state.status).toBe(ApplicationStatus.IDENTITY_VERIFYING);
      expect(state.progress).toBeCloseTo(66.67, 1);
    });

    it('應該正確返回上一步', () => {
      // 先前進到步驟2
      service.moveToNextStep('身份驗證');
      
      // 再返回步驟1
      service.moveToPreviousStep('條款同意', ApplicationStatus.TERMS_AGREEING);
      const state = service.getCurrentState();
      
      expect(state.currentStep).toBe(1);
      expect(state.stepTitle).toBe('條款同意');
      expect(state.status).toBe(ApplicationStatus.TERMS_AGREEING);
    });

    it('不應該返回到步驟0以下', () => {
      service.moveToPreviousStep('條款同意');
      const state = service.getCurrentState();
      
      expect(state.currentStep).toBe(1); // 最小值是1
    });
  });

  describe('申請完成', () => {
    beforeEach(() => {
      service.startNewApplication('individual');
    });

    it('應該正確完成申請', () => {
      const completionData = {
        applicationNumber: 'IBR20241201001',
        submissionTime: new Date()
      };
      
      service.completeApplication(completionData);
      const state = service.getCurrentState();
      
      expect(state.status).toBe(ApplicationStatus.COMPLETED);
      expect(state.stepTitle).toBe('申請完成');
      expect(state.progress).toBe(100);
      expect(state.completedTime).toBeDefined();
      expect(state.data.applicationNumber).toBe('IBR20241201001');
    });
  });

  describe('載入狀態管理', () => {
    it('應該正確設定載入狀態', (done) => {
      service.loading$.subscribe(loading => {
        if (loading) {
          expect(loading).toBe(true);
          done();
        }
      });
      
      service.setLoading(true);
    });

    it('應該正確清除載入狀態', () => {
      service.setLoading(true);
      service.setLoading(false);
      
      service.loading$.subscribe(loading => {
        expect(loading).toBe(false);
      });
    });
  });

  describe('本地儲存', () => {
    it('應該將狀態儲存到sessionStorage', () => {
      service.startNewApplication('individual');
      
      const storedData = sessionStorage.getItem('ibr_application_state');
      expect(storedData).toBeTruthy();
      
      const parsedData = JSON.parse(storedData!);
      expect(parsedData.userType).toBe('individual');
    });

    it('應該從sessionStorage載入狀態', () => {
      const testState = {
        id: 'test-123',
        userType: 'corporate' as UserType,
        status: ApplicationStatus.PROCESSING,
        currentStep: 2,
        totalSteps: 3,
        stepTitle: '測試步驟',
        progress: 67,
        data: { test: 'data' }
      };
      
      sessionStorage.setItem('ibr_application_state', JSON.stringify(testState));
      
      // 重新建立服務實例來測試載入
      const newService = TestBed.inject(IbrStateService);
      const loadedState = newService.getCurrentState();
      
      expect(loadedState.id).toBe('test-123');
      expect(loadedState.userType).toBe('corporate');
      expect(loadedState.status).toBe(ApplicationStatus.PROCESSING);
    });
  });

  describe('重設功能', () => {
    it('應該正確重設申請狀態', () => {
      // 先建立一些狀態
      service.startNewApplication('corporate');
      service.updateApplicationData({ test: 'data' });
      service.moveToNextStep('測試步驟');
      
      // 重設
      service.resetApplication();
      const state = service.getCurrentState();
      
      expect(state.userType).toBe('individual');
      expect(state.status).toBe(ApplicationStatus.NOT_STARTED);
      expect(state.currentStep).toBe(0);
      expect(state.progress).toBe(0);
      expect(state.data).toBeUndefined();
    });

    it('重設後應該清除本地儲存', () => {
      service.startNewApplication('individual');
      service.resetApplication();
      
      const storedData = sessionStorage.getItem('ibr_application_state');
      expect(storedData).toBeNull();
    });
  });

  describe('錯誤處理', () => {
    it('應該正確設定錯誤訊息', () => {
      service.setError('測試錯誤訊息');
      const state = service.getCurrentState();
      
      expect(state.errorMessage).toBe('測試錯誤訊息');
      expect(state.status).toBe(ApplicationStatus.FAILED);
    });

    it('應該正確清除錯誤訊息', () => {
      service.setError('測試錯誤');
      service.clearError();
      const state = service.getCurrentState();
      
      expect(state.errorMessage).toBeUndefined();
    });

    it('本地儲存錯誤時應該優雅處理', () => {
      // 模擬sessionStorage錯誤
      const originalSetItem = sessionStorage.setItem;
      sessionStorage.setItem = jasmine.createSpy().and.throwError('Storage error');
      
      expect(() => {
        service.startNewApplication('individual');
      }).not.toThrow();
      
      // 恢復原始方法
      sessionStorage.setItem = originalSetItem;
    });
  });
});