import { Injectable } from '@angular/core';

/**
 * 費用結構介面
 */
export interface FeeStructure {
  remittanceFee: number;        // 匯款手續費
  releaseFee: number;           // 解款手續費
  additionalFee?: number;       // 額外費用 (法人)
  totalFees: number;            // 總費用
}

/**
 * 金額明細介面
 */
export interface AmountBreakdown {
  originalAmount: number;       // 原始匯款金額
  fees: FeeStructure;           // 費用結構
  finalAmount: number;          // 最終入帳金額
  currency: string;             // 幣別
}

/**
 * IBR 金額計算服務
 * 
 * 提供匯款手續費、解款手續費等各種費用計算
 * 支援個人和企業不同的費用結構
 */
@Injectable({
  providedIn: 'root'
})
export class IbrCalculationService {

  // 費用常數設定
  private readonly INDIVIDUAL_REMITTANCE_FEE = 30;     // 個人匯款手續費
  private readonly CORPORATE_REMITTANCE_FEE = 50;      // 企業匯款手續費
  private readonly INDIVIDUAL_RELEASE_FEE = 200;       // 個人解款手續費
  private readonly CORPORATE_RELEASE_FEE = 300;        // 企業解款手續費
  private readonly CORPORATE_ADDITIONAL_FEE = 100;     // 企業額外費用

  // 額度限制
  private readonly INDIVIDUAL_LIMIT = 500000;          // 個人限額 50萬
  private readonly CORPORATE_LIMIT = 5000000;          // 企業限額 500萬

  /**
   * 計算個人解款費用
   * @param amount 匯款金額
   * @returns 完整金額明細
   */
  calculateIndividualFees(amount: number): AmountBreakdown {
    const fees: FeeStructure = {
      remittanceFee: this.INDIVIDUAL_REMITTANCE_FEE,
      releaseFee: this.INDIVIDUAL_RELEASE_FEE,
      totalFees: this.INDIVIDUAL_REMITTANCE_FEE + this.INDIVIDUAL_RELEASE_FEE
    };

    return {
      originalAmount: amount,
      fees: fees,
      finalAmount: amount - fees.totalFees,
      currency: 'NTD'
    };
  }

  /**
   * 計算企業解款費用
   * @param amount 匯款金額
   * @returns 完整金額明細
   */
  calculateCorporateFees(amount: number): AmountBreakdown {
    const fees: FeeStructure = {
      remittanceFee: this.CORPORATE_REMITTANCE_FEE,
      releaseFee: this.CORPORATE_RELEASE_FEE,
      additionalFee: this.CORPORATE_ADDITIONAL_FEE,
      totalFees: this.CORPORATE_REMITTANCE_FEE + this.CORPORATE_RELEASE_FEE + this.CORPORATE_ADDITIONAL_FEE
    };

    return {
      originalAmount: amount,
      fees: fees,
      finalAmount: amount - fees.totalFees,
      currency: 'NTD'
    };
  }

  /**
   * 計算動態手續費 (根據金額區間)
   * @param amount 匯款金額
   * @param userType 用戶類型
   * @returns 手續費金額
   */
  calculateDynamicFee(amount: number, userType: 'individual' | 'corporate'): number {
    if (userType === 'individual') {
      if (amount <= 10000) return 100;
      if (amount <= 50000) return 150;
      if (amount <= 100000) return 200;
      if (amount <= 300000) return 250;
      return 300;
    } else {
      if (amount <= 50000) return 200;
      if (amount <= 200000) return 300;
      if (amount <= 500000) return 400;
      if (amount <= 1000000) return 500;
      return 600;
    }
  }

  /**
   * 檢查金額是否超過限額
   * @param amount 匯款金額
   * @param userType 用戶類型
   * @returns 是否超過限額
   */
  isAmountExceedsLimit(amount: number, userType: 'individual' | 'corporate'): boolean {
    const limit = userType === 'individual' ? this.INDIVIDUAL_LIMIT : this.CORPORATE_LIMIT;
    return amount > limit;
  }

  /**
   * 取得額度限制
   * @param userType 用戶類型
   * @returns 額度限制
   */
  getAmountLimit(userType: 'individual' | 'corporate'): number {
    return userType === 'individual' ? this.INDIVIDUAL_LIMIT : this.CORPORATE_LIMIT;
  }

  /**
   * 格式化金額顯示
   * @param amount 金額
   * @param currency 幣別
   * @returns 格式化後的金額字串
   */
  formatAmount(amount: number, currency = 'NTD'): string {
    return `${currency} ${amount.toLocaleString('zh-TW')}`;
  }

  /**
   * 計算匯率轉換 (預留功能)
   * @param amount 金額
   * @param fromCurrency 源幣別
   * @param toCurrency 目標幣別
   * @returns 轉換後金額
   */
  convertCurrency(amount: number, fromCurrency: string, toCurrency: string): number {
    // 目前僅支援 NTD，未來可擴展
    if (fromCurrency === toCurrency) {
      return amount;
    }
    
    // 預留匯率轉換邏輯
    const exchangeRates: Record<string, number> = {
      'USD_NTD': 31.5,
      'EUR_NTD': 34.2,
      'JPY_NTD': 0.21
    };

    const rateKey = `${fromCurrency}_${toCurrency}`;
    const rate = exchangeRates[rateKey] || 1;
    
    return Math.round(amount * rate * 100) / 100;
  }

  /**
   * 計算優惠折扣 (預留功能)
   * @param originalFee 原始費用
   * @param discountType 優惠類型
   * @returns 折扣後費用
   */
  applyDiscount(originalFee: number, discountType?: string): number {
    if (!discountType) {
      return originalFee;
    }

    const discounts: Record<string, number> = {
      'VIP': 0.8,        // VIP 8折
      'FIRST_TIME': 0.5, // 首次使用 5折
      'BULK': 0.9        // 大額優惠 9折
    };

    const discount = discounts[discountType] || 1;
    return Math.round(originalFee * discount);
  }

  /**
   * 驗證金額有效性
   * @param amount 金額
   * @returns 驗證結果
   */
  validateAmount(amount: number): { valid: boolean; message?: string } {
    if (amount === null || amount === undefined) {
      return { valid: false, message: '請輸入金額' };
    }

    if (amount <= 0) {
      return { valid: false, message: '金額必須大於 0' };
    }

    if (amount < 100) {
      return { valid: false, message: '最小匯款金額為 NTD 100' };
    }

    return { valid: true };
  }

  /**
   * 計算預計到帳時間
   * @param userType 用戶類型
   * @param amount 金額
   * @returns 預計到帳時間 (小時)
   */
  calculateEstimatedArrivalTime(userType: 'individual' | 'corporate', amount: number): number {
    // 個人用戶通常較快
    if (userType === 'individual') {
      return amount > 100000 ? 2 : 1; // 大額需要 2 小時，一般 1 小時
    } else {
      return amount > 500000 ? 4 : 2; // 企業大額需要 4 小時，一般 2 小時
    }
  }
}