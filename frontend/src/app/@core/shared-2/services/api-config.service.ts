import { Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';

/**
 * API 配置服務
 * 管理 mock/real API 切換機制
 */
@Injectable({
  providedIn: 'root'
})
export class ApiConfigService {
  
  private readonly USE_MOCK_DATA = environment.ibrConfig?.enableMockMode || false;
  private readonly API_BASE_URL = environment.apiUrl || 'http://localhost:8080';
  
  /**
   * API 路徑對應表
   * 將前端路徑映射到後端統一路徑
   * 
   * 設計理念：
   * - 後端使用統一的 Controller 處理自然人和法人
   * - 透過參數（如 PayeeID）自動判斷客戶類型
   * - 自然人和法人只差在工商憑證驗證部分
   * - 查詢功能也統一處理，透過參數區分
   */
  private readonly API_MAPPING = {
    // Individual Module APIs - 映射到統一的 remittance 路徑
    'individual': {
      'terms': '/api/ibr/remittance/terms',
      'termsAgree': '/api/ibr/remittance/terms/agree',
      'verify': '/api/ibr/remittance/data/verify',
      'otpSend': '/api/ibr/remittance/otp/send',
      'otpVerify': '/api/ibr/remittance/otp/verify',
      'remittanceDetail': '/api/ibr/remittance/remittance/detail',
      'remittanceConfirm': '/api/ibr/remittance/remittance/confirm',
      'amountCalculate': '/api/ibr/remittance/amount/calculate',
      'submit': '/api/ibr/remittance/submit',
      'complete': '/api/ibr/remittance/complete',
      'banks': '/api/ibr/remittance/banks',
      'exchangeRate': '/api/ibr/remittance/exchange-rate'
    },
    
    // Corporate Module APIs - 映射到統一的 remittance 路徑
    'corporate': {
      'terms': '/api/ibr/remittance/terms',
      'termsAgree': '/api/ibr/remittance/terms/agree',
      'verify': '/api/ibr/remittance/data/verify',
      'certificateDetect': '/api/ibr/remittance/certificate/detect',
      'certificateRead': '/api/ibr/remittance/certificate/read',
      'certificateVerify': '/api/ibr/remittance/certificate/verify',
      'remittanceDetail': '/api/ibr/remittance/remittance/detail',
      'remittanceConfirm': '/api/ibr/remittance/remittance/confirm',
      'natures': '/api/ibr/remittance/natures',
      'exchangeRate': '/api/ibr/remittance/exchange-rate',
      'amountCalculate': '/api/ibr/remittance/amount/calculate',
      'submit': '/api/ibr/remittance/submit',
      'complete': '/api/ibr/remittance/complete'
    },
    
    // Supplement Module APIs - 統一處理個人和企業補件
    'supplement': {
      'initialize': '/api/ibr/remittance/supplement/initialize',
      'notification': '/api/ibr/remittance/supplement/notification',
      'verify': '/api/ibr/remittance/supplement/verify',
      'correct': '/api/ibr/remittance/supplement/correct',
      'complete': '/api/ibr/remittance/supplement/complete',
      'uploadDocument': '/api/ibr/remittance/supplement/document/upload',
      'deleteDocument': '/api/ibr/remittance/supplement/document/delete'
    },
    
    // Query Module APIs - 統一查詢，透過參數區分客戶類型
    'query': {
      'search': '/api/ibr/query/search',
      'detail': '/api/ibr/query/remittance/detail',
      'history': '/api/ibr/query/remittance/history',
      // 動態路徑的 API 需要特別處理
      'transaction': '/api/ibr/query/transaction', // 使用時需要加上 /{id}
      'status': '/api/ibr/query/status' // 使用時需要加上 /{id}
    },
    
    // Gateway APIs - 跨境平台整合
    'gateway': {
      'inboundNotification': '/api/ibr/gateway/inbound/notification',
      'statusUpdate': '/api/ibr/gateway/status/update'
    }
  };
  
  constructor() {
    console.log('API Configuration Service initialized');
    console.log('Mock mode:', this.USE_MOCK_DATA ? 'ENABLED' : 'DISABLED');
    console.log('API Base URL:', this.API_BASE_URL);
  }
  
  /**
   * 檢查是否使用 Mock 模式
   */
  get isMockMode(): boolean {
    return this.USE_MOCK_DATA;
  }
  
  /**
   * 切換 Mock 模式（僅供開發使用）
   */
  toggleMockMode(): void {
    if (!environment.production) {
      environment.ibrConfig.enableMockMode = !environment.ibrConfig.enableMockMode;
      console.log('Mock mode toggled to:', environment.ibrConfig.enableMockMode);
      // 重新載入頁面以套用變更
      window.location.reload();
    } else {
      console.warn('Cannot toggle mock mode in production environment');
    }
  }
  
  /**
   * 取得 API URL
   * @param module 模組名稱 (individual, corporate, supplement, query)
   * @param endpoint 端點名稱
   * @returns 完整的 API URL 或 null（使用 mock）
   */
  getApiUrl(module: string, endpoint: string): string | null {
    if (this.USE_MOCK_DATA) {
      console.log(`[Mock Mode] Intercepting API call: ${module}/${endpoint}`);
      return null; // 返回 null 表示使用 mock data
    }
    
    const moduleMapping = this.API_MAPPING[module];
    if (!moduleMapping) {
      console.error(`Unknown module: ${module}`);
      return null;
    }
    
    const path = moduleMapping[endpoint];
    if (!path) {
      console.error(`Unknown endpoint: ${endpoint} in module: ${module}`);
      return null;
    }
    
    const fullUrl = `${this.API_BASE_URL}${path}`;
    console.log(`[Real Mode] API URL: ${fullUrl}`);
    return fullUrl;
  }
  
  /**
   * 取得 Mock 資料
   * @param module 模組名稱
   * @param endpoint 端點名稱
   * @param params 參數
   * @returns Mock 資料
   */
  getMockData(module: string, endpoint: string, params?: any): any {
    console.log(`[Mock Mode] Getting mock data for: ${module}/${endpoint}`, params);
    
    // 根據不同的端點返回對應的 mock 資料
    const mockDataMap = {
      'individual': {
        'terms': {
          content: '個人資料保護聲明內容...',
          version: '1.0',
          effectiveDate: '2024-01-01'
        },
        'termsAgree': {
          success: true,
          agreedAt: new Date().toISOString()
        },
        'verify': {
          success: true,
          verified: true,
          customerName: '王小明',
          maskedAccount: '****1234'
        },
        'otpSend': {
          success: true,
          maskedPhone: '0912****678',
          expiresIn: 300
        },
        'otpVerify': {
          success: true,
          token: 'mock-session-token-' + Date.now()
        },
        'remittanceDetail': {
          remittanceNo: 'REM20240101001',
          amount: 100000,
          currency: 'USD',
          payerName: 'JOHN DOE',
          payerBank: 'CHASE BANK',
          purpose: '商品貨款'
        },
        'amountCalculate': {
          originalAmount: 100000,
          exchangeRate: 31.5,
          remittanceFee: 800,
          processingFee: 200,
          totalAmount: 3151000
        },
        'submit': {
          success: true,
          applicationNo: 'APP' + Date.now(),
          submittedAt: new Date().toISOString()
        },
        'complete': {
          applicationNo: 'APP' + Date.now(),
          status: 'COMPLETED',
          completedAt: new Date().toISOString(),
          receiptUrl: '/mock/receipt.pdf'
        }
      },
      'corporate': {
        'terms': {
          content: '企業資料保護聲明內容...',
          version: '1.0',
          effectiveDate: '2024-01-01'
        },
        'verify': {
          success: true,
          verified: true,
          companyName: '測試企業有限公司',
          unifiedNumber: '********',
          maskedAccount: '****5678'
        },
        'certificateDetect': {
          detected: true,
          readers: ['內建讀卡機', 'USB讀卡機']
        },
        'certificateRead': {
          success: true,
          certificateInfo: {
            cn: '測試企業有限公司',
            serialNumber: 'CERT123456',
            validFrom: '2024-01-01',
            validTo: '2025-01-01'
          }
        },
        'certificateVerify': {
          success: true,
          verified: true,
          token: 'mock-cert-token-' + Date.now()
        }
      },
      'supplement': {
        'notification': {
          caseNo: params?.caseNo || 'CASE20240101001',
          supplementType: 'NAME_MISMATCH',
          reason: '匯款人姓名與銀行記錄不符',
          deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          originalData: {
            payeeName: '王小明',
            payeeId: 'A********9'
          }
        },
        'correct': {
          success: true,
          correctionId: 'CORR' + Date.now()
        },
        'complete': {
          success: true,
          supplementNo: 'SUPP' + Date.now(),
          completedAt: new Date().toISOString()
        }
      },
      'query': {
        'search': {
          results: [
            {
              remittanceNo: 'REM20240101001',
              applicationDate: '2024-01-01',
              amount: 100000,
              currency: 'USD',
              status: 'COMPLETED'
            },
            {
              remittanceNo: 'REM20240101002',
              applicationDate: '2024-01-02',
              amount: 50000,
              currency: 'EUR',
              status: 'PROCESSING'
            }
          ],
          totalCount: 2
        }
      }
    };
    
    const moduleData = mockDataMap[module];
    if (!moduleData) {
      console.warn(`No mock data for module: ${module}`);
      return { error: 'No mock data available' };
    }
    
    const endpointData = moduleData[endpoint];
    if (!endpointData) {
      console.warn(`No mock data for endpoint: ${endpoint} in module: ${module}`);
      return { error: 'No mock data available' };
    }
    
    // 模擬延遲以更真實
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(endpointData);
      }, Math.random() * 500 + 200); // 200-700ms 延遲
    });
  }
  
  /**
   * 取得完整配置資訊（用於除錯）
   */
  getConfiguration(): any {
    return {
      mockMode: this.USE_MOCK_DATA,
      apiBaseUrl: this.API_BASE_URL,
      apiMapping: this.API_MAPPING,
      environment: environment.production ? 'production' : 'development'
    };
  }
}