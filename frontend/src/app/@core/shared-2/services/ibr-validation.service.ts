import { Injectable } from '@angular/core';

/**
 * IBR 驗證服務
 * 
 * 提供台灣身分證、統一編號、銀行帳號等驗證功能
 * 支援 IBR 系統所需的各種格式驗證
 */
@Injectable({
  providedIn: 'root'
})
export class IbrValidationService {

  /**
   * 驗證台灣身分證號碼
   * @param idNumber 身分證號碼
   * @returns 是否有效
   */
  validateTaiwanId(idNumber: string): boolean {
    if (!idNumber || idNumber.length !== 10) {
      return false;
    }

    // 身分證格式：第一碼英文字母，後九碼數字
    const regex = /^[A-Z][0-9]{9}$/;
    if (!regex.test(idNumber)) {
      return false;
    }

    // 字母對應數字轉換
    const letterToNumber: Record<string, string> = {
      'A': '10', 'B': '11', 'C': '12', 'D': '13', 'E': '14', 'F': '15',
      'G': '16', 'H': '17', 'I': '34', 'J': '18', 'K': '19', 'L': '20',
      'M': '21', 'N': '22', 'O': '35', 'P': '23', 'Q': '24', 'R': '25',
      'S': '26', 'T': '27', 'U': '28', 'V': '29', 'W': '32', 'X': '30',
      'Y': '31', 'Z': '33'
    };

    const firstLetter = idNumber.charAt(0);
    const letterValue = letterToNumber[firstLetter];
    
    if (!letterValue) {
      return false;
    }

    // 檢查碼驗證
    const digits = letterValue + idNumber.substring(1);
    let sum = 0;
    
    for (let i = 0; i < digits.length - 1; i++) {
      if (i === 0) {
        sum += parseInt(digits.charAt(i)) * 1;
      } else if (i === 1) {
        sum += parseInt(digits.charAt(i)) * 9;
      } else {
        sum += parseInt(digits.charAt(i)) * (10 - i);
      }
    }

    const checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit === parseInt(idNumber.charAt(9));
  }

  /**
   * 驗證台灣統一編號
   * @param businessId 統一編號
   * @returns 是否有效
   */
  validateBusinessId(businessId: string): boolean {
    if (!businessId || businessId.length !== 8) {
      return false;
    }

    // 統一編號格式：8位數字
    const regex = /^[0-9]{8}$/;
    if (!regex.test(businessId)) {
      return false;
    }

    // 檢查碼驗證
    const weights = [1, 2, 1, 2, 1, 2, 4, 1];
    let sum = 0;

    for (let i = 0; i < 8; i++) {
      const digit = parseInt(businessId.charAt(i));
      const product = digit * weights[i];
      
      // 個位數和十位數相加
      sum += Math.floor(product / 10) + (product % 10);
    }

    return sum % 10 === 0;
  }

  /**
   * 驗證銀行代碼
   * @param bankCode 銀行代碼 (3或7位)
   * @returns 是否有效
   */
  validateBankCode(bankCode: string): boolean {
    if (!bankCode) {
      return false;
    }

    // 銀行代碼格式：3位或7位數字
    const regex3 = /^[0-9]{3}$/;
    const regex7 = /^[0-9]{7}$/;
    
    return regex3.test(bankCode) || regex7.test(bankCode);
  }

  /**
   * 驗證銀行帳號
   * @param accountNumber 銀行帳號
   * @returns 是否有效
   */
  validateAccountNumber(accountNumber: string): boolean {
    if (!accountNumber) {
      return false;
    }

    // 銀行帳號格式：10-16位數字
    const regex = /^[0-9]{10,16}$/;
    return regex.test(accountNumber);
  }

  /**
   * 驗證手機號碼
   * @param phoneNumber 手機號碼
   * @returns 是否有效
   */
  validateMobileNumber(phoneNumber: string): boolean {
    if (!phoneNumber) {
      return false;
    }

    // 台灣手機號碼格式：09開頭的10位數字
    const regex = /^09[0-9]{8}$/;
    return regex.test(phoneNumber);
  }

  /**
   * 驗證電子郵件
   * @param email 電子郵件
   * @returns 是否有效
   */
  validateEmail(email: string): boolean {
    if (!email) {
      return false;
    }

    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
  }

  /**
   * 驗證金額
   * @param amount 金額
   * @param maxLimit 最大限額
   * @returns 是否有效
   */
  validateAmount(amount: number, maxLimit = 500000): boolean {
    if (amount === null || amount === undefined) {
      return false;
    }

    return amount > 0 && amount <= maxLimit;
  }

  /**
   * 驗證中文姓名
   * @param name 姓名
   * @returns 是否有效
   */
  validateChineseName(name: string): boolean {
    if (!name) {
      return false;
    }

    // 中文姓名：2-10個中文字符
    const regex = /^[\u4e00-\u9fa5]{2,10}$/;
    return regex.test(name);
  }

  /**
   * 驗證英文姓名
   * @param name 英文姓名
   * @returns 是否有效
   */
  validateEnglishName(name: string): boolean {
    if (!name) {
      return false;
    }

    // 英文姓名：字母、空格、點號，2-50字符
    const regex = /^[a-zA-Z\s.]{2,50}$/;
    return regex.test(name);
  }

  /**
   * 驗證企業中文名稱
   * @param companyName 企業名稱
   * @returns 是否有效
   */
  validateCompanyChineseName(companyName: string): boolean {
    if (!companyName) {
      return false;
    }

    // 企業中文名稱：中文字符、數字、特定符號，2-100字符
    const regex = /^[\u4e00-\u9fa5\u3040-\u309f\u30a0-\u30ff0-9()-\s]{2,100}$/;
    return regex.test(companyName);
  }

  /**
   * 驗證企業英文名稱
   * @param companyName 企業英文名稱
   * @returns 是否有效
   */
  validateCompanyEnglishName(companyName: string): boolean {
    if (!companyName) {
      return false;
    }

    // 企業英文名稱：字母、數字、空格、點號、逗號、括號，2-100字符
    const regex = /^[a-zA-Z0-9\s.,()-]{2,100}$/;
    return regex.test(companyName);
  }
}