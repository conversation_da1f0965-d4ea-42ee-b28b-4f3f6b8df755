import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * 統一測試資料模型
 * 對應 TO API 格式的通知資料
 */
export interface UnifiedTestData {
  TheirRefNo: string;      // 跨境平台編號
  RemitRefNo: string;      // 匯入匯款編號
  PayerName: string;       // 匯款人姓名
  PayerCountry: string;    // 匯款人國家
  Currency: string;        // 幣別
  Amount: string;          // 金額
  PayeeEngName: string;    // 收款人英文姓名/公司英文名稱
  PayeeName: string;       // 收款人中文姓名/公司中文名稱
  PayeeID: string;         // 收款人身分證號/統一編號
  PayeeAccount: string;    // 收款人帳號
  PayeeBankCode: string;   // 收款行代碼
  PayeeTel: string;        // 收款人電話
  PayeeMail: string;       // 收款人信箱
  SourceOfFund: string;    // 匯款性質
  WhileFlag: string;       // 白名單標記
  Memo: string;            // 備註
  SupplementNo?: string;   // IBR補件編號 (當WhileFlag='S'時使用)
}

/**
 * 頁面顯示用的匯款資訊
 */
export interface RemittanceDisplayInfo {
  senderName: string;
  country: string;
  notificationDate: string;
  daysRemaining: number;
  remittanceType: string;
  message: string;
  amount: string;
  currency: string;
  remittanceId: string;
}

/**
 * 身份資料顯示資訊
 */
export interface IdentityDisplayInfo {
  chineseName: string;
  englishName: string;
  idNumber: string;
  birthDate: string;
  bankName: string;
  branchCode: string;
  accountNumber: string;
  mobilePhone: string;
}

/**
 * 法人資料顯示資訊
 */
export interface CorporateDisplayInfo {
  unifiedNumber: string;
  companyName: string;
  companyEnglishName: string;
  bankName: string;
  branchCode: string;
  accountNumber: string;
  contactPhone: string;
  contactEmail: string;
}

/**
 * 統一測試資料服務
 * 管理整個IBR流程（個人/法人）的測試資料一致性
 */
@Injectable({
  providedIn: 'root'
})
export class SharedTestDataService {
  
  private testDataSubject = new BehaviorSubject<UnifiedTestData | null>(null);
  public testData$ = this.testDataSubject.asObservable();
  
  private sessionStorageKey = 'ibr_notification_data';
  
  constructor() {
    // 初始化時嘗試從 sessionStorage 載入資料
    this.loadFromSessionStorage();
  }
  
  /**
   * 從 sessionStorage 載入測試資料
   */
  private loadFromSessionStorage(): void {
    const storedData = sessionStorage.getItem(this.sessionStorageKey);
    if (storedData) {
      try {
        const data = JSON.parse(storedData);
        this.testDataSubject.next(data);
      } catch (error) {
        console.error('載入測試資料失敗:', error);
      }
    }
  }
  
  /**
   * 設定測試資料
   */
  setTestData(data: UnifiedTestData): void {
    this.testDataSubject.next(data);
    sessionStorage.setItem(this.sessionStorageKey, JSON.stringify(data));
  }
  
  /**
   * 取得當前測試資料
   */
  getCurrentTestData(): UnifiedTestData | null {
    return this.testDataSubject.value;
  }
  
  /**
   * 清除測試資料
   */
  clearTestData(): void {
    this.testDataSubject.next(null);
    sessionStorage.removeItem(this.sessionStorageKey);
  }
  
  /**
   * 檢查是否有測試資料
   */
  hasTestData(): boolean {
    return this.testDataSubject.value !== null;
  }
  
  /**
   * 檢查是否為法人資料（統一編號為8碼）
   */
  isCorporateData(data: UnifiedTestData | null): boolean {
    return data !== null && data.PayeeID && data.PayeeID.length === 8;
  }
  
  /**
   * 檢查是否為個人資料（身分證號為10碼）
   */
  isIndividualData(data: UnifiedTestData | null): boolean {
    return data !== null && data.PayeeID && data.PayeeID.length === 10;
  }
  
  /**
   * 轉換為匯款顯示資訊
   */
  toRemittanceDisplayInfo(data: UnifiedTestData): RemittanceDisplayInfo {
    const remittanceTypeMap: { [key: string]: string } = {
      '410': '410 薪資款匯入',
      '001': '001 貨物貿易',
      '002': '002 服務貿易',
      '320': '320 投資收益',
      '510': '510 其他經常移轉',
      '': '待確認匯款性質'
    };
    
    // 法人特有的匯款性質
    const corporateRemittanceTypeMap: { [key: string]: string } = {
      '001': '001 貨物貿易',
      '002': '002 服務貿易',
      '210': '210 國外直接投資',
      '230': '230 其他投資',
      '320': '320 投資收益',
      '510': '510 其他經常移轉'
    };
    
    const isCorporate = this.isCorporateData(data);
    const typeMap = isCorporate ? corporateRemittanceTypeMap : remittanceTypeMap;
    
    return {
      senderName: data.PayerName,
      country: data.PayerCountry,
      notificationDate: new Date().toISOString().split('T')[0].replace(/-/g, '/'),
      daysRemaining: this.calculateDaysRemaining(),
      remittanceType: typeMap[data.SourceOfFund] || '其他匯款',
      message: data.Memo || '',
      amount: data.Amount,
      currency: data.Currency,
      remittanceId: data.RemitRefNo
    };
  }
  
  /**
   * 轉換為身份顯示資訊（個人）
   */
  toIdentityDisplayInfo(data: UnifiedTestData): IdentityDisplayInfo {
    // 根據銀行代碼前3碼取得銀行名稱
    const bankCode = data.PayeeBankCode ? data.PayeeBankCode.substring(0, 3) : '';
    const bankNameMap: { [key: string]: string } = {
      '008': '華南商業銀行',
      '009': '彰化商業銀行',
      '012': '臺灣銀行',
      '013': '國泰世華商業銀行',
      '017': '兆豐國際商業銀行',
      '050': '臺灣企銀',
      '803': '聯邦商業銀行',
      '809': '凱基銀行',
      '812': '台新國際商業銀行',
      '822': '中國信託商業銀行',
      '': '請選擇銀行'
    };
    
    // PayeeBankCode 格式: 銀行代碼(3碼) + 分行代碼(4碼) = 總共7碼
    // 例如: 0080016 = 008(華南銀行) + 0016(分行代碼)
    const branchCode = data.PayeeBankCode.length >= 7 ? data.PayeeBankCode.substring(3, 7) : '';
    
    return {
      chineseName: data.PayeeName,
      englishName: data.PayeeEngName,
      idNumber: data.PayeeID,
      birthDate: '', // 需要用戶輸入
      bankName: bankNameMap[bankCode] || `其他銀行 (${bankCode})`,
      branchCode: branchCode, // 取 PayeeBankCode 的第4-7碼作為分行代碼
      accountNumber: data.PayeeAccount,
      mobilePhone: data.PayeeTel
    };
  }
  
  /**
   * 轉換為法人顯示資訊
   */
  toCorporateDisplayInfo(data: UnifiedTestData): CorporateDisplayInfo {
    // 根據銀行代碼前3碼取得銀行名稱
    const bankCode = data.PayeeBankCode ? data.PayeeBankCode.substring(0, 3) : '';
    const bankNameMap: { [key: string]: string } = {
      '008': '華南商業銀行',
      '009': '彰化商業銀行',
      '012': '臺灣銀行',
      '013': '國泰世華商業銀行',
      '017': '兆豐國際商業銀行',
      '050': '臺灣企銀',
      '803': '聯邦商業銀行',
      '809': '凱基銀行',
      '812': '台新國際商業銀行',
      '822': '中國信託商業銀行',
      '': '請選擇銀行'
    };
    
    const branchCode = data.PayeeBankCode.length >= 7 ? data.PayeeBankCode.substring(3, 7) : '';
    
    return {
      unifiedNumber: data.PayeeID,
      companyName: data.PayeeName,
      companyEnglishName: data.PayeeEngName,
      bankName: bankNameMap[bankCode] || `其他銀行 (${bankCode})`,
      branchCode: branchCode,
      accountNumber: data.PayeeAccount,
      contactPhone: data.PayeeTel,
      contactEmail: data.PayeeMail
    };
  }
  
  /**
   * 計算解款期限剩餘天數
   */
  private calculateDaysRemaining(): number {
    // 模擬解款期限為通知後30天
    const notificationDate = new Date();
    const deadline = new Date(notificationDate);
    deadline.setDate(deadline.getDate() + 30);
    
    const today = new Date();
    const diffTime = deadline.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  }
  
  /**
   * 取得預設測試資料（個人）
   */
  getDefaultIndividualTestData(): UnifiedTestData {
    return {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812345',
      PayerName: 'Shiang Ru',
      PayerCountry: 'US',
      Currency: 'USD',
      Amount: '5000.00',
      PayeeEngName: 'Qing Lan, Wang',
      PayeeName: '王清蘭',
      PayeeID: 'A123456789',
      PayeeAccount: '****************',
      PayeeBankCode: '0080016',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '410',
      WhileFlag: '',
      Memo: 'Monthly Salary'
    };
  }
  
  /**
   * 取得預設測試資料（法人）
   */
  getDefaultCorporateTestData(): UnifiedTestData {
    return {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812346',
      PayerName: 'Wells Fargo Bank, N.A.',
      PayerCountry: 'US',
      Currency: 'USD',
      Amount: '50000.00',
      PayeeEngName: 'Wistron Corporation',
      PayeeName: '緯創資通股份有限公司',
      PayeeID: '********',
      PayeeAccount: '*************',
      PayeeBankCode: '8090015',
      PayeeTel: '02-2720-1234',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '001',
      WhileFlag: '',
      Memo: 'Payment for Invoice #2024-0542'
    };
  }
  
  /**
   * 取得預設測試資料（根據類型）
   */
  getDefaultTestData(type: 'individual' | 'corporate' = 'individual'): UnifiedTestData {
    return type === 'corporate' ? this.getDefaultCorporateTestData() : this.getDefaultIndividualTestData();
  }
  
  /**
   * 取得預設補件測試資料
   * 補件資料的特點是 WhileFlag = 'S' 表示需要補件
   */
  getDefaultSupplementTestData(): UnifiedTestData {
    return {
      TheirRefNo: '***************',
      RemitRefNo: 'RMT2024060812347',
      PayerName: 'MARY JOHNSON',
      PayerCountry: 'UK',
      Currency: 'GBP',
      Amount: '3000.00',
      PayeeEngName: 'CHEN MEI LING',
      PayeeName: '陳美玲',
      PayeeID: 'B234567890',
      PayeeAccount: '****************',
      PayeeBankCode: '0120034',
      PayeeTel: '**********',
      PayeeMail: '<EMAIL>',
      SourceOfFund: '510',
      WhileFlag: 'S', // S 表示需要補件
      Memo: 'Family support',
      SupplementNo: 'IBRSUP2024060800003'
    };
  }
  
  /**
   * 驗證測試資料完整性
   */
  validateTestData(data: UnifiedTestData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data.TheirRefNo) errors.push('缺少跨境平台編號');
    if (!data.RemitRefNo) errors.push('缺少匯入匯款編號');
    if (!data.PayerName) errors.push('缺少匯款人姓名');
    if (!data.PayeeEngName) errors.push('缺少收款人英文姓名');
    if (!data.PayeeName) errors.push('缺少收款人中文姓名');
    if (!data.Currency) errors.push('缺少幣別');
    if (!data.Amount) errors.push('缺少金額');
    
    // 根據 PayeeID 長度判斷是個人或法人
    if (data.PayeeID) {
      if (data.PayeeID.length === 10) {
        // 個人：驗證身分證號格式
        if (!/^[A-Z][12]\d{8}$/.test(data.PayeeID)) {
          errors.push('身分證號格式錯誤');
        }
      } else if (data.PayeeID.length === 8) {
        // 法人：驗證統一編號格式
        if (!/^\d{8}$/.test(data.PayeeID)) {
          errors.push('統一編號格式錯誤');
        }
      } else {
        errors.push('收款人證號長度錯誤（個人10碼/法人8碼）');
      }
    } else {
      errors.push('缺少收款人證號');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * 格式化金額顯示
   */
  formatAmount(amount: string, currency: string): string {
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount)) return amount;
    
    return new Intl.NumberFormat('zh-TW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numAmount);
  }
}