import { Component, OnInit } from '@angular/core';
import { ApiConfigService } from '../../services/api-config.service';
import { environment } from '../../../../../environments/environment';

/**
 * 開發者工具元件
 * 提供 Mock/Real API 切換功能
 * 僅在非生產環境中顯示
 */
@Component({
  selector: 'app-dev-tools',
  template: `
    <div class="dev-tools" *ngIf="!isProduction">
      <button class="dev-tools-toggle" (click)="togglePanel()">
        <span class="icon">🛠️</span>
      </button>
      
      <div class="dev-tools-panel" *ngIf="isPanelOpen">
        <h3>開發者工具</h3>
        
        <div class="dev-tools-section">
          <h4>API 模式</h4>
          <div class="toggle-container">
            <label class="toggle-switch">
              <input 
                type="checkbox" 
                [checked]="isMockMode"
                (change)="toggleMockMode()"
              />
              <span class="slider"></span>
            </label>
            <span class="toggle-label">
              {{ isMockMode ? 'Mock 模式' : 'Real API 模式' }}
            </span>
          </div>
          <p class="info">
            當前模式: <strong>{{ isMockMode ? 'Mock' : 'Real API' }}</strong>
          </p>
        </div>
        
        <div class="dev-tools-section">
          <h4>API 配置</h4>
          <div class="config-info">
            <div class="config-item">
              <span class="label">Base URL:</span>
              <span class="value">{{ apiBaseUrl }}</span>
            </div>
            <div class="config-item">
              <span class="label">Timeout:</span>
              <span class="value">{{ requestTimeout }}ms</span>
            </div>
            <div class="config-item">
              <span class="label">Retry Count:</span>
              <span class="value">{{ retryCount }}</span>
            </div>
          </div>
        </div>
        
        <div class="dev-tools-section">
          <h4>快速操作</h4>
          <button class="action-btn" (click)="clearLocalStorage()">
            清除 LocalStorage
          </button>
          <button class="action-btn" (click)="clearSessionStorage()">
            清除 SessionStorage
          </button>
          <button class="action-btn" (click)="showApiMapping()">
            顯示 API 對應表
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dev-tools {
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
    }
    
    .dev-tools-toggle {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: #333;
      color: white;
      border: none;
      cursor: pointer;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease;
    }
    
    .dev-tools-toggle:hover {
      transform: scale(1.1);
    }
    
    .dev-tools-toggle .icon {
      font-size: 24px;
    }
    
    .dev-tools-panel {
      position: absolute;
      bottom: 60px;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      box-shadow: 0 2px 20px rgba(0,0,0,0.1);
      padding: 20px;
      width: 350px;
      max-height: 500px;
      overflow-y: auto;
    }
    
    .dev-tools-panel h3 {
      margin: 0 0 15px 0;
      color: #333;
      border-bottom: 2px solid #0044ad;
      padding-bottom: 10px;
    }
    
    .dev-tools-section {
      margin-bottom: 20px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .dev-tools-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
    
    .dev-tools-section h4 {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
      font-weight: 600;
    }
    
    .toggle-container {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 10px;
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #0044ad;
    }
    
    input:focus + .slider {
      box-shadow: 0 0 1px #0044ad;
    }
    
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    
    .toggle-label {
      font-weight: 500;
      color: #333;
    }
    
    .info {
      margin: 10px 0 0 0;
      padding: 10px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 13px;
      color: #666;
    }
    
    .config-info {
      background: #f9f9f9;
      padding: 10px;
      border-radius: 4px;
    }
    
    .config-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 13px;
    }
    
    .config-item:last-child {
      margin-bottom: 0;
    }
    
    .config-item .label {
      color: #666;
      font-weight: 500;
    }
    
    .config-item .value {
      color: #0044ad;
      font-family: monospace;
    }
    
    .action-btn {
      display: block;
      width: 100%;
      padding: 8px 12px;
      margin-bottom: 8px;
      background: #0044ad;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: background 0.3s ease;
    }
    
    .action-btn:last-child {
      margin-bottom: 0;
    }
    
    .action-btn:hover {
      background: #003399;
    }
    
    @media (max-width: 768px) {
      .dev-tools-panel {
        width: 300px;
        right: -10px;
      }
    }
  `]
})
export class DevToolsComponent implements OnInit {
  isProduction = environment.production;
  isPanelOpen = false;
  isMockMode = false;
  apiBaseUrl = '';
  requestTimeout = 0;
  retryCount = 0;
  
  constructor(private apiConfig: ApiConfigService) {}
  
  ngOnInit(): void {
    this.loadConfiguration();
  }
  
  /**
   * 載入當前配置
   */
  private loadConfiguration(): void {
    this.isMockMode = this.apiConfig.isMockMode;
    this.apiBaseUrl = environment.apiUrl || 'http://localhost:8080';
    this.requestTimeout = environment.ibrConfig?.requestTimeout || 30000;
    this.retryCount = environment.ibrConfig?.retryCount || 2;
  }
  
  /**
   * 切換面板顯示/隱藏
   */
  togglePanel(): void {
    this.isPanelOpen = !this.isPanelOpen;
  }
  
  /**
   * 切換 Mock 模式
   */
  toggleMockMode(): void {
    if (confirm('切換 API 模式將重新載入頁面，確定要繼續嗎？')) {
      this.apiConfig.toggleMockMode();
    } else {
      // 還原 checkbox 狀態
      this.loadConfiguration();
    }
  }
  
  /**
   * 清除 LocalStorage
   */
  clearLocalStorage(): void {
    if (confirm('確定要清除所有 LocalStorage 資料嗎？')) {
      localStorage.clear();
      alert('LocalStorage 已清除');
    }
  }
  
  /**
   * 清除 SessionStorage
   */
  clearSessionStorage(): void {
    if (confirm('確定要清除所有 SessionStorage 資料嗎？')) {
      sessionStorage.clear();
      alert('SessionStorage 已清除');
    }
  }
  
  /**
   * 顯示 API 對應表
   */
  showApiMapping(): void {
    const config = this.apiConfig.getConfiguration();
    console.log('=== API Configuration ===');
    console.log('Mock Mode:', config.mockMode);
    console.log('Base URL:', config.apiBaseUrl);
    console.log('Environment:', config.environment);
    console.log('=== API Mapping ===');
    console.table(config.apiMapping);
    alert('API 對應表已輸出至 Console，請開啟開發者工具查看');
  }
}