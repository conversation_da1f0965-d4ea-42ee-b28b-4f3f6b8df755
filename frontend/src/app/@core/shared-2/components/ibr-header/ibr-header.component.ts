import { Component, EventEmitter, Input, Output } from '@angular/core';

/**
 * IBR 共用 Header 元件
 * 提供統一的頁面頭部設計，包含 KGI logo 和客服按鈕
 */
@Component({
  selector: 'app-ibr-header',
  template: `
    <div class="ibr-header-wrapper">
      <header class="ibr-header">
        <div class="header-content">
          <div class="atom-logo-mobile">
            <img class="graphic" src="assets/image/icon/graphic.svg" alt="KGI Logo">
            <img class="kgib" src="assets/image/icon/kgib.svg" alt="KGIB">
          </div>
          <div class="action-buttons">
            <button 
              type="button"
              class="basic-costumer-service" 
              (click)="onCustomerServiceClick()"
              (keydown.enter)="onCustomerServiceClick()"
              (keydown.space)="onCustomerServiceClick()"
              aria-label="聯繫客服"
            >
              <img 
                src="assets/image/icon/customer-service.svg" 
                alt="客服"
                aria-hidden="true"
              >
            </button>
          </div>
        </div>
      </header>
    </div>
  `,
  styles: [`
    /* === Header Wrapper (控制整體寬度) === */
    .ibr-header-wrapper {
      width: 100%;
      display: flex;
      justify-content: center;
      position: sticky;
      top: 0;
      z-index: 100;
      background: #f8f9fa;  /* 與頁面背景一致 */
      padding: 0 20px;  /* 與 content 的 padding 一致 */
    }

    /* === IBR Header 設計 === */
    .ibr-header {
      width: 100%;
      max-width: 400px;
      background: #ffffff;
      box-shadow: -4px 0 8px rgba(0, 0, 0, 0.05), 4px 0 8px rgba(0, 0, 0, 0.05), 0 -4px 8px rgba(0, 0, 0, 0.05);  /* 只有側面和頂部陰影 */
      border-radius: 8px 8px 0 0;  /* 只有頂部圓角 */
      margin-bottom: 0;  /* 確保沒有底部間距 */
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 22.5px;
      height: 72px;
      width: 100%;
    }

    /* Logo 區域 */
    .atom-logo-mobile {
      display: flex;
      flex-direction: row;
      gap: 12px;
      align-items: center;
      justify-content: flex-start;
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
    }

    .graphic {
      flex-shrink: 0;
      width: 34px;
      height: 34px;
      position: relative;
      overflow: visible;
    }

    .kgib {
      flex-shrink: 0;
      width: 90.73px;
      height: 29.99px;
      position: relative;
      overflow: visible;
    }

    /* 右側按鈕區域 */
    .action-buttons {
      display: flex;
      flex-direction: row;
      gap: 20px;
      align-items: center;
      justify-content: flex-start;
      flex-shrink: 0;
      position: relative;
    }

    .basic-costumer-service {
      flex-shrink: 0;
      width: 24px;
      height: 24px;
      position: relative;
      overflow: visible;
      cursor: pointer;
      transition: opacity 0.3s ease;
      background: transparent;
      border: none;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .basic-costumer-service:hover,
    .basic-costumer-service:focus {
      opacity: 0.7;
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }

    .basic-costumer-service img {
      width: 100%;
      height: 100%;
    }

    /* === 響應式設計 === */
    
    /* 平板和小螢幕桌機 */
    @media (min-width: 768px) {
      .ibr-header {
        max-width: 500px;
      }
      
      .header-content {
        padding: 0 40px;
      }
    }

    /* 桌機版本 - 2/3 寬度 */
    @media (min-width: 1024px) {
      .ibr-header-wrapper {
        padding: 0 20px;  /* 桌機版也保持 padding */
      }
      
      .ibr-header {
        width: 66.666%;
        min-width: 600px;
        max-width: 800px;
      }
      
      .header-content {
        padding: 0 40px;
      }
    }

    /* 大螢幕桌機 */
    @media (min-width: 1440px) {
      .ibr-header-wrapper {
        padding: 0 40px;  /* 大螢幕增加 padding */
      }
      
      .ibr-header {
        max-width: 900px;
      }
      
      .header-content {
        padding: 0 60px;
      }
    }

    /* 手機版調整 */
    @media (max-width: 767px) {
      .ibr-header-wrapper {
        padding: 0 15px;  /* 與 main-content 的 padding 一致 */
      }
      
      .ibr-header {
        max-width: 100%;
        border-radius: 0;  /* 手機版移除圓角 */
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);  /* 較淡的陰影 */
      }
      
      .header-content {
        padding: 0 16px;
      }
    }
  `]
})
export class IbrHeaderComponent {
  
  /**
   * 客服按鈕點擊事件
   */
  @Output() customerServiceClick = new EventEmitter<void>();

  /**
   * 客服按鈕點擊處理
   */
  onCustomerServiceClick(): void {
    this.customerServiceClick.emit();
  }
}