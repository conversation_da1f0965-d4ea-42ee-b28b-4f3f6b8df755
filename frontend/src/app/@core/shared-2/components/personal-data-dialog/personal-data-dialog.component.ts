import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogRef } from '../../../shared/service/slide-dialog.service';

@Component({
  selector: 'app-personal-data-dialog',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './personal-data-dialog.component.html',
  styleUrls: ['./personal-data-dialog.component.scss']
})
export class PersonalDataDialogComponent implements OnInit {

  constructor(private dialogRef: DialogRef<PersonalDataDialogComponent>) { }

  ngOnInit(): void {
  }

  // 關閉對話框
  close(): void {
    this.dialogRef.close();
  }

  // 閱讀全部內容才能同意並送出
  onReadComplete(): void {
    this.dialogRef.close(true);
  }
} 
