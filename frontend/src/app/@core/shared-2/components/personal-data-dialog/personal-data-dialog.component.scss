.personal-data-dialog-content {
  width: 100%;
  height: auto;
  background: white;
  display: flex;
  flex-direction: column;
  font-family: "Noto Sans TC", sans-serif;
  position: relative;
  overflow: auto;
  padding: 20px;
  max-height: 70vh;

  // 公司標題區
  .company-header {
    text-align: center;
    margin-bottom: 24px;

    .company-logo {
      .logo-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 8px;

        .logo-diamond {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #ff6b35, #f7931e, #4a90e2, #0066cc);
          transform: rotate(45deg);
          border-radius: 4px;
          position: relative;

          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            width: 20px;
            height: 20px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
          }
        }

        .logo-text {
          text-align: left;

          .company-name-zh {
            font-size: 20px;
            font-weight: 700;
            color: #041c43;
            line-height: 1.2;
          }

          .company-name-en {
            font-size: 14px;
            font-weight: 600;
            color: #041c43;
            letter-spacing: 1px;
          }
        }
      }

      .company-subtitle {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }

  // 聲明內容
  .statement-content {
    .intro-text {
      font-size: 14px;
      line-height: 1.6;
      color: #333;
      margin-bottom: 20px;
      text-align: justify;
    }

    // 費用表格
    .fee-table {
      margin-bottom: 24px;

      .table-title {
        font-size: 16px;
        font-weight: 600;
        color: #041c43;
        margin-bottom: 12px;
      }

      .fee-table-content {
        width: 100%;
        border-collapse: collapse;
        font-size: 13px;
        line-height: 1.5;

        th, td {
          border: 1px solid #ddd;
          padding: 8px 10px;
          text-align: left;
          vertical-align: top;
        }

        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #041c43;
        }

        td {
          color: #333;
        }

        // 第一列寬度調整
        th:first-child,
        td:first-child {
          width: 35%;
        }
      }
    }

    // 計算說明
    .calculation-section {
      margin-bottom: 20px;

      h3 {
        font-size: 15px;
        font-weight: 600;
        color: #041c43;
        margin-bottom: 8px;
      }

      p {
        font-size: 13px;
        line-height: 1.6;
        color: #333;
        text-align: justify;
      }
    }

    // 舉例說明
    .example-section {
      margin-bottom: 20px;

      h3 {
        font-size: 15px;
        font-weight: 600;
        color: #041c43;
        margin-bottom: 8px;
      }

      p {
        font-size: 13px;
        line-height: 1.6;
        color: #333;
        margin-bottom: 12px;
      }

      ol {
        font-size: 13px;
        line-height: 1.6;
        color: #333;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
        }

        ol {
          margin-top: 8px;
          margin-bottom: 0;
        }
      }
    }
  }

  // 對話框底部
  .dialog-footer {
    margin-top: auto;
    padding: 16px 0 0;
    border-top: 1px solid #e9ecef;
    background: white;
    position: sticky;
    bottom: 0;

    .read-notice {
      text-align: center;
      margin-bottom: 16px;

      .read-notice-zh {
        font-size: 14px;
        color: #041c43;
        font-weight: 500;
        margin: 0 0 4px 0;
      }

      .read-notice-en {
        font-size: 12px;
        color: #666;
        margin: 0;
        font-family: "Montserrat", sans-serif;
      }
    }

    .confirm-btn {
      width: 100%;
      height: 48px;
      background: #0044ad;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background: #003399;
      }

      &:active {
        background: #002266;
      }
    }
  }
}

// 手機版調整
@media (max-width: 767px) {
  .personal-data-dialog-content {
    padding: 16px;
  }
}

// 隱藏 DialogContainerComponent 的標題區域
:host ::ng-deep {
  app-dialog-container {
    .row:first-child {
      // 如果有標題，保留它
      h3, h6 {
        display: block;
      }
    }
  }
}