# G-OTP 元件使用範例

## 在自然人模組中使用

### 1. 修改 i-identity-verification.component.html

```html
<!-- 替換原本的 OTP 輸入區域 -->
<div class="otp-section">
  <h3>請輸入簡訊驗證碼</h3>
  
  <!-- 使用 g-otp 元件 -->
  <app-g-otp
    [prefix]="displayCode"
    [error]="hasError"
    [disabled]="isLoading"
    [autoFocus]="true"
    (otpComplete)="onOtpComplete($event)"
    (otpChange)="onOtpChange($event)"
    #otpComponent
  ></app-g-otp>
  
  <!-- 錯誤訊息 -->
  <div class="error-message" *ngIf="hasError">
    {{ errorMessage }}
  </div>
  
  <!-- 重新發送按鈕 -->
  <button 
    class="resend-button" 
    [disabled]="!canResend"
    (click)="resendOtp()"
  >
    重新發送 {{ countdown > 0 ? '(' + countdown + 's)' : '' }}
  </button>
</div>
```

### 2. 修改 i-identity-verification.component.ts

```typescript
import { Component, ViewChild } from '@angular/core';
import { GOtpComponent } from '@core/shared-2/components/g-otp/g-otp.component';

export class IIdentityVerificationComponent {
  @ViewChild('otpComponent') otpComponent!: GOtpComponent;
  
  // 其他屬性保持不變...
  
  /**
   * OTP 完成事件處理
   */
  onOtpComplete(otp: string): void {
    console.log('OTP 輸入完成:', otp);
    this.verifyOTP(otp);
  }
  
  /**
   * OTP 變更事件處理
   */
  onOtpChange(otp: string): void {
    // 清除錯誤狀態
    if (this.hasError && otp.length > 0) {
      this.hasError = false;
      this.errorMessage = '';
    }
  }
  
  /**
   * 驗證 OTP
   */
  private verifyOTP(otp: string): void {
    if (otp.length !== 6) {
      return;
    }
    
    // 開發模式使用模擬驗證
    if (this.isDevelopment && otp === this.devVerificationCode) {
      this.handleVerificationSuccess();
      return;
    }
    
    // 呼叫 OTP 服務進行驗證
    this.otpService.verifyOtp(otp)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.handleVerificationSuccess();
          } else {
            this.handleVerificationFailure(response.message);
          }
        },
        error: (error) => {
          this.handleVerificationFailure(error.message);
        }
      });
  }
  
  /**
   * 處理驗證失敗
   */
  private handleVerificationFailure(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    
    // 觸發抖動效果
    if (this.otpComponent) {
      this.otpComponent.shake();
    }
  }
  
  /**
   * 重新發送 OTP
   */
  resendOtp(): void {
    // ... 原有邏輯
    
    // 清空 OTP 輸入
    if (this.otpComponent) {
      this.otpComponent.clear();
    }
  }
}
```

## 在查詢模組中使用

### 1. 修改 query-otp.component.html

```html
<div class="verification-code-section">
  <div class="code-input-wrapper">
    <!-- 使用 g-otp 元件 -->
    <app-g-otp
      [prefix]="baseModel.dynacPwd"
      [error]="hasError"
      [disabled]="loading"
      (otpComplete)="onOtpComplete($event)"
      (otpChange)="onOtpChange($event)"
      #queryOtpComponent
    ></app-g-otp>
  </div>
  
  <!-- 提示訊息 -->
  <div class="code-hint" *ngIf="!hasError">
    請輸入簡訊中的 6 位數字驗證碼
  </div>
  <div class="code-error" *ngIf="hasError">
    {{ error }}
  </div>
</div>
```

### 2. 修改 query-otp.component.ts

```typescript
import { Component, ViewChild } from '@angular/core';
import { GOtpComponent } from '@core/shared-2/components/g-otp/g-otp.component';

export class QueryOtpComponent {
  @ViewChild('queryOtpComponent') queryOtpComponent!: GOtpComponent;
  
  /**
   * OTP 完成事件處理
   */
  onOtpComplete(otp: string): void {
    // 組合完整的 OTP (prefix + 6位數字)
    const fullOtp = this.baseModel.dynacPwd + otp;
    this.baseModel.otp = fullOtp;
    
    // 自動驗證
    this.verifyOtpCode();
  }
  
  /**
   * OTP 變更事件處理
   */
  onOtpChange(otp: string): void {
    // 清除錯誤
    this.clearError();
  }
  
  /**
   * 清空輸入
   */
  cleanInputValue(): void {
    if (this.queryOtpComponent) {
      this.queryOtpComponent.clear();
    }
    this.clearError();
  }
}
```

## 進階使用

### 設定初始值

```typescript
// 設定預設值（例如從 localStorage 恢復）
ngAfterViewInit() {
  const savedOtp = localStorage.getItem('savedOtp');
  if (savedOtp && this.otpComponent) {
    this.otpComponent.setValue(savedOtp);
  }
}
```

### 自訂長度

```html
<!-- 4 位數 OTP -->
<app-g-otp
  [length]="4"
  (otpComplete)="onOtpComplete($event)"
></app-g-otp>
```

### 禁用狀態

```typescript
// 動態控制禁用狀態
disableOtpInput(): void {
  this.isOtpDisabled = true;
}

enableOtpInput(): void {
  this.isOtpDisabled = false;
  // 重新聚焦
  if (this.otpComponent) {
    this.otpComponent.focusFirstInput();
  }
}
```

### 樣式覆寫

```scss
// 在元件的 SCSS 中覆寫樣式
:host ::ng-deep {
  .g-otp-input {
    // 自訂輸入框大小
    width: 60px;
    height: 60px;
    
    // 自訂顏色
    &:focus {
      border-color: #your-color;
    }
  }
  
  // 自訂間距
  .g-otp-container {
    gap: 16px;
  }
}
```

## API 參考

### 輸入屬性

| 屬性 | 類型 | 預設值 | 說明 |
|------|------|--------|------|
| `length` | number | 6 | OTP 長度 |
| `prefix` | string | '' | 前綴顯示 |
| `disabled` | boolean | false | 是否禁用 |
| `error` | boolean | false | 錯誤狀態 |
| `autoFocus` | boolean | true | 自動聚焦 |
| `placeholder` | string | '' | 佔位符 |

### 輸出事件

| 事件 | 參數類型 | 說明 |
|------|----------|------|
| `otpComplete` | string | OTP 輸入完成時觸發 |
| `otpChange` | string | OTP 值變更時觸發 |
| `otpError` | void | 發生錯誤時觸發 |

### 公開方法

| 方法 | 參數 | 返回值 | 說明 |
|------|------|--------|------|
| `getValue()` | - | string | 取得當前 OTP 值 |
| `setValue(value)` | string | void | 設定 OTP 值 |
| `clear()` | - | void | 清空所有輸入 |
| `focusFirstInput()` | - | void | 聚焦第一格 |
| `shake()` | - | void | 觸發抖動效果 |