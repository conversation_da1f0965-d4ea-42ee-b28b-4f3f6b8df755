/**
 * 共用 OTP 輸入元件
 * 
 * === 功能說明 ===
 * 提供標準化的 6 位數 OTP 輸入介面，可被自然人、查詢等模組重複使用
 * 支援單格輸入、自動跳轉、貼上、鍵盤導航等功能
 * 
 * === 使用方式 ===
 * <app-g-otp 
 *   [prefix]="'XYZ'" 
 *   [disabled]="false"
 *   (otpComplete)="onOtpComplete($event)"
 *   (otpChange)="onOtpChange($event)">
 * </app-g-otp>
 * 
 * <AUTHOR> Code
 * @date 2025/06/08
 */

import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ViewChildren, 
  QueryList, 
  ElementRef,
  OnInit,
  OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-g-otp',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './g-otp.component.html',
  styleUrls: ['./g-otp.component.scss']
})
export class GOtpComponent implements OnInit, OnDestroy {
  @ViewChildren('otpInput') otpInputs!: QueryList<ElementRef<HTMLInputElement>>;
  
  // 輸入屬性
  @Input() length = 6; // OTP 長度，預設 6 位
  @Input() disabled = false; // 是否禁用
  @Input() error = false; // 是否顯示錯誤狀態
  @Input() autoFocus = true; // 是否自動聚焦第一格
  @Input() placeholder = ''; // 佔位符
  @Input() showPrefix = true; // 是否顯示前綴
  @Input() errorMessage = ''; // 錯誤訊息
  @Input() hintText = ''; // 提示文字
  @Input() sentTime = ''; // 發送時間
  @Input() resendCountdown = 0; // 重新發送倒數
  @Input() maskedPhoneNumber = ''; // 遮罩後的電話號碼

  // 輸出事件
  @Output() otpComplete = new EventEmitter<string>(); // OTP 輸入完成
  @Output() otpChange = new EventEmitter<string>(); // OTP 值變更
  @Output() otpError = new EventEmitter<void>(); // 發生錯誤
  @Output() resend = new EventEmitter<void>(); // 重新發送
  
  // 內部狀態
  otpDigits: string[] = [];
  
  constructor() {}
  
  ngOnInit(): void {
    // 初始化數字陣列
    this.otpDigits = new Array(this.length).fill('');
    
    // 自動聚焦第一格
    if (this.autoFocus) {
      setTimeout(() => this.focusFirstInput(), 100);
    }
  }
  
  ngOnDestroy(): void {
    // 清理資源
  }
  
  /**
   * 處理 OTP 輸入
   */
  onOtpInput(event: Event, index: number): void {
    if (this.disabled) return;
    
    const target = event.target as HTMLInputElement;
    let value = target.value;
    
    // 修復：只取最後輸入的字符，避免重複
    if (value.length > 1) {
      value = value.slice(-1);
      target.value = value;
    }
    
    // 只允許數字
    if (!/^\d*$/.test(value)) {
      target.value = '';
      this.otpDigits[index] = '';
      return;
    }
    
    // 更新數字陣列
    this.otpDigits[index] = value;
    
    // 發送變更事件
    this.emitChange();
    
    // 如果輸入了值，自動跳到下一格
    if (value && index < this.length - 1) {
      // 使用 requestAnimationFrame 避免焦點切換時的輸入衝突
      requestAnimationFrame(() => {
        const inputs = this.otpInputs.toArray();
        if (inputs[index + 1]) {
          // 清空下一格的值
          inputs[index + 1].nativeElement.value = '';
          this.otpDigits[index + 1] = '';
          
          // 聚焦並選中下一格
          inputs[index + 1].nativeElement.focus();
          inputs[index + 1].nativeElement.select();
        }
      });
    }
    
    // 如果所有格子都填滿，觸發完成事件
    if (this.isComplete()) {
      setTimeout(() => this.emitComplete(), 100);
    }
  }
  
  /**
   * 處理鍵盤事件
   */
  onOtpKeydown(event: KeyboardEvent, index: number): void {
    if (this.disabled) return;
    
    const inputs = this.otpInputs.toArray();
    
    // 處理 Backspace
    if (event.key === 'Backspace') {
      event.preventDefault();
      
      // 如果當前格有數字，刪除它
      if (this.otpDigits[index]) {
        this.otpDigits[index] = '';
        inputs[index].nativeElement.value = '';
        this.emitChange();
      }
      
      // 如果不是第一格，總是移到前一格
      if (index > 0 && inputs[index - 1]) {
        requestAnimationFrame(() => {
          inputs[index - 1].nativeElement.focus();
          inputs[index - 1].nativeElement.select();
        });
      }
    }
    
    // 處理左右箭頭
    if (event.key === 'ArrowLeft' && index > 0) {
      event.preventDefault();
      if (inputs[index - 1]) {
        inputs[index - 1].nativeElement.focus();
      }
    }
    
    if (event.key === 'ArrowRight' && index < this.length - 1) {
      event.preventDefault();
      if (inputs[index + 1]) {
        inputs[index + 1].nativeElement.focus();
      }
    }
    
    // 處理 Enter 鍵
    if (event.key === 'Enter' && this.isComplete()) {
      event.preventDefault();
      this.emitComplete();
    }
  }
  
  /**
   * 處理貼上
   */
  onPaste(event: ClipboardEvent): void {
    if (this.disabled) return;
    
    event.preventDefault();
    const pasteData = event.clipboardData?.getData('text/plain');
    
    if (pasteData) {
      // 只取數字
      const digits = pasteData.replace(/\D/g, '').slice(0, this.length);
      
      // 填入數字並更新對應的輸入框
      const inputs = this.otpInputs.toArray();
      for (let i = 0; i < digits.length && i < this.length; i++) {
        this.otpDigits[i] = digits[i];
        // 同步更新輸入框的值
        if (inputs[i]) {
          inputs[i].nativeElement.value = digits[i];
        }
      }
      
      // 發送變更事件
      this.emitChange();
      
      // 如果填滿所有格子，觸發完成事件
      if (digits.length >= this.length) {
        setTimeout(() => this.emitComplete(), 100);
      } else {
        // 否則 focus 到下一個空格
        const nextEmptyIndex = this.otpDigits.findIndex(d => d === '');
        if (nextEmptyIndex !== -1 && inputs[nextEmptyIndex]) {
          requestAnimationFrame(() => {
            inputs[nextEmptyIndex].nativeElement.focus();
          });
        }
      }
    }
  }
  
  /**
   * 檢查是否完成
   */
  private isComplete(): boolean {
    return this.otpDigits.every(digit => digit !== '') && 
           this.otpDigits.length === this.length;
  }
  
  /**
   * 發送變更事件
   */
  private emitChange(): void {
    const value = this.getValue();
    this.otpChange.emit(value);
  }
  
  /**
   * 發送完成事件
   */
  private emitComplete(): void {
    if (this.isComplete()) {
      const value = this.getValue();
      this.otpComplete.emit(value);
    }
  }
  
  /**
   * 取得當前值
   */
  getValue(): string {
    return this.otpDigits.join('');
  }
  
  /**
   * 設定值（外部呼叫）
   */
  setValue(value: string): void {
    const digits = value.replace(/\D/g, '').slice(0, this.length).split('');
    this.otpDigits = new Array(this.length).fill('');
    
    digits.forEach((digit, index) => {
      this.otpDigits[index] = digit;
    });
    
    // 更新輸入框
    this.updateInputs();
  }
  
  /**
   * 清空所有輸入
   */
  clear(): void {
    this.otpDigits = new Array(this.length).fill('');
    this.updateInputs();
    this.focusFirstInput();
    this.emitChange();
  }
  
  /**
   * 聚焦第一個輸入框
   */
  focusFirstInput(): void {
    const inputs = this.otpInputs?.toArray();
    if (inputs && inputs[0]) {
      inputs[0].nativeElement.focus();
    }
  }
  
  /**
   * 更新所有輸入框的值
   */
  private updateInputs(): void {
    const inputs = this.otpInputs?.toArray();
    if (inputs) {
      this.otpDigits.forEach((digit, index) => {
        if (inputs[index]) {
          inputs[index].nativeElement.value = digit;
        }
      });
    }
  }
  
  /**
   * 添加錯誤抖動效果
   */
  shake(): void {
    const container = document.querySelector('.otp-input-container');
    if (container) {
      container.classList.add('shake');
      setTimeout(() => {
        container.classList.remove('shake');
      }, 500);
    }
  }

  /**
   * 處理重新發送
   */
  onResend(): void {
    this.resend.emit();
  }

  /**
   * 取得當前時間（格式化）
   */
  getCurrentTime(): string {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  /**
   * 格式化時間（用於倒數計時）
   */
  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}
