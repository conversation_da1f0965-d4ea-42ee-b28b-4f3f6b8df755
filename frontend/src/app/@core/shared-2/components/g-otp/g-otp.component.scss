/**
 * G-OTP 共用元件樣式
 * 提供一致的 OTP 輸入框視覺效果
 */

// 變數定義
$primary-color: #0044ad;
$error-color: #ff4444;
$border-color: #d1e7ff;
$focus-shadow: 0 0 0 4px rgba(0, 68, 173, 0.15);
$error-shadow: 0 0 0 4px rgba(255, 68, 68, 0.15);

// 主容器
.g-otp-full-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  width: 100%;
}

// 文件圖示區域
.document-icon-section {
  display: flex;
  justify-content: center;
  
  .document-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 驗證碼輸入區域
.verification-code-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  
  .code-input-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 24px 32px;
    background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
    border-radius: 12px;
    border: 1px solid #e1ecf7;
    box-shadow: 0 2px 8px rgba(0, 68, 173, 0.08);
    width: 100%;
    max-width: 480px;
    min-width: 420px;
  }
  
  .code-hint {
    color: #666666;
    font-family: "Noto Sans TC", sans-serif;
    font-size: 14px;
    text-align: center;
    margin-top: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid $primary-color;
  }
  
  .code-error {
    color: $error-color;
    font-family: "Noto Sans TC", sans-serif;
    font-size: 14px;
    text-align: center;
    font-weight: 600;
    margin-top: 8px;
    padding: 8px 16px;
    background: #fff5f5;
    border-radius: 6px;
    border-left: 3px solid $error-color;
    animation: fadeInError 0.3s ease-out;
  }
}

// 狀態訊息區域
.status-section {
  width: 100%;
  max-width: 400px;
  
  .status-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px;
    background: #f0f7ff;
    border-radius: 8px;
    border-left: 4px solid $primary-color;
  }
  
  .status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
  }
  
  .status-text {
    flex: 1;
    
    .status-main {
      color: #041c43;
      font-family: "Noto Sans TC", sans-serif;
      font-size: 14px;
      line-height: 150%;
      margin-bottom: 4px;
    }
    
    .status-sub {
      color: #666666;
      font-family: "Montserrat", sans-serif;
      font-size: 12px;
      line-height: 140%;
    }
  }
}

// 重新發送區域
.resend-section {
  display: flex;
  justify-content: center;
  
  .resend-button {
    background: none;
    border: none;
    color: $primary-color;
    font-family: "Noto Sans TC", sans-serif;
    font-size: 14px;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    
    &:hover:not(:disabled) {
      background: #f0f7ff;
    }
    
    &:disabled {
      color: #999999;
      cursor: not-allowed;
    }
    
    .resend-text {
      font-weight: 500;
    }
    
    .countdown-timer {
      color: $error-color;
      font-family: "Montserrat", sans-serif;
      font-weight: 600;
      font-size: 14px;
      margin-left: 4px;
    }
  }
}

// 發送目標訊息
.target-info {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
  
  .target-text {
    display: block;
    color: #041c43;
    font-family: "Noto Sans TC", sans-serif;
    font-size: 14px;
    line-height: 150%;
    margin-bottom: 4px;
  }
  
  .target-text-en {
    color: #666666;
    font-family: "Montserrat", sans-serif;
    font-size: 12px;
    line-height: 140%;
  }
}

// 前綴樣式
.code-prefix-section {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  
  .code-prefix {
    color: $primary-color;
    font-family: "Montserrat", sans-serif;
    font-size: 28px;
    font-weight: 700;
    letter-spacing: 3px;
    text-shadow: 0 1px 2px rgba(0, 68, 173, 0.1);
    text-align: center;
  }
}

// OTP 容器
.otp-input-container {
  display: flex;
  gap: 8px;
  justify-content: center;
  
  // 錯誤狀態動畫
  &.error {
    animation: shake 0.5s ease-in-out;
  }
  
  // 禁用狀態
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
  
  &.shake {
    animation: shake 0.5s ease-in-out;
  }
}

// 單個輸入框
.otp-input {
  width: 45px;
  height: 56px;
  border: 2px solid $border-color;
  border-radius: 8px;
  text-align: center;
  font-family: "Montserrat", sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #041c43;
  background: #ffffff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  caret-color: $primary-color;
  
  // 聚焦狀態
  &:focus {
    border-color: $primary-color;
    box-shadow: $focus-shadow, 0 2px 8px rgba(0, 68, 173, 0.2);
    transform: translateY(-1px);
  }
  
  // 已填入值
  &.filled {
    background: linear-gradient(135deg, #f0f7ff 0%, #e8f2ff 100%);
    border-color: $primary-color;
    color: $primary-color;
    box-shadow: 0 2px 6px rgba(0, 68, 173, 0.15);
  }
  
  // 佔位符
  &::placeholder {
    color: #e0e0e0;
    font-size: 16px;
    font-weight: 400;
  }
  
  // 禁用狀態
  &:disabled {
    background: #f5f5f5;
    border-color: #e0e0e0;
    color: #999999;
    cursor: not-allowed;
  }
  
  // 錯誤狀態
  &.error {
    border-color: $error-color;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
    color: #cc0000;
    
    &:focus {
      box-shadow: $error-shadow, 0 2px 8px rgba(255, 68, 68, 0.2);
    }
  }
  
  // 移除數字輸入的上下箭頭
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  &[type=number] {
    -moz-appearance: textfield;
  }
}

// 抖動動畫
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

// 動畫效果
@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 響應式設計
// 平板
@media (min-width: 768px) {
  .code-prefix {
    font-size: 32px;
    letter-spacing: 4px;
  }
  
  .otp-input {
    width: 50px;
    height: 60px;
    font-size: 26px;
  }
  
  .otp-input-container {
    gap: 10px;
  }
  
  .code-input-wrapper {
    padding: 28px 36px;
    gap: 20px;
    max-width: 520px;
    min-width: 460px;
  }
}

// 桌面
@media (min-width: 1024px) {
  .otp-input {
    width: 56px;
    height: 64px;
    font-size: 28px;
  }
  
  .otp-input-container {
    gap: 12px;
  }
}

// 手機
@media (max-width: 767px) {
  .g-otp-full-wrapper {
    gap: 24px;
  }
  
  .code-prefix {
    font-size: 22px;
    letter-spacing: 2px;
  }
  
  .otp-input {
    width: 40px;
    height: 48px;
    font-size: 20px;
  }
  
  .otp-input-container {
    gap: 6px;
  }
  
  .code-input-wrapper {
    padding: 20px 24px;
    gap: 14px;
    flex-direction: column;
    min-width: 320px;
    max-width: 380px;
  }
}

// 小手機
@media (max-width: 375px) {
  .otp-input {
    width: 36px;
    height: 44px;
    font-size: 18px;
  }
  
  .otp-input-container {
    gap: 4px;
  }
}