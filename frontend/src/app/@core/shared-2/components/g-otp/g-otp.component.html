<!-- G-OTP 共用元件模板 -->
<div class="g-otp-full-wrapper">
  <!-- 文件圖示區域 -->
  <div class="document-icon-section">
    <div class="document-icon">
      <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
        <!-- 文件背景 -->
        <rect x="20" y="10" width="40" height="50" rx="4" fill="#E8F2FF" stroke="#B3D9FF" stroke-width="2"/>
        <!-- 文件內容線條 -->
        <line x1="28" y1="20" x2="52" y2="20" stroke="#0044AD" stroke-width="2"/>
        <line x1="28" y1="26" x2="48" y2="26" stroke="#0044AD" stroke-width="2"/>
        <line x1="28" y1="32" x2="50" y2="32" stroke="#0044AD" stroke-width="2"/>
        <!-- 對話框 -->
        <rect x="35" y="35" width="30" height="20" rx="4" fill="#0044AD"/>
        <line x1="40" y1="42" x2="58" y2="42" stroke="white" stroke-width="2"/>
        <line x1="40" y1="47" x2="55" y2="47" stroke="white" stroke-width="2"/>
      </svg>
    </div>
  </div>

  <!-- 驗證碼輸入區域 -->
  <div class="verification-code-section">
    <div class="code-input-wrapper">
      <!-- 前綴顯示（可選） - 置中顯示在上方 -->
      <div class="code-prefix-section" *ngIf="showPrefix">
        <div class="code-prefix">XYZ-</div>
      </div>
      <!-- OTP 輸入容器 -->
      <div class="otp-input-container" [class.error]="error" [class.disabled]="disabled">
        <input
          *ngFor="let digit of otpDigits; let i = index"
          #otpInput
          type="text"
          class="otp-input"
          [class.filled]="digit !== ''"
          [class.error]="error"
          [value]="digit"
          [disabled]="disabled"
          [placeholder]="placeholder"
          [attr.aria-label]="'OTP digit ' + (i + 1)"
          [attr.aria-invalid]="error"
          (input)="onOtpInput($event, i)"
          (keydown)="onOtpKeydown($event, i)"
          (paste)="onPaste($event)"
          maxlength="1"
          autocomplete="off"
          inputmode="numeric"
          pattern="[0-9]"
        />
      </div>
    </div>
    <div class="code-hint" *ngIf="!error && !errorMessage">
      {{ hintText || '請輸入簡訊中的 6 位數字驗證碼' }}
    </div>
    <div class="code-error" *ngIf="error || errorMessage">
      {{ errorMessage || '驗證碼錯誤，請重新輸入' }}
    </div>
  </div>

  <!-- 狀態訊息區域 -->
  <div class="status-section">
    <div class="status-message">
      <div class="status-icon">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
          <circle cx="10" cy="10" r="10" fill="#0044ad"/>
          <path d="M6 10l2 2 6-6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
      <div class="status-text">
        <div class="status-main">驗證碼於 {{ sentTime || getCurrentTime() }} 發送，有效時間 5 分鐘</div>
        <div class="status-sub">Verification code was sent at {{ sentTime || getCurrentTime() }} and is valid for 5 minutes</div>
      </div>
    </div>

    <!-- 重新發送按鈕 -->
    <div class="resend-section">
      <button
        type="button"
        class="resend-button"
        [disabled]="resendCountdown > 0"
        (click)="onResend()"
      >
        <span class="resend-text" *ngIf="resendCountdown === 0">重新發送 Resend</span>
        <span class="resend-text" *ngIf="resendCountdown > 0">
          重新發送 Resend
          <span class="countdown-timer">{{ formatTime(resendCountdown) }}</span>
        </span>
      </button>
    </div>
  </div>

  <!-- 發送目標訊息 -->
  <div class="target-info">
    <span class="target-text">驗證碼發送至 {{ maskedPhoneNumber }}</span>
    <div class="target-text-en">Verification code was sent to {{ maskedPhoneNumber }}</div>
  </div>
</div>
