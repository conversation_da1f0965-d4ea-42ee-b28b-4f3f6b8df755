import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';

// UI 組件庫 (暫時註解，避免編譯錯誤)
// import { KendoUIModule } from '@progress/kendo-angular-inputs';

// IBR 共用服務
import { IbrValidationService } from './services/ibr-validation.service';
import { IbrCalculationService } from './services/ibr-calculation.service';
import { IbrStateService } from './services/ibr-state.service';
import { SmartCardService } from './services/smart-card.service';
import { ApiConfigService } from './services/api-config.service';
import { BaseApiService } from './services/base-api.service';
import { SharedTestDataService } from './services/shared-test-data.service';

// IBR 共用元件
import { IbrHeaderComponent } from './components/ibr-header/ibr-header.component';
import { DevToolsComponent } from './components/dev-tools/dev-tools.component';
import { GOtpComponent } from './components/g-otp/g-otp.component';
// GOtpComponent 是 standalone 元件，需要在 imports 中引入
// import { IbrButtonComponent } from './components/ibr-button/ibr-button.component';
// import { IbrFormFieldComponent } from './components/ibr-form-field/ibr-form-field.component';
// import { IbrProgressBarComponent } from './components/ibr-progress-bar/ibr-progress-bar.component';

// IBR 共用指令
// import { TaiwanIdValidatorDirective } from './directives/taiwan-id-validator.directive';
// import { BusinessIdValidatorDirective } from './directives/business-id-validator.directive';

/**
 * IBR 共用模組
 * 
 * 提供三大模組共用的：
 * - 核心服務 (驗證、計算、狀態管理)
 * - UI 元件 (按鈕、表單、進度條)
 * - 驗證指令 (身分證、統編驗證)
 * - 管道 (格式化顯示)
 */
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    // KendoUIModule // 暫時註解，避免編譯錯誤
    
    // Standalone 元件需要在 imports 中引入
    GOtpComponent
  ],
  declarations: [
    // 共用元件
    IbrHeaderComponent,
    DevToolsComponent,
    // IbrButtonComponent,
    // IbrFormFieldComponent,
    // IbrProgressBarComponent,
    
    // 共用指令
    // TaiwanIdValidatorDirective,
    // BusinessIdValidatorDirective
  ],
  providers: [
    // 核心服務
    IbrValidationService,
    IbrCalculationService,
    IbrStateService,
    SmartCardService,
    ApiConfigService,
    SharedTestDataService
    // BaseApiService 是抽象類，不需要在 providers 中註冊
  ],
  exports: [
    // 重新匯出 Angular 基礎模組
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    
    // 重新匯出共用元件
    IbrHeaderComponent,
    DevToolsComponent,
    GOtpComponent,
    // IbrButtonComponent,
    // IbrFormFieldComponent,
    // IbrProgressBarComponent,
    
    // 重新匯出共用指令
    // TaiwanIdValidatorDirective,
    // BusinessIdValidatorDirective
  ]
})
export class IbrSharedModule { }
