//
//@media(min-width:768px) {
//  header {
//    &.font-size-vw {
//      .navbar {
//        .navbar-brand {
//          // font-size: 2.25vw;
//          // padding: 1vw 0 1vw 18vw;
//          // margin: 1.25vw 0 1.25vw 4vw
//        }
//      }
//    }
//  }
//}
//
//// header左方logo
//.navbar-left-kgi-brand {
//  padding: 0px 0px;
//  height:40px; width:115px;
//  background: url('./assets/stp/kgi_logo.svg') 8px center no-repeat;
//  background-size: 100px auto;
//  margin-right: 0px;
//}
//
//@media (min-width: 576px) {
//  .navbar-left-kgi-brand {
//    padding: 0px 0px;
//    //height:60px;
//    width:195px;
//    background: url('./assets/stp/kgi_logo.svg') 0px center no-repeat;
//    background-size: 105px auto;
//    margin-top: 5px;
//  }
//}
//
//// header右方logo
//.navbar-right-logo {
//  //display: block;
//  //margin-left: auto;
//  padding: 0px 0px;
//  width:195px;
//  text-align: right;
//  //background: url('./assets/stp/icon/07-phone.svg') 8px center no-repeat;
//}
//
//.navbar-center{
//  margin-top: 5px;
//}



ul {
  padding-left: 0;
  margin-bottom: 0;
  li {
    list-style: none;
  }
}
//nav {
//  box-shadow: 0 -1px 30px rgba(41, 63, 95, 0.08);
//  position: sticky;
//  top: 0;
//  z-index: 1024;
//  .topMenu {
//    padding: 1rem 6rem; // 16px 96px
//  }
//  .brandLogo {
//    margin-bottom: 0;
//  }
//  .brandLogo a {
//    //background-image: url("~src/assets/image/icon/brand_logo.svg");
//    background-image: url("~src/assets/image/icon/brand_logo.svg");
//    background-position: center;
//    background-size: cover;
//    width: 175px;
//    height: 44px;
//    float: left;
//    text-indent: 101%;
//    overflow: hidden;
//    white-space: nowrap;
//  }
//  .btn {
//    border-radius: 0;
//  }
//  .navbar-brand {
//    padding-top: 1rem;
//    padding-bottom: 1rem;
//  }
//}
//.navbar {
//  padding: 0 1rem;
//  height: 80px;
//}

@media (max-width: 991px) {
  nav {
    .navbar {
      padding: 0;
      .navbar-brand {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
      }
    }
    .topMenu {
      padding: 1.125rem 2rem; // 18px 32px
    }
  }
}
@media (max-width: 767px) {
  nav {
    box-shadow: 0 1px 10px rgba(41, 63, 95, 0.08);
    .topMenu {
      padding-left: 1.5rem; //24px
      height: 80px;
    }
  }
}
