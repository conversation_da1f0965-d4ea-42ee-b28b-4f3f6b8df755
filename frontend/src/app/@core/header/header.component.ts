import { Component, EventEmitter, Inject, Input, LOCALE_ID, Output } from '@angular/core';
import { CldrIntlService, IntlService } from '@progress/kendo-angular-intl';

interface Theme {
  href: string;
  text: string;
}

interface Language {
  locale: string;
  localeId: string;
}

interface PopupSettings {
  width: string;
}

@Component({
    selector: 'kgi-header-component',
    styleUrls: ['./header.component.scss'],
    templateUrl: './header.component.html'
})
export class HeaderComponent {
    @Output() public toggleMenu = new EventEmitter<void>();
    @Input() public selectedPage?: string;

    public selectedLanguage: Language = { locale: 'English', localeId: 'en-US' };
    public popupSettings: PopupSettings = { width: '150' };
    public themes: Theme[] = [
        {
            href: 'assets/kendo-theme-default/dist/all.css',
            text: 'Default'
        },
        {
            href: 'assets/kendo-theme-material/dist/all.css',
            text: 'Material'
        }
    ];
    public selectedTheme: Theme = this.themes[0];

    constructor(@Inject(LOCALE_ID) public localeId: string, public intlService: IntlService) {
        this.localeId = this.selectedLanguage.localeId;
        this.setLocale(this.localeId);
    }

    public changeTheme(theme: Theme): void {
        this.selectedTheme = theme;
        const themeEl = document.getElementById('theme') as HTMLLinkElement;
        if (themeEl) {
            themeEl.href = theme.href;
        }
    }

    public setLocale(locale: string): void {
        (this.intlService as CldrIntlService).localeId = locale;
    }

    public onButtonClick(): void {
        this.toggleMenu.emit();
    }
}
