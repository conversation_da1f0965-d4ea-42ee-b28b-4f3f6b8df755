import { CUSTOM_ELEMENTS_SCHEMA, ModuleWithProviders, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FooterComponent } from './footer/footer.component';
import { HeaderComponent } from './header/header.component';
import { ButtonModule, ButtonsModule } from '@progress/kendo-angular-buttons';
import { ExcelModule, GridModule, PDFModule } from '@progress/kendo-angular-grid';
import { LabelModule } from '@progress/kendo-angular-label';
import { DrawerModule, LayoutModule } from '@progress/kendo-angular-layout';
import { SchedulerModule } from '@progress/kendo-angular-scheduler';
import { EditorModule } from '@progress/kendo-angular-editor';
import { FileSelectModule } from '@progress/kendo-angular-upload';
import { ChartModule, ChartsModule } from '@progress/kendo-angular-charts';
import { IntlModule } from '@progress/kendo-angular-intl';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { DropDownsModule } from '@progress/kendo-angular-dropdowns';
import { NotificationModule } from '@progress/kendo-angular-notification';
import { RouterModule } from '@angular/router';
import { ThreeRowsLayoutComponent } from './layouts/three-rows/three-rows.layout';
import { ThreeRowsDrawerLayoutComponent } from './layouts';
import { OneRowLayoutComponent } from './layouts/one-row/one-row.layout';
import { TwoRowsLayoutComponent } from './layouts/two-rows/two-rows.layout';


const CORE_MODULES = [
    ButtonModule,
    ButtonsModule,
    LayoutModule,
    DrawerModule,
    DropDownsModule,
    GridModule,
    PDFModule,
    ExcelModule,
    LabelModule,
    SchedulerModule,
    EditorModule,
    FileSelectModule,
    ChartsModule,
    ChartModule,
    IntlModule,
    DateInputsModule,
    InputsModule,
    NotificationModule
];


const COMPONENTS = [
    HeaderComponent,
    FooterComponent,
    OneRowLayoutComponent,
    TwoRowsLayoutComponent,
    ThreeRowsLayoutComponent,
    ThreeRowsDrawerLayoutComponent
];
const PIPES = [];

@NgModule({
    imports: [CommonModule, ...CORE_MODULES, RouterModule],
    exports: [CommonModule, ...PIPES, ...COMPONENTS, CORE_MODULES],
    declarations: [...COMPONENTS, ...PIPES],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
/**
 * Singleton module
 */
export class ThemeModule {
    static forRoot(providers = []): ModuleWithProviders<ThemeModule> {
        return {
            ngModule: ThemeModule,
            providers: [
                ...providers,
                // { provide: MessageService, useClass: CustomMessagesService },
                // { provide: LOCALE_ID, useValue: 'zh-TW' }
            ],
        };
    }
}

