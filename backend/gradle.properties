# -Xmx: è¨­ç½® JVM æå¤§å å§å­å¤§å°ï¼é è¨­çº 2GBï¼ä¹å¯ä»¥è¨­ç½®çº 4GB ææ´å¤§
# -XX:MaxPermSize=512mè¨­ç½®æ°¸ä¹ä»£çæå¤§å§å­å¤§å°ï¼åé©ç¨æ¼ Java 8 åä»¥ä¸çæ¬ï¼
# -XX:+HeapDumpOnOutOfMemoryErroråç¨å§å­æº¢åºæçæå å§å­å¿«ç§
# -Dfile.encoding=UTF-8: å¼·å¶ JVM ä½¿ç¨ UTF-8 ç·¨ç¢¼æ ¼å¼
org.gradle.jvmargs=-Xmx8192m -XX:MaxPermSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#ç¦ç¨ Gradle Daemon
#org.gradle.daemon=false
#org.gradle.caching=true
#org.gradle.parallel=true
#org.gradle.incremental=true
