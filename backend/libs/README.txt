安裝方式:

Maven:
1. 使用 mvn 直接安裝
mvn install:install-file -Dfile=IBM_Common_Component.jar -DgroupId=com.ibm.mq -DartifactId=IBM_Common_Component -Dversion=1.0 -Dpackaging=jar
mvn install:install-file -Dfile=ojdbc7.jar -DgroupId=com.oracle -DartifactId=ojdbc7 -Dversion=12.1.0.2 -Dpackaging=jar

2. 設定 localrepository, 包括完整的 DgroupId,artifactId,version以及 jar, pom檔

PS: scope:system + systemPath方式不會自動把external jar打包至 war


Gradle:
以 fileTree(dir: 'libs', include: '*.jar') 自動安裝, 無需額外處理
