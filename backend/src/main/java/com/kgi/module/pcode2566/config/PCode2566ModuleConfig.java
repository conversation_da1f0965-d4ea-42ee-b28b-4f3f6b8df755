package com.kgi.module.pcode2566.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * PCode2566 模組配置
 * 確保 Spring 能夠掃描到 PCode2566 相關的組件
 */
@Configuration
@ComponentScan(basePackages = "com.kgi.module.pcode2566")
@EnableJpaRepositories(
    basePackages = "com.kgi.module.pcode2566.infrastructure.repository",
    considerNestedRepositories = true
)
public class PCode2566ModuleConfig {
    // 配置類，用於確保 Spring 正確掃描 PCode2566 模組的所有組件
}