package com.kgi.module.pcode2566.infrastructure.external;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 錯誤次數計數器服務
 * 用於管理驗證失敗次數
 */
@Slf4j
@Service
public class ErrorCounterService {
    
    private final Map<String, Map<String, Integer>> counters = new ConcurrentHashMap<>();
    
    /**
     * 增加錯誤次數
     * @param uniqId 案件編號
     * @param type 錯誤類型
     */
    public void incrementErrorCount(String uniqId, String type) {
        log.info("Incrementing error count for uniqId: {}, type: {}", uniqId, type);
        
        counters.computeIfAbsent(uniqId, k -> new ConcurrentHashMap<>())
                .merge(type, 1, Integer::sum);
    }
    
    /**
     * 取得錯誤次數
     * @param uniqId 案件編號
     * @param type 錯誤類型
     * @return 錯誤次數
     */
    public Integer getErrorCount(String uniqId, String type) {
        Map<String, Integer> userCounters = counters.get(uniqId);
        if (userCounters == null) {
            return 0;
        }
        
        Integer count = userCounters.get(type);
        return count != null ? count : 0;
    }
    
    /**
     * 重設錯誤次數
     * @param uniqId 案件編號
     * @param type 錯誤類型
     */
    public void resetErrorCount(String uniqId, String type) {
        log.info("Resetting error count for uniqId: {}, type: {}", uniqId, type);
        
        Map<String, Integer> userCounters = counters.get(uniqId);
        if (userCounters != null) {
            userCounters.remove(type);
            
            // 如果使用者的所有計數器都為空，移除整個條目
            if (userCounters.isEmpty()) {
                counters.remove(uniqId);
            }
        }
    }
    
    /**
     * 取得所有錯誤次數 (用於除錯)
     * @param uniqId 案件編號
     * @return 所有錯誤次數
     */
    public Map<String, Integer> getAllErrorCounts(String uniqId) {
        return counters.getOrDefault(uniqId, new ConcurrentHashMap<>());
    }
}