package com.kgi.module.pcode2566.infrastructure.external;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 銀行 API 服務
 * 負責與外部銀行 API 的實際連接
 */
@Slf4j
@Service
public class BankApiService {
    
    @Value("${kgi.bank-api.kgi.url:http://localhost:8080}")
    private String kgiApiUrl;
    
    @Value("${kgi.bank-api.other.url:http://localhost:8080}")
    private String otherBankApiUrl;
    
    /**
     * 呼叫凱基銀行 API
     * @param endpoint API 端點
     * @param request 請求資料
     * @return API 回應
     */
    public Map<String, Object> callKGIApi(String endpoint, Map<String, Object> request) {
        log.info("Calling KGI API: {}", endpoint);
        
        // TODO: 實際的 HTTP 請求實作
        // 目前返回模擬回應
        Map<String, Object> response = new HashMap<>();
        response.put("returnCode", "0000");
        response.put("message", "模擬凱基銀行驗證成功");
        
        return response;
    }
    
    /**
     * 呼叫他行 API
     * @param endpoint API 端點
     * @param request 請求資料
     * @return API 回應
     */
    public Map<String, Object> callOtherBankApi(String endpoint, Map<String, Object> request) {
        log.info("Calling other bank API: {}", endpoint);
        
        // TODO: 實際的 HTTP 請求實作
        // 目前返回模擬回應
        Map<String, Object> response = new HashMap<>();
        response.put("verifyCodeByVerifyType", "0");
        response.put("verifyCodeByAccountState", "0");
        response.put("verifyCodeByOpenAccountState", "0");
        
        return response;
    }
}