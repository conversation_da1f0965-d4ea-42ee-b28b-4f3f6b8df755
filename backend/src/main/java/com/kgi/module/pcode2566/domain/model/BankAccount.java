package com.kgi.module.pcode2566.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 銀行帳戶值物件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankAccount {
    
    private String bankCode;
    private String bankName;
    private String accountNumber;
    
    /**
     * 格式化帳號為 16 位數
     */
    public String getFormattedAccountNumber() {
        if (accountNumber == null) {
            return null;
        }
        
        // 移除非數字字元
        String cleanAccount = accountNumber.replaceAll("[^0-9]", "");
        
        // 補零到 16 位
        if (cleanAccount.length() < 16) {
            return String.format("%016d", Long.parseLong(cleanAccount));
        }
        
        return cleanAccount;
    }
    
    /**
     * 驗證帳號格式
     */
    public boolean isValidAccountNumber() {
        if (accountNumber == null || accountNumber.isEmpty()) {
            return false;
        }
        
        String cleanAccount = accountNumber.replaceAll("[^0-9]", "");
        return cleanAccount.length() > 0 && cleanAccount.length() <= 16;
    }
    
    /**
     * 是否為凱基銀行
     */
    public boolean isKgiBank() {
        return "012".equals(bankCode);
    }
}