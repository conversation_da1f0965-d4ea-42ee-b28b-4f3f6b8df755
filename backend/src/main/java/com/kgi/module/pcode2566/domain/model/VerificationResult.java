package com.kgi.module.pcode2566.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 驗證結果值物件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationResult {
    
    private boolean success;
    private String code;
    private String verificationCode;
    private String message;
    private VerificationStatus identityStatus;
    private VerificationStatus accountStatus;
    private AccountOpenStatus openStatus;
    private boolean identityVerified;
    private boolean accountVerified;
    private boolean phoneVerified;
    private String accountType;
    private boolean phoneChangedIn90Days;
    private String rawResponse;
    
    /**
     * 驗證是否成功
     */
    public boolean isSuccess() {
        // 優先使用 success 欄位，若未設定則根據 verificationCode 判斷
        if (success) {
            return true;
        }
        // 0000 = 成功, 9901 = D2產品特殊成功
        String checkCode = code != null ? code : verificationCode;
        return "0000".equals(checkCode) || "9901".equals(checkCode);
    }
    
    /**
     * 是否為身分驗證失敗
     */
    public boolean isIdentityVerificationFailed() {
        return identityStatus == VerificationStatus.FAILED;
    }
    
    /**
     * 是否為帳戶狀態異常
     */
    public boolean isAccountStatusAbnormal() {
        return accountStatus == VerificationStatus.FAILED;
    }
    
    /**
     * 是否為數位帳戶
     */
    public boolean isDigitalAccount() {
        return openStatus == AccountOpenStatus.DIGITAL;
    }
    
    /**
     * 建立成功結果
     */
    public static VerificationResult success(String message) {
        return VerificationResult.builder()
                .success(true)
                .code("0000")
                .verificationCode("0000")
                .message(message)
                .identityStatus(VerificationStatus.SUCCESS)
                .accountStatus(VerificationStatus.SUCCESS)
                .openStatus(AccountOpenStatus.BRANCH)
                .identityVerified(true)
                .accountVerified(true)
                .phoneVerified(true)
                .build();
    }
    
    /**
     * 建立失敗結果
     */
    public static VerificationResult failure(String code, String message) {
        return VerificationResult.builder()
                .success(false)
                .code(code)
                .verificationCode(code)
                .message(message)
                .identityStatus(VerificationStatus.FAILED)
                .accountStatus(VerificationStatus.FAILED)
                .identityVerified(false)
                .accountVerified(false)
                .phoneVerified(false)
                .build();
    }
}

/**
 * 驗證狀態枚舉
 */
enum VerificationStatus {
    SUCCESS("0", "成功"),
    FAILED("1", "失敗");
    
    private final String code;
    private final String description;
    
    VerificationStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public static VerificationStatus fromCode(String code) {
        for (VerificationStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return FAILED;
    }
}

/**
 * 開戶狀態枚舉
 */
enum AccountOpenStatus {
    BRANCH("01", "臨櫃開立"),
    DIGITAL("02", "數位存款帳戶");
    
    private final String code;
    private final String description;
    
    AccountOpenStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public static AccountOpenStatus fromCode(String code) {
        for (AccountOpenStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return BRANCH;
    }
}