package com.kgi.module.pcode2566.infrastructure.repository;

import com.kgi.module.pcode2566.infrastructure.entity.AccountVerificationEntity;
import com.kgi.module.pcode2566.infrastructure.entity.AccountVerificationEntity.VerificationStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帳戶驗證 JPA Repository
 */
@Repository
public interface AccountVerificationJpaRepository extends JpaRepository<AccountVerificationEntity, Long> {
    
    /**
     * 根據 uniqId 查詢最新的驗證記錄
     */
    @Query("SELECT a FROM AccountVerificationEntity a WHERE a.uniqId = :uniqId ORDER BY a.createdAt DESC")
    Optional<AccountVerificationEntity> findLatestByUniqId(@Param("uniqId") String uniqId);
    
    /**
     * 根據 uniqId 和銀行代碼查詢
     */
    Optional<AccountVerificationEntity> findByUniqIdAndBankCode(String uniqId, String bankCode);
    
    /**
     * 根據 uniqId 查詢所有驗證記錄
     */
    List<AccountVerificationEntity> findByUniqIdOrderByCreatedAtDesc(String uniqId);
    
    /**
     * 統計錯誤次數
     */
    @Query("SELECT COUNT(a) FROM AccountVerificationEntity a WHERE a.uniqId = :uniqId AND a.status = 'FAILED' AND a.createdAt > :after")
    long countFailedByUniqIdAfter(@Param("uniqId") String uniqId, @Param("after") LocalDateTime after);
    
    /**
     * 查詢特定狀態的記錄
     */
    List<AccountVerificationEntity> findByStatus(VerificationStatus status);
    
    /**
     * 更新驗證狀態
     */
    @Modifying
    @Query("UPDATE AccountVerificationEntity a SET a.status = :status, a.updatedAt = :now WHERE a.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") VerificationStatus status, @Param("now") LocalDateTime now);
    
    /**
     * 根據身分證號和銀行代碼查詢
     */
    @Query("SELECT a FROM AccountVerificationEntity a WHERE a.idNumber = :idNumber AND a.bankCode = :bankCode ORDER BY a.createdAt DESC")
    List<AccountVerificationEntity> findByIdNumberAndBankCode(@Param("idNumber") String idNumber, @Param("bankCode") String bankCode);
    
    /**
     * 刪除過期資料
     */
    @Modifying
    @Query("DELETE FROM AccountVerificationEntity a WHERE a.createdAt < :before")
    int deleteOlderThan(@Param("before") LocalDateTime before);
}