package com.kgi.module.pcode2566.domain.repository;

import com.kgi.module.pcode2566.domain.model.AccountVerification;

import java.util.List;
import java.util.Optional;

/**
 * 帳戶驗證倉儲介面
 */
public interface AccountVerificationRepository {
    
    /**
     * 儲存驗證記錄
     */
    AccountVerification save(AccountVerification verification);
    
    /**
     * 根據 ID 查詢
     */
    Optional<AccountVerification> findById(Long id);
    
    /**
     * 根據 uniqId 查詢最新的驗證記錄
     */
    Optional<AccountVerification> findLatestByUniqId(String uniqId);
    
    /**
     * 根據 uniqId 查詢所有驗證記錄
     */
    List<AccountVerification> findByUniqId(String uniqId);
    
    /**
     * 統計錯誤次數
     */
    int countErrorsByUniqIdAndBankAccount(String uniqId, String bankCode, String accountNumber);
    
    /**
     * 檢查最近是否有成功的驗證
     */
    boolean hasSuccessfulVerificationRecently(String uniqId, String bankCode, String accountNumber, int minutes);
}