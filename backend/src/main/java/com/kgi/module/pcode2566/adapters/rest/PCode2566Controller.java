package com.kgi.module.pcode2566.adapters.rest;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.core.domain.code.ReturnCode;
import com.kgi.core.util.OWASPSecurity;
import com.kgi.core.util.SystemConst;
import com.kgi.module.pcode2566.application.dto.request.PCode2566VO;
import com.kgi.module.pcode2566.application.dto.response.PCode2566Resp;
import com.kgi.module.pcode2566.application.facade.PCode2566Facade;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.owasp.esapi.errors.ValidationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * PCode2566 Controller
 * 提供銀行帳戶驗證功能
 */
@Slf4j
@Controller
@CrossOrigin
@RequestMapping("/api/pcode2566")
public class PCode2566Controller {
    
    @Autowired
    private PCode2566Facade pCode2566Facade;
    
    /**
     * 一般 pCode2566 驗證
     * 用於驗證用戶提供的銀行帳戶資訊是否正確
     */
    @PostMapping(value = "/verify", produces = {"application/json;charset=UTF-8"})
    @ResponseBody
    public ResponseEntity<ResponseVO> verifyBankAccount(@RequestBody PCode2566VO vo) {
        ResponseVO res = new ResponseVO();
        
        try {
            PCode2566VO inputVo = (PCode2566VO) OWASPSecurity.getAntiXSSObject(vo);
            
            log.info("verifyBankAccount bank = {}, account = {}", inputVo.getBank(), inputVo.getAccount());
            
            // 使用 Facade 處理
            PCode2566Resp resp = pCode2566Facade.checkPCode2566(inputVo);
            
            if ("0000".equals(resp.getPcode2566CheckCode()) || "9901".equals(resp.getPcode2566CheckCode())) {
                res.setRtnCode(ReturnCode.SUCCESS.getRtnCode());
                res.setRtnMessage("驗證成功");
            } else {
                res.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
                res.setRtnMessage(resp.getPcode2566Message());
            }
            res.setRtnObj(resp);
            
        } catch (ValidationException e) {
            log.info("verifyBankAccount OWASPSecurity Fail");
            log.info(ExceptionUtils.getStackTrace(e));
            res.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
            res.setRtnMessage("請檢查輸入資料");
        } catch (Exception ex) {
            log.error("驗證銀行帳戶失敗", ex);
            res.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
            res.setRtnMessage("驗證失敗，請稍後再試");
        }
        
        return new ResponseEntity<>(res, HttpStatus.OK);
    }
    
}