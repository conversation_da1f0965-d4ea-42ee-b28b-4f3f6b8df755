package com.kgi.module.pcode2566.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 帳戶驗證實體
 * 對應資料庫表 account_verification
 */
@Entity
@Table(name = "account_verification")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountVerificationEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "verification_id", unique = true, nullable = false, length = 50)
    private String verificationId;
    
    @Column(name = "uniq_id", nullable = false, length = 50)
    private String uniqId;
    
    @Column(name = "uniq_type", length = 20)
    private String uniqType;
    
    @Column(name = "bank_code", nullable = false, length = 3)
    private String bankCode;
    
    @Column(name = "bank_name", length = 50)
    private String bankName;
    
    @Column(name = "account_number", nullable = false, length = 20)
    private String accountNumber;
    
    @Column(name = "id_number", nullable = false, length = 10)
    private String idNumber;
    
    @Column(name = "birthday", nullable = false, length = 8)
    private String birthday;
    
    @Column(name = "phone_number", nullable = false, length = 10)
    private String phoneNumber;
    
    @Column(name = "verification_code", length = 10)
    private String verificationCode;
    
    @Column(name = "verification_message", length = 255)
    private String verificationMessage;
    
    @Column(name = "identity_status", length = 1)
    private String identityStatus;
    
    @Column(name = "account_status", length = 1)
    private String accountStatus;
    
    @Column(name = "open_status", length = 2)
    private String openStatus;
    
    @Column(name = "phone_changed_within_90_days")
    private Boolean phoneChangedWithin90Days;
    
    @Column(name = "error_count")
    private Integer errorCount;
    
    @Column(name = "raw_response", columnDefinition = "TEXT")
    private String rawResponse;
    
    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private VerificationStatus status = VerificationStatus.PENDING;
    
    @Column(name = "pcode_uid", length = 20)
    private String pcodeUid;
    
    @Column(name = "request_ip", length = 50)
    private String requestIp;
    
    @Column(name = "identity_verified")
    private boolean identityVerified;
    
    @Column(name = "account_verified")
    private boolean accountVerified;
    
    @Column(name = "phone_verified")
    private boolean phoneVerified;
    
    @Column(name = "account_type", length = 20)
    private String accountType;
    
    @Column(name = "phone_changed_in_90_days")
    private boolean phoneChangedIn90Days;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (errorCount == null) {
            errorCount = 0;
        }
        if (status == null) {
            status = VerificationStatus.PENDING;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 驗證狀態列舉
     */
    public enum VerificationStatus {
        PENDING,    // 待驗證
        SUCCESS,    // 驗證成功
        FAILED,     // 驗證失敗
        ERROR       // 系統錯誤
    }
}