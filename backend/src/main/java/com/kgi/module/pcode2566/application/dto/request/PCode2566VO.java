package com.kgi.module.pcode2566.application.dto.request;

import com.kgi.core.annotation.CheckNullAndEmpty;
import lombok.Data;

@Data
public class PCode2566VO {

    @CheckNullAndEmpty
    private String phone = "";
    @CheckNullAndEmpty
    private String bank = "";

    private String bankName = "";
    @CheckNullAndEmpty
    private String account = "";

    private String branchId = "";

    private String idno = "";
    private String pcodeUid = "";
    private String birthday = "";

    private String tmnlId = "";
    private String tmnlType = "";

    private String ESBClientId;
    private String ESBClientPAZZD;
    private String ESBTimestamp;
    private String ESBChannel;
    private String ESBQueueReceiveName;
    private String userAgent;
    
    // 新增用於 IBR 的欄位
    private String uniqId = "";
    private String uniqType = "";
}