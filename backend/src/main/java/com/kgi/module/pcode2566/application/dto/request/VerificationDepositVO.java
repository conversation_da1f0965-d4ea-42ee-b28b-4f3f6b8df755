package com.kgi.module.pcode2566.application.dto.request;

import lombok.Data;

@Data
public class VerificationDepositVO {
    /** 案編 */
    private String uniqId;
    
    /** 案件類型 */
    private String uniqType;

    /** 驗身-信用卡銀行別 */
    private String deposit;

    /** 驗身-信用卡卡號 */
    private String creditCard;

    /**
     * 驗身-存款帳戶銀行別
     */
    private String depositBank;

    /**
     * 驗身-銀行帳號
     */
    private String bankAccount;

    /**
     * 驗身-留存銀行電話號碼
     */
    private String bankPhone;

    /** 驗身-信用卡卡號 */
    private String creditCardNumber;

    /** 驗身-信用有效期 */
    private String validityPeriod;

    /** 驗身-數二帳戶驗身-選擇分行 */
    private String branchBank;

    /** 驗身-90天手機異動-銀行無法檢核
     * 要可以下一步，故需要開關控制跳過電文流程
     */
    private String pCodeResEmpty;

    /** 驗身-房屋貸款-紀錄手機型號 */
    private String phoneModel;
}