# PCode2566 模組

本模組實現了 pCode2566 銀行帳戶驗證功能，用於驗證用戶提供的銀行帳戶資訊是否正確。

## 功能概述

### 主要功能
1. **身份驗證**：確認身分證號、生日是否與銀行紀錄相符
2. **帳戶驗證**：確認帳號狀態是否正常（未掛失、未凍結）
3. **手機驗證**：確認手機號碼是否與銀行留存資料相符
4. **開戶狀態**：確認帳戶開戶狀態

### 支援銀行
- 凱基銀行 (012)：使用 BNS00040007 電文
- 他行：使用 pCode2566 API

## 模組架構

```
com.kgi.module.pcode2566/
├── adapters/rest/           # REST API 控制器
│   └── PCode2566Controller.java
├── application/
│   ├── dto/
│   │   ├── request/         # 請求 DTO
│   │   └── response/        # 回應 DTO
│   ├── facade/              # 應用服務門面
│   └── usecase/             # 業務用例
├── domain/
│   ├── model/               # 領域模型
│   └── service/             # 領域服務
├── infrastructure/
│   └── external/            # 外部服務整合
└── config/                  # 模組配置
```

## API 端點

### 1. 一般 pCode2566 驗證
```
POST /api/pcode2566/verify
```

**請求範例：**
```json
{
  "bank": "012",
  "account": "****************",
  "phone": "**********",
  "idno": "A*********",
  "birthday": "********",
  "pcodeUid": "5980",
  "uniqId": "TEST123456",
  "uniqType": "IBR"
}
```

**回應範例：**
```json
{
  "rtnCode": "0000",
  "rtnMessage": "驗證成功",
  "rtnObj": {
    "pcode2566CheckCode": "0000",
    "pcode2566Message": "驗證成功",
    "authErrCount": 0
  }
}
```


## 錯誤處理

### 錯誤次數限制
- 每個 uniqId 最多允許 3 次驗證失敗
- 超過限制後會回傳「驗證失敗次數超過限制」錯誤

### 常見錯誤碼

#### 凱基銀行錯誤碼
| 錯誤碼 | 說明 |
|--------|------|
| 0000 | 驗證成功 |
| 1000 | 帳號長度錯誤 |
| 1001 | 存摺已掛失 |
| 1002 | 印鑑已掛失 |
| 1003 | 帳號不存在 |
| 2002 | 身分證或生日不符 |
| 2004 | 手機號碼不符 |
| 9901 | D2產品特殊成功 |

#### 他行驗證碼
| 驗證類型 | 碼值 | 說明 |
|----------|------|------|
| 身份驗證碼 | 0 | 身份驗證通過 |
| 身份驗證碼 | 1 | 身份驗證失敗 |
| 帳戶狀態碼 | 0 | 帳戶狀態正常 |
| 帳戶狀態碼 | 1 | 帳戶狀態異常 |
| 開戶狀態碼 | 0 | 開戶狀態正常 |
| 開戶狀態碼 | 1 | 開戶狀態異常 |

## 配置設定

### application.yml 配置
```yaml
kgi:
  useMockupData: true  # 是否使用模擬資料
  pcode2566:
    enabled: true
    timeout: 30000
    retry-count: 2
    
  bank-api:
    kgi:
      url: ${KGI_BANK_API_URL:http://bank-api.kgi.com}
      auth-key: ${KGI_BANK_AUTH_KEY}
    other:
      url: ${OTHER_BANK_API_URL:http://pcode2566-api.com}
      auth-key: ${OTHER_BANK_AUTH_KEY}
```

## 使用方式

### 1. 在 Controller 中注入
```java
@Autowired
private PCode2566Facade pCode2566Facade;

// 使用
PCode2566Resp response = pCode2566Facade.checkPCode2566(request);
```

### 2. 直接使用 UseCase
```java
@Autowired
private VerifyBankAccountUseCase verifyBankAccountUseCase;

// 使用
PCode2566Resp response = verifyBankAccountUseCase.execute(request);
```

## 測試

### 測試檔案位置
- HTTP 測試範例：`src/test/java/com/kgi/module/pcode2566/PCode2566TestExample.http`

### 測試用例
1. 凱基銀行成功驗證
2. 他行成功驗證
3. 各種錯誤情況測試
4. 錯誤次數限制測試

## 開發注意事項

### Mock 模式
- 當 `kgi.useMockupData=true` 時，使用模擬回應
- 測試帳號：以 `9999` 結尾會回傳「帳號不存在」錯誤
- 測試手機：`**********` 會回傳「手機號碼不符」錯誤

### 帳號格式化
- 系統會自動將帳號格式化為 16 位數
- 例如：`*********` → `****************`

### 擴充說明
1. **新增銀行支援**：在 `BankVerificationService` 中新增對應的驗證邏輯
2. **自訂驗證規則**：擴充 `PCode2566VO` 並修改驗證邏輯
3. **特殊處理**：在 `PCode2566Service` 中加入產品別判斷邏輯

## 相依性

### 核心相依性
- Spring Boot 3.x
- Core 模組（提供基礎設施）

### 外部整合
- 銀行 API 服務
- 錯誤計數器服務

---

最後更新：2025/6/8