package com.kgi.module.pcode2566.infrastructure.repository.impl;

import com.kgi.module.pcode2566.domain.model.AccountVerification;
import com.kgi.module.pcode2566.domain.repository.AccountVerificationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 帳戶驗證倉儲實作
 * 暫時使用記憶體存儲，未來可改為資料庫實作
 */
@Slf4j
@Repository
public class AccountVerificationRepositoryImpl implements AccountVerificationRepository {
    
    // 使用執行緒安全的 Map 存儲
    private final Map<Long, AccountVerification> storage = new ConcurrentHashMap<>();
    private Long idCounter = 1L;
    
    @Override
    public AccountVerification save(AccountVerification verification) {
        if (verification.getId() == null) {
            verification.setId(idCounter++);
        }
        
        storage.put(verification.getId(), verification);
        
        log.info("Saved account verification: id={}, uniqId={}", 
                verification.getId(), verification.getUniqId());
        
        return verification;
    }
    
    @Override
    public Optional<AccountVerification> findById(Long id) {
        return Optional.ofNullable(storage.get(id));
    }
    
    @Override
    public Optional<AccountVerification> findLatestByUniqId(String uniqId) {
        return storage.values().stream()
                .filter(v -> uniqId.equals(v.getUniqId()))
                .max(Comparator.comparing(AccountVerification::getCreatedAt));
    }
    
    @Override
    public List<AccountVerification> findByUniqId(String uniqId) {
        return storage.values().stream()
                .filter(v -> uniqId.equals(v.getUniqId()))
                .sorted(Comparator.comparing(AccountVerification::getCreatedAt).reversed())
                .collect(Collectors.toList());
    }
    
    @Override
    public int countErrorsByUniqIdAndBankAccount(String uniqId, String bankCode, String accountNumber) {
        return (int) storage.values().stream()
                .filter(v -> uniqId.equals(v.getUniqId()) && 
                           v.getBankAccount() != null &&
                           bankCode.equals(v.getBankAccount().getBankCode()) &&
                           accountNumber.equals(v.getBankAccount().getAccountNumber()) &&
                           v.getResult() != null &&
                           !v.getResult().isSuccess())
                .count();
    }
    
    @Override
    public boolean hasSuccessfulVerificationRecently(String uniqId, String bankCode, String accountNumber, int minutes) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(minutes);
        
        return storage.values().stream()
                .anyMatch(v -> uniqId.equals(v.getUniqId()) && 
                             v.getBankAccount() != null &&
                             bankCode.equals(v.getBankAccount().getBankCode()) &&
                             accountNumber.equals(v.getBankAccount().getAccountNumber()) &&
                             v.getResult() != null &&
                             v.getResult().isSuccess() &&
                             v.getCreatedAt() != null &&
                             v.getCreatedAt().isAfter(cutoffTime));
    }
}