package com.kgi.module.pcode2566.application.facade;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.pcode2566.application.dto.request.PCode2566VO;
import com.kgi.module.pcode2566.application.dto.response.PCode2566Resp;
import com.kgi.module.pcode2566.domain.service.PCode2566Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PCode2566 Facade
 * 負責協調 pCode2566 相關的業務邏輯
 */
@Slf4j
@Service
public class PCode2566Facade {
    
    @Autowired
    private PCode2566Service pCode2566Service;
    
    /**
     * 一般 pCode2566 驗證
     * @param vo 驗證請求資料
     * @return 驗證結果
     */
    public PCode2566Resp checkPCode2566(PCode2566VO vo) throws Exception {
        log.info("PCode2566Facade checkPCode2566 - bank: {}, account: {}", vo.getBank(), vo.getAccount());
        
        try {
            // 執行驗證
            return pCode2566Service.checkPCode2566(vo);
        } catch (BusinessException e) {
            log.error("Business error in checkPCode2566", e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in checkPCode2566", e);
            throw new BusinessException("驗證過程發生錯誤");
        }
    }
    
}