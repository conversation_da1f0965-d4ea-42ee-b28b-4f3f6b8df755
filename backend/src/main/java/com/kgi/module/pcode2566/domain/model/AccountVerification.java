package com.kgi.module.pcode2566.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 帳戶驗證聚合根
 * 代表一次完整的 pCode2566 驗證流程
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountVerification {
    
    private Long id;
    private String verificationId;
    private String uniqId;
    private String uniqType;
    private BankAccount bankAccount;
    private CustomerIdentity customerIdentity;
    private VerificationResult result;
    private PhoneChangeStatus phoneChangeStatus;
    private String pcodeUid;
    private String requestIp;
    private Integer errorCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime verifiedAt;
    
    /**
     * 執行驗證
     */
    public void verify(VerificationResult verificationResult) {
        this.result = verificationResult;
        this.verifiedAt = LocalDateTime.now();
        
        // 如果驗證失敗，增加錯誤次數
        if (!verificationResult.isSuccess()) {
            this.errorCount = (this.errorCount == null ? 0 : this.errorCount) + 1;
        }
    }
    
    /**
     * 是否超過錯誤次數限制
     */
    public boolean isExceedErrorLimit(int maxErrorCount) {
        return this.errorCount != null && this.errorCount >= maxErrorCount;
    }
    
    /**
     * 檢查是否為凱基銀行
     */
    public boolean isKgiBank() {
        return bankAccount != null && "012".equals(bankAccount.getBankCode());
    }
    
    /**
     * 檢查手機是否在 90 天內有異動
     */
    public boolean hasPhoneChangedRecently() {
        return phoneChangeStatus != null && phoneChangeStatus.isChangedWithin90Days();
    }
}