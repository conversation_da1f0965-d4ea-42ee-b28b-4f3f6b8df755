package com.kgi.module.pcode2566.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 手機異動狀態值物件
 * 記錄手機號碼在 90 天內是否有異動
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PhoneChangeStatus {
    
    private boolean changedWithin90Days;
    private LocalDate changeDate;
    private String previousPhoneNumber;
    private String currentPhoneNumber;
    
    /**
     * 從 pCode2566 回應解析手機異動狀態
     * NOTE 欄位第 90-97 位
     */
    public static PhoneChangeStatus parseFromNote(String noteField) {
        if (noteField == null || noteField.length() < 97) {
            return PhoneChangeStatus.builder()
                    .changedWithin90Days(false)
                    .build();
        }
        
        String phoneChangeCode = noteField.substring(90, 97);
        boolean changed = "000001Y".equals(phoneChangeCode);
        
        return PhoneChangeStatus.builder()
                .changedWithin90Days(changed)
                .build();
    }
    
    /**
     * 是否需要阻擋（D3 產品特殊邏輯）
     */
    public boolean shouldBlockForD3() {
        return changedWithin90Days;
    }
}