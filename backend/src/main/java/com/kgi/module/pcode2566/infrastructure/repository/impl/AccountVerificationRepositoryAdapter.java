package com.kgi.module.pcode2566.infrastructure.repository.impl;

import com.kgi.core.config.RepositoryConfig;
import com.kgi.module.pcode2566.domain.model.AccountVerification;
import com.kgi.module.pcode2566.domain.model.BankAccount;
import com.kgi.module.pcode2566.domain.model.CustomerIdentity;
import com.kgi.module.pcode2566.domain.model.VerificationResult;
import com.kgi.module.pcode2566.domain.repository.AccountVerificationRepository;
import com.kgi.module.pcode2566.infrastructure.entity.AccountVerificationEntity;
import com.kgi.module.pcode2566.infrastructure.entity.AccountVerificationEntity.VerificationStatus;
import com.kgi.module.pcode2566.infrastructure.repository.AccountVerificationJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 帳戶驗證倉儲適配器
 * 根據配置自動切換 Memory 或 JPA 實作
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class AccountVerificationRepositoryAdapter implements AccountVerificationRepository {
    
    private final RepositoryConfig repositoryConfig;
    private final AccountVerificationJpaRepository jpaRepository;
    
    // Memory 模式存儲
    private final Map<Long, AccountVerification> memoryStorage = new ConcurrentHashMap<>();
    private Long memoryIdCounter = 1L;
    
    @Override
    @Transactional
    public AccountVerification save(AccountVerification verification) {
        if (repositoryConfig.isJpaMode()) {
            return saveWithJpa(verification);
        } else {
            return saveWithMemory(verification);
        }
    }
    
    @Override
    public Optional<AccountVerification> findById(Long id) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findById(id)
                    .map(this::entityToDomain);
        } else {
            return Optional.ofNullable(memoryStorage.get(id));
        }
    }
    
    @Override
    public Optional<AccountVerification> findLatestByUniqId(String uniqId) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findLatestByUniqId(uniqId)
                    .map(this::entityToDomain);
        } else {
            return memoryStorage.values().stream()
                    .filter(v -> uniqId.equals(v.getUniqId()))
                    .max(Comparator.comparing(AccountVerification::getCreatedAt));
        }
    }
    
    @Override
    public List<AccountVerification> findByUniqId(String uniqId) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findByUniqIdOrderByCreatedAtDesc(uniqId).stream()
                    .map(this::entityToDomain)
                    .collect(Collectors.toList());
        } else {
            return memoryStorage.values().stream()
                    .filter(v -> uniqId.equals(v.getUniqId()))
                    .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                    .collect(Collectors.toList());
        }
    }
    
    @Override
    public int countErrorsByUniqIdAndBankAccount(String uniqId, String bankCode, String accountNumber) {
        if (repositoryConfig.isJpaMode()) {
            // 計算 24 小時內的錯誤次數
            LocalDateTime after = LocalDateTime.now().minusHours(24);
            return (int) jpaRepository.countFailedByUniqIdAfter(uniqId, after);
        } else {
            long count = memoryStorage.values().stream()
                    .filter(v -> uniqId.equals(v.getUniqId()) && 
                               bankCode.equals(v.getBankAccount() != null ? v.getBankAccount().getBankCode() : null) &&
                               accountNumber.equals(v.getBankAccount() != null ? v.getBankAccount().getAccountNumber() : null) &&
                               v.getResult() != null &&
                               !v.getResult().isSuccess())
                    .count();
            return (int) count;
        }
    }
    
    @Override
    public boolean hasSuccessfulVerificationRecently(String uniqId, String bankCode, String accountNumber, int minutes) {
        LocalDateTime after = LocalDateTime.now().minusMinutes(minutes);
        
        if (repositoryConfig.isJpaMode()) {
            List<AccountVerificationEntity> verifications = jpaRepository.findByUniqIdOrderByCreatedAtDesc(uniqId);
            return verifications.stream()
                    .filter(v -> v.getCreatedAt().isAfter(after))
                    .filter(v -> bankCode.equals(v.getBankCode()))
                    .filter(v -> accountNumber.equals(v.getAccountNumber()))
                    .anyMatch(v -> v.getStatus() == VerificationStatus.SUCCESS);
        } else {
            return memoryStorage.values().stream()
                    .filter(v -> uniqId.equals(v.getUniqId()))
                    .filter(v -> v.getCreatedAt().isAfter(after))
                    .filter(v -> v.getBankAccount() != null)
                    .filter(v -> bankCode.equals(v.getBankAccount().getBankCode()))
                    .filter(v -> accountNumber.equals(v.getBankAccount().getAccountNumber()))
                    .anyMatch(v -> v.getResult() != null && v.getResult().isSuccess());
        }
    }
    
    
    // Memory 模式儲存
    private AccountVerification saveWithMemory(AccountVerification verification) {
        if (verification.getId() == null) {
            verification.setId(memoryIdCounter++);
        }
        
        memoryStorage.put(verification.getId(), verification);
        
        log.info("Saved account verification in memory: id={}, uniqId={}", 
                verification.getId(), verification.getUniqId());
        
        return verification;
    }
    
    // JPA 模式儲存
    private AccountVerification saveWithJpa(AccountVerification verification) {
        AccountVerificationEntity entity = domainToEntity(verification);
        entity = jpaRepository.save(entity);
        
        log.info("Saved account verification in database: id={}, uniqId={}", 
                entity.getId(), entity.getUniqId());
        
        return entityToDomain(entity);
    }
    
    // Domain 轉 Entity
    private AccountVerificationEntity domainToEntity(AccountVerification domain) {
        AccountVerificationEntity entity = new AccountVerificationEntity();
        
        entity.setId(domain.getId());
        entity.setUniqId(domain.getUniqId());
        entity.setUniqType(domain.getUniqType());
        
        if (domain.getBankAccount() != null) {
            entity.setBankCode(domain.getBankAccount().getBankCode());
            entity.setAccountNumber(domain.getBankAccount().getAccountNumber());
        }
        
        if (domain.getCustomerIdentity() != null) {
            entity.setIdNumber(domain.getCustomerIdentity().getIdNumber());
            entity.setBirthday(domain.getCustomerIdentity().getBirthday());
            entity.setPhoneNumber(domain.getCustomerIdentity().getPhoneNumber());
        }
        
        entity.setPcodeUid(domain.getPcodeUid());
        
        if (domain.getResult() != null) {
            entity.setStatus(domain.getResult().isSuccess() ? 
                    VerificationStatus.SUCCESS : VerificationStatus.FAILED);
            entity.setVerificationCode(domain.getResult().getCode());
            entity.setVerificationMessage(domain.getResult().getMessage());
            entity.setIdentityVerified(domain.getResult().isIdentityVerified());
            entity.setAccountVerified(domain.getResult().isAccountVerified());
            entity.setPhoneVerified(domain.getResult().isPhoneVerified());
            entity.setAccountType(domain.getResult().getAccountType());
            entity.setPhoneChangedIn90Days(domain.getResult().isPhoneChangedIn90Days());
        } else {
            entity.setStatus(VerificationStatus.PENDING);
        }
        
        entity.setRequestIp(domain.getRequestIp());
        entity.setErrorCount(domain.getErrorCount());
        entity.setCreatedAt(domain.getCreatedAt());
        entity.setUpdatedAt(domain.getUpdatedAt());
        
        return entity;
    }
    
    // Entity 轉 Domain
    private AccountVerification entityToDomain(AccountVerificationEntity entity) {
        AccountVerification domain = new AccountVerification();
        
        domain.setId(entity.getId());
        domain.setUniqId(entity.getUniqId());
        domain.setUniqType(entity.getUniqType());
        
        if (entity.getBankCode() != null && entity.getAccountNumber() != null) {
            domain.setBankAccount(BankAccount.builder()
                    .bankCode(entity.getBankCode())
                    .accountNumber(entity.getAccountNumber())
                    .build());
        }
        
        if (entity.getIdNumber() != null) {
            domain.setCustomerIdentity(new CustomerIdentity(
                    entity.getIdNumber(),
                    entity.getBirthday(),
                    entity.getPhoneNumber()
            ));
        }
        
        domain.setPcodeUid(entity.getPcodeUid());
        
        if (entity.getVerificationCode() != null) {
            VerificationResult result = new VerificationResult();
            result.setSuccess(entity.getStatus() == VerificationStatus.SUCCESS);
            result.setCode(entity.getVerificationCode());
            result.setMessage(entity.getVerificationMessage());
            result.setIdentityVerified(entity.isIdentityVerified());
            result.setAccountVerified(entity.isAccountVerified());
            result.setPhoneVerified(entity.isPhoneVerified());
            result.setAccountType(entity.getAccountType());
            result.setPhoneChangedIn90Days(entity.isPhoneChangedIn90Days());
            domain.setResult(result);
        }
        
        domain.setRequestIp(entity.getRequestIp());
        domain.setErrorCount(entity.getErrorCount());
        domain.setCreatedAt(entity.getCreatedAt());
        domain.setUpdatedAt(entity.getUpdatedAt());
        
        return domain;
    }
    
    // 更新 Entity 的驗證結果
    private void updateEntityResult(AccountVerificationEntity entity, VerificationResult result) {
        entity.setStatus(result.isSuccess() ? VerificationStatus.SUCCESS : VerificationStatus.FAILED);
        entity.setVerificationCode(result.getCode());
        entity.setVerificationMessage(result.getMessage());
        entity.setIdentityVerified(result.isIdentityVerified());
        entity.setAccountVerified(result.isAccountVerified());
        entity.setPhoneVerified(result.isPhoneVerified());
        entity.setAccountType(result.getAccountType());
        entity.setPhoneChangedIn90Days(result.isPhoneChangedIn90Days());
        entity.setUpdatedAt(LocalDateTime.now());
    }
}