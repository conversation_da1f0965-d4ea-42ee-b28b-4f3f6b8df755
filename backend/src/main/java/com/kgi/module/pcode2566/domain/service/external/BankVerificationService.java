package com.kgi.module.pcode2566.domain.service.external;

import com.kgi.core.util.JsonUtil;
import com.kgi.module.pcode2566.application.dto.request.PCode2566VO;
import com.kgi.module.pcode2566.application.dto.response.PCode2566Resp;
import com.kgi.module.pcode2566.domain.model.PCode2566Dto;
import com.kgi.module.pcode2566.infrastructure.external.BankApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 銀行驗證服務
 * 負責與不同銀行的 API 整合
 */
@Slf4j
@Service
public class BankVerificationService {
    
    @Value("${kgi.useMockupData:true}")
    private boolean useMockupData;
    
    @Autowired(required = false)
    private BankApiService bankApiService;
    
    /**
     * 凱基銀行帳戶驗證 (BNS00040007)
     * @param vo 驗證請求
     * @return 驗證結果
     */
    public PCode2566Resp verifyKGIAccount(PCode2566VO vo) {
        log.info("verifyKGIAccount - account: {}", vo.getAccount());
        
        if (useMockupData || bankApiService == null) {
            return createMockKGIResponse(vo);
        }
        
        try {
            // 準備 BNS00040007 電文
            Map<String, Object> request = prepareBNS00040007Request(vo);
            
            // 呼叫銀行 API
            Map<String, Object> response = bankApiService.callKGIApi("/BNS00040007", request);
            
            // 解析回應
            return parseBNS00040007Response(response, vo);
        } catch (Exception e) {
            log.error("Error calling KGI API", e);
            return createErrorResponse("9999", "系統錯誤");
        }
    }
    
    /**
     * 他行帳戶驗證 (pCode2566)
     * @param vo 驗證請求
     * @return 驗證結果
     */
    public PCode2566Resp verifyOtherBankAccount(PCode2566VO vo) {
        log.info("verifyOtherBankAccount - bank: {}, account: {}", vo.getBank(), vo.getAccount());
        
        if (useMockupData || bankApiService == null) {
            return createMockOtherBankResponse(vo);
        }
        
        try {
            // 準備 pCode2566 請求
            Map<String, Object> request = preparePCode2566Request(vo);
            
            // 呼叫外部 API
            Map<String, Object> response = bankApiService.callOtherBankApi("/pCode2566/verify", request);
            
            // 解析三組驗證碼
            return parsePCode2566Response(response);
        } catch (Exception e) {
            log.error("Error calling other bank API", e);
            return createErrorResponse("9999", "系統錯誤");
        }
    }
    
    /**
     * 準備 BNS00040007 電文
     */
    private Map<String, Object> prepareBNS00040007Request(PCode2566VO vo) {
        Map<String, Object> request = new HashMap<>();
        request.put("idno", vo.getIdno());
        request.put("birthday", vo.getBirthday());
        request.put("phone", vo.getPhone());
        request.put("account", vo.getAccount());
        request.put("pcodeUid", vo.getPcodeUid());
        return request;
    }
    
    /**
     * 準備 pCode2566 請求
     */
    private Map<String, Object> preparePCode2566Request(PCode2566VO vo) {
        Map<String, Object> request = new HashMap<>();
        request.put("bankId", vo.getBank());
        request.put("accountNo", vo.getAccount());
        request.put("idno", vo.getIdno());
        request.put("birthday", vo.getBirthday());
        request.put("phone", vo.getPhone());
        request.put("pcodeUid", vo.getPcodeUid());
        return request;
    }
    
    /**
     * 解析 BNS00040007 回應
     */
    private PCode2566Resp parseBNS00040007Response(Map<String, Object> response, PCode2566VO vo) {
        PCode2566Resp resp = new PCode2566Resp();
        
        String returnCode = (String) response.get("returnCode");
        String message = (String) response.get("message");
        
        resp.setPcode2566CheckCode(returnCode);
        resp.setPcode2566Message(message);
        
        return resp;
    }
    
    /**
     * 解析 pCode2566 回應 (三組驗證碼)
     */
    private PCode2566Resp parsePCode2566Response(Map<String, Object> response) {
        PCode2566Resp resp = new PCode2566Resp();
        
        try {
            String verifyCodeByVerifyType = (String) response.get("verifyCodeByVerifyType");
            String verifyCodeByAccountState = (String) response.get("verifyCodeByAccountState");
            String verifyCodeByOpenAccountState = (String) response.get("verifyCodeByOpenAccountState");
            
            // 判斷驗證結果
            if ("0".equals(verifyCodeByVerifyType) && 
                "0".equals(verifyCodeByAccountState) && 
                "0".equals(verifyCodeByOpenAccountState)) {
                resp.setPcode2566CheckCode("0000");
                resp.setPcode2566Message("驗證成功");
            } else {
                resp.setPcode2566CheckCode("1000");
                resp.setPcode2566Message("驗證失敗");
            }
        } catch (Exception e) {
            log.error("Error parsing pCode2566 response", e);
            resp.setPcode2566CheckCode("9999");
            resp.setPcode2566Message("解析回應錯誤");
        }
        
        return resp;
    }
    
    /**
     * 建立測試用的凱基銀行回應
     */
    private PCode2566Resp createMockKGIResponse(PCode2566VO vo) {
        PCode2566Resp resp = new PCode2566Resp();
        
        // 簡單的測試邏輯
        if (vo.getAccount() != null && vo.getAccount().endsWith("9999")) {
            resp.setPcode2566CheckCode("1003");
            resp.setPcode2566Message("帳號不存在");
        } else if (vo.getPhone() != null && vo.getPhone().equals("**********")) {
            resp.setPcode2566CheckCode("2004");
            resp.setPcode2566Message("手機號碼不符");
        } else {
            resp.setPcode2566CheckCode("0000");
            resp.setPcode2566Message("驗證成功");
        }
        
        return resp;
    }
    
    /**
     * 建立測試用的他行回應
     */
    private PCode2566Resp createMockOtherBankResponse(PCode2566VO vo) {
        PCode2566Resp resp = new PCode2566Resp();
        
        // 簡單的測試邏輯
        if (vo.getAccount() != null && vo.getAccount().contains("8888")) {
            resp.setPcode2566CheckCode("1000");
            resp.setPcode2566Message("帳戶異常");
        } else {
            resp.setPcode2566CheckCode("0000");
            resp.setPcode2566Message("驗證成功");
        }
        
        return resp;
    }
    
    /**
     * 建立錯誤回應
     */
    private PCode2566Resp createErrorResponse(String code, String message) {
        PCode2566Resp resp = new PCode2566Resp();
        resp.setPcode2566CheckCode(code);
        resp.setPcode2566Message(message);
        return resp;
    }
}