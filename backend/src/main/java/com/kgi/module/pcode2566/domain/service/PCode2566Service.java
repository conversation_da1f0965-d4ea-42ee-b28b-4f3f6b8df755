package com.kgi.module.pcode2566.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.pcode2566.application.dto.request.PCode2566VO;
import com.kgi.module.pcode2566.application.dto.response.PCode2566Resp;
import com.kgi.module.pcode2566.domain.service.external.BankVerificationService;
import com.kgi.module.pcode2566.infrastructure.external.ErrorCounterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * PCode2566 核心業務服務
 * 負責協調銀行帳戶驗證的核心邏輯
 */
@Slf4j
@Service
public class PCode2566Service {
    
    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;
    
    @Autowired
    private BankVerificationService bankVerificationService;
    
    @Autowired(required = false)
    private ErrorCounterService errorCounterService;
    
    private static final String KGI_BANK_ID = "012"; // 凱基銀行代碼
    private static final int MAX_ERROR_COUNT = 3;
    
    /**
     * 一般 pCode2566 驗證
     * @param vo 驗證請求資料
     * @return 驗證結果
     */
    public PCode2566Resp checkPCode2566(PCode2566VO vo) throws Exception {
        log.info("checkPCode2566 - uniqId: {}, bank: {}", vo.getUniqId(), vo.getBank());
        
        // 檢查錯誤次數
        if (errorCounterService != null) {
            Integer errorCount = errorCounterService.getErrorCount(vo.getUniqId(), "pCode2566");
            if (errorCount != null && errorCount >= MAX_ERROR_COUNT) {
                throw new BusinessException("驗證失敗次數超過限制");
            }
        }
        
        // 格式化帳號 - 轉為16位數
        String formattedAccount = formatAccountNumber(vo.getAccount());
        vo.setAccount(formattedAccount);
        
        PCode2566Resp resp;
        
        // 根據銀行代碼選擇驗證方式
        if (KGI_BANK_ID.equals(vo.getBank())) {
            resp = bankVerificationService.verifyKGIAccount(vo);
        } else {
            resp = bankVerificationService.verifyOtherBankAccount(vo);
        }
        
        // 如果驗證失敗，增加錯誤次數
        if (!"0000".equals(resp.getPcode2566CheckCode()) && !"9901".equals(resp.getPcode2566CheckCode())) {
            if (errorCounterService != null) {
                errorCounterService.incrementErrorCount(vo.getUniqId(), "pCode2566");
            }
        }
        
        return resp;
    }
    
    /**
     * 格式化銀行帳號為16位數
     * @param account 原始帳號
     * @return 16位數格式的帳號
     */
    private String formatAccountNumber(String account) {
        if (account == null || account.isEmpty()) {
            return account;
        }
        
        try {
            String cleanAccount = account.replace("-", "");
            long accountNumber = Long.parseLong(cleanAccount);
            return String.format("%016d", accountNumber);
        } catch (NumberFormatException e) {
            log.warn("Unable to format account number: {}", account);
            return account;
        }
    }
}