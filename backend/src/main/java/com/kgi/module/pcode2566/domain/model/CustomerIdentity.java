package com.kgi.module.pcode2566.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客戶身份資訊值物件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerIdentity {
    
    private String idNumber;
    private String birthday;
    private String phoneNumber;
    
    /**
     * 驗證身分證號格式
     */
    public boolean isValidIdNumber() {
        if (idNumber == null || idNumber.length() != 10) {
            return false;
        }
        
        // 簡單驗證：第一個字元為英文，其餘為數字
        return idNumber.matches("^[A-Z][0-9]{9}$");
    }
    
    /**
     * 驗證生日格式 (YYYYMMDD)
     */
    public boolean isValidBirthday() {
        if (birthday == null || birthday.length() != 8) {
            return false;
        }
        
        return birthday.matches("^[0-9]{8}$");
    }
    
    /**
     * 驗證手機號碼格式
     */
    public boolean isValidPhoneNumber() {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }
        
        // 台灣手機號碼格式：09 開頭，共 10 碼
        return phoneNumber.matches("^09[0-9]{8}$");
    }
    
    /**
     * 遮罩身分證號（保留前 3 碼和最後 2 碼）
     */
    public String getMaskedIdNumber() {
        if (idNumber == null || idNumber.length() < 5) {
            return "***";
        }
        
        return idNumber.substring(0, 3) + "*****" + idNumber.substring(8);
    }
    
    /**
     * 遮罩手機號碼（保留前 4 碼和最後 3 碼）
     */
    public String getMaskedPhoneNumber() {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return "***";
        }
        
        return phoneNumber.substring(0, 4) + "***" + phoneNumber.substring(7);
    }
}