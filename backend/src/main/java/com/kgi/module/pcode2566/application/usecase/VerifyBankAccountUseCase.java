package com.kgi.module.pcode2566.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.pcode2566.application.dto.request.PCode2566VO;
import com.kgi.module.pcode2566.application.dto.response.PCode2566Resp;
import com.kgi.module.pcode2566.domain.service.PCode2566Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 驗證銀行帳戶 Use Case
 * 負責協調銀行帳戶驗證的業務流程
 */
@Slf4j
@Service
public class VerifyBankAccountUseCase {
    
    @Autowired
    private PCode2566Service pCode2566Service;
    
    /**
     * 執行銀行帳戶驗證
     * @param request 驗證請求
     * @return 驗證結果
     */
    public PCode2566Resp execute(PCode2566VO request) throws BusinessException {
        log.info("VerifyBankAccountUseCase execute - bank: {}, account: {}", 
                request.getBank(), request.getAccount());
        
        try {
            // 驗證輸入參數
            validateInput(request);
            
            // 執行驗證
            return pCode2566Service.checkPCode2566(request);
            
        } catch (BusinessException e) {
            log.error("Business error in bank account verification", e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in bank account verification", e);
            throw new BusinessException("銀行帳戶驗證失敗");
        }
    }
    
    /**
     * 驗證輸入參數
     * @param request 請求資料
     */
    private void validateInput(PCode2566VO request) throws BusinessException {
        if (request.getBank() == null || request.getBank().trim().isEmpty()) {
            throw new BusinessException("銀行代碼不能為空");
        }
        
        if (request.getAccount() == null || request.getAccount().trim().isEmpty()) {
            throw new BusinessException("銀行帳號不能為空");
        }
        
        if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
            throw new BusinessException("手機號碼不能為空");
        }
        
        if (request.getIdno() == null || request.getIdno().trim().isEmpty()) {
            throw new BusinessException("身分證號不能為空");
        }
        
        if (request.getBirthday() == null || request.getBirthday().trim().isEmpty()) {
            throw new BusinessException("生日不能為空");
        }
    }
}