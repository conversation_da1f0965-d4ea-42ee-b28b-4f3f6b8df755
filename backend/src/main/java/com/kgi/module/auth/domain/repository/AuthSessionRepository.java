package com.kgi.module.auth.domain.repository;

import com.kgi.module.auth.domain.model.AuthSession;

import java.util.List;
import java.util.Optional;

/**
 * 認證會話倉儲介面
 * 定義認證會話相關的資料存取操作
 */
public interface AuthSessionRepository {
    
    /**
     * 保存認證會話
     */
    AuthSession save(AuthSession authSession);
    
    /**
     * 根據會話ID查詢
     */
    Optional<AuthSession> findBySessionId(String sessionId);
    
    /**
     * 根據使用者識別號查詢活躍會話
     */
    List<AuthSession> findActiveSessionsByUser(String userIdentifier);
    
    /**
     * 根據使用者識別號查詢所有會話
     */
    List<AuthSession> findAllSessionsByUser(String userIdentifier);
    
    /**
     * 根據使用者識別號查詢過期會話
     */
    List<AuthSession> findExpiredSessionsByUser(String userIdentifier);
    
    /**
     * 查詢所有過期會話
     */
    List<AuthSession> findExpiredSessions();
    
    /**
     * 根據狀態查詢會話
     */
    List<AuthSession> findByStatus(AuthSession.SessionStatus status);
    
    /**
     * 根據 JWT Token 查詢會話
     */
    Optional<AuthSession> findByJwtToken(String jwtToken);
    
    /**
     * 根據 Refresh Token 查詢會話
     */
    Optional<AuthSession> findByRefreshToken(String refreshToken);
    
    /**
     * 根據客戶端IP查詢會話
     */
    List<AuthSession> findByClientIp(String clientIp);
    
    /**
     * 根據裝置ID查詢會話
     */
    List<AuthSession> findByDeviceId(String deviceId);
    
    /**
     * 根據登入方式查詢會話
     */
    List<AuthSession> findByLoginMethod(AuthSession.LoginMethod loginMethod);
    
    /**
     * 根據使用者類型查詢會話
     */
    List<AuthSession> findByUserType(AuthSession.UserType userType);
    
    /**
     * 根據會話ID刪除會話
     */
    void deleteBySessionId(String sessionId);
    
    /**
     * 檢查會話是否存在
     */
    boolean existsBySessionId(String sessionId);
    
    /**
     * 統計使用者的活躍會話數量
     */
    long countActiveSessionsByUser(String userIdentifier);
    
    /**
     * 統計各狀態的會話數量
     */
    long countByStatus(AuthSession.SessionStatus status);
    
    /**
     * 查詢即將過期的會話（指定時間內）
     */
    List<AuthSession> findSessionsNearExpiry(int thresholdMinutes);
    
    /**
     * 查詢長時間未活動的會話
     */
    List<AuthSession> findInactiveSessions(int inactiveMinutes);
    
    /**
     * 根據時間範圍查詢會話
     */
    List<AuthSession> findByCreatedTimeBetween(
            java.time.LocalDateTime startTime, 
            java.time.LocalDateTime endTime);
    
    /**
     * 查詢最近登入的會話
     */
    List<AuthSession> findRecentSessions(int limitHours, int limit);
    
    /**
     * 根據多個條件查詢會話
     */
    List<AuthSession> findByCriteria(
            String userIdentifier,
            AuthSession.UserType userType,
            AuthSession.SessionStatus status,
            AuthSession.LoginMethod loginMethod,
            String clientIp,
            int page,
            int size);
}