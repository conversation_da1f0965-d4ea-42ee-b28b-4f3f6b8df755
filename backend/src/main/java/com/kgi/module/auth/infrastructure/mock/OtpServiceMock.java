package com.kgi.module.auth.infrastructure.mock;

import com.kgi.core.service.mock.base.BaseMockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * OTP 服務 Mock 實作
 * 模擬 OTP 發送和驗證功能
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service("otpServiceMock")
public class OtpServiceMock extends BaseMockService {

    // 內部 OTP 資料結構
    private static class MockOtpData {
        String mobile;
        String otp;
        String purpose;
        LocalDateTime createTime;
        LocalDateTime expireTime;
        int verifyCount;
        boolean verified;
    }

    // Mock OTP 儲存
    private final Map<String, MockOtpData> otpStore = new ConcurrentHashMap<>();
    
    // 測試用固定 OTP
    private static final String TEST_OTP = "123456";
    private static final String TEST_MOBILE = "0912345678";

    /**
     * 發送 OTP
     */
    public String sendOtp(String mobile, String purpose, String clientIp) {
        log.info("[MOCK] Sending OTP. Mobile: {}, Purpose: {}, IP: {}", 
            maskMobile(mobile), purpose, clientIp);
        
        simulateDelay();
        
        // 模擬錯誤情況
        if (shouldSimulateError()) {
            log.error("[MOCK] Simulating OTP send failure");
            throw new RuntimeException("Mock OTP send error");
        }
        
        String sessionId = generateId();
        String otp = TEST_OTP; // 測試環境固定 OTP
        
        // 若是測試手機號，使用固定 OTP
        if (!TEST_MOBILE.equals(mobile)) {
            otp = generateOtp();
        }
        
        MockOtpData otpData = new MockOtpData();
        otpData.mobile = mobile;
        otpData.otp = otp;
        otpData.purpose = purpose;
        otpData.createTime = LocalDateTime.now();
        otpData.expireTime = LocalDateTime.now().plusMinutes(5);
        otpData.verifyCount = 0;
        otpData.verified = false;
        
        // 儲存 OTP 資料
        otpStore.put(sessionId, otpData);
        
        log.info("[MOCK] OTP sent successfully. SessionId: {}, OTP: {}", sessionId, otp);
        
        return sessionId;
    }

    /**
     * 驗證 OTP
     */
    public boolean verifyOtp(String sessionId, String otp) {
        log.info("[MOCK] Verifying OTP. SessionId: {}", sessionId);
        
        simulateDelay();
        
        MockOtpData otpData = otpStore.get(sessionId);
        
        if (otpData == null) {
            log.warn("[MOCK] OTP session not found: {}", sessionId);
            return false;
        }
        
        // 檢查是否過期
        if (LocalDateTime.now().isAfter(otpData.expireTime)) {
            log.warn("[MOCK] OTP expired for session: {}", sessionId);
            return false;
        }
        
        // 檢查驗證次數
        if (otpData.verifyCount >= 3) {
            log.warn("[MOCK] OTP verify count exceeded for session: {}", sessionId);
            return false;
        }
        
        // 增加驗證次數
        otpData.verifyCount++;
        
        // 驗證 OTP
        if (otp.equals(otpData.otp)) {
            otpData.verified = true;
            log.info("[MOCK] OTP verified successfully for session: {}", sessionId);
            return true;
        } else {
            log.warn("[MOCK] OTP verification failed for session: {}", sessionId);
            return false;
        }
    }

    /**
     * 重新發送 OTP
     */
    public String resendOtp(String sessionId) {
        log.info("[MOCK] Resending OTP for session: {}", sessionId);
        
        MockOtpData oldData = otpStore.get(sessionId);
        if (oldData == null) {
            log.error("[MOCK] Cannot resend OTP - session not found: {}", sessionId);
            return null;
        }
        
        return sendOtp(oldData.mobile, oldData.purpose, "127.0.0.1");
    }

    /**
     * 檢查 OTP 是否有效
     */
    public boolean isOtpValid(String sessionId) {
        MockOtpData otpData = otpStore.get(sessionId);
        
        if (otpData == null) {
            return false;
        }
        
        // 檢查是否過期
        if (LocalDateTime.now().isAfter(otpData.expireTime)) {
            return false;
        }
        
        // 檢查是否已驗證
        return otpData.verified;
    }

    /**
     * 生成隨機 OTP
     */
    private String generateOtp() {
        return String.format("%06d", random.nextInt(1000000));
    }

    /**
     * 遮罩手機號碼
     */
    private String maskMobile(String mobile) {
        if (mobile == null || mobile.length() < 4) {
            return mobile;
        }
        return mobile.substring(0, 4) + "****" + mobile.substring(mobile.length() - 2);
    }

    /**
     * 測試用方法：清空所有 OTP
     */
    public void clearAllOtp() {
        otpStore.clear();
        log.info("[MOCK] All OTP data cleared");
    }

    /**
     * 測試用方法：取得 OTP（僅測試用）
     */
    public String getOtpForTesting(String sessionId) {
        MockOtpData otpData = otpStore.get(sessionId);
        return otpData != null ? otpData.otp : null;
    }
}