package com.kgi.module.auth.domain.repository;

import com.kgi.module.auth.domain.model.OtpToken;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OTP Token 倉儲介面
 * 定義 OTP 相關的資料存取操作
 */
public interface OtpRepository {
    
    /**
     * 保存 OTP Token
     */
    OtpToken save(OtpToken otpToken);
    
    /**
     * 根據 OTP ID 查詢
     */
    Optional<OtpToken> findByOtpId(String otpId);
    
    /**
     * 查詢有效的 OTP（指定手機號碼和類型）
     */
    Optional<OtpToken> findValidOtp(String phoneNumber, OtpToken.OtpType otpType);
    
    /**
     * 查詢最新的 OTP（指定手機號碼和類型）
     */
    Optional<OtpToken> findLatestOtp(String phoneNumber, OtpToken.OtpType otpType);
    
    /**
     * 查詢待驗證的 OTP
     */
    List<OtpToken> findPendingOtps(String phoneNumber, OtpToken.OtpType otpType);
    
    /**
     * 查詢所有過期的 OTP
     */
    List<OtpToken> findExpiredOtps();
    
    /**
     * 根據手機號碼查詢 OTP 列表
     */
    List<OtpToken> findByPhoneNumber(String phoneNumber);
    
    /**
     * 根據使用者識別號查詢 OTP 列表
     */
    List<OtpToken> findByUserIdentifier(String userIdentifier);
    
    /**
     * 根據狀態查詢 OTP 列表
     */
    List<OtpToken> findByStatus(OtpToken.OtpStatus status);
    
    /**
     * 根據 OTP 類型查詢 OTP 列表
     */
    List<OtpToken> findByOtpType(OtpToken.OtpType otpType);
    
    /**
     * 根據客戶端IP查詢 OTP 列表
     */
    List<OtpToken> findByClientIp(String clientIp);
    
    /**
     * 根據業務流程ID查詢 OTP 列表
     */
    List<OtpToken> findByBusinessProcessId(String businessProcessId);
    
    /**
     * 根據 OTP ID 刪除
     */
    void deleteByOtpId(String otpId);
    
    /**
     * 檢查 OTP 是否存在
     */
    boolean existsByOtpId(String otpId);
    
    /**
     * 統計指定時間範圍內的 OTP 數量
     */
    long countByPhoneNumberAndCreatedTimeBetween(
            String phoneNumber, 
            LocalDateTime startTime, 
            LocalDateTime endTime);
    
    /**
     * 統計各狀態的 OTP 數量
     */
    long countByStatus(OtpToken.OtpStatus status);
    
    /**
     * 統計指定手機號碼的 OTP 數量
     */
    long countByPhoneNumber(String phoneNumber);
    
    /**
     * 統計指定使用者的 OTP 數量
     */
    long countByUserIdentifier(String userIdentifier);
    
    /**
     * 查詢需要清理的 OTP（過期超過指定天數）
     */
    List<OtpToken> findOtpsForCleanup(int daysOld);
    
    /**
     * 查詢被封鎖的 OTP
     */
    List<OtpToken> findBlockedOtps();
    
    /**
     * 根據時間範圍查詢 OTP
     */
    List<OtpToken> findByCreatedTimeBetween(
            LocalDateTime startTime, 
            LocalDateTime endTime);
    
    /**
     * 查詢驗證失敗次數過多的手機號碼
     */
    List<String> findPhoneNumbersWithExcessiveFailures(
            int failureThreshold, 
            int timeWindowHours);
    
    /**
     * 根據多個條件查詢 OTP
     */
    List<OtpToken> findByCriteria(
            String phoneNumber,
            String userIdentifier,
            OtpToken.OtpType otpType,
            OtpToken.OtpStatus status,
            LocalDateTime startTime,
            LocalDateTime endTime,
            int page,
            int size);
}