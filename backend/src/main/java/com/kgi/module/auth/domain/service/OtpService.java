package com.kgi.module.auth.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.auth.domain.model.OtpToken;
import com.kgi.module.auth.domain.repository.OtpRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * OTP 領域服務
 * 負責簡訊驗證碼的生成、發送、驗證和管理
 */
@Slf4j
@Service("authOtpService")
@RequiredArgsConstructor
public class OtpService {
    
    private final OtpRepository otpRepository;
    private final SmsService smsService;
    
    // OTP 設定常數
    private static final int OTP_LENGTH = 6;
    private static final int OTP_VALIDITY_MINUTES = 5;
    private static final int MAX_ATTEMPTS = 3;
    private static final int RESEND_INTERVAL_SECONDS = 60;
    private static final int MAX_DAILY_REQUESTS = 10;
    
    private final SecureRandom secureRandom = new SecureRandom();
    
    /**
     * 生成並發送 OTP
     */
    @Transactional
    public OtpToken generateAndSendOtp(
            String phoneNumber, 
            OtpToken.OtpType otpType, 
            String userIdentifier,
            String clientIp) {
        
        log.info("生成 OTP: phoneNumber={}, otpType={}, userIdentifier={}", 
                maskPhoneNumber(phoneNumber), otpType, maskId(userIdentifier));
        
        // 檢查當日發送次數限制
        validateDailyLimit(phoneNumber);
        
        // 檢查重新發送間隔
        validateResendInterval(phoneNumber, otpType);
        
        // 使舊的 OTP 失效
        invalidateExistingOtps(phoneNumber, otpType);
        
        // 生成新的 OTP
        String otpId = UUID.randomUUID().toString();
        String otpCode = generateOtpCode();
        
        OtpToken otpToken = OtpToken.createNew(
                otpId,
                phoneNumber,
                otpCode,
                otpType,
                userIdentifier,
                clientIp,
                OTP_VALIDITY_MINUTES,
                MAX_ATTEMPTS
        );
        
        // 保存 OTP
        otpRepository.save(otpToken);
        
        // 發送簡訊
        try {
            smsService.sendOtp(phoneNumber, otpCode, otpType);
            log.info("OTP 簡訊發送成功: otpId={}", otpId);
        } catch (Exception e) {
            log.error("OTP 簡訊發送失敗: otpId={}, error={}", otpId, e.getMessage());
            throw new BusinessException("SMS_SEND_FAILED", "簡訊發送失敗，請稍後再試");
        }
        
        return otpToken;
    }
    
    /**
     * 驗證 OTP
     */
    @Transactional
    public boolean verifyOtp(String phoneNumber, String otpCode, OtpToken.OtpType otpType) {
        log.info("驗證 OTP: phoneNumber={}, otpType={}", maskPhoneNumber(phoneNumber), otpType);
        
        // 查找有效的 OTP
        OtpToken otpToken = otpRepository.findValidOtp(phoneNumber, otpType)
                .orElseThrow(() -> new BusinessException("OTP_NOT_FOUND", "找不到有效的驗證碼"));
        
        // 檢查是否已過期
        if (otpToken.isExpired()) {
            otpToken.markExpired();
            otpRepository.save(otpToken);
            throw new BusinessException("OTP_EXPIRED", "驗證碼已過期，請重新取得");
        }
        
        // 檢查是否已被封鎖
        if (otpToken.isBlocked()) {
            throw new BusinessException("OTP_BLOCKED", "驗證碼已被封鎖，請重新取得");
        }
        
        // 執行驗證
        boolean verified = otpToken.verify(otpCode);
        
        // 保存驗證結果
        otpRepository.save(otpToken);
        
        if (verified) {
            log.info("OTP 驗證成功: otpId={}", otpToken.getOtpId());
        } else {
            log.warn("OTP 驗證失敗: otpId={}, remainingAttempts={}", 
                    otpToken.getOtpId(), otpToken.getRemainingAttempts());
            
            if (otpToken.isBlocked()) {
                throw new BusinessException("OTP_BLOCKED", "驗證次數過多，驗證碼已被封鎖");
            } else {
                throw new BusinessException("OTP_INVALID", 
                        String.format("驗證碼錯誤，剩餘嘗試次數：%d", otpToken.getRemainingAttempts()));
            }
        }
        
        return verified;
    }
    
    /**
     * 檢查 OTP 狀態
     */
    public OtpStatus checkOtpStatus(String phoneNumber, OtpToken.OtpType otpType) {
        log.debug("檢查 OTP 狀態: phoneNumber={}, otpType={}", maskPhoneNumber(phoneNumber), otpType);
        
        return otpRepository.findValidOtp(phoneNumber, otpType)
                .map(otpToken -> {
                    if (otpToken.isVerified()) {
                        return OtpStatus.builder()
                                .status(otpToken.getStatus())
                                .remainingSeconds(0)
                                .remainingAttempts(0)
                                .canResend(false)
                                .build();
                    } else if (otpToken.isExpired()) {
                        return OtpStatus.builder()
                                .status(OtpToken.OtpStatus.EXPIRED)
                                .remainingSeconds(0)
                                .remainingAttempts(0)
                                .canResend(true)
                                .build();
                    } else if (otpToken.isBlocked()) {
                        return OtpStatus.builder()
                                .status(OtpToken.OtpStatus.BLOCKED)
                                .remainingSeconds(0)
                                .remainingAttempts(0)
                                .canResend(true)
                                .build();
                    } else {
                        return OtpStatus.builder()
                                .status(otpToken.getStatus())
                                .remainingSeconds(otpToken.getRemainingSeconds())
                                .remainingAttempts(otpToken.getRemainingAttempts())
                                .canResend(otpToken.canResend(RESEND_INTERVAL_SECONDS))
                                .build();
                    }
                })
                .orElse(OtpStatus.builder()
                        .status(null)
                        .remainingSeconds(0)
                        .remainingAttempts(0)
                        .canResend(true)
                        .build());
    }
    
    /**
     * 重新發送 OTP
     */
    @Transactional
    public OtpToken resendOtp(String phoneNumber, OtpToken.OtpType otpType, String userIdentifier, String clientIp) {
        log.info("重新發送 OTP: phoneNumber={}, otpType={}", maskPhoneNumber(phoneNumber), otpType);
        
        // 檢查是否可以重新發送
        OtpStatus status = checkOtpStatus(phoneNumber, otpType);
        if (!status.isCanResend()) {
            throw new BusinessException("RESEND_NOT_ALLOWED", 
                    String.format("請等待 %d 秒後再重新發送", RESEND_INTERVAL_SECONDS));
        }
        
        // 生成並發送新的 OTP
        return generateAndSendOtp(phoneNumber, otpType, userIdentifier, clientIp);
    }
    
    /**
     * 清理過期的 OTP
     */
    @Transactional
    public void cleanupExpiredOtps() {
        log.info("清理過期的 OTP Token");
        
        List<OtpToken> expiredOtps = otpRepository.findExpiredOtps();
        
        expiredOtps.forEach(otp -> {
            otp.markExpired();
            otpRepository.save(otp);
        });
        
        log.info("清理完成，處理了 {} 個過期的 OTP", expiredOtps.size());
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 生成 OTP 驗證碼
     */
    private String generateOtpCode() {
        StringBuilder otpBuilder = new StringBuilder();
        
        for (int i = 0; i < OTP_LENGTH; i++) {
            otpBuilder.append(secureRandom.nextInt(10));
        }
        
        return otpBuilder.toString();
    }
    
    /**
     * 驗證當日發送次數限制
     */
    private void validateDailyLimit(String phoneNumber) {
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = startOfDay.plusDays(1);
        
        long todayCount = otpRepository.countByPhoneNumberAndCreatedTimeBetween(
                phoneNumber, startOfDay, endOfDay);
        
        if (todayCount >= MAX_DAILY_REQUESTS) {
            throw new BusinessException("DAILY_LIMIT_EXCEEDED", 
                    String.format("當日 OTP 發送次數已達上限（%d次）", MAX_DAILY_REQUESTS));
        }
    }
    
    /**
     * 驗證重新發送間隔
     */
    private void validateResendInterval(String phoneNumber, OtpToken.OtpType otpType) {
        otpRepository.findLatestOtp(phoneNumber, otpType)
                .ifPresent(latestOtp -> {
                    if (!latestOtp.canResend(RESEND_INTERVAL_SECONDS)) {
                        long waitTime = RESEND_INTERVAL_SECONDS - 
                                java.time.Duration.between(latestOtp.getCreatedTime(), LocalDateTime.now()).getSeconds();
                        throw new BusinessException("RESEND_TOO_FREQUENT", 
                                String.format("請等待 %d 秒後再重新發送", Math.max(waitTime, 0)));
                    }
                });
    }
    
    /**
     * 使舊的 OTP 失效
     */
    private void invalidateExistingOtps(String phoneNumber, OtpToken.OtpType otpType) {
        List<OtpToken> existingOtps = otpRepository.findPendingOtps(phoneNumber, otpType);
        
        existingOtps.forEach(otp -> {
            otp.markExpired();
            otpRepository.save(otp);
        });
        
        if (!existingOtps.isEmpty()) {
            log.debug("使 {} 個舊的 OTP 失效", existingOtps.size());
        }
    }
    
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 2);
    }
    
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return "****";
        }
        return id.substring(0, 2) + "****" + id.substring(id.length() - 2);
    }
    
    /**
     * OTP 狀態資訊類別
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class OtpStatus {
        private OtpToken.OtpStatus status;
        private long remainingSeconds;
        private int remainingAttempts;
        private boolean canResend;
    }
}