package com.kgi.module.auth.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.auth.domain.model.AuthSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.UUID;

/**
 * JWT Token 領域服務
 * 負責 JWT Token 的生成、驗證和管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JwtTokenService {
    
    // JWT 簽名密鑰（實際部署時應從配置文件或環境變數讀取）
    private static final String JWT_SECRET = "kgi-ibr-jwt-secret-key-2024-very-long-secret-key";
    
    // Token 有效期設定（分鐘）
    private static final int ACCESS_TOKEN_VALIDITY_MINUTES = 30;
    private static final int REFRESH_TOKEN_VALIDITY_MINUTES = 1440; // 24小時
    
    /**
     * 生成 Access Token
     */
    public String generateAccessToken(String userIdentifier, AuthSession.UserType userType, String sessionId) {
        log.debug("生成 Access Token: userIdentifier={}, userType={}", maskId(userIdentifier), userType);
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiry = now.plusMinutes(ACCESS_TOKEN_VALIDITY_MINUTES);
            
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .subject(userIdentifier)
                    .issuer("kgi-ibr")
                    .audience("kgi-ibr-client")
                    .expirationTime(Date.from(expiry.atZone(ZoneId.systemDefault()).toInstant()))
                    .notBeforeTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                    .issueTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                    .jwtID(UUID.randomUUID().toString())
                    .claim("userType", userType.name())
                    .claim("sessionId", sessionId)
                    .claim("tokenType", "ACCESS")
                    .build();
            
            SignedJWT signedJWT = new SignedJWT(
                    new JWSHeader(JWSAlgorithm.HS256),
                    claimsSet
            );
            
            signedJWT.sign(new MACSigner(JWT_SECRET));
            
            String token = signedJWT.serialize();
            log.debug("Access Token 生成成功");
            return token;
            
        } catch (Exception e) {
            log.error("生成 Access Token 失敗: {}", e.getMessage(), e);
            throw new BusinessException("TOKEN_GENERATION_FAILED", "Token 生成失敗");
        }
    }
    
    /**
     * 生成 Refresh Token
     */
    public String generateRefreshToken(String userIdentifier, AuthSession.UserType userType, String sessionId) {
        log.debug("生成 Refresh Token: userIdentifier={}, userType={}", maskId(userIdentifier), userType);
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiry = now.plusMinutes(REFRESH_TOKEN_VALIDITY_MINUTES);
            
            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .subject(userIdentifier)
                    .issuer("kgi-ibr")
                    .audience("kgi-ibr-client")
                    .expirationTime(Date.from(expiry.atZone(ZoneId.systemDefault()).toInstant()))
                    .notBeforeTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                    .issueTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()))
                    .jwtID(UUID.randomUUID().toString())
                    .claim("userType", userType.name())
                    .claim("sessionId", sessionId)
                    .claim("tokenType", "REFRESH")
                    .build();
            
            SignedJWT signedJWT = new SignedJWT(
                    new JWSHeader(JWSAlgorithm.HS256),
                    claimsSet
            );
            
            signedJWT.sign(new MACSigner(JWT_SECRET));
            
            String token = signedJWT.serialize();
            log.debug("Refresh Token 生成成功");
            return token;
            
        } catch (Exception e) {
            log.error("生成 Refresh Token 失敗: {}", e.getMessage(), e);
            throw new BusinessException("TOKEN_GENERATION_FAILED", "Token 生成失敗");
        }
    }
    
    /**
     * 驗證 JWT Token
     */
    public JwtTokenInfo validateToken(String token) {
        log.debug("驗證 JWT Token");
        
        try {
            SignedJWT signedJWT = SignedJWT.parse(token);
            
            // 驗證簽名
            JWSVerifier verifier = new MACVerifier(JWT_SECRET);
            if (!signedJWT.verify(verifier)) {
                log.warn("JWT Token 簽名驗證失敗");
                throw new BusinessException("INVALID_TOKEN", "Token 簽名無效");
            }
            
            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
            
            // 檢查過期時間
            Date expiration = claimsSet.getExpirationTime();
            if (expiration == null || expiration.before(new Date())) {
                log.warn("JWT Token 已過期");
                throw new BusinessException("TOKEN_EXPIRED", "Token 已過期");
            }
            
            // 檢查生效時間
            Date notBefore = claimsSet.getNotBeforeTime();
            if (notBefore != null && notBefore.after(new Date())) {
                log.warn("JWT Token 尚未生效");
                throw new BusinessException("TOKEN_NOT_ACTIVE", "Token 尚未生效");
            }
            
            // 提取 Token 資訊
            String userIdentifier = claimsSet.getSubject();
            String userTypeStr = claimsSet.getStringClaim("userType");
            String sessionId = claimsSet.getStringClaim("sessionId");
            String tokenType = claimsSet.getStringClaim("tokenType");
            
            AuthSession.UserType userType = AuthSession.UserType.valueOf(userTypeStr);
            
            JwtTokenInfo tokenInfo = JwtTokenInfo.builder()
                    .userIdentifier(userIdentifier)
                    .userType(userType)
                    .sessionId(sessionId)
                    .tokenType(tokenType)
                    .expiration(expiration.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .issueTime(claimsSet.getIssueTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                    .build();
            
            log.debug("JWT Token 驗證成功: userIdentifier={}", maskId(userIdentifier));
            return tokenInfo;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("JWT Token 驗證失敗: {}", e.getMessage(), e);
            throw new BusinessException("TOKEN_VALIDATION_FAILED", "Token 驗證失敗");
        }
    }
    
    /**
     * 檢查 Token 是否即將過期
     */
    public boolean isTokenNearExpiry(String token, int thresholdMinutes) {
        try {
            JwtTokenInfo tokenInfo = validateToken(token);
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expiry = tokenInfo.getExpiration();
            
            long minutesUntilExpiry = java.time.Duration.between(now, expiry).toMinutes();
            return minutesUntilExpiry <= thresholdMinutes;
            
        } catch (Exception e) {
            log.warn("檢查 Token 過期時間失敗: {}", e.getMessage());
            return true; // 如果無法檢查，假設即將過期
        }
    }
    
    /**
     * 提取 Token 中的使用者識別號
     */
    public String extractUserIdentifier(String token) {
        try {
            JwtTokenInfo tokenInfo = validateToken(token);
            return tokenInfo.getUserIdentifier();
        } catch (Exception e) {
            log.warn("提取使用者識別號失敗: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 提取 Token 中的會話ID
     */
    public String extractSessionId(String token) {
        try {
            JwtTokenInfo tokenInfo = validateToken(token);
            return tokenInfo.getSessionId();
        } catch (Exception e) {
            log.warn("提取會話ID失敗: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * JWT Token 資訊類別
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class JwtTokenInfo {
        private String userIdentifier;
        private AuthSession.UserType userType;
        private String sessionId;
        private String tokenType;
        private LocalDateTime expiration;
        private LocalDateTime issueTime;
    }
    
    /**
     * 遮蔽識別號（用於日誌）
     */
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return "****";
        }
        
        return id.substring(0, 2) + "****" + id.substring(id.length() - 2);
    }
}