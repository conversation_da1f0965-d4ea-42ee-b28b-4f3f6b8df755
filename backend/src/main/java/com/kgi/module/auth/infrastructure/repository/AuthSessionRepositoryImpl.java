package com.kgi.module.auth.infrastructure.repository;

import com.kgi.module.auth.domain.model.AuthSession;
import com.kgi.module.auth.domain.repository.AuthSessionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 認證會話倉儲實現
 * 使用內存存儲實現（開發環境）
 */
@Slf4j
@Repository
public class AuthSessionRepositoryImpl implements AuthSessionRepository {
    
    // 使用 ConcurrentHashMap 存儲會話資料
    private final Map<String, AuthSession> sessionStore = new ConcurrentHashMap<>();
    private final Map<String, List<String>> userSessionIndex = new ConcurrentHashMap<>();
    private final Map<String, String> tokenSessionIndex = new ConcurrentHashMap<>();
    
    @Override
    public AuthSession save(AuthSession authSession) {
        log.debug("保存認證會話: sessionId={}, user={}", 
                authSession.getSessionId(), authSession.getUserIdentifier());
        
        // 儲存會話
        sessionStore.put(authSession.getSessionId(), authSession);
        
        // 更新用戶會話索引
        userSessionIndex.computeIfAbsent(authSession.getUserIdentifier(), 
                k -> new ArrayList<>()).add(authSession.getSessionId());
        
        // 更新 token 索引
        if (authSession.getJwtToken() != null) {
            tokenSessionIndex.put(authSession.getJwtToken(), authSession.getSessionId());
        }
        if (authSession.getRefreshToken() != null) {
            tokenSessionIndex.put(authSession.getRefreshToken(), authSession.getSessionId());
        }
        
        return authSession;
    }
    
    @Override
    public Optional<AuthSession> findBySessionId(String sessionId) {
        return Optional.ofNullable(sessionStore.get(sessionId));
    }
    
    @Override
    public List<AuthSession> findActiveSessionsByUser(String userIdentifier) {
        return findAllSessionsByUser(userIdentifier).stream()
                .filter(session -> session.getStatus() == AuthSession.SessionStatus.ACTIVE)
                .filter(session -> session.getExpiryTime().isAfter(LocalDateTime.now()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findAllSessionsByUser(String userIdentifier) {
        List<String> sessionIds = userSessionIndex.get(userIdentifier);
        if (sessionIds == null) {
            return Collections.emptyList();
        }
        
        return sessionIds.stream()
                .map(sessionStore::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findExpiredSessionsByUser(String userIdentifier) {
        return findAllSessionsByUser(userIdentifier).stream()
                .filter(session -> session.getExpiryTime().isBefore(LocalDateTime.now()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findExpiredSessions() {
        LocalDateTime now = LocalDateTime.now();
        return sessionStore.values().stream()
                .filter(session -> session.getExpiryTime().isBefore(now))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByStatus(AuthSession.SessionStatus status) {
        return sessionStore.values().stream()
                .filter(session -> session.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<AuthSession> findByJwtToken(String jwtToken) {
        String sessionId = tokenSessionIndex.get(jwtToken);
        return sessionId != null ? findBySessionId(sessionId) : Optional.empty();
    }
    
    @Override
    public Optional<AuthSession> findByRefreshToken(String refreshToken) {
        String sessionId = tokenSessionIndex.get(refreshToken);
        return sessionId != null ? findBySessionId(sessionId) : Optional.empty();
    }
    
    @Override
    public List<AuthSession> findByClientIp(String clientIp) {
        return sessionStore.values().stream()
                .filter(session -> clientIp.equals(session.getClientIp()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByDeviceId(String deviceId) {
        return sessionStore.values().stream()
                .filter(session -> deviceId.equals(session.getDeviceId()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByLoginMethod(AuthSession.LoginMethod loginMethod) {
        return sessionStore.values().stream()
                .filter(session -> session.getLoginMethod() == loginMethod)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByUserType(AuthSession.UserType userType) {
        return sessionStore.values().stream()
                .filter(session -> session.getUserType() == userType)
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteBySessionId(String sessionId) {
        log.debug("刪除認證會話: sessionId={}", sessionId);
        
        AuthSession session = sessionStore.remove(sessionId);
        if (session != null) {
            // 清理用戶會話索引
            List<String> userSessions = userSessionIndex.get(session.getUserIdentifier());
            if (userSessions != null) {
                userSessions.remove(sessionId);
            }
            
            // 清理 token 索引
            if (session.getJwtToken() != null) {
                tokenSessionIndex.remove(session.getJwtToken());
            }
            if (session.getRefreshToken() != null) {
                tokenSessionIndex.remove(session.getRefreshToken());
            }
        }
    }
    
    @Override
    public boolean existsBySessionId(String sessionId) {
        return sessionStore.containsKey(sessionId);
    }
    
    @Override
    public long countActiveSessionsByUser(String userIdentifier) {
        return findActiveSessionsByUser(userIdentifier).size();
    }
    
    @Override
    public long countByStatus(AuthSession.SessionStatus status) {
        return findByStatus(status).size();
    }
    
    @Override
    public List<AuthSession> findSessionsNearExpiry(int thresholdMinutes) {
        LocalDateTime threshold = LocalDateTime.now().plusMinutes(thresholdMinutes);
        LocalDateTime now = LocalDateTime.now();
        
        return sessionStore.values().stream()
                .filter(session -> session.getStatus() == AuthSession.SessionStatus.ACTIVE)
                .filter(session -> {
                    LocalDateTime expiry = session.getExpiryTime();
                    return expiry.isAfter(now) && expiry.isBefore(threshold);
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findInactiveSessions(int inactiveMinutes) {
        LocalDateTime threshold = LocalDateTime.now().minusMinutes(inactiveMinutes);
        
        return sessionStore.values().stream()
                .filter(session -> session.getStatus() == AuthSession.SessionStatus.ACTIVE)
                .filter(session -> session.getLastAccessTime() != null)
                .filter(session -> session.getLastAccessTime().isBefore(threshold))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByCreatedTimeBetween(
            LocalDateTime startTime, LocalDateTime endTime) {
        return sessionStore.values().stream()
                .filter(session -> {
                    LocalDateTime created = session.getCreatedTime();
                    return !created.isBefore(startTime) && !created.isAfter(endTime);
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findRecentSessions(int limitHours, int limit) {
        LocalDateTime threshold = LocalDateTime.now().minusHours(limitHours);
        
        return sessionStore.values().stream()
                .filter(session -> session.getCreatedTime().isAfter(threshold))
                .sorted((s1, s2) -> s2.getCreatedTime().compareTo(s1.getCreatedTime()))
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AuthSession> findByCriteria(
            String userIdentifier,
            AuthSession.UserType userType,
            AuthSession.SessionStatus status,
            AuthSession.LoginMethod loginMethod,
            String clientIp,
            int page,
            int size) {
        
        var stream = sessionStore.values().stream();
        
        if (userIdentifier != null) {
            stream = stream.filter(s -> userIdentifier.equals(s.getUserIdentifier()));
        }
        if (userType != null) {
            stream = stream.filter(s -> s.getUserType() == userType);
        }
        if (status != null) {
            stream = stream.filter(s -> s.getStatus() == status);
        }
        if (loginMethod != null) {
            stream = stream.filter(s -> s.getLoginMethod() == loginMethod);
        }
        if (clientIp != null) {
            stream = stream.filter(s -> clientIp.equals(s.getClientIp()));
        }
        
        return stream
                .sorted((s1, s2) -> s2.getCreatedTime().compareTo(s1.getCreatedTime()))
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }
}