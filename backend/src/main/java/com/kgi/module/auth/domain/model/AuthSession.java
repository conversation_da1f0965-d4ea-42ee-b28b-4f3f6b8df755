package com.kgi.module.auth.domain.model;

import lombok.*;

import java.time.LocalDateTime;

/**
 * 認證會話領域實體
 * 管理使用者認證會話的生命週期
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AuthSession {
    
    /**
     * 會話ID
     */
    private String sessionId;
    
    /**
     * 使用者識別號
     */
    private String userIdentifier;
    
    /**
     * 使用者類型
     */
    private UserType userType;
    
    /**
     * 會話狀態
     */
    private SessionStatus status;
    
    /**
     * JWT Token
     */
    private String jwtToken;
    
    /**
     * Refresh Token
     */
    private String refreshToken;
    
    /**
     * 建立時間
     */
    private LocalDateTime createdTime;
    
    /**
     * 最後存取時間
     */
    private LocalDateTime lastAccessTime;
    
    /**
     * 過期時間
     */
    private LocalDateTime expiryTime;
    
    /**
     * 客戶端IP
     */
    private String clientIp;
    
    /**
     * User Agent
     */
    private String userAgent;
    
    /**
     * 裝置識別碼
     */
    private String deviceId;
    
    /**
     * 登入方式
     */
    private LoginMethod loginMethod;
    
    /**
     * 使用者類型枚舉
     */
    public enum UserType {
        INDIVIDUAL("個人"),
        CORPORATE("企業"),
        ADMIN("管理員"),
        OPERATOR("操作員");
        
        private final String description;
        
        UserType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 會話狀態枚舉
     */
    public enum SessionStatus {
        ACTIVE("活躍"),
        EXPIRED("已過期"),
        INVALIDATED("已失效"),
        LOGGED_OUT("已登出");
        
        private final String description;
        
        SessionStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 登入方式枚舉
     */
    public enum LoginMethod {
        PASSWORD("密碼"),
        OTP("簡訊驗證碼"),
        CERTIFICATE("憑證"),
        BIOMETRIC("生物識別");
        
        private final String description;
        
        LoginMethod(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 建立新的認證會話
     */
    public static AuthSession createNew(
            String sessionId,
            String userIdentifier,
            UserType userType,
            String jwtToken,
            String refreshToken,
            String clientIp,
            String userAgent,
            LoginMethod loginMethod,
            int expiryMinutes) {
        
        LocalDateTime now = LocalDateTime.now();
        
        return AuthSession.builder()
                .sessionId(sessionId)
                .userIdentifier(userIdentifier)
                .userType(userType)
                .status(SessionStatus.ACTIVE)
                .jwtToken(jwtToken)
                .refreshToken(refreshToken)
                .createdTime(now)
                .lastAccessTime(now)
                .expiryTime(now.plusMinutes(expiryMinutes))
                .clientIp(clientIp)
                .userAgent(userAgent)
                .loginMethod(loginMethod)
                .build();
    }
    
    /**
     * 檢查會話是否有效
     */
    public boolean isValid() {
        return status == SessionStatus.ACTIVE && 
               expiryTime != null && 
               LocalDateTime.now().isBefore(expiryTime);
    }
    
    /**
     * 檢查會話是否已過期
     */
    public boolean isExpired() {
        return expiryTime != null && LocalDateTime.now().isAfter(expiryTime);
    }
    
    /**
     * 更新最後存取時間
     */
    public void updateLastAccess() {
        this.lastAccessTime = LocalDateTime.now();
    }
    
    /**
     * 延長會話時間
     */
    public void extendSession(int minutesToAdd) {
        if (isValid()) {
            this.expiryTime = this.expiryTime.plusMinutes(minutesToAdd);
            updateLastAccess();
        }
    }
    
    /**
     * 使會話失效
     */
    public void invalidate() {
        this.status = SessionStatus.INVALIDATED;
    }
    
    /**
     * 標記為已登出
     */
    public void logout() {
        this.status = SessionStatus.LOGGED_OUT;
    }
    
    /**
     * 標記為已過期
     */
    public void markExpired() {
        this.status = SessionStatus.EXPIRED;
    }
    
    /**
     * 更新 JWT Token
     */
    public void updateJwtToken(String newJwtToken) {
        this.jwtToken = newJwtToken;
        updateLastAccess();
    }
    
    /**
     * 更新 Refresh Token
     */
    public void updateRefreshToken(String newRefreshToken) {
        this.refreshToken = newRefreshToken;
        updateLastAccess();
    }
    
    /**
     * 設定裝置識別碼
     */
    public void setDeviceIdentifier(String deviceId) {
        this.deviceId = deviceId;
    }
    
    /**
     * 檢查是否為同一裝置
     */
    public boolean isSameDevice(String checkDeviceId) {
        return deviceId != null && deviceId.equals(checkDeviceId);
    }
    
    /**
     * 檢查是否為同一IP
     */
    public boolean isSameIp(String checkIp) {
        return clientIp != null && clientIp.equals(checkIp);
    }
    
    /**
     * 取得剩餘有效時間（分鐘）
     */
    public long getRemainingMinutes() {
        if (!isValid()) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return java.time.Duration.between(now, expiryTime).toMinutes();
    }
    
    /**
     * 檢查是否需要續期（剩餘時間少於指定分鐘）
     */
    public boolean needsRenewal(int thresholdMinutes) {
        return isValid() && getRemainingMinutes() <= thresholdMinutes;
    }
}