package com.kgi.module.auth.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.auth.domain.model.AuthSession;
import com.kgi.module.auth.domain.service.AuthSessionService;
import com.kgi.module.auth.domain.service.OtpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 登入用例
 * 處理使用者登入相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginUseCase {
    
    private final AuthSessionService authSessionService;
    private final OtpService otpService;
    
    /**
     * OTP 登入
     */
    @Transactional
    public LoginResult loginWithOtp(LoginWithOtpCommand command) {
        log.info("執行 OTP 登入: phoneNumber={}, userType={}", 
                maskPhoneNumber(command.getPhoneNumber()), command.getUserType());
        
        try {
            // 驗證輸入參數
            validateOtpLoginCommand(command);
            
            // 驗證 OTP
            boolean otpValid = otpService.verifyOtp(
                    command.getPhoneNumber(), 
                    command.getOtpCode(), 
                    command.getOtpType()
            );
            
            if (!otpValid) {
                throw new BusinessException("OTP_VERIFICATION_FAILED", "OTP 驗證失敗");
            }
            
            // 建立認證會話
            AuthSession session = authSessionService.createSession(
                    command.getUserIdentifier(),
                    command.getUserType(),
                    command.getClientIp(),
                    command.getUserAgent(),
                    AuthSession.LoginMethod.OTP
            );
            
            // 建立登入結果
            LoginResult result = LoginResult.builder()
                    .success(true)
                    .sessionId(session.getSessionId())
                    .accessToken(session.getJwtToken())
                    .refreshToken(session.getRefreshToken())
                    .userIdentifier(session.getUserIdentifier())
                    .userType(session.getUserType())
                    .expiryTime(session.getExpiryTime())
                    .message("登入成功")
                    .build();
            
            log.info("OTP 登入成功: userIdentifier={}, sessionId={}", 
                    maskId(command.getUserIdentifier()), session.getSessionId());
            
            return result;
            
        } catch (BusinessException e) {
            log.warn("OTP 登入失敗: userIdentifier={}, error={}", 
                    maskId(command.getUserIdentifier()), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("OTP 登入異常: userIdentifier={}, error={}", 
                    maskId(command.getUserIdentifier()), e.getMessage(), e);
            throw new BusinessException("LOGIN_FAILED", "登入失敗: " + e.getMessage());
        }
    }
    
    /**
     * 密碼登入（預留功能）
     */
    @Transactional
    public LoginResult loginWithPassword(LoginWithPasswordCommand command) {
        log.info("執行密碼登入: userIdentifier={}, userType={}", 
                maskId(command.getUserIdentifier()), command.getUserType());
        
        try {
            // 驗證輸入參數
            validatePasswordLoginCommand(command);
            
            // TODO: 實作密碼驗證邏輯
            // 這裡應該整合實際的密碼驗證服務
            boolean passwordValid = validatePassword(command.getUserIdentifier(), command.getPassword());
            
            if (!passwordValid) {
                throw new BusinessException("PASSWORD_VERIFICATION_FAILED", "密碼驗證失敗");
            }
            
            // 建立認證會話
            AuthSession session = authSessionService.createSession(
                    command.getUserIdentifier(),
                    command.getUserType(),
                    command.getClientIp(),
                    command.getUserAgent(),
                    AuthSession.LoginMethod.PASSWORD
            );
            
            // 建立登入結果
            LoginResult result = LoginResult.builder()
                    .success(true)
                    .sessionId(session.getSessionId())
                    .accessToken(session.getJwtToken())
                    .refreshToken(session.getRefreshToken())
                    .userIdentifier(session.getUserIdentifier())
                    .userType(session.getUserType())
                    .expiryTime(session.getExpiryTime())
                    .message("登入成功")
                    .build();
            
            log.info("密碼登入成功: userIdentifier={}, sessionId={}", 
                    maskId(command.getUserIdentifier()), session.getSessionId());
            
            return result;
            
        } catch (BusinessException e) {
            log.warn("密碼登入失敗: userIdentifier={}, error={}", 
                    maskId(command.getUserIdentifier()), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("密碼登入異常: userIdentifier={}, error={}", 
                    maskId(command.getUserIdentifier()), e.getMessage(), e);
            throw new BusinessException("LOGIN_FAILED", "登入失敗: " + e.getMessage());
        }
    }
    
    /**
     * 刷新 Token
     */
    @Transactional
    public RefreshTokenResult refreshToken(RefreshTokenCommand command) {
        log.info("執行 Token 刷新");
        
        try {
            // 驗證輸入參數
            validateRefreshTokenCommand(command);
            
            // 刷新會話
            AuthSession session = authSessionService.refreshSession(command.getRefreshToken());
            
            // 建立刷新結果
            RefreshTokenResult result = RefreshTokenResult.builder()
                    .success(true)
                    .sessionId(session.getSessionId())
                    .accessToken(session.getJwtToken())
                    .refreshToken(session.getRefreshToken())
                    .expiryTime(session.getExpiryTime())
                    .message("Token 刷新成功")
                    .build();
            
            log.info("Token 刷新成功: sessionId={}", session.getSessionId());
            return result;
            
        } catch (BusinessException e) {
            log.warn("Token 刷新失敗: error={}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Token 刷新異常: error={}", e.getMessage(), e);
            throw new BusinessException("TOKEN_REFRESH_FAILED", "Token 刷新失敗: " + e.getMessage());
        }
    }
    
    /**
     * 登出
     */
    @Transactional
    public void logout(LogoutCommand command) {
        log.info("執行登出: sessionId={}", command.getSessionId());
        
        try {
            authSessionService.logout(command.getSessionId());
            log.info("登出成功: sessionId={}", command.getSessionId());
            
        } catch (Exception e) {
            log.error("登出異常: sessionId={}, error={}", command.getSessionId(), e.getMessage(), e);
            // 登出錯誤不拋出異常，確保客戶端能正常處理
        }
    }
    
    // ==================== 驗證方法 ====================
    
    private void validateOtpLoginCommand(LoginWithOtpCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getPhoneNumber())) {
            throw new BusinessException("INVALID_COMMAND", "手機號碼不能為空");
        }
        
        if (isBlank(command.getOtpCode())) {
            throw new BusinessException("INVALID_COMMAND", "OTP 驗證碼不能為空");
        }
        
        if (command.getOtpType() == null) {
            throw new BusinessException("INVALID_COMMAND", "OTP 類型不能為空");
        }
        
        if (isBlank(command.getUserIdentifier())) {
            throw new BusinessException("INVALID_COMMAND", "使用者識別號不能為空");
        }
        
        if (command.getUserType() == null) {
            throw new BusinessException("INVALID_COMMAND", "使用者類型不能為空");
        }
    }
    
    private void validatePasswordLoginCommand(LoginWithPasswordCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getUserIdentifier())) {
            throw new BusinessException("INVALID_COMMAND", "使用者識別號不能為空");
        }
        
        if (isBlank(command.getPassword())) {
            throw new BusinessException("INVALID_COMMAND", "密碼不能為空");
        }
        
        if (command.getUserType() == null) {
            throw new BusinessException("INVALID_COMMAND", "使用者類型不能為空");
        }
    }
    
    private void validateRefreshTokenCommand(RefreshTokenCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getRefreshToken())) {
            throw new BusinessException("INVALID_COMMAND", "Refresh Token 不能為空");
        }
    }
    
    private boolean validatePassword(String userIdentifier, String password) {
        // TODO: 實作實際的密碼驗證邏輯
        // 這裡應該整合密碼加密和驗證服務
        return true; // 暫時返回 true
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 2);
    }
    
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return "****";
        }
        return id.substring(0, 2) + "****" + id.substring(id.length() - 2);
    }
    
    // ==================== 命令和結果類別 ====================
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LoginWithOtpCommand {
        private String phoneNumber;
        private String otpCode;
        private com.kgi.module.auth.domain.model.OtpToken.OtpType otpType;
        private String userIdentifier;
        private AuthSession.UserType userType;
        private String clientIp;
        private String userAgent;
        private String deviceId;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LoginWithPasswordCommand {
        private String userIdentifier;
        private String password;
        private AuthSession.UserType userType;
        private String clientIp;
        private String userAgent;
        private String deviceId;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class RefreshTokenCommand {
        private String refreshToken;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LogoutCommand {
        private String sessionId;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class LoginResult {
        private Boolean success;
        private String sessionId;
        private String accessToken;
        private String refreshToken;
        private String userIdentifier;
        private AuthSession.UserType userType;
        private java.time.LocalDateTime expiryTime;
        private String message;
        private String errorCode;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class RefreshTokenResult {
        private Boolean success;
        private String sessionId;
        private String accessToken;
        private String refreshToken;
        private java.time.LocalDateTime expiryTime;
        private String message;
        private String errorCode;
    }
}