package com.kgi.module.auth.domain.model;

import lombok.*;

import java.time.LocalDateTime;

/**
 * OTP Token 領域實體
 * 管理簡訊驗證碼的生成、驗證和過期
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OtpToken {
    
    /**
     * OTP ID
     */
    private String otpId;
    
    /**
     * 手機號碼
     */
    private String phoneNumber;
    
    /**
     * OTP 驗證碼
     */
    private String otpCode;
    
    /**
     * OTP 類型
     */
    private OtpType otpType;
    
    /**
     * 狀態
     */
    private OtpStatus status;
    
    /**
     * 建立時間
     */
    private LocalDateTime createdTime;
    
    /**
     * 過期時間
     */
    private LocalDateTime expiryTime;
    
    /**
     * 驗證時間
     */
    private LocalDateTime verifiedTime;
    
    /**
     * 嘗試次數
     */
    private Integer attemptCount;
    
    /**
     * 最大嘗試次數
     */
    private Integer maxAttempts;
    
    /**
     * 客戶端IP
     */
    private String clientIp;
    
    /**
     * 關聯的使用者識別號
     */
    private String userIdentifier;
    
    /**
     * 關聯的業務流程ID
     */
    private String businessProcessId;
    
    /**
     * OTP 類型枚舉
     */
    public enum OtpType {
        LOGIN("登入驗證"),
        REGISTER("註冊驗證"),
        RESET_PASSWORD("重設密碼"),
        TRANSACTION("交易驗證"),
        REMITTANCE("解款驗證"),
        IDENTITY_VERIFICATION("身分驗證");
        
        private final String description;
        
        OtpType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * OTP 狀態枚舉
     */
    public enum OtpStatus {
        PENDING("待驗證"),
        VERIFIED("已驗證"),
        EXPIRED("已過期"),
        FAILED("驗證失敗"),
        BLOCKED("已封鎖");
        
        private final String description;
        
        OtpStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 建立新的 OTP Token
     */
    public static OtpToken createNew(
            String otpId,
            String phoneNumber,
            String otpCode,
            OtpType otpType,
            String userIdentifier,
            String clientIp,
            int validityMinutes,
            int maxAttempts) {
        
        LocalDateTime now = LocalDateTime.now();
        
        return OtpToken.builder()
                .otpId(otpId)
                .phoneNumber(phoneNumber)
                .otpCode(otpCode)
                .otpType(otpType)
                .status(OtpStatus.PENDING)
                .createdTime(now)
                .expiryTime(now.plusMinutes(validityMinutes))
                .attemptCount(0)
                .maxAttempts(maxAttempts)
                .clientIp(clientIp)
                .userIdentifier(userIdentifier)
                .build();
    }
    
    /**
     * 檢查 OTP 是否有效
     */
    public boolean isValid() {
        return status == OtpStatus.PENDING && 
               !isExpired() && 
               !isBlocked();
    }
    
    /**
     * 檢查是否已過期
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiryTime);
    }
    
    /**
     * 檢查是否已被封鎖
     */
    public boolean isBlocked() {
        return status == OtpStatus.BLOCKED ||
               (attemptCount != null && maxAttempts != null && attemptCount >= maxAttempts);
    }
    
    /**
     * 檢查是否已驗證
     */
    public boolean isVerified() {
        return status == OtpStatus.VERIFIED;
    }
    
    /**
     * 驗證 OTP 碼
     */
    public boolean verify(String inputCode) {
        if (!isValid()) {
            return false;
        }
        
        incrementAttemptCount();
        
        if (otpCode.equals(inputCode)) {
            this.status = OtpStatus.VERIFIED;
            this.verifiedTime = LocalDateTime.now();
            return true;
        } else {
            if (isBlocked()) {
                this.status = OtpStatus.BLOCKED;
            } else {
                this.status = OtpStatus.FAILED;
            }
            return false;
        }
    }
    
    /**
     * 增加嘗試次數
     */
    private void incrementAttemptCount() {
        if (attemptCount == null) {
            attemptCount = 0;
        }
        attemptCount++;
    }
    
    /**
     * 標記為已過期
     */
    public void markExpired() {
        if (status == OtpStatus.PENDING) {
            this.status = OtpStatus.EXPIRED;
        }
    }
    
    /**
     * 強制封鎖
     */
    public void block() {
        this.status = OtpStatus.BLOCKED;
    }
    
    /**
     * 設定業務流程ID
     */
    public void setBusinessProcessId(String businessProcessId) {
        this.businessProcessId = businessProcessId;
    }
    
    /**
     * 檢查是否為相同手機號碼
     */
    public boolean isSamePhoneNumber(String checkPhoneNumber) {
        return phoneNumber != null && phoneNumber.equals(checkPhoneNumber);
    }
    
    /**
     * 檢查是否為相同使用者
     */
    public boolean isSameUser(String checkUserIdentifier) {
        return userIdentifier != null && userIdentifier.equals(checkUserIdentifier);
    }
    
    /**
     * 取得剩餘有效時間（秒）
     */
    public long getRemainingSeconds() {
        if (!isValid()) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return java.time.Duration.between(now, expiryTime).getSeconds();
    }
    
    /**
     * 取得剩餘嘗試次數
     */
    public int getRemainingAttempts() {
        if (maxAttempts == null || attemptCount == null) {
            return 0;
        }
        
        return Math.max(0, maxAttempts - attemptCount);
    }
    
    /**
     * 檢查是否可以重新發送
     */
    public boolean canResend(int resendIntervalSeconds) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextAllowedTime = createdTime.plusSeconds(resendIntervalSeconds);
        return now.isAfter(nextAllowedTime);
    }
    
    /**
     * 產生遮蔽的手機號碼（用於日誌）
     */
    public String getMaskedPhoneNumber() {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        
        String prefix = phoneNumber.substring(0, 3);
        String suffix = phoneNumber.substring(phoneNumber.length() - 2);
        return prefix + "****" + suffix;
    }
}