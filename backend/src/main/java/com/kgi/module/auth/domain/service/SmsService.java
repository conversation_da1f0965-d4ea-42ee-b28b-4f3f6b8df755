package com.kgi.module.auth.domain.service;

import com.kgi.module.auth.domain.model.OtpToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 簡訊服務
 * 負責發送簡訊通知和 OTP 驗證碼
 */
@Slf4j
@Service("authSmsService")
@RequiredArgsConstructor
public class SmsService {
    
    /**
     * 發送 OTP 簡訊
     */
    public void sendOtp(String phoneNumber, String otpCode, OtpToken.OtpType otpType) {
        log.info("發送 OTP 簡訊: phoneNumber={}, otpType={}", maskPhoneNumber(phoneNumber), otpType);
        
        String message = buildOtpMessage(otpCode, otpType);
        
        try {
            // TODO: 整合實際的簡訊服務提供商 API
            // 這裡應該調用實際的簡訊發送服務
            simulateSmsDelivery(phoneNumber, message);
            
            log.info("OTP 簡訊發送成功: phoneNumber={}", maskPhoneNumber(phoneNumber));
            
        } catch (Exception e) {
            log.error("OTP 簡訊發送失敗: phoneNumber={}, error={}", 
                    maskPhoneNumber(phoneNumber), e.getMessage(), e);
            throw new RuntimeException("簡訊發送失敗", e);
        }
    }
    
    /**
     * 發送通知簡訊
     */
    public void sendNotification(String phoneNumber, String message) {
        log.info("發送通知簡訊: phoneNumber={}", maskPhoneNumber(phoneNumber));
        
        try {
            // TODO: 整合實際的簡訊服務提供商 API
            simulateSmsDelivery(phoneNumber, message);
            
            log.info("通知簡訊發送成功: phoneNumber={}", maskPhoneNumber(phoneNumber));
            
        } catch (Exception e) {
            log.error("通知簡訊發送失敗: phoneNumber={}, error={}", 
                    maskPhoneNumber(phoneNumber), e.getMessage(), e);
            throw new RuntimeException("簡訊發送失敗", e);
        }
    }
    
    /**
     * 建立 OTP 簡訊內容
     */
    private String buildOtpMessage(String otpCode, OtpToken.OtpType otpType) {
        String purpose = getOtpPurpose(otpType);
        
        return String.format(
                "【KGI數位跨境匯款】您的%s驗證碼為：%s，有效期限5分鐘，請勿提供給他人。",
                purpose, otpCode
        );
    }
    
    /**
     * 取得 OTP 用途說明
     */
    private String getOtpPurpose(OtpToken.OtpType otpType) {
        switch (otpType) {
            case LOGIN:
                return "登入";
            case REGISTER:
                return "註冊";
            case RESET_PASSWORD:
                return "重設密碼";
            case TRANSACTION:
                return "交易";
            case REMITTANCE:
                return "解款";
            case IDENTITY_VERIFICATION:
                return "身分驗證";
            default:
                return "驗證";
        }
    }
    
    /**
     * 模擬簡訊發送（開發測試用）
     */
    private void simulateSmsDelivery(String phoneNumber, String message) {
        // 在實際環境中，這裡應該調用真實的簡訊服務 API
        // 例如：Twilio, AWS SNS, 或其他簡訊服務提供商
        
        log.info("=== 模擬簡訊發送 ===");
        log.info("收件人: {}", maskPhoneNumber(phoneNumber));
        log.info("內容: {}", message);
        log.info("=====================");
        
        // 模擬網路延遲
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 模擬發送成功
        // 在實際整合時，這裡應該檢查 API 回應並處理可能的錯誤
    }
    
    /**
     * 遮蔽手機號碼
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        
        String prefix = phoneNumber.substring(0, 3);
        String suffix = phoneNumber.substring(phoneNumber.length() - 2);
        return prefix + "****" + suffix;
    }
    
    /**
     * 驗證手機號碼格式
     */
    public boolean validatePhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }
        
        // 台灣手機號碼格式驗證
        // 09開頭，共10位數字
        String cleanNumber = phoneNumber.replaceAll("[^0-9]", "");
        
        return cleanNumber.matches("^09\\d{8}$");
    }
    
    /**
     * 標準化手機號碼格式
     */
    public String normalizePhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return null;
        }
        
        // 移除所有非數字字符
        String cleanNumber = phoneNumber.replaceAll("[^0-9]", "");
        
        // 如果是台灣手機號碼格式，直接返回
        if (cleanNumber.matches("^09\\d{8}$")) {
            return cleanNumber;
        }
        
        // 如果是國際格式 (+886 9...)，轉換為本地格式
        if (cleanNumber.startsWith("8869") && cleanNumber.length() == 12) {
            return "0" + cleanNumber.substring(3);
        }
        
        // 其他情況直接返回清理後的號碼
        return cleanNumber;
    }
}