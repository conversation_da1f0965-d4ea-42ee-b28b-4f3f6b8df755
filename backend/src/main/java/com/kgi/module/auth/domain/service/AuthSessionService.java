package com.kgi.module.auth.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.auth.domain.model.AuthSession;
import com.kgi.module.auth.domain.repository.AuthSessionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 認證會話領域服務
 * 負責會話的建立、管理、驗證和清理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthSessionService {
    
    private final AuthSessionRepository authSessionRepository;
    private final JwtTokenService jwtTokenService;
    
    // 會話設定常數
    private static final int DEFAULT_SESSION_EXPIRY_MINUTES = 30;
    private static final int REFRESH_TOKEN_EXPIRY_MINUTES = 1440; // 24小時
    private static final int MAX_CONCURRENT_SESSIONS = 3;
    private static final int SESSION_RENEWAL_THRESHOLD_MINUTES = 10;
    
    /**
     * 建立新的認證會話
     */
    @Transactional
    public AuthSession createSession(
            String userIdentifier,
            AuthSession.UserType userType,
            String clientIp,
            String userAgent,
            AuthSession.LoginMethod loginMethod) {
        
        log.info("建立認證會話: userIdentifier={}, userType={}, loginMethod={}", 
                maskId(userIdentifier), userType, loginMethod);
        
        // 檢查並清理過期會話
        cleanupExpiredSessions(userIdentifier);
        
        // 檢查並發會話數量限制
        validateConcurrentSessions(userIdentifier);
        
        // 生成會話ID
        String sessionId = UUID.randomUUID().toString();
        
        // 生成 JWT Token
        String jwtToken = jwtTokenService.generateAccessToken(userIdentifier, userType, sessionId);
        String refreshToken = jwtTokenService.generateRefreshToken(userIdentifier, userType, sessionId);
        
        // 建立會話
        AuthSession session = AuthSession.createNew(
                sessionId,
                userIdentifier,
                userType,
                jwtToken,
                refreshToken,
                clientIp,
                userAgent,
                loginMethod,
                DEFAULT_SESSION_EXPIRY_MINUTES
        );
        
        // 保存會話
        authSessionRepository.save(session);
        
        log.info("認證會話建立成功: sessionId={}, userIdentifier={}", 
                sessionId, maskId(userIdentifier));
        
        return session;
    }
    
    /**
     * 驗證會話
     */
    public AuthSession validateSession(String sessionId) {
        log.debug("驗證會話: sessionId={}", sessionId);
        
        AuthSession session = authSessionRepository.findBySessionId(sessionId)
                .orElseThrow(() -> new BusinessException("SESSION_NOT_FOUND", "會話不存在"));
        
        if (!session.isValid()) {
            if (session.isExpired()) {
                session.markExpired();
                authSessionRepository.save(session);
                throw new BusinessException("SESSION_EXPIRED", "會話已過期");
            } else {
                throw new BusinessException("SESSION_INVALID", "會話無效");
            }
        }
        
        // 更新最後存取時間
        session.updateLastAccess();
        authSessionRepository.save(session);
        
        log.debug("會話驗證成功: sessionId={}", sessionId);
        return session;
    }
    
    /**
     * 更新會話 Token
     */
    @Transactional
    public AuthSession refreshSession(String refreshToken) {
        log.info("更新會話 Token: refreshToken={}", maskToken(refreshToken));
        
        // 驗證 Refresh Token
        JwtTokenService.JwtTokenInfo tokenInfo = jwtTokenService.validateToken(refreshToken);
        
        if (!"REFRESH".equals(tokenInfo.getTokenType())) {
            throw new BusinessException("INVALID_TOKEN_TYPE", "Token 類型不正確");
        }
        
        // 查找會話
        AuthSession session = authSessionRepository.findBySessionId(tokenInfo.getSessionId())
                .orElseThrow(() -> new BusinessException("SESSION_NOT_FOUND", "會話不存在"));
        
        if (!session.isValid()) {
            throw new BusinessException("SESSION_INVALID", "會話已失效");
        }
        
        // 生成新的 Access Token
        String newJwtToken = jwtTokenService.generateAccessToken(
                session.getUserIdentifier(), 
                session.getUserType(), 
                session.getSessionId()
        );
        
        // 更新會話
        session.updateJwtToken(newJwtToken);
        session.extendSession(DEFAULT_SESSION_EXPIRY_MINUTES);
        
        authSessionRepository.save(session);
        
        log.info("會話 Token 更新成功: sessionId={}", session.getSessionId());
        return session;
    }
    
    /**
     * 登出會話
     */
    @Transactional
    public void logout(String sessionId) {
        log.info("登出會話: sessionId={}", sessionId);
        
        Optional<AuthSession> sessionOpt = authSessionRepository.findBySessionId(sessionId);
        
        if (sessionOpt.isPresent()) {
            AuthSession session = sessionOpt.get();
            session.logout();
            authSessionRepository.save(session);
            
            log.info("會話登出成功: sessionId={}, userIdentifier={}", 
                    sessionId, maskId(session.getUserIdentifier()));
        } else {
            log.warn("嘗試登出不存在的會話: sessionId={}", sessionId);
        }
    }
    
    /**
     * 登出使用者的所有會話
     */
    @Transactional
    public void logoutAllSessions(String userIdentifier) {
        log.info("登出所有會話: userIdentifier={}", maskId(userIdentifier));
        
        List<AuthSession> activeSessions = authSessionRepository.findActiveSessionsByUser(userIdentifier);
        
        activeSessions.forEach(session -> {
            session.logout();
            authSessionRepository.save(session);
        });
        
        log.info("所有會話登出完成: userIdentifier={}, count={}", 
                maskId(userIdentifier), activeSessions.size());
    }
    
    /**
     * 檢查會話是否需要續期
     */
    public boolean needsRenewal(String sessionId) {
        return authSessionRepository.findBySessionId(sessionId)
                .map(session -> session.needsRenewal(SESSION_RENEWAL_THRESHOLD_MINUTES))
                .orElse(false);
    }
    
    /**
     * 取得使用者的活躍會話列表
     */
    public List<AuthSession> getActiveSessions(String userIdentifier) {
        log.debug("查詢活躍會話: userIdentifier={}", maskId(userIdentifier));
        
        return authSessionRepository.findActiveSessionsByUser(userIdentifier);
    }
    
    /**
     * 清理過期會話
     */
    @Transactional
    public void cleanupExpiredSessions() {
        log.info("清理過期會話");
        
        List<AuthSession> expiredSessions = authSessionRepository.findExpiredSessions();
        
        expiredSessions.forEach(session -> {
            session.markExpired();
            authSessionRepository.save(session);
        });
        
        log.info("過期會話清理完成，處理了 {} 個會話", expiredSessions.size());
    }
    
    /**
     * 強制使會話失效
     */
    @Transactional
    public void invalidateSession(String sessionId, String reason) {
        log.info("強制使會話失效: sessionId={}, reason={}", sessionId, reason);
        
        authSessionRepository.findBySessionId(sessionId)
                .ifPresent(session -> {
                    session.invalidate();
                    authSessionRepository.save(session);
                    log.info("會話已失效: sessionId={}", sessionId);
                });
    }
    
    // ==================== 私有方法 ====================
    
    /**
     * 清理指定使用者的過期會話
     */
    private void cleanupExpiredSessions(String userIdentifier) {
        List<AuthSession> expiredSessions = authSessionRepository.findExpiredSessionsByUser(userIdentifier);
        
        expiredSessions.forEach(session -> {
            session.markExpired();
            authSessionRepository.save(session);
        });
        
        if (!expiredSessions.isEmpty()) {
            log.debug("清理了 {} 個過期會話: userIdentifier={}", 
                    expiredSessions.size(), maskId(userIdentifier));
        }
    }
    
    /**
     * 驗證並發會話數量限制
     */
    private void validateConcurrentSessions(String userIdentifier) {
        List<AuthSession> activeSessions = authSessionRepository.findActiveSessionsByUser(userIdentifier);
        
        if (activeSessions.size() >= MAX_CONCURRENT_SESSIONS) {
            // 強制登出最舊的會話
            AuthSession oldestSession = activeSessions.stream()
                    .min((s1, s2) -> s1.getLastAccessTime().compareTo(s2.getLastAccessTime()))
                    .orElse(null);
            
            if (oldestSession != null) {
                oldestSession.logout();
                authSessionRepository.save(oldestSession);
                
                log.info("強制登出最舊會話以符合並發限制: sessionId={}, userIdentifier={}", 
                        oldestSession.getSessionId(), maskId(userIdentifier));
            }
        }
    }
    
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return "****";
        }
        return id.substring(0, 2) + "****" + id.substring(id.length() - 2);
    }
    
    private String maskToken(String token) {
        if (token == null || token.length() < 20) {
            return "****";
        }
        return token.substring(0, 10) + "..." + token.substring(token.length() - 10);
    }
}