package com.kgi.module.auth.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.auth.domain.model.OtpToken;
import com.kgi.module.auth.domain.service.OtpService;
import com.kgi.module.auth.domain.service.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * OTP 用例
 * 處理 OTP 相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OtpUseCase {
    
    private final OtpService otpService;
    private final SmsService smsService;
    
    /**
     * 發送 OTP
     */
    @Transactional
    public SendOtpResult sendOtp(SendOtpCommand command) {
        log.info("執行發送 OTP: phoneNumber={}, otpType={}", 
                maskPhoneNumber(command.getPhoneNumber()), command.getOtpType());
        
        try {
            // 驗證輸入參數
            validateSendOtpCommand(command);
            
            // 驗證手機號碼格式
            if (!smsService.validatePhoneNumber(command.getPhoneNumber())) {
                throw new BusinessException("INVALID_PHONE_NUMBER", "手機號碼格式不正確");
            }
            
            // 標準化手機號碼
            String normalizedPhoneNumber = smsService.normalizePhoneNumber(command.getPhoneNumber());
            
            // 生成並發送 OTP
            OtpToken otpToken = otpService.generateAndSendOtp(
                    normalizedPhoneNumber,
                    command.getOtpType(),
                    command.getUserIdentifier(),
                    command.getClientIp()
            );
            
            // 建立回應結果
            SendOtpResult result = SendOtpResult.builder()
                    .success(true)
                    .otpId(otpToken.getOtpId())
                    .phoneNumber(maskPhoneNumber(normalizedPhoneNumber))
                    .otpType(otpToken.getOtpType())
                    .expiryTime(otpToken.getExpiryTime())
                    .remainingSeconds(otpToken.getRemainingSeconds())
                    .maxAttempts(otpToken.getMaxAttempts())
                    .message("OTP 已發送")
                    .build();
            
            log.info("OTP 發送成功: otpId={}, phoneNumber={}", 
                    otpToken.getOtpId(), maskPhoneNumber(normalizedPhoneNumber));
            
            return result;
            
        } catch (BusinessException e) {
            log.warn("OTP 發送失敗: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage());
            
            return SendOtpResult.builder()
                    .success(false)
                    .message(e.getMessage())
                    .errorCode(e.getErrorCode())
                    .build();
                    
        } catch (Exception e) {
            log.error("OTP 發送異常: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage(), e);
            
            return SendOtpResult.builder()
                    .success(false)
                    .message("OTP 發送失敗，請稍後再試")
                    .errorCode("OTP_SEND_FAILED")
                    .build();
        }
    }
    
    /**
     * 驗證 OTP
     */
    public VerifyOtpResult verifyOtp(VerifyOtpCommand command) {
        log.info("執行驗證 OTP: phoneNumber={}, otpType={}", 
                maskPhoneNumber(command.getPhoneNumber()), command.getOtpType());
        
        try {
            // 驗證輸入參數
            validateVerifyOtpCommand(command);
            
            // 標準化手機號碼
            String normalizedPhoneNumber = smsService.normalizePhoneNumber(command.getPhoneNumber());
            
            // 驗證 OTP
            boolean verified = otpService.verifyOtp(
                    normalizedPhoneNumber,
                    command.getOtpCode(),
                    command.getOtpType()
            );
            
            if (verified) {
                VerifyOtpResult result = VerifyOtpResult.builder()
                        .success(true)
                        .verified(true)
                        .message("OTP 驗證成功")
                        .build();
                
                log.info("OTP 驗證成功: phoneNumber={}, otpType={}", 
                        maskPhoneNumber(normalizedPhoneNumber), command.getOtpType());
                
                return result;
            } else {
                // 這種情況不應該發生，因為 otpService.verifyOtp 會拋出異常
                return VerifyOtpResult.builder()
                        .success(false)
                        .verified(false)
                        .message("OTP 驗證失敗")
                        .errorCode("OTP_VERIFICATION_FAILED")
                        .build();
            }
            
        } catch (BusinessException e) {
            log.warn("OTP 驗證失敗: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage());
            
            return VerifyOtpResult.builder()
                    .success(false)
                    .verified(false)
                    .message(e.getMessage())
                    .errorCode(e.getErrorCode())
                    .build();
                    
        } catch (Exception e) {
            log.error("OTP 驗證異常: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage(), e);
            
            return VerifyOtpResult.builder()
                    .success(false)
                    .verified(false)
                    .message("OTP 驗證失敗，請稍後再試")
                    .errorCode("OTP_VERIFY_FAILED")
                    .build();
        }
    }
    
    /**
     * 重新發送 OTP
     */
    @Transactional
    public SendOtpResult resendOtp(ResendOtpCommand command) {
        log.info("執行重新發送 OTP: phoneNumber={}, otpType={}", 
                maskPhoneNumber(command.getPhoneNumber()), command.getOtpType());
        
        try {
            // 驗證輸入參數
            validateResendOtpCommand(command);
            
            // 標準化手機號碼
            String normalizedPhoneNumber = smsService.normalizePhoneNumber(command.getPhoneNumber());
            
            // 重新發送 OTP
            OtpToken otpToken = otpService.resendOtp(
                    normalizedPhoneNumber,
                    command.getOtpType(),
                    command.getUserIdentifier(),
                    command.getClientIp()
            );
            
            // 建立回應結果
            SendOtpResult result = SendOtpResult.builder()
                    .success(true)
                    .otpId(otpToken.getOtpId())
                    .phoneNumber(maskPhoneNumber(normalizedPhoneNumber))
                    .otpType(otpToken.getOtpType())
                    .expiryTime(otpToken.getExpiryTime())
                    .remainingSeconds(otpToken.getRemainingSeconds())
                    .maxAttempts(otpToken.getMaxAttempts())
                    .message("OTP 已重新發送")
                    .build();
            
            log.info("OTP 重新發送成功: otpId={}, phoneNumber={}", 
                    otpToken.getOtpId(), maskPhoneNumber(normalizedPhoneNumber));
            
            return result;
            
        } catch (BusinessException e) {
            log.warn("OTP 重新發送失敗: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage());
            
            return SendOtpResult.builder()
                    .success(false)
                    .message(e.getMessage())
                    .errorCode(e.getErrorCode())
                    .build();
                    
        } catch (Exception e) {
            log.error("OTP 重新發送異常: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage(), e);
            
            return SendOtpResult.builder()
                    .success(false)
                    .message("OTP 重新發送失敗，請稍後再試")
                    .errorCode("OTP_RESEND_FAILED")
                    .build();
        }
    }
    
    /**
     * 檢查 OTP 狀態
     */
    public CheckOtpStatusResult checkOtpStatus(CheckOtpStatusCommand command) {
        log.debug("檢查 OTP 狀態: phoneNumber={}, otpType={}", 
                maskPhoneNumber(command.getPhoneNumber()), command.getOtpType());
        
        try {
            // 驗證輸入參數
            validateCheckOtpStatusCommand(command);
            
            // 標準化手機號碼
            String normalizedPhoneNumber = smsService.normalizePhoneNumber(command.getPhoneNumber());
            
            // 檢查 OTP 狀態
            OtpService.OtpStatus otpStatus = otpService.checkOtpStatus(
                    normalizedPhoneNumber,
                    command.getOtpType()
            );
            
            CheckOtpStatusResult result = CheckOtpStatusResult.builder()
                    .success(true)
                    .status(otpStatus.getStatus())
                    .remainingSeconds(otpStatus.getRemainingSeconds())
                    .remainingAttempts(otpStatus.getRemainingAttempts())
                    .canResend(otpStatus.isCanResend())
                    .build();
            
            log.debug("OTP 狀態檢查完成: phoneNumber={}, status={}", 
                    maskPhoneNumber(normalizedPhoneNumber), otpStatus.getStatus());
            
            return result;
            
        } catch (Exception e) {
            log.error("OTP 狀態檢查異常: phoneNumber={}, error={}", 
                    maskPhoneNumber(command.getPhoneNumber()), e.getMessage(), e);
            
            return CheckOtpStatusResult.builder()
                    .success(false)
                    .message("狀態檢查失敗")
                    .errorCode("STATUS_CHECK_FAILED")
                    .build();
        }
    }
    
    // ==================== 驗證方法 ====================
    
    private void validateSendOtpCommand(SendOtpCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getPhoneNumber())) {
            throw new BusinessException("INVALID_COMMAND", "手機號碼不能為空");
        }
        
        if (command.getOtpType() == null) {
            throw new BusinessException("INVALID_COMMAND", "OTP 類型不能為空");
        }
        
        if (isBlank(command.getUserIdentifier())) {
            throw new BusinessException("INVALID_COMMAND", "使用者識別號不能為空");
        }
    }
    
    private void validateVerifyOtpCommand(VerifyOtpCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getPhoneNumber())) {
            throw new BusinessException("INVALID_COMMAND", "手機號碼不能為空");
        }
        
        if (isBlank(command.getOtpCode())) {
            throw new BusinessException("INVALID_COMMAND", "OTP 驗證碼不能為空");
        }
        
        if (command.getOtpType() == null) {
            throw new BusinessException("INVALID_COMMAND", "OTP 類型不能為空");
        }
    }
    
    private void validateResendOtpCommand(ResendOtpCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getPhoneNumber())) {
            throw new BusinessException("INVALID_COMMAND", "手機號碼不能為空");
        }
        
        if (command.getOtpType() == null) {
            throw new BusinessException("INVALID_COMMAND", "OTP 類型不能為空");
        }
        
        if (isBlank(command.getUserIdentifier())) {
            throw new BusinessException("INVALID_COMMAND", "使用者識別號不能為空");
        }
    }
    
    private void validateCheckOtpStatusCommand(CheckOtpStatusCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getPhoneNumber())) {
            throw new BusinessException("INVALID_COMMAND", "手機號碼不能為空");
        }
        
        if (command.getOtpType() == null) {
            throw new BusinessException("INVALID_COMMAND", "OTP 類型不能為空");
        }
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 4) {
            return "****";
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 2);
    }
    
    // ==================== 命令和結果類別 ====================
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SendOtpCommand {
        private String phoneNumber;
        private OtpToken.OtpType otpType;
        private String userIdentifier;
        private String clientIp;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class VerifyOtpCommand {
        private String phoneNumber;
        private String otpCode;
        private OtpToken.OtpType otpType;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ResendOtpCommand {
        private String phoneNumber;
        private OtpToken.OtpType otpType;
        private String userIdentifier;
        private String clientIp;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CheckOtpStatusCommand {
        private String phoneNumber;
        private OtpToken.OtpType otpType;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SendOtpResult {
        private Boolean success;
        private String otpId;
        private String phoneNumber;
        private OtpToken.OtpType otpType;
        private java.time.LocalDateTime expiryTime;
        private Long remainingSeconds;
        private Integer maxAttempts;
        private String message;
        private String errorCode;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class VerifyOtpResult {
        private Boolean success;
        private Boolean verified;
        private String message;
        private String errorCode;
    }
    
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CheckOtpStatusResult {
        private Boolean success;
        private OtpToken.OtpStatus status;
        private Long remainingSeconds;
        private Integer remainingAttempts;
        private Boolean canResend;
        private String message;
        private String errorCode;
    }
}