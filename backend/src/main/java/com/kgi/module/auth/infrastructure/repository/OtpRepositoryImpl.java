package com.kgi.module.auth.infrastructure.repository;

import com.kgi.module.auth.domain.model.OtpToken;
import com.kgi.module.auth.domain.repository.OtpRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * OTP Repository 的內存實現
 * 使用 ConcurrentHashMap 作為儲存機制
 */
@Repository
public class OtpRepositoryImpl implements OtpRepository {
    
    private final Map<String, OtpToken> otpStorage = new ConcurrentHashMap<>();
    
    @Override
    public OtpToken save(OtpToken otpToken) {
        if (otpToken.getOtpId() == null) {
            otpToken.setOtpId(UUID.randomUUID().toString());
        }
        otpStorage.put(otpToken.getOtpId(), otpToken);
        return otpToken;
    }
    
    @Override
    public Optional<OtpToken> findByOtpId(String otpId) {
        return Optional.ofNullable(otpStorage.get(otpId));
    }
    
    @Override
    public Optional<OtpToken> findValidOtp(String phoneNumber, OtpToken.OtpType otpType) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .filter(otp -> otp.getOtpType() == otpType)
                .filter(OtpToken::isValid)
                .findFirst();
    }
    
    @Override
    public Optional<OtpToken> findLatestOtp(String phoneNumber, OtpToken.OtpType otpType) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .filter(otp -> otp.getOtpType() == otpType)
                .max(Comparator.comparing(OtpToken::getCreatedTime));
    }
    
    @Override
    public List<OtpToken> findPendingOtps(String phoneNumber, OtpToken.OtpType otpType) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .filter(otp -> otp.getOtpType() == otpType)
                .filter(otp -> otp.getStatus() == OtpToken.OtpStatus.PENDING)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findExpiredOtps() {
        return otpStorage.values().stream()
                .filter(OtpToken::isExpired)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByPhoneNumber(String phoneNumber) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByUserIdentifier(String userIdentifier) {
        return otpStorage.values().stream()
                .filter(otp -> userIdentifier.equals(otp.getUserIdentifier()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByStatus(OtpToken.OtpStatus status) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByOtpType(OtpToken.OtpType otpType) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getOtpType() == otpType)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByClientIp(String clientIp) {
        return otpStorage.values().stream()
                .filter(otp -> clientIp.equals(otp.getClientIp()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByBusinessProcessId(String businessProcessId) {
        return otpStorage.values().stream()
                .filter(otp -> businessProcessId.equals(otp.getBusinessProcessId()))
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteByOtpId(String otpId) {
        otpStorage.remove(otpId);
    }
    
    @Override
    public boolean existsByOtpId(String otpId) {
        return otpStorage.containsKey(otpId);
    }
    
    @Override
    public long countByPhoneNumberAndCreatedTimeBetween(
            String phoneNumber, 
            LocalDateTime startTime, 
            LocalDateTime endTime) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .filter(otp -> otp.getCreatedTime().isAfter(startTime))
                .filter(otp -> otp.getCreatedTime().isBefore(endTime))
                .count();
    }
    
    @Override
    public long countByStatus(OtpToken.OtpStatus status) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getStatus() == status)
                .count();
    }
    
    @Override
    public long countByPhoneNumber(String phoneNumber) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getPhoneNumber().equals(phoneNumber))
                .count();
    }
    
    @Override
    public long countByUserIdentifier(String userIdentifier) {
        return otpStorage.values().stream()
                .filter(otp -> userIdentifier.equals(otp.getUserIdentifier()))
                .count();
    }
    
    @Override
    public List<OtpToken> findOtpsForCleanup(int daysOld) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        return otpStorage.values().stream()
                .filter(otp -> otp.getCreatedTime().isBefore(cutoffDate))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findBlockedOtps() {
        return otpStorage.values().stream()
                .filter(otp -> otp.getStatus() == OtpToken.OtpStatus.BLOCKED)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByCreatedTimeBetween(
            LocalDateTime startTime, 
            LocalDateTime endTime) {
        return otpStorage.values().stream()
                .filter(otp -> otp.getCreatedTime().isAfter(startTime))
                .filter(otp -> otp.getCreatedTime().isBefore(endTime))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> findPhoneNumbersWithExcessiveFailures(
            int failureThreshold, 
            int timeWindowHours) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(timeWindowHours);
        
        Map<String, Long> failureCountsByPhone = otpStorage.values().stream()
                .filter(otp -> otp.getCreatedTime().isAfter(cutoffTime))
                .filter(otp -> otp.getStatus() == OtpToken.OtpStatus.FAILED || 
                              otp.getStatus() == OtpToken.OtpStatus.BLOCKED)
                .collect(Collectors.groupingBy(
                        OtpToken::getPhoneNumber,
                        Collectors.counting()
                ));
        
        return failureCountsByPhone.entrySet().stream()
                .filter(entry -> entry.getValue() >= failureThreshold)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<OtpToken> findByCriteria(
            String phoneNumber,
            String userIdentifier,
            OtpToken.OtpType otpType,
            OtpToken.OtpStatus status,
            LocalDateTime startTime,
            LocalDateTime endTime,
            int page,
            int size) {
        
        var stream = otpStorage.values().stream();
        
        if (phoneNumber != null) {
            stream = stream.filter(otp -> phoneNumber.equals(otp.getPhoneNumber()));
        }
        
        if (userIdentifier != null) {
            stream = stream.filter(otp -> userIdentifier.equals(otp.getUserIdentifier()));
        }
        
        if (otpType != null) {
            stream = stream.filter(otp -> otp.getOtpType() == otpType);
        }
        
        if (status != null) {
            stream = stream.filter(otp -> otp.getStatus() == status);
        }
        
        if (startTime != null) {
            stream = stream.filter(otp -> otp.getCreatedTime().isAfter(startTime));
        }
        
        if (endTime != null) {
            stream = stream.filter(otp -> otp.getCreatedTime().isBefore(endTime));
        }
        
        // 排序並分頁
        return stream
                .sorted(Comparator.comparing(OtpToken::getCreatedTime).reversed())
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }
}