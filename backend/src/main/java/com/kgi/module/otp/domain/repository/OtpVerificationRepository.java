package com.kgi.module.otp.domain.repository;

import com.kgi.module.otp.domain.model.OtpChannel;
import com.kgi.module.otp.domain.model.OtpStatus;
import com.kgi.module.otp.domain.model.OtpVerification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OTP 驗證倉儲介面
 */
public interface OtpVerificationRepository {
    
    /**
     * 儲存 OTP 驗證記錄
     */
    OtpVerification save(OtpVerification otpVerification);
    
    /**
     * 根據 ID 查詢
     */
    Optional<OtpVerification> findById(Long id);
    
    /**
     * 根據 OTP ID 查詢
     */
    Optional<OtpVerification> findByOtpId(String otpId);
    
    /**
     * 根據 uniqId 查詢最新的 OTP
     */
    Optional<OtpVerification> findLatestByUniqId(String uniqId);
    
    /**
     * 根據 session key 和 transaction ID 查詢
     */
    Optional<OtpVerification> findBySessionKeyAndTransactionId(String sessionKey, String transactionId);
    
    /**
     * 查詢指定時間內的 OTP 發送記錄
     */
    List<OtpVerification> findByUniqIdAndSentAtAfter(String uniqId, LocalDateTime after);
    
    /**
     * 統計發送次數
     */
    long countByUniqIdAndChannelAndSentAtAfter(String uniqId, OtpChannel channel, LocalDateTime after);
    
    /**
     * 查詢待過期的 OTP
     */
    List<OtpVerification> findByStatusAndExpiredAtBefore(OtpStatus status, LocalDateTime expiredAt);
    
    /**
     * 更新狀態
     */
    void updateStatus(Long id, OtpStatus status);
}