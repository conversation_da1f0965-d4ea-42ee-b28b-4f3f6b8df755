package com.kgi.module.otp.infrastructure.repository.impl;

import com.kgi.module.otp.domain.model.*;
import com.kgi.module.otp.domain.repository.OtpVerificationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * OTP 驗證倉儲實作
 * 暫時使用記憶體存儲，未來可改為資料庫實作
 */
@Slf4j
@Repository
public class OtpVerificationRepositoryImpl implements OtpVerificationRepository {
    
    // 使用執行緒安全的 Map 存儲
    private final Map<Long, OtpVerification> storage = new ConcurrentHashMap<>();
    private final Map<String, Long> otpIdIndex = new ConcurrentHashMap<>();
    private Long idCounter = 1L;
    
    @Override
    public OtpVerification save(OtpVerification otpVerification) {
        if (otpVerification.getId() == null) {
            otpVerification.setId(idCounter++);
        }
        
        storage.put(otpVerification.getId(), otpVerification);
        otpIdIndex.put(otpVerification.getOtpId(), otpVerification.getId());
        
        log.info("Saved OTP verification: id={}, otpId={}", 
                otpVerification.getId(), otpVerification.getOtpId());
        
        return otpVerification;
    }
    
    @Override
    public Optional<OtpVerification> findById(Long id) {
        return Optional.ofNullable(storage.get(id));
    }
    
    @Override
    public Optional<OtpVerification> findByOtpId(String otpId) {
        Long id = otpIdIndex.get(otpId);
        return id != null ? findById(id) : Optional.empty();
    }
    
    @Override
    public Optional<OtpVerification> findLatestByUniqId(String uniqId) {
        return storage.values().stream()
                .filter(otp -> uniqId.equals(otp.getUniqId()))
                .max(Comparator.comparing(OtpVerification::getCreatedAt));
    }
    
    @Override
    public Optional<OtpVerification> findBySessionKeyAndTransactionId(String sessionKey, String transactionId) {
        return storage.values().stream()
                .filter(otp -> {
                    OtpSession session = otp.getSession();
                    return session != null && 
                           sessionKey.equals(session.getSessionKey()) &&
                           transactionId.equals(session.getTransactionId());
                })
                .findFirst();
    }
    
    @Override
    public List<OtpVerification> findByUniqIdAndSentAtAfter(String uniqId, LocalDateTime after) {
        return storage.values().stream()
                .filter(otp -> uniqId.equals(otp.getUniqId()) && 
                             otp.getSentAt() != null && 
                             otp.getSentAt().isAfter(after))
                .sorted(Comparator.comparing(OtpVerification::getSentAt).reversed())
                .collect(Collectors.toList());
    }
    
    @Override
    public long countByUniqIdAndChannelAndSentAtAfter(String uniqId, OtpChannel channel, LocalDateTime after) {
        return storage.values().stream()
                .filter(otp -> uniqId.equals(otp.getUniqId()) && 
                             otp.getChannel() == channel &&
                             otp.getSentAt() != null && 
                             otp.getSentAt().isAfter(after))
                .count();
    }
    
    @Override
    public List<OtpVerification> findByStatusAndExpiredAtBefore(OtpStatus status, LocalDateTime expiredAt) {
        return storage.values().stream()
                .filter(otp -> otp.getStatus() == status && 
                             otp.getExpiredAt() != null && 
                             otp.getExpiredAt().isBefore(expiredAt))
                .collect(Collectors.toList());
    }
    
    @Override
    public void updateStatus(Long id, OtpStatus status) {
        OtpVerification otp = storage.get(id);
        if (otp != null) {
            otp.setStatus(status);
            log.info("Updated OTP status: id={}, status={}", id, status);
        }
    }
}