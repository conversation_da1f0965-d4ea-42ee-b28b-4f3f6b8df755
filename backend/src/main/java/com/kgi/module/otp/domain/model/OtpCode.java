package com.kgi.module.otp.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.security.SecureRandom;

/**
 * OTP 驗證碼值物件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OtpCode {
    
    private String code;
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    
    /**
     * 生成新的 OTP 碼
     */
    public static OtpCode generate() {
        return new OtpCode(generateRandomCode());
    }
    
    /**
     * 生成指定的 OTP 碼（用於測試）
     */
    public static OtpCode of(String code) {
        validateCode(code);
        return new OtpCode(code);
    }
    
    /**
     * 驗證碼是否匹配
     */
    public boolean matches(String inputCode) {
        return code != null && code.equals(inputCode);
    }
    
    /**
     * 獲取遮罩後的驗證碼（用於日誌）
     */
    public String getMaskedCode() {
        if (code == null || code.length() < 4) {
            return "***";
        }
        return code.substring(0, 2) + "****";
    }
    
    /**
     * 生成 6 位隨機數字
     */
    private static String generateRandomCode() {
        int randomNum = SECURE_RANDOM.nextInt(900000) + 100000;
        return String.valueOf(randomNum);
    }
    
    /**
     * 驗證碼格式檢查
     */
    private static void validateCode(String code) {
        if (code == null || !code.matches("^[0-9]{6}$")) {
            throw new IllegalArgumentException("OTP code must be 6 digits");
        }
    }
}