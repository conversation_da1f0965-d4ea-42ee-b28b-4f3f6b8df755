package com.kgi.module.otp.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OTP Session 值物件
 * 記錄與外部服務互動的 session 資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpSession {
    
    private String sessionKey;
    private String transactionId;
    private String billDepartment;
    private String operatorId;
    
    /**
     * 建立簡訊 OTP Session
     */
    public static OtpSession forSms(String sessionKey, String transactionId, String productCode) {
        return OtpSession.builder()
                .sessionKey(sessionKey)
                .transactionId(transactionId)
                .billDepartment(getBillDepartmentByProduct(productCode))
                .operatorId("AP08")
                .build();
    }
    
    /**
     * 建立 Email OTP Session
     */
    public static OtpSession forEmail(String sessionKey, String transactionId) {
        return OtpSession.builder()
                .sessionKey(sessionKey)
                .transactionId(transactionId)
                .operatorId("ID13")
                .build();
    }
    
    /**
     * 根據產品代碼取得費用部門
     */
    private static String getBillDepartmentByProduct(String productCode) {
        // 這裡應該從 OTPConst.billDepMapping 取得
        switch (productCode) {
            case "S01":
                return "9722"; // 貸款
            case "S05":
                return "6605"; // 數位存款
            case "C04":
                return "8883"; // 信用卡
            default:
                return "6605"; // 預設
        }
    }
}