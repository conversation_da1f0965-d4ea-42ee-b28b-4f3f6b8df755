package com.kgi.module.otp.application.dto.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class CheckOtpVO {

    private String uniqId;
    private String txnID;
    private String txnDate;
    private String sk;
    private String otp;

    public boolean check() {
        return StringUtils.isNotEmpty(this.uniqId) && StringUtils.isNotEmpty(this.txnID) && StringUtils.isNotEmpty(this.txnDate) && StringUtils.isNotEmpty(this.sk) && StringUtils.isNotEmpty(this.otp);
    }
    
    // 相容性方法
    public String getTxnId() {
        return this.txnID;
    }
    
    public void setTxnId(String txnId) {
        this.txnID = txnId;
    }
}
