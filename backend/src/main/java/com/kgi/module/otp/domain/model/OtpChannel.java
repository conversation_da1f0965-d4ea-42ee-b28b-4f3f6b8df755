package com.kgi.module.otp.domain.model;

/**
 * OTP 發送通道枚舉
 */
public enum OtpChannel {
    SMS("SMS", "簡訊"),
    EMAIL("EMAIL", "電子郵件");
    
    private final String code;
    private final String description;
    
    OtpChannel(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根據接收者資訊判斷通道
     */
    public static OtpChannel determineChannel(String email, String phone) {
        if (email != null && !email.isEmpty()) {
            return EMAIL;
        } else if (phone != null && !phone.isEmpty()) {
            return SMS;
        }
        throw new IllegalArgumentException("Must provide either email or phone");
    }
    
    public static OtpChannel fromCode(String code) {
        for (OtpChannel channel : values()) {
            if (channel.code.equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("Unknown OTP channel code: " + code);
    }
}