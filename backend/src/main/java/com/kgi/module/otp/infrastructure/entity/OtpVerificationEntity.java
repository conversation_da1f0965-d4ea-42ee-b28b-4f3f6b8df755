package com.kgi.module.otp.infrastructure.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * OTP 驗證實體
 * 新設計的 Entity，未來將取代 OTPData
 */
@Entity
@Table(name = "otp_verification", indexes = {
    @Index(name = "idx_otp_id", columnList = "otp_id", unique = true),
    @Index(name = "idx_uniq_id", columnList = "uniq_id"),
    @Index(name = "idx_session_key_txn_id", columnList = "session_key, transaction_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_expired_at", columnList = "expired_at")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class OtpVerificationEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @EqualsAndHashCode.Include
    private Long id;

    /**
     * OTP 唯一識別碼
     */
    @Column(name = "otp_id", nullable = false, length = 50, unique = true)
    private String otpId;

    /**
     * 案件唯一識別碼
     */
    @Column(name = "uniq_id", nullable = false, length = 50)
    private String uniqId;

    /**
     * 案件類型
     */
    @Column(name = "uniq_type", nullable = false, length = 20)
    private String uniqType;

    /**
     * OTP 碼（加密儲存）
     */
    @Column(name = "otp_code", nullable = false, length = 10)
    private String otpCode;

    /**
     * 發送通道：SMS, EMAIL
     */
    @Column(name = "channel", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private OtpChannelType channel;

    /**
     * 接收者（手機號碼或 Email）
     */
    @Column(name = "recipient", nullable = false, length = 100)
    private String recipient;

    /**
     * OTP 狀態
     */
    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private OtpStatusType status;

    /**
     * Session Key
     */
    @Column(name = "session_key", length = 100)
    private String sessionKey;

    /**
     * Transaction ID
     */
    @Column(name = "transaction_id", length = 50)
    private String transactionId;

    /**
     * 發送時間
     */
    @Column(name = "sent_at")
    private LocalDateTime sentAt;

    /**
     * 驗證時間
     */
    @Column(name = "verified_at")
    private LocalDateTime verifiedAt;

    /**
     * 過期時間
     */
    @Column(name = "expired_at", nullable = false)
    private LocalDateTime expiredAt;

    /**
     * 錯誤嘗試次數
     */
    @Column(name = "error_count", nullable = false)
    @Builder.Default
    private Integer errorCount = 0;

    /**
     * 最後錯誤時間
     */
    @Column(name = "last_error_at")
    private LocalDateTime lastErrorAt;

    /**
     * 發送次數
     */
    @Column(name = "send_count", nullable = false)
    @Builder.Default
    private Integer sendCount = 0;

    /**
     * 建立時間
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * 更新時間
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 建立者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * OTP 通道類型
     */
    public enum OtpChannelType {
        SMS, EMAIL
    }

    /**
     * OTP 狀態類型
     */
    public enum OtpStatusType {
        PENDING,    // 待發送
        SENT,       // 已發送
        VERIFIED,   // 已驗證
        EXPIRED,    // 已過期
        FAILED      // 失敗
    }
}