package com.kgi.module.otp.application.facade;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.domain.service.SimpleGenericService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;

/**
 * OTP Facade
 * 提供 OTP 相關操作的門面介面
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OtpFacade {
    
    @Autowired
    private SimpleGenericService simpleGenericService;
    
    @Autowired
    private HttpServletRequest httpServletRequest;
    
    /**
     * 發送 OTP
     */
    public ResponseEntity<ResponseVO> getOtpCode(OtpVO otpVO) {
        try {
            ResponseVO response = simpleGenericService.getOtp(
                otpVO.getUniqId(),
                otpVO.getEmail(),
                otpVO
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error sending OTP", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("發送OTP失敗");
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 驗證 OTP
     */
    public ResponseEntity<ResponseVO> checkOtpCode(OtpVO otpVO) {
        try {
            String ipAddress = httpServletRequest.getRemoteAddr();
            ResponseVO response = simpleGenericService.checkOtp(ipAddress, otpVO);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error verifying OTP", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("驗證OTP失敗");
            return ResponseEntity.ok(errorResponse);
        }
    }
}
