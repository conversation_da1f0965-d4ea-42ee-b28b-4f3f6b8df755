package com.kgi.module.otp.domain.service;

import com.kgi.module.otp.domain.model.*;
import com.kgi.module.otp.domain.repository.OtpVerificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * OTP 領域服務
 * 處理 OTP 相關的核心業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OtpDomainService {
    
    private final OtpVerificationRepository repository;
    
    @Value("${kgi.otp.send-interval-seconds:60}")
    private int sendIntervalSeconds;
    
    @Value("${kgi.otp.expire-minutes:5}")
    private int expireMinutes;
    
    @Value("${kgi.otp.max-attempts:3}")
    private int maxAttempts;
    
    @Value("${kgi.otp.daily-limit:10}")
    private int dailyLimit;
    
    /**
     * 建立新的 OTP 驗證
     */
    public OtpVerification createOtpVerification(
            String uniqId,
            String uniqType,
            OtpChannel channel,
            String recipient) {
        
        // 檢查發送頻率
        checkSendFrequency(uniqId, channel);
        
        // 檢查每日發送限制
        checkDailyLimit(uniqId, channel);
        
        // 建立 OTP 驗證
        OtpVerification otpVerification = OtpVerification.builder()
                .otpId(generateOtpId())
                .uniqId(uniqId)
                .uniqType(uniqType)
                .channel(channel)
                .recipient(recipient)
                .status(OtpStatus.GENERATED)
                .attemptCount(0)
                .createdAt(LocalDateTime.now())
                .expiredAt(LocalDateTime.now().plusMinutes(expireMinutes))
                .build();
        
        // 生成 OTP 碼
        otpVerification.generateOtpCode();
        
        return repository.save(otpVerification);
    }
    
    /**
     * 處理 OTP 發送成功
     */
    public void handleSendSuccess(Long otpId, OtpSession session) {
        OtpVerification otp = repository.findById(otpId)
                .orElseThrow(() -> new IllegalArgumentException("OTP not found: " + otpId));
        
        otp.markAsSent(session);
        repository.save(otp);
        
        log.info("OTP {} sent successfully to {}", otp.getOtpId(), otp.getRecipient());
    }
    
    /**
     * 驗證 OTP
     */
    public boolean verifyOtp(String sessionKey, String transactionId, String inputOtpCode) {
        OtpVerification otp = repository.findBySessionKeyAndTransactionId(sessionKey, transactionId)
                .orElseThrow(() -> new IllegalArgumentException("OTP session not found"));
        
        boolean isValid = otp.verify(inputOtpCode);
        repository.save(otp);
        
        if (isValid) {
            log.info("OTP {} verified successfully", otp.getOtpId());
        } else {
            log.warn("OTP {} verification failed, attempt: {}", otp.getOtpId(), otp.getAttemptCount());
        }
        
        return isValid;
    }
    
    /**
     * 檢查發送頻率
     */
    private void checkSendFrequency(String uniqId, OtpChannel channel) {
        LocalDateTime timeLimit = LocalDateTime.now().minusSeconds(sendIntervalSeconds);
        
        var recentOtps = repository.findByUniqIdAndSentAtAfter(uniqId, timeLimit);
        
        if (!recentOtps.isEmpty()) {
            OtpVerification lastOtp = recentOtps.get(0);
            if (!lastOtp.canResend(sendIntervalSeconds)) {
                long remainingSeconds = sendIntervalSeconds - 
                        java.time.Duration.between(lastOtp.getSentAt(), LocalDateTime.now()).getSeconds();
                throw new IllegalStateException(
                        String.format("請等待 %d 秒後再試", remainingSeconds));
            }
        }
    }
    
    /**
     * 檢查每日發送限制
     */
    private void checkDailyLimit(String uniqId, OtpChannel channel) {
        LocalDateTime startOfDay = LocalDateTime.now().toLocalDate().atStartOfDay();
        
        long todayCount = repository.countByUniqIdAndChannelAndSentAtAfter(uniqId, channel, startOfDay);
        
        if (todayCount >= dailyLimit) {
            throw new IllegalStateException("今日發送次數已達上限");
        }
    }
    
    /**
     * 處理過期的 OTP
     */
    public void processExpiredOtps() {
        List<OtpVerification> expiredOtps = repository.findByStatusAndExpiredAtBefore(
                OtpStatus.SENT, LocalDateTime.now());
        
        for (OtpVerification otp : expiredOtps) {
            otp.setStatus(OtpStatus.EXPIRED);
            repository.save(otp);
            log.info("OTP {} expired", otp.getOtpId());
        }
    }
    
    /**
     * 生成唯一的 OTP ID
     */
    private String generateOtpId() {
        return "OTP_" + UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    
    /**
     * 生成簡訊內容
     */
    public String generateSmsContent(String otpCode) {
        return String.format("動態密碼為%s為凱基銀行線上申請", otpCode);
    }
    
    /**
     * 生成郵件內容
     */
    public String generateEmailContent(String otpCode, String serviceName) {
        return String.format(
            "親愛的客戶您好：\n\n" +
            "您的 %s 驗證碼為：%s\n" +
            "此驗證碼將於 %d 分鐘後失效，請盡速完成驗證。\n\n" +
            "凱基銀行 敬上",
            serviceName, otpCode, expireMinutes
        );
    }
}