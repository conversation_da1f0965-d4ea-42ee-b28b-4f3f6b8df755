package com.kgi.module.otp.infrastructure.repository.impl;

import com.kgi.core.config.RepositoryConfig;
import com.kgi.module.otp.domain.model.*;
import com.kgi.module.otp.domain.repository.OtpVerificationRepository;
import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity;
import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity.OtpChannelType;
import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity.OtpStatusType;
import com.kgi.module.otp.infrastructure.repository.OtpVerificationJpaRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * OTP 驗證倉儲適配器
 * 根據配置自動切換 Memory 或 JPA 實作
 */
@Slf4j
@Repository
@Primary
@RequiredArgsConstructor
public class OtpVerificationRepositoryAdapter implements OtpVerificationRepository {
    
    private final RepositoryConfig repositoryConfig;
    private final OtpVerificationJpaRepository jpaRepository;
    
    // Memory 模式存儲
    private final Map<Long, OtpVerification> memoryStorage = new ConcurrentHashMap<>();
    private final Map<String, Long> memoryOtpIdIndex = new ConcurrentHashMap<>();
    private Long memoryIdCounter = 1L;
    
    @Override
    @Transactional
    public OtpVerification save(OtpVerification otpVerification) {
        if (repositoryConfig.isJpaMode()) {
            return saveWithJpa(otpVerification);
        } else {
            return saveWithMemory(otpVerification);
        }
    }
    
    @Override
    public Optional<OtpVerification> findById(Long id) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findById(id)
                    .map(this::entityToDomain);
        } else {
            return Optional.ofNullable(memoryStorage.get(id));
        }
    }
    
    @Override
    public Optional<OtpVerification> findByOtpId(String otpId) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findByOtpId(otpId)
                    .map(this::entityToDomain);
        } else {
            Long id = memoryOtpIdIndex.get(otpId);
            return id != null ? findById(id) : Optional.empty();
        }
    }
    
    @Override
    public Optional<OtpVerification> findLatestByUniqId(String uniqId) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findLatestByUniqId(uniqId)
                    .map(this::entityToDomain);
        } else {
            return memoryStorage.values().stream()
                    .filter(otp -> uniqId.equals(otp.getUniqId()))
                    .max(Comparator.comparing(OtpVerification::getCreatedAt));
        }
    }
    
    @Override
    public Optional<OtpVerification> findBySessionKeyAndTransactionId(String sessionKey, String transactionId) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findBySessionKeyAndTransactionId(sessionKey, transactionId)
                    .map(this::entityToDomain);
        } else {
            return memoryStorage.values().stream()
                    .filter(otp -> {
                        OtpSession session = otp.getSession();
                        return session != null && 
                               sessionKey.equals(session.getSessionKey()) &&
                               transactionId.equals(session.getTransactionId());
                    })
                    .findFirst();
        }
    }
    
    @Override
    public List<OtpVerification> findByUniqIdAndSentAtAfter(String uniqId, LocalDateTime after) {
        if (repositoryConfig.isJpaMode()) {
            return jpaRepository.findByUniqIdAndSentAtAfter(uniqId, after).stream()
                    .map(this::entityToDomain)
                    .collect(Collectors.toList());
        } else {
            return memoryStorage.values().stream()
                    .filter(otp -> uniqId.equals(otp.getUniqId()) && 
                                 otp.getSentAt() != null && 
                                 otp.getSentAt().isAfter(after))
                    .sorted(Comparator.comparing(OtpVerification::getSentAt).reversed())
                    .collect(Collectors.toList());
        }
    }
    
    @Override
    public long countByUniqIdAndChannelAndSentAtAfter(String uniqId, OtpChannel channel, LocalDateTime after) {
        if (repositoryConfig.isJpaMode()) {
            OtpChannelType channelType = OtpChannelType.valueOf(channel.name());
            return jpaRepository.countByUniqIdAndChannelAndSentAtAfter(uniqId, channelType, after);
        } else {
            return memoryStorage.values().stream()
                    .filter(otp -> uniqId.equals(otp.getUniqId()) && 
                                 otp.getChannel() == channel &&
                                 otp.getSentAt() != null && 
                                 otp.getSentAt().isAfter(after))
                    .count();
        }
    }
    
    @Override
    public List<OtpVerification> findByStatusAndExpiredAtBefore(OtpStatus status, LocalDateTime expiredAt) {
        if (repositoryConfig.isJpaMode()) {
            OtpStatusType statusType = OtpStatusType.valueOf(status.name());
            return jpaRepository.findByStatusAndExpiredAtBefore(statusType, expiredAt).stream()
                    .map(this::entityToDomain)
                    .collect(Collectors.toList());
        } else {
            return memoryStorage.values().stream()
                    .filter(otp -> otp.getStatus() == status && 
                                 otp.getExpiredAt() != null && 
                                 otp.getExpiredAt().isBefore(expiredAt))
                    .collect(Collectors.toList());
        }
    }
    
    @Override
    @Transactional
    public void updateStatus(Long id, OtpStatus status) {
        if (repositoryConfig.isJpaMode()) {
            OtpStatusType statusType = OtpStatusType.valueOf(status.name());
            jpaRepository.updateStatus(id, statusType, LocalDateTime.now());
        } else {
            OtpVerification otp = memoryStorage.get(id);
            if (otp != null) {
                otp.setStatus(status);
                log.info("Updated OTP status in memory: id={}, status={}", id, status);
            }
        }
    }
    
    // Memory 模式儲存
    private OtpVerification saveWithMemory(OtpVerification otpVerification) {
        if (otpVerification.getId() == null) {
            otpVerification.setId(memoryIdCounter++);
        }
        
        memoryStorage.put(otpVerification.getId(), otpVerification);
        memoryOtpIdIndex.put(otpVerification.getOtpId(), otpVerification.getId());
        
        log.info("Saved OTP verification in memory: id={}, otpId={}", 
                otpVerification.getId(), otpVerification.getOtpId());
        
        return otpVerification;
    }
    
    // JPA 模式儲存
    private OtpVerification saveWithJpa(OtpVerification otpVerification) {
        OtpVerificationEntity entity = domainToEntity(otpVerification);
        entity = jpaRepository.save(entity);
        
        log.info("Saved OTP verification in database: id={}, otpId={}", 
                entity.getId(), entity.getOtpId());
        
        return entityToDomain(entity);
    }
    
    // Domain 轉 Entity
    private OtpVerificationEntity domainToEntity(OtpVerification domain) {
        OtpVerificationEntity entity = new OtpVerificationEntity();
        
        entity.setId(domain.getId());
        entity.setOtpId(domain.getOtpId());
        entity.setUniqId(domain.getUniqId());
        entity.setUniqType(domain.getUniqType());
        entity.setOtpCode(domain.getOtpCode().getCode());
        entity.setChannel(OtpChannelType.valueOf(domain.getChannel().name()));
        entity.setRecipient(domain.getRecipient());
        entity.setStatus(OtpStatusType.valueOf(domain.getStatus().name()));
        
        if (domain.getSession() != null) {
            entity.setSessionKey(domain.getSession().getSessionKey());
            entity.setTransactionId(domain.getSession().getTransactionId());
        }
        
        entity.setSentAt(domain.getSentAt());
        entity.setVerifiedAt(domain.getVerifiedAt());
        entity.setExpiredAt(domain.getExpiredAt());
        entity.setErrorCount(domain.getErrorCount());
        entity.setLastErrorAt(domain.getLastErrorAt());
        entity.setSendCount(domain.getSendCount());
        entity.setCreatedAt(domain.getCreatedAt());
        entity.setUpdatedAt(domain.getUpdatedAt());
        
        return entity;
    }
    
    // Entity 轉 Domain
    private OtpVerification entityToDomain(OtpVerificationEntity entity) {
        OtpVerification domain = new OtpVerification();
        
        domain.setId(entity.getId());
        domain.setOtpId(entity.getOtpId());
        domain.setUniqId(entity.getUniqId());
        domain.setUniqType(entity.getUniqType());
        domain.setOtpCode(new OtpCode(entity.getOtpCode()));
        domain.setChannel(OtpChannel.valueOf(entity.getChannel().name()));
        domain.setRecipient(entity.getRecipient());
        domain.setStatus(OtpStatus.valueOf(entity.getStatus().name()));
        
        if (entity.getSessionKey() != null || entity.getTransactionId() != null) {
            domain.setSession(OtpSession.builder()
                    .sessionKey(entity.getSessionKey())
                    .transactionId(entity.getTransactionId())
                    .build());
        }
        
        domain.setSentAt(entity.getSentAt());
        domain.setVerifiedAt(entity.getVerifiedAt());
        domain.setExpiredAt(entity.getExpiredAt());
        domain.setErrorCount(entity.getErrorCount());
        domain.setLastErrorAt(entity.getLastErrorAt());
        domain.setSendCount(entity.getSendCount());
        domain.setCreatedAt(entity.getCreatedAt());
        domain.setUpdatedAt(entity.getUpdatedAt());
        
        return domain;
    }
}