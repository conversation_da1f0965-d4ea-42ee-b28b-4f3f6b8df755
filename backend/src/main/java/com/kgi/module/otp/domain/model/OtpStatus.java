package com.kgi.module.otp.domain.model;

/**
 * OTP 狀態枚舉
 */
public enum OtpStatus {
    GENERATED("0", "已生成"),
    SENT("1", "已發送"),
    VERIFIED("2", "已驗證"),
    EXPIRED("3", "已過期"),
    FAILED("4", "驗證失敗"),
    CANCELLED("9", "已取消");
    
    private final String code;
    private final String description;
    
    OtpStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static OtpStatus fromCode(String code) {
        for (OtpStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OTP status code: " + code);
    }
    
    /**
     * 是否為終態
     */
    public boolean isFinalStatus() {
        return this == VERIFIED || this == EXPIRED || this == FAILED || this == CANCELLED;
    }
}