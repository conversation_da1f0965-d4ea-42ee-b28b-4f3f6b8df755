package com.kgi.module.otp.infrastructure.repository;

import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity;
import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity.OtpChannelType;
import com.kgi.module.otp.infrastructure.entity.OtpVerificationEntity.OtpStatusType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * OTP 驗證 JPA Repository
 */
@Repository
public interface OtpVerificationJpaRepository extends JpaRepository<OtpVerificationEntity, Long> {
    
    /**
     * 根據 OTP ID 查詢
     */
    Optional<OtpVerificationEntity> findByOtpId(String otpId);
    
    /**
     * 根據 uniqId 查詢最新的 OTP
     */
    @Query("SELECT o FROM OtpVerificationEntity o WHERE o.uniqId = :uniqId ORDER BY o.createdAt DESC")
    Optional<OtpVerificationEntity> findLatestByUniqId(@Param("uniqId") String uniqId);
    
    /**
     * 根據 session key 和 transaction ID 查詢
     */
    Optional<OtpVerificationEntity> findBySessionKeyAndTransactionId(String sessionKey, String transactionId);
    
    /**
     * 查詢指定時間內的 OTP 發送記錄
     */
    List<OtpVerificationEntity> findByUniqIdAndSentAtAfter(String uniqId, LocalDateTime after);
    
    /**
     * 統計發送次數
     */
    long countByUniqIdAndChannelAndSentAtAfter(String uniqId, OtpChannelType channel, LocalDateTime after);
    
    /**
     * 查詢待過期的 OTP
     */
    List<OtpVerificationEntity> findByStatusAndExpiredAtBefore(OtpStatusType status, LocalDateTime expiredAt);
    
    /**
     * 更新狀態
     */
    @Modifying
    @Query("UPDATE OtpVerificationEntity o SET o.status = :status, o.updatedAt = :now WHERE o.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") OtpStatusType status, @Param("now") LocalDateTime now);
    
    /**
     * 更新驗證資訊
     */
    @Modifying
    @Query("UPDATE OtpVerificationEntity o SET o.status = :status, o.verifiedAt = :verifiedAt, o.updatedAt = :now WHERE o.id = :id")
    void updateVerification(@Param("id") Long id, @Param("status") OtpStatusType status, 
                          @Param("verifiedAt") LocalDateTime verifiedAt, @Param("now") LocalDateTime now);
    
    /**
     * 增加錯誤次數
     */
    @Modifying
    @Query("UPDATE OtpVerificationEntity o SET o.errorCount = o.errorCount + 1, o.lastErrorAt = :now, o.updatedAt = :now WHERE o.id = :id")
    void incrementErrorCount(@Param("id") Long id, @Param("now") LocalDateTime now);
    
    /**
     * 刪除過期資料
     */
    @Modifying
    @Query("DELETE FROM OtpVerificationEntity o WHERE o.status = 'EXPIRED' AND o.expiredAt < :before")
    int deleteExpiredBefore(@Param("before") LocalDateTime before);
}