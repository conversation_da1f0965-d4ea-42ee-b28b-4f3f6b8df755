package com.kgi.module.otp.infrastructure.external;

import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
import com.kgi.module.otp.application.dto.request.SendOTPVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 簡化版 KGI Service Mock
 * 用於測試環境的模擬實作
 */
@Slf4j
@Service
public class SimpleKgiServiceMock {
    
    /**
     * 模擬發送 OTP
     * @param url API URL (在 Mock 中忽略)
     * @param jsonStr JSON 請求字串  
     */
    public SendOTPResp sendOTP(String url, String jsonStr) throws Exception {
        SendOTPResp response = new SendOTPResp();
        
        try {
            log.info("[MOCK] Sending OTP to URL: {}, request: {}", url, jsonStr);
            
            // 模擬成功回應
            response.setStatus("0000");
            response.setCode("0000");
            response.setMessage("OTP已發送(測試模式)");
            response.setSk("MOCK_SK_" + System.currentTimeMillis());
            response.setTxnID("MOCK_TXN_" + System.currentTimeMillis());
            response.setTxnDate(new java.text.SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new java.util.Date()));
            
            // 在測試模式下，直接顯示 OTP 碼
            log.info("[MOCK] Generated OTP: 123456");
            
        } catch (Exception e) {
            log.error("[MOCK] Error sending OTP", e);
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("模擬發送失敗");
            throw e;
        }
        
        return response;
    }
    
    /**
     * 模擬驗證 OTP  
     * @param url API URL (在 Mock 中忽略)
     * @param jsonStr JSON 請求字串
     */
    public CheckOTPResp checkOTP(String url, String jsonStr) throws Exception {
        CheckOTPResp response = new CheckOTPResp();
        
        try {
            log.info("[MOCK] Checking OTP at URL: {}, request: {}", url, jsonStr);
            
            // 簡單解析 JSON 來取得 OTP
            String otp = extractOtpFromJson(jsonStr);
            
            // 在測試模式下，OTP "123456" 永遠是正確的
            if ("123456".equals(otp)) {
                response.setStatus("0000");
                response.setCode("0000");
                response.setMessage("驗證成功");
                log.info("[MOCK] OTP verification successful");
            } else {
                response.setStatus("9996");
                response.setCode("9996");
                response.setErrorMsg("OTP錯誤");
                log.info("[MOCK] OTP verification failed");
            }
            
        } catch (Exception e) {
            log.error("[MOCK] Error checking OTP", e);
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("模擬驗證失敗");
            throw e;
        }
        
        return response;
    }
    
    /**
     * 從 JSON 字串中提取 OTP
     */
    private String extractOtpFromJson(String jsonStr) {
        try {
            // 簡單的字串解析，尋找 "otp":"value" 模式
            int index = jsonStr.indexOf("\"otp\"");
            if (index != -1) {
                int start = jsonStr.indexOf("\"", index + 6) + 1;
                int end = jsonStr.indexOf("\"", start);
                return jsonStr.substring(start, end);
            }
        } catch (Exception e) {
            log.error("Error extracting OTP from JSON", e);
        }
        return "";
    }
    
    /**
     * 模擬發送 OTP (保留舊方法以維持相容性)
     */
    public SendOTPResp sendOTPMock(String uniqId, OtpVO otpVO) {
        try {
            // 將 OtpVO 轉換為 JSON 字串
            String jsonStr = String.format("{\"uniqId\":\"%s\",\"phone\":\"%s\"}", 
                                         uniqId, otpVO.getUsePhone());
            return sendOTP("mock://sendOTP", jsonStr);
        } catch (Exception e) {
            SendOTPResp response = new SendOTPResp();
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("模擬發送失敗");
            return response;
        }
    }
    
    /**
     * 模擬驗證 OTP (保留舊方法以維持相容性) 
     */
    public CheckOTPResp checkOTPMock(String ipAddress, OtpVO otpVO) {
        try {
            // 將 OtpVO 轉換為 JSON 字串
            String jsonStr = String.format("{\"uniqId\":\"%s\",\"otp\":\"%s\"}", 
                                         otpVO.getUniqId(), otpVO.getOtp());
            return checkOTP("mock://checkOTP", jsonStr);
        } catch (Exception e) {
            CheckOTPResp response = new CheckOTPResp();
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("模擬驗證失敗");
            return response;
        }
    }
}