# OTP Module (簡化版)

## 概述
這是從原始專案移植過來的 OTP 模組簡化版本，移除了所有非 OTP 相關的依賴和功能。

## 模組結構

### Controllers
- `OtpController.java` - 主要的 OTP API 控制器 (/publicApi/getOtpCode, /publicApi/checkOtpCode)
- `SimpleOtpController.java` - 簡化版 OTP 控制器 (/api/otp/send, /api/otp/verify)

### Services
- `SimpleGenericService.java` - 簡化版 OTP 服務，處理 OTP 發送和驗證邏輯
- `SimpleValidationService.java` - 簡化版驗證服務（用於外部驗證服務整合）
- `SimpleKgiService.java` - 簡化版 KGI 服務（真實環境）
- `SimpleKgiServiceMock.java` - 簡化版 KGI 服務（測試環境）

### DTOs
- `OtpVO.java` - OTP 請求物件
- `SendOTPVO.java` - 發送 OTP 請求物件
- `CheckOtpVO.java` - 驗證 OTP 請求物件
- `SendOTPResp.java` - 發送 OTP 回應物件
- `CheckOTPResp.java` - 驗證 OTP 回應物件

### Entity & Repository
- `OTPData.java` - OTP 資料實體
- `OTPDataRepository.java` - OTP 資料存取介面

### Facade
- `OtpFacade.java` - OTP 門面模式實作

## 使用方式

### 發送 OTP
```
POST /publicApi/getOtpCode
Header: Authorization: <token>
Body: {
    "uniqId": "12345",
    "email": "<EMAIL>",
    "usePhone": "0912345678",
    "uniqType": "QUERY"
}
```

### 驗證 OTP
```
POST /publicApi/checkOtpCode
Header: Authorization: <token>
Body: {
    "uniqId": "12345",
    "otp": "123456",
    "sk": "SK1234567890",
    "txnId": "TXN1234567890"
}
```

## 測試模式
當 `kgi.useMockupData=true` 時，系統會使用 Mock 服務：
- OTP 碼固定為 "123456"
- 所有發送和驗證都會成功

## 移除的元件
以下是從原始專案中移除的非 OTP 相關元件：
- GenericService.java (包含太多非 OTP 依賴)
- ValidationService.java (包含太多非 OTP 依賴)
- KgiService.java (包含太多非 OTP 依賴)
- KgiServiceMock.java (包含太多非 OTP 依賴)
- OtpProcessService.java (空檔案)
- IHttpService.java (非 OTP 相關)
- HttpDao2.java (非 OTP 相關)
- AioCaseDataRepository.java (非 OTP 相關)
- AIOCaseData.java (非 OTP 相關)
- AioApiLogRepository.java (非 OTP 相關)
- AioApiLog.java (非 OTP 相關)