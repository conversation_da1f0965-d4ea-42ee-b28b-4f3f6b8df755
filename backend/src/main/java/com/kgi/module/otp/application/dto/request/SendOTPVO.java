package com.kgi.module.otp.application.dto.request;

import com.google.gson.annotations.SerializedName;
import com.kgi.core.annotation.CheckNullAndEmpty;
import lombok.Data;

@Data
public class SendOTPVO {

	@CheckNullAndEmpty
	private String phone = "";

	private String idno = "";
	private String opId = "";
	private String billDep = "";
	@SerializedName(value="txnId", alternate={"txnID"})
	private String txnId = "";
	private String process = "";
	private String productId = "";
	@SerializedName(value="mailAddr", alternate={"email"})
	private String mailAddr = "";
	private String message = "";

	private String dynac_PWD = "";

	public SendOTPVO() {}
}
