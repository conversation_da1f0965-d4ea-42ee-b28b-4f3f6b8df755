package com.kgi.module.otp.infrastructure.external;

import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 簡化版 KGI Service
 * 用於與外部 KGI 系統整合
 */
@Slf4j
@Service
public class SimpleKgiService {
    
    @Value("${kgi.validation.server.url:}")
    private String validationServerUrl;
    
    /**
     * 發送 OTP
     * @param url API URL
     * @param jsonStr JSON 請求字串
     */
    public SendOTPResp sendOTP(String url, String jsonStr) throws Exception {
        SendOTPResp response = new SendOTPResp();
        
        try {
            log.info("Sending OTP to URL: {}, request: {}", url, jsonStr);
            
            // 在真實環境中，這裡會呼叫外部 KGI 系統
            // 模擬成功回應
            response.setStatus("0000");
            response.setCode("0000");
            response.setMessage("OTP已發送");
            response.setSk("SK" + System.currentTimeMillis());
            response.setTxnID("TXN" + System.currentTimeMillis());
            response.setTxnDate(new java.text.SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new java.util.Date()));
            
        } catch (Exception e) {
            log.error("Error sending OTP", e);
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("發送失敗");
            throw e;
        }
        
        return response;
    }
    
    /**
     * 驗證 OTP
     * @param url API URL
     * @param jsonStr JSON 請求字串
     */
    public CheckOTPResp checkOTP(String url, String jsonStr) throws Exception {
        CheckOTPResp response = new CheckOTPResp();
        
        try {
            log.info("Checking OTP at URL: {}, request: {}", url, jsonStr);
            
            // 在真實環境中，這裡會呼叫外部 KGI 系統
            // 模擬成功回應
            response.setStatus("0000");
            response.setCode("0000");
            response.setMessage("驗證成功");
            
        } catch (Exception e) {
            log.error("Error checking OTP", e);
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("驗證失敗");
            throw e;
        }
        
        return response;
    }
}