package com.kgi.module.otp.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * OTP 模組配置
 * 確保 Spring 能夠掃描到 OTP 相關的組件
 */
@Configuration
@ComponentScan(basePackages = "com.kgi.module.otp")
@EnableJpaRepositories(
    basePackages = "com.kgi.module.otp.infrastructure.repository",
    considerNestedRepositories = true
)
public class OtpModuleConfig {
    // 配置類，用於確保 Spring 正確掃描 OTP 模組的所有組件
}
