package com.kgi.module.otp.domain.model;

public class OTPConst {

    public static final String MOCK = "THIS IS PSEUDO MODE";

    /** OTP Process 目前一律 AIO **/
    public static final String PROCESS = "AIO";

    /** OTP Dynac_PWD 目前一律 Y **/
    public static final String DynacPWD = "Y";

    /** otp message 預設值*/
    public static final String defaultMessageTitle = "動態密碼為{0}為凱基銀行線上申請";

    /** otp message 預設值*/
    public static final String defaultMessageEnd = "所寄送5分鐘內有效，請勿將驗證碼提供他人或不明網站以防詐騙或盜開帳戶。";

    /** otp AIO message 預設值*/
    public static final String AIOMessage = "多合一產品";

    /** otp LOAN message */
    public static final String LOAN = "個人信貸";

    /** otp CC message*/
    public static final String CC = "信用卡";

    /** otp D3 message */
    public static final String D3 = "數位存款帳戶";

    /** otp D2 message */
    public static final String D2 = "數位存款帳戶";

    /** otp SAL message */
    public static final String SAL = "薪資轉帳開戶";

    /** otp APPT message */
    public static final String APPT = "分行開戶預填";// R2-3400 更改標題

    /** otp TRUST message */
    public static final String TRUST = "信託開戶";
    /** otp LOAN_CONTRACT message */
    public static final String LOAN_CONTRACT = "貸款立約";

    /** otp STOCK message */
    public static final String STOCK = "證券開戶";

    /** otp DACH message */
    public static final String DACH = "開心戶";

    /*otp flex-card message */
    public static final String FlexCard="現金卡";/* 新增現金卡-20230830 #4421 */
    /** otp HL message */
    public static final String HL = "房屋貸款";
}