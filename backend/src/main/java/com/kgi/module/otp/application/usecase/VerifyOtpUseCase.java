package com.kgi.module.otp.application.usecase;

import com.kgi.module.otp.application.dto.request.CheckOtpVO;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
import com.kgi.module.otp.domain.service.OtpDomainService;
import com.kgi.module.otp.infrastructure.external.SimpleKgiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 驗證 OTP 用例
 * 保持與原系統完全相容
 */
@Slf4j
@Component("otpVerifyOtpUseCase")
@RequiredArgsConstructor
public class VerifyOtpUseCase {
    
    private final OtpDomainService otpDomainService;
    private final SimpleKgiService kgiService;
    
    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;
    
    @Transactional
    public CheckOTPResp execute(CheckOtpVO request) {
        try {
            log.info("Verifying OTP - uniqId: {}, sk: {}, txnId: {}", 
                    request.getUniqId(), request.getSk(), request.getTxnId());
            
            // 準備請求 JSON
            String jsonRequest = buildCheckOtpRequest(request);
            String apiUrl = "/api/otp/verify";
            
            // 呼叫外部服務驗證 OTP
            CheckOTPResp response = kgiService.checkOTP(apiUrl, jsonRequest);
            
            // 更新本地 OTP 狀態
            if (response != null && "0000".equals(response.getStatus())) {
                boolean isValid = otpDomainService.verifyOtp(
                        request.getSk(),
                        request.getTxnId(),
                        request.getOtp()
                );
                
                if (isValid) {
                    log.info("OTP verified successfully for uniqId: {}", request.getUniqId());
                } else {
                    log.warn("OTP verification failed locally for uniqId: {}", request.getUniqId());
                }
            } else {
                log.error("OTP verification failed - response: {}", response);
            }
            
            return response;
            
        } catch (IllegalArgumentException e) {
            log.warn("OTP verification failed due to invalid input: {}", e.getMessage());
            return createErrorResponse("9002", "驗證資料錯誤");
        } catch (Exception e) {
            log.error("Unexpected error verifying OTP", e);
            return createErrorResponse("9999", "驗證OTP失敗，請稍後再試");
        }
    }
    
    private CheckOTPResp createErrorResponse(String code, String message) {
        CheckOTPResp response = new CheckOTPResp();
        response.setStatus(code);
        response.setCode(code);
        response.setMessage(message);
        response.setErrorMsg(message);
        return response;
    }
    
    private String buildCheckOtpRequest(CheckOtpVO request) {
        // 建立 JSON 請求
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"sk\":\"").append(request.getSk()).append("\",");
        json.append("\"txnId\":\"").append(request.getTxnId()).append("\",");
        json.append("\"otp\":\"").append(request.getOtp()).append("\",");
        json.append("\"uniqId\":\"").append(request.getUniqId()).append("\"");
        json.append("}");
        return json.toString();
    }
}