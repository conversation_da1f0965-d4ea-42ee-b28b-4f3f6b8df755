package com.kgi.module.otp.application.facade;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.module.otp.application.dto.request.CheckOtpVO;
import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.application.usecase.SendOtpUseCase;
import com.kgi.module.otp.application.usecase.VerifyOtpUseCase;
import com.kgi.module.otp.domain.service.SimpleGenericService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * 增強版 OTP Facade
 * 整合原有功能與 DDD 架構
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OtpFacadeEnhanced {
    
    @Autowired
    private SimpleGenericService simpleGenericService;
    
    @Autowired
    private SendOtpUseCase sendOtpUseCase;
    
    @Autowired
    private VerifyOtpUseCase verifyOtpUseCase;
    
    @Autowired
    private HttpServletRequest httpServletRequest;
    
    /**
     * 發送 OTP（保持原有介面）
     */
    public ResponseEntity<ResponseVO> getOtpCode(OtpVO otpVO) {
        try {
            // 使用原有的 SimpleGenericService 以保持相容性
            ResponseVO response = simpleGenericService.getOtp(
                otpVO.getUniqId(),
                otpVO.getEmail(),
                otpVO
            );
            
            // 同時使用新的 UseCase 進行追蹤和管理
            try {
                SendOTPResp useCaseResp = sendOtpUseCase.execute(otpVO);
                log.debug("UseCase response: {}", useCaseResp);
            } catch (Exception e) {
                log.warn("UseCase execution failed, but original service succeeded", e);
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error sending OTP", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("發送OTP失敗");
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 驗證 OTP（保持原有介面）
     */
    public ResponseEntity<ResponseVO> checkOtpCode(OtpVO otpVO) {
        try {
            String ipAddress = httpServletRequest.getRemoteAddr();
            
            // 使用原有的 SimpleGenericService 以保持相容性
            ResponseVO response = simpleGenericService.checkOtp(ipAddress, otpVO);
            
            // 同時使用新的 UseCase 進行追蹤和管理
            try {
                CheckOtpVO checkRequest = new CheckOtpVO();
                checkRequest.setUniqId(otpVO.getUniqId());
                checkRequest.setOtp(otpVO.getOtp());
                checkRequest.setSk(otpVO.getSk());
                checkRequest.setTxnId(otpVO.getTxnId());
                
                CheckOTPResp useCaseResp = verifyOtpUseCase.execute(checkRequest);
                log.debug("UseCase response: {}", useCaseResp);
            } catch (Exception e) {
                log.warn("UseCase execution failed, but original service succeeded", e);
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error verifying OTP", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("驗證OTP失敗");
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 發送 OTP（新的 DDD 介面）
     */
    public SendOTPResp sendOtp(OtpVO request) {
        return sendOtpUseCase.execute(request);
    }
    
    /**
     * 驗證 OTP（新的 DDD 介面）
     */
    public CheckOTPResp verifyOtp(CheckOtpVO request) {
        return verifyOtpUseCase.execute(request);
    }
}