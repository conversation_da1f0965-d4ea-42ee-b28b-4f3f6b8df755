package com.kgi.module.otp.domain.service;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.application.dto.request.SendOTPVO;
import com.kgi.module.otp.application.dto.request.CheckOtpVO;
import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
// import com.kgi.module.otp.infrastructure.entity.OTPData; // Deprecated
// import com.kgi.module.otp.infrastructure.repository.OTPDataRepository; // Deprecated
import com.kgi.module.otp.infrastructure.external.SimpleKgiService;
import com.kgi.module.otp.infrastructure.external.SimpleKgiServiceMock;
import com.kgi.core.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * 簡化版 Generic 服務
 * 只保留 OTP 相關功能
 */
@Slf4j
@Service
public class SimpleGenericService {
    
    @Value("${kgi.useMockupData:true}")
    private boolean useMockupData;
    
    @Value("${kgi.validation.server.url:http://localhost:8080}")
    private String validationServerUrl;
    
    // @Autowired(required = false)
    // private OTPDataRepository otpDataRepository; // Deprecated - 使用 OtpVerificationRepository
    
    @Autowired(required = false)
    private SimpleValidationService simpleValidationService;
    
    @Autowired(required = false)
    private SimpleKgiService simpleKgiService;
    
    @Autowired(required = false)
    private SimpleKgiServiceMock simpleKgiServiceMock;
    
    /**
     * 取得 OTP
     */
    public ResponseVO getOtp(String uniqId, String email, OtpVO vo) {
        ResponseVO response = new ResponseVO();
        
        try {
            log.info("getOtp: uniqId={}, email={}, usePhone={}", uniqId, email, vo.getUsePhone());
            
            // TODO: 使用新的 OtpVerificationRepository 來檢查 OTP 發送頁率
            // 目前暫時移除此檢查，等整合完成後再補回
            
            // 發送 OTP
            SendOTPResp sendResp;
            if (useMockupData) {
                // 假打模式
                sendResp = simpleKgiServiceMock.sendOTPMock(uniqId, vo);
            } else {
                // 真打模式 - 需要建立 SendOTPVO 並轉換為 JSON
                SendOTPVO sendOTPVO = new SendOTPVO();
                sendOTPVO.setPhone(vo.getUsePhone());
                sendOTPVO.setIdno(""); // OtpVO 沒有 idno，設為空字串
                sendOTPVO.setMailAddr(vo.getEmail());
                sendOTPVO.setProductId(vo.getUniqType());
                
                try {
                    String jsonStr = JsonUtil.toJson(sendOTPVO);
                    String url = validationServerUrl + "/api/KGI/SEND_OTP"; 
                    sendResp = simpleKgiService.sendOTP(url, jsonStr);
                } catch (Exception e) {
                    log.error("Error converting to JSON", e);
                    sendResp = new SendOTPResp();
                    sendResp.setStatus("9999");
                    sendResp.setErrorMsg("JSON轉換錯誤");
                }
            }
            
            if (sendResp.getStatus().equals("0000")) {
                // TODO: 使用新的 OtpVerificationRepository 來儲存 OTP 資訊
                // 目前暫時移除此功能，等整合完成後再補回
                log.info("OTP sent successfully, need to integrate with new OtpVerificationRepository");
                
                // 準備回應
                response.setRtnCode("0000");
                response.setRtnMessage("OTP已發送");
                response.setRtnObj(sendResp);
            } else {
                response.setRtnCode(sendResp.getStatus());
                response.setRtnMessage(sendResp.getErrorMsg());
            }
            
        } catch (Exception e) {
            log.error("發送OTP時發生錯誤", e);
            response.setRtnCode("9999");
            response.setRtnMessage("系統錯誤");
        }
        
        return response;
    }
    
    /**
     * 檢查 OTP
     */
    public ResponseVO checkOtp(String ipAddress, OtpVO vo) {
        ResponseVO response = new ResponseVO();
        
        try {
            log.info("checkOtp: uniqId={}, sk={}, txnId={}", vo.getUniqId(), vo.getSk(), vo.getTxnId());
            
            // 驗證 OTP
            CheckOTPResp checkResp;
            if (useMockupData) {
                // 假打模式
                checkResp = simpleKgiServiceMock.checkOTPMock(ipAddress, vo);
            } else {
                // 真打模式 - 需要建立 CheckOtpVO 並轉換為 JSON
                CheckOtpVO checkOtpVO = new CheckOtpVO();
                checkOtpVO.setUniqId(vo.getUniqId());
                checkOtpVO.setSk(vo.getSk());
                checkOtpVO.setTxnId(vo.getTxnId());
                checkOtpVO.setTxnDate(vo.getTxnDate());
                checkOtpVO.setOtp(vo.getOtp());
                
                try {
                    String jsonStr = JsonUtil.toJson(checkOtpVO);
                    String url = validationServerUrl + "/api/KGI/CHECK_OTP";
                    checkResp = simpleKgiService.checkOTP(url, jsonStr);
                } catch (Exception e) {
                    log.error("Error converting to JSON", e);
                    checkResp = new CheckOTPResp();
                    checkResp.setStatus("9999");
                    checkResp.setErrorMsg("JSON轉換錯誤");
                }
            }
            
            if (checkResp.getStatus().equals("0000")) {
                response.setRtnCode("0000");
                response.setRtnMessage("驗證成功");
                response.setRtnObj(checkResp);
            } else {
                response.setRtnCode(checkResp.getStatus());
                response.setRtnMessage(checkResp.getErrorMsg());
            }
            
        } catch (Exception e) {
            log.error("驗證OTP時發生錯誤", e);
            response.setRtnCode("9999");
            response.setRtnMessage("系統錯誤");
        }
        
        return response;
    }
}