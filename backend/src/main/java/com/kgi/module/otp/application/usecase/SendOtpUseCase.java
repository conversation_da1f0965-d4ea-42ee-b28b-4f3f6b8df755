package com.kgi.module.otp.application.usecase;

import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.domain.model.OtpChannel;
import com.kgi.module.otp.domain.model.OtpSession;
import com.kgi.module.otp.domain.model.OtpVerification;
import com.kgi.module.otp.domain.service.OtpDomainService;
import com.kgi.module.otp.infrastructure.external.SimpleKgiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 發送 OTP 用例
 * 保持與原系統完全相容
 */
@Slf4j
@Component("otpSendOtpUseCase")
@RequiredArgsConstructor
public class SendOtpUseCase {
    
    private final OtpDomainService otpDomainService;
    private final SimpleKgiService kgiService;
    
    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;
    
    @Transactional
    public SendOTPResp execute(OtpVO request) {
        try {
            log.info("Sending OTP - uniqId: {}, email: {}, phone: {}", 
                    request.getUniqId(), request.getEmail(), request.getUsePhone());
            
            // 判斷發送通道
            OtpChannel channel = OtpChannel.determineChannel(request.getEmail(), request.getUsePhone());
            String recipient = channel == OtpChannel.EMAIL ? request.getEmail() : request.getUsePhone();
            
            // 建立 OTP 驗證
            OtpVerification otpVerification = otpDomainService.createOtpVerification(
                    request.getUniqId(),
                    request.getUniqType(),
                    channel,
                    recipient
            );
            
            // 準備發送參數
            String otpCode = otpVerification.getOtpCode().getCode();
            SendOTPResp response;
            
            // 準備請求 JSON
            String jsonRequest = buildOtpRequest(request, otpCode, channel);
            String apiUrl = buildApiUrl(channel);
            
            // 發送 OTP
            response = kgiService.sendOTP(apiUrl, jsonRequest);
            
            // 處理發送結果
            if (response != null && "0000".equals(response.getStatus())) {
                OtpSession session = OtpSession.builder()
                        .sessionKey(response.getSk())
                        .transactionId(response.getTxnID())
                        .build();
                
                otpDomainService.handleSendSuccess(otpVerification.getId(), session);
                
                log.info("OTP sent successfully - sk: {}, txnId: {}", 
                        response.getSk(), response.getTxnID());
            } else {
                log.error("Failed to send OTP - response: {}", response);
            }
            
            return response;
            
        } catch (IllegalStateException e) {
            // 業務邏輯錯誤（如發送頻率限制）
            log.warn("OTP send failed due to business rule: {}", e.getMessage());
            return createErrorResponse("9001", e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error sending OTP", e);
            return createErrorResponse("9999", "發送OTP失敗，請稍後再試");
        }
    }
    
    private SendOTPResp createErrorResponse(String code, String message) {
        SendOTPResp response = new SendOTPResp();
        response.setStatus(code);
        response.setCode(code);
        response.setMessage(message);
        response.setErrorMsg(message);
        return response;
    }
    
    private String buildOtpRequest(OtpVO request, String otpCode, OtpChannel channel) {
        // 建立 JSON 請求
        StringBuilder json = new StringBuilder();
        json.append("{");
        json.append("\"uniqId\":\"").append(request.getUniqId()).append("\",");
        json.append("\"uniqType\":\"").append(request.getUniqType()).append("\",");
        json.append("\"otpCode\":\"").append(otpCode).append("\",");
        if (channel == OtpChannel.SMS) {
            json.append("\"phone\":\"").append(request.getUsePhone()).append("\"");
        } else {
            json.append("\"email\":\"").append(request.getEmail()).append("\"");
        }
        json.append("}");
        return json.toString();
    }
    
    private String buildApiUrl(OtpChannel channel) {
        // 根據通道返回不同的 API URL
        return channel == OtpChannel.SMS ? "/api/otp/sendSms" : "/api/otp/sendEmail";
    }
}