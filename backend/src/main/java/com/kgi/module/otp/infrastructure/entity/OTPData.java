package com.kgi.module.otp.infrastructure.entity;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Data
@Deprecated
//@Entity // 暫時關閉Entity註解，避免JPA錯誤
@Table(name = "OTPData")
public class OTPData implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "UniqId", nullable = false)
    private String uniqId;

    @Column(name = "UniqType", nullable = false)
    private String uniqType;

    @Column(name = "PhoneNum", nullable = false)
    private String phoneNum;

    /** 20240611 WT20240530001 Maggie 將此欄位更改成儲存該用戶 sendOtp 次數 */
    @Column(name = "OTPSource", nullable = false)
    private String OTPSource;

    @Column(name = "UTime", nullable = false)
    private Date UTime;

    @Column(name = "OTPType", nullable = false)
    private String OTPType;

    @Column(name = "Email")
    private String email;

    @Column(name = "Phone")
    private String phone;

    @Column(name = "OtpCode")
    private String otpCode;

    @Column(name = "CreateTime")
    private Date createTime;

    @Column(name = "TxnId")
    private String txnId;

    @Column(name = "TxnDate")
    private Date txnDate;

    @Column(name = "Sk")
    private String sk;

}
