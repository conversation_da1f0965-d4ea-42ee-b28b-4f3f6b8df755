package com.kgi.module.otp.domain.service;

import com.kgi.module.otp.application.dto.request.SendOTPVO;
import com.kgi.module.otp.application.dto.request.CheckOtpVO;
import com.kgi.module.otp.application.dto.response.SendOTPResp;
import com.kgi.module.otp.application.dto.response.CheckOTPResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 簡化版 Validation Service
 * 用於與外部 Validation Server 溝通
 * 
 * 注意：在實際使用中，OTP 的發送和驗證主要由 SimpleKgiService 處理
 * 本服務主要提供測試和備用功能
 */
@Slf4j
@Service
public class SimpleValidationService {
    
    @Value("${kgi.validation.server.url:}")
    private String validationServerUrl;
    
    /**
     * 發送 OTP 到 Validation Server
     */
    public SendOTPResp sendOtp(SendOTPVO sendOTPVO) {
        SendOTPResp response = new SendOTPResp();
        
        try {
            // 在真實環境中，這裡會呼叫外部 Validation Server
            log.info("Sending OTP to validation server for phone: {}", sendOTPVO.getPhone());
            
            // 模擬成功回應
            response.setStatus("0000");
            response.setCode("0000");
            response.setMessage("OTP已發送");
            response.setSk("VS_SK_" + System.currentTimeMillis());
            response.setTxnID("VS_TXN_" + System.currentTimeMillis());
            response.setTxnDate(new java.text.SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new java.util.Date()));
            
        } catch (Exception e) {
            log.error("Error sending OTP to validation server", e);
            response.setStatus("9999");
            response.setErrorMsg("發送失敗");
        }
        
        return response;
    }
    
    /**
     * 驗證 OTP 與 Validation Server
     */
    public CheckOTPResp verifyOtp(CheckOtpVO checkOtpVO) {
        CheckOTPResp response = new CheckOTPResp();
        
        try {
            // 在真實環境中，這裡會呼叫外部 Validation Server
            log.info("Verifying OTP with validation server, sk: {}, txnId: {}", 
                     checkOtpVO.getSk(), checkOtpVO.getTxnId());
            
            // 模擬成功回應
            response.setStatus("0000");
            response.setCode("0000");
            response.setMessage("驗證成功");
            
        } catch (Exception e) {
            log.error("Error verifying OTP with validation server", e);
            response.setStatus("9999");
            response.setCode("9999");
            response.setErrorMsg("驗證失敗");
        }
        
        return response;
    }
}