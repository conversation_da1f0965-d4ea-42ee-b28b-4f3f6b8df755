package com.kgi.module.otp.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * OTP 驗證聚合根
 * 代表一個 OTP 驗證流程的完整生命週期
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpVerification {
    
    private Long id;
    private String otpId;
    private String uniqId;
    private String uniqType;
    private OtpCode otpCode;
    private OtpChannel channel;
    private String recipient;
    private OtpStatus status;
    private OtpSession session;
    private Integer attemptCount;
    private Integer errorCount;
    private Integer sendCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime sentAt;
    private LocalDateTime verifiedAt;
    private LocalDateTime expiredAt;
    private LocalDateTime lastErrorAt;
    
    /**
     * 生成新的 OTP 碼
     */
    public void generateOtpCode() {
        this.otpCode = OtpCode.generate();
        this.status = OtpStatus.GENERATED;
        this.expiredAt = LocalDateTime.now().plusMinutes(5); // 5分鐘有效期
        this.attemptCount = 0;
    }
    
    /**
     * 標記為已發送
     */
    public void markAsSent(OtpSession session) {
        this.session = session;
        this.status = OtpStatus.SENT;
        this.sentAt = LocalDateTime.now();
    }
    
    /**
     * 驗證 OTP
     */
    public boolean verify(String inputOtpCode) {
        // 增加嘗試次數
        this.attemptCount = (this.attemptCount == null ? 0 : this.attemptCount) + 1;
        
        // 檢查是否過期
        if (isExpired()) {
            this.status = OtpStatus.EXPIRED;
            return false;
        }
        
        // 檢查是否已經驗證過
        if (this.status == OtpStatus.VERIFIED) {
            return false;
        }
        
        // 檢查是否超過最大嘗試次數
        if (isExceedMaxAttempts()) {
            this.status = OtpStatus.FAILED;
            return false;
        }
        
        // 驗證 OTP 碼
        boolean isValid = this.otpCode != null && this.otpCode.matches(inputOtpCode);
        
        if (isValid) {
            this.status = OtpStatus.VERIFIED;
            this.verifiedAt = LocalDateTime.now();
        } else if (this.attemptCount >= 3) {
            this.status = OtpStatus.FAILED;
        }
        
        return isValid;
    }
    
    /**
     * 檢查是否過期
     */
    public boolean isExpired() {
        return expiredAt != null && LocalDateTime.now().isAfter(expiredAt);
    }
    
    /**
     * 檢查是否超過最大嘗試次數
     */
    public boolean isExceedMaxAttempts() {
        return attemptCount != null && attemptCount >= 3;
    }
    
    /**
     * 是否可以重新發送
     */
    public boolean canResend(int intervalSeconds) {
        if (sentAt == null) {
            return true;
        }
        
        LocalDateTime nextAllowedTime = sentAt.plusSeconds(intervalSeconds);
        return LocalDateTime.now().isAfter(nextAllowedTime);
    }
    
    /**
     * 標記為失敗
     */
    public void markAsFailed(String reason) {
        this.status = OtpStatus.FAILED;
        log.warn("OTP verification failed for {}: {}", this.otpId, reason);
    }
    
    /**
     * 獲取剩餘有效時間（秒）
     */
    public long getRemainingSeconds() {
        if (expiredAt == null || isExpired()) {
            return 0;
        }
        
        return java.time.Duration.between(LocalDateTime.now(), expiredAt).getSeconds();
    }
    
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(OtpVerification.class);
}