package com.kgi.module.individual.domain.model;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 匯款詳細資料模型
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
public class RemittanceDetail {
    
    // 案件資訊
    private String caseNo;                // 案件編號
    private String transactionId;         // 交易編號
    
    // 匯款人資訊
    private String payerName;             // 匯款人姓名
    private String payerEngName;          // 匯款人英文姓名
    private String payerCountry;          // 匯款人國家
    
    // 收款人資訊
    private String payeeName;             // 收款人姓名
    private String payeeEngName;          // 收款人英文姓名
    private String payeeId;               // 收款人身分證號/統一編號
    private String payeeAccount;          // 收款人帳號
    private String payeeBankCode;         // 收款銀行代碼
    private String payeeBankName;         // 收款銀行名稱
    
    // 匯款資訊
    private String currency;              // 幣別
    private BigDecimal amount;            // 金額
    private BigDecimal exchangeRate;      // 匯率
    private BigDecimal twdAmount;         // 台幣金額
    private BigDecimal fee;              // 手續費
    private BigDecimal totalAmount;       // 總金額
    
    // 匯款性質
    private String sourceOfFund;          // 匯款性質代碼
    private String sourceOfFundDesc;      // 匯款性質說明
    private String detail;                // 匯款詳細說明
    
    // 狀態資訊
    private String status;                // 狀態
    private LocalDateTime confirmTime;    // 確認時間
    private LocalDateTime createTime;     // 建立時間
    private LocalDateTime updateTime;     // 更新時間
}