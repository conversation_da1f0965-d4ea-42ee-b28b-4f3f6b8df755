package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 身份驗證響應
 * 返回身份驗證結果和相關資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentityVerificationResponse {
    
    /**
     * 驗證是否成功
     */
    private Boolean verified;
    
    /**
     * 驗證結果代碼
     */
    private String resultCode;
    
    /**
     * 驗證結果訊息
     */
    private String message;
    
    /**
     * 驗證時間
     */
    private LocalDateTime verificationTime;
    
    /**
     * 驗證ID (用於後續流程追蹤)
     */
    private String verificationId;
    
    /**
     * 驗證詳情
     */
    private VerificationDetails details;
    
    /**
     * 下一步建議動作
     */
    private List<String> nextActions;
    
    /**
     * 驗證有效期限
     */
    private LocalDateTime expiryTime;
    
    /**
     * 風險等級
     */
    private RiskLevel riskLevel;
    
    /**
     * 驗證詳情內部類
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationDetails {
        
        /**
         * 身分證號驗證結果
         */
        private Boolean idNumberValid;
        
        /**
         * 姓名驗證結果
         */
        private Boolean nameMatched;
        
        /**
         * 生日驗證結果
         */
        private Boolean birthDateMatched;
        
        /**
         * 手機號碼驗證結果
         */
        private Boolean phoneNumberValid;
        
        /**
         * 政府資料庫驗證結果
         */
        private Boolean governmentDataVerified;
        
        /**
         * 黑名單檢查結果
         */
        private Boolean blacklistClean;
        
        /**
         * 信用評分 (可選)
         */
        private Integer creditScore;
        
        /**
         * 驗證使用的資料來源
         */
        private List<String> dataSources;
    }
    
    /**
     * 風險等級枚舉
     */
    public enum RiskLevel {
        /**
         * 低風險
         */
        LOW,
        
        /**
         * 中等風險
         */
        MEDIUM,
        
        /**
         * 高風險
         */
        HIGH,
        
        /**
         * 極高風險
         */
        CRITICAL
    }
    
    /**
     * 快速建立成功響應
     */
    public static IdentityVerificationResponse success(String verificationId, String message) {
        return IdentityVerificationResponse.builder()
                .verified(true)
                .resultCode("SUCCESS")
                .message(message)
                .verificationTime(LocalDateTime.now())
                .verificationId(verificationId)
                .expiryTime(LocalDateTime.now().plusHours(24))
                .riskLevel(RiskLevel.LOW)
                .build();
    }
    
    /**
     * 快速建立失敗響應
     */
    public static IdentityVerificationResponse failure(String resultCode, String message) {
        return IdentityVerificationResponse.builder()
                .verified(false)
                .resultCode(resultCode)
                .message(message)
                .verificationTime(LocalDateTime.now())
                .riskLevel(RiskLevel.HIGH)
                .build();
    }
}