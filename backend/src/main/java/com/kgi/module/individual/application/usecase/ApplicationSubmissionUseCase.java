package com.kgi.module.individual.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.core.application.dto.DigitalRemittanceRequest;
import com.kgi.module.individual.application.dto.response.ApplicationSubmissionResponse;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 申請提交用例
 * 負責處理解款申請的提交和確認
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationSubmissionUseCase {
    
    private final IndividualRemittanceRepository remittanceRepository;
    
    /**
     * 提交解款申請 (用於Controller)
     */
    @Transactional
    public ApplicationSubmissionResponse submitApplication(DigitalRemittanceRequest request) {
        log.info("提交解款申請: theirRefNo={}", request.getTheirRefNo());
        
        try {
            // 將前端請求轉換為內部請求
            ApplicationSubmissionRequest internalRequest = ApplicationSubmissionRequest.builder()
                    .remittanceId(request.getTheirRefNo()) // 使用theirRefNo查詢
                    .submitTime(LocalDateTime.now())
                    .confirmations(ApplicationConfirmations.builder()
                            .dataAccuracy(true)
                            .termsAgreed(true)
                            .identityVerified(true)
                            .remittanceConfirmed(true)
                            .build())
                    .build();
            
            // 執行內部提交程序
            ApplicationSubmissionResult result = submitApplicationInternal(internalRequest);
            
            // 轉換為Controller響應格式
            return ApplicationSubmissionResponse.builder()
                    .applicationId(result.getApplicationId())
                    .status(mapToApiStatus(result.getStatus()))
                    .processingProgress(ApplicationSubmissionResponse.ProcessingProgress.builder()
                            .currentStep("申請已提交")
                            .estimatedCompletion(result.getEstimatedProcessTime())
                            .nextStepDescription("等待系統處理")
                            .build())
                    .followUpInfo(ApplicationSubmissionResponse.FollowUpInfo.builder()
                            .trackingNumber(result.getTrackingNumber())
                            .contactPhone(result.getContactInfo().getPhoneNumber())
                            .serviceEmail(result.getContactInfo().getEmail())
                            .serviceHours(result.getContactInfo().getServiceHours())
                            .build())
                    .build();
            
        } catch (Exception e) {
            log.error("提交解款申請失敗: theirRefNo={}, error={}", 
                    request.getTheirRefNo(), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 提交解款申請 (內部版本)
     * 
     * @param request 申請提交請求
     * @return 申請提交結果
     */
    @Transactional
    public ApplicationSubmissionResult submitApplicationInternal(ApplicationSubmissionRequest request) {
        log.info("開始提交解款申請: remittanceId={}", request.getRemittanceId());
        
        try {
            // 1. 驗證請求資料
            validateSubmissionRequest(request);
            
            // 2. 查詢解款記錄
            IndividualRemittance remittance = findRemittanceRecord(request.getRemittanceId());
            
            // 3. 驗證解款狀態
            validateRemittanceForSubmission(remittance);
            
            // 4. 驗證確認項目
            validateConfirmations(request.getConfirmations());
            
            // 5. 更新解款記錄狀態
            updateRemittanceForSubmission(remittance, request);
            
            // 6. 儲存申請記錄
            IndividualRemittance savedRemittance = remittanceRepository.save(remittance);
            
            // 7. 觸發後續處理流程
            triggerPostSubmissionProcess(savedRemittance);
            
            // 8. 建立回應
            ApplicationSubmissionResult result = createSubmissionResult(savedRemittance, request);
            
            log.info("解款申請提交完成: applicationId={}", result.getApplicationId());
            return result;
            
        } catch (BusinessException e) {
            log.error("申請提交業務異常: remittanceId={}, error={}", 
                    request.getRemittanceId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("申請提交系統異常: remittanceId={}", request.getRemittanceId(), e);
            throw new BusinessException("APPLICATION_SUBMISSION_ERROR", 
                    "申請提交系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 確認申請資料
     * 
     * @param remittanceId 解款編號
     * @param confirmationType 確認類型
     * @return 確認結果
     */
    @Transactional
    public ApplicationConfirmationResult confirmApplication(String remittanceId, 
                                                          ApplicationConfirmationType confirmationType) {
        log.info("確認申請資料: remittanceId={}, type={}", remittanceId, confirmationType);
        
        try {
            // 1. 查詢解款記錄
            IndividualRemittance remittance = findRemittanceRecord(remittanceId);
            
            // 2. 處理確認操作
            switch (confirmationType) {
                case ACCEPT:
                    return processAcceptConfirmation(remittance);
                case REJECT:
                    return processRejectConfirmation(remittance);
                case MODIFY:
                    return processModifyConfirmation(remittance);
                default:
                    throw new BusinessException("INVALID_CONFIRMATION_TYPE", 
                            "無效的確認類型: " + confirmationType);
            }
            
        } catch (BusinessException e) {
            log.error("申請確認業務異常: remittanceId={}, error={}", remittanceId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("申請確認系統異常: remittanceId={}", remittanceId, e);
            throw new BusinessException("APPLICATION_CONFIRMATION_ERROR", 
                    "申請確認系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 映射到API狀態
     */
    private ApplicationSubmissionResponse.ApplicationStatus mapToApiStatus(ApplicationStatus status) {
        if (status == null) {
            return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
        }
        
        switch (status) {
            case ACCEPTED:
                return ApplicationSubmissionResponse.ApplicationStatus.ACCEPTED;
            case REJECTED:
                return ApplicationSubmissionResponse.ApplicationStatus.REJECTED;
            case UNDER_MODIFICATION:
                return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
            case PENDING_APPROVAL:
                return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
            case COMPLETED:
                return ApplicationSubmissionResponse.ApplicationStatus.ACCEPTED;
            case FAILED:
                return ApplicationSubmissionResponse.ApplicationStatus.REJECTED;
            default:
                return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
        }
    }
    
    /**
     * 驗證提交請求
     */
    private void validateSubmissionRequest(ApplicationSubmissionRequest request) {
        if (request == null) {
            throw new BusinessException("INVALID_REQUEST", "申請提交請求不能為空");
        }
        
        if (request.getRemittanceId() == null || request.getRemittanceId().trim().isEmpty()) {
            throw new BusinessException("INVALID_REMITTANCE_ID", "解款編號不能為空");
        }
        
        if (request.getConfirmations() == null) {
            throw new BusinessException("INVALID_CONFIRMATIONS", "確認項目不能為空");
        }
        
        if (request.getSubmitTime() == null) {
            throw new BusinessException("INVALID_SUBMIT_TIME", "提交時間不能為空");
        }
    }
    
    /**
     * 查詢解款記錄
     */
    private IndividualRemittance findRemittanceRecord(String remittanceId) {
        return remittanceRepository.findByRemittanceId(remittanceId)
                .orElseThrow(() -> new BusinessException("REMITTANCE_NOT_FOUND", 
                        "解款記錄不存在: " + remittanceId));
    }
    
    /**
     * 驗證解款狀態是否可提交
     */
    private void validateRemittanceForSubmission(IndividualRemittance remittance) {
        // 檢查是否已完成或失敗
        if (remittance.isCompleted()) {
            throw new BusinessException("REMITTANCE_ALREADY_COMPLETED", "解款已完成，無法重複提交");
        }
        
        if (remittance.isFailed()) {
            throw new BusinessException("REMITTANCE_FAILED", "解款已失敗，無法提交申請");
        }
        
        if (remittance.isCancelled()) {
            throw new BusinessException("REMITTANCE_CANCELLED", "解款已取消，無法提交申請");
        }
        
        // 檢查是否已通過必要驗證
        if (!remittance.isVerified()) {
            throw new BusinessException("VERIFICATION_REQUIRED", "需要完成身分驗證才能提交申請");
        }
        
        // 檢查是否需要補件
        if (Boolean.TRUE.equals(remittance.getRequiresSupplement())) {
            throw new BusinessException("SUPPLEMENT_REQUIRED", "需要完成補件才能提交申請");
        }
    }
    
    /**
     * 驗證確認項目
     */
    private void validateConfirmations(ApplicationConfirmations confirmations) {
        if (!Boolean.TRUE.equals(confirmations.getDataAccuracy())) {
            throw new BusinessException("DATA_ACCURACY_NOT_CONFIRMED", "必須確認資料正確性");
        }
        
        if (!Boolean.TRUE.equals(confirmations.getTermsAgreed())) {
            throw new BusinessException("TERMS_NOT_AGREED", "必須同意使用條款");
        }
        
        if (!Boolean.TRUE.equals(confirmations.getIdentityVerified())) {
            throw new BusinessException("IDENTITY_NOT_VERIFIED", "必須完成身分驗證");
        }
        
        if (!Boolean.TRUE.equals(confirmations.getRemittanceConfirmed())) {
            throw new BusinessException("REMITTANCE_NOT_CONFIRMED", "必須確認解款資訊");
        }
    }
    
    /**
     * 更新解款記錄以進行提交
     */
    private void updateRemittanceForSubmission(IndividualRemittance remittance, 
                                             ApplicationSubmissionRequest request) {
        // 更新處理狀態
        remittance.startProcessing();
        
        // 設置提交相關資訊
        String note = String.format("申請提交於 %s", LocalDateTime.now());
        if (request.getDigitalSignature() != null) {
            note += ", 數位簽章: " + request.getDigitalSignature().substring(0, 10) + "...";
        }
        
        remittance.setSystemNote(
                (remittance.getSystemNote() == null ? "" : remittance.getSystemNote() + "; ") + note
        );
        
        // 更新預計完成時間
        if (remittance.getEstimatedCompletionTime() == null) {
            LocalDateTime estimatedTime = Boolean.TRUE.equals(remittance.getIsUrgent()) ?
                    LocalDateTime.now().plusHours(2) : LocalDateTime.now().plusDays(1);
            remittance.setEstimatedCompletionTime(estimatedTime);
        }
        
        log.debug("解款記錄已更新為提交狀態: remittanceId={}", remittance.getRemittanceId());
    }
    
    /**
     * 觸發提交後處理流程
     */
    private void triggerPostSubmissionProcess(IndividualRemittance remittance) {
        try {
            // TODO: 整合工作流管理模組
            // workflowFacade.startApplicationProcess(remittance.getRemittanceId());
            
            // TODO: 發送提交通知
            // notificationFacade.sendApplicationSubmittedNotification(remittance);
            
            // TODO: 觸發風險檢核
            // riskAssessmentFacade.performRiskCheck(remittance);
            
            log.debug("提交後處理流程已觸發: remittanceId={}", remittance.getRemittanceId());
            
        } catch (Exception e) {
            log.error("觸發提交後處理流程失敗: remittanceId={}, error={}", 
                    remittance.getRemittanceId(), e.getMessage(), e);
            // 記錄錯誤但不影響主流程
        }
    }
    
    /**
     * 建立提交結果
     */
    private ApplicationSubmissionResult createSubmissionResult(IndividualRemittance remittance, 
                                                             ApplicationSubmissionRequest request) {
        // 生成追蹤號碼
        String trackingNumber = generateTrackingNumber();
        
        return ApplicationSubmissionResult.builder()
                .applicationId(remittance.getRemittanceId())
                .status(mapToApplicationStatus(remittance.getProcessingStatus()))
                .submitTime(request.getSubmitTime())
                .estimatedProcessTime(remittance.getEstimatedCompletionTime())
                .trackingNumber(trackingNumber)
                .receiptUrl(generateReceiptUrl(remittance.getRemittanceId()))
                .nextSteps(generateNextSteps(remittance))
                .contactInfo(createContactInfo())
                .build();
    }
    
    /**
     * 處理接受確認
     */
    private ApplicationConfirmationResult processAcceptConfirmation(IndividualRemittance remittance) {
        // 更新狀態為處理中
        remittance.startProcessing();
        remittanceRepository.save(remittance);
        
        return ApplicationConfirmationResult.builder()
                .success(true)
                .confirmationId(generateConfirmationId())
                .processingStatus(mapToApplicationStatus(remittance.getProcessingStatus()))
                .estimatedProcessingTime(remittance.getEstimatedCompletionTime())
                .nextStepInstructions("申請已確認接受，正在進行處理")
                .build();
    }
    
    /**
     * 處理拒絕確認
     */
    private ApplicationConfirmationResult processRejectConfirmation(IndividualRemittance remittance) {
        // 取消解款
        remittance.cancel("客戶拒絕解款申請");
        remittanceRepository.save(remittance);
        
        return ApplicationConfirmationResult.builder()
                .success(true)
                .confirmationId(generateConfirmationId())
                .processingStatus(mapToApplicationStatus(remittance.getProcessingStatus()))
                .estimatedProcessingTime(null)
                .nextStepInstructions("申請已拒絕，解款流程已取消")
                .build();
    }
    
    /**
     * 處理修改確認
     */
    private ApplicationConfirmationResult processModifyConfirmation(IndividualRemittance remittance) {
        // 設置為需要補件狀態
        remittance.requireSupplement(LocalDateTime.now().plusDays(7), "客戶要求修改申請資料");
        remittanceRepository.save(remittance);
        
        return ApplicationConfirmationResult.builder()
                .success(true)
                .confirmationId(generateConfirmationId())
                .processingStatus(mapToApplicationStatus(remittance.getProcessingStatus()))
                .estimatedProcessingTime(remittance.getSupplementDeadline())
                .nextStepInstructions("申請已標記為需要修改，請在期限內提供補件資料")
                .build();
    }
    
    /**
     * 映射到應用程式狀態
     */
    private ApplicationStatus mapToApplicationStatus(IndividualRemittance.ProcessingStatus status) {
        if (status == null) {
            return ApplicationStatus.PENDING_APPROVAL;
        }
        
        switch (status) {
            case PENDING:
                return ApplicationStatus.PENDING_APPROVAL;
            case PROCESSING:
                return ApplicationStatus.ACCEPTED;
            case COMPLETED:
                return ApplicationStatus.COMPLETED;
            case FAILED:
                return ApplicationStatus.FAILED;
            case CANCELLED:
                return ApplicationStatus.REJECTED;
            default:
                return ApplicationStatus.PENDING_APPROVAL;
        }
    }
    
    /**
     * 生成追蹤號碼
     */
    private String generateTrackingNumber() {
        return "TRK" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * 生成確認ID
     */
    private String generateConfirmationId() {
        return "CONF" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6).toUpperCase();
    }
    
    /**
     * 生成收據URL
     */
    private String generateReceiptUrl(String remittanceId) {
        return String.format("/api/v1/ibr/individual/application/%s/receipt", remittanceId);
    }
    
    /**
     * 生成下一步驟
     */
    private List<String> generateNextSteps(IndividualRemittance remittance) {
        return List.of(
                "等待系統自動處理",
                "如有疑問請聯繫客服",
                "可隨時查詢處理進度"
        );
    }
    
    /**
     * 建立聯繫資訊
     */
    private ContactInfo createContactInfo() {
        return ContactInfo.builder()
                .phoneNumber("0800-212-365")
                .email("<EMAIL>")
                .serviceHours("週一至週五 09:00-17:30")
                .build();
    }
    
    // ==================== 請求和響應類別 ====================
    
    /**
     * 申請提交請求
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ApplicationSubmissionRequest {
        private String remittanceId;
        private ApplicationConfirmations confirmations;
        private String digitalSignature;
        private LocalDateTime submitTime;
    }
    
    /**
     * 申請確認項目
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ApplicationConfirmations {
        private Boolean dataAccuracy;
        private Boolean termsAgreed;
        private Boolean identityVerified;
        private Boolean remittanceConfirmed;
    }
    
    /**
     * 申請提交結果
     */
    @lombok.Data
    @lombok.Builder
    public static class ApplicationSubmissionResult {
        private String applicationId;
        private ApplicationStatus status;
        private LocalDateTime submitTime;
        private LocalDateTime estimatedProcessTime;
        private String trackingNumber;
        private String receiptUrl;
        private List<String> nextSteps;
        private ContactInfo contactInfo;
    }
    
    /**
     * 申請確認結果
     */
    @lombok.Data
    @lombok.Builder
    public static class ApplicationConfirmationResult {
        private Boolean success;
        private String confirmationId;
        private ApplicationStatus processingStatus;
        private LocalDateTime estimatedProcessingTime;
        private String nextStepInstructions;
        private String errorMessage;
    }
    
    /**
     * 聯繫資訊
     */
    @lombok.Data
    @lombok.Builder
    public static class ContactInfo {
        private String phoneNumber;
        private String email;
        private String serviceHours;
    }
    
    /**
     * 申請狀態枚舉
     */
    public enum ApplicationStatus {
        ACCEPTED("已接受"),
        REJECTED("已拒絕"),
        UNDER_MODIFICATION("修改中"),
        PENDING_APPROVAL("等待審核"),
        COMPLETED("處理完成"),
        FAILED("處理失敗");
        
        private final String description;
        
        ApplicationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 確認類型枚舉
     */
    public enum ApplicationConfirmationType {
        ACCEPT("接受"),
        REJECT("拒絕"),
        MODIFY("修改");
        
        private final String description;
        
        ApplicationConfirmationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}