package com.kgi.module.individual.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * OTP驗證請求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpVerificationRequest {
    
    /**
     * 解款編號或會話編號
     */
    @NotBlank(message = "解款編號不能為空")
    @Size(max = 50, message = "解款編號長度不能超過50字元")
    private String businessKey;
    
    /**
     * 台灣身分證號
     */
    @NotBlank(message = "台灣身分證號不能為空")
    @Size(min = 10, max = 10, message = "台灣身分證號長度必須為10字元")
    private String taiwanId;
    
    /**
     * 解款編號
     */
    @Size(max = 50, message = "解款編號長度不能超過50字元")
    private String remittanceId;
    
    /**
     * 手機號碼
     */
    @NotBlank(message = "手機號碼不能為空")
    @Pattern(regexp = "^09\\d{8}$", message = "手機號碼格式錯誤，應為09開頭的10位數字")
    private String phoneNumber;
    
    /**
     * OTP驗證碼
     */
    @NotBlank(message = "OTP驗證碼不能為空")
    @Pattern(regexp = "^\\d{6}$", message = "OTP驗證碼應為6位數字")
    private String otpCode;
    
    /**
     * 驗證用途
     */
    @NotBlank(message = "驗證用途不能為空")
    private String purpose;
    
    /**
     * 客戶端裝置資訊
     */
    private String deviceInfo;
    
    /**
     * 客戶端IP位址
     */
    private String clientIp;
    
    /**
     * 時間戳記(防重放攻擊)
     */
    private Long timestamp;
    
    /**
     * 驗證用途枚舉
     */
    public enum OtpPurpose {
        /**
         * 登入驗證
         */
        LOGIN_VERIFICATION("login_verification", "登入驗證"),
        
        /**
         * 交易確認
         */
        TRANSACTION_CONFIRMATION("transaction_confirmation", "交易確認"),
        
        /**
         * 解款申請驗證
         */
        REMITTANCE_APPLICATION("remittance_application", "解款申請驗證"),
        
        /**
         * 身份驗證
         */
        IDENTITY_VERIFICATION("identity_verification", "身份驗證"),
        
        /**
         * 密碼重設
         */
        PASSWORD_RESET("password_reset", "密碼重設"),
        
        /**
         * 帳戶變更
         */
        ACCOUNT_CHANGE("account_change", "帳戶變更");
        
        private final String code;
        private final String description;
        
        OtpPurpose(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static OtpPurpose fromCode(String code) {
            for (OtpPurpose purpose : values()) {
                if (purpose.code.equals(code)) {
                    return purpose;
                }
            }
            throw new IllegalArgumentException("未知的OTP用途代碼: " + code);
        }
    }
    
    /**
     * 驗證OTP格式
     */
    public boolean isValidOtpFormat() {
        return otpCode != null && otpCode.matches("^\\d{6}$");
    }
    
    /**
     * 驗證手機號碼格式
     */
    public boolean isValidPhoneFormat() {
        return phoneNumber != null && phoneNumber.matches("^09\\d{8}$");
    }
    
    /**
     * 取得遮罩手機號碼
     */
    public String getMaskedPhoneNumber() {
        if (phoneNumber == null || phoneNumber.length() != 10) {
            return phoneNumber;
        }
        
        // 顯示前3碼和後2碼，中間用*代替
        return phoneNumber.substring(0, 3) + "*****" + phoneNumber.substring(8);
    }
    
    /**
     * 檢查請求是否過期
     */
    public boolean isExpired(long maxAgeInSeconds) {
        if (timestamp == null) {
            return true;
        }
        
        long currentTime = System.currentTimeMillis();
        long requestTime = timestamp;
        long ageInSeconds = (currentTime - requestTime) / 1000;
        
        return ageInSeconds > maxAgeInSeconds;
    }
    
    /**
     * 取得OTP用途描述
     */
    public String getPurposeDescription() {
        try {
            return OtpPurpose.fromCode(purpose).getDescription();
        } catch (IllegalArgumentException e) {
            return purpose;
        }
    }
    
    /**
     * 驗證必要欄位
     */
    public boolean hasRequiredFields() {
        return businessKey != null && !businessKey.trim().isEmpty() &&
               phoneNumber != null && !phoneNumber.trim().isEmpty() &&
               otpCode != null && !otpCode.trim().isEmpty() &&
               purpose != null && !purpose.trim().isEmpty();
    }
}