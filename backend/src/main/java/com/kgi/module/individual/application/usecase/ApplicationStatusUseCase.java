package com.kgi.module.individual.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.individual.application.dto.response.ApplicationSubmissionResponse;
import com.kgi.module.individual.application.usecase.ApplicationSubmissionUseCase.ApplicationStatus;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 申請狀態查詢用例
 * 負責處理申請狀態查詢和歷史記錄查詢
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApplicationStatusUseCase {
    
    private final IndividualRemittanceRepository remittanceRepository;
    
    /**
     * 查詢申請狀態 (用於Controller)
     */
    @Transactional(readOnly = true)
    public ApplicationSubmissionResponse getApplicationStatus(String applicationId) {
        log.info("查詢申請狀態: applicationId={}", applicationId);
        
        try {
            IndividualRemittance remittance = findRemittanceRecord(applicationId);
            
            return ApplicationSubmissionResponse.builder()
                    .applicationId(applicationId)
                    .status(mapToApiStatus(remittance.getProcessingStatus()))
                    .processingProgress(ApplicationSubmissionResponse.ProcessingProgress.builder()
                            .currentStep(getProcessingStepDescription(remittance.getProcessingStatus()))
                            .progressPercentage(calculateProgressPercentage(remittance))
                            .estimatedCompletion(remittance.getEstimatedCompletionTime())
                            .nextStepDescription(getNextStepDescription(remittance))
                            .build())
                    .followUpInfo(ApplicationSubmissionResponse.FollowUpInfo.builder()
                            .trackingNumber("TRK" + remittance.getRemittanceId())
                            .contactPhone("0800-212-365")
                            .serviceEmail("<EMAIL>")
                            .serviceHours("週一至週五 09:00-17:30")
                            .build())
                    .build();
                    
        } catch (Exception e) {
            log.error("查詢申請狀態失敗: applicationId={}, error={}", 
                    applicationId, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 查詢申請歷史 (用於Controller)
     */
    @Transactional(readOnly = true)
    public List<ApplicationSubmissionResponse> getApplicationHistory(String taiwanId, int page, int size) {
        log.info("查詢申請歷史: taiwanId={}, page={}, size={}", 
                maskIdNumber(taiwanId), page, size);
        
        try {
            Pageable pageable = PageRequest.of(page, size);
            List<IndividualRemittance> remittances = findRemittancesByTaiwanId(taiwanId, pageable);
            
            return remittances.stream()
                    .map(remittance -> ApplicationSubmissionResponse.builder()
                            .applicationId(remittance.getRemittanceId())
                            .status(mapToApiStatus(remittance.getProcessingStatus()))
                            .processingProgress(ApplicationSubmissionResponse.ProcessingProgress.builder()
                                    .currentStep(getProcessingStepDescription(remittance.getProcessingStatus()))
                                    .progressPercentage(calculateProgressPercentage(remittance))
                                    .estimatedCompletion(remittance.getEstimatedCompletionTime())
                                    .build())
                            .followUpInfo(ApplicationSubmissionResponse.FollowUpInfo.builder()
                                    .trackingNumber("TRK" + remittance.getRemittanceId())
                                    .build())
                            .build())
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("查詢申請歷史失敗: taiwanId={}, error={}", 
                    maskIdNumber(taiwanId), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 查詢申請狀態 (內部版本)
     * 
     * @param applicationId 申請編號
     * @return 申請狀態響應
     */
    @Transactional(readOnly = true)
    public ApplicationStatusResponse getApplicationStatusInternal(String applicationId) {
        log.info("查詢申請狀態: applicationId={}", applicationId);
        
        try {
            // 1. 查詢解款記錄
            IndividualRemittance remittance = findRemittanceRecord(applicationId);
            
            // 2. 建立狀態歷史
            List<StatusHistoryItem> statusHistory = buildStatusHistory(remittance);
            
            // 3. 計算處理進度
            int progressPercentage = calculateProgressPercentage(remittance);
            
            // 4. 取得需要的操作
            List<RequiredAction> requiredActions = getRequiredActions(remittance);
            
            // 5. 建立響應
            ApplicationStatusResponse response = ApplicationStatusResponse.builder()
                    .applicationId(applicationId)
                    .currentStatus(mapToApplicationStatus(remittance.getProcessingStatus()))
                    .statusHistory(statusHistory)
                    .progressPercentage(progressPercentage)
                    .estimatedCompletionTime(remittance.getEstimatedCompletionTime())
                    .requiredActions(requiredActions)
                    .lastUpdate(createLastUpdate(remittance))
                    .build();
            
            log.debug("申請狀態查詢完成: applicationId={}, status={}", 
                    applicationId, response.getCurrentStatus());
            
            return response;
            
        } catch (BusinessException e) {
            log.error("申請狀態查詢業務異常: applicationId={}, error={}", applicationId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("申請狀態查詢系統異常: applicationId={}", applicationId, e);
            throw new BusinessException("APPLICATION_STATUS_QUERY_ERROR", 
                    "申請狀態查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 取得處理步驟描述
     */
    private String getProcessingStepDescription(IndividualRemittance.ProcessingStatus status) {
        if (status == null) {
            return "等待處理";
        }
        
        switch (status) {
            case PENDING:
                return "等待審核";
            case PROCESSING:
                return "處理中";
            case COMPLETED:
                return "處理完成";
            case FAILED:
                return "處理失敗";
            case CANCELLED:
                return "已取消";
            default:
                return "等待處理";
        }
    }
    
    /**
     * 取得下一步驟描述
     */
    private String getNextStepDescription(IndividualRemittance remittance) {
        if (remittance.getProcessingStatus() == null) {
            return "系統正在驗證申請資料";
        }
        
        switch (remittance.getProcessingStatus()) {
            case PENDING:
                if (Boolean.TRUE.equals(remittance.getRequiresSupplement())) {
                    return "請提供補件資料";
                }
                return "系統正在驗證申請資料";
            case PROCESSING:
                return "系統正在處理您的申請";
            case COMPLETED:
                return "申請已完成處理";
            case FAILED:
                return "請聯繫客服人員";
            case CANCELLED:
                return "申請已取消";
            default:
                return "請等待進一步通知";
        }
    }
    
    /**
     * 映射到API狀態
     */
    private ApplicationSubmissionResponse.ApplicationStatus mapToApiStatus(IndividualRemittance.ProcessingStatus status) {
        if (status == null) {
            return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
        }
        
        switch (status) {
            case PENDING:
            case PROCESSING:
                return ApplicationSubmissionResponse.ApplicationStatus.ACCEPTED;
            case COMPLETED:
                return ApplicationSubmissionResponse.ApplicationStatus.ACCEPTED;
            case FAILED:
            case CANCELLED:
                return ApplicationSubmissionResponse.ApplicationStatus.REJECTED;
            default:
                return ApplicationSubmissionResponse.ApplicationStatus.UNDER_MODIFICATION;
        }
    }
    
    /**
     * 查詢申請歷史記錄 (內部版本)
     * 
     * @param idNumber 身份證號
     * @param pageSize 每頁筆數
     * @param page 頁數
     * @return 申請歷史記錄
     */
    @Transactional(readOnly = true)
    public ApplicationHistoryResponse getApplicationHistoryInternal(String idNumber, int pageSize, int page) {
        log.info("查詢申請歷史: idNumber={}, page={}, size={}", 
                maskIdNumber(idNumber), page, pageSize);
        
        try {
            // 1. 驗證參數
            validateHistoryQueryParams(idNumber, pageSize, page);
            
            // 2. 轉換分頁參數 (前端從1開始，後端從0開始)
            Pageable pageable = PageRequest.of(page - 1, pageSize);
            
            // 3. 查詢申請記錄
            // 注意：這裡需要根據身份證號查詢，需要在Repository中添加相應方法
            List<IndividualRemittance> remittances = findRemittancesByTaiwanId(idNumber, pageable);
            
            // 4. 轉換為歷史記錄項目
            List<ApplicationHistoryItem> applications = remittances.stream()
                    .map(this::convertToHistoryItem)
                    .collect(Collectors.toList());
            
            // 5. 計算總數 (簡化實作，實際應該有專門的count查詢)
            long totalCount = applications.size();
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            
            ApplicationHistoryResponse response = ApplicationHistoryResponse.builder()
                    .applications(applications)
                    .totalCount(totalCount)
                    .currentPage(page)
                    .totalPages(totalPages)
                    .build();
            
            log.debug("申請歷史查詢完成: idNumber={}, totalCount={}", 
                    maskIdNumber(idNumber), totalCount);
            
            return response;
            
        } catch (BusinessException e) {
            log.error("申請歷史查詢業務異常: idNumber={}, error={}", 
                    maskIdNumber(idNumber), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("申請歷史查詢系統異常: idNumber={}", maskIdNumber(idNumber), e);
            throw new BusinessException("APPLICATION_HISTORY_QUERY_ERROR", 
                    "申請歷史查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢處理統計
     * 
     * @return 處理統計資訊
     */
    @Transactional(readOnly = true)
    public ProcessingStatistics getProcessingStatistics() {
        log.debug("查詢處理統計");
        
        try {
            // TODO: 實際應該有專門的統計查詢方法
            // 這裡簡化實作，實際環境需要優化查詢效率
            
            List<IndividualRemittance> allRemittances = remittanceRepository.findAll();
            
            long totalApplications = allRemittances.size();
            long pendingCount = allRemittances.stream()
                    .mapToLong(r -> IndividualRemittance.ProcessingStatus.PENDING.equals(r.getProcessingStatus()) ? 1 : 0)
                    .sum();
            long processingCount = allRemittances.stream()
                    .mapToLong(r -> IndividualRemittance.ProcessingStatus.PROCESSING.equals(r.getProcessingStatus()) ? 1 : 0)
                    .sum();
            long completedCount = allRemittances.stream()
                    .mapToLong(r -> IndividualRemittance.ProcessingStatus.COMPLETED.equals(r.getProcessingStatus()) ? 1 : 0)
                    .sum();
            long failedCount = allRemittances.stream()
                    .mapToLong(r -> IndividualRemittance.ProcessingStatus.FAILED.equals(r.getProcessingStatus()) ? 1 : 0)
                    .sum();
            
            double completionRate = totalApplications > 0 ? (double) completedCount / totalApplications * 100 : 0;
            
            return ProcessingStatistics.builder()
                    .totalApplications(totalApplications)
                    .pendingCount(pendingCount)
                    .processingCount(processingCount)
                    .completedCount(completedCount)
                    .failedCount(failedCount)
                    .completionRate(completionRate)
                    .lastUpdated(LocalDateTime.now())
                    .build();
            
        } catch (Exception e) {
            log.error("查詢處理統計異常", e);
            throw new BusinessException("PROCESSING_STATISTICS_ERROR", 
                    "處理統計查詢異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢解款記錄
     */
    private IndividualRemittance findRemittanceRecord(String applicationId) {
        return remittanceRepository.findByRemittanceId(applicationId)
                .orElseThrow(() -> new BusinessException("APPLICATION_NOT_FOUND", 
                        "申請記錄不存在: " + applicationId));
    }
    
    /**
     * 根據身份證號查詢解款記錄
     */
    private List<IndividualRemittance> findRemittancesByTaiwanId(String taiwanId, Pageable pageable) {
        // 實際環境需要在Repository中添加此方法
        // 這裡使用現有的分頁查詢方法
        try {
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusYears(1); // 查詢一年內的記錄
            
            Page<IndividualRemittance> page = remittanceRepository.findByTaiwanIdAndDateRange(
                    taiwanId, startDate, endDate, pageable);
            
            return page.getContent();
        } catch (Exception e) {
            log.warn("使用日期範圍查詢失敗，返回空列表: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 建立狀態歷史
     */
    private List<StatusHistoryItem> buildStatusHistory(IndividualRemittance remittance) {
        List<StatusHistoryItem> history = new ArrayList<>();
        
        // 建立時間
        if (remittance.getCreatedAt() != null) {
            history.add(StatusHistoryItem.builder()
                    .status(ApplicationStatus.PENDING_APPROVAL)
                    .timestamp(remittance.getCreatedAt())
                    .description("申請已建立")
                    .build());
        }
        
        // 處理中
        if (IndividualRemittance.ProcessingStatus.PROCESSING.equals(remittance.getProcessingStatus()) ||
            IndividualRemittance.ProcessingStatus.COMPLETED.equals(remittance.getProcessingStatus())) {
            history.add(StatusHistoryItem.builder()
                    .status(ApplicationStatus.ACCEPTED)
                    .timestamp(remittance.getUpdatedAt())
                    .description("申請已開始處理")
                    .build());
        }
        
        // 完成時間
        if (remittance.getCompletedDate() != null) {
            history.add(StatusHistoryItem.builder()
                    .status(ApplicationStatus.COMPLETED)
                    .timestamp(remittance.getCompletedDate())
                    .description("申請處理完成")
                    .build());
        }
        
        return history;
    }
    
    /**
     * 計算處理進度
     */
    private int calculateProgressPercentage(IndividualRemittance remittance) {
        if (remittance.getProcessingStatus() == null) {
            return 0;
        }
        
        switch (remittance.getProcessingStatus()) {
            case PENDING:
                return 10;
            case PROCESSING:
                return 50;
            case COMPLETED:
                return 100;
            case FAILED:
            case CANCELLED:
                return 0;
            default:
                return 0;
        }
    }
    
    /**
     * 取得需要的操作
     */
    private List<RequiredAction> getRequiredActions(IndividualRemittance remittance) {
        List<RequiredAction> actions = new ArrayList<>();
        
        if (Boolean.TRUE.equals(remittance.getRequiresSupplement())) {
            actions.add(RequiredAction.builder()
                    .action("PROVIDE_SUPPLEMENT")
                    .description("提供補件資料")
                    .dueDate(remittance.getSupplementDeadline())
                    .build());
        }
        
        if (remittance.needsVerification()) {
            actions.add(RequiredAction.builder()
                    .action("COMPLETE_VERIFICATION")
                    .description("完成身分驗證")
                    .dueDate(LocalDateTime.now().plusDays(3))
                    .build());
        }
        
        return actions;
    }
    
    /**
     * 建立最新更新資訊
     */
    private LastUpdateInfo createLastUpdate(IndividualRemittance remittance) {
        return LastUpdateInfo.builder()
                .timestamp(remittance.getUpdatedAt() != null ? remittance.getUpdatedAt() : remittance.getCreatedAt())
                .description(remittance.getBusinessStatusDesc() != null ? 
                        remittance.getBusinessStatusDesc() : "狀態更新")
                .updatedBy("系統")
                .build();
    }
    
    /**
     * 轉換為歷史記錄項目
     */
    private ApplicationHistoryItem convertToHistoryItem(IndividualRemittance remittance) {
        return ApplicationHistoryItem.builder()
                .applicationId(remittance.getRemittanceId())
                .submitTime(remittance.getCreatedAt())
                .status(mapToApplicationStatus(remittance.getProcessingStatus()))
                .remittanceAmount(remittance.getAmount())
                .currency(remittance.getCurrency())
                .build();
    }
    
    /**
     * 映射到應用程式狀態
     */
    private ApplicationStatus mapToApplicationStatus(IndividualRemittance.ProcessingStatus status) {
        if (status == null) {
            return ApplicationStatus.PENDING_APPROVAL;
        }
        
        switch (status) {
            case PENDING:
                return ApplicationStatus.PENDING_APPROVAL;
            case PROCESSING:
                return ApplicationStatus.ACCEPTED;
            case COMPLETED:
                return ApplicationStatus.COMPLETED;
            case FAILED:
                return ApplicationStatus.FAILED;
            case CANCELLED:
                return ApplicationStatus.REJECTED;
            default:
                return ApplicationStatus.PENDING_APPROVAL;
        }
    }
    
    /**
     * 驗證歷史查詢參數
     */
    private void validateHistoryQueryParams(String idNumber, int pageSize, int page) {
        if (idNumber == null || idNumber.trim().isEmpty()) {
            throw new BusinessException("INVALID_ID_NUMBER", "身份證號不能為空");
        }
        
        if (page < 1) {
            throw new BusinessException("INVALID_PAGE", "頁數必須大於0");
        }
        
        if (pageSize < 1 || pageSize > 100) {
            throw new BusinessException("INVALID_PAGE_SIZE", "每頁筆數必須在1-100之間");
        }
    }
    
    /**
     * 遮罩身份證號
     */
    private String maskIdNumber(String idNumber) {
        if (idNumber == null || idNumber.length() != 10) {
            return idNumber;
        }
        return idNumber.substring(0, 3) + "****" + idNumber.substring(7);
    }
    
    // ==================== 響應類別 ====================
    
    /**
     * 申請狀態響應
     */
    @lombok.Data
    @lombok.Builder
    public static class ApplicationStatusResponse {
        private String applicationId;
        private ApplicationStatus currentStatus;
        private List<StatusHistoryItem> statusHistory;
        private Integer progressPercentage;
        private LocalDateTime estimatedCompletionTime;
        private List<RequiredAction> requiredActions;
        private LastUpdateInfo lastUpdate;
    }
    
    /**
     * 狀態歷史項目
     */
    @lombok.Data
    @lombok.Builder
    public static class StatusHistoryItem {
        private ApplicationStatus status;
        private LocalDateTime timestamp;
        private String description;
        private String processedBy;
    }
    
    /**
     * 需要的操作
     */
    @lombok.Data
    @lombok.Builder
    public static class RequiredAction {
        private String action;
        private String description;
        private LocalDateTime dueDate;
    }
    
    /**
     * 最新更新資訊
     */
    @lombok.Data
    @lombok.Builder
    public static class LastUpdateInfo {
        private LocalDateTime timestamp;
        private String description;
        private String updatedBy;
    }
    
    /**
     * 申請歷史響應
     */
    @lombok.Data
    @lombok.Builder
    public static class ApplicationHistoryResponse {
        private List<ApplicationHistoryItem> applications;
        private Long totalCount;
        private Integer currentPage;
        private Integer totalPages;
    }
    
    /**
     * 申請歷史項目
     */
    @lombok.Data
    @lombok.Builder
    public static class ApplicationHistoryItem {
        private String applicationId;
        private LocalDateTime submitTime;
        private ApplicationStatus status;
        private java.math.BigDecimal remittanceAmount;
        private String currency;
    }
    
    /**
     * 處理統計
     */
    @lombok.Data
    @lombok.Builder
    public static class ProcessingStatistics {
        private Long totalApplications;
        private Long pendingCount;
        private Long processingCount;
        private Long completedCount;
        private Long failedCount;
        private Double completionRate;
        private LocalDateTime lastUpdated;
    }
}