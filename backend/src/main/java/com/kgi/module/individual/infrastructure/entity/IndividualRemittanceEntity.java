package com.kgi.module.individual.infrastructure.entity;

import com.kgi.core.domain.model.BaseEntity;
import lombok.*;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 自然人解款JPA實體
 */
@Entity
@Table(name = "individual_remittance", indexes = {
    @Index(name = "idx_remittance_id", columnList = "remittance_id", unique = true),
    @Index(name = "idx_their_ref_no", columnList = "their_ref_no", unique = true),
    @Index(name = "idx_taiwan_id", columnList = "beneficiary_taiwan_id"),
    @Index(name = "idx_processing_status", columnList = "processing_status"),
    @Index(name = "idx_business_status", columnList = "business_status_code"),
    @Index(name = "idx_created_date", columnList = "created_date"),
    @Index(name = "idx_estimated_completion", columnList = "estimated_completion_time")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class IndividualRemittanceEntity extends BaseEntity {
    
    /**
     * 解款編號(業務主鍵)
     */
    @Column(name = "remittance_id", nullable = false, unique = true, length = 50)
    private String remittanceId;
    
    /**
     * 跨境平台參考編號
     */
    @Column(name = "their_ref_no", nullable = false, unique = true, length = 50)
    private String theirRefNo;
    
    /**
     * 匯款參考編號
     */
    @Column(name = "remit_ref_no", length = 50)
    private String remitRefNo;
    
    /**
     * 匯款人姓名
     */
    @Column(name = "payer_name", nullable = false, length = 100)
    private String payerName;
    
    /**
     * 幣別
     */
    @Column(name = "currency", nullable = false, length = 3)
    private String currency;
    
    /**
     * 匯款金額
     */
    @Column(name = "amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;
    
    /**
     * 台幣金額(轉換後)
     */
    @Column(name = "twd_amount", precision = 15, scale = 2)
    private BigDecimal twdAmount;
    
    /**
     * 使用匯率
     */
    @Column(name = "exchange_rate", precision = 10, scale = 6)
    private BigDecimal exchangeRate;
    
    /**
     * 手續費
     */
    @Column(name = "fee", precision = 10, scale = 2)
    private BigDecimal fee;
    
    /**
     * 收款人台灣身分證號
     */
    @Column(name = "beneficiary_taiwan_id", nullable = false, length = 10)
    private String beneficiaryTaiwanId;
    
    /**
     * 收款人中文姓名
     */
    @Column(name = "beneficiary_chinese_name", nullable = false, length = 50)
    private String beneficiaryChineseName;
    
    /**
     * 收款人英文姓名
     */
    @Column(name = "beneficiary_english_name", nullable = false, length = 100)
    private String beneficiaryEnglishName;
    
    /**
     * 收款人聯絡電話
     */
    @Column(name = "beneficiary_phone", length = 20)
    private String beneficiaryPhone;
    
    /**
     * 收款人電子郵件
     */
    @Column(name = "beneficiary_email", length = 100)
    private String beneficiaryEmail;
    
    /**
     * 收款人銀行代碼
     */
    @Column(name = "beneficiary_bank_code", nullable = false, length = 10)
    private String beneficiaryBankCode;
    
    /**
     * 收款人分行代碼
     */
    @Column(name = "beneficiary_branch_code", length = 10)
    private String beneficiaryBranchCode;
    
    /**
     * 收款人帳戶號碼
     */
    @Column(name = "beneficiary_account_number", nullable = false, length = 50)
    private String beneficiaryAccountNumber;
    
    /**
     * 收款人銀行名稱
     */
    @Column(name = "beneficiary_bank_name", length = 100)
    private String beneficiaryBankName;
    
    /**
     * 收款人分行名稱
     */
    @Column(name = "beneficiary_branch_name", length = 100)
    private String beneficiaryBranchName;
    
    /**
     * 資金來源代碼
     */
    @Column(name = "source_of_fund", nullable = false, length = 10)
    private String sourceOfFund;
    
    /**
     * 匯款性質代碼
     */
    @Column(name = "remittance_purpose", length = 10)
    private String remittancePurpose;
    
    /**
     * 緊急處理標示
     */
    @Column(name = "is_urgent", nullable = false)
    @Builder.Default
    private Boolean isUrgent = false;
    
    /**
     * 客戶備註
     */
    @Column(name = "customer_note", length = 500)
    private String customerNote;
    
    /**
     * 系統備註
     */
    @Column(name = "system_note", length = 500)
    private String systemNote;
    
    /**
     * 匯款國別 (TO API 欄位)
     */
    @Column(name = "payer_country", length = 2)
    private String payerCountry;
    
    /**
     * 白名單標示 (TO API 欄位)
     */
    @Column(name = "while_flag", length = 1)
    private String whileFlag;
    
    /**
     * 附言 (TO API 欄位)
     */
    @Column(name = "memo", length = 40)
    private String memo;
    
    /**
     * 英文姓名無誤 (FROM API 欄位)
     */
    @Column(name = "eng_name_yn", length = 1)
    private String engNameYN;
    
    /**
     * 居留證日期起 (FROM API 欄位)
     */
    @Column(name = "residence_date_begin", length = 8)
    private String residenceDateBegin;
    
    /**
     * 居留證日期迄 (FROM API 欄位)
     */
    @Column(name = "residence_date_end", length = 8)
    private String residenceDateEnd;
    
    /**
     * 出生日期 (FROM API 欄位)
     */
    @Column(name = "bod", length = 8)
    private String bod;
    
    /**
     * 匯款性質DETAIL (FROM API 欄位)
     */
    @Column(name = "detail", length = 80)
    private String detail;
    
    /**
     * 簽章 (FROM API 欄位)
     */
    @Column(name = "signature", length = 64)
    private String signature;
    
    /**
     * 解款日期 (FROM API 欄位)
     */
    @Column(name = "release_date", length = 8)
    private String releaseDate;
    
    /**
     * 處理狀態
     */
    @Column(name = "processing_status", nullable = false, length = 20)
    private String processingStatus;
    
    /**
     * 驗證狀態
     */
    @Column(name = "verification_status", nullable = false, length = 20)
    private String verificationStatus;
    
    /**
     * 業務狀態代碼
     */
    @Column(name = "business_status_code", nullable = false, length = 10)
    private String businessStatusCode;
    
    /**
     * 業務狀態描述
     */
    @Column(name = "business_status_desc", length = 100)
    private String businessStatusDesc;
    
    /**
     * 預計完成時間
     */
    @Column(name = "estimated_completion_time")
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 完成時間
     */
    @Column(name = "completed_date")
    private LocalDateTime completedDate;
    
    /**
     * 是否需要補件
     */
    @Column(name = "requires_supplement", nullable = false)
    @Builder.Default
    private Boolean requiresSupplement = false;
    
    /**
     * 補件截止時間
     */
    @Column(name = "supplement_deadline")
    private LocalDateTime supplementDeadline;
    
    // 注意：版本號繼承自BaseEntity，不需要重複定義
    
    // 注意：軟刪除標示(isDeleted)繼承自BaseEntity，不需要重複定義
    
    /**
     * 檢查是否為最終狀態
     */
    public boolean isFinalStatus() {
        return "COMPLETED".equals(processingStatus) || 
               "FAILED".equals(processingStatus) || 
               "CANCELLED".equals(processingStatus);
    }
    
    /**
     * 檢查是否可以取消
     */
    public boolean canBeCancelled() {
        return !isFinalStatus() && 
               !"PROCESSING".equals(processingStatus);
    }
    
    /**
     * 檢查是否超過預計完成時間
     */
    public boolean isOverdue() {
        if (estimatedCompletionTime == null || isFinalStatus()) {
            return false;
        }
        return LocalDateTime.now().isAfter(estimatedCompletionTime);
    }
    
    /**
     * 取得遮罩後的身分證號
     */
    public String getMaskedTaiwanId() {
        if (beneficiaryTaiwanId == null || beneficiaryTaiwanId.length() != 10) {
            return beneficiaryTaiwanId;
        }
        return beneficiaryTaiwanId.substring(0, 3) + "****" + beneficiaryTaiwanId.substring(7);
    }
    
    /**
     * 取得遮罩後的帳號
     */
    public String getMaskedAccountNumber() {
        if (beneficiaryAccountNumber == null || beneficiaryAccountNumber.length() < 6) {
            return beneficiaryAccountNumber;
        }
        
        int length = beneficiaryAccountNumber.length();
        if (length <= 6) {
            return beneficiaryAccountNumber.substring(0, 3) + "***";
        } else {
            return beneficiaryAccountNumber.substring(0, 3) + 
                   "*".repeat(length - 6) + 
                   beneficiaryAccountNumber.substring(length - 3);
        }
    }
}