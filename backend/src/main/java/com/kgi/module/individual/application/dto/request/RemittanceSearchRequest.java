package com.kgi.module.individual.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 匯款搜尋請求
 * 用於搜尋個人匯款記錄
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemittanceSearchRequest {
    
    /**
     * 台灣身分證號
     */
    @NotBlank(message = "身分證號不能為空")
    @Pattern(regexp = "^[A-Z][12][0-9]{8}$", message = "身分證號格式不正確")
    private String taiwanId;
    
    /**
     * 收款人姓名
     */
    @NotBlank(message = "收款人姓名不能為空")
    private String beneficiaryName;
    
    /**
     * 收款人英文姓名 (可選)
     */
    private String beneficiaryEnglishName;
    
    /**
     * 匯款起始日期
     */
    private LocalDate startDate;
    
    /**
     * 匯款結束日期
     */
    private LocalDate endDate;
    
    /**
     * 匯款金額範圍 - 最小值
     */
    private Double minAmount;
    
    /**
     * 匯款金額範圍 - 最大值
     */
    private Double maxAmount;
    
    /**
     * 匯款幣別
     */
    private String currency;
    
    /**
     * 匯款銀行代碼
     */
    private String bankCode;
    
    /**
     * 付款人姓名 (可選)
     */
    private String payerName;
    
    /**
     * 匯款性質
     */
    private List<String> remittanceNatures;
    
    /**
     * 匯款狀態過濾
     */
    private List<String> statusFilter;
    
    /**
     * 是否包含已處理的匯款
     */
    @Builder.Default
    private Boolean includeProcessed = true;
    
    /**
     * 是否包含已取消的匯款
     */
    @Builder.Default
    private Boolean includeCancelled = false;
    
    /**
     * 排序方式
     */
    @Builder.Default
    private SortOption sortBy = SortOption.DATE_DESC;
    
    /**
     * 分頁大小
     */
    @Builder.Default
    private Integer pageSize = 10;
    
    /**
     * 頁碼 (從0開始)
     */
    @Builder.Default
    private Integer page = 0;
    
    /**
     * 排序選項枚舉
     */
    public enum SortOption {
        /**
         * 按日期降序 (最新的在前)
         */
        DATE_DESC,
        
        /**
         * 按日期升序 (最舊的在前)
         */
        DATE_ASC,
        
        /**
         * 按金額降序 (金額大的在前)
         */
        AMOUNT_DESC,
        
        /**
         * 按金額升序 (金額小的在前)
         */
        AMOUNT_ASC,
        
        /**
         * 按狀態排序
         */
        STATUS
    }
}