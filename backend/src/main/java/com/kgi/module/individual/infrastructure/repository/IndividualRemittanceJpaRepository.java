package com.kgi.module.individual.infrastructure.repository;

import com.kgi.module.individual.infrastructure.entity.IndividualRemittanceEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 自然人解款JPA Repository
 */
@Repository
public interface IndividualRemittanceJpaRepository extends JpaRepository<IndividualRemittanceEntity, Long> {
    
    /**
     * 根據解款編號查詢
     */
    Optional<IndividualRemittanceEntity> findByRemittanceId(String remittanceId);
    
    /**
     * 根據跨境平台參考編號查詢
     */
    Optional<IndividualRemittanceEntity> findByTheirRefNo(String theirRefNo);
    
    /**
     * 檢查跨境平台參考編號是否存在
     */
    boolean existsByTheirRefNo(String theirRefNo);
    
    /**
     * 根據身分證號和日期範圍查詢
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.beneficiaryTaiwanId = :taiwanId " +
           "AND r.createdAt >= :startDate " +
           "AND r.createdAt <= :endDate " +
           "AND r.isDeleted = false " +
           "ORDER BY r.createdAt DESC")
    Page<IndividualRemittanceEntity> findByTaiwanIdAndDateRange(
            @Param("taiwanId") String taiwanId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            Pageable pageable
    );
    
    /**
     * 根據處理狀態查詢
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.processingStatus = :status " +
           "AND r.isDeleted = false " +
           "ORDER BY r.createdAt ASC")
    List<IndividualRemittanceEntity> findByProcessingStatusOrderByCreatedDateAsc(
            @Param("status") String status,
            Pageable pageable
    );
    
    /**
     * 查詢超時的解款記錄
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.estimatedCompletionTime < :cutoffTime " +
           "AND r.processingStatus NOT IN ('COMPLETED', 'FAILED', 'CANCELLED') " +
           "AND r.isDeleted = false " +
           "ORDER BY r.estimatedCompletionTime ASC")
    List<IndividualRemittanceEntity> findTimeoutRemittances(
            @Param("cutoffTime") LocalDateTime cutoffTime,
            Pageable pageable
    );
    
    /**
     * 根據業務狀態代碼和時間範圍查詢
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.businessStatusCode = :statusCode " +
           "AND r.updatedAt >= :startTime " +
           "AND r.updatedAt <= :endTime " +
           "AND r.isDeleted = false " +
           "ORDER BY r.updatedAt DESC")
    List<IndividualRemittanceEntity> findByBusinessStatusCodeAndTimeRange(
            @Param("statusCode") String statusCode,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
    
    /**
     * 查詢需要補件的解款記錄
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.requiresSupplement = true " +
           "AND r.supplementDeadline < :deadlineBefore " +
           "AND r.processingStatus NOT IN ('COMPLETED', 'FAILED', 'CANCELLED') " +
           "AND r.isDeleted = false " +
           "ORDER BY r.supplementDeadline ASC")
    List<IndividualRemittanceEntity> findRemittancesRequiringSupplement(
            @Param("deadlineBefore") LocalDateTime deadlineBefore
    );
    
    /**
     * 根據匯款人姓名模糊查詢
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.payerName LIKE CONCAT('%', :payerName, '%') " +
           "AND r.isDeleted = false " +
           "ORDER BY r.createdAt DESC")
    Page<IndividualRemittanceEntity> findByPayerNameContaining(
            @Param("payerName") String payerName,
            Pageable pageable
    );
    
    /**
     * 統計解款記錄數量(按狀態)
     */
    @Query("SELECT r.processingStatus, COUNT(r) " +
           "FROM IndividualRemittanceEntity r " +
           "WHERE FUNCTION('DATE', r.createdAt) >= :startDate " +
           "AND FUNCTION('DATE', r.createdAt) <= :endDate " +
           "AND r.isDeleted = false " +
           "GROUP BY r.processingStatus")
    List<Object[]> countByStatusAndDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
    
    /**
     * 統計解款金額(按幣別)
     */
    @Query("SELECT r.currency, SUM(r.amount), COUNT(r) " +
           "FROM IndividualRemittanceEntity r " +
           "WHERE FUNCTION('DATE', r.createdAt) >= :startDate " +
           "AND FUNCTION('DATE', r.createdAt) <= :endDate " +
           "AND r.isDeleted = false " +
           "GROUP BY r.currency")
    List<Object[]> sumAmountByCurrencyAndDateRange(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );
    
    /**
     * 批次更新解款狀態
     */
    @Modifying
    @Query("UPDATE IndividualRemittanceEntity r " +
           "SET r.processingStatus = :newStatus, " +
           "    r.businessStatusDesc = :statusDescription, " +
           "    r.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE r.remittanceId IN :remittanceIds " +
           "AND r.isDeleted = false")
    int batchUpdateStatus(
            @Param("remittanceIds") List<String> remittanceIds,
            @Param("newStatus") String newStatus,
            @Param("statusDescription") String statusDescription
    );
    
    /**
     * 軟刪除解款記錄
     */
    @Modifying
    @Query("UPDATE IndividualRemittanceEntity r " +
           "SET r.isDeleted = true, r.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE r.remittanceId = :remittanceId")
    int softDeleteByRemittanceId(@Param("remittanceId") String remittanceId);
    
    /**
     * 查詢今日解款統計
     */
    @Query("SELECT COUNT(r), " +
           "SUM(CASE WHEN r.processingStatus = 'COMPLETED' THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN r.processingStatus = 'FAILED' THEN 1 ELSE 0 END), " +
           "SUM(r.twdAmount) " +
           "FROM IndividualRemittanceEntity r " +
           "WHERE FUNCTION('DATE', r.createdAt) = CURRENT_DATE " +
           "AND r.isDeleted = false")
    Object[] getTodayStatistics();
    
    /**
     * 查詢特定用戶的解款次數
     */
    @Query("SELECT COUNT(r) " +
           "FROM IndividualRemittanceEntity r " +
           "WHERE r.beneficiaryTaiwanId = :taiwanId " +
           "AND r.createdAt >= :fromDate " +
           "AND r.isDeleted = false")
    long countByTaiwanIdSince(
            @Param("taiwanId") String taiwanId,
            @Param("fromDate") LocalDateTime fromDate
    );
    
    /**
     * 查詢高風險金額的解款記錄
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.twdAmount >= :thresholdAmount " +
           "AND r.createdAt >= :fromDate " +
           "AND r.isDeleted = false " +
           "ORDER BY r.twdAmount DESC")
    List<IndividualRemittanceEntity> findHighValueRemittances(
            @Param("thresholdAmount") BigDecimal thresholdAmount,
            @Param("fromDate") LocalDateTime fromDate,
            Pageable pageable
    );
    
    /**
     * 查詢待處理的OTP驗證
     */
    @Query("SELECT r FROM IndividualRemittanceEntity r " +
           "WHERE r.verificationStatus = 'PENDING' " +
           "AND r.createdAt >= :cutoffTime " +
           "AND r.isDeleted = false " +
           "ORDER BY r.createdAt ASC")
    List<IndividualRemittanceEntity> findPendingOtpVerification(
            @Param("cutoffTime") LocalDateTime cutoffTime
    );
}