package com.kgi.module.individual.infrastructure.mapper;

import com.kgi.core.domain.model.TaiwanId;
import com.kgi.module.individual.domain.model.BankAccount;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.model.PersonalInfo;
import com.kgi.module.individual.infrastructure.entity.IndividualRemittanceEntity;
import org.springframework.stereotype.Component;

/**
 * 自然人解款實體映射器
 * 負責領域模型與JPA實體之間的轉換
 */
@Component
public class IndividualRemittanceMapper {
    
    /**
     * 領域模型轉JPA實體
     */
    public IndividualRemittanceEntity toEntity(IndividualRemittance domain) {
        if (domain == null) {
            return null;
        }
        
        IndividualRemittanceEntity.IndividualRemittanceEntityBuilder builder = IndividualRemittanceEntity.builder()
                .remittanceId(domain.getRemittanceId())
                .theirRefNo(domain.getTheirRefNo())
                .remitRefNo(domain.getRemitRefNo())
                .payerName(domain.getPayerName())
                .currency(domain.getCurrency())
                .amount(domain.getAmount())
                .twdAmount(domain.getTwdAmount())
                .exchangeRate(domain.getExchangeRate())
                .fee(domain.getFee())
                .sourceOfFund(domain.getSourceOfFund())
                .remittancePurpose(domain.getRemittancePurpose())
                .isUrgent(domain.getIsUrgent())
                .customerNote(domain.getCustomerNote())
                .systemNote(domain.getSystemNote())
                .processingStatus(domain.getProcessingStatus() != null ? domain.getProcessingStatus().name() : null)
                .verificationStatus(domain.getVerificationStatus() != null ? domain.getVerificationStatus().name() : null)
                .businessStatusCode(domain.getBusinessStatusCode())
                .businessStatusDesc(domain.getBusinessStatusDesc())
                .estimatedCompletionTime(domain.getEstimatedCompletionTime())
                .completedDate(domain.getCompletedDate())
                .requiresSupplement(domain.getRequiresSupplement())
                .supplementDeadline(domain.getSupplementDeadline());
        
        // 映射收款人資訊
        if (domain.getBeneficiary() != null) {
            PersonalInfo beneficiary = domain.getBeneficiary();
            
            // 身分證號
            if (beneficiary.getTaiwanId() != null) {
                builder.beneficiaryTaiwanId(beneficiary.getTaiwanId().getValue());
            }
            
            // 基本資訊
            builder.beneficiaryChineseName(beneficiary.getChineseName())
                   .beneficiaryEnglishName(beneficiary.getEnglishName())
                   .beneficiaryPhone(beneficiary.getContactPhone())
                   .beneficiaryEmail(beneficiary.getEmail());
            
            // 銀行帳戶資訊
            if (beneficiary.getBankAccount() != null) {
                BankAccount bankAccount = beneficiary.getBankAccount();
                builder.beneficiaryBankCode(bankAccount.getBankCode())
                       .beneficiaryBranchCode(bankAccount.getBranchCode())
                       .beneficiaryAccountNumber(bankAccount.getAccountNumber())
                       .beneficiaryBankName(bankAccount.getBankName())
                       .beneficiaryBranchName(bankAccount.getBranchName());
            }
        }
        
        IndividualRemittanceEntity entity = builder.build();
        
        // 手動設置BaseEntity欄位
        entity.setCreatedAt(domain.getCreatedAt());
        entity.setUpdatedAt(domain.getUpdatedAt());
        entity.setCreatedBy(domain.getCreatedBy());
        entity.setUpdatedBy(domain.getUpdatedBy());
        
        return entity;
    }
    
    /**
     * JPA實體轉領域模型
     */
    public IndividualRemittance toDomain(IndividualRemittanceEntity entity) {
        if (entity == null) {
            return null;
        }
        
        IndividualRemittance.IndividualRemittanceBuilder builder = IndividualRemittance.builder()
                .remittanceId(entity.getRemittanceId())
                .theirRefNo(entity.getTheirRefNo())
                .remitRefNo(entity.getRemitRefNo())
                .payerName(entity.getPayerName())
                .currency(entity.getCurrency())
                .amount(entity.getAmount())
                .twdAmount(entity.getTwdAmount())
                .exchangeRate(entity.getExchangeRate())
                .fee(entity.getFee())
                .sourceOfFund(entity.getSourceOfFund())
                .remittancePurpose(entity.getRemittancePurpose())
                .isUrgent(entity.getIsUrgent())
                .customerNote(entity.getCustomerNote())
                .systemNote(entity.getSystemNote())
                .businessStatusCode(entity.getBusinessStatusCode())
                .businessStatusDesc(entity.getBusinessStatusDesc())
                .estimatedCompletionTime(entity.getEstimatedCompletionTime())
                .completedDate(entity.getCompletedDate())
                .requiresSupplement(entity.getRequiresSupplement())
                .supplementDeadline(entity.getSupplementDeadline());
        
        // 映射處理狀態
        if (entity.getProcessingStatus() != null) {
            try {
                builder.processingStatus(IndividualRemittance.ProcessingStatus.valueOf(entity.getProcessingStatus()));
            } catch (IllegalArgumentException e) {
                // 處理未知狀態，設為預設值
                builder.processingStatus(IndividualRemittance.ProcessingStatus.PENDING);
            }
        }
        
        // 映射驗證狀態
        if (entity.getVerificationStatus() != null) {
            try {
                builder.verificationStatus(IndividualRemittance.VerificationStatus.valueOf(entity.getVerificationStatus()));
            } catch (IllegalArgumentException e) {
                // 處理未知狀態，設為預設值
                builder.verificationStatus(IndividualRemittance.VerificationStatus.PENDING);
            }
        }
        
        // 映射收款人資訊
        PersonalInfo beneficiary = buildBeneficiaryInfo(entity);
        if (beneficiary != null) {
            builder.beneficiary(beneficiary);
        }
        
        // 設置基礎實體欄位
        IndividualRemittance domain = builder.build();
        domain.setId(entity.getId());
        domain.setCreatedAt(entity.getCreatedAt());
        domain.setUpdatedAt(entity.getUpdatedAt());
        domain.setCreatedBy(entity.getCreatedBy());
        domain.setUpdatedBy(entity.getUpdatedBy());
        
        return domain;
    }
    
    /**
     * 建立收款人資訊
     */
    private PersonalInfo buildBeneficiaryInfo(IndividualRemittanceEntity entity) {
        if (entity.getBeneficiaryTaiwanId() == null) {
            return null;
        }
        
        // 建立台灣身分證號
        TaiwanId taiwanId = null;
        try {
            taiwanId = TaiwanId.of(entity.getBeneficiaryTaiwanId());
        } catch (Exception e) {
            // 如果身分證號無效，記錄錯誤但繼續處理
            // 這種情況在資料遷移或歷史資料中可能出現
        }
        
        // 建立銀行帳戶
        BankAccount bankAccount = null;
        if (entity.getBeneficiaryBankCode() != null && entity.getBeneficiaryAccountNumber() != null) {
            bankAccount = new BankAccount(
                    entity.getBeneficiaryBankCode(),
                    entity.getBeneficiaryBranchCode(),
                    entity.getBeneficiaryAccountNumber(),
                    entity.getBeneficiaryBankName(),
                    entity.getBeneficiaryBranchName()
            );
        }
        
        // 建立個人資訊
        return new PersonalInfo(
                taiwanId,
                entity.getBeneficiaryChineseName(),
                entity.getBeneficiaryEnglishName(),
                null, // 出生日期在實體中未存儲
                entity.getBeneficiaryPhone(),
                entity.getBeneficiaryEmail(),
                bankAccount
        );
    }
    
    /**
     * 更新實體(用於部分更新場景)
     */
    public void updateEntity(IndividualRemittanceEntity entity, IndividualRemittance domain) {
        if (entity == null || domain == null) {
            return;
        }
        
        // 更新可變欄位
        entity.setSystemNote(domain.getSystemNote());
        entity.setProcessingStatus(domain.getProcessingStatus() != null ? domain.getProcessingStatus().name() : null);
        entity.setVerificationStatus(domain.getVerificationStatus() != null ? domain.getVerificationStatus().name() : null);
        entity.setBusinessStatusCode(domain.getBusinessStatusCode());
        entity.setBusinessStatusDesc(domain.getBusinessStatusDesc());
        entity.setCompletedDate(domain.getCompletedDate());
        entity.setRequiresSupplement(domain.getRequiresSupplement());
        entity.setSupplementDeadline(domain.getSupplementDeadline());
        entity.setTwdAmount(domain.getTwdAmount());
        entity.setExchangeRate(domain.getExchangeRate());
        entity.setFee(domain.getFee());
        
        // 更新基礎實體欄位
        entity.setUpdatedAt(domain.getUpdatedAt());
        entity.setUpdatedBy(domain.getUpdatedBy());
    }
}