package com.kgi.module.individual.infrastructure.repository;

import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 自然人解款Repository內存實作
 * 使用 ConcurrentHashMap 作為儲存機制
 */
@Slf4j
@Repository
public class IndividualRemittanceRepositoryImpl implements IndividualRemittanceRepository {
    
    private final Map<String, IndividualRemittance> remittanceStorage = new ConcurrentHashMap<>();
    
    @Override
    public IndividualRemittance save(IndividualRemittance remittance) {
        log.debug("儲存解款記錄: {}", remittance.getRemittanceId());
        
        if (remittance.getRemittanceId() == null) {
            // 生成新的ID
            String newId = UUID.randomUUID().toString();
            remittance.setRemittanceId(newId);
        }
        
        remittanceStorage.put(remittance.getRemittanceId(), remittance);
        log.debug("解款記錄儲存完成: id={}", remittance.getRemittanceId());
        return remittance;
    }
    
    @Override
    public List<IndividualRemittance> findAll() {
        log.debug("查詢所有解款記錄");
        
        List<IndividualRemittance> remittances = new ArrayList<>(remittanceStorage.values());
        log.debug("查詢結果: {} 筆記錄", remittances.size());
        
        return remittances;
    }
    
    @Override
    public Optional<IndividualRemittance> findByRemittanceId(String remittanceId) {
        log.debug("根據解款ID查詢: {}", remittanceId);
        
        return Optional.ofNullable(remittanceStorage.get(remittanceId));
    }
    
    @Override
    public Optional<IndividualRemittance> findByTheirRefNo(String theirRefNo) {
        log.debug("根據跨境平台參考編號查詢: {}", theirRefNo);
        
        return remittanceStorage.values().stream()
                .filter(r -> theirRefNo.equals(r.getTheirRefNo()))
                .findFirst();
    }
    
    @Override
    public boolean existsByTheirRefNo(String theirRefNo) {
        log.debug("檢查跨境平台參考編號是否存在: {}", theirRefNo);
        
        return remittanceStorage.values().stream()
                .anyMatch(r -> theirRefNo.equals(r.getTheirRefNo()));
    }
    
    
    @Override
    public Page<IndividualRemittance> findByTaiwanIdAndDateRange(String taiwanId, 
                                                               LocalDateTime startDate, 
                                                               LocalDateTime endDate,
                                                               Pageable pageable) {
        log.debug("根據身分證號和時間範圍分頁查詢: taiwanId={}, {} to {}, page={}, size={}", 
                taiwanId, startDate, endDate, pageable.getPageNumber(), pageable.getPageSize());
        
        List<IndividualRemittance> filtered = remittanceStorage.values().stream()
                .filter(r -> r.getBeneficiary() != null && 
                           taiwanId.equals(r.getBeneficiary().getTaiwanId()))
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isAfter(startDate) && 
                           r.getCreatedAt().isBefore(endDate))
                .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
                .collect(Collectors.toList());
        
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filtered.size());
        
        List<IndividualRemittance> pageContent = start < filtered.size() ? 
                filtered.subList(start, end) : new ArrayList<>();
        
        return new PageImpl<>(pageContent, pageable, filtered.size());
    }
    
    @Override
    public long countByTaiwanIdAndDateRange(String taiwanId, LocalDate startDate, LocalDate endDate) {
        log.debug("統計身分證號在時間範圍內的解款筆數: taiwanId={}, {} to {}", taiwanId, startDate, endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getBeneficiary() != null && 
                           taiwanId.equals(r.getBeneficiary().getTaiwanId()))
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isAfter(startDateTime) && 
                           r.getCreatedAt().isBefore(endDateTime))
                .count();
    }
    
    @Override
    public List<IndividualRemittance> findByProcessingStatus(IndividualRemittance.ProcessingStatus status, int limit) {
        log.debug("根據處理狀態查詢解款記錄: status={}, limit={}", status, limit);
        
        return remittanceStorage.values().stream()
                .filter(r -> status == r.getProcessingStatus())
                .sorted((a, b) -> {
                    if (a.getCreatedAt() == null || b.getCreatedAt() == null) {
                        return 0;
                    }
                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                })
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndividualRemittance> findTimeoutRemittances(LocalDateTime cutoffTime, int limit) {
        log.debug("查詢超時的解款記錄: cutoffTime={}, limit={}", cutoffTime, limit);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getProcessingStatus() == IndividualRemittance.ProcessingStatus.PENDING ||
                           r.getProcessingStatus() == IndividualRemittance.ProcessingStatus.PROCESSING)
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isBefore(cutoffTime))
                .sorted((a, b) -> a.getCreatedAt().compareTo(b.getCreatedAt()))
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndividualRemittance> findByBusinessStatusCodeAndTimeRange(String statusCode,
                                                                         LocalDateTime startTime,
                                                                         LocalDateTime endTime) {
        log.debug("根據業務狀態代碼和時間範圍查詢: statusCode={}, {} to {}", statusCode, startTime, endTime);
        
        return remittanceStorage.values().stream()
                .filter(r -> statusCode.equals(r.getBusinessStatusCode()))
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isAfter(startTime) && 
                           r.getCreatedAt().isBefore(endTime))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndividualRemittance> findRemittancesRequiringSupplement(LocalDateTime deadlineBefore) {
        log.debug("查詢需要補件的解款記錄: deadlineBefore={}", deadlineBefore);
        
        return remittanceStorage.values().stream()
                .filter(r -> Boolean.TRUE.equals(r.getRequiresSupplement()))
                .filter(r -> r.getSupplementDeadline() != null)
                .filter(r -> r.getSupplementDeadline().isBefore(deadlineBefore))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndividualRemittance> findByPayerNameContaining(String payerName, int pageNumber, int pageSize) {
        log.debug("根據匯款人姓名模糊查詢: payerName={}, page={}, size={}", payerName, pageNumber, pageSize);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getPayerName() != null && 
                           r.getPayerName().toLowerCase().contains(payerName.toLowerCase()))
                .sorted((a, b) -> {
                    if (a.getCreatedAt() == null || b.getCreatedAt() == null) {
                        return 0;
                    }
                    return b.getCreatedAt().compareTo(a.getCreatedAt());
                })
                .skip((long) pageNumber * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Object[]> countByStatusAndDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("統計時間範圍內各狀態的筆數: {} to {}", startDate, endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        Map<IndividualRemittance.ProcessingStatus, Long> statusCounts = remittanceStorage.values().stream()
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isAfter(startDateTime) && 
                           r.getCreatedAt().isBefore(endDateTime))
                .collect(Collectors.groupingBy(
                        IndividualRemittance::getProcessingStatus,
                        Collectors.counting()
                ));
        
        return statusCounts.entrySet().stream()
                .map(entry -> new Object[]{entry.getKey().name(), entry.getValue()})
                .collect(Collectors.toList());
    }
    
    @Override
    public List<IndividualRemittanceRepository.CurrencyAmountSum> sumAmountByCurrencyAndDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("統計時間範圍內各幣別的金額: {} to {}", startDate, endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        Map<String, List<IndividualRemittance>> byCurrency = remittanceStorage.values().stream()
                .filter(r -> r.getCreatedAt() != null)
                .filter(r -> r.getCreatedAt().isAfter(startDateTime) && 
                           r.getCreatedAt().isBefore(endDateTime))
                .filter(r -> r.getCurrency() != null)
                .collect(Collectors.groupingBy(IndividualRemittance::getCurrency));
        
        return byCurrency.entrySet().stream()
                .map(entry -> {
                    String currency = entry.getKey();
                    List<IndividualRemittance> remittances = entry.getValue();
                    BigDecimal totalAmount = remittances.stream()
                            .map(IndividualRemittance::getAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    long count = remittances.size();
                    return new IndividualRemittanceRepository.CurrencyAmountSum(currency, totalAmount, count);
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public int batchUpdateStatus(List<String> remittanceIds, 
                               IndividualRemittance.ProcessingStatus newStatus,
                               String statusDescription) {
        log.debug("批次更新解款狀態: count={}, newStatus={}, desc={}", 
                remittanceIds.size(), newStatus, statusDescription);
        
        int updateCount = 0;
        for (String id : remittanceIds) {
            IndividualRemittance remittance = remittanceStorage.get(id);
            if (remittance != null) {
                remittance.setProcessingStatus(newStatus);
                remittance.setBusinessStatusDesc(statusDescription);
                remittance.setUpdatedAt(LocalDateTime.now());
                updateCount++;
            }
        }
        
        return updateCount;
    }
    
    @Override
    public boolean softDelete(String remittanceId) {
        log.debug("軟刪除解款記錄: {}", remittanceId);
        
        // 內存實現中直接刪除
        return remittanceStorage.remove(remittanceId) != null;
    }
}