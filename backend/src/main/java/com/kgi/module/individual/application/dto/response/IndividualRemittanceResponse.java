package com.kgi.module.individual.application.dto.response;

import com.kgi.module.individual.domain.model.BankAccount;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.model.PersonalInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 自然人解款響應DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualRemittanceResponse {
    
    /**
     * 解款編號
     */
    private String remittanceId;
    
    /**
     * 跨境平台參考編號
     */
    private String theirRefNo;
    
    /**
     * 匯款參考編號
     */
    private String remitRefNo;
    
    /**
     * 匯款人姓名
     */
    private String payerName;
    
    /**
     * 幣別
     */
    private String currency;
    
    /**
     * 匯款金額
     */
    private BigDecimal amount;
    
    /**
     * 台幣金額(轉換後)
     */
    private BigDecimal twdAmount;
    
    /**
     * 使用匯率
     */
    private BigDecimal exchangeRate;
    
    /**
     * 手續費
     */
    private BigDecimal fee;
    
    /**
     * 收款人資訊
     */
    private PayeeInfo payeeInfo;
    
    /**
     * 處理狀態
     */
    private String processingStatus;
    
    /**
     * 處理狀態描述
     */
    private String processingStatusDesc;
    
    /**
     * 驗證狀態
     */
    private String verificationStatus;
    
    /**
     * 驗證狀態描述
     */
    private String verificationStatusDesc;
    
    /**
     * 業務狀態代碼
     */
    private String businessStatusCode;
    
    /**
     * 業務狀態描述
     */
    private String businessStatusDesc;
    
    /**
     * 申請時間
     */
    private LocalDateTime applicationTime;
    
    /**
     * 預計完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 實際完成時間
     */
    private LocalDateTime actualCompletionTime;
    
    /**
     * 是否需要補件
     */
    private Boolean requiresSupplement;
    
    /**
     * 補件截止時間
     */
    private LocalDateTime supplementDeadline;
    
    /**
     * 客戶備註
     */
    private String customerNote;
    
    /**
     * 系統備註
     */
    private String systemNote;
    
    /**
     * 資金來源
     */
    private String sourceOfFund;
    
    /**
     * 匯款目的
     */
    private String remittancePurpose;
    
    /**
     * 是否緊急
     */
    private Boolean isUrgent;
    
    /**
     * 完成時間
     */
    private LocalDateTime completedDate;
    
    /**
     * 創建時間
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    private LocalDateTime updatedAt;
    
    /**
     * 收款人資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayeeInfo {
        
        /**
         * 中文姓名
         */
        private String chineseName;
        
        /**
         * 英文姓名
         */
        private String englishName;
        
        /**
         * 遮罩身分證號
         */
        private String maskedId;
        
        /**
         * 遮罩銀行帳號
         */
        private String maskedAccount;
        
        /**
         * 銀行代碼
         */
        private String bankCode;
        
        /**
         * 銀行名稱
         */
        private String bankName;
        
        /**
         * 分行代碼
         */
        private String branchCode;
        
        /**
         * 分行名稱
         */
        private String branchName;
        
        /**
         * 聯絡電話
         */
        private String contactPhone;
        
        /**
         * 電子郵件
         */
        private String email;
    }
    
    /**
     * 從領域實體轉換為響應DTO
     */
    public static IndividualRemittanceResponse fromDomain(IndividualRemittance remittance) {
        return IndividualRemittanceResponse.builder()
                .remittanceId(remittance.getRemittanceId())
                .theirRefNo(remittance.getTheirRefNo())
                .remitRefNo(remittance.getRemitRefNo())
                .payerName(remittance.getPayerName())
                .currency(remittance.getCurrency())
                .amount(remittance.getAmount())
                .twdAmount(remittance.getTwdAmount())
                .exchangeRate(remittance.getExchangeRate())
                .fee(remittance.getFee())
                .payeeInfo(buildPayeeInfo(remittance))
                .processingStatus(remittance.getProcessingStatus().name())
                .processingStatusDesc(remittance.getProcessingStatus().getDescription())
                .verificationStatus(remittance.getVerificationStatus().name())
                .verificationStatusDesc(remittance.getVerificationStatus().getDescription())
                .businessStatusCode(remittance.getBusinessStatusCode())
                .businessStatusDesc(remittance.getBusinessStatusDesc())
                .applicationTime(remittance.getCreatedAt())
                .estimatedCompletionTime(remittance.getEstimatedCompletionTime())
                .actualCompletionTime(remittance.getCompletedDate())
                .requiresSupplement(remittance.getRequiresSupplement())
                .supplementDeadline(remittance.getSupplementDeadline())
                .customerNote(remittance.getCustomerNote())
                .systemNote(remittance.getSystemNote())
                .sourceOfFund(remittance.getSourceOfFund())
                .remittancePurpose(remittance.getRemittancePurpose())
                .isUrgent(remittance.getIsUrgent())
                .completedDate(remittance.getCompletedDate())
                .createdAt(remittance.getCreatedAt())
                .updatedAt(remittance.getUpdatedAt())
                .build();
    }
    
    /**
     * 建立收款人資訊
     */
    private static PayeeInfo buildPayeeInfo(IndividualRemittance remittance) {
        if (remittance.getBeneficiary() == null) {
            return null;
        }
        
        PersonalInfo beneficiary = remittance.getBeneficiary();
        BankAccount bankAccount = beneficiary.getBankAccount();
        
        return PayeeInfo.builder()
                .chineseName(beneficiary.getChineseName())
                .englishName(beneficiary.getEnglishName())
                .maskedId(beneficiary.getMaskedId())
                .maskedAccount(bankAccount != null ? bankAccount.getMaskedAccountNumber() : null)
                .bankCode(bankAccount != null ? bankAccount.getBankCode() : null)
                .bankName(bankAccount != null ? bankAccount.getBankName() : null)
                .branchCode(bankAccount != null ? bankAccount.getBranchCode() : null)
                .branchName(bankAccount != null ? bankAccount.getBranchName() : null)
                .contactPhone(beneficiary.getContactPhone())
                .email(beneficiary.getEmail())
                .build();
    }
    
    /**
     * 檢查是否為最終狀態
     */
    public boolean isFinalStatus() {
        if (processingStatus == null) {
            return false;
        }
        
        return processingStatus.equals("COMPLETED") || 
               processingStatus.equals("FAILED") || 
               processingStatus.equals("CANCELLED");
    }
    
    /**
     * 檢查是否需要客戶操作
     */
    public boolean requiresCustomerAction() {
        return requiresSupplement != null && requiresSupplement ||
               "OTP_PENDING".equals(verificationStatus) ||
               "WAITING_CONFIRMATION".equals(processingStatus);
    }
    
    /**
     * 取得處理進度百分比
     */
    public Integer getProgressPercentage() {
        if (processingStatus == null) {
            return 0;
        }
        
        switch (processingStatus) {
            case "PENDING":
                return 10;
            case "IDENTITY_VERIFYING":
                return 20;
            case "OTP_VERIFYING":
                return 30;
            case "AMOUNT_CALCULATING":
                return 40;
            case "BANK_VERIFYING":
                return 60;
            case "PROCESSING":
                return 80;
            case "COMPLETED":
                return 100;
            case "FAILED":
            case "CANCELLED":
                return 0;
            default:
                return 50;
        }
    }
}