package com.kgi.module.individual.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 身份驗證請求
 * 用於Individual模組的身份驗證
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IdentityVerificationRequest {
    
    /**
     * 台灣身分證號
     */
    @NotBlank(message = "身分證號不能為空")
    @Pattern(regexp = "^[A-Z][12][0-9]{8}$", message = "身分證號格式不正確")
    private String taiwanId;
    
    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能為空")
    private String name;
    
    /**
     * 生日 (可選，用於增強驗證)
     */
    private LocalDate birthDate;
    
    /**
     * 手機號碼
     */
    @Pattern(regexp = "^09[0-9]{8}$", message = "手機號碼格式不正確")
    private String phoneNumber;
    
    /**
     * 驗證類型
     */
    @Builder.Default
    private VerificationType verificationType = VerificationType.BASIC;
    
    /**
     * 驗證來源IP
     */
    private String ipAddress;
    
    /**
     * 使用者代理
     */
    private String userAgent;
    
    /**
     * 驗證類型枚舉
     */
    public enum VerificationType {
        /**
         * 基本驗證 - 只驗證身分證號和姓名
         */
        BASIC,
        
        /**
         * 增強驗證 - 包含生日驗證
         */
        ENHANCED,
        
        /**
         * 完整驗證 - 包含所有可用驗證項目
         */
        FULL
    }
}