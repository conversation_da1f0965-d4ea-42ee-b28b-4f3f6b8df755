package com.kgi.module.individual.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 個人解款業務服務
 * 處理個人解款的核心業務邏輯和狀態管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndividualRemittanceService {
    
    private final IndividualRemittanceRepository remittanceRepository;
    private final RemittanceValidationService validationService;
    private final RemittanceCalculationService calculationService;
    
    /**
     * 啟動身分驗證流程
     */
    @Transactional
    public void startIdentityVerification(String remittanceId) {
        log.info("啟動身分驗證流程: remittanceId={}", remittanceId);
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (remittance.getVerificationStatus() != IndividualRemittance.VerificationStatus.PENDING) {
            throw new BusinessException("INVALID_STATUS", "只有待驗證狀態的申請可以啟動身分驗證");
        }
        
        // 開始身分驗證流程
        remittance.updateBusinessStatus("02", "身分驗證進行中");
        remittance.setProcessingStatus(IndividualRemittance.ProcessingStatus.PROCESSING);
        
        remittanceRepository.save(remittance);
        
        log.info("身分驗證流程已啟動: remittanceId={}", remittanceId);
    }
    
    /**
     * 完成身分驗證
     */
    @Transactional
    public void completeIdentityVerification(String remittanceId, boolean verificationPassed, String notes) {
        log.info("完成身分驗證: remittanceId={}, passed={}", remittanceId, verificationPassed);
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (verificationPassed) {
            remittance.completeVerification();
            
            // 根據金額和風險等級決定下一步流程
            if (requiresAdditionalReview(remittance)) {
                remittance.updateBusinessStatus("13", "等待主管審核");
            } else {
                remittance.updateBusinessStatus("15", "準備執行解款");
                remittance.setProcessingStatus(IndividualRemittance.ProcessingStatus.PROCESSING);
            }
        } else {
            remittance.setVerificationStatus(IndividualRemittance.VerificationStatus.FAILED);
            remittance.updateBusinessStatus("14", "身分驗證失敗");
            remittance.setSystemNote(notes);
        }
        
        remittanceRepository.save(remittance);
        
        log.info("身分驗證完成: remittanceId={}, status={}", remittanceId, remittance.getBusinessStatusDesc());
    }
    
    /**
     * 執行解款處理
     */
    @Transactional
    public void executeRemittance(String remittanceId, String processor) {
        log.info("執行解款處理: remittanceId={}, processor={}", remittanceId, maskId(processor));
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (!remittance.isVerified()) {
            throw new BusinessException("VERIFICATION_REQUIRED", "必須先完成身分驗證");
        }
        
        if (remittance.getProcessingStatus() == IndividualRemittance.ProcessingStatus.COMPLETED) {
            throw new BusinessException("ALREADY_COMPLETED", "解款已完成，無法重複處理");
        }
        
        try {
            // 開始解款處理
            remittance.startProcessing();
            remittance.updateBusinessStatus("20", "解款處理中");
            
            // 執行實際的解款操作
            performRemittanceExecution(remittance);
            
            // 完成解款
            remittance.complete();
            
            remittanceRepository.save(remittance);
            
            log.info("解款處理完成: remittanceId={}", remittanceId);
            
        } catch (Exception e) {
            log.error("解款處理失敗: remittanceId={}, error={}", remittanceId, e.getMessage());
            
            remittance.markAsFailed("解款處理失敗: " + e.getMessage());
            remittanceRepository.save(remittance);
            
            throw new BusinessException("REMITTANCE_EXECUTION_FAILED", "解款執行失敗: " + e.getMessage());
        }
    }
    
    /**
     * 要求補件
     */
    @Transactional
    public void requestSupplement(String remittanceId, String reason, int deadlineHours) {
        log.info("要求補件: remittanceId={}, reason={}, deadlineHours={}", remittanceId, reason, deadlineHours);
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (remittance.isCompleted()) {
            throw new BusinessException("ALREADY_COMPLETED", "已完成的解款無法要求補件");
        }
        
        LocalDateTime deadline = LocalDateTime.now().plusHours(deadlineHours);
        remittance.requireSupplement(deadline, reason);
        
        remittanceRepository.save(remittance);
        
        log.info("補件要求已設定: remittanceId={}, deadline={}", remittanceId, deadline);
    }
    
    /**
     * 完成補件
     */
    @Transactional
    public void completeSupplement(String remittanceId, String notes) {
        log.info("完成補件: remittanceId={}", remittanceId);
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (!Boolean.TRUE.equals(remittance.getRequiresSupplement())) {
            throw new BusinessException("NO_SUPPLEMENT_REQUIRED", "此解款申請未要求補件");
        }
        
        remittance.completeSupplement();
        remittance.setSystemNote(notes);
        
        // 補件完成後，重新啟動處理流程
        remittance.setProcessingStatus(IndividualRemittance.ProcessingStatus.PROCESSING);
        remittance.updateBusinessStatus("27", "補件完成，繼續處理");
        
        remittanceRepository.save(remittance);
        
        log.info("補件完成，重新啟動處理: remittanceId={}", remittanceId);
    }
    
    /**
     * 取消解款
     */
    @Transactional
    public void cancelRemittance(String remittanceId, String reason, String cancelledBy) {
        log.info("取消解款: remittanceId={}, reason={}, cancelledBy={}", 
                remittanceId, reason, maskId(cancelledBy));
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (remittance.isCompleted()) {
            throw new BusinessException("ALREADY_COMPLETED", "已完成的解款無法取消");
        }
        
        remittance.cancel(reason);
        remittanceRepository.save(remittance);
        
        log.info("解款已取消: remittanceId={}", remittanceId);
    }
    
    /**
     * 查詢解款狀態
     */
    public IndividualRemittance getRemittanceStatus(String remittanceId) {
        return getRemittanceById(remittanceId);
    }
    
    /**
     * 根據身分證號查詢解款記錄
     */
    public List<IndividualRemittance> getRemittancesByTaiwanId(String taiwanId, int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        LocalDateTime endDate = LocalDateTime.now();
        
        return remittanceRepository.findByTaiwanIdAndDateRange(
                taiwanId, startDate, endDate, 
                org.springframework.data.domain.Pageable.unpaged()
        ).getContent();
    }
    
    /**
     * 處理逾期的解款申請
     */
    @Transactional
    public void processOverdueRemittances() {
        log.info("開始處理逾期解款申請");
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
        List<IndividualRemittance> overdueRemittances = 
                remittanceRepository.findTimeoutRemittances(cutoffTime, 100);
        
        for (IndividualRemittance remittance : overdueRemittances) {
            try {
                processOverdueRemittance(remittance);
            } catch (Exception e) {
                log.error("處理逾期解款失敗: remittanceId={}, error={}", 
                        remittance.getRemittanceId(), e.getMessage());
            }
        }
        
        log.info("逾期解款處理完成，處理數量: {}", overdueRemittances.size());
    }
    
    /**
     * 更新匯率
     */
    @Transactional
    public void updateExchangeRate(String remittanceId, BigDecimal newRate, String updatedBy) {
        log.info("更新匯率: remittanceId={}, newRate={}, updatedBy={}", 
                remittanceId, newRate, maskId(updatedBy));
        
        IndividualRemittance remittance = getRemittanceById(remittanceId);
        
        if (remittance.isCompleted()) {
            throw new BusinessException("ALREADY_COMPLETED", "已完成的解款無法更新匯率");
        }
        
        // 重新計算金額
        BigDecimal newTwdAmount = remittance.getAmount().multiply(newRate);
        
        remittance.setExchangeRate(newRate);
        remittance.setTwdAmount(newTwdAmount);
        remittance.setSystemNote("匯率已更新 by " + updatedBy);
        
        remittanceRepository.save(remittance);
        
        log.info("匯率更新完成: remittanceId={}, newTwdAmount={}", remittanceId, newTwdAmount);
    }
    
    /**
     * 批次更新狀態
     */
    @Transactional
    public int batchUpdateStatus(List<String> remittanceIds, 
                                IndividualRemittance.ProcessingStatus newStatus,
                                String reason) {
        log.info("批次更新狀態: count={}, newStatus={}", remittanceIds.size(), newStatus);
        
        int updatedCount = remittanceRepository.batchUpdateStatus(
                remittanceIds, newStatus, reason);
        
        log.info("批次狀態更新完成: updatedCount={}", updatedCount);
        return updatedCount;
    }
    
    /**
     * 驗證解款資格
     */
    public boolean validateRemittanceEligibility(String taiwanId, BigDecimal amount, String currency) {
        log.debug("驗證解款資格: taiwanId={}, amount={} {}", maskId(taiwanId), amount, currency);
        
        try {
            // 檢查個人額度限制
            if (!validationService.checkPersonalLimit(taiwanId, amount, currency)) {
                log.warn("超出個人額度限制: taiwanId={}", maskId(taiwanId));
                return false;
            }
            
            // 檢查風險評估
            if (!validationService.checkRiskAssessment(taiwanId, amount)) {
                log.warn("風險評估未通過: taiwanId={}", maskId(taiwanId));
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("解款資格驗證失敗: {}", e.getMessage());
            return false;
        }
    }
    
    // ==================== 私有方法 ====================
    
    private IndividualRemittance getRemittanceById(String remittanceId) {
        return remittanceRepository.findByRemittanceId(remittanceId)
                .orElseThrow(() -> new BusinessException("REMITTANCE_NOT_FOUND", 
                        "找不到解款記錄: " + remittanceId));
    }
    
    private boolean requiresAdditionalReview(IndividualRemittance remittance) {
        // 高金額需要主管審核
        if (remittance.isHighRiskAmount()) {
            return true;
        }
        
        // 特定資金來源需要審核
        String sourceOfFund = remittance.getSourceOfFund();
        if ("07".equals(sourceOfFund) || "08".equals(sourceOfFund)) { // 投資收益或其他
            return true;
        }
        
        return false;
    }
    
    private void performRemittanceExecution(IndividualRemittance remittance) {
        log.debug("執行實際解款操作: remittanceId={}", remittance.getRemittanceId());
        
        // TODO: 整合核心銀行系統
        // 1. 調用核心系統API執行解款
        // 2. 檢查帳戶餘額
        // 3. 執行轉帳
        // 4. 更新交易記錄
        
        // 模擬處理時間
        try {
            Thread.sleep(1000); // 模擬1秒處理時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.debug("解款執行完成: remittanceId={}", remittance.getRemittanceId());
    }
    
    private void processOverdueRemittance(IndividualRemittance remittance) {
        log.debug("處理逾期解款: remittanceId={}", remittance.getRemittanceId());
        
        if (remittance.isSupplementOverdue()) {
            // 補件逾期，取消申請
            remittance.cancel("補件逾期未提供");
        } else if (remittance.isOverdue()) {
            // 處理逾期，標記為失敗
            remittance.markAsFailed("處理逾期");
        }
        
        remittanceRepository.save(remittance);
    }
    
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return id;
        }
        return id.substring(0, 2) + "***" + id.substring(id.length() - 2);
    }
}