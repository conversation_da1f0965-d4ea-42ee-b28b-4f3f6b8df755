package com.kgi.module.individual.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 銀行帳戶驗證服務
 * 負責驗證銀行帳戶的有效性和帳戶名稱比對
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BankAccountVerificationService {
    
    // 銀行代碼對照表(部分主要銀行)
    private static final Map<String, String> BANK_CODE_MAP = new HashMap<>();
    
    static {
        BANK_CODE_MAP.put("004", "台灣銀行");
        BANK_CODE_MAP.put("005", "土地銀行");
        BANK_CODE_MAP.put("006", "合作金庫");
        BANK_CODE_MAP.put("007", "第一銀行");
        BANK_CODE_MAP.put("008", "華南銀行");
        BANK_CODE_MAP.put("009", "彰化銀行");
        BANK_CODE_MAP.put("011", "上海銀行");
        BANK_CODE_MAP.put("012", "台北富邦");
        BANK_CODE_MAP.put("013", "國泰世華");
        BANK_CODE_MAP.put("017", "兆豐銀行");
        BANK_CODE_MAP.put("021", "花旗銀行");
        BANK_CODE_MAP.put("700", "中華郵政");
        BANK_CODE_MAP.put("803", "聯邦銀行");
        BANK_CODE_MAP.put("806", "元大銀行");
        BANK_CODE_MAP.put("807", "永豐銀行");
        BANK_CODE_MAP.put("808", "玉山銀行");
        BANK_CODE_MAP.put("809", "凱基銀行");
        BANK_CODE_MAP.put("812", "台新銀行");
        BANK_CODE_MAP.put("816", "安泰銀行");
        BANK_CODE_MAP.put("822", "中國信託");
    }
    
    /**
     * 驗證銀行帳戶
     * 
     * @param bankCode 銀行代碼
     * @param accountNumber 帳戶號碼
     * @param expectedAccountName 預期的帳戶名稱
     * @return 驗證結果
     */
    public boolean verifyAccount(String bankCode, String accountNumber, String expectedAccountName) {
        log.debug("驗證銀行帳戶: bankCode={}, account={}, expectedName={}", 
                bankCode, maskAccount(accountNumber), maskName(expectedAccountName));
        
        try {
            // 1. 驗證銀行代碼
            if (!isValidBankCode(bankCode)) {
                log.warn("無效的銀行代碼: {}", bankCode);
                return false;
            }
            
            // 2. 驗證帳戶號碼格式
            if (!isValidAccountNumberFormat(accountNumber)) {
                log.warn("帳戶號碼格式錯誤: {}", maskAccount(accountNumber));
                return false;
            }
            
            // 3. 呼叫外部API驗證帳戶存在性
            AccountVerificationResult result = callBankAccountAPI(bankCode, accountNumber);
            
            if (!result.isAccountExists()) {
                log.warn("帳戶不存在: bankCode={}, account={}", bankCode, maskAccount(accountNumber));
                return false;
            }
            
            // 4. 比對帳戶名稱
            if (!compareAccountName(result.getAccountName(), expectedAccountName)) {
                log.warn("帳戶名稱不符: expected={}, actual={}", 
                        maskName(expectedAccountName), maskName(result.getAccountName()));
                return false;
            }
            
            log.debug("銀行帳戶驗證成功");
            return true;
            
        } catch (Exception e) {
            log.error("銀行帳戶驗證發生異常: {}", e.getMessage(), e);
            // 依據業務規則決定是否允許在API異常時繼續處理
            return handleVerificationException(e);
        }
    }
    
    /**
     * 取得銀行名稱
     */
    public String getBankName(String bankCode) {
        String bankName = BANK_CODE_MAP.get(bankCode);
        if (bankName == null) {
            log.warn("未知的銀行代碼: {}", bankCode);
            return "未知銀行";
        }
        return bankName;
    }
    
    /**
     * 驗證銀行代碼是否有效
     */
    public boolean isValidBankCode(String bankCode) {
        if (bankCode == null || !bankCode.matches("^\\d{3}$")) {
            return false;
        }
        
        // 檢查是否為已知的銀行代碼
        return BANK_CODE_MAP.containsKey(bankCode) || isValidOtherBankCode(bankCode);
    }
    
    /**
     * 驗證帳戶號碼格式
     */
    private boolean isValidAccountNumberFormat(String accountNumber) {
        if (accountNumber == null) {
            return false;
        }
        
        // 移除空白和分隔符號
        String cleanAccount = accountNumber.replaceAll("[\\s-]", "");
        
        // 帳戶號碼通常為7-20位數字
        return cleanAccount.matches("^\\d{7,20}$");
    }
    
    /**
     * 呼叫銀行帳戶驗證API
     */
    private AccountVerificationResult callBankAccountAPI(String bankCode, String accountNumber) {
        log.debug("呼叫銀行API驗證帳戶: bankCode={}, account={}", bankCode, maskAccount(accountNumber));
        
        try {
            // TODO: 實際整合金融API或財金公司API
            // 1. 可能整合財金公司的帳戶驗證服務
            // 2. 或各銀行提供的帳戶驗證API
            // 3. 或透過ACH系統進行小額驗證
            
            // 模擬API呼叫(實際環境需要替換為真實API)
            return simulateBankAPICall(bankCode, accountNumber);
            
        } catch (Exception e) {
            log.error("銀行API呼叫失敗: {}", e.getMessage(), e);
            throw new BankApiException("銀行帳戶驗證API呼叫失敗", e);
        }
    }
    
    /**
     * 比對帳戶名稱
     */
    private boolean compareAccountName(String actualName, String expectedName) {
        if (actualName == null || expectedName == null) {
            return false;
        }
        
        // 正規化處理: 移除空白、轉換為統一格式
        String normalizedActual = normalizeName(actualName);
        String normalizedExpected = normalizeName(expectedName);
        
        // 精確比對
        if (normalizedActual.equals(normalizedExpected)) {
            return true;
        }
        
        // 模糊比對(考慮全形半形、大小寫等差異)
        return fuzzyNameMatch(normalizedActual, normalizedExpected);
    }
    
    /**
     * 正規化姓名
     */
    private String normalizeName(String name) {
        if (name == null) {
            return "";
        }
        
        return name.trim()
                   .replaceAll("\\s+", "") // 移除所有空白
                   .toUpperCase() // 轉大寫
                   .replaceAll("[　\\u3000]", ""); // 移除全形空白
    }
    
    /**
     * 模糊姓名比對
     */
    private boolean fuzzyNameMatch(String name1, String name2) {
        // 計算相似度
        double similarity = calculateStringSimilarity(name1, name2);
        
        // 相似度閾值(可設定為可配置參數)
        double threshold = 0.8;
        
        boolean isMatch = similarity >= threshold;
        log.debug("姓名模糊比對: similarity={}, threshold={}, match={}", 
                similarity, threshold, isMatch);
        
        return isMatch;
    }
    
    /**
     * 計算字串相似度(使用Levenshtein距離)
     */
    private double calculateStringSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) {
            return 0.0;
        }
        
        if (s1.equals(s2)) {
            return 1.0;
        }
        
        int maxLength = Math.max(s1.length(), s2.length());
        if (maxLength == 0) {
            return 1.0;
        }
        
        int distance = levenshteinDistance(s1, s2);
        return 1.0 - (double) distance / maxLength;
    }
    
    /**
     * Levenshtein距離演算法
     */
    private int levenshteinDistance(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();
        
        int[][] dp = new int[m + 1][n + 1];
        
        for (int i = 0; i <= m; i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= n; j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = 1 + Math.min(Math.min(dp[i - 1][j], dp[i][j - 1]), dp[i - 1][j - 1]);
                }
            }
        }
        
        return dp[m][n];
    }
    
    /**
     * 模擬銀行API呼叫
     */
    private AccountVerificationResult simulateBankAPICall(String bankCode, String accountNumber) {
        log.debug("模擬銀行API呼叫");
        
        // 模擬延遲
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 模擬回應(實際環境需要替換為真實API回應)
        // 假設90%的帳戶驗證會成功
        boolean accountExists = !accountNumber.endsWith("000"); // 簡單模擬邏輯
        
        String accountName = accountExists ? generateMockAccountName() : null;
        
        return new AccountVerificationResult(accountExists, accountName, bankCode);
    }
    
    /**
     * 產生模擬帳戶名稱
     */
    private String generateMockAccountName() {
        // 模擬常見姓名
        String[] surnames = {"王", "李", "張", "劉", "陳", "楊", "黃", "趙", "吳", "周"};
        String[] names = {"小明", "美麗", "志強", "淑芬", "建國", "淑惠", "志豪", "雅婷"};
        
        int surnameIndex = (int) (Math.random() * surnames.length);
        int nameIndex = (int) (Math.random() * names.length);
        
        return surnames[surnameIndex] + names[nameIndex];
    }
    
    /**
     * 檢查是否為其他有效銀行代碼
     */
    private boolean isValidOtherBankCode(String bankCode) {
        // 其他銀行代碼範圍檢查
        // 本國銀行: 001-999
        // 外商銀行: 001-099 (部分範圍)
        // 信用合作社: 101-399
        // 農漁會信用部: 501-699
        // 票券公司: 901-999
        
        try {
            int code = Integer.parseInt(bankCode);
            return code >= 1 && code <= 999;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 處理驗證異常
     */
    private boolean handleVerificationException(Exception e) {
        // 依據異常類型決定處理策略
        if (e instanceof BankApiException) {
            // API異常時，可依據業務規則決定是否允許繼續
            // 例如：非關鍵驗證可允許，關鍵驗證則拒絕
            log.warn("銀行API異常，允許繼續處理: {}", e.getMessage());
            return true; // 或false，依據業務需求
        }
        
        return false;
    }
    
    /**
     * 遮罩帳號
     */
    private String maskAccount(String account) {
        if (account == null || account.length() < 6) {
            return account;
        }
        
        int length = account.length();
        if (length <= 6) {
            return account.substring(0, 3) + "***";
        } else {
            return account.substring(0, 3) + 
                   "*".repeat(length - 6) + 
                   account.substring(length - 3);
        }
    }
    
    /**
     * 遮罩姓名
     */
    private String maskName(String name) {
        if (name == null || name.length() < 2) {
            return name;
        }
        
        if (name.length() == 2) {
            return name.charAt(0) + "*";
        } else {
            return name.charAt(0) + "*".repeat(name.length() - 2) + name.charAt(name.length() - 1);
        }
    }
    
    /**
     * 帳戶驗證結果內部類別
     */
    public static class AccountVerificationResult {
        private final boolean accountExists;
        private final String accountName;
        private final String bankCode;
        
        public AccountVerificationResult(boolean accountExists, String accountName, String bankCode) {
            this.accountExists = accountExists;
            this.accountName = accountName;
            this.bankCode = bankCode;
        }
        
        public boolean isAccountExists() {
            return accountExists;
        }
        
        public String getAccountName() {
            return accountName;
        }
        
        public String getBankCode() {
            return bankCode;
        }
    }
    
    /**
     * 銀行API異常
     */
    public static class BankApiException extends RuntimeException {
        public BankApiException(String message) {
            super(message);
        }
        
        public BankApiException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}