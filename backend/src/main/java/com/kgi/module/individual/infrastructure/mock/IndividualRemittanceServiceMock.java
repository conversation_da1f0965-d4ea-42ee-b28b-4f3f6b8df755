package com.kgi.module.individual.infrastructure.mock;

import com.kgi.core.service.mock.base.BaseMockService;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.model.IdentityVerification;
import com.kgi.module.individual.domain.model.RemittanceDetail;
import com.kgi.module.individual.domain.model.PersonalInfo;
import com.kgi.module.individual.domain.model.BankAccount;
import com.kgi.core.domain.model.TaiwanId;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 個人解款服務 Mock 實作
 * 模擬個人客戶的解款申請流程
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service("individualRemittanceServiceMock")
public class IndividualRemittanceServiceMock extends BaseMockService {

    /**
     * 初始化解款申請
     */
    public IndividualRemittance initializeRemittance(String caseNo, String type) {
        log.info("[MOCK] Initializing individual remittance. CaseNo: {}, Type: {}", caseNo, type);
        
        simulateDelay();
        
        // 從 Mock 資料庫取得測試資料
        Map<String, Object> testData = getMockData("test.individual.default", Map.class);
        
        IndividualRemittance remittance = IndividualRemittance.builder()
                .remittanceId(generateId())
                .theirRefNo("MOCK" + caseNo)
                .remitRefNo("RMT" + caseNo)
                .processingStatus(IndividualRemittance.ProcessingStatus.PENDING)
                .verificationStatus(IndividualRemittance.VerificationStatus.PENDING)
                .businessStatusCode("01")
                .businessStatusDesc("申請初始化")
                .isUrgent(false)
                .requiresSupplement(false)
                .build();
        
        // 設定測試資料
        if (testData != null) {
            remittance.setPayerName("JOHN DOE");
            remittance.setCurrency((String) testData.get("currency"));
            remittance.setAmount(BigDecimal.valueOf((Double) testData.get("amount")));
            
            // 建立收款人資訊
            PersonalInfo beneficiary = new PersonalInfo();
            // 創建 TaiwanId 對象
            String payeeId = (String) testData.get("payeeId");
            if (payeeId != null) {
                beneficiary.setTaiwanId(TaiwanId.of(payeeId));
            }
            beneficiary.setChineseName((String) testData.get("payeeName"));
            beneficiary.setEnglishName((String) testData.get("payeeEngName"));
            beneficiary.setContactPhone((String) testData.get("payeeTel"));
            beneficiary.setEmail((String) testData.get("payeeMail"));
            
            // 建立銀行帳戶資訊
            BankAccount bankAccount = new BankAccount();
            bankAccount.setBankCode((String) testData.get("payeeBankCode"));
            bankAccount.setAccountNumber((String) testData.get("payeeAccount"));
            beneficiary.setBankAccount(bankAccount);
            
            remittance.setBeneficiary(beneficiary);
        }
        
        // 儲存到 Mock 資料庫
        saveMockData("remittance:" + caseNo, remittance);
        
        logMockCall("IndividualRemittanceService", "initializeRemittance", 
            Map.of("caseNo", caseNo, "type", type), 
            remittance);
        
        return remittance;
    }

    /**
     * 驗證身份
     */
    public boolean verifyIdentity(String caseNo, IdentityVerification verification) {
        log.info("[MOCK] Verifying identity for case: {}", caseNo);
        
        simulateDelay();
        
        // 模擬錯誤情況
        if (shouldSimulateError()) {
            log.warn("[MOCK] Simulating identity verification failure");
            return false;
        }
        
        // 取得案件資料
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        if (remittance == null) {
            log.error("[MOCK] Case not found: {}", caseNo);
            return false;
        }
        
        PersonalInfo beneficiary = remittance.getBeneficiary();
        if (beneficiary == null) {
            log.error("[MOCK] Beneficiary info not found");
            return false;
        }
        
        // 驗證身份資料
        boolean isValid = true;
        
        // 驗證中文姓名
        if (!beneficiary.getChineseName().equals(verification.getChineseName())) {
            log.warn("[MOCK] Chinese name mismatch. Expected: {}, Actual: {}", 
                beneficiary.getChineseName(), verification.getChineseName());
            isValid = false;
        }
        
        // 驗證身分證號
        String beneficiaryId = beneficiary.getTaiwanId() != null ? 
            beneficiary.getTaiwanId().getValue() : null;
        if (beneficiaryId != null && !beneficiaryId.equals(verification.getIdNumber())) {
            log.warn("[MOCK] ID number mismatch. Expected: {}, Actual: {}", 
                beneficiaryId, verification.getIdNumber());
            isValid = false;
        }
        
        // 驗證銀行帳號
        BankAccount bankAccount = beneficiary.getBankAccount();
        if (bankAccount != null && !bankAccount.getAccountNumber().equals(verification.getBankAccount())) {
            log.warn("[MOCK] Bank account mismatch. Expected: {}, Actual: {}", 
                bankAccount.getAccountNumber(), verification.getBankAccount());
            isValid = false;
        }
        
        if (isValid) {
            remittance.completeVerification();
            saveMockData("remittance:" + caseNo, remittance);
            log.info("[MOCK] Identity verification successful");
        } else {
            log.warn("[MOCK] Identity verification failed");
        }
        
        logMockCall("IndividualRemittanceService", "verifyIdentity", 
            Map.of("caseNo", caseNo, "verification", verification), 
            Map.of("success", isValid));
        
        return isValid;
    }

    /**
     * 確認匯款資料
     */
    public RemittanceDetail confirmRemittance(String caseNo, String sourceOfFund, String detail) {
        log.info("[MOCK] Confirming remittance for case: {}", caseNo);
        
        simulateDelay();
        
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        if (remittance == null) {
            log.error("[MOCK] Case not found: {}", caseNo);
            return null;
        }
        
        PersonalInfo beneficiary = remittance.getBeneficiary();
        BankAccount bankAccount = beneficiary != null ? beneficiary.getBankAccount() : null;
        
        RemittanceDetail remittanceDetail = new RemittanceDetail();
        remittanceDetail.setCaseNo(caseNo);
        remittanceDetail.setTransactionId(remittance.getRemittanceId());
        remittanceDetail.setSourceOfFund(sourceOfFund);
        remittanceDetail.setDetail(detail);
        remittanceDetail.setPayerName(remittance.getPayerName());
        
        if (beneficiary != null) {
            remittanceDetail.setPayeeName(beneficiary.getChineseName());
            remittanceDetail.setPayeeEngName(beneficiary.getEnglishName());
            String payeeId = beneficiary.getTaiwanId() != null ? 
                beneficiary.getTaiwanId().getValue() : null;
            remittanceDetail.setPayeeId(payeeId);
        }
        
        if (bankAccount != null) {
            remittanceDetail.setPayeeAccount(bankAccount.getAccountNumber());
            remittanceDetail.setPayeeBankCode(bankAccount.getBankCode());
        }
        
        remittanceDetail.setCurrency(remittance.getCurrency());
        remittanceDetail.setAmount(remittance.getAmount());
        remittanceDetail.setConfirmTime(LocalDateTime.now());
        
        // 更新匯款資訊
        remittance.setSourceOfFund(sourceOfFund);
        remittance.setCustomerNote(detail);
        remittance.updateBusinessStatus("20", "匯款資料已確認");
        saveMockData("remittance:" + caseNo, remittance);
        
        logMockCall("IndividualRemittanceService", "confirmRemittance", 
            Map.of("caseNo", caseNo, "sourceOfFund", sourceOfFund, "detail", detail), 
            remittanceDetail);
        
        return remittanceDetail;
    }

    /**
     * 提交申請
     */
    public IndividualRemittance submitApplication(String caseNo) {
        log.info("[MOCK] Submitting application for case: {}", caseNo);
        
        simulateDelay();
        
        // 模擬提交失敗
        if (shouldSimulateError()) {
            log.error("[MOCK] Simulating submission failure");
            throw new RuntimeException("Mock submission error");
        }
        
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        if (remittance == null) {
            log.error("[MOCK] Case not found: {}", caseNo);
            return null;
        }
        
        // 開始處理
        remittance.startProcessing();
        remittance.setEstimatedCompletionTime(LocalDateTime.now().plusHours(4));
        
        saveMockData("remittance:" + caseNo, remittance);
        
        logMockCall("IndividualRemittanceService", "submitApplication", 
            Map.of("caseNo", caseNo), 
            remittance);
        
        return remittance;
    }

    /**
     * 查詢解款狀態
     */
    public IndividualRemittance getRemittanceStatus(String caseNo) {
        log.info("[MOCK] Getting remittance status for case: {}", caseNo);
        
        simulateDelay();
        
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        
        if (remittance == null) {
            log.warn("[MOCK] Case not found: {}", caseNo);
            // 創建一個空的案件資料
            remittance = IndividualRemittance.builder()
                    .remittanceId(caseNo)
                    .businessStatusCode("00")
                    .businessStatusDesc("查無資料")
                    .processingStatus(IndividualRemittance.ProcessingStatus.FAILED)
                    .build();
        }
        
        logMockCall("IndividualRemittanceService", "getRemittanceStatus", 
            Map.of("caseNo", caseNo), 
            remittance);
        
        return remittance;
    }

    /**
     * 測試用方法：完成解款
     */
    public void completeRemittance(String caseNo) {
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        if (remittance != null) {
            remittance.complete();
            saveMockData("remittance:" + caseNo, remittance);
            log.info("[MOCK] Completed remittance: {}", caseNo);
        }
    }

    /**
     * 測試用方法：觸發補件
     */
    public void triggerSupplement(String caseNo, String reason) {
        IndividualRemittance remittance = getMockData("remittance:" + caseNo, IndividualRemittance.class);
        if (remittance != null) {
            remittance.requireSupplement(LocalDateTime.now().plusDays(7), reason);
            saveMockData("remittance:" + caseNo, remittance);
            log.info("[MOCK] Triggered supplement for case: {}", caseNo);
        }
    }
}