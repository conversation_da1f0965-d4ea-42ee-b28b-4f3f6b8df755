package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 匯款搜尋響應
 * 返回匯款搜尋結果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemittanceSearchResponse {
    
    /**
     * 匯款記錄列表
     */
    private List<RemittanceRecord> remittances;
    
    /**
     * 總記錄數
     */
    private Long totalCount;
    
    /**
     * 當前頁碼
     */
    private Integer currentPage;
    
    /**
     * 每頁大小
     */
    private Integer pageSize;
    
    /**
     * 總頁數
     */
    private Integer totalPages;
    
    /**
     * 是否有下一頁
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一頁
     */
    private Boolean hasPrevious;
    
    /**
     * 搜尋統計資訊
     */
    private SearchSummary summary;
    
    /**
     * 搜尋時間
     */
    private LocalDateTime searchTime;
    
    /**
     * 匯款記錄內部類
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RemittanceRecord {
        
        /**
         * 匯款編號
         */
        private String remittanceId;
        
        /**
         * 外部參考號
         */
        private String theirRefNo;
        
        /**
         * 付款人姓名
         */
        private String payerName;
        
        /**
         * 收款人姓名
         */
        private String beneficiaryName;
        
        /**
         * 匯款金額
         */
        private BigDecimal amount;
        
        /**
         * 匯款幣別
         */
        private String currency;
        
        /**
         * 收款日期
         */
        private LocalDateTime receivedDate;
        
        /**
         * 處理狀態
         */
        private String processingStatus;
        
        /**
         * 狀態描述
         */
        private String statusDescription;
        
        /**
         * 匯款性質
         */
        private String remittanceNature;
        
        /**
         * 銀行代碼
         */
        private String bankCode;
        
        /**
         * 銀行名稱
         */
        private String bankName;
        
        /**
         * 是否可以申請解款
         */
        private Boolean canApplyRemittance;
        
        /**
         * 不可申請的原因
         */
        private String restrictionReason;
        
        /**
         * 到期日
         */
        private LocalDateTime expiryDate;
        
        /**
         * 備註
         */
        private String remarks;
        
        /**
         * 最後更新時間
         */
        private LocalDateTime lastUpdated;
    }
    
    /**
     * 搜尋統計摘要
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchSummary {
        
        /**
         * 可申請解款的筆數
         */
        private Integer availableCount;
        
        /**
         * 已處理的筆數
         */
        private Integer processedCount;
        
        /**
         * 已過期的筆數
         */
        private Integer expiredCount;
        
        /**
         * 總金額
         */
        private BigDecimal totalAmount;
        
        /**
         * 平均金額
         */
        private BigDecimal averageAmount;
        
        /**
         * 涉及的幣別列表
         */
        private List<String> currencies;
        
        /**
         * 涉及的銀行列表
         */
        private List<String> banks;
        
        /**
         * 最早匯款日期
         */
        private LocalDateTime earliestDate;
        
        /**
         * 最晚匯款日期
         */
        private LocalDateTime latestDate;
    }
}