package com.kgi.module.individual.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 條款同意請求DTO
 * 對應前端的TermsAgreementRequest介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TermsAgreementRequest {
    
    /**
     * 使用者ID (可選，可從session中取得)
     */
    @Size(max = 50, message = "使用者ID長度不能超過50字元")
    private String userId;
    
    /**
     * 台灣身分證號
     */
    @NotBlank(message = "台灣身分證號不能為空")
    @Size(min = 10, max = 10, message = "台灣身分證號長度必須為10字元")
    private String taiwanId;
    
    /**
     * 同意的條款版本
     */
    @NotBlank(message = "條款版本不能為空")
    @Size(max = 10, message = "條款版本長度不能超過10字元")
    private String termsVersion;
    
    /**
     * 同意時間
     */
    @NotNull(message = "同意時間不能為空")
    private LocalDateTime agreedAt;
    
    /**
     * 客戶端IP位址
     */
    @Size(max = 45, message = "IP位址長度不能超過45字元")
    private String ipAddress;
    
    /**
     * 瀏覽器User-Agent
     */
    @Size(max = 500, message = "User-Agent長度不能超過500字元")
    private String userAgent;
    
    /**
     * 同意方式 (WEB/MOBILE/API)
     */
    @Size(max = 20, message = "同意方式長度不能超過20字元")
    private String agreementMethod;
    
    /**
     * 裝置識別碼 (可選)
     */
    @Size(max = 100, message = "裝置識別碼長度不能超過100字元")
    private String deviceId;
    
    /**
     * 瀏覽器指紋 (可選)
     */
    @Size(max = 200, message = "瀏覽器指紋長度不能超過200字元")
    private String browserFingerprint;
    
    /**
     * 地理位置資訊 (可選)
     */
    private GeolocationInfo geolocation;
    
    /**
     * 額外的追蹤資訊 (可選)
     */
    private AdditionalTrackingInfo additionalInfo;
    
    /**
     * 地理位置資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GeolocationInfo {
        
        /**
         * 緯度
         */
        private Double latitude;
        
        /**
         * 經度
         */
        private Double longitude;
        
        /**
         * 精確度 (公尺)
         */
        private Double accuracy;
        
        /**
         * 國家代碼
         */
        @Size(max = 2, message = "國家代碼長度不能超過2字元")
        private String countryCode;
        
        /**
         * 城市
         */
        @Size(max = 50, message = "城市長度不能超過50字元")
        private String city;
        
        /**
         * 時區
         */
        @Size(max = 50, message = "時區長度不能超過50字元")
        private String timezone;
    }
    
    /**
     * 額外追蹤資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalTrackingInfo {
        
        /**
         * 來源頁面URL
         */
        @Size(max = 1000, message = "來源頁面URL長度不能超過1000字元")
        private String referrerUrl;
        
        /**
         * 當前頁面URL
         */
        @Size(max = 1000, message = "當前頁面URL長度不能超過1000字元")
        private String currentUrl;
        
        /**
         * 螢幕解析度
         */
        @Size(max = 20, message = "螢幕解析度長度不能超過20字元")
        private String screenResolution;
        
        /**
         * 瀏覽器語言
         */
        @Size(max = 10, message = "瀏覽器語言長度不能超過10字元")
        private String browserLanguage;
        
        /**
         * 作業系統
         */
        @Size(max = 50, message = "作業系統長度不能超過50字元")
        private String operatingSystem;
        
        /**
         * 是否為行動裝置
         */
        private Boolean isMobile;
        
        /**
         * 網路類型 (WIFI/4G/5G等)
         */
        @Size(max = 20, message = "網路類型長度不能超過20字元")
        private String networkType;
        
        /**
         * 會話ID
         */
        @Size(max = 100, message = "會話ID長度不能超過100字元")
        private String sessionId;
    }
    
    /**
     * 驗證IP位址格式
     */
    public boolean isValidIpAddress() {
        if (ipAddress == null) {
            return true; // IP位址為可選欄位
        }
        
        // IPv4 格式驗證
        String ipv4Pattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
        if (ipAddress.matches(ipv4Pattern)) {
            return true;
        }
        
        // IPv6 格式驗證 (簡化版)
        String ipv6Pattern = "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$";
        return ipAddress.matches(ipv6Pattern);
    }
    
    /**
     * 檢查是否為有效的條款版本格式
     */
    public boolean isValidTermsVersion() {
        if (termsVersion == null) {
            return false;
        }
        
        // 版本格式: x.y 或 x.y.z
        String versionPattern = "^\\d+\\.\\d+(\\.\\d+)?$";
        return termsVersion.matches(versionPattern);
    }
    
    /**
     * 檢查同意時間是否合理
     */
    public boolean isValidAgreedTime() {
        if (agreedAt == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneHourAgo = now.minusHours(1);
        
        // 同意時間應該在過去一小時內到現在之間
        return !agreedAt.isBefore(oneHourAgo) && !agreedAt.isAfter(now);
    }
    
    /**
     * 取得簡化的User-Agent資訊
     */
    public String getSimplifiedUserAgent() {
        if (userAgent == null || userAgent.isEmpty()) {
            return "未知";
        }
        
        // 提取主要瀏覽器資訊
        if (userAgent.contains("Chrome")) {
            return "Chrome";
        } else if (userAgent.contains("Firefox")) {
            return "Firefox";
        } else if (userAgent.contains("Safari")) {
            return "Safari";
        } else if (userAgent.contains("Edge")) {
            return "Edge";
        } else {
            return "其他";
        }
    }
    
    /**
     * 取得裝置類型
     */
    public String getDeviceType() {
        if (additionalInfo != null && additionalInfo.getIsMobile() != null) {
            return Boolean.TRUE.equals(additionalInfo.getIsMobile()) ? "行動裝置" : "桌面裝置";
        }
        
        if (userAgent != null) {
            if (userAgent.contains("Mobile") || userAgent.contains("Android") || userAgent.contains("iPhone")) {
                return "行動裝置";
            } else {
                return "桌面裝置";
            }
        }
        
        return "未知";
    }
    
    /**
     * 檢查地理位置資訊是否有效
     */
    public boolean hasValidGeolocation() {
        return geolocation != null && 
               geolocation.getLatitude() != null && 
               geolocation.getLongitude() != null &&
               geolocation.getLatitude() >= -90 && geolocation.getLatitude() <= 90 &&
               geolocation.getLongitude() >= -180 && geolocation.getLongitude() <= 180;
    }
    
    /**
     * 取得同意的摘要資訊
     */
    public String getAgreementSummary() {
        return String.format("版本 %s，於 %s 透過 %s 同意", 
                termsVersion, 
                agreedAt != null ? agreedAt.toString() : "未知時間",
                getDeviceType());
    }
}