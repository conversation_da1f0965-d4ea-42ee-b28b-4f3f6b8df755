# Individual Module - 自然人解款模組

## 📋 模組概述

自然人解款模組負責處理個人客戶的跨境匯款解付業務，提供完整的自然人數位解款流程。

## 🎯 核心功能

### 主要業務流程
1. **條款同意與合規檢查** - 個資告知聲明和數位解款條款
2. **身份證件驗證** - 台灣身分證格式和檢查碼驗證
3. **OTP 簡訊驗證** - 手機簡訊雙因子認證
4. **匯款資訊確認** - 匯款人、金額、匯率確認
5. **匯款性質選擇** - 符合外匯申報要求的性質代碼
6. **金額計算與手續費處理** - 匯率轉換和手續費計算
7. **跨行通匯檢核** - 透過財金公司驗證收款人資訊

### 特殊功能
- **白名單機制** - 支援未來同一匯款人自動解付
- **資料預填** - 系統預填部分解款資訊減少錯誤
- **即時狀態追蹤** - 36種業務狀態即時更新

## 🏗️ 架構設計

### Clean Architecture 分層

```
┌─────────────────────────────────────────────┐
│               Adapters Layer                │
│  IndividualRemittanceController             │
├─────────────────────────────────────────────┤
│              Application Layer              │
│  Facade │ UseCase │ DTO (Request/Response) │
├─────────────────────────────────────────────┤
│                Domain Layer                 │
│  IndividualRemittance │ PersonalInfo       │
│  BankAccount │ Repository │ DomainService  │
├─────────────────────────────────────────────┤
│            Infrastructure Layer             │
│  JPA Repository │ External Service         │
└─────────────────────────────────────────────┘
```

### 核心實體和值對象

#### 🔸 IndividualRemittance (核心實體)
```java
- remittanceId: String           // 匯款編號
- theirRefNo: String            // 跨境平台編號  
- payerName: String             // 匯款人姓名
- currency: String              // 匯款幣別
- amount: BigDecimal            // 匯款金額
- beneficiary: PersonalInfo     // 收款人資訊
- processingStatus: Enum        // 處理狀態
- verificationStatus: Enum      // 驗證狀態
```

#### 🔸 PersonalInfo (個人資訊值對象)
```java
- taiwanId: TaiwanId           // 台灣身分證號
- chineseName: String          // 中文姓名
- englishName: String          // 英文姓名
- birthDate: LocalDate         // 出生日期
- contactPhone: String         // 聯絡電話
- bankAccount: BankAccount     // 銀行帳戶
```

#### 🔸 BankAccount (銀行帳戶值對象)
```java
- bankCode: String             // 銀行代碼
- branchCode: String           // 分行代碼
- accountNumber: String        // 帳戶號碼
- bankName: String             // 銀行名稱
```

## 🔗 API 端點

### RESTful API 設計

```http
POST   /api/ibr/individual/remittance           # 自然人解款申請
POST   /api/ibr/individual/otp/verify           # OTP驗證
GET    /api/ibr/individual/remittance/{id}/status # 查詢解款狀態
POST   /api/ibr/individual/remittance/search    # 搜尋匯款記錄
```

### 請求/響應格式

#### 解款申請請求
```json
{
  "theirrefno": "REF123456789",
  "remitrefno": "RMT987654321", 
  "payername": "John Smith",
  "currency": "USD",
  "amount": 1000.00,
  "payeeengname": "王小明",
  "payeename": "Wang Xiao Ming",
  "payeeid": "A123456789",
  "payeeaccount": "*************",
  "payeebankcode": "012",
  "sourceoffound": "611"
}
```

#### 標準響應格式
```json
{
  "StatusCode": "00",
  "TxntMsg": "自然人解款申請成功",
  "data": { ... },
  "timestamp": *************
}
```

## 📊 業務狀態管理

### 處理狀態 (ProcessingStatus)
- `PENDING` - 待處理
- `PROCESSING` - 處理中  
- `COMPLETED` - 已完成
- `FAILED` - 失敗
- `CANCELLED` - 已取消

### 驗證狀態 (VerificationStatus)
- `PENDING` - 待驗證
- `OTP_VERIFIED` - OTP已驗證
- `FAILED` - 驗證失敗

## 🔒 安全機制

### 身份驗證
- **台灣身分證驗證** - 格式檢查 + 檢查碼驗算
- **OTP簡訊驗證** - 6位數字 + 5分鐘倒數
- **手機號碼驗證** - 09開頭10位數格式

### 資料保護
- **身分證遮罩** - 中間四位用*代替 (A12****789)
- **帳號遮罩** - 僅顯示後三碼
- **會話管理** - JWT Token時效控制

## 🔄 外部系統整合

### 整合系統
- **資通系統** - 跨行通匯檢核
- **AML系統** - 反洗錢檢核  
- **OTP服務** - 簡訊發送服務
- **匯率系統** - 即時匯率查詢

### 事件發布
```java
// 匯款接收事件
RemittanceReceivedEvent
// OTP驗證完成事件  
OtpVerificationCompletedEvent
// 解款完成事件
RemittanceCompletedEvent
```

## 📈 效能考量

### 快取策略
- 銀行列表快取 (30分鐘)
- 匯率資訊快取 (5分鐘)
- 用戶資訊快取 (會話期間)

### 併發處理
- 樂觀鎖版本控制
- 事務隔離級別控制
- 連接池優化配置

## 🧪 測試策略

### 單元測試
- 台灣身分證驗證邏輯
- 金額計算邏輯
- 業務規則驗證

### 整合測試  
- API端點測試
- 資料庫操作測試
- 外部系統模擬測試

## 📝 使用範例

### 基本解款流程
```java
// 1. 建立解款申請
DigitalRemittanceRequest request = DigitalRemittanceRequest.builder()
    .theirRefNo("REF123")
    .payerName("John Smith")
    .currency("USD")
    .amount(new BigDecimal("1000.00"))
    .build();

// 2. 處理解款
IndividualRemittanceResponse response = 
    individualRemittanceFacade.processRemittance(request);

// 3. OTP驗證
OtpVerificationRequest otpRequest = new OtpVerificationRequest();
otpRequest.setOtpCode("123456");
boolean verified = individualRemittanceFacade.verifyOtp(otpRequest);
```

## 🔮 未來擴展

### 計劃功能
- **生物辨識整合** - FIDO WebAuthn支援
- **多幣別支援** - 擴展更多幣別
- **批次處理** - 多筆匯款批次解付
- **行動端優化** - 行動裝置專用API

---

**模組負責人**: IBR開發團隊  
**最後更新**: 2025年6月1日  
**相關文件**: [IBR系統概覽](../../../../../../../.internal/ai_docs/00_IBR_system_overview.md)