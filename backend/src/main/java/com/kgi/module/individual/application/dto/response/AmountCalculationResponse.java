package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 金額計算響應DTO
 * 對應前端的AmountCalculation介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AmountCalculationResponse {
    
    /**
     * 原始匯款金額
     */
    private BigDecimal originalAmount;
    
    /**
     * 匯款幣別
     */
    private String currency;
    
    /**
     * 原始幣別
     */
    private String originalCurrency;
    
    /**
     * 適用匯率
     */
    private BigDecimal exchangeRate;
    
    /**
     * 台幣換算金額
     */
    private BigDecimal twdAmount;
    
    /**
     * 轉換後金額
     */
    private BigDecimal convertedAmount;
    
    /**
     * 手續費資訊
     */
    private FeeBreakdown fees;
    
    /**
     * 實收金額 (扣除手續費後)
     */
    private BigDecimal netAmount;
    
    /**
     * 計算時間
     */
    private LocalDateTime calculationTime;
    
    /**
     * 匯率有效期限 (分鐘)
     */
    private Integer exchangeRateValidityMinutes;
    
    /**
     * 是否為即時匯率
     */
    private Boolean isRealTimeRate;
    
    /**
     * 匯率來源
     */
    private String rateSource;
    
    /**
     * 警告訊息 (如果有)
     */
    private List<String> warnings;
    
    /**
     * 額外資訊
     */
    private AdditionalInfo additionalInfo;
    
    /**
     * 手續費明細
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeBreakdown {
        
        /**
         * 解款手續費 (台幣)
         */
        private BigDecimal remittanceFee;
        
        /**
         * 處理費 (台幣)
         */
        private BigDecimal processingFee;
        
        /**
         * 電報費 (台幣)
         */
        private BigDecimal telegraphicFee;
        
        /**
         * 郵費 (台幣)
         */
        private BigDecimal postageFee;
        
        /**
         * 其他費用 (台幣)
         */
        private BigDecimal otherFees;
        
        /**
         * 手續費總計 (台幣)
         */
        private BigDecimal totalFees;
        
        /**
         * 手續費計算方式
         */
        private String calculationMethod;
        
        /**
         * 客戶類型 (影響費率)
         */
        private String customerType;
        
        /**
         * 費用明細列表
         */
        private List<FeeDetail> feeDetails;
    }
    
    /**
     * 費用明細項目
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeDetail {
        
        /**
         * 費用類型
         */
        private String feeType;
        
        /**
         * 費用名稱
         */
        private String feeName;
        
        /**
         * 費用金額
         */
        private BigDecimal amount;
        
        /**
         * 費用幣別
         */
        private String currency;
        
        /**
         * 計算基準
         */
        private String calculationBasis;
        
        /**
         * 費率
         */
        private BigDecimal rate;
        
        /**
         * 最小費用
         */
        private BigDecimal minimumFee;
        
        /**
         * 最大費用
         */
        private BigDecimal maximumFee;
        
        /**
         * 費用說明
         */
        private String description;
    }
    
    /**
     * 額外資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalInfo {
        
        /**
         * 匯率趨勢 (UP/DOWN/STABLE)
         */
        private String rateTrend;
        
        /**
         * 與昨日匯率比較
         */
        private BigDecimal rateChange;
        
        /**
         * 匯率變動百分比
         */
        private BigDecimal rateChangePercentage;
        
        /**
         * 銀行公告匯率
         */
        private BigDecimal bankRate;
        
        /**
         * 優惠匯率 (如適用)
         */
        private BigDecimal preferentialRate;
        
        /**
         * 匯率優惠說明
         */
        private String preferentialRateDescription;
        
        /**
         * 預估到帳時間
         */
        private String estimatedArrivalTime;
        
        /**
         * 匯款限額資訊
         */
        private LimitInfo limitInfo;
        
        /**
         * 合規資訊
         */
        private ComplianceInfo complianceInfo;
    }
    
    /**
     * 限額資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitInfo {
        
        /**
         * 單筆最小金額
         */
        private BigDecimal minAmount;
        
        /**
         * 單筆最大金額
         */
        private BigDecimal maxAmount;
        
        /**
         * 日累計限額
         */
        private BigDecimal dailyLimit;
        
        /**
         * 月累計限額
         */
        private BigDecimal monthlyLimit;
        
        /**
         * 年累計限額
         */
        private BigDecimal yearlyLimit;
        
        /**
         * 已使用額度
         */
        private BigDecimal usedAmount;
        
        /**
         * 剩餘額度
         */
        private BigDecimal remainingAmount;
        
        /**
         * 限額重置時間
         */
        private LocalDateTime limitResetTime;
    }
    
    /**
     * 合規資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplianceInfo {
        
        /**
         * 是否需要申報
         */
        private Boolean requiresReporting;
        
        /**
         * 申報門檻金額
         */
        private BigDecimal reportingThreshold;
        
        /**
         * 風險等級
         */
        private String riskLevel;
        
        /**
         * 是否需要額外文件
         */
        private Boolean requiresDocuments;
        
        /**
         * 需要的文件類型
         */
        private List<String> requiredDocuments;
        
        /**
         * 合規檢查狀態
         */
        private String complianceStatus;
        
        /**
         * 合規備註
         */
        private String complianceNotes;
    }
    
    /**
     * 檢查是否為高風險金額
     */
    public boolean isHighRiskAmount() {
        return twdAmount != null && twdAmount.compareTo(new BigDecimal("500000")) >= 0;
    }
    
    /**
     * 檢查是否需要申報
     */
    public boolean requiresReporting() {
        return additionalInfo != null && 
               additionalInfo.getComplianceInfo() != null &&
               Boolean.TRUE.equals(additionalInfo.getComplianceInfo().getRequiresReporting());
    }
    
    /**
     * 取得匯率有效期限描述
     */
    public String getExchangeRateValidityDescription() {
        if (exchangeRateValidityMinutes == null) {
            return "匯率已失效";
        }
        
        if (exchangeRateValidityMinutes <= 0) {
            return "匯率已過期";
        }
        
        if (exchangeRateValidityMinutes < 60) {
            return String.format("匯率有效期限：%d分鐘", exchangeRateValidityMinutes);
        } else {
            int hours = exchangeRateValidityMinutes / 60;
            int minutes = exchangeRateValidityMinutes % 60;
            if (minutes == 0) {
                return String.format("匯率有效期限：%d小時", hours);
            } else {
                return String.format("匯率有效期限：%d小時%d分鐘", hours, minutes);
            }
        }
    }
    
    /**
     * 取得費用節省金額 (如果有優惠匯率)
     */
    public BigDecimal getSavingsAmount() {
        if (additionalInfo == null || additionalInfo.getBankRate() == null || exchangeRate == null) {
            return BigDecimal.ZERO;
        }
        
        if (originalAmount == null) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal bankAmount = originalAmount.multiply(additionalInfo.getBankRate());
        BigDecimal preferentialAmount = originalAmount.multiply(exchangeRate);
        
        return bankAmount.subtract(preferentialAmount);
    }
    
    /**
     * 檢查是否有匯率優惠
     */
    public boolean hasPreferentialRate() {
        return additionalInfo != null && 
               additionalInfo.getPreferentialRate() != null &&
               additionalInfo.getBankRate() != null &&
               additionalInfo.getPreferentialRate().compareTo(additionalInfo.getBankRate()) > 0;
    }
    
    /**
     * 取得手續費率
     */
    public BigDecimal getFeeRate() {
        if (fees == null || fees.getTotalFees() == null || twdAmount == null) {
            return BigDecimal.ZERO;
        }
        
        if (twdAmount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return fees.getTotalFees().divide(twdAmount, 4, java.math.RoundingMode.HALF_UP)
                   .multiply(new BigDecimal("100"));
    }
    
    /**
     * 檢查計算結果是否有效
     */
    public boolean isValid() {
        return originalAmount != null && 
               currency != null && 
               exchangeRate != null && 
               twdAmount != null && 
               fees != null && 
               netAmount != null &&
               calculationTime != null;
    }
    
    /**
     * 取得計算摘要
     */
    public String getCalculationSummary() {
        if (!isValid()) {
            return "計算結果無效";
        }
        
        return String.format("%s %s → TWD %s (匯率: %s, 手續費: TWD %s)", 
                currency, 
                originalAmount.toString(),
                netAmount.toString(),
                exchangeRate.toString(),
                fees.getTotalFees().toString());
    }
}