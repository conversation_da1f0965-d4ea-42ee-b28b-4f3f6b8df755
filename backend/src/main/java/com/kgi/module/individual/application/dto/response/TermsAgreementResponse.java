package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 條款同意響應DTO
 * 對應前端的TermsAgreementResponse介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TermsAgreementResponse {
    
    /**
     * 同意是否成功
     */
    private Boolean success;
    
    /**
     * 同意記錄ID
     */
    private String agreementId;
    
    /**
     * 處理訊息
     */
    private String message;
    
    /**
     * 同意的條款版本
     */
    private String agreedTermsVersion;
    
    /**
     * 同意時間
     */
    private LocalDateTime agreedAt;
    
    /**
     * 會話有效期
     */
    private LocalDateTime sessionExpiresAt;
    
    /**
     * 下一步指示
     */
    private String nextStepInstructions;
    
    /**
     * 條款同意狀態
     */
    private AgreementStatus status;
    
    /**
     * 後續需要完成的項目
     */
    private List<RequiredNextStep> requiredNextSteps;
    
    /**
     * 同意追蹤資訊
     */
    private AgreementTrackingInfo trackingInfo;
    
    /**
     * 錯誤訊息 (如果有)
     */
    private String errorMessage;
    
    /**
     * 錯誤代碼 (如果有)
     */
    private String errorCode;
    
    /**
     * 額外資訊
     */
    private AdditionalResponseInfo additionalInfo;
    
    /**
     * 條款同意狀態枚舉
     */
    public enum AgreementStatus {
        /** 同意成功 */
        AGREED("同意成功"),
        
        /** 同意失敗 */
        FAILED("同意失敗"),
        
        /** 條款版本已過期 */
        VERSION_EXPIRED("條款版本已過期"),
        
        /** 需要重新同意 */
        REQUIRES_RECONFIRMATION("需要重新同意"),
        
        /** 處理中 */
        PROCESSING("處理中");
        
        private final String description;
        
        AgreementStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 需要完成的下一步驟
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequiredNextStep {
        
        /**
         * 步驟代碼
         */
        private String stepCode;
        
        /**
         * 步驟名稱
         */
        private String stepName;
        
        /**
         * 步驟描述
         */
        private String description;
        
        /**
         * 是否為必需步驟
         */
        private Boolean isRequired;
        
        /**
         * 預計完成時間
         */
        private LocalDateTime estimatedCompletionTime;
        
        /**
         * 步驟狀態
         */
        private StepStatus status;
        
        /**
         * 步驟URL (如適用)
         */
        private String actionUrl;
        
        /**
         * 步驟優先級
         */
        private Integer priority;
    }
    
    /**
     * 步驟狀態枚舉
     */
    public enum StepStatus {
        /** 待完成 */
        PENDING("待完成"),
        
        /** 進行中 */
        IN_PROGRESS("進行中"),
        
        /** 已完成 */
        COMPLETED("已完成"),
        
        /** 跳過 */
        SKIPPED("跳過"),
        
        /** 失敗 */
        FAILED("失敗");
        
        private final String description;
        
        StepStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 同意追蹤資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgreementTrackingInfo {
        
        /**
         * 追蹤號碼
         */
        private String trackingNumber;
        
        /**
         * 使用者IP位址
         */
        private String ipAddress;
        
        /**
         * 瀏覽器資訊
         */
        private String browserInfo;
        
        /**
         * 裝置資訊
         */
        private String deviceInfo;
        
        /**
         * 地理位置 (如有)
         */
        private String location;
        
        /**
         * 同意方式
         */
        private String agreementMethod;
        
        /**
         * 會話ID
         */
        private String sessionId;
        
        /**
         * 數位指紋
         */
        private String digitalFingerprint;
        
        /**
         * 時間戳記
         */
        private LocalDateTime timestamp;
        
        /**
         * 驗證狀態
         */
        private String verificationStatus;
        
        /**
         * 過期時間
         */
        private LocalDateTime expiryDate;
        
        /**
         * 狀態
         */
        private String status;
        
        /**
         * 訊息
         */
        private String message;
        
        /**
         * 需要重新同意
         */
        private Boolean requiresNewAgreement;
    }
    
    /**
     * 額外響應資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalResponseInfo {
        
        /**
         * 條款有效期限
         */
        private LocalDateTime termsValidUntil;
        
        /**
         * 下次需要重新同意的時間
         */
        private LocalDateTime nextRenewalRequired;
        
        /**
         * 條款變更歷史
         */
        private List<TermsVersionHistory> versionHistory;
        
        /**
         * 相關法規資訊
         */
        private List<RegulatoryInfo> regulatoryInfo;
        
        /**
         * 客戶權利說明
         */
        private String customerRights;
        
        /**
         * 申訴管道資訊
         */
        private ComplaintChannelInfo complaintInfo;
        
        /**
         * 服務聯絡資訊
         */
        private ServiceContactInfo serviceContact;
        
        /**
         * 處理統計
         */
        private ProcessingStatistics processingStats;
    }
    
    /**
     * 條款版本歷史
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermsVersionHistory {
        
        /**
         * 版本號
         */
        private String version;
        
        /**
         * 版本名稱
         */
        private String versionName;
        
        /**
         * 生效時間
         */
        private LocalDateTime effectiveDate;
        
        /**
         * 失效時間
         */
        private LocalDateTime expiryDate;
        
        /**
         * 主要變更內容
         */
        private String majorChanges;
        
        /**
         * 版本狀態
         */
        private String status;
    }
    
    /**
     * 法規資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryInfo {
        
        /**
         * 法規名稱
         */
        private String regulationName;
        
        /**
         * 法規代碼
         */
        private String regulationCode;
        
        /**
         * 適用條文
         */
        private String applicableArticles;
        
        /**
         * 說明
         */
        private String description;
        
        /**
         * 參考連結
         */
        private String referenceUrl;
    }
    
    /**
     * 申訴管道資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplaintChannelInfo {
        
        /**
         * 申訴電話
         */
        private String complaintPhone;
        
        /**
         * 申訴信箱
         */
        private String complaintEmail;
        
        /**
         * 線上申訴表單
         */
        private String onlineFormUrl;
        
        /**
         * 服務時間
         */
        private String serviceHours;
        
        /**
         * 處理時效
         */
        private String processingTime;
    }
    
    /**
     * 服務聯絡資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceContactInfo {
        
        /**
         * 客服電話
         */
        private String servicePhone;
        
        /**
         * 客服信箱
         */
        private String serviceEmail;
        
        /**
         * 線上客服
         */
        private String onlineChatUrl;
        
        /**
         * 服務時間
         */
        private String serviceHours;
        
        /**
         * FAQ連結
         */
        private String faqUrl;
    }
    
    /**
     * 處理統計
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingStatistics {
        
        /**
         * 處理時間 (毫秒)
         */
        private Long processingTimeMs;
        
        /**
         * 資料庫查詢次數
         */
        private Integer dbQueryCount;
        
        /**
         * 快取命中率
         */
        private Double cacheHitRate;
        
        /**
         * 處理節點
         */
        private String processingNode;
        
        /**
         * 請求ID
         */
        private String requestId;
    }
    
    /**
     * 檢查是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) && AgreementStatus.AGREED.equals(status);
    }
    
    /**
     * 檢查是否需要重新同意
     */
    public boolean requiresReconfirmation() {
        return AgreementStatus.REQUIRES_RECONFIRMATION.equals(status) ||
               AgreementStatus.VERSION_EXPIRED.equals(status);
    }
    
    /**
     * 檢查是否有錯誤
     */
    public boolean hasError() {
        return errorMessage != null || errorCode != null || !isSuccess();
    }
    
    /**
     * 取得錯誤描述
     */
    public String getErrorDescription() {
        if (errorMessage != null) {
            return errorMessage;
        }
        
        if (status != null) {
            return status.getDescription();
        }
        
        return "未知錯誤";
    }
    
    /**
     * 檢查會話是否即將過期
     */
    public boolean isSessionNearExpiry() {
        if (sessionExpiresAt == null) {
            return false;
        }
        
        LocalDateTime warningTime = sessionExpiresAt.minusMinutes(5);
        return LocalDateTime.now().isAfter(warningTime);
    }
    
    /**
     * 取得會話剩餘時間 (分鐘)
     */
    public long getSessionRemainingMinutes() {
        if (sessionExpiresAt == null) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(sessionExpiresAt)) {
            return 0;
        }
        
        return java.time.Duration.between(now, sessionExpiresAt).toMinutes();
    }
    
    /**
     * 取得必要的下一步驟
     */
    public List<RequiredNextStep> getRequiredSteps() {
        if (requiredNextSteps == null) {
            return List.of();
        }
        
        return requiredNextSteps.stream()
                .filter(step -> Boolean.TRUE.equals(step.getIsRequired()))
                .filter(step -> !StepStatus.COMPLETED.equals(step.getStatus()))
                .sorted((a, b) -> Integer.compare(
                        a.getPriority() != null ? a.getPriority() : 999,
                        b.getPriority() != null ? b.getPriority() : 999
                ))
                .toList();
    }
    
    /**
     * 檢查是否可以繼續下一步
     */
    public boolean canProceedToNext() {
        return isSuccess() && getRequiredSteps().isEmpty();
    }
    
    /**
     * 取得回應摘要
     */
    public String getResponseSummary() {
        if (isSuccess()) {
            return String.format("條款同意成功 (版本: %s, 時間: %s)", 
                    agreedTermsVersion, 
                    agreedAt != null ? agreedAt.toString() : "未知");
        } else {
            return String.format("條款同意失敗: %s", getErrorDescription());
        }
    }
    
    /**
     * 取得追蹤摘要
     */
    public String getTrackingSummary() {
        if (trackingInfo == null) {
            return "無追蹤資訊";
        }
        
        return String.format("追蹤號碼: %s, 方式: %s, IP: %s", 
                trackingInfo.getTrackingNumber(),
                trackingInfo.getAgreementMethod() != null ? trackingInfo.getAgreementMethod() : "未知",
                trackingInfo.getIpAddress() != null ? 
                        maskIpAddress(trackingInfo.getIpAddress()) : "未知");
    }
    
    /**
     * 遮罩IP位址
     */
    private String maskIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.length() < 7) {
            return ipAddress;
        }
        
        String[] parts = ipAddress.split("\\.");
        if (parts.length == 4) {
            return parts[0] + "." + parts[1] + ".***." + parts[3];
        }
        
        return ipAddress.substring(0, 4) + "***" + ipAddress.substring(ipAddress.length() - 3);
    }
}