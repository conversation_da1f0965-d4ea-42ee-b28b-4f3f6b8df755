package com.kgi.module.individual.application.dto.response;

import com.kgi.module.individual.domain.model.IndividualRemittance;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 解款狀態響應DTO
 * 用於返回解款狀態查詢結果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RemittanceStatusResponse {
    
    /**
     * 解款編號
     */
    private String remittanceId;
    
    /**
     * 跨境平台參考編號
     */
    private String theirRefNo;
    
    /**
     * 匯款參考編號
     */
    private String remitRefNo;
    
    /**
     * 匯款人姓名
     */
    private String payerName;
    
    /**
     * 幣別
     */
    private String currency;
    
    /**
     * 匯款金額
     */
    private BigDecimal amount;
    
    /**
     * 台幣金額
     */
    private BigDecimal twdAmount;
    
    /**
     * 適用匯率
     */
    private BigDecimal exchangeRate;
    
    /**
     * 手續費
     */
    private BigDecimal fee;
    
    /**
     * 淨收款金額(扣除手續費)
     */
    private BigDecimal netAmount;
    
    /**
     * 收款人姓名
     */
    private String beneficiaryName;
    
    /**
     * 收款人身分證號(遮罩)
     */
    private String beneficiaryMaskedId;
    
    /**
     * 處理狀態
     */
    private IndividualRemittance.ProcessingStatus processingStatus;
    
    /**
     * 處理狀態描述
     */
    private String processingStatusDesc;
    
    /**
     * 驗證狀態
     */
    private IndividualRemittance.VerificationStatus verificationStatus;
    
    /**
     * 驗證狀態描述
     */
    private String verificationStatusDesc;
    
    /**
     * 業務狀態代碼
     */
    private String businessStatusCode;
    
    /**
     * 業務狀態描述
     */
    private String businessStatusDesc;
    
    /**
     * 是否緊急件
     */
    private Boolean isUrgent;
    
    /**
     * 是否需要補件
     */
    private Boolean requiresSupplement;
    
    /**
     * 補件截止時間
     */
    private LocalDateTime supplementDeadline;
    
    /**
     * 預計完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 實際完成時間
     */
    private LocalDateTime completedDate;
    
    /**
     * 建立時間
     */
    private LocalDateTime createdAt;
    
    /**
     * 最後更新時間
     */
    private LocalDateTime updatedAt;
    
    /**
     * 是否超時
     */
    private Boolean isOverdue;
    
    /**
     * 是否高風險金額
     */
    private Boolean isHighRisk;
    
    /**
     * 處理進度百分比
     */
    private Integer progressPercentage;
    
    /**
     * 下一步驟描述
     */
    private String nextStepDescription;
    
    /**
     * 是否可以取消
     */
    private Boolean canCancel;
    
    /**
     * 是否可以修改
     */
    private Boolean canModify;
    
    /**
     * 取得處理狀態描述
     */
    public String getProcessingStatusDesc() {
        if (processingStatus != null) {
            return processingStatus.getDescription();
        }
        return processingStatusDesc;
    }
    
    /**
     * 取得驗證狀態描述
     */
    public String getVerificationStatusDesc() {
        if (verificationStatus != null) {
            return verificationStatus.getDescription();
        }
        return verificationStatusDesc;
    }
    
    /**
     * 取得進度百分比
     */
    public Integer getProgressPercentage() {
        if (progressPercentage != null) {
            return progressPercentage;
        }
        
        // 根據業務狀態代碼計算進度
        if (businessStatusCode != null) {
            return calculateProgressByStatusCode(businessStatusCode);
        }
        
        // 根據處理狀態計算進度
        if (processingStatus != null) {
            switch (processingStatus) {
                case PENDING:
                    return 10;
                case PROCESSING:
                    return 50;
                case COMPLETED:
                    return 100;
                case FAILED:
                case CANCELLED:
                    return 0;
                default:
                    return 0;
            }
        }
        
        return 0;
    }
    
    /**
     * 取得下一步驟描述
     */
    public String getNextStepDescription() {
        if (nextStepDescription != null) {
            return nextStepDescription;
        }
        
        if (processingStatus == null) {
            return "狀態未知";
        }
        
        switch (processingStatus) {
            case PENDING:
                return Boolean.TRUE.equals(requiresSupplement) ? "等待補件" : "等待身分驗證";
            case PROCESSING:
                if (verificationStatus == IndividualRemittance.VerificationStatus.PENDING) {
                    return "進行身分驗證";
                } else {
                    return "處理解款作業";
                }
            case COMPLETED:
                return "解款已完成";
            case FAILED:
                return "處理失敗，請聯繫客服";
            case CANCELLED:
                return "解款已取消";
            default:
                return "請聯繫客服查詢";
        }
    }
    
    /**
     * 檢查是否可以取消
     */
    public Boolean getCanCancel() {
        if (canCancel != null) {
            return canCancel;
        }
        
        if (processingStatus == null) {
            return false;
        }
        
        return processingStatus == IndividualRemittance.ProcessingStatus.PENDING ||
               (processingStatus == IndividualRemittance.ProcessingStatus.PROCESSING && 
                verificationStatus == IndividualRemittance.VerificationStatus.PENDING);
    }
    
    /**
     * 檢查是否可以修改
     */
    public Boolean getCanModify() {
        if (canModify != null) {
            return canModify;
        }
        
        if (processingStatus == null) {
            return false;
        }
        
        return processingStatus == IndividualRemittance.ProcessingStatus.PENDING &&
               verificationStatus == IndividualRemittance.VerificationStatus.PENDING;
    }
    
    /**
     * 根據業務狀態代碼計算進度
     */
    private Integer calculateProgressByStatusCode(String statusCode) {
        try {
            int code = Integer.parseInt(statusCode);
            
            if (code <= 5) {
                return 10; // 初始階段
            } else if (code <= 15) {
                return 30; // 驗證階段
            } else if (code <= 25) {
                return 60; // 處理階段
            } else if (code <= 35) {
                return 90; // 完成階段
            } else if (code == 36) {
                return 100; // 完成
            } else {
                return 0; // 失敗或取消
            }
            
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 取得狀態顏色(用於前端顯示)
     */
    public String getStatusColor() {
        if (processingStatus == null) {
            return "gray";
        }
        
        switch (processingStatus) {
            case PENDING:
                return "orange";
            case PROCESSING:
                return "blue";
            case COMPLETED:
                return "green";
            case FAILED:
                return "red";
            case CANCELLED:
                return "gray";
            default:
                return "gray";
        }
    }
    
    /**
     * 檢查是否為最終狀態
     */
    public Boolean isFinalStatus() {
        if (processingStatus == null) {
            return false;
        }
        
        return processingStatus == IndividualRemittance.ProcessingStatus.COMPLETED ||
               processingStatus == IndividualRemittance.ProcessingStatus.FAILED ||
               processingStatus == IndividualRemittance.ProcessingStatus.CANCELLED;
    }
    
    /**
     * 取得狀態摘要
     */
    public String getStatusSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (processingStatus != null) {
            summary.append("處理狀態: ").append(getProcessingStatusDesc());
        }
        
        if (verificationStatus != null) {
            if (summary.length() > 0) {
                summary.append(", ");
            }
            summary.append("驗證狀態: ").append(getVerificationStatusDesc());
        }
        
        if (businessStatusDesc != null) {
            if (summary.length() > 0) {
                summary.append(", ");
            }
            summary.append("詳細狀態: ").append(businessStatusDesc);
        }
        
        return summary.toString();
    }
}