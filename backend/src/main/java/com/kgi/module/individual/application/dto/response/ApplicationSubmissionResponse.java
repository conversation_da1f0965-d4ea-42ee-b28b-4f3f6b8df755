package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 申請提交響應DTO
 * 對應前端的ApplicationSubmissionResponse介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationSubmissionResponse {
    
    /**
     * 提交是否成功
     */
    private Boolean success;
    
    /**
     * 申請編號
     */
    private String applicationId;
    
    /**
     * 申請狀態
     */
    private ApplicationStatus status;
    
    /**
     * 提交時間
     */
    private LocalDateTime submitTime;
    
    /**
     * 預計處理時間
     */
    private LocalDateTime estimatedProcessTime;
    
    /**
     * 追蹤號碼
     */
    private String trackingNumber;
    
    /**
     * 收據URL
     */
    private String receiptUrl;
    
    /**
     * 下一步指示
     */
    private List<String> nextSteps;
    
    /**
     * 聯繫資訊
     */
    private ContactInfo contactInfo;
    
    /**
     * 提交結果詳情
     */
    private SubmissionDetails submissionDetails;
    
    /**
     * 處理進度
     */
    private ProcessingProgress processingProgress;
    
    /**
     * 後續追蹤資訊
     */
    private FollowUpInfo followUpInfo;
    
    /**
     * 錯誤訊息 (如果有)
     */
    private String errorMessage;
    
    /**
     * 錯誤代碼 (如果有)
     */
    private String errorCode;
    
    /**
     * 申請狀態枚舉
     */
    public enum ApplicationStatus {
        /** 已接受 */
        ACCEPTED("已接受"),
        
        /** 已拒絕 */
        REJECTED("已拒絕"),
        
        /** 修改中 */
        UNDER_MODIFICATION("修改中"),
        
        /** 等待審核 */
        PENDING_APPROVAL("等待審核"),
        
        /** 處理完成 */
        COMPLETED("處理完成"),
        
        /** 處理失敗 */
        FAILED("處理失敗");
        
        private final String description;
        
        ApplicationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 聯繫資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        
        /**
         * 客服電話
         */
        private String phoneNumber;
        
        /**
         * 客服信箱
         */
        private String email;
        
        /**
         * 服務時間
         */
        private String serviceHours;
        
        /**
         * 線上客服連結
         */
        private String onlineChatUrl;
        
        /**
         * FAQ連結
         */
        private String faqUrl;
        
        /**
         * 緊急聯絡電話
         */
        private String emergencyPhone;
    }
    
    /**
     * 提交詳情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubmissionDetails {
        
        /**
         * 申請類型
         */
        private String applicationType;
        
        /**
         * 申請人身份
         */
        private String applicantType;
        
        /**
         * 匯款金額
         */
        private java.math.BigDecimal remittanceAmount;
        
        /**
         * 匯款幣別
         */
        private String currency;
        
        /**
         * 解款方式
         */
        private String paymentMethod;
        
        /**
         * 優先等級
         */
        private String priorityLevel;
        
        /**
         * 特殊標記
         */
        private List<String> specialFlags;
        
        /**
         * 風險等級
         */
        private String riskLevel;
        
        /**
         * 預計到帳時間
         */
        private LocalDateTime estimatedArrivalTime;
        
        /**
         * 手續費總額
         */
        private java.math.BigDecimal totalFees;
    }
    
    /**
     * 處理進度
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingProgress {
        
        /**
         * 目前進度百分比 (0-100)
         */
        private Integer progressPercentage;
        
        /**
         * 目前處理階段
         */
        private String currentStage;
        
        /**
         * 目前處理步驟描述
         */
        private String currentStep;
        
        /**
         * 預計完成時間
         */
        private LocalDateTime estimatedCompletion;
        
        /**
         * 下一步驟描述
         */
        private String nextStepDescription;
        
        /**
         * 處理階段列表
         */
        private List<ProcessingStage> stages;
        
        /**
         * 預計剩餘時間 (分鐘)
         */
        private Integer estimatedRemainingMinutes;
        
        /**
         * 最後更新時間
         */
        private LocalDateTime lastUpdated;
        
        /**
         * 更新者
         */
        private String updatedBy;
        
        /**
         * 更新說明
         */
        private String updateDescription;
    }
    
    /**
     * 處理階段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingStage {
        
        /**
         * 階段代碼
         */
        private String stageCode;
        
        /**
         * 階段名稱
         */
        private String stageName;
        
        /**
         * 階段描述
         */
        private String description;
        
        /**
         * 階段狀態
         */
        private StageStatus status;
        
        /**
         * 開始時間
         */
        private LocalDateTime startTime;
        
        /**
         * 完成時間
         */
        private LocalDateTime completionTime;
        
        /**
         * 預計完成時間
         */
        private LocalDateTime estimatedCompletionTime;
        
        /**
         * 階段順序
         */
        private Integer sequence;
        
        /**
         * 是否為關鍵階段
         */
        private Boolean isCritical;
        
        /**
         * 階段備註
         */
        private String remarks;
    }
    
    /**
     * 階段狀態枚舉
     */
    public enum StageStatus {
        /** 待開始 */
        PENDING("待開始"),
        
        /** 進行中 */
        IN_PROGRESS("進行中"),
        
        /** 已完成 */
        COMPLETED("已完成"),
        
        /** 跳過 */
        SKIPPED("跳過"),
        
        /** 失敗 */
        FAILED("失敗"),
        
        /** 暫停 */
        PAUSED("暫停");
        
        private final String description;
        
        StageStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 後續追蹤資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FollowUpInfo {
        
        /**
         * 追蹤號碼
         */
        private String trackingNumber;
        
        /**
         * 客服電話
         */
        private String contactPhone;
        
        /**
         * 客服信箱
         */
        private String serviceEmail;
        
        /**
         * 服務時間
         */
        private String serviceHours;
        
        /**
         * 狀態查詢URL
         */
        private String statusCheckUrl;
        
        /**
         * 推薦查詢時間間隔 (分鐘)
         */
        private Integer recommendedCheckIntervalMinutes;
        
        /**
         * 自動通知設定
         */
        private NotificationSettings notificationSettings;
        
        /**
         * 重要提醒事項
         */
        private List<String> importantReminders;
        
        /**
         * 可執行操作
         */
        private List<AvailableAction> availableActions;
        
        /**
         * 查詢憑證資訊
         */
        private QueryCredentials queryCredentials;
    }
    
    /**
     * 通知設定
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationSettings {
        
        /**
         * 簡訊通知
         */
        private Boolean smsEnabled;
        
        /**
         * 簡訊號碼
         */
        private String smsNumber;
        
        /**
         * 電子郵件通知
         */
        private Boolean emailEnabled;
        
        /**
         * 電子郵件地址
         */
        private String emailAddress;
        
        /**
         * 推播通知
         */
        private Boolean pushEnabled;
        
        /**
         * 通知時機
         */
        private List<String> notificationTriggers;
    }
    
    /**
     * 可執行操作
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AvailableAction {
        
        /**
         * 操作代碼
         */
        private String actionCode;
        
        /**
         * 操作名稱
         */
        private String actionName;
        
        /**
         * 操作描述
         */
        private String description;
        
        /**
         * 操作URL
         */
        private String actionUrl;
        
        /**
         * 是否可執行
         */
        private Boolean isEnabled;
        
        /**
         * 不可執行原因
         */
        private String disabledReason;
        
        /**
         * 操作類型
         */
        private ActionType actionType;
        
        /**
         * 操作期限
         */
        private LocalDateTime deadline;
        
        /**
         * 操作優先級
         */
        private Integer priority;
    }
    
    /**
     * 操作類型枚舉
     */
    public enum ActionType {
        /** 主要操作 */
        PRIMARY("主要操作"),
        
        /** 次要操作 */
        SECONDARY("次要操作"),
        
        /** 危險操作 */
        DANGER("危險操作"),
        
        /** 資訊操作 */
        INFO("資訊操作");
        
        private final String description;
        
        ActionType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 查詢憑證
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryCredentials {
        
        /**
         * 查詢代碼
         */
        private String queryCode;
        
        /**
         * 查詢密碼
         */
        private String queryPassword;
        
        /**
         * 憑證有效期
         */
        private LocalDateTime validUntil;
        
        /**
         * 使用說明
         */
        private String instructions;
        
        /**
         * 安全提醒
         */
        private String securityReminder;
    }
    
    /**
     * 檢查是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) && 
               (ApplicationStatus.ACCEPTED.equals(status) || 
                ApplicationStatus.PENDING_APPROVAL.equals(status));
    }
    
    /**
     * 檢查是否需要補件
     */
    public boolean requiresSupplement() {
        return ApplicationStatus.UNDER_MODIFICATION.equals(status);
    }
    
    /**
     * 檢查是否已完成
     */
    public boolean isCompleted() {
        return ApplicationStatus.COMPLETED.equals(status);
    }
    
    /**
     * 檢查是否失敗
     */
    public boolean isFailed() {
        return ApplicationStatus.FAILED.equals(status) || 
               ApplicationStatus.REJECTED.equals(status);
    }
    
    /**
     * 取得預計處理剩餘時間 (分鐘)
     */
    public long getEstimatedRemainingMinutes() {
        if (estimatedProcessTime == null) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(estimatedProcessTime)) {
            return 0;
        }
        
        return java.time.Duration.between(now, estimatedProcessTime).toMinutes();
    }
    
    /**
     * 取得目前處理進度描述
     */
    public String getProgressDescription() {
        if (processingProgress == null) {
            return "處理中";
        }
        
        return String.format("%s (%d%%)", 
                processingProgress.getCurrentStage() != null ? 
                        processingProgress.getCurrentStage() : "處理中",
                processingProgress.getProgressPercentage() != null ? 
                        processingProgress.getProgressPercentage() : 0);
    }
    
    /**
     * 取得重要操作列表
     */
    public List<AvailableAction> getImportantActions() {
        if (followUpInfo == null || followUpInfo.getAvailableActions() == null) {
            return List.of();
        }
        
        return followUpInfo.getAvailableActions().stream()
                .filter(action -> ActionType.PRIMARY.equals(action.getActionType()) ||
                                ActionType.DANGER.equals(action.getActionType()))
                .filter(action -> Boolean.TRUE.equals(action.getIsEnabled()))
                .sorted((a, b) -> Integer.compare(
                        a.getPriority() != null ? a.getPriority() : 999,
                        b.getPriority() != null ? b.getPriority() : 999
                ))
                .toList();
    }
    
    /**
     * 檢查是否可以查詢狀態
     */
    public boolean canCheckStatus() {
        return followUpInfo != null && 
               followUpInfo.getStatusCheckUrl() != null &&
               followUpInfo.getQueryCredentials() != null &&
               followUpInfo.getQueryCredentials().getQueryCode() != null;
    }
    
    /**
     * 取得提交摘要
     */
    public String getSubmissionSummary() {
        if (submissionDetails == null) {
            return String.format("申請 %s (追蹤號碼: %s)", 
                    applicationId, trackingNumber);
        }
        
        return String.format("申請 %s - %s %s (追蹤號碼: %s)", 
                applicationId,
                submissionDetails.getCurrency(),
                submissionDetails.getRemittanceAmount() != null ? 
                        submissionDetails.getRemittanceAmount().toString() : "未知金額",
                trackingNumber);
    }
    
    /**
     * 取得狀態說明
     */
    public String getStatusDescription() {
        if (isSuccess()) {
            return "申請提交成功，系統正在處理中";
        } else if (requiresSupplement()) {
            return "申請需要補件，請依指示提供相關文件";
        } else if (isFailed()) {
            return "申請處理失敗: " + (errorMessage != null ? errorMessage : "請聯繫客服");
        } else {
            return "申請處理中，請耐心等候";
        }
    }
}