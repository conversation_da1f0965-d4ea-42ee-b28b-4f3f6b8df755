package com.kgi.module.individual.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.individual.application.dto.response.RemittanceStatusResponse;
import com.kgi.module.individual.application.dto.response.IndividualRemittanceResponse;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 查詢解款狀態用例
 * 負責提供解款狀態查詢功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QueryRemittanceStatusUseCase {
    
    private final IndividualRemittanceRepository remittanceRepository;
    
    /**
     * 根據解款編號查詢狀態
     * 
     * @param remittanceId 解款編號
     * @return 解款狀態響應
     */
    @Transactional(readOnly = true)
    public RemittanceStatusResponse queryByRemittanceId(String remittanceId) {
        log.info("查詢解款狀態: remittanceId={}", remittanceId);
        
        try {
            IndividualRemittance remittance = remittanceRepository.findByRemittanceId(remittanceId)
                    .orElseThrow(() -> new BusinessException("REMITTANCE_NOT_FOUND", 
                            "解款記錄不存在: " + remittanceId));
            
            RemittanceStatusResponse response = convertToStatusResponse(remittance);
            
            log.debug("解款狀態查詢完成: remittanceId={}, status={}", 
                    remittanceId, response.getProcessingStatus());
            
            return response;
            
        } catch (BusinessException e) {
            log.error("解款狀態查詢業務異常: remittanceId={}, error={}", remittanceId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("解款狀態查詢系統異常: remittanceId={}", remittanceId, e);
            throw new BusinessException("QUERY_ERROR", "查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 根據跨境平台參考編號查詢狀態
     * 
     * @param theirRefNo 跨境平台參考編號
     * @return 解款狀態響應
     */
    @Transactional(readOnly = true)
    public RemittanceStatusResponse queryByTheirRefNo(String theirRefNo) {
        log.info("查詢解款狀態: theirRefNo={}", theirRefNo);
        
        try {
            IndividualRemittance remittance = remittanceRepository.findByTheirRefNo(theirRefNo)
                    .orElseThrow(() -> new BusinessException("REMITTANCE_NOT_FOUND", 
                            "解款記錄不存在: " + theirRefNo));
            
            RemittanceStatusResponse response = convertToStatusResponse(remittance);
            
            log.debug("解款狀態查詢完成: theirRefNo={}, status={}", 
                    theirRefNo, response.getProcessingStatus());
            
            return response;
            
        } catch (BusinessException e) {
            log.error("解款狀態查詢業務異常: theirRefNo={}, error={}", theirRefNo, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("解款狀態查詢系統異常: theirRefNo={}", theirRefNo, e);
            throw new BusinessException("QUERY_ERROR", "查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 根據身分證號搜尋解款記錄 (用於Controller)
     */
    @Transactional(readOnly = true)
    public Page<IndividualRemittanceResponse> searchByTaiwanId(String taiwanId, String startDate, 
                                                             String endDate, Pageable pageable) {
        log.info("搜尋解款記錄: taiwanId={}, startDate={}, endDate={}, page={}, size={}", 
                maskTaiwanId(taiwanId), startDate, endDate, pageable.getPageNumber(), pageable.getPageSize());
        
        try {
            // 轉換日期字串
            LocalDateTime startDateTime = parseDateTime(startDate, true);
            LocalDateTime endDateTime = parseDateTime(endDate, false);
            
            // 查詢解款記錄
            Page<IndividualRemittance> remittancePage = remittanceRepository.findByTaiwanIdAndDateRange(
                    taiwanId, startDateTime, endDateTime, pageable);
            
            // 轉換為Response
            List<IndividualRemittanceResponse> responseList = remittancePage.getContent().stream()
                    .map(this::convertToIndividualRemittanceResponse)
                    .collect(Collectors.toList());
            
            Page<IndividualRemittanceResponse> responsePage = new PageImpl<>(
                    responseList, pageable, remittancePage.getTotalElements());
            
            log.debug("搜尋結果: taiwanId={}, totalElements={}", 
                    maskTaiwanId(taiwanId), responsePage.getTotalElements());
            
            return responsePage;
            
        } catch (Exception e) {
            log.error("搜尋解款記錄異常: taiwanId={}, error={}", maskTaiwanId(taiwanId), e.getMessage(), e);
            throw new BusinessException("SEARCH_ERROR", "搜尋系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 根據身分證號查詢解款記錄 (原版本)
     * 
     * @param taiwanId 身分證號
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @param pageNumber 頁碼
     * @param pageSize 頁面大小
     * @return 解款狀態列表
     */
    @Transactional(readOnly = true)
    public RemittanceStatusListResponse queryByTaiwanId(String taiwanId, LocalDate startDate, 
                                                       LocalDate endDate, int pageNumber, int pageSize) {
        log.info("根據身分證查詢解款記錄: taiwanId={}, startDate={}, endDate={}, page={}, size={}", 
                maskTaiwanId(taiwanId), startDate, endDate, pageNumber, pageSize);
        
        try {
            // 參數驗證
            validateQueryParameters(taiwanId, startDate, endDate, pageNumber, pageSize);
            
            // 轉換為LocalDateTime
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
            
            // 建立分頁參數
            Pageable pageable = PageRequest.of(pageNumber, pageSize);
            
            // 查詢解款記錄
            Page<IndividualRemittance> remittancePage = remittanceRepository.findByTaiwanIdAndDateRange(
                    taiwanId, startDateTime, endDateTime, pageable);
            
            // 轉換為響應對象
            List<RemittanceStatusResponse> statusList = remittancePage.getContent().stream()
                    .map(this::convertToStatusResponse)
                    .collect(Collectors.toList());
            
            RemittanceStatusListResponse response = RemittanceStatusListResponse.builder()
                    .remittances(statusList)
                    .totalElements(remittancePage.getTotalElements())
                    .totalPages(remittancePage.getTotalPages())
                    .currentPage(pageNumber)
                    .pageSize(pageSize)
                    .hasNext(remittancePage.hasNext())
                    .hasPrevious(remittancePage.hasPrevious())
                    .build();
            
            log.debug("身分證查詢完成: taiwanId={}, totalElements={}", 
                    maskTaiwanId(taiwanId), response.getTotalElements());
            
            return response;
            
        } catch (BusinessException e) {
            log.error("身分證查詢業務異常: taiwanId={}, error={}", maskTaiwanId(taiwanId), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("身分證查詢系統異常: taiwanId={}", maskTaiwanId(taiwanId), e);
            throw new BusinessException("QUERY_ERROR", "查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢處理狀態統計
     * 
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 狀態統計
     */
    @Transactional(readOnly = true)
    public ProcessingStatusStatistics queryStatusStatistics(LocalDate startDate, LocalDate endDate) {
        log.info("查詢狀態統計: startDate={}, endDate={}", startDate, endDate);
        
        try {
            // 查詢狀態統計
            List<Object[]> statisticsData = remittanceRepository.countByStatusAndDateRange(startDate, endDate);
            
            // 轉換統計數據
            ProcessingStatusStatistics statistics = new ProcessingStatusStatistics();
            
            for (Object[] data : statisticsData) {
                String status = (String) data[0];
                Long count = (Long) data[1];
                
                switch (status) {
                    case "PENDING":
                        statistics.setPendingCount(count);
                        break;
                    case "PROCESSING":
                        statistics.setProcessingCount(count);
                        break;
                    case "COMPLETED":
                        statistics.setCompletedCount(count);
                        break;
                    case "FAILED":
                        statistics.setFailedCount(count);
                        break;
                    case "CANCELLED":
                        statistics.setCancelledCount(count);
                        break;
                    default:
                        log.warn("未知的處理狀態: {}", status);
                        break;
                }
            }
            
            statistics.setTotalCount(statistics.getPendingCount() + statistics.getProcessingCount() + 
                                   statistics.getCompletedCount() + statistics.getFailedCount() + 
                                   statistics.getCancelledCount());
            
            log.debug("狀態統計查詢完成: total={}", statistics.getTotalCount());
            return statistics;
            
        } catch (Exception e) {
            log.error("狀態統計查詢異常", e);
            throw new BusinessException("QUERY_ERROR", "統計查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢超時的解款記錄
     * 
     * @param pageNumber 頁碼
     * @param pageSize 頁面大小
     * @return 超時記錄列表
     */
    @Transactional(readOnly = true)
    public RemittanceStatusListResponse queryTimeoutRemittances(int pageNumber, int pageSize) {
        log.info("查詢超時解款記錄: page={}, size={}", pageNumber, pageSize);
        
        try {
            Pageable pageable = PageRequest.of(pageNumber, pageSize);
            
            List<IndividualRemittance> timeoutRemittances = remittanceRepository.findTimeoutRemittances(
                    LocalDateTime.now(), pageSize);
            
            List<RemittanceStatusResponse> statusList = timeoutRemittances.stream()
                    .map(this::convertToStatusResponse)
                    .collect(Collectors.toList());
            
            // 簡化的分頁響應(實際可能需要count查詢來獲取總數)
            RemittanceStatusListResponse response = RemittanceStatusListResponse.builder()
                    .remittances(statusList)
                    .totalElements((long) statusList.size())
                    .totalPages(statusList.size() > 0 ? (statusList.size() - 1) / pageSize + 1 : 0)
                    .currentPage(pageNumber)
                    .pageSize(pageSize)
                    .hasNext(statusList.size() == pageSize)
                    .hasPrevious(pageNumber > 0)
                    .build();
            
            log.debug("超時記錄查詢完成: count={}", statusList.size());
            return response;
            
        } catch (Exception e) {
            log.error("超時記錄查詢異常", e);
            throw new BusinessException("QUERY_ERROR", "超時記錄查詢系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 驗證查詢參數
     */
    private void validateQueryParameters(String taiwanId, LocalDate startDate, LocalDate endDate, 
                                        int pageNumber, int pageSize) {
        if (taiwanId == null || taiwanId.trim().isEmpty()) {
            throw new BusinessException("INVALID_PARAMETER", "身分證號不能為空");
        }
        
        if (startDate == null || endDate == null) {
            throw new BusinessException("INVALID_PARAMETER", "日期範圍不能為空");
        }
        
        if (startDate.isAfter(endDate)) {
            throw new BusinessException("INVALID_PARAMETER", "開始日期不能晚於結束日期");
        }
        
        if (startDate.isBefore(LocalDate.now().minusYears(1))) {
            throw new BusinessException("INVALID_PARAMETER", "查詢範圍不能超過一年");
        }
        
        if (pageNumber < 0) {
            throw new BusinessException("INVALID_PARAMETER", "頁碼不能為負數");
        }
        
        if (pageSize <= 0 || pageSize > 100) {
            throw new BusinessException("INVALID_PARAMETER", "頁面大小必須在1-100之間");
        }
    }
    
    /**
     * 轉換為IndividualRemittanceResponse
     */
    private IndividualRemittanceResponse convertToIndividualRemittanceResponse(IndividualRemittance remittance) {
        return IndividualRemittanceResponse.builder()
                .remittanceId(remittance.getRemittanceId())
                .theirRefNo(remittance.getTheirRefNo())
                .remitRefNo(remittance.getRemitRefNo())
                .payerName(remittance.getPayerName())
                .currency(remittance.getCurrency())
                .amount(remittance.getAmount())
                .twdAmount(remittance.getTwdAmount())
                .exchangeRate(remittance.getExchangeRate())
                .fee(remittance.getFee())
                .sourceOfFund(remittance.getSourceOfFund())
                .remittancePurpose(remittance.getRemittancePurpose())
                .isUrgent(remittance.getIsUrgent())
                .customerNote(remittance.getCustomerNote())
                .systemNote(remittance.getSystemNote())
                .processingStatus(remittance.getProcessingStatus().name())
                .verificationStatus(remittance.getVerificationStatus().name())
                .businessStatusCode(remittance.getBusinessStatusCode())
                .businessStatusDesc(remittance.getBusinessStatusDesc())
                .estimatedCompletionTime(remittance.getEstimatedCompletionTime())
                .completedDate(remittance.getCompletedDate())
                .requiresSupplement(remittance.getRequiresSupplement())
                .supplementDeadline(remittance.getSupplementDeadline())
                .createdAt(remittance.getCreatedAt())
                .updatedAt(remittance.getUpdatedAt())
                .build();
    }
    
    /**
     * 轉換為狀態響應對象
     */
    private RemittanceStatusResponse convertToStatusResponse(IndividualRemittance remittance) {
        return RemittanceStatusResponse.builder()
                .remittanceId(remittance.getRemittanceId())
                .theirRefNo(remittance.getTheirRefNo())
                .remitRefNo(remittance.getRemitRefNo())
                .payerName(remittance.getPayerName())
                .currency(remittance.getCurrency())
                .amount(remittance.getAmount())
                .twdAmount(remittance.getTwdAmount())
                .exchangeRate(remittance.getExchangeRate())
                .fee(remittance.getFee())
                .netAmount(remittance.getNetAmount())
                .beneficiaryName(remittance.getBeneficiary() != null ? 
                        remittance.getBeneficiary().getChineseName() : null)
                .beneficiaryMaskedId(remittance.getBeneficiaryMaskedId())
                .processingStatus(remittance.getProcessingStatus())
                .verificationStatus(remittance.getVerificationStatus())
                .businessStatusCode(remittance.getBusinessStatusCode())
                .businessStatusDesc(remittance.getBusinessStatusDesc())
                .isUrgent(remittance.getIsUrgent())
                .requiresSupplement(remittance.getRequiresSupplement())
                .supplementDeadline(remittance.getSupplementDeadline())
                .estimatedCompletionTime(remittance.getEstimatedCompletionTime())
                .completedDate(remittance.getCompletedDate())
                .createdAt(remittance.getCreatedAt())
                .updatedAt(remittance.getUpdatedAt())
                .isOverdue(remittance.isOverdue())
                .isHighRisk(remittance.isHighRiskAmount())
                .build();
    }
    
    /**
     * 解析日期時間字串
     */
    private LocalDateTime parseDateTime(String dateStr, boolean isStartOfDay) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            // 預設為一個月前到今天
            if (isStartOfDay) {
                return LocalDate.now().minusMonths(1).atStartOfDay();
            } else {
                return LocalDate.now().atTime(23, 59, 59);
            }
        }
        
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            return isStartOfDay ? date.atStartOfDay() : date.atTime(23, 59, 59);
        } catch (Exception e) {
            log.warn("日期格式錯誤，使用預設值: dateStr={}, error={}", dateStr, e.getMessage());
            if (isStartOfDay) {
                return LocalDate.now().minusMonths(1).atStartOfDay();
            } else {
                return LocalDate.now().atTime(23, 59, 59);
            }
        }
    }
    
    /**
     * 遮罩身分證號
     */
    private String maskTaiwanId(String taiwanId) {
        if (taiwanId == null || taiwanId.length() != 10) {
            return taiwanId;
        }
        return taiwanId.substring(0, 3) + "****" + taiwanId.substring(7);
    }
    
    // ==================== 響應類別 ====================
    
    /**
     * 解款狀態列表響應
     */
    @lombok.Data
    @lombok.Builder
    public static class RemittanceStatusListResponse {
        private List<RemittanceStatusResponse> remittances;
        private Long totalElements;
        private Integer totalPages;
        private Integer currentPage;
        private Integer pageSize;
        private Boolean hasNext;
        private Boolean hasPrevious;
    }
    
    /**
     * 處理狀態統計
     */
    @lombok.Data
    public static class ProcessingStatusStatistics {
        private Long pendingCount = 0L;
        private Long processingCount = 0L;
        private Long completedCount = 0L;
        private Long failedCount = 0L;
        private Long cancelledCount = 0L;
        private Long totalCount = 0L;
        
        /**
         * 取得完成率
         */
        public double getCompletionRate() {
            if (totalCount == 0) {
                return 0.0;
            }
            return (double) completedCount / totalCount * 100;
        }
        
        /**
         * 取得失敗率
         */
        public double getFailureRate() {
            if (totalCount == 0) {
                return 0.0;
            }
            return (double) failedCount / totalCount * 100;
        }
    }
}