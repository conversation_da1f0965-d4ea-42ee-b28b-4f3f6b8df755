package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 匯率查詢響應DTO
 * 對應前端的ExchangeRateResponse介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRateResponse {
    
    /**
     * 查詢是否成功
     */
    private Boolean success;
    
    /**
     * 基礎幣別 (通常是TWD)
     */
    private String baseCurrency;
    
    /**
     * 來源幣別
     */
    private String fromCurrency;
    
    /**
     * 目標幣別
     */
    private String targetCurrency;
    
    /**
     * 目標幣別 (別名)
     */
    private String toCurrency;
    
    /**
     * 匯率 (1單位目標幣別 = ? 基礎幣別)
     */
    private BigDecimal exchangeRate;
    
    /**
     * 買入匯率
     */
    private BigDecimal buyRate;
    
    /**
     * 賣出匯率
     */
    private BigDecimal sellRate;
    
    /**
     * 現金買入匯率
     */
    private BigDecimal cashBuyRate;
    
    /**
     * 現金賣出匯率
     */
    private BigDecimal cashSellRate;
    
    /**
     * 價差
     */
    private BigDecimal spread;
    
    /**
     * 匯率類型
     */
    private RateType rateType;
    
    /**
     * 匯率來源
     */
    private String rateSource;
    
    /**
     * 匯率時間
     */
    private LocalDateTime rateTime;
    
    /**
     * 報價時間
     */
    private LocalDateTime quotationTime;
    
    /**
     * 有效期限
     */
    private LocalDateTime validUntil;
    
    /**
     * 匯率趨勢
     */
    private RateTrend trend;
    
    /**
     * 歷史匯率資訊
     */
    private List<HistoricalRate> historicalRates;
    
    /**
     * 匯率詳細資訊
     */
    private RateDetails rateDetails;
    
    /**
     * 市場資訊
     */
    private MarketInfo marketInfo;
    
    /**
     * 風險警告
     */
    private List<String> riskWarnings;
    
    /**
     * 錯誤訊息 (如果有)
     */
    private String errorMessage;
    
    /**
     * 錯誤代碼 (如果有)
     */
    private String errorCode;
    
    /**
     * 查詢時間
     */
    private LocalDateTime queryTime;
    
    /**
     * 匯率類型枚舉
     */
    public enum RateType {
        /** 即期匯率 */
        SPOT("即期匯率"),
        
        /** 遠期匯率 */
        FORWARD("遠期匯率"),
        
        /** 現鈔匯率 */
        CASH("現鈔匯率"),
        
        /** 即時匯率 */
        REAL_TIME("即時匯率"),
        
        /** 優惠匯率 */
        PREFERENTIAL("優惠匯率"),
        
        /** 歷史匯率 */
        HISTORICAL("歷史匯率");
        
        private final String description;
        
        RateType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 匯率趨勢枚舉
     */
    public enum RateTrend {
        /** 上升 */
        UP("上升"),
        
        /** 下降 */
        DOWN("下降"),
        
        /** 平穩 */
        STABLE("平穩"),
        
        /** 波動 */
        VOLATILE("波動"),
        
        /** 未知 */
        UNKNOWN("未知");
        
        private final String description;
        
        RateTrend(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 歷史匯率
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalRate {
        
        /**
         * 日期
         */
        private LocalDateTime date;
        
        /**
         * 開盤匯率
         */
        private BigDecimal openRate;
        
        /**
         * 收盤匯率
         */
        private BigDecimal closeRate;
        
        /**
         * 最高匯率
         */
        private BigDecimal highRate;
        
        /**
         * 最低匯率
         */
        private BigDecimal lowRate;
        
        /**
         * 平均匯率
         */
        private BigDecimal averageRate;
        
        /**
         * 交易量
         */
        private BigDecimal volume;
        
        /**
         * 變動幅度
         */
        private BigDecimal changeAmount;
        
        /**
         * 變動百分比
         */
        private BigDecimal changePercentage;
    }
    
    /**
     * 匯率詳細資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateDetails {
        
        /**
         * 報價銀行
         */
        private String quotingBank;
        
        /**
         * 報價時間
         */
        private LocalDateTime quotingTime;
        
        /**
         * 更新頻率 (分鐘)
         */
        private Integer updateFrequencyMinutes;
        
        /**
         * 點差 (買賣差價)
         */
        private BigDecimal spread;
        
        /**
         * 流動性評級
         */
        private LiquidityRating liquidityRating;
        
        /**
         * 波動率
         */
        private BigDecimal volatility;
        
        /**
         * 52週最高
         */
        private BigDecimal yearHigh;
        
        /**
         * 52週最低
         */
        private BigDecimal yearLow;
        
        /**
         * 基準利率差
         */
        private BigDecimal interestRateDifferential;
        
        /**
         * 購買力平價
         */
        private BigDecimal purchasingPowerParity;
        
        /**
         * 技術指標
         */
        private TechnicalIndicators technicalIndicators;
    }
    
    /**
     * 流動性評級枚舉
     */
    public enum LiquidityRating {
        /** 極高 */
        VERY_HIGH("極高"),
        
        /** 高 */
        HIGH("高"),
        
        /** 中等 */
        MEDIUM("中等"),
        
        /** 低 */
        LOW("低"),
        
        /** 極低 */
        VERY_LOW("極低");
        
        private final String description;
        
        LiquidityRating(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 技術指標
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalIndicators {
        
        /**
         * 移動平均線 (5日)
         */
        private BigDecimal movingAverage5;
        
        /**
         * 移動平均線 (20日)
         */
        private BigDecimal movingAverage20;
        
        /**
         * 移動平均線 (50日)
         */
        private BigDecimal movingAverage50;
        
        /**
         * RSI指標
         */
        private BigDecimal rsi;
        
        /**
         * MACD指標
         */
        private BigDecimal macd;
        
        /**
         * 布林通道上軌
         */
        private BigDecimal bollingerUpper;
        
        /**
         * 布林通道下軌
         */
        private BigDecimal bollingerLower;
        
        /**
         * 支撐位
         */
        private BigDecimal supportLevel;
        
        /**
         * 阻力位
         */
        private BigDecimal resistanceLevel;
    }
    
    /**
     * 市場資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketInfo {
        
        /**
         * 市場狀態
         */
        private MarketStatus marketStatus;
        
        /**
         * 開市時間
         */
        private String marketOpenTime;
        
        /**
         * 閉市時間
         */
        private String marketCloseTime;
        
        /**
         * 交易日
         */
        private Boolean isTradingDay;
        
        /**
         * 主要影響因素
         */
        private List<String> majorFactors;
        
        /**
         * 經濟事件
         */
        private List<EconomicEvent> economicEvents;
        
        /**
         * 央行政策
         */
        private String centralBankPolicy;
        
        /**
         * 市場情緒
         */
        private MarketSentiment marketSentiment;
        
        /**
         * 預測方向
         */
        private ForecastDirection forecastDirection;
    }
    
    /**
     * 市場狀態枚舉
     */
    public enum MarketStatus {
        /** 開市 */
        OPEN("開市"),
        
        /** 閉市 */
        CLOSED("閉市"),
        
        /** 盤前 */
        PRE_MARKET("盤前"),
        
        /** 盤後 */
        AFTER_MARKET("盤後"),
        
        /** 暫停 */
        SUSPENDED("暫停");
        
        private final String description;
        
        MarketStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 經濟事件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EconomicEvent {
        
        /**
         * 事件名稱
         */
        private String eventName;
        
        /**
         * 事件時間
         */
        private LocalDateTime eventTime;
        
        /**
         * 重要性級別
         */
        private ImportanceLevel importance;
        
        /**
         * 預期值
         */
        private String expectedValue;
        
        /**
         * 前值
         */
        private String previousValue;
        
        /**
         * 實際值
         */
        private String actualValue;
        
        /**
         * 影響說明
         */
        private String impact;
    }
    
    /**
     * 重要性級別枚舉
     */
    public enum ImportanceLevel {
        /** 高 */
        HIGH("高"),
        
        /** 中 */
        MEDIUM("中"),
        
        /** 低 */
        LOW("低");
        
        private final String description;
        
        ImportanceLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 市場情緒枚舉
     */
    public enum MarketSentiment {
        /** 樂觀 */
        BULLISH("樂觀"),
        
        /** 悲觀 */
        BEARISH("悲觀"),
        
        /** 中性 */
        NEUTRAL("中性"),
        
        /** 謹慎 */
        CAUTIOUS("謹慎"),
        
        /** 恐慌 */
        PANIC("恐慌");
        
        private final String description;
        
        MarketSentiment(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 預測方向枚舉
     */
    public enum ForecastDirection {
        /** 看漲 */
        BULLISH("看漲"),
        
        /** 看跌 */
        BEARISH("看跌"),
        
        /** 橫盤 */
        SIDEWAYS("橫盤"),
        
        /** 不確定 */
        UNCERTAIN("不確定");
        
        private final String description;
        
        ForecastDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 檢查匯率是否有效
     */
    public boolean isRateValid() {
        return Boolean.TRUE.equals(success) && 
               exchangeRate != null && 
               exchangeRate.compareTo(BigDecimal.ZERO) > 0 &&
               (validUntil == null || LocalDateTime.now().isBefore(validUntil));
    }
    
    /**
     * 檢查匯率是否即將過期
     */
    public boolean isRateNearExpiry() {
        if (validUntil == null) {
            return false;
        }
        
        LocalDateTime warningTime = validUntil.minusMinutes(5);
        return LocalDateTime.now().isAfter(warningTime);
    }
    
    /**
     * 取得匯率剩餘有效時間 (分鐘)
     */
    public long getRemainingValidityMinutes() {
        if (validUntil == null) {
            return Long.MAX_VALUE;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(validUntil)) {
            return 0;
        }
        
        return java.time.Duration.between(now, validUntil).toMinutes();
    }
    
    /**
     * 計算匯兌金額
     */
    public BigDecimal calculateExchangeAmount(BigDecimal sourceAmount) {
        if (!isRateValid() || sourceAmount == null) {
            return BigDecimal.ZERO;
        }
        
        return sourceAmount.multiply(exchangeRate);
    }
    
    /**
     * 取得最優匯率
     */
    public BigDecimal getBestRate(boolean isBuying) {
        if (isBuying) {
            // 客戶買入外幣，使用銀行賣出匯率
            return sellRate != null ? sellRate : exchangeRate;
        } else {
            // 客戶賣出外幣，使用銀行買入匯率
            return buyRate != null ? buyRate : exchangeRate;
        }
    }
    
    /**
     * 計算點差
     */
    public BigDecimal calculateSpread() {
        if (buyRate != null && sellRate != null) {
            return sellRate.subtract(buyRate);
        }
        
        if (rateDetails != null && rateDetails.getSpread() != null) {
            return rateDetails.getSpread();
        }
        
        return BigDecimal.ZERO;
    }
    
    /**
     * 檢查是否為主要貨幣對
     */
    public boolean isMajorCurrencyPair() {
        String pair = targetCurrency + baseCurrency;
        List<String> majorPairs = List.of(
                "USDTWD", "EURTWD", "JPYTWD", "GBPTWD", 
                "AUDTWD", "CADTWD", "CHFTWD", "HKDTWD",
                "SGDTWD", "NZDTWD", "SEKTWD", "DKKTWD"
        );
        
        return majorPairs.contains(pair);
    }
    
    /**
     * 取得趨勢描述
     */
    public String getTrendDescription() {
        if (trend == null) {
            return "趨勢未知";
        }
        
        String baseDescription = trend.getDescription();
        
        if (historicalRates != null && historicalRates.size() >= 2) {
            HistoricalRate latest = historicalRates.get(0);
            HistoricalRate previous = historicalRates.get(1);
            
            if (latest.getChangePercentage() != null) {
                return String.format("%s (%.2f%%)", 
                        baseDescription, latest.getChangePercentage());
            }
        }
        
        return baseDescription;
    }
    
    /**
     * 檢查是否有風險警告
     */
    public boolean hasRiskWarnings() {
        return riskWarnings != null && !riskWarnings.isEmpty();
    }
    
    /**
     * 取得匯率摘要
     */
    public String getRateSummary() {
        if (!isRateValid()) {
            return "無效匯率";
        }
        
        return String.format("%s/%s: %s (%s)", 
                targetCurrency, baseCurrency, 
                exchangeRate.toString(),
                rateType != null ? rateType.getDescription() : "一般匯率");
    }
    
    /**
     * 檢查查詢是否成功
     */
    public boolean isQuerySuccessful() {
        return Boolean.TRUE.equals(success) && errorCode == null && errorMessage == null;
    }
    
    /**
     * 取得錯誤描述
     */
    public String getErrorDescription() {
        if (errorMessage != null) {
            return errorMessage;
        }
        
        if (errorCode != null) {
            return "錯誤代碼: " + errorCode;
        }
        
        if (!isQuerySuccessful()) {
            return "匯率查詢失敗";
        }
        
        return null;
    }
    
    /**
     * 取得市場狀態描述
     */
    public String getMarketStatusDescription() {
        if (marketInfo == null || marketInfo.getMarketStatus() == null) {
            return "市場狀態未知";
        }
        
        String status = marketInfo.getMarketStatus().getDescription();
        
        if (Boolean.FALSE.equals(marketInfo.getIsTradingDay())) {
            status += " (非交易日)";
        }
        
        return status;
    }
}