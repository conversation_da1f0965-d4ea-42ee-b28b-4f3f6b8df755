package com.kgi.module.individual.domain.model;

import com.kgi.core.domain.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 自然人解款領域實體
 * 
 * 本實體包含自然人跨境解款的完整資訊，包括：
 * - 基本解款資訊 (金額、幣別、參考編號等)
 * - 收款人資訊 (個人資料、銀行帳戶)
 * - 處理狀態追蹤 (處理狀態、驗證狀態、業務狀態)
 * - 時間戳記 (建立時間、完成時間、預計完成時間)
 * - 補件相關資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class IndividualRemittance extends BaseEntity {
    
    // ==================== 基本解款資訊 ====================
    
    /**
     * 系統解款編號 (內部生成的唯一識別碼)
     */
    private String remittanceId;
    
    /**
     * 跨境平台參考編號 (外部系統提供的參考編號)
     */
    private String theirRefNo;
    
    /**
     * 匯入匯款編號 (SWIFT或其他國際匯款系統的編號)
     */
    private String remitRefNo;
    
    /**
     * 匯款人姓名
     */
    private String payerName;
    
    /**
     * 匯款幣別 (ISO 4217標準: USD, EUR, JPY等)
     */
    private String currency;
    
    /**
     * 原始匯款金額 (外幣金額)
     */
    private BigDecimal amount;
    
    /**
     * 台幣換算金額 (依當時匯率換算)
     */
    private BigDecimal twdAmount;
    
    /**
     * 適用匯率
     */
    private BigDecimal exchangeRate;
    
    /**
     * 手續費總額 (台幣)
     */
    private BigDecimal fee;
    
    // ==================== 收款人資訊 ====================
    
    /**
     * 收款人個人資訊 (包含身分證號、姓名、聯絡方式、銀行帳戶)
     */
    private PersonalInfo beneficiary;
    
    // ==================== 業務相關資訊 ====================
    
    /**
     * 資金來源代碼 (依中央銀行規定的資金來源分類代碼)
     */
    private String sourceOfFund;
    
    /**
     * 匯款性質代碼 (貿易性質、非貿易性質等)
     */
    private String remittancePurpose;
    
    /**
     * 緊急處理標示
     */
    @Builder.Default
    private Boolean isUrgent = false;
    
    /**
     * 客戶備註
     */
    private String customerNote;
    
    /**
     * 系統處理備註
     */
    private String systemNote;
    
    // ==================== 狀態追蹤 ====================
    
    /**
     * 處理狀態 (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED)
     */
    private ProcessingStatus processingStatus;
    
    /**
     * 身分驗證狀態 (PENDING, VERIFIED, FAILED)
     */
    private VerificationStatus verificationStatus;
    
    /**
     * 業務狀態代碼 (01-36的詳細業務狀態)
     */
    private String businessStatusCode;
    
    /**
     * 業務狀態描述
     */
    private String businessStatusDesc;
    
    // ==================== 時間追蹤 ====================
    
    /**
     * 預計完成時間 (依據緊急程度和業務規則計算)
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 實際完成時間
     */
    private LocalDateTime completedDate;
    
    // ==================== 補件相關 ====================
    
    /**
     * 是否需要補件
     */
    @Builder.Default
    private Boolean requiresSupplement = false;
    
    /**
     * 補件截止時間
     */
    private LocalDateTime supplementDeadline;
    
    // ==================== 版本控制 ====================
    
    // 注意：版本號繼承自BaseEntity，不需要重複定義
    
    // ==================== 業務方法 ====================
    
    /**
     * 檢查是否為緊急件
     */
    public boolean isUrgentRemittance() {
        return Boolean.TRUE.equals(isUrgent);
    }
    
    /**
     * 檢查是否已完成處理
     */
    public boolean isCompleted() {
        return ProcessingStatus.COMPLETED.equals(processingStatus);
    }
    
    /**
     * 檢查是否處理失敗
     */
    public boolean isFailed() {
        return ProcessingStatus.FAILED.equals(processingStatus);
    }
    
    /**
     * 檢查是否已取消
     */
    public boolean isCancelled() {
        return ProcessingStatus.CANCELLED.equals(processingStatus);
    }
    
    /**
     * 檢查是否需要身分驗證
     */
    public boolean needsVerification() {
        return VerificationStatus.PENDING.equals(verificationStatus);
    }
    
    /**
     * 檢查是否已通過身分驗證
     */
    public boolean isVerified() {
        return VerificationStatus.VERIFIED.equals(verificationStatus);
    }
    
    /**
     * 檢查是否已超過預計完成時間
     */
    public boolean isOverdue() {
        return estimatedCompletionTime != null && 
               LocalDateTime.now().isAfter(estimatedCompletionTime) &&
               !isCompleted();
    }
    
    /**
     * 檢查是否需要補件且已過期
     */
    public boolean isSupplementOverdue() {
        return Boolean.TRUE.equals(requiresSupplement) &&
               supplementDeadline != null &&
               LocalDateTime.now().isAfter(supplementDeadline);
    }
    
    /**
     * 完成身分驗證
     */
    public void completeVerification() {
        this.verificationStatus = VerificationStatus.VERIFIED;
        updateBusinessStatus("12", "身分驗證完成");
    }
    
    /**
     * 設定需要補件
     */
    public void requireSupplement(LocalDateTime deadline, String reason) {
        this.requiresSupplement = true;
        this.supplementDeadline = deadline;
        this.systemNote = (systemNote == null ? "" : systemNote + "; ") + "需要補件: " + reason;
        updateBusinessStatus("25", "待補件");
    }
    
    /**
     * 完成補件
     */
    public void completeSupplement() {
        this.requiresSupplement = false;
        this.supplementDeadline = null;
        updateBusinessStatus("26", "補件完成");
    }
    
    /**
     * 開始處理
     */
    public void startProcessing() {
        this.processingStatus = ProcessingStatus.PROCESSING;
        updateBusinessStatus("15", "處理中");
    }
    
    /**
     * 完成解款
     */
    public void complete() {
        this.processingStatus = ProcessingStatus.COMPLETED;
        this.completedDate = LocalDateTime.now();
        updateBusinessStatus("36", "解款完成");
    }
    
    /**
     * 標記失敗
     */
    public void markAsFailed(String reason) {
        this.processingStatus = ProcessingStatus.FAILED;
        this.verificationStatus = VerificationStatus.FAILED;
        this.systemNote = (systemNote == null ? "" : systemNote + "; ") + "失敗原因: " + reason;
        updateBusinessStatus("99", "處理失敗");
    }
    
    /**
     * 取消解款
     */
    public void cancel(String reason) {
        this.processingStatus = ProcessingStatus.CANCELLED;
        this.systemNote = (systemNote == null ? "" : systemNote + "; ") + "取消原因: " + reason;
        updateBusinessStatus("98", "已取消");
    }
    
    /**
     * 更新業務狀態
     */
    public void updateBusinessStatus(String statusCode, String statusDesc) {
        this.businessStatusCode = statusCode;
        this.businessStatusDesc = statusDesc;
    }
    
    /**
     * 取得收款人身分證號遮罩
     */
    public String getBeneficiaryMaskedId() {
        return beneficiary != null ? beneficiary.getMaskedId() : null;
    }
    
    /**
     * 取得淨解款金額 (扣除手續費後)
     */
    public BigDecimal getNetAmount() {
        if (twdAmount == null || fee == null) {
            return twdAmount;
        }
        return twdAmount.subtract(fee);
    }
    
    /**
     * 檢查金額是否為高風險 (>= 50萬台幣)
     */
    public boolean isHighRiskAmount() {
        return twdAmount != null && 
               twdAmount.compareTo(new BigDecimal("500000")) >= 0;
    }
    
    // ==================== 狀態枚舉 ====================
    
    /**
     * 處理狀態枚舉
     */
    public enum ProcessingStatus {
        PENDING("待處理"),
        PROCESSING("處理中"), 
        COMPLETED("已完成"),
        FAILED("處理失敗"),
        CANCELLED("已取消");
        
        private final String description;
        
        ProcessingStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 身分驗證狀態枚舉
     */
    public enum VerificationStatus {
        PENDING("待驗證"),
        VERIFIED("已驗證"),
        FAILED("驗證失敗");
        
        private final String description;
        
        VerificationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}