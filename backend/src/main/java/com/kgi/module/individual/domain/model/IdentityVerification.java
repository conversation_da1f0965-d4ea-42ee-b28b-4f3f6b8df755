package com.kgi.module.individual.domain.model;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 身份驗證資料模型
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
public class IdentityVerification {
    
    // 基本資料
    private String chineseName;      // 中文姓名
    private String englishName;      // 英文姓名
    private String idNumber;         // 身分證號/居留證號
    private String birthDate;        // 出生日期 (yyyyMMdd)
    private String mobile;           // 手機號碼
    
    // 銀行資料
    private String bankCode;         // 銀行代碼
    private String bankAccount;      // 銀行帳號
    
    // 驗證資料
    private String verificationType; // 驗證類型 (OTP/P-CODE)
    private boolean pcodeVerified;   // P-code 驗證結果
    private boolean otpVerified;     // OTP 驗證結果
    
    // 外籍人士資料
    private boolean isForeigner;     // 是否為外籍人士
    private String residencePermitNo; // 居留證號碼
    private String residenceDateBegin; // 居留期限起始日
    private String residenceDateEnd;   // 居留期限結束日
    
    // 驗證時間
    private LocalDateTime verificationTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}