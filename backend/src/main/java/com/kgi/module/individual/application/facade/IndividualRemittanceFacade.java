package com.kgi.module.individual.application.facade;

import com.kgi.core.application.dto.DigitalRemittanceRequest;
import com.kgi.module.individual.application.dto.request.IndividualRemittanceRequest;
import com.kgi.module.individual.application.dto.request.OtpVerificationRequest;
import com.kgi.module.individual.application.dto.response.IndividualRemittanceResponse;
import com.kgi.module.individual.application.dto.response.RemittanceStatusResponse;

/**
 * 自然人解款門面介面
 * 提供自然人解款相關的統一入口
 */
public interface IndividualRemittanceFacade {
    
    /**
     * 處理自然人解款申請
     * 
     * @param request 數位解款請求
     * @return 解款處理響應
     */
    IndividualRemittanceResponse processRemittance(DigitalRemittanceRequest request);
    
    /**
     * 處理自然人解款申請(使用專用請求格式)
     * 
     * @param request 自然人解款請求
     * @return 解款處理響應
     */
    IndividualRemittanceResponse processIndividualRemittance(IndividualRemittanceRequest request);
    
    /**
     * 驗證OTP
     * 
     * @param request OTP驗證請求
     * @return 驗證結果
     */
    boolean verifyOtp(OtpVerificationRequest request);
    
    /**
     * 查詢解款狀態
     * 
     * @param remittanceId 解款編號
     * @return 解款狀態響應
     */
    RemittanceStatusResponse getRemittanceStatus(String remittanceId);
    
    /**
     * 根據跨境平台參考編號查詢解款狀態
     * 
     * @param theirRefNo 跨境平台參考編號
     * @return 解款狀態響應
     */
    RemittanceStatusResponse getRemittanceStatusByTheirRefNo(String theirRefNo);
    
    /**
     * 重新發送OTP
     * 
     * @param remittanceId 解款編號
     * @param phoneNumber 手機號碼
     * @return 發送結果
     */
    boolean resendOtp(String remittanceId, String phoneNumber);
    
    /**
     * 取消解款申請
     * 
     * @param remittanceId 解款編號
     * @param reason 取消原因
     * @return 取消結果
     */
    boolean cancelRemittance(String remittanceId, String reason);
    
    /**
     * 查詢匯率和手續費試算
     * 
     * @param currency 幣別
     * @param amount 金額
     * @param customerType 客戶類型
     * @return 試算結果
     */
    RemittanceCalculationResponse calculateRemittance(String currency, 
                                                    java.math.BigDecimal amount, 
                                                    String customerType);
    
    /**
     * 驗證收款人銀行帳戶
     * 
     * @param bankCode 銀行代碼
     * @param accountNumber 帳戶號碼
     * @param accountName 帳戶名稱
     * @return 驗證結果
     */
    BankAccountVerificationResponse verifyBankAccount(String bankCode, 
                                                     String accountNumber, 
                                                     String accountName);
    
    /**
     * 查詢個人解款歷史記錄
     * 
     * @param taiwanId 身分證號
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @param pageNumber 頁碼
     * @param pageSize 每頁數量
     * @return 解款歷史列表
     */
    RemittanceHistoryResponse getRemittanceHistory(String taiwanId,
                                                  java.time.LocalDate startDate,
                                                  java.time.LocalDate endDate,
                                                  int pageNumber,
                                                  int pageSize);
    
    /**
     * 試算響應內部類別
     */
    class RemittanceCalculationResponse {
        private java.math.BigDecimal exchangeRate;
        private java.math.BigDecimal convertedAmount;
        private java.math.BigDecimal totalFees;
        private java.math.BigDecimal netAmount;
        private java.time.LocalDateTime validUntil;
        private String rateSource;
        
        // Getters and Setters
        public java.math.BigDecimal getExchangeRate() { return exchangeRate; }
        public void setExchangeRate(java.math.BigDecimal exchangeRate) { this.exchangeRate = exchangeRate; }
        
        public java.math.BigDecimal getConvertedAmount() { return convertedAmount; }
        public void setConvertedAmount(java.math.BigDecimal convertedAmount) { this.convertedAmount = convertedAmount; }
        
        public java.math.BigDecimal getTotalFees() { return totalFees; }
        public void setTotalFees(java.math.BigDecimal totalFees) { this.totalFees = totalFees; }
        
        public java.math.BigDecimal getNetAmount() { return netAmount; }
        public void setNetAmount(java.math.BigDecimal netAmount) { this.netAmount = netAmount; }
        
        public java.time.LocalDateTime getValidUntil() { return validUntil; }
        public void setValidUntil(java.time.LocalDateTime validUntil) { this.validUntil = validUntil; }
        
        public String getRateSource() { return rateSource; }
        public void setRateSource(String rateSource) { this.rateSource = rateSource; }
    }
    
    /**
     * 銀行帳戶驗證響應內部類別
     */
    class BankAccountVerificationResponse {
        private boolean valid;
        private String bankName;
        private String branchName;
        private String accountHolderName;
        private String verificationResult;
        private String errorMessage;
        
        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }
        
        public String getBankName() { return bankName; }
        public void setBankName(String bankName) { this.bankName = bankName; }
        
        public String getBranchName() { return branchName; }
        public void setBranchName(String branchName) { this.branchName = branchName; }
        
        public String getAccountHolderName() { return accountHolderName; }
        public void setAccountHolderName(String accountHolderName) { this.accountHolderName = accountHolderName; }
        
        public String getVerificationResult() { return verificationResult; }
        public void setVerificationResult(String verificationResult) { this.verificationResult = verificationResult; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 解款歷史響應內部類別
     */
    class RemittanceHistoryResponse {
        private java.util.List<IndividualRemittanceResponse> remittances;
        private int totalPages;
        private long totalElements;
        private int currentPage;
        private int pageSize;
        
        // Getters and Setters
        public java.util.List<IndividualRemittanceResponse> getRemittances() { return remittances; }
        public void setRemittances(java.util.List<IndividualRemittanceResponse> remittances) { this.remittances = remittances; }
        
        public int getTotalPages() { return totalPages; }
        public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
        
        public long getTotalElements() { return totalElements; }
        public void setTotalElements(long totalElements) { this.totalElements = totalElements; }
        
        public int getCurrentPage() { return currentPage; }
        public void setCurrentPage(int currentPage) { this.currentPage = currentPage; }
        
        public int getPageSize() { return pageSize; }
        public void setPageSize(int pageSize) { this.pageSize = pageSize; }
    }
}