package com.kgi.module.individual.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.core.domain.model.TaiwanId;
import com.kgi.module.individual.application.dto.request.IndividualRemittanceRequest;
import com.kgi.module.individual.domain.model.BankAccount;
import com.kgi.module.individual.domain.model.IndividualRemittance;
import com.kgi.module.individual.domain.model.PersonalInfo;
import com.kgi.module.individual.domain.repository.IndividualRemittanceRepository;
import com.kgi.module.individual.domain.service.RemittanceValidationService;
import com.kgi.module.individual.domain.service.RemittanceCalculationService;
import com.kgi.module.individual.domain.service.BankAccountVerificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 處理自然人解款用例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessIndividualRemittanceUseCase {
    
    private final IndividualRemittanceRepository remittanceRepository;
    private final RemittanceValidationService validationService;
    private final RemittanceCalculationService calculationService;
    private final BankAccountVerificationService bankAccountVerificationService;
    
    /**
     * 執行自然人解款處理
     * 
     * @param request 解款請求
     * @return 解款實體
     */
    @Transactional
    public IndividualRemittance execute(IndividualRemittanceRequest request) {
        log.info("開始處理自然人解款: theirRefNo={}", request.getTheirRefNo());
        
        // 1. 驗證請求資料
        validateRequest(request);
        
        // 2. 檢查重複申請
        checkDuplicateApplication(request.getTheirRefNo());
        
        // 3. 驗證收款人身分證
        TaiwanId taiwanId = validateTaiwanId(request.getPayeeId());
        
        // 4. 建立收款人資訊
        PersonalInfo beneficiary = createBeneficiaryInfo(request, taiwanId);
        
        // 5. 驗證銀行帳戶
        validateBankAccount(beneficiary.getBankAccount(), request.getPayeeChineseName());
        
        // 6. 計算匯率和手續費
        IndividualRemittance remittance = calculateAndCreateRemittance(request, beneficiary);
        
        // 7. 儲存解款記錄
        IndividualRemittance savedRemittance = remittanceRepository.save(remittance);
        
        // 8. 觸發後續流程(工作流、通知等)
        triggerDownstreamProcesses(savedRemittance);
        
        log.info("自然人解款處理完成: remittanceId={}", savedRemittance.getRemittanceId());
        return savedRemittance;
    }
    
    /**
     * 驗證請求資料
     */
    private void validateRequest(IndividualRemittanceRequest request) {
        log.debug("驗證解款請求資料");
        
        // 基礎欄位驗證
        if (!validationService.isValidRequest(request)) {
            throw new BusinessException("INVALID_REQUEST", "請求資料驗證失敗");
        }
        
        // 金額範圍驗證
        if (!validationService.isValidAmount(request.getAmount(), request.getCurrency())) {
            throw new BusinessException("INVALID_AMOUNT", "金額超出允許範圍");
        }
        
        // 幣別支援檢查
        if (!validationService.isSupportedCurrency(request.getCurrency())) {
            throw new BusinessException("UNSUPPORTED_CURRENCY", "不支援的幣別: " + request.getCurrency());
        }
        
        // 資金來源代碼驗證
        if (!validationService.isValidSourceOfFund(request.getSourceOfFund())) {
            throw new BusinessException("INVALID_SOURCE_OF_FUND", "無效的資金來源代碼");
        }
    }
    
    /**
     * 檢查重複申請
     */
    private void checkDuplicateApplication(String theirRefNo) {
        log.debug("檢查重複申請: theirRefNo={}", theirRefNo);
        
        if (remittanceRepository.existsByTheirRefNo(theirRefNo)) {
            throw new BusinessException("DUPLICATE_APPLICATION", 
                    "重複的解款申請: " + theirRefNo);
        }
    }
    
    /**
     * 驗證台灣身分證號
     */
    private TaiwanId validateTaiwanId(String idNumber) {
        log.debug("驗證台灣身分證號: {}", maskId(idNumber));
        
        try {
            TaiwanId taiwanId = TaiwanId.of(idNumber);
            
            if (!TaiwanId.isValid(idNumber)) {
                throw new BusinessException("INVALID_TAIWAN_ID", "身分證號格式錯誤");
            }
            
            return taiwanId;
            
        } catch (Exception e) {
            throw new BusinessException("INVALID_TAIWAN_ID", "身分證號驗證失敗: " + e.getMessage());
        }
    }
    
    /**
     * 建立收款人資訊
     */
    private PersonalInfo createBeneficiaryInfo(IndividualRemittanceRequest request, TaiwanId taiwanId) {
        log.debug("建立收款人資訊");
        
        // 建立銀行帳戶資訊
        BankAccount bankAccount = new BankAccount(
                request.getPayeeBankCode(),
                request.getPayeeBranchCode(),
                request.getPayeeAccount(),
                null, // 銀行名稱稍後從外部服務取得
                null  // 分行名稱稍後從外部服務取得
        );
        
        // 建立個人資訊
        PersonalInfo beneficiary = new PersonalInfo(
                taiwanId,
                request.getPayeeChineseName(),
                request.getPayeeEnglishName(),
                null, // 出生日期(非必要)
                request.getPayeePhone(),
                request.getPayeeEmail(),
                bankAccount
        );
        
        return beneficiary;
    }
    
    /**
     * 驗證銀行帳戶
     */
    private void validateBankAccount(BankAccount bankAccount, String expectedAccountName) {
        log.debug("驗證銀行帳戶: bankCode={}, account={}", 
                bankAccount.getBankCode(), maskAccount(bankAccount.getAccountNumber()));
        
        try {
            // 調用銀行帳戶驗證服務
            boolean isValid = bankAccountVerificationService.verifyAccount(
                    bankAccount.getBankCode(),
                    bankAccount.getAccountNumber(),
                    expectedAccountName
            );
            
            if (!isValid) {
                throw new BusinessException("INVALID_BANK_ACCOUNT", "銀行帳戶驗證失敗");
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.warn("銀行帳戶驗證服務異常，允許繼續處理: {}", e.getMessage());
            // 外部服務異常時，記錄警告但允許繼續處理
        }
    }
    
    /**
     * 計算匯率手續費並建立解款實體
     */
    private IndividualRemittance calculateAndCreateRemittance(IndividualRemittanceRequest request, 
                                                            PersonalInfo beneficiary) {
        log.debug("計算匯率手續費並建立解款實體");
        
        // 生成解款編號
        String remittanceId = generateRemittanceId();
        
        // 計算匯率和手續費
        RemittanceCalculationService.CalculationResult calculationResult = calculationService.calculate(
                request.getCurrency(),
                request.getAmount(),
                "INDIVIDUAL"
        );
        
        // 建立解款實體
        IndividualRemittance remittance = IndividualRemittance.builder()
                .remittanceId(remittanceId)
                .theirRefNo(request.getTheirRefNo())
                .remitRefNo(request.getRemitRefNo())
                .payerName(request.getPayerName())
                .currency(request.getCurrency())
                .amount(request.getAmount())
                .twdAmount(calculationResult.getConvertedAmount())
                .exchangeRate(calculationResult.getExchangeRate())
                .fee(calculationResult.getTotalFees())
                .beneficiary(beneficiary)
                .sourceOfFund(request.getSourceOfFund())
                .remittancePurpose(request.getRemittancePurpose())
                .isUrgent(request.getIsUrgent())
                .customerNote(request.getCustomerNote())
                .processingStatus(IndividualRemittance.ProcessingStatus.PENDING)
                .verificationStatus(IndividualRemittance.VerificationStatus.PENDING)
                .businessStatusCode("01")
                .businessStatusDesc("身份驗證中")
                .estimatedCompletionTime(calculateEstimatedCompletionTime(request.getIsUrgent()))
                .build();
        
        return remittance;
    }
    
    /**
     * 觸發後續流程
     */
    private void triggerDownstreamProcesses(IndividualRemittance remittance) {
        log.debug("觸發後續流程: remittanceId={}", remittance.getRemittanceId());
        
        try {
            // TODO: 整合工作流管理模組
            // workflowFacade.startProcess("individual_remittance_process", remittance.getRemittanceId());
            
            // TODO: 整合通知服務模組
            // notificationFacade.sendRemittanceNotification(remittance);
            
            log.debug("後續流程觸發完成");
            
        } catch (Exception e) {
            log.error("觸發後續流程失敗: {}", e.getMessage(), e);
            // 記錄錯誤但不影響主流程
        }
    }
    
    /**
     * 生成解款編號
     */
    private String generateRemittanceId() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return "IBR" + timestamp.substring(timestamp.length() - 10) + uuid;
    }
    
    /**
     * 計算預計完成時間
     */
    private LocalDateTime calculateEstimatedCompletionTime(Boolean isUrgent) {
        LocalDateTime now = LocalDateTime.now();
        
        if (Boolean.TRUE.equals(isUrgent)) {
            // 緊急件：2小時內完成
            return now.plusHours(2);
        } else {
            // 一般件：1個工作日內完成
            return now.plusDays(1);
        }
    }
    
    /**
     * 遮罩身分證號
     */
    private String maskId(String id) {
        if (id == null || id.length() != 10) {
            return id;
        }
        return id.substring(0, 3) + "****" + id.substring(7);
    }
    
    /**
     * 遮罩帳號
     */
    private String maskAccount(String account) {
        if (account == null || account.length() < 6) {
            return account;
        }
        
        int length = account.length();
        if (length <= 6) {
            return account.substring(0, 3) + "***";
        } else {
            return account.substring(0, 3) + 
                   "*".repeat(length - 6) + 
                   account.substring(length - 3);
        }
    }
}