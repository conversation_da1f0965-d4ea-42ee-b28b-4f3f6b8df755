package com.kgi.module.individual.domain.repository;

import com.kgi.module.individual.domain.model.IndividualRemittance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 自然人解款Repository介面
 * 定義資料存取相關的操作
 */
public interface IndividualRemittanceRepository {
    
    /**
     * 儲存解款記錄
     * 
     * @param remittance 解款實體
     * @return 儲存後的解款實體
     */
    IndividualRemittance save(IndividualRemittance remittance);
    
    /**
     * 根據解款編號查詢
     * 
     * @param remittanceId 解款編號
     * @return 解款實體
     */
    Optional<IndividualRemittance> findByRemittanceId(String remittanceId);
    
    /**
     * 根據跨境平台參考編號查詢
     * 
     * @param theirRefNo 跨境平台參考編號
     * @return 解款實體
     */
    Optional<IndividualRemittance> findByTheirRefNo(String theirRefNo);
    
    /**
     * 檢查跨境平台參考編號是否存在
     * 
     * @param theirRefNo 跨境平台參考編號
     * @return 是否存在
     */
    boolean existsByTheirRefNo(String theirRefNo);
    
    /**
     * 根據身分證號查詢解款記錄
     * 
     * @param taiwanId 台灣身分證號
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @param pageNumber 頁碼 (從0開始)
     * @param pageSize 每頁數量
     * @return 解款記錄列表
     */
    Page<IndividualRemittance> findByTaiwanIdAndDateRange(String taiwanId, 
                                                         LocalDateTime startDate, 
                                                         LocalDateTime endDate,
                                                         Pageable pageable);
    
    /**
     * 計算根據身分證號的解款記錄總數
     * 
     * @param taiwanId 台灣身分證號
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 記錄總數
     */
    long countByTaiwanIdAndDateRange(String taiwanId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 根據處理狀態查詢解款記錄
     * 
     * @param status 處理狀態
     * @param limit 限制數量
     * @return 解款記錄列表
     */
    List<IndividualRemittance> findByProcessingStatus(IndividualRemittance.ProcessingStatus status, int limit);
    
    /**
     * 查詢超時的解款記錄
     * 
     * @param cutoffTime 截止時間
     * @param limit 限制數量
     * @return 超時的解款記錄列表
     */
    List<IndividualRemittance> findTimeoutRemittances(LocalDateTime cutoffTime, int limit);
    
    /**
     * 根據業務狀態代碼查詢解款記錄
     * 
     * @param statusCode 業務狀態代碼
     * @param startTime 開始時間
     * @param endTime 結束時間
     * @return 解款記錄列表
     */
    List<IndividualRemittance> findByBusinessStatusCodeAndTimeRange(String statusCode,
                                                                   LocalDateTime startTime,
                                                                   LocalDateTime endTime);
    
    /**
     * 查詢需要補件的解款記錄
     * 
     * @param deadlineBefore 補件截止時間之前
     * @return 需要補件的解款記錄列表
     */
    List<IndividualRemittance> findRemittancesRequiringSupplement(LocalDateTime deadlineBefore);
    
    /**
     * 根據匯款人姓名模糊查詢
     * 
     * @param payerName 匯款人姓名(部分)
     * @param pageNumber 頁碼
     * @param pageSize 每頁數量
     * @return 解款記錄列表
     */
    List<IndividualRemittance> findByPayerNameContaining(String payerName, int pageNumber, int pageSize);
    
    /**
     * 統計解款記錄數量(按狀態)
     * 
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 狀態統計結果
     */
    List<Object[]> countByStatusAndDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * 統計解款金額(按幣別)
     * 
     * @param startDate 開始日期
     * @param endDate 結束日期
     * @return 幣別金額統計結果
     */
    List<CurrencyAmountSum> sumAmountByCurrencyAndDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * 批次更新解款狀態
     * 
     * @param remittanceIds 解款編號列表
     * @param newStatus 新狀態
     * @param statusDescription 狀態描述
     * @return 更新的記錄數量
     */
    int batchUpdateStatus(List<String> remittanceIds, 
                         IndividualRemittance.ProcessingStatus newStatus,
                         String statusDescription);
    
    /**
     * 軟刪除解款記錄
     * 
     * @param remittanceId 解款編號
     * @return 是否成功
     */
    boolean softDelete(String remittanceId);
    
    /**
     * 查詢所有解款記錄
     * 
     * @return 所有解款記錄列表
     */
    List<IndividualRemittance> findAll();
    
    /**
     * 狀態統計結果內部類別
     */
    class StatusCount {
        private final String status;
        private final Long count;
        
        public StatusCount(String status, Long count) {
            this.status = status;
            this.count = count;
        }
        
        public String getStatus() { return status; }
        public Long getCount() { return count; }
    }
    
    /**
     * 幣別金額統計結果內部類別
     */
    class CurrencyAmountSum {
        private final String currency;
        private final java.math.BigDecimal totalAmount;
        private final Long transactionCount;
        
        public CurrencyAmountSum(String currency, java.math.BigDecimal totalAmount, Long transactionCount) {
            this.currency = currency;
            this.totalAmount = totalAmount;
            this.transactionCount = transactionCount;
        }
        
        public String getCurrency() { return currency; }
        public java.math.BigDecimal getTotalAmount() { return totalAmount; }
        public Long getTransactionCount() { return transactionCount; }
    }
}