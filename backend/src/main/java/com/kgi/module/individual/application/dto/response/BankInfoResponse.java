package com.kgi.module.individual.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 銀行資訊響應DTO
 * 對應前端的BankInfoResponse介面
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BankInfoResponse {
    
    /**
     * 查詢是否成功
     */
    private Boolean success;
    
    /**
     * 銀行代碼
     */
    private String bankCode;
    
    /**
     * 銀行名稱
     */
    private String bankName;
    
    /**
     * 銀行英文名稱
     */
    private String bankNameEn;
    
    /**
     * 銀行簡稱
     */
    private String bankShortName;
    
    /**
     * 分行代碼
     */
    private String branchCode;
    
    /**
     * 分行名稱
     */
    private String branchName;
    
    /**
     * 分行英文名稱
     */
    private String branchNameEn;
    
    /**
     * 銀行地址
     */
    private String bankAddress;
    
    /**
     * 銀行電話
     */
    private String bankPhone;
    
    /**
     * SWIFT代碼
     */
    private String swiftCode;
    
    /**
     * 銀行狀態
     */
    private BankStatus bankStatus;
    
    /**
     * 是否啟用
     */
    private Boolean isActive;
    
    /**
     * 支援的服務
     */
    private List<SupportedService> supportedServices;
    
    /**
     * 銀行詳細資訊
     */
    private BankDetails bankDetails;
    
    /**
     * 跨行資訊
     */
    private InterBankInfo interBankInfo;
    
    /**
     * 服務時間
     */
    private ServiceHours serviceHours;
    
    /**
     * 錯誤訊息 (如果有)
     */
    private String errorMessage;
    
    /**
     * 錯誤代碼 (如果有)
     */
    private String errorCode;
    
    /**
     * 查詢時間
     */
    private LocalDateTime queryTime;
    
    /**
     * 資料更新時間
     */
    private LocalDateTime dataLastUpdated;
    
    /**
     * 銀行狀態枚舉
     */
    public enum BankStatus {
        /** 正常營運 */
        ACTIVE("正常營運"),
        
        /** 暫停服務 */
        SUSPENDED("暫停服務"),
        
        /** 維護中 */
        MAINTENANCE("維護中"),
        
        /** 已停業 */
        CLOSED("已停業"),
        
        /** 限制服務 */
        LIMITED("限制服務");
        
        private final String description;
        
        BankStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 支援的服務
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupportedService {
        
        /**
         * 服務代碼
         */
        private String serviceCode;
        
        /**
         * 服務名稱
         */
        private String serviceName;
        
        /**
         * 服務描述
         */
        private String description;
        
        /**
         * 是否支援
         */
        private Boolean isSupported;
        
        /**
         * 是否可用
         */
        private Boolean isAvailable;
        
        /**
         * 服務狀態
         */
        private ServiceStatus status;
        
        /**
         * 服務費用
         */
        private java.math.BigDecimal serviceFee;
        
        /**
         * 處理時間 (小時)
         */
        private Integer processingHours;
        
        /**
         * 服務限制
         */
        private String limitations;
        
        /**
         * 備註
         */
        private String remarks;
    }
    
    /**
     * 服務狀態枚舉
     */
    public enum ServiceStatus {
        /** 正常 */
        NORMAL("正常"),
        
        /** 緩慢 */
        SLOW("緩慢"),
        
        /** 暫停 */
        SUSPENDED("暫停"),
        
        /** 維護 */
        MAINTENANCE("維護"),
        
        /** 不可用 */
        UNAVAILABLE("不可用");
        
        private final String description;
        
        ServiceStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 銀行詳細資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BankDetails {
        
        /**
         * 銀行類型
         */
        private String bankType;
        
        /**
         * 成立日期
         */
        private String establishedDate;
        
        /**
         * 資本額
         */
        private java.math.BigDecimal capitalAmount;
        
        /**
         * 員工數
         */
        private Integer employeeCount;
        
        /**
         * 分行數
         */
        private Integer branchCount;
        
        /**
         * ATM數量
         */
        private Integer atmCount;
        
        /**
         * 監管機關
         */
        private String regulator;
        
        /**
         * 許可證號
         */
        private String licenseNumber;
        
        /**
         * 信用評等
         */
        private String creditRating;
        
        /**
         * 官方網站
         */
        private String officialWebsite;
        
        /**
         * 客服電話
         */
        private String customerServicePhone;
        
        /**
         * 客服信箱
         */
        private String customerServiceEmail;
    }
    
    /**
     * 跨行資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterBankInfo {
        
        /**
         * 是否支援跨行轉帳
         */
        private Boolean supportsInterBankTransfer;
        
        /**
         * 跨行轉帳手續費
         */
        private java.math.BigDecimal interBankTransferFee;
        
        /**
         * 跨行轉帳限額
         */
        private TransferLimits transferLimits;
        
        /**
         * 財金公司代碼
         */
        private String fiscode;
        
        /**
         * 支援的跨行服務
         */
        private List<String> supportedInterBankServices;
        
        /**
         * 跨行處理時間
         */
        private ProcessingTimes processingTimes;
        
        /**
         * 特殊限制
         */
        private String specialRestrictions;
    }
    
    /**
     * 轉帳限額
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransferLimits {
        
        /**
         * 單筆最小金額
         */
        private java.math.BigDecimal minAmount;
        
        /**
         * 單筆最大金額
         */
        private java.math.BigDecimal maxAmount;
        
        /**
         * 日累計限額
         */
        private java.math.BigDecimal dailyLimit;
        
        /**
         * 月累計限額
         */
        private java.math.BigDecimal monthlyLimit;
        
        /**
         * 年累計限額
         */
        private java.math.BigDecimal yearlyLimit;
        
        /**
         * 限額幣別
         */
        private String currency;
        
        /**
         * 限額重置時間
         */
        private String resetTime;
    }
    
    /**
     * 處理時間
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingTimes {
        
        /**
         * 即時轉帳 (分鐘)
         */
        private Integer realTimeMinutes;
        
        /**
         * 一般轉帳 (小時)
         */
        private Integer normalHours;
        
        /**
         * 緊急轉帳 (分鐘)
         */
        private Integer urgentMinutes;
        
        /**
         * 批次處理 (小時)
         */
        private Integer batchHours;
        
        /**
         * 營業日限制
         */
        private Boolean businessDaysOnly;
        
        /**
         * 截止時間
         */
        private String cutoffTime;
    }
    
    /**
     * 服務時間
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceHours {
        
        /**
         * 櫃檯服務時間
         */
        private String counterHours;
        
        /**
         * ATM服務時間
         */
        private String atmHours;
        
        /**
         * 網路銀行服務時間
         */
        private String onlineBankingHours;
        
        /**
         * 客服時間
         */
        private String customerServiceHours;
        
        /**
         * 假日服務
         */
        private String holidayService;
        
        /**
         * 特殊時段說明
         */
        private String specialSchedule;
        
        /**
         * 國定假日安排
         */
        private String nationalHolidaySchedule;
    }
    
    /**
     * 檢查銀行是否正常運作
     */
    public boolean isBankOperational() {
        return BankStatus.ACTIVE.equals(bankStatus);
    }
    
    /**
     * 檢查是否支援特定服務
     */
    public boolean supportsService(String serviceCode) {
        if (supportedServices == null) {
            return false;
        }
        
        return supportedServices.stream()
                .anyMatch(service -> serviceCode.equals(service.getServiceCode()) &&
                                   Boolean.TRUE.equals(service.getIsSupported()) &&
                                   Boolean.TRUE.equals(service.getIsAvailable()));
    }
    
    /**
     * 取得服務資訊
     */
    public SupportedService getServiceInfo(String serviceCode) {
        if (supportedServices == null) {
            return null;
        }
        
        return supportedServices.stream()
                .filter(service -> serviceCode.equals(service.getServiceCode()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 檢查是否支援跨行轉帳
     */
    public boolean supportsInterBankTransfer() {
        return interBankInfo != null && 
               Boolean.TRUE.equals(interBankInfo.getSupportsInterBankTransfer());
    }
    
    /**
     * 取得跨行轉帳手續費
     */
    public java.math.BigDecimal getInterBankTransferFee() {
        if (interBankInfo == null) {
            return java.math.BigDecimal.ZERO;
        }
        
        return interBankInfo.getInterBankTransferFee() != null ? 
                interBankInfo.getInterBankTransferFee() : java.math.BigDecimal.ZERO;
    }
    
    /**
     * 檢查金額是否在轉帳限額內
     */
    public boolean isAmountWithinLimits(java.math.BigDecimal amount) {
        if (interBankInfo == null || interBankInfo.getTransferLimits() == null) {
            return true; // 沒有限制資訊，假設可以
        }
        
        TransferLimits limits = interBankInfo.getTransferLimits();
        
        if (limits.getMinAmount() != null && amount.compareTo(limits.getMinAmount()) < 0) {
            return false;
        }
        
        if (limits.getMaxAmount() != null && amount.compareTo(limits.getMaxAmount()) > 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 取得銀行完整名稱
     */
    public String getFullBankName() {
        if (branchName != null && !branchName.isEmpty()) {
            return String.format("%s %s", bankName, branchName);
        }
        return bankName;
    }
    
    /**
     * 取得銀行完整英文名稱
     */
    public String getFullBankNameEn() {
        if (branchNameEn != null && !branchNameEn.isEmpty()) {
            return String.format("%s %s", bankNameEn, branchNameEn);
        }
        return bankNameEn;
    }
    
    /**
     * 取得銀行代碼與名稱組合
     */
    public String getBankCodeAndName() {
        return String.format("(%s) %s", bankCode, getFullBankName());
    }
    
    /**
     * 檢查資料是否過期
     */
    public boolean isDataStale(int maxAgeHours) {
        if (dataLastUpdated == null) {
            return true;
        }
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(maxAgeHours);
        return dataLastUpdated.isBefore(cutoffTime);
    }
    
    /**
     * 取得銀行狀態描述
     */
    public String getStatusDescription() {
        if (bankStatus == null) {
            return "狀態未知";
        }
        
        String baseDescription = bankStatus.getDescription();
        
        if (!isBankOperational()) {
            if (serviceHours != null && serviceHours.getSpecialSchedule() != null) {
                return baseDescription + " - " + serviceHours.getSpecialSchedule();
            }
        }
        
        return baseDescription;
    }
    
    /**
     * 檢查查詢是否成功
     */
    public boolean isQuerySuccessful() {
        return Boolean.TRUE.equals(success) && errorCode == null && errorMessage == null;
    }
    
    /**
     * 取得錯誤描述
     */
    public String getErrorDescription() {
        if (errorMessage != null) {
            return errorMessage;
        }
        
        if (errorCode != null) {
            return "錯誤代碼: " + errorCode;
        }
        
        if (!isQuerySuccessful()) {
            return "查詢失敗";
        }
        
        return null;
    }
}