package com.kgi.module.individual.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 解款計算服務
 * 負責匯率換算、手續費計算等金額相關計算
 */
@Slf4j
@Service("individualRemittanceCalculationService")
@RequiredArgsConstructor
public class RemittanceCalculationService {
    
    /**
     * 計算解款金額
     */
    public CalculationResult calculate(String currency, BigDecimal amount, String customerType) {
        log.debug("計算解款金額: currency={}, amount={}, customerType={}", currency, amount, customerType);
        
        // 查詢匯率
        BigDecimal exchangeRate = getExchangeRate(currency);
        
        // 計算台幣金額
        BigDecimal twdAmount = amount.multiply(exchangeRate).setScale(0, RoundingMode.HALF_UP);
        
        // 計算手續費
        BigDecimal totalFees = calculateFees(currency, amount, customerType);
        
        // 建立計算結果
        CalculationResult result = CalculationResult.builder()
                .exchangeRate(exchangeRate)
                .convertedAmount(twdAmount)
                .totalFees(totalFees)
                .netAmount(twdAmount.subtract(totalFees))
                .calculationTime(LocalDateTime.now())
                .validUntil(LocalDateTime.now().plusHours(1))
                .build();
        
        log.debug("計算完成: exchangeRate={}, twdAmount={}, totalFees={}", 
                exchangeRate, twdAmount, totalFees);
        
        return result;
    }
    
    /**
     * 取得匯率
     */
    private BigDecimal getExchangeRate(String currency) {
        // TODO: 實際應該從外部匯率服務取得即時匯率
        switch (currency.toUpperCase()) {
            case "USD":
                return new BigDecimal("32.50");
            case "EUR":
                return new BigDecimal("35.20");
            case "JPY":
                return new BigDecimal("0.22");
            case "HKD":
                return new BigDecimal("4.16");
            case "CNY":
                return new BigDecimal("4.48");
            case "GBP":
                return new BigDecimal("40.15");
            case "AUD":
                return new BigDecimal("21.35");
            case "CAD":
                return new BigDecimal("23.80");
            case "SGD":
                return new BigDecimal("24.20");
            default:
                return new BigDecimal("32.50"); // 預設使用USD匯率
        }
    }
    
    /**
     * 計算手續費
     */
    private BigDecimal calculateFees(String currency, BigDecimal amount, String customerType) {
        BigDecimal handlingFee = calculateHandlingFee(amount, customerType);
        BigDecimal telegraphicFee = calculateTelegraphicFee();
        BigDecimal postage = calculatePostage();
        
        return handlingFee.add(telegraphicFee).add(postage);
    }
    
    /**
     * 計算手續費
     */
    private BigDecimal calculateHandlingFee(BigDecimal amount, String customerType) {
        BigDecimal feeRate;
        BigDecimal minFee;
        BigDecimal maxFee;
        
        // 根據客戶類型設定費率
        switch (customerType.toUpperCase()) {
            case "VIP":
                feeRate = new BigDecimal("0.001"); // 0.1%
                minFee = new BigDecimal("50");
                maxFee = new BigDecimal("500");
                break;
            case "PREMIUM":
                feeRate = new BigDecimal("0.0015"); // 0.15%
                minFee = new BigDecimal("80");
                maxFee = new BigDecimal("800");
                break;
            default:
                feeRate = new BigDecimal("0.002"); // 0.2%
                minFee = new BigDecimal("100");
                maxFee = new BigDecimal("1000");
                break;
        }
        
        BigDecimal calculatedFee = amount.multiply(feeRate);
        
        // 應用最小和最大限制
        if (calculatedFee.compareTo(minFee) < 0) {
            return minFee;
        } else if (calculatedFee.compareTo(maxFee) > 0) {
            return maxFee;
        } else {
            return calculatedFee.setScale(2, RoundingMode.HALF_UP);
        }
    }
    
    /**
     * 計算電報費
     */
    private BigDecimal calculateTelegraphicFee() {
        return new BigDecimal("30.00");
    }
    
    /**
     * 計算郵費
     */
    private BigDecimal calculatePostage() {
        return new BigDecimal("20.00");
    }
    
    /**
     * 取得支援的幣別列表
     */
    public List<String> getSupportedCurrencies() {
        return Arrays.asList("USD", "EUR", "JPY", "HKD", "CNY", "GBP", "AUD", "CAD", "SGD");
    }
    
    /**
     * 驗證金額限制
     */
    public boolean validateAmountLimits(String currency, BigDecimal amount, String customerType, String taiwanId) {
        log.debug("驗證金額限制: currency={}, amount={}, customerType={}", 
                currency, amount, customerType);
        
        try {
            // 單筆交易限額
            BigDecimal singleTransactionLimit = getSingleTransactionLimit(customerType);
            if (amount.compareTo(singleTransactionLimit) > 0) {
                log.warn("超過單筆交易限額: amount={}, limit={}", amount, singleTransactionLimit);
                return false;
            }
            
            // 最小金額限制
            BigDecimal minimumAmount = getMinimumAmount(currency);
            if (amount.compareTo(minimumAmount) < 0) {
                log.warn("低於最小金額: amount={}, minimum={}", amount, minimumAmount);
                return false;
            }
            
            // TODO: 日累計、月累計限額檢查 (需要整合數據庫查詢)
            
            return true;
            
        } catch (Exception e) {
            log.error("金額限制驗證異常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 取得手續費結構
     */
    public Object getFeeStructure(String currency, String customerType) {
        log.debug("查詢手續費結構: currency={}, customerType={}", currency, customerType);
        
        BigDecimal feeRate;
        BigDecimal minFee;
        BigDecimal maxFee;
        
        switch (customerType.toUpperCase()) {
            case "VIP":
                feeRate = new BigDecimal("0.001");
                minFee = new BigDecimal("50");
                maxFee = new BigDecimal("500");
                break;
            case "PREMIUM":
                feeRate = new BigDecimal("0.0015");
                minFee = new BigDecimal("80");
                maxFee = new BigDecimal("800");
                break;
            default:
                feeRate = new BigDecimal("0.002");
                minFee = new BigDecimal("100");
                maxFee = new BigDecimal("1000");
                break;
        }
        
        return Map.of(
                "customerType", customerType,
                "currency", currency,
                "handlingFee", Map.of(
                        "rate", feeRate,
                        "minimumAmount", minFee,
                        "maximumAmount", maxFee
                ),
                "fixedFees", Map.of(
                        "telegraphicFee", new BigDecimal("30.00"),
                        "postage", new BigDecimal("20.00")
                ),
                "description", "手續費 = (金額 * 費率) + 電報費 + 郵費"
        );
    }
    
    /**
     * 取得單筆交易限額
     */
    private BigDecimal getSingleTransactionLimit(String customerType) {
        switch (customerType.toUpperCase()) {
            case "VIP":
                return new BigDecimal("2000000"); // 200萬
            case "PREMIUM":
                return new BigDecimal("1000000"); // 100萬
            default:
                return new BigDecimal("500000");  // 50萬
        }
    }
    
    /**
     * 取得最小金額
     */
    private BigDecimal getMinimumAmount(String currency) {
        // 所有幣別最小金額為 100 單位
        return new BigDecimal("100");
    }
    
    /**
     * 計算結果內部類別
     */
    @lombok.Data
    @lombok.Builder
    public static class CalculationResult {
        private BigDecimal exchangeRate;
        private BigDecimal convertedAmount;
        private BigDecimal totalFees;
        private BigDecimal netAmount;
        private LocalDateTime calculationTime;
        private LocalDateTime validUntil;
        
        // 為了支援AmountCalculationResponse，增加細分手續費
        private BigDecimal processingFee;
        private BigDecimal transferFee;
        private BigDecimal otherFees;
        
        // 建造器後處理方法
        public CalculationResult processDetailedFees() {
            if (this.totalFees != null) {
                // 將總手續費分解為細項
                BigDecimal remaining = this.totalFees;
                
                // 電報費和郵費是固定的
                this.transferFee = new BigDecimal("30.00");
                this.otherFees = new BigDecimal("20.00");
                
                // 其餘為處理費
                this.processingFee = remaining.subtract(this.transferFee).subtract(this.otherFees);
            }
            return this;
        }
        
        public BigDecimal getProcessingFee() {
            if (processingFee == null) {
                processDetailedFees();
            }
            return processingFee;
        }
        
        public BigDecimal getTransferFee() {
            if (transferFee == null) {
                processDetailedFees();
            }
            return transferFee;
        }
        
        public BigDecimal getOtherFees() {
            if (otherFees == null) {
                processDetailedFees();
            }
            return otherFees;
        }
        
        public LocalDateTime getRateValidUntil() {
            return validUntil;
        }
    }
}