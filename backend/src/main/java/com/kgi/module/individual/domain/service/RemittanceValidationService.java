package com.kgi.module.individual.domain.service;

import com.kgi.module.individual.application.dto.request.IndividualRemittanceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 解款驗證服務
 * 負責自然人解款相關的業務規則驗證
 */
@Slf4j
@Service("individualRemittanceValidationService")
@RequiredArgsConstructor
public class RemittanceValidationService {
    
    // 支援的幣別列表
    private static final List<String> SUPPORTED_CURRENCIES = Arrays.asList(
            "USD", "EUR", "JPY", "GBP", "AUD", "CAD", "HKD", "SGD", "CNY"
    );
    
    // 金額限制配置
    private static final BigDecimal MIN_AMOUNT_USD = new BigDecimal("1.00");
    private static final BigDecimal MAX_AMOUNT_USD = new BigDecimal("50000.00");
    private static final BigDecimal MAX_AMOUNT_EUR = new BigDecimal("45000.00");
    private static final BigDecimal MAX_AMOUNT_JPY = new BigDecimal("5000000.00");
    
    // 資金來源代碼列表(依央行規定)
    private static final List<String> VALID_SOURCE_OF_FUND_CODES = Arrays.asList(
            "611", // 薪資所得
            "612", // 營業收入
            "613", // 財產交易所得
            "614", // 利息股利所得
            "615", // 其他所得
            "621", // 存款
            "622", // 有價證券
            "623", // 不動產
            "624", // 其他資產
            "631", // 借款
            "632", // 其他來源
            "699"  // 其他
    );
    
    // 手機號碼格式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^09\\d{8}$");
    
    // 電子郵件格式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    /**
     * 驗證解款請求是否有效
     */
    public boolean isValidRequest(IndividualRemittanceRequest request) {
        log.debug("驗證解款請求: {}", request.getTheirRefNo());
        
        if (request == null) {
            log.warn("解款請求為空");
            return false;
        }
        
        // 基礎欄位驗證
        if (!isValidBasicFields(request)) {
            return false;
        }
        
        // 收款人資訊驗證
        if (!isValidBeneficiaryInfo(request)) {
            return false;
        }
        
        // 銀行資訊驗證
        if (!isValidBankInfo(request)) {
            return false;
        }
        
        log.debug("解款請求驗證通過");
        return true;
    }
    
    /**
     * 驗證金額是否有效
     */
    public boolean isValidAmount(BigDecimal amount, String currency) {
        log.debug("驗證金額: amount={}, currency={}", amount, currency);
        
        if (amount == null || currency == null) {
            log.warn("金額或幣別為空");
            return false;
        }
        
        // 檢查金額是否為正數
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("金額必須大於零: {}", amount);
            return false;
        }
        
        // 檢查最小金額
        if (amount.compareTo(getMinAmount(currency)) < 0) {
            log.warn("金額低於最小限制: {} < {}", amount, getMinAmount(currency));
            return false;
        }
        
        // 檢查最大金額
        if (amount.compareTo(getMaxAmount(currency)) > 0) {
            log.warn("金額超過最大限制: {} > {}", amount, getMaxAmount(currency));
            return false;
        }
        
        // 檢查小數位數
        if (!isValidDecimalPlaces(amount, currency)) {
            log.warn("金額小數位數不正確: {}", amount);
            return false;
        }
        
        log.debug("金額驗證通過");
        return true;
    }
    
    /**
     * 檢查是否支援的幣別
     */
    public boolean isSupportedCurrency(String currency) {
        boolean supported = currency != null && SUPPORTED_CURRENCIES.contains(currency.toUpperCase());
        log.debug("幣別支援檢查: {} -> {}", currency, supported);
        return supported;
    }
    
    /**
     * 驗證資金來源代碼
     */
    public boolean isValidSourceOfFund(String sourceOfFund) {
        boolean valid = sourceOfFund != null && VALID_SOURCE_OF_FUND_CODES.contains(sourceOfFund);
        log.debug("資金來源代碼驗證: {} -> {}", sourceOfFund, valid);
        return valid;
    }
    
    /**
     * 驗證台灣身分證號格式
     */
    public boolean isValidTaiwanId(String taiwanId) {
        if (taiwanId == null || taiwanId.length() != 10) {
            return false;
        }
        
        // 檢查格式: 第一位為英文字母，第二位為1或2，後8位為數字
        if (!taiwanId.matches("^[A-Z][12]\\d{8}$")) {
            return false;
        }
        
        // 驗證檢查碼
        return validateTaiwanIdChecksum(taiwanId);
    }
    
    /**
     * 驗證銀行代碼格式
     */
    public boolean isValidBankCode(String bankCode) {
        // 台灣銀行代碼為3位數字
        return bankCode != null && bankCode.matches("^\\d{3}$");
    }
    
    /**
     * 驗證帳戶號碼格式
     */
    public boolean isValidAccountNumber(String accountNumber) {
        if (accountNumber == null) {
            return false;
        }
        
        // 帳戶號碼長度通常為7-20位數字
        return accountNumber.matches("^\\d{7,20}$");
    }
    
    /**
     * 驗證手機號碼格式
     */
    public boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && PHONE_PATTERN.matcher(phoneNumber).matches();
    }
    
    /**
     * 驗證電子郵件格式
     */
    public boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * 驗證基礎欄位
     */
    private boolean isValidBasicFields(IndividualRemittanceRequest request) {
        // 必要欄位檢查
        if (isBlank(request.getTheirRefNo()) ||
            isBlank(request.getPayerName()) ||
            isBlank(request.getCurrency()) ||
            request.getAmount() == null ||
            isBlank(request.getSourceOfFund())) {
            log.warn("必要欄位為空");
            return false;
        }
        
        // 欄位長度檢查
        if (request.getTheirRefNo().length() > 50 ||
            request.getPayerName().length() > 100) {
            log.warn("欄位長度超過限制");
            return false;
        }
        
        return true;
    }
    
    /**
     * 驗證收款人資訊
     */
    private boolean isValidBeneficiaryInfo(IndividualRemittanceRequest request) {
        // 收款人基本資訊
        if (isBlank(request.getPayeeChineseName()) ||
            isBlank(request.getPayeeEnglishName()) ||
            isBlank(request.getPayeeId())) {
            log.warn("收款人基本資訊為空");
            return false;
        }
        
        // 驗證身分證號
        if (!isValidTaiwanId(request.getPayeeId())) {
            log.warn("收款人身分證號格式錯誤: {}", maskId(request.getPayeeId()));
            return false;
        }
        
        // 驗證手機號碼(如果提供)
        if (request.getPayeePhone() != null && !isValidPhoneNumber(request.getPayeePhone())) {
            log.warn("收款人手機號碼格式錯誤");
            return false;
        }
        
        // 驗證電子郵件(如果提供)
        if (request.getPayeeEmail() != null && !isValidEmail(request.getPayeeEmail())) {
            log.warn("收款人電子郵件格式錯誤");
            return false;
        }
        
        return true;
    }
    
    /**
     * 驗證銀行資訊
     */
    private boolean isValidBankInfo(IndividualRemittanceRequest request) {
        // 銀行代碼和帳號為必要
        if (isBlank(request.getPayeeBankCode()) ||
            isBlank(request.getPayeeAccount())) {
            log.warn("銀行代碼或帳號為空");
            return false;
        }
        
        // 驗證銀行代碼格式
        if (!isValidBankCode(request.getPayeeBankCode())) {
            log.warn("銀行代碼格式錯誤: {}", request.getPayeeBankCode());
            return false;
        }
        
        // 驗證帳戶號碼格式
        if (!isValidAccountNumber(request.getPayeeAccount())) {
            log.warn("帳戶號碼格式錯誤");
            return false;
        }
        
        return true;
    }
    
    /**
     * 取得幣別最小金額
     */
    private BigDecimal getMinAmount(String currency) {
        // 所有幣別的最小金額都轉換為相當於1美元
        switch (currency.toUpperCase()) {
            case "USD":
                return MIN_AMOUNT_USD;
            case "EUR":
                return new BigDecimal("0.85");
            case "JPY":
                return new BigDecimal("100");
            case "HKD":
                return new BigDecimal("7.8");
            case "CNY":
                return new BigDecimal("6.5");
            default:
                return MIN_AMOUNT_USD;
        }
    }
    
    /**
     * 取得幣別最大金額
     */
    private BigDecimal getMaxAmount(String currency) {
        switch (currency.toUpperCase()) {
            case "USD":
                return MAX_AMOUNT_USD;
            case "EUR":
                return MAX_AMOUNT_EUR;
            case "JPY":
                return MAX_AMOUNT_JPY;
            case "HKD":
                return new BigDecimal("390000");
            case "CNY":
                return new BigDecimal("325000");
            default:
                return MAX_AMOUNT_USD;
        }
    }
    
    /**
     * 檢查金額小數位數
     */
    private boolean isValidDecimalPlaces(BigDecimal amount, String currency) {
        int scale = amount.scale();
        
        switch (currency.toUpperCase()) {
            case "JPY":
                // 日圓不允許小數
                return scale == 0;
            default:
                // 其他幣別最多2位小數
                return scale <= 2;
        }
    }
    
    /**
     * 驗證台灣身分證號檢查碼
     */
    private boolean validateTaiwanIdChecksum(String taiwanId) {
        // 地區碼對照表
        String areaCode = "ABCDEFGHJKLMNPQRSTUVXYWZIO";
        char firstChar = taiwanId.charAt(0);
        int areaValue = areaCode.indexOf(firstChar) + 10;
        
        // 計算檢查碼
        int sum = (areaValue / 10) + (areaValue % 10) * 9;
        
        for (int i = 1; i < 9; i++) {
            sum += Character.getNumericValue(taiwanId.charAt(i)) * (9 - i);
        }
        
        int checkDigit = (10 - (sum % 10)) % 10;
        return checkDigit == Character.getNumericValue(taiwanId.charAt(9));
    }
    
    /**
     * 檢查字串是否為空
     */
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 檢查個人額度限制
     */
    public boolean checkPersonalLimit(String taiwanId, BigDecimal amount, String currency) {
        log.debug("檢查個人額度限制: taiwanId={}, amount={} {}", maskId(taiwanId), amount, currency);
        
        try {
            // TODO: 整合額度管理系統
            // 1. 查詢個人當日已使用額度
            // 2. 查詢個人月累計額度
            // 3. 檢查是否超過個人限制
            
            // 暫時使用固定限制進行驗證
            BigDecimal dailyLimit = new BigDecimal("100000"); // 10萬美元等值
            BigDecimal monthlyLimit = new BigDecimal("500000"); // 50萬美元等值
            
            // 將金額轉換為美元等值進行比較
            BigDecimal usdEquivalent = convertToUsdEquivalent(amount, currency);
            
            if (usdEquivalent.compareTo(dailyLimit) > 0) {
                log.warn("超出單日限額: {} > {}", usdEquivalent, dailyLimit);
                return false;
            }
            
            // TODO: 檢查月累計額度
            
            return true;
            
        } catch (Exception e) {
            log.error("檢查個人額度限制失敗: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 檢查風險評估
     */
    public boolean checkRiskAssessment(String taiwanId, BigDecimal amount) {
        log.debug("檢查風險評估: taiwanId={}, amount={}", maskId(taiwanId), amount);
        
        try {
            // TODO: 整合風險評估系統
            // 1. 檢查客戶風險等級
            // 2. 檢查交易行為模式
            // 3. 檢查黑名單和制裁清單
            
            // 暫時使用簡單規則進行評估
            BigDecimal highRiskThreshold = new BigDecimal("50000");
            
            if (amount.compareTo(highRiskThreshold) > 0) {
                log.info("高風險金額，需要額外審核: {}", amount);
                // 高風險但不拒絕，交由人工審核
                return true;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("風險評估失敗: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 轉換為美元等值
     */
    private BigDecimal convertToUsdEquivalent(BigDecimal amount, String currency) {
        // 簡化的匯率轉換，實際應該調用匯率服務
        switch (currency.toUpperCase()) {
            case "USD":
                return amount;
            case "EUR":
                return amount.multiply(new BigDecimal("1.18"));
            case "JPY":
                return amount.divide(new BigDecimal("110"), 2, BigDecimal.ROUND_HALF_UP);
            case "HKD":
                return amount.divide(new BigDecimal("7.8"), 2, BigDecimal.ROUND_HALF_UP);
            case "CNY":
                return amount.divide(new BigDecimal("6.5"), 2, BigDecimal.ROUND_HALF_UP);
            default:
                return amount; // 預設當作美元
        }
    }
    
    /**
     * 遮罩身分證號
     */
    private String maskId(String id) {
        if (id == null || id.length() != 10) {
            return id;
        }
        return id.substring(0, 3) + "****" + id.substring(7);
    }
}