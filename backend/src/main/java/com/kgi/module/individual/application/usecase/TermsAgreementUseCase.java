package com.kgi.module.individual.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.individual.application.dto.request.TermsAgreementRequest;
import com.kgi.module.individual.application.dto.response.TermsAgreementResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 條款同意用例
 * 負責處理使用條款的同意和查詢功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TermsAgreementUseCase {
    
    // 條款同意記錄 (實際環境應使用數據庫)
    private final Map<String, TermsAgreementRecord> agreementRecords = new ConcurrentHashMap<>();
    
    // 條款配置
    private static final String CURRENT_TERMS_VERSION = "1.0";
    private static final int AGREEMENT_VALIDITY_HOURS = 24;
    
    /**
     * 同意使用條款 (用於Controller)
     */
    @Transactional
    public TermsAgreementResponse agreeToTerms(TermsAgreementRequest request) {
        log.info("處理條款同意: taiwanId={}, termsVersion={}", 
                maskUserId(request.getTaiwanId()), request.getTermsVersion());
        
        try {
            // 使用內部請求格式
            InternalTermsAgreementRequest internalRequest = InternalTermsAgreementRequest.builder()
                    .userId(request.getTaiwanId())
                    .termsVersion(request.getTermsVersion())
                    .agreedAt(request.getAgreedAt() != null ? request.getAgreedAt() : LocalDateTime.now())
                    .ipAddress(request.getIpAddress())
                    .userAgent(request.getUserAgent())
                    .build();
            
            // 執行同意程序
            TermsAgreementResult result = agreeToTermsInternal(internalRequest);
            
            // 轉換為Controller響應格式
            return TermsAgreementResponse.builder()
                    .success(result.getSuccess())
                    .agreementId(result.getAgreementId())
                    .agreedTermsVersion(result.getTermsVersion())
                    .agreedAt(result.getAgreedAt())
                    .trackingInfo(TermsAgreementResponse.AgreementTrackingInfo.builder()
                            .expiryDate(result.getExpiryDate())
                            .build())
                    .build();
            
        } catch (Exception e) {
            log.error("條款同意失敗: taiwanId={}, error={}", 
                    maskUserId(request.getTaiwanId()), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 查詢條款同意狀態 (用於Controller)
     */
    @Transactional(readOnly = true)
    public TermsAgreementResponse getTermsAgreementStatus(String taiwanId, String termsVersion) {
        log.info("查詢條款同意狀態: taiwanId={}, termsVersion={}", 
                maskUserId(taiwanId), termsVersion);
        
        try {
            TermsAgreementStatus status = checkTermsStatus(taiwanId);
            
            return TermsAgreementResponse.builder()
                    .success(status.getIsAgreed())
                    .agreementId(status.getAgreementId())
                    .agreedTermsVersion(status.getAgreedVersion())
                    .agreedAt(status.getAgreedAt())
                    .trackingInfo(TermsAgreementResponse.AgreementTrackingInfo.builder()
                            .expiryDate(status.getExpiryDate())
                            .status(status.getStatus())
                            .message(status.getMessage())
                            .requiresNewAgreement(status.getRequiresAgreement())
                            .build())
                    .build();
            
        } catch (Exception e) {
            log.error("查詢條款狀態失敗: taiwanId={}, error={}", 
                    maskUserId(taiwanId), e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 取得最新條款版本 (用於Controller)
     */
    @Transactional(readOnly = true)
    public String getLatestTermsVersion() {
        log.debug("取得最新條款版本");
        return CURRENT_TERMS_VERSION;
    }
    
    /**
     * 取得條款內容 (用於Controller)
     */
    @Transactional(readOnly = true)
    public Object getTermsContent(String version, String language) {
        log.info("取得條款內容: version={}, language={}", version, language);
        
        String targetVersion = version != null ? version : CURRENT_TERMS_VERSION;
        
        // TODO: 實作從數據庫或文件系統取得條款內容
        return Map.of(
                "version", targetVersion,
                "language", language,
                "title", "KGI銀行數位跨境匯款解付平台使用條款",
                "lastUpdated", "2025-06-01T00:00:00",
                "content", "條款內容將在實際環境中從數據庫讀取...",
                "contentUrl", "/api/ibr/terms/content/" + targetVersion + "/" + language
        );
    }
    
    /**
     * 同意使用條款 (內部版本)
     * 
     * @param request 條款同意請求
     * @return 條款同意結果
     */
    @Transactional
    public TermsAgreementResult agreeToTermsInternal(InternalTermsAgreementRequest request) {
        log.info("處理條款同意: userId={}, termsVersion={}", 
                maskUserId(request.getUserId()), request.getTermsVersion());
        
        try {
            // 1. 驗證請求
            validateTermsAgreementRequest(request);
            
            // 2. 檢查條款版本
            validateTermsVersion(request.getTermsVersion());
            
            // 3. 建立同意記錄
            String agreementId = generateAgreementId();
            TermsAgreementRecord record = createAgreementRecord(agreementId, request);
            
            // 4. 儲存同意記錄
            saveAgreementRecord(record);
            
            // 5. 建立響應
            TermsAgreementResult result = createAgreementResult(record);
            
            log.info("條款同意完成: agreementId={}, userId={}", 
                    agreementId, maskUserId(request.getUserId()));
            
            return result;
            
        } catch (BusinessException e) {
            log.error("條款同意業務異常: userId={}, error={}", 
                    maskUserId(request.getUserId()), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("條款同意系統異常: userId={}", maskUserId(request.getUserId()), e);
            throw new BusinessException("TERMS_AGREEMENT_ERROR", "條款同意系統異常: " + e.getMessage());
        }
    }
    
    /**
     * 檢查條款同意狀態
     * 
     * @param userId 使用者ID
     * @return 條款同意狀態
     */
    @Transactional(readOnly = true)
    public TermsAgreementStatus checkTermsStatus(String userId) {
        log.debug("檢查條款同意狀態: userId={}", maskUserId(userId));
        
        try {
            // 查詢最新的同意記錄
            TermsAgreementRecord latestRecord = findLatestAgreementRecord(userId);
            
            if (latestRecord == null) {
                return createNotAgreedStatus();
            }
            
            // 檢查是否過期
            if (isAgreementExpired(latestRecord)) {
                log.info("條款同意已過期: userId={}, agreementId={}", 
                        maskUserId(userId), latestRecord.getAgreementId());
                return createExpiredStatus(latestRecord);
            }
            
            // 檢查版本是否為最新
            if (!isLatestVersion(latestRecord.getTermsVersion())) {
                log.info("條款版本已更新: userId={}, currentVersion={}, latestVersion={}", 
                        maskUserId(userId), latestRecord.getTermsVersion(), CURRENT_TERMS_VERSION);
                return createOutdatedStatus(latestRecord);
            }
            
            return createValidStatus(latestRecord);
            
        } catch (Exception e) {
            log.error("檢查條款狀態異常: userId={}", maskUserId(userId), e);
            throw new BusinessException("TERMS_STATUS_CHECK_ERROR", "條款狀態檢查異常: " + e.getMessage());
        }
    }
    
    /**
     * 取得當前條款版本資訊
     * 
     * @return 條款版本資訊
     */
    @Transactional(readOnly = true)
    public TermsVersionInfo getCurrentTermsVersion() {
        log.debug("取得當前條款版本");
        
        return TermsVersionInfo.builder()
                .version(CURRENT_TERMS_VERSION)
                .title("KGI銀行數位跨境匯款解付平台使用條款")
                .lastUpdated(LocalDateTime.of(2025, 6, 1, 0, 0))
                .validityHours(AGREEMENT_VALIDITY_HOURS)
                .contentUrl("/terms/v1.0/content")
                .isRequired(true)
                .build();
    }
    
    /**
     * 驗證條款同意請求
     */
    private void validateTermsAgreementRequest(InternalTermsAgreementRequest request) {
        if (request == null) {
            throw new BusinessException("INVALID_REQUEST", "條款同意請求不能為空");
        }
        
        if (request.getTermsVersion() == null || request.getTermsVersion().trim().isEmpty()) {
            throw new BusinessException("INVALID_TERMS_VERSION", "條款版本不能為空");
        }
        
        if (request.getAgreedAt() == null) {
            throw new BusinessException("INVALID_AGREED_TIME", "同意時間不能為空");
        }
        
        // 檢查同意時間不能是未來時間
        if (request.getAgreedAt().isAfter(LocalDateTime.now())) {
            throw new BusinessException("INVALID_AGREED_TIME", "同意時間不能是未來時間");
        }
    }
    
    /**
     * 驗證條款版本
     */
    private void validateTermsVersion(String termsVersion) {
        if (!CURRENT_TERMS_VERSION.equals(termsVersion)) {
            throw new BusinessException("OUTDATED_TERMS_VERSION", 
                    String.format("條款版本已過期，請使用最新版本: %s", CURRENT_TERMS_VERSION));
        }
    }
    
    /**
     * 生成同意記錄ID
     */
    private String generateAgreementId() {
        return "TERMS_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 10000)).toUpperCase();
    }
    
    /**
     * 建立同意記錄
     */
    private TermsAgreementRecord createAgreementRecord(String agreementId, InternalTermsAgreementRequest request) {
        return TermsAgreementRecord.builder()
                .agreementId(agreementId)
                .userId(request.getUserId())
                .termsVersion(request.getTermsVersion())
                .agreedAt(request.getAgreedAt())
                .ipAddress(request.getIpAddress())
                .userAgent(request.getUserAgent())
                .expiryDate(request.getAgreedAt().plusHours(AGREEMENT_VALIDITY_HOURS))
                .isActive(true)
                .createdAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * 儲存同意記錄
     */
    private void saveAgreementRecord(TermsAgreementRecord record) {
        // 實際環境應儲存到數據庫
        agreementRecords.put(record.getUserId() + "_" + record.getTermsVersion(), record);
        log.debug("條款同意記錄已儲存: agreementId={}", record.getAgreementId());
    }
    
    /**
     * 查詢最新同意記錄
     */
    private TermsAgreementRecord findLatestAgreementRecord(String userId) {
        if (userId == null) {
            return null;
        }
        
        // 實際環境應從數據庫查詢
        return agreementRecords.get(userId + "_" + CURRENT_TERMS_VERSION);
    }
    
    /**
     * 檢查同意是否過期
     */
    private boolean isAgreementExpired(TermsAgreementRecord record) {
        return record.getExpiryDate() != null && LocalDateTime.now().isAfter(record.getExpiryDate());
    }
    
    /**
     * 檢查是否為最新版本
     */
    private boolean isLatestVersion(String version) {
        return CURRENT_TERMS_VERSION.equals(version);
    }
    
    /**
     * 建立同意結果
     */
    private TermsAgreementResult createAgreementResult(TermsAgreementRecord record) {
        return TermsAgreementResult.builder()
                .agreementId(record.getAgreementId())
                .success(true)
                .agreedAt(record.getAgreedAt())
                .expiryDate(record.getExpiryDate())
                .message("條款同意成功")
                .termsVersion(record.getTermsVersion())
                .build();
    }
    
    /**
     * 建立未同意狀態
     */
    private TermsAgreementStatus createNotAgreedStatus() {
        return TermsAgreementStatus.builder()
                .isAgreed(false)
                .status("NOT_AGREED")
                .message("尚未同意使用條款")
                .currentVersion(CURRENT_TERMS_VERSION)
                .requiresAgreement(true)
                .build();
    }
    
    /**
     * 建立過期狀態
     */
    private TermsAgreementStatus createExpiredStatus(TermsAgreementRecord record) {
        return TermsAgreementStatus.builder()
                .isAgreed(false)
                .agreementId(record.getAgreementId())
                .agreedAt(record.getAgreedAt())
                .expiryDate(record.getExpiryDate())
                .status("EXPIRED")
                .message("條款同意已過期，請重新同意")
                .currentVersion(CURRENT_TERMS_VERSION)
                .requiresAgreement(true)
                .build();
    }
    
    /**
     * 建立版本過舊狀態
     */
    private TermsAgreementStatus createOutdatedStatus(TermsAgreementRecord record) {
        return TermsAgreementStatus.builder()
                .isAgreed(false)
                .agreementId(record.getAgreementId())
                .agreedAt(record.getAgreedAt())
                .expiryDate(record.getExpiryDate())
                .status("OUTDATED")
                .message("條款版本已更新，請同意最新版本")
                .currentVersion(CURRENT_TERMS_VERSION)
                .agreedVersion(record.getTermsVersion())
                .requiresAgreement(true)
                .build();
    }
    
    /**
     * 建立有效狀態
     */
    private TermsAgreementStatus createValidStatus(TermsAgreementRecord record) {
        return TermsAgreementStatus.builder()
                .isAgreed(true)
                .agreementId(record.getAgreementId())
                .agreedAt(record.getAgreedAt())
                .expiryDate(record.getExpiryDate())
                .status("VALID")
                .message("條款同意有效")
                .currentVersion(CURRENT_TERMS_VERSION)
                .agreedVersion(record.getTermsVersion())
                .requiresAgreement(false)
                .build();
    }
    
    /**
     * 遮罩使用者ID
     */
    private String maskUserId(String userId) {
        if (userId == null || userId.length() < 6) {
            return userId;
        }
        return userId.substring(0, 3) + "***" + userId.substring(userId.length() - 2);
    }
    
    // ==================== 請求和響應類別 ====================
    
    /**
     * 內部條款同意請求
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class InternalTermsAgreementRequest {
        private String userId;
        private String termsVersion;
        private LocalDateTime agreedAt;
        private String ipAddress;
        private String userAgent;
    }
    
    /**
     * 條款同意結果
     */
    @lombok.Data
    @lombok.Builder
    public static class TermsAgreementResult {
        private String agreementId;
        private Boolean success;
        private LocalDateTime agreedAt;
        private LocalDateTime expiryDate;
        private String message;
        private String termsVersion;
    }
    
    /**
     * 條款同意狀態
     */
    @lombok.Data
    @lombok.Builder
    public static class TermsAgreementStatus {
        private Boolean isAgreed;
        private String agreementId;
        private LocalDateTime agreedAt;
        private LocalDateTime expiryDate;
        private String status;
        private String message;
        private String currentVersion;
        private String agreedVersion;
        private Boolean requiresAgreement;
    }
    
    /**
     * 條款版本資訊
     */
    @lombok.Data
    @lombok.Builder
    public static class TermsVersionInfo {
        private String version;
        private String title;
        private LocalDateTime lastUpdated;
        private Integer validityHours;
        private String contentUrl;
        private Boolean isRequired;
    }
    
    /**
     * 條款同意記錄 (內部使用)
     */
    @lombok.Data
    @lombok.Builder
    private static class TermsAgreementRecord {
        private String agreementId;
        private String userId;
        private String termsVersion;
        private LocalDateTime agreedAt;
        private String ipAddress;
        private String userAgent;
        private LocalDateTime expiryDate;
        private Boolean isActive;
        private LocalDateTime createdAt;
    }
}