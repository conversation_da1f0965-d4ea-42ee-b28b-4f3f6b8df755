package com.kgi.module.individual.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 自然人解款請求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualRemittanceRequest {
    
    /**
     * 跨境平台參考編號
     */
    @NotBlank(message = "跨境平台參考編號不能為空")
    @Size(max = 50, message = "跨境平台參考編號長度不能超過50字元")
    private String theirRefNo;
    
    /**
     * 匯款參考編號
     */
    @Size(max = 50, message = "匯款參考編號長度不能超過50字元")
    private String remitRefNo;
    
    /**
     * 匯款人姓名
     */
    @NotBlank(message = "匯款人姓名不能為空")
    @Size(max = 100, message = "匯款人姓名長度不能超過100字元")
    private String payerName;
    
    /**
     * 幣別
     */
    @NotBlank(message = "幣別不能為空")
    @Pattern(regexp = "^[A-Z]{3}$", message = "幣別格式錯誤，應為3位大寫字母")
    private String currency;
    
    /**
     * 匯款金額
     */
    @NotNull(message = "匯款金額不能為空")
    @DecimalMin(value = "0.01", message = "匯款金額必須大於0")
    @Digits(integer = 12, fraction = 2, message = "匯款金額格式錯誤")
    private BigDecimal amount;
    
    /**
     * 收款人中文姓名
     */
    @NotBlank(message = "收款人中文姓名不能為空")
    @Size(max = 50, message = "收款人中文姓名長度不能超過50字元")
    private String payeeChineseName;
    
    /**
     * 收款人英文姓名
     */
    @NotBlank(message = "收款人英文姓名不能為空")
    @Size(max = 100, message = "收款人英文姓名長度不能超過100字元")
    private String payeeEnglishName;
    
    /**
     * 收款人身分證號
     */
    @NotBlank(message = "收款人身分證號不能為空")
    @Pattern(regexp = "^[A-Z][12]\\d{8}$", message = "身分證號格式錯誤")
    private String payeeId;
    
    /**
     * 收款人銀行帳號
     */
    @NotBlank(message = "收款人銀行帳號不能為空")
    @Size(max = 20, message = "銀行帳號長度不能超過20字元")
    private String payeeAccount;
    
    /**
     * 收款人銀行代碼
     */
    @NotBlank(message = "收款人銀行代碼不能為空")
    @Pattern(regexp = "^\\d{3}$", message = "銀行代碼格式錯誤，應為3位數字")
    private String payeeBankCode;
    
    /**
     * 收款人分行代碼
     */
    @Size(max = 10, message = "分行代碼長度不能超過10字元")
    private String payeeBranchCode;
    
    /**
     * 收款人聯絡電話
     */
    @Pattern(regexp = "^09\\d{8}$", message = "手機號碼格式錯誤")
    private String payeePhone;
    
    /**
     * 收款人電子郵件
     */
    @Email(message = "電子郵件格式錯誤")
    @Size(max = 100, message = "電子郵件長度不能超過100字元")
    private String payeeEmail;
    
    /**
     * 資金來源代碼
     */
    @NotBlank(message = "資金來源代碼不能為空")
    @Pattern(regexp = "^\\d{3}$", message = "資金來源代碼格式錯誤，應為3位數字")
    private String sourceOfFund;
    
    /**
     * 匯款性質代碼
     */
    @Size(max = 10, message = "匯款性質代碼長度不能超過10字元")
    private String remittancePurpose;
    
    /**
     * 緊急處理標示
     */
    @Builder.Default
    private Boolean isUrgent = false;
    
    /**
     * 客戶備註
     */
    @Size(max = 500, message = "客戶備註長度不能超過500字元")
    private String customerNote;
    
    /**
     * 驗證收款人身分證號格式
     */
    public boolean isValidPayeeId() {
        if (payeeId == null || payeeId.length() != 10) {
            return false;
        }
        
        // 台灣身分證號驗證邏輯
        String pattern = "^[A-Z][12]\\d{8}$";
        return payeeId.matches(pattern);
    }
    
    /**
     * 取得遮罩後的身分證號
     */
    public String getMaskedPayeeId() {
        if (payeeId == null || payeeId.length() != 10) {
            return payeeId;
        }
        return payeeId.substring(0, 3) + "****" + payeeId.substring(7);
    }
    
    /**
     * 取得遮罩後的帳號
     */
    public String getMaskedPayeeAccount() {
        if (payeeAccount == null || payeeAccount.length() < 6) {
            return payeeAccount;
        }
        
        int length = payeeAccount.length();
        StringBuilder masked = new StringBuilder();
        
        // 顯示前3碼和後3碼，中間用*代替
        if (length <= 6) {
            return payeeAccount.substring(0, 3) + "***";
        } else {
            masked.append(payeeAccount.substring(0, 3));
            for (int i = 3; i < length - 3; i++) {
                masked.append("*");
            }
            masked.append(payeeAccount.substring(length - 3));
        }
        
        return masked.toString();
    }
}