package com.kgi.module.individual.application.usecase;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * OTP 驗證用例
 * 處理 OTP 相關業務邏輯
 */
@Slf4j
@Service("individualVerifyOtpUseCase")
@RequiredArgsConstructor
public class VerifyOtpUseCase {
    
    /**
     * 重新發送 OTP
     */
    public void resendOtp(String remittanceId) {
        log.info("重新發送 OTP: remittanceId={}", remittanceId);
        
        // TODO: 實作 OTP 重發邏輯
        // 1. 驗證解款ID存在
        // 2. 檢查重發限制
        // 3. 生成新的OTP
        // 4. 發送簡訊
        
        log.info("OTP 重發成功: remittanceId={}", remittanceId);
    }
}