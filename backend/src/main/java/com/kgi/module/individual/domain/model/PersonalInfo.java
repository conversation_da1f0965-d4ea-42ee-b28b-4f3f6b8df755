package com.kgi.module.individual.domain.model;

import com.kgi.core.domain.model.TaiwanId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Embedded;
import java.time.LocalDate;

/**
 * 個人資訊值對象
 * 包含自然人的基本資訊
 */
@Data
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class PersonalInfo {
    
    /**
     * 台灣身分證號
     */
    @Embedded
    private TaiwanId taiwanId;
    
    /**
     * 中文姓名
     */
    @Column(name = "chinese_name", nullable = false, length = 50)
    private String chineseName;
    
    /**
     * 英文姓名
     */
    @Column(name = "english_name", nullable = false, length = 100)
    private String englishName;
    
    /**
     * 出生日期
     */
    @Column(name = "birth_date")
    private LocalDate birthDate;
    
    /**
     * 聯絡電話
     */
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;
    
    /**
     * 電子郵件
     */
    @Column(name = "email", length = 100)
    private String email;
    
    /**
     * 銀行帳戶資訊
     */
    @Embedded
    private BankAccount bankAccount;
    
    /**
     * 驗證姓名是否一致
     */
    public boolean isNameConsistent(String inputChineseName, String inputEnglishName) {
        return this.chineseName.equals(inputChineseName) && 
               this.englishName.equals(inputEnglishName);
    }
    
    /**
     * 取得遮罩身分證號
     */
    public String getMaskedId() {
        return taiwanId != null ? taiwanId.getMasked() : null;
    }
}