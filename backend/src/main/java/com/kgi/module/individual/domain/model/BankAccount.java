package com.kgi.module.individual.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

/**
 * 銀行帳戶值對象
 */
@Data
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class BankAccount {
    
    /**
     * 銀行代碼
     */
    @Column(name = "bank_code", length = 10)
    private String bankCode;
    
    /**
     * 分行代碼
     */
    @Column(name = "branch_code", length = 10)
    private String branchCode;
    
    /**
     * 帳戶號碼
     */
    @Column(name = "account_number", length = 50)
    private String accountNumber;
    
    /**
     * 銀行名稱
     */
    @Column(name = "bank_name", length = 100)
    private String bankName;
    
    /**
     * 分行名稱
     */
    @Column(name = "branch_name", length = 100)
    private String branchName;
    
    /**
     * 取得遮罩帳號（顯示後三碼）
     */
    public String getMaskedAccountNumber() {
        if (accountNumber == null || accountNumber.length() < 3) {
            return accountNumber;
        }
        
        int length = accountNumber.length();
        StringBuilder masked = new StringBuilder();
        
        // 前面用*代替，保留後三碼
        for (int i = 0; i < length - 3; i++) {
            masked.append("*");
        }
        masked.append(accountNumber.substring(length - 3));
        
        return masked.toString();
    }
    
    /**
     * 取得完整銀行資訊描述
     */
    public String getFullBankInfo() {
        return String.format("%s %s (%s)", bankName, branchName, bankCode);
    }
}