package com.kgi.module.gateway.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * TO 數位解款 API Request DTO
 * 符合 API 規格書 v1.3 的欄位定義
 * 使用駝峰命名規範
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToApiRequestDTO {
    
    /**
     * 跨境平台編號
     */
    @NotBlank(message = "跨境平台編號不可為空")
    @Size(max = 20, message = "跨境平台編號長度不可超過20")
    private String TheirRefNo;
    
    /**
     * 匯入匯款編號
     */
    @NotBlank(message = "匯入匯款編號不可為空")
    @Size(max = 16, message = "匯入匯款編號長度不可超過16")
    private String RemitRefNo;
    
    /**
     * 匯款英文名
     */
    @NotBlank(message = "匯款英文名不可為空")
    @Size(max = 70, message = "匯款英文名長度不可超過70")
    private String PayerName;
    
    /**
     * 匯款國別
     */
    @NotBlank(message = "匯款國別不可為空")
    @Size(min = 2, max = 2, message = "匯款國別必須為2位")
    private String PayerCountry;
    
    /**
     * 匯款幣別
     */
    @NotBlank(message = "匯款幣別不可為空")
    @Size(min = 3, max = 3, message = "匯款幣別必須為3位")
    private String Currency;
    
    /**
     * 匯款金額
     */
    @NotBlank(message = "匯款金額不可為空")
    private String Amount;
    
    /**
     * 英文姓名
     */
    @NotBlank(message = "英文姓名不可為空")
    @Size(max = 70, message = "英文姓名長度不可超過70")
    private String PayeeEngName;
    
    /**
     * 收款人中文名稱
     */
    @NotBlank(message = "收款人中文名稱不可為空")
    @Size(max = 70, message = "收款人中文名稱長度不可超過70")
    private String PayeeName;
    
    /**
     * 收款人ID
     */
    @NotBlank(message = "收款人ID不可為空")
    @Size(max = 16, message = "收款人ID長度不可超過16")
    private String PayeeID;
    
    /**
     * 收款人帳號
     */
    @NotBlank(message = "收款人帳號不可為空")
    @Size(max = 35, message = "收款人帳號長度不可超過35")
    private String PayeeAccount;
    
    /**
     * 收款行代碼
     */
    @NotBlank(message = "收款行代碼不可為空")
    @Size(max = 7, message = "收款行代碼長度不可超過7")
    private String PayeeBankCode;
    
    /**
     * 收款人電話/手機
     */
    @NotBlank(message = "收款人電話不可為空")
    @Size(max = 20, message = "收款人電話長度不可超過20")
    private String PayeeTel;
    
    /**
     * 收款人信箱
     */
    @NotBlank(message = "收款人信箱不可為空")
    @Size(max = 40, message = "收款人信箱長度不可超過40")
    private String PayeeMail;
    
    /**
     * 匯款性質
     */
    @NotBlank(message = "匯款性質不可為空")
    @Size(min = 3, max = 3, message = "匯款性質必須為3位")
    private String SourceOfFund;
    
    /**
     * 白名單
     * 值=S時為補通訊用，只帶中文名字和兩個編號。其它為空
     */
    @Size(max = 1, message = "白名單標示長度不可超過1")
    private String WhileFlag;
    
    /**
     * 附言
     */
    @Size(max = 40, message = "附言長度不可超過40")
    private String Memo;
}