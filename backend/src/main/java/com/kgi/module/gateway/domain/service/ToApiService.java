package com.kgi.module.gateway.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * TO API 服務
 * 處理跨境平台接收的資料
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToApiService {
    
    private final CustomerTypeIdentificationService customerTypeService;
    
    /**
     * 驗證TO API資料格式 (14個必要欄位)
     */
    public void validateToApiData(Map<String, Object> toApiData) {
        String[] requiredFields = {
            "TheirRefNo", "RemitRefNo", "PayerName", "Currency", "Amount",
            "PayeeEngName", "PayeeName", "PayeeID", "PayeeAccount", 
            "PayeeBankCode", "PayeeTel", "PayeeMail", "SourceOfFund", "WhileFlag"
        };
        
        for (String field : requiredFields) {
            if (!toApiData.containsKey(field) || toApiData.get(field) == null) {
                throw new IllegalArgumentException("必要欄位缺失: " + field);
            }
        }
        
        // 額外驗證欄位格式
        validateFieldFormats(toApiData);
    }
    
    /**
     * 生成系統案件編號
     */
    public String generateCaseNumber() {
        // 格式: IBR + 年月日 + 流水號
        String dateStr = LocalDateTime.now().toString()
                .replace("-", "")
                .replace(":", "")
                .replace("T", "")
                .substring(0, 8);
        String sequence = String.format("%04d", System.currentTimeMillis() % 10000);
        return "IBR" + dateStr + sequence;
    }
    
    /**
     * 存儲TO API資料
     */
    public Map<String, Object> storeToApiData(Map<String, Object> toApiData, String caseNo) {
        log.info("存儲TO API資料: caseNo={}", caseNo);
        
        Map<String, Object> storageInfo = new HashMap<>();
        storageInfo.put("caseNo", caseNo);
        storageInfo.put("originalData", toApiData);
        storageInfo.put("storedTime", LocalDateTime.now().toString());
        storageInfo.put("status", "STORED");
        storageInfo.put("dataHash", generateDataHash(toApiData));
        
        // TODO: 實際存儲到資料庫
        
        return storageInfo;
    }
    
    /**
     * 處理TO API資料
     */
    public Map<String, Object> processToApiData(Map<String, Object> toApiData) {
        // 1. 驗證資料
        validateToApiData(toApiData);
        
        // 2. 生成案件編號
        String caseNo = generateCaseNumber();
        
        // 3. 確定客戶類型
        String payeeId = (String) toApiData.get("PayeeID");
        String customerType = customerTypeService.determineCustomerType(payeeId);
        
        // 4. 存儲資料
        Map<String, Object> storageInfo = storeToApiData(toApiData, caseNo);
        
        // 5. 組合處理結果
        Map<String, Object> result = new HashMap<>();
        result.put("caseNo", caseNo);
        result.put("customerType", customerType);
        result.put("storageInfo", storageInfo);
        result.put("processedTime", LocalDateTime.now().toString());
        
        return result;
    }
    
    /**
     * 取得原始TO API資料
     */
    public Map<String, Object> getOriginalToApiData(String caseNo) {
        log.info("取得原始TO API資料: caseNo={}", caseNo);
        
        // TODO: 從資料庫查詢
        // 模擬資料
        Map<String, Object> data = new HashMap<>();
        data.put("caseNo", caseNo);
        data.put("TheirRefNo", "CB2025010101");
        data.put("RemitRefNo", "RM2025010101");
        data.put("PayerName", "JOHN DOE");
        data.put("Currency", "USD");
        data.put("Amount", 50000);
        data.put("PayeeEngName", "WANG DAMING");
        data.put("PayeeName", "王大明");
        data.put("PayeeID", "A123456789");
        data.put("PayeeAccount", "**********");
        data.put("PayeeBankCode", "812");
        data.put("PayeeTel", "**********");
        data.put("PayeeMail", "<EMAIL>");
        data.put("SourceOfFund", "TRADE");
        data.put("WhileFlag", "N");
        
        return data;
    }
    
    /**
     * 驗證欄位格式
     */
    private void validateFieldFormats(Map<String, Object> toApiData) {
        // 驗證金額
        Object amount = toApiData.get("Amount");
        if (!(amount instanceof Number) || ((Number) amount).doubleValue() <= 0) {
            throw new IllegalArgumentException("金額必須為正數");
        }
        
        // 驗證幣別
        String currency = (String) toApiData.get("Currency");
        if (!currency.matches("[A-Z]{3}")) {
            throw new IllegalArgumentException("幣別格式錯誤");
        }
        
        // 驗證Email
        String email = (String) toApiData.get("PayeeMail");
        if (!email.contains("@")) {
            throw new IllegalArgumentException("Email格式錯誤");
        }
        
        // 驗證電話
        String phone = (String) toApiData.get("PayeeTel");
        if (!phone.matches("\\d{10,15}")) {
            throw new IllegalArgumentException("電話號碼格式錯誤");
        }
    }
    
    /**
     * 生成資料雜湊值
     */
    private String generateDataHash(Map<String, Object> data) {
        // 簡單的雜湊實現
        return String.valueOf(data.hashCode());
    }
}