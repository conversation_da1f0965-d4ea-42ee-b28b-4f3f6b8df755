package com.kgi.module.gateway.domain.service;

import org.springframework.stereotype.Service;

/**
 * 客戶類型識別服務
 */
@Service
public class CustomerTypeIdentificationService {
    
    /**
     * 根據PayeeID確定客戶類型
     * 
     * @param payeeId 收款人ID
     * @return INDIVIDUAL (自然人) 或 CORPORATE (法人)
     */
    public String determineCustomerType(String payeeId) {
        if (payeeId == null || payeeId.trim().isEmpty()) {
            throw new IllegalArgumentException("PayeeID不能為空");
        }
        
        payeeId = payeeId.trim().toUpperCase();
        
        // 自然人: 身分證字號 (10位，第一位英文)
        if (isIndividualId(payeeId)) {
            return "INDIVIDUAL";
        }
        // 法人: 統一編號 (8位數字)
        else if (isCorporateId(payeeId)) {
            return "CORPORATE";
        }
        else {
            throw new IllegalArgumentException("無法識別的PayeeID格式: " + payeeId);
        }
    }
    
    /**
     * 檢查是否為身分證號
     */
    private boolean isIndividualId(String id) {
        if (id.length() != 10) {
            return false;
        }
        
        // 第一位必須是英文字母
        if (!Character.isLetter(id.charAt(0))) {
            return false;
        }
        
        // 第二位必須是1或2
        char secondChar = id.charAt(1);
        if (secondChar != '1' && secondChar != '2') {
            return false;
        }
        
        // 其餘8位必須是數字
        for (int i = 2; i < 10; i++) {
            if (!Character.isDigit(id.charAt(i))) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 檢查是否為統一編號
     */
    private boolean isCorporateId(String id) {
        if (id.length() != 8) {
            return false;
        }
        
        // 全部必須是數字
        for (char c : id.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 取得客戶類型的中文描述
     */
    public String getCustomerTypeDescription(String customerType) {
        switch (customerType) {
            case "INDIVIDUAL":
                return "自然人";
            case "CORPORATE":
                return "法人";
            default:
                return "未知";
        }
    }
}