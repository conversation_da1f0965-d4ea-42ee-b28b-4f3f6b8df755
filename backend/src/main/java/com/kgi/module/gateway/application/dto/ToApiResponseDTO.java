package com.kgi.module.gateway.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TO 數位解款 API Response DTO
 * 符合 API 規格書 v1.3 的欄位定義
 * 使用駝峰命名規範
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToApiResponseDTO {
    
    /**
     * 狀態碼
     * 00：成功
     * 99：失敗
     */
    private String StatusCode;
    
    /**
     * 回覆訊息
     */
    private String TxntMsg;
    
    /**
     * 預留
     */
    private String Remark;
    
    /**
     * 檢查是否成功
     */
    public boolean isSuccess() {
        return "00".equals(StatusCode);
    }
    
    /**
     * 建立成功回應
     */
    public static ToApiResponseDTO success() {
        return ToApiResponseDTO.builder()
                .StatusCode("00")
                .TxntMsg("交易成功")
                .Remark("")
                .build();
    }
    
    /**
     * 建立成功回應（含訊息）
     */
    public static ToApiResponseDTO success(String message) {
        return ToApiResponseDTO.builder()
                .StatusCode("00")
                .TxntMsg(message)
                .Remark("")
                .build();
    }
    
    /**
     * 建立失敗回應
     */
    public static ToApiResponseDTO failure(String message) {
        return ToApiResponseDTO.builder()
                .StatusCode("99")
                .TxntMsg(message)
                .Remark("")
                .build();
    }
    
    /**
     * 建立失敗回應（含備註）
     */
    public static ToApiResponseDTO failure(String message, String remark) {
        return ToApiResponseDTO.builder()
                .StatusCode("99")
                .TxntMsg(message)
                .Remark(remark)
                .build();
    }
}