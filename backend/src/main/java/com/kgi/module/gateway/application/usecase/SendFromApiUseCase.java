package com.kgi.module.gateway.application.usecase;

import com.kgi.module.gateway.application.dto.request.FromApiRequest;
import com.kgi.module.gateway.application.dto.response.FromApiResponse;
import com.kgi.module.gateway.domain.service.FromApiService;
import com.kgi.module.gateway.domain.service.ToApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 發送FROM API資料用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendFromApiUseCase {
    
    private final ToApiService toApiService;
    private final FromApiService fromApiService;
    
    public FromApiResponse execute(FromApiRequest request) {
        log.info("執行發送FROM API資料: caseNo={}", request.getCaseNo());
        
        // 1. 取得原始TO API資料
        Map<String, Object> originalToData = toApiService.getOriginalToApiData(request.getCaseNo());
        
        // 2. 取得客戶補充資料
        Map<String, Object> customerData = getCustomerApplicationData(request.getApplicationId());
        
        // 3. 整合所有資料
        Map<String, Object> integratedData = fromApiService.integrateDataForFromApi(
                originalToData, customerData);
        
        // 4. 呼叫跨境平台FROM API
        Map<String, Object> fromApiResult = fromApiService.callCrossBorderFromApi(integratedData);
        
        // 5. 建立回應
        return FromApiResponse.builder()
                .caseNo(request.getCaseNo())
                .applicationId(request.getApplicationId())
                .integratedData(integratedData)
                .fromApiResult(fromApiResult)
                .sentTime(LocalDateTime.now().toString())
                .status("SUCCESS")
                .message("FROM API資料發送成功")
                .build();
    }
    
    /**
     * 取得客戶申請資料
     * TODO: 應該從資料庫查詢
     */
    private Map<String, Object> getCustomerApplicationData(String applicationId) {
        return Map.of(
            "applicationId", applicationId,
            "remittanceNature", "TRADE_PAYMENT",
            "verificationStatus", "VERIFIED",
            "additionalInfo", Map.of(
                "purpose", "貿易付款",
                "description", "商品採購款"
            )
        );
    }
}