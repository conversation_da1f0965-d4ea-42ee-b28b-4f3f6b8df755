package com.kgi.module.gateway.application.usecase;

import com.kgi.module.gateway.application.dto.request.SupplementFromApiRequest;
import com.kgi.module.gateway.application.dto.response.SupplementFromApiResponse;
import com.kgi.module.gateway.domain.service.FromApiService;
import com.kgi.module.gateway.domain.service.ToApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 發送補件FROM API資料用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendSupplementFromApiUseCase {
    
    private final ToApiService toApiService;
    private final FromApiService fromApiService;
    
    public SupplementFromApiResponse execute(SupplementFromApiRequest request) {
        log.info("執行發送補件FROM API資料: caseNo={}, supplementId={}", 
                request.getCaseNo(), request.getSupplementId());
        
        // 1. 取得補件資料
        Map<String, Object> supplementData = getSupplementData(request.getSupplementId());
        
        // 2. 取得原始案件資料
        Map<String, Object> originalData = toApiService.getOriginalToApiData(request.getCaseNo());
        
        // 3. 整合補件資料
        Map<String, Object> correctedData = fromApiService.integrateCorrectedData(
                originalData, supplementData);
        
        // 4. 發送補件FROM API到跨境平台
        Map<String, Object> supplementFromApiResult = 
                fromApiService.callCrossBorderSupplementFromApi(correctedData);
        
        // 5. 建立回應
        return SupplementFromApiResponse.builder()
                .caseNo(request.getCaseNo())
                .supplementId(request.getSupplementId())
                .correctedData(correctedData)
                .fromApiResult(supplementFromApiResult)
                .sentTime(LocalDateTime.now().toString())
                .status("SUCCESS")
                .message("補件FROM API發送成功")
                .build();
    }
    
    /**
     * 取得補件資料
     * TODO: 應該從資料庫查詢
     */
    private Map<String, Object> getSupplementData(String supplementId) {
        return Map.of(
            "supplementId", supplementId,
            "correctedFields", Map.of(
                "PayeeName", "王大明",
                "PayeeAccount", "**********"
            ),
            "supplementTime", LocalDateTime.now().toString()
        );
    }
}