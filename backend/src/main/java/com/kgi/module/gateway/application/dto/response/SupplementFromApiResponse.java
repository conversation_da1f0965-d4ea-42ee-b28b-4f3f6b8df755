package com.kgi.module.gateway.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 補件FROM API響應
 */
@Data
@Builder
public class SupplementFromApiResponse {
    private String caseNo;
    private String supplementId;
    private Map<String, Object> correctedData;
    private Map<String, Object> fromApiResult;
    private String sentTime;
    private String status;
    private String message;
}