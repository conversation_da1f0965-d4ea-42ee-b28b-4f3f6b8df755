package com.kgi.module.gateway.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FROM 數位解款 API Response DTO
 * 符合 API 規格書 v1.3 的欄位定義
 * 使用駝峰命名規範
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FromApiResponseDTO {
    
    /**
     * 狀態碼
     * 00：成功
     * 99：失敗
     */
    private String StatusCode;
    
    /**
     * 回覆訊息
     */
    private String TxntMsg;
    
    /**
     * 預留
     */
    private String Remark;
    
    /**
     * 檢查是否成功
     */
    public boolean isSuccess() {
        return "00".equals(StatusCode);
    }
    
    /**
     * 建立成功回應
     */
    public static FromApiResponseDTO success() {
        return FromApiResponseDTO.builder()
                .StatusCode("00")
                .TxntMsg("解款資訊更新成功")
                .Remark("")
                .build();
    }
    
    /**
     * 建立成功回應（含訊息）
     */
    public static FromApiResponseDTO success(String message) {
        return FromApiResponseDTO.builder()
                .StatusCode("00")
                .TxntMsg(message)
                .Remark("")
                .build();
    }
    
    /**
     * 建立失敗回應
     */
    public static FromApiResponseDTO failure(String message) {
        return FromApiResponseDTO.builder()
                .StatusCode("99")
                .TxntMsg(message)
                .Remark("")
                .build();
    }
    
    /**
     * 建立失敗回應（含備註）
     */
    public static FromApiResponseDTO failure(String message, String remark) {
        return FromApiResponseDTO.builder()
                .StatusCode("99")
                .TxntMsg(message)
                .Remark(remark)
                .build();
    }
}