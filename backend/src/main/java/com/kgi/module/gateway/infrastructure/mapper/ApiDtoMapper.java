package com.kgi.module.gateway.infrastructure.mapper;

import com.kgi.module.gateway.application.dto.FromApiRequestDTO;
import com.kgi.module.gateway.application.dto.ToApiRequestDTO;
import com.kgi.module.individual.infrastructure.entity.IndividualRemittanceEntity;
import com.kgi.module.corporate.infrastructure.entity.CorporateRemittanceEntity;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * API DTO 與實體之間的映射器
 * 處理欄位名稱轉換和資料格式轉換
 */
@Component
public class ApiDtoMapper {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 將 TO API Request DTO 映射到個人解款實體
     */
    public void mapToIndividualEntity(ToApiRequestDTO dto, IndividualRemittanceEntity entity) {
        // 基本資訊
        entity.setTheirRefNo(dto.getTheirRefNo());
        entity.setRemitRefNo(dto.getRemitRefNo());
        entity.setPayerName(dto.getPayerName());
        entity.setPayerCountry(dto.getPayerCountry());
        entity.setCurrency(dto.getCurrency());
        entity.setAmount(new BigDecimal(dto.getAmount()));
        
        // 收款人資訊（欄位名稱轉換）
        entity.setBeneficiaryEnglishName(dto.getPayeeEngName());
        entity.setBeneficiaryChineseName(dto.getPayeeName());
        entity.setBeneficiaryTaiwanId(dto.getPayeeID());
        entity.setBeneficiaryAccountNumber(dto.getPayeeAccount());
        entity.setBeneficiaryBankCode(dto.getPayeeBankCode());
        entity.setBeneficiaryPhone(dto.getPayeeTel());
        entity.setBeneficiaryEmail(dto.getPayeeMail());
        
        // 其他資訊
        entity.setSourceOfFund(dto.getSourceOfFund());
        entity.setWhileFlag(dto.getWhileFlag());
        entity.setMemo(dto.getMemo());
    }
    
    /**
     * 將 TO API Request DTO 映射到企業解款實體
     */
    public void mapToCorporateEntity(ToApiRequestDTO dto, CorporateRemittanceEntity entity) {
        // 基本資訊
        entity.setTheirRefNo(dto.getTheirRefNo());
        entity.setRemitRefNo(dto.getRemitRefNo());
        entity.setPayerName(dto.getPayerName());
        entity.setPayerCountry(dto.getPayerCountry());
        entity.setCurrency(dto.getCurrency());
        entity.setAmount(new BigDecimal(dto.getAmount()));
        
        // 收款人資訊（欄位名稱轉換）
        entity.setPayeeEngName(dto.getPayeeEngName());
        entity.setBeneficiaryName(dto.getPayeeName());
        entity.setPayeeID(dto.getPayeeID());
        entity.setBeneficiaryAccount(dto.getPayeeAccount());
        entity.setBeneficiaryBankCode(dto.getPayeeBankCode());
        entity.setPayeeTel(dto.getPayeeTel());
        entity.setPayeeMail(dto.getPayeeMail());
        
        // 其他資訊
        entity.setSourceOfFunds(dto.getSourceOfFund()); // 注意：企業實體用 sourceOfFunds（多了s）
        entity.setWhileFlag(dto.getWhileFlag());
        entity.setMemo(dto.getMemo());
    }
    
    /**
     * 從個人解款實體建立 FROM API Request DTO
     */
    public FromApiRequestDTO createFromApiRequest(IndividualRemittanceEntity entity) {
        return FromApiRequestDTO.builder()
                .TheirRefNo(entity.getTheirRefNo())
                .RemitRefNo(entity.getRemitRefNo())
                .PayerName(entity.getPayerName())
                .Currency(entity.getCurrency())
                .Amount(entity.getAmount().toString())
                .PayeeEngName(entity.getBeneficiaryEnglishName())
                .EngNameYN(entity.getEngNameYN())
                .ResidenceDateBegin(entity.getResidenceDateBegin())
                .ResidenceDateEnd(entity.getResidenceDateEnd())
                .BOD(entity.getBod())
                .PayeeName(entity.getBeneficiaryChineseName())
                .PayeeID(entity.getBeneficiaryTaiwanId())
                .PayeeAccount(entity.getBeneficiaryAccountNumber())
                .PayeeBankCode(entity.getBeneficiaryBankCode())
                .PayeeTel(entity.getBeneficiaryPhone())
                .PayeeMail(entity.getBeneficiaryEmail())
                .SourceOfFund(entity.getSourceOfFund())
                .Detail(entity.getDetail())
                .Signature(entity.getSignature())
                .WhileFlag(entity.getWhileFlag())
                .ReleaseDate(entity.getReleaseDate())
                .build();
    }
    
    /**
     * 從企業解款實體建立 FROM API Request DTO
     */
    public FromApiRequestDTO createFromApiRequest(CorporateRemittanceEntity entity) {
        return FromApiRequestDTO.builder()
                .TheirRefNo(entity.getTheirRefNo())
                .RemitRefNo(entity.getRemitRefNo())
                .PayerName(entity.getPayerName())
                .Currency(entity.getCurrency())
                .Amount(entity.getAmount().toString())
                .PayeeEngName(entity.getPayeeEngName())
                .PayeeName(entity.getBeneficiaryName())
                .PayeeID(entity.getPayeeID())
                .PayeeAccount(entity.getBeneficiaryAccount())
                .PayeeBankCode(entity.getBeneficiaryBankCode())
                .PayeeTel(entity.getPayeeTel())
                .PayeeMail(entity.getPayeeMail())
                .SourceOfFund(entity.getSourceOfFunds()) // 注意：企業實體用 sourceOfFunds
                .WhileFlag(entity.getWhileFlag())
                .ReleaseDate(LocalDate.now().format(DATE_FORMATTER)) // 使用當前日期
                .build();
    }
    
    /**
     * 將 FROM API 請求的資料更新到個人解款實體
     */
    public void updateIndividualEntity(FromApiRequestDTO dto, IndividualRemittanceEntity entity) {
        entity.setEngNameYN(dto.getEngNameYN());
        entity.setResidenceDateBegin(dto.getResidenceDateBegin());
        entity.setResidenceDateEnd(dto.getResidenceDateEnd());
        entity.setBod(dto.getBOD());
        entity.setDetail(dto.getDetail());
        entity.setSignature(dto.getSignature());
        entity.setReleaseDate(dto.getReleaseDate());
    }
}