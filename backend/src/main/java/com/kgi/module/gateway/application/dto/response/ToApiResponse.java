package com.kgi.module.gateway.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * TO API響應
 */
@Data
@Builder
public class ToApiResponse {
    private boolean success;
    private String caseNo;
    private String customerType;
    private Map<String, Object> storageInfo;
    private Map<String, String> customerUrls;
    private Map<String, Object> notificationSent;
    private String processedTime;
    private String message;
}