package com.kgi.module.gateway.application.usecase;

import com.kgi.module.gateway.application.dto.request.ToApiRequest;
import com.kgi.module.gateway.application.dto.response.ToApiResponse;
import com.kgi.module.gateway.domain.service.CustomerTypeIdentificationService;
import com.kgi.core.domain.service.NotificationService;
import com.kgi.module.gateway.domain.service.ToApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 接收TO API資料用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReceiveToApiUseCase {
    
    private final ToApiService toApiService;
    private final CustomerTypeIdentificationService customerTypeService;
    private final NotificationService notificationService;
    
    public ToApiResponse execute(ToApiRequest request) {
        log.info("執行接收TO API資料");
        
        Map<String, Object> toApiData = request.getToApiData();
        
        // 1. 處理TO API資料（驗證、生成案號、存儲）
        Map<String, Object> processResult = toApiService.processToApiData(toApiData);
        
        String caseNo = (String) processResult.get("caseNo");
        String customerType = (String) processResult.get("customerType");
        
        // 2. 生成客戶專屬URL
        Map<String, String> customerUrls = generateCustomerUrls(caseNo, customerType);
        
        // 3. 發送通知給客戶
        Map<String, Object> notificationResult = notificationService.sendCustomerNotification(
                toApiData, caseNo, customerUrls);
        
        // 4. 建立回應
        return ToApiResponse.builder()
                .success(true)
                .caseNo(caseNo)
                .customerType(customerType)
                .storageInfo((Map<String, Object>) processResult.get("storageInfo"))
                .customerUrls(customerUrls)
                .notificationSent(notificationResult)
                .processedTime(LocalDateTime.now().toString())
                .message("跨境TO API資料接收成功，客戶通知已發送")
                .build();
    }
    
    /**
     * 生成客戶專屬URL連結
     */
    private Map<String, String> generateCustomerUrls(String caseNo, String customerType) {
        String baseUrl = "https://ibr.kgibank.com";
        String moduleType = customerType.equals("INDIVIDUAL") ? "individual" : "corporate";
        
        return Map.of(
            "applicationUrl", String.format("%s/%s?type=A&case=%s", baseUrl, moduleType, caseNo),
            "supplementUrl", String.format("%s/%s?type=S&case=%s", baseUrl, moduleType, caseNo),
            "queryUrl", String.format("%s/query?type=Q&case=%s", baseUrl, caseNo)
        );
    }
}