package com.kgi.module.gateway.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * FROM 數位解款 API Request DTO
 * 符合 API 規格書 v1.3 的欄位定義
 * 使用駝峰命名規範
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FromApiRequestDTO {
    
    /**
     * 跨境平台編號
     */
    @NotBlank(message = "跨境平台編號不可為空")
    @Size(max = 20, message = "跨境平台編號長度不可超過20")
    private String TheirRefNo;
    
    /**
     * 匯入匯款編號
     */
    @NotBlank(message = "匯入匯款編號不可為空")
    @Size(max = 16, message = "匯入匯款編號長度不可超過16")
    private String RemitRefNo;
    
    /**
     * 匯款英文名
     */
    @NotBlank(message = "匯款英文名不可為空")
    @Size(max = 70, message = "匯款英文名長度不可超過70")
    private String PayerName;
    
    /**
     * 匯款幣別
     */
    @NotBlank(message = "匯款幣別不可為空")
    @Size(min = 3, max = 3, message = "匯款幣別必須為3位")
    private String Currency;
    
    /**
     * 匯款金額
     */
    @NotBlank(message = "匯款金額不可為空")
    private String Amount;
    
    /**
     * 英文姓名
     */
    @NotBlank(message = "英文姓名不可為空")
    @Size(max = 70, message = "英文姓名長度不可超過70")
    private String PayeeEngName;
    
    /**
     * 英文姓名無誤
     */
    @Size(max = 1, message = "英文姓名無誤標示長度不可超過1")
    @Pattern(regexp = "[YN]", message = "英文姓名無誤標示必須為Y或N")
    private String EngNameYN;
    
    /**
     * 居留證日期起
     * 格式: YYYYMMDD
     */
    @Size(min = 8, max = 8, message = "居留證日期起必須為8位")
    @Pattern(regexp = "\\d{8}", message = "居留證日期起格式必須為YYYYMMDD")
    private String ResidenceDateBegin;
    
    /**
     * 居留證日期迄
     * 格式: YYYYMMDD
     */
    @Size(min = 8, max = 8, message = "居留證日期迄必須為8位")
    @Pattern(regexp = "\\d{8}", message = "居留證日期迄格式必須為YYYYMMDD")
    private String ResidenceDateEnd;
    
    /**
     * 出生日期
     * 格式: YYYYMMDD
     */
    @Size(min = 8, max = 8, message = "出生日期必須為8位")
    @Pattern(regexp = "\\d{8}", message = "出生日期格式必須為YYYYMMDD")
    private String BOD;
    
    /**
     * 收款人中文名稱
     */
    @NotBlank(message = "收款人中文名稱不可為空")
    @Size(max = 70, message = "收款人中文名稱長度不可超過70")
    private String PayeeName;
    
    /**
     * 收款人ID
     */
    @NotBlank(message = "收款人ID不可為空")
    @Size(max = 16, message = "收款人ID長度不可超過16")
    private String PayeeID;
    
    /**
     * 收款人帳號
     */
    @NotBlank(message = "收款人帳號不可為空")
    @Size(max = 35, message = "收款人帳號長度不可超過35")
    private String PayeeAccount;
    
    /**
     * 收款行代碼
     */
    @NotBlank(message = "收款行代碼不可為空")
    @Size(max = 7, message = "收款行代碼長度不可超過7")
    private String PayeeBankCode;
    
    /**
     * 收款人電話/手機
     */
    @NotBlank(message = "收款人電話不可為空")
    @Size(max = 20, message = "收款人電話長度不可超過20")
    private String PayeeTel;
    
    /**
     * 收款人信箱
     */
    @NotBlank(message = "收款人信箱不可為空")
    @Size(max = 40, message = "收款人信箱長度不可超過40")
    private String PayeeMail;
    
    /**
     * 匯款性質
     */
    @NotBlank(message = "匯款性質不可為空")
    @Size(min = 3, max = 3, message = "匯款性質必須為3位")
    private String SourceOfFund;
    
    /**
     * 匯款性質DETAIL
     */
    @Size(max = 80, message = "匯款性質DETAIL長度不可超過80")
    private String Detail;
    
    /**
     * signature
     * 法人戶以憑證作電子簽章
     * (RemitRefNo+ PayerName+ Currency+ Amount+ PayeeEngName+ PayeeID+ SourceOfFund)
     */
    @Size(max = 64, message = "簽章長度不可超過64")
    private String Signature;
    
    /**
     * 白名單
     * 值=S時為補通訊用，只帶中文名字和兩個編號。其它為空
     */
    @Size(max = 1, message = "白名單標示長度不可超過1")
    private String WhileFlag;
    
    /**
     * 解款日期
     * 格式: YYYYMMDD
     */
    @NotBlank(message = "解款日期不可為空")
    @Size(min = 8, max = 8, message = "解款日期必須為8位")
    @Pattern(regexp = "\\d{8}", message = "解款日期格式必須為YYYYMMDD")
    private String ReleaseDate;
}