package com.kgi.module.gateway.adapters.rest;

import com.kgi.core.adapters.rest.BaseController;
import com.kgi.core.application.dto.IbrApiResponse;
import com.kgi.module.gateway.application.dto.request.*;
import com.kgi.module.gateway.application.dto.response.*;
import com.kgi.module.gateway.application.usecase.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 重構後的跨境API閘道控制器
 * 
 * 職責明確：
 * 1. 只負責接收HTTP請求和返回響應
 * 2. 參數驗證委託給 @Valid 和 DTO
 * 3. 業務邏輯委託給 UseCase
 * 4. 不包含任何業務邏輯實現
 * 
 * 核心功能：
 * 1. 接收跨境平台TO API資料 (14個欄位)
 * 2. 存儲資料到資料庫並生成案件編號
 * 3. 發送通知給客戶 (Email/SMS)
 * 4. 處理FROM API回傳資料給跨境平台
 */
@Slf4j
@RestController
@RequestMapping("/api/ibr/gateway")
@RequiredArgsConstructor
public class CrossBorderApiControllerRefactored extends BaseController {
    
    // 注入所有需要的 UseCase
    private final ReceiveToApiUseCase receiveToApiUseCase;
    private final SendFromApiUseCase sendFromApiUseCase;
    private final SendSupplementFromApiUseCase sendSupplementFromApiUseCase;
    
    /**
     * 接收跨境平台TO API資料
     * POST /api/ibr/gateway/receive-to-api
     */
    @PostMapping("/receive-to-api")
    public ResponseEntity<IbrApiResponse<ToApiResponse>> receiveToApi(
            @Valid @RequestBody Map<String, Object> toApiData) {
        
        log.info("接收跨境平台TO API資料");
        
        try {
            ToApiRequest request = new ToApiRequest();
            request.setToApiData(toApiData);
            
            ToApiResponse response = receiveToApiUseCase.execute(request);
            return success(response, "TO API資料處理成功");
        } catch (Exception e) {
            log.error("跨境TO API資料處理失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 處理FROM API - 回傳整合資料給跨境平台
     * POST /api/ibr/gateway/send-from-api
     */
    @PostMapping("/send-from-api")
    public ResponseEntity<IbrApiResponse<FromApiResponse>> sendFromApi(
            @Valid @RequestBody FromApiRequest request) {
        
        log.info("發送FROM API資料: caseNo={}", request.getCaseNo());
        
        try {
            FromApiResponse response = sendFromApiUseCase.execute(request);
            return success(response, "FROM API處理成功");
        } catch (Exception e) {
            log.error("FROM API處理失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 處理補件FROM API
     * POST /api/ibr/gateway/send-supplement-from-api
     */
    @PostMapping("/send-supplement-from-api")
    public ResponseEntity<IbrApiResponse<SupplementFromApiResponse>> sendSupplementFromApi(
            @Valid @RequestBody SupplementFromApiRequest request) {
        
        log.info("發送補件FROM API資料: caseNo={}, supplementId={}", 
                request.getCaseNo(), request.getSupplementId());
        
        try {
            SupplementFromApiResponse response = sendSupplementFromApiUseCase.execute(request);
            return success(response, "補件FROM API處理成功");
        } catch (Exception e) {
            log.error("補件FROM API處理失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 查詢案件資料 (供跨境平台查詢用)
     * GET /api/ibr/gateway/case/{caseNo}
     */
    @GetMapping("/case/{caseNo}")
    public ResponseEntity<IbrApiResponse<Map<String, Object>>> getCaseData(
            @PathVariable String caseNo) {
        
        log.info("查詢案件資料: caseNo={}", caseNo);
        
        try {
            // TODO: 應該創建專門的查詢UseCase
            Map<String, Object> caseData = Map.of(
                "caseNo", caseNo,
                "status", "PROCESSING",
                "createdTime", LocalDateTime.now().minusHours(2).toString(),
                "lastUpdatedTime", LocalDateTime.now().minusMinutes(30).toString(),
                "customerType", "INDIVIDUAL",
                "applicationStatus", "COMPLETED"
            );
            
            return success(caseData, "案件資料查詢成功");
        } catch (Exception e) {
            log.error("案件資料查詢失敗", e);
            return error(e.getMessage());
        }
    }
}