package com.kgi.module.gateway.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * FROM API響應
 */
@Data
@Builder
public class FromApiResponse {
    private String caseNo;
    private String applicationId;
    private Map<String, Object> integratedData;
    private Map<String, Object> fromApiResult;
    private String sentTime;
    private String status;
    private String message;
}