package com.kgi.module.gateway.adapters.rest;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.module.otp.application.dto.request.OtpVO;
import com.kgi.module.otp.application.facade.OtpFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 跨境匯款 Gateway API 控制器
 * 提供統一的 API 端點供前端呼叫
 */
@Slf4j
@RestController
@RequestMapping("/api/gateway")
@CrossOrigin
@RequiredArgsConstructor
@Tag(name = "跨境匯款 Gateway API", description = "提供統一的跨境匯款相關 API 端點")
public class CrossBorderGatewayController {
    
    private final OtpFacade otpFacade;
    
    /**
     * 發送手機 OTP
     * 
     * @param request OTP 請求資料，包含：
     *        - uniqId: 使用者唯一識別碼
     *        - uniqType: 使用者類型 (IND/COR)
     *        - phoneNumber: 手機號碼
     * @return OTP 發送結果
     */
    @Operation(summary = "發送手機 OTP", description = "發送 OTP 驗證碼到指定手機號碼")
    @PostMapping("/otp/sendOTPByPhone")
    public ResponseEntity<ResponseVO> sendOTPByPhone(@RequestBody OtpVO request) {
        log.info("Gateway API - Sending OTP by phone for uniqId: {}, uniqType: {}, phone: {}", 
                request.getUniqId(), request.getUniqType(), request.getPhoneNumber());
        
        try {
            // 設定發送方式為 SMS
            request.setChannel("SMS");
            
            // 呼叫 OTP Facade 發送 OTP
            return otpFacade.getOtpCode(request);
            
        } catch (Exception e) {
            log.error("Gateway API - Error sending OTP by phone", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("發送OTP失敗：" + e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 發送 Email OTP
     * 
     * @param request OTP 請求資料，包含：
     *        - uniqId: 使用者唯一識別碼
     *        - uniqType: 使用者類型 (IND/COR)
     *        - email: Email 地址
     * @return OTP 發送結果
     */
    @Operation(summary = "發送 Email OTP", description = "發送 OTP 驗證碼到指定 Email")
    @PostMapping("/otp/sendOTPByEmail")
    public ResponseEntity<ResponseVO> sendOTPByEmail(@RequestBody OtpVO request) {
        log.info("Gateway API - Sending OTP by email for uniqId: {}, uniqType: {}, email: {}", 
                request.getUniqId(), request.getUniqType(), request.getEmail());
        
        try {
            // 設定發送方式為 EMAIL
            request.setChannel("EMAIL");
            
            // 呼叫 OTP Facade 發送 OTP
            return otpFacade.getOtpCode(request);
            
        } catch (Exception e) {
            log.error("Gateway API - Error sending OTP by email", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("發送OTP失敗：" + e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * 驗證 OTP
     * 
     * @param request OTP 驗證請求資料，包含：
     *        - uniqId: 使用者唯一識別碼
     *        - uniqType: 使用者類型 (IND/COR)
     *        - otpCode: OTP 驗證碼
     * @return OTP 驗證結果
     */
    @Operation(summary = "驗證 OTP", description = "驗證使用者輸入的 OTP 驗證碼")
    @PostMapping("/otp/verifyOTP")
    public ResponseEntity<ResponseVO> verifyOTP(@RequestBody OtpVO request) {
        log.info("Gateway API - Verifying OTP for uniqId: {}, uniqType: {}", 
                request.getUniqId(), request.getUniqType());
        
        try {
            // 呼叫 OTP Facade 驗證 OTP
            return otpFacade.checkOtpCode(request);
            
        } catch (Exception e) {
            log.error("Gateway API - Error verifying OTP", e);
            ResponseVO errorResponse = new ResponseVO();
            errorResponse.setRtnCode("9999");
            errorResponse.setRtnMessage("驗證OTP失敗：" + e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }
}