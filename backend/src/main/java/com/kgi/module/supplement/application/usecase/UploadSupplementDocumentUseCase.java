package com.kgi.module.supplement.application.usecase;

import com.kgi.module.supplement.application.dto.request.DocumentUploadRequest;
import com.kgi.module.supplement.application.dto.response.DocumentUploadResponse;
import com.kgi.core.domain.service.DocumentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 上傳補件文件用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UploadSupplementDocumentUseCase {
    
    private final DocumentService documentService;
    
    public DocumentUploadResponse execute(
            String caseNo,
            String documentType,
            MultipartFile file,
            String description) {
        
        log.info("執行文件上傳: caseNo={}, documentType={}, fileName={}", 
                caseNo, documentType, file.getOriginalFilename());
        
        // 上傳文件
        Map<String, Object> uploadResult = documentService.uploadDocument(caseNo, documentType, file);
        
        // 建立回應
        return DocumentUploadResponse.builder()
                .documentId((String) uploadResult.get("documentId"))
                .caseNo(caseNo)
                .documentType(documentType)
                .fileName((String) uploadResult.get("fileName"))
                .fileSize((Long) uploadResult.get("fileSize"))
                .mimeType((String) uploadResult.get("mimeType"))
                .description(description)
                .uploadTimeStr((String) uploadResult.get("uploadTime"))
                .status("UPLOADED")
                .message("文件上傳成功")
                .build();
    }
}