package com.kgi.module.supplement.adapters.rest;

import com.kgi.core.adapters.rest.BaseController;
import com.kgi.core.application.dto.IbrApiResponse;
import com.kgi.module.supplement.application.usecase.*;
import com.kgi.module.supplement.application.dto.request.*;
import com.kgi.module.supplement.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

/**
 * 統一補件控制器
 * 支援自然人/法人統一處理，根據客戶類型區分業務邏輯
 * 
 * 職責明確：
 * 1. 只負責接收HTTP請求和返回響應
 * 2. 參數驗證委託給 @Valid 和 DTO
 * 3. 業務邏輯委託給 UseCase
 * 4. 不包含任何業務邏輯實現
 * 
 * 核心流程 (individual/corporate共用)：
 * 1. initialize: URL參數驗證 + 客戶類型判斷 + 補件需求分析
 * 2. notification: 補件通知資料 (個人/企業通知內容不同)
 * 3. upload: 文件上傳
 * 4. submit: 補件提交 + FROM API呼叫
 * 5. complete: 補件完成頁面
 * 
 * API路徑: /api/ibr/application/supplement/*
 */
@Slf4j
@RestController
@RequestMapping("/api/ibr/application/supplement")
@RequiredArgsConstructor
public class SupplementController extends BaseController {
    
    // 注入所有需要的 UseCase
    private final InitializeSupplementUseCase initializeSupplementUseCase;
    private final GetSupplementNotificationUseCase getNotificationUseCase;
    private final UploadSupplementDocumentUseCase uploadDocumentUseCase;
    private final SubmitSupplementUseCase submitSupplementUseCase;
    private final GetSupplementStatusUseCase getStatusUseCase;
    
    /**
     * 初始化補件流程
     * GET /api/ibr/application/supplement/initialize?type=S&caseNo=xxx
     */
    @GetMapping("/initialize")
    public ResponseEntity<IbrApiResponse<SupplementInitializeResponse>> initializeSupplement(
            @RequestParam String type,
            @RequestParam String caseNo) {
        
        log.info("初始化補件流程: type={}, caseNo={}", type, caseNo);
        
        try {
            SupplementInitializeResponse response = initializeSupplementUseCase.execute(type, caseNo);
            return success(response, "補件流程初始化成功");
        } catch (Exception e) {
            log.error("補件流程初始化失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得補件通知
     * GET /api/ibr/application/supplement/notification?caseNo=xxx
     */
    @GetMapping("/notification")
    public ResponseEntity<IbrApiResponse<SupplementNotificationResponse>> getSupplementNotification(
            @RequestParam String caseNo,
            @RequestParam String customerType) {
        
        log.info("取得補件通知: caseNo={}, customerType={}", caseNo, customerType);
        
        try {
            SupplementNotificationResponse response = getNotificationUseCase.execute(caseNo, customerType);
            return success(response, "補件通知取得成功");
        } catch (Exception e) {
            log.error("取得補件通知失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 上傳補件文件
     * POST /api/ibr/application/supplement/documents/upload
     */
    @PostMapping("/documents/upload")
    public ResponseEntity<IbrApiResponse<DocumentUploadResponse>> uploadDocument(
            @RequestParam String caseNo,
            @RequestParam String documentType,
            @RequestParam MultipartFile file,
            @RequestParam(required = false) String description) {
        
        log.info("上傳補件文件: caseNo={}, documentType={}, fileName={}", 
                caseNo, documentType, file.getOriginalFilename());
        
        try {
            DocumentUploadResponse response = uploadDocumentUseCase.execute(
                caseNo, documentType, file, description
            );
            return success(response, "文件上傳成功");
        } catch (Exception e) {
            log.error("文件上傳失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 提交補件
     * POST /api/ibr/application/supplement/submit
     */
    @PostMapping("/submit")
    public ResponseEntity<IbrApiResponse<SupplementSubmitResponse>> submitSupplement(
            @Valid @RequestBody SupplementSubmitRequest request) {
        
        log.info("提交補件: caseNo={}", request.getCaseNo());
        
        try {
            SupplementSubmitResponse response = submitSupplementUseCase.execute(request);
            return success(response, "補件提交成功");
        } catch (Exception e) {
            log.error("補件提交失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 查詢補件狀態
     * GET /api/ibr/application/supplement/status/{caseNo}
     */
    @GetMapping("/status/{caseNo}")
    public ResponseEntity<IbrApiResponse<SupplementStatusResponse>> getSupplementStatus(
            @PathVariable String caseNo) {
        
        log.info("查詢補件狀態: caseNo={}", caseNo);
        
        try {
            SupplementStatusResponse response = getStatusUseCase.execute(caseNo);
            return success(response, "補件狀態查詢成功");
        } catch (Exception e) {
            log.error("查詢補件狀態失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得補件完成資訊
     * GET /api/ibr/application/supplement/complete?caseNo=xxx
     */
    @GetMapping("/complete")
    public ResponseEntity<IbrApiResponse<SupplementCompleteResponse>> getSupplementComplete(
            @RequestParam String caseNo) {
        
        log.info("取得補件完成資訊: caseNo={}", caseNo);
        
        try {
            SupplementCompleteResponse response = getStatusUseCase.getCompleteInfo(caseNo);
            return success(response, "補件完成資訊取得成功");
        } catch (Exception e) {
            log.error("取得補件完成資訊失敗", e);
            return error(e.getMessage());
        }
    }
}