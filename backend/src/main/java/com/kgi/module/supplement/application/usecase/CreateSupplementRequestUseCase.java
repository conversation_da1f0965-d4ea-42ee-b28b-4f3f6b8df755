package com.kgi.module.supplement.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.supplement.domain.model.DocumentRequirement;
import com.kgi.module.supplement.domain.model.SupplementRequest;
import com.kgi.module.supplement.domain.service.SupplementProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 建立補件請求用例
 * 負責處理補件請求的建立業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CreateSupplementRequestUseCase {
    
    private final SupplementProcessingService supplementProcessingService;
    
    /**
     * 執行建立補件請求
     */
    @Transactional
    public SupplementRequest execute(CreateSupplementRequestCommand command) {
        log.info("執行建立補件請求: remittanceId={}, applicantType={}", 
                command.getOriginalRemittanceId(), command.getApplicantType());
        
        try {
            // 驗證輸入參數
            validateCommand(command);
            
            // 建立補件請求
            SupplementRequest supplementRequest = supplementProcessingService.createSupplementRequest(
                    command.getOriginalRemittanceId(),
                    command.getApplicantType(),
                    command.getApplicantIdentifier(),
                    command.getApplicantName(),
                    command.getSupplementType(),
                    command.getReason(),
                    command.getRequiredDocuments()
            );
            
            log.info("補件請求建立成功: supplementId={}", supplementRequest.getSupplementId());
            return supplementRequest;
            
        } catch (Exception e) {
            log.error("建立補件請求失敗: {}", e.getMessage(), e);
            throw new BusinessException("CREATE_SUPPLEMENT_FAILED", "建立補件請求失敗: " + e.getMessage());
        }
    }
    
    /**
     * 驗證命令參數
     */
    private void validateCommand(CreateSupplementRequestCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getOriginalRemittanceId())) {
            throw new BusinessException("INVALID_COMMAND", "原始解款編號不能為空");
        }
        
        if (command.getApplicantType() == null) {
            throw new BusinessException("INVALID_COMMAND", "申請人類型不能為空");
        }
        
        if (isBlank(command.getApplicantIdentifier())) {
            throw new BusinessException("INVALID_COMMAND", "申請人識別號不能為空");
        }
        
        if (isBlank(command.getApplicantName())) {
            throw new BusinessException("INVALID_COMMAND", "申請人姓名不能為空");
        }
        
        if (command.getSupplementType() == null) {
            throw new BusinessException("INVALID_COMMAND", "補件類型不能為空");
        }
        
        if (isBlank(command.getReason())) {
            throw new BusinessException("INVALID_COMMAND", "補件原因不能為空");
        }
        
        if (command.getRequiredDocuments() == null || command.getRequiredDocuments().isEmpty()) {
            throw new BusinessException("INVALID_COMMAND", "需要補充的文件清單不能為空");
        }
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 建立補件請求命令
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class CreateSupplementRequestCommand {
        
        /**
         * 原始解款編號
         */
        private String originalRemittanceId;
        
        /**
         * 申請人類型
         */
        private SupplementRequest.ApplicantType applicantType;
        
        /**
         * 申請人識別號
         */
        private String applicantIdentifier;
        
        /**
         * 申請人姓名
         */
        private String applicantName;
        
        /**
         * 補件類型
         */
        private SupplementRequest.SupplementType supplementType;
        
        /**
         * 補件原因
         */
        private String reason;
        
        /**
         * 補件說明
         */
        private String description;
        
        /**
         * 需要補充的文件清單
         */
        private List<DocumentRequirement> requiredDocuments;
        
        /**
         * 是否緊急件
         */
        private Boolean isUrgent;
        
        /**
         * 自定義截止天數
         */
        private Integer deadlineDays;
    }
}