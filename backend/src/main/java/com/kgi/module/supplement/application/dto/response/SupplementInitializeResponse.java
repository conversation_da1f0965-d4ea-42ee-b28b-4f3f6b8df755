package com.kgi.module.supplement.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 補件初始化響應
 */
@Data
@Builder
public class SupplementInitializeResponse {
    private String caseNo;
    private String customerType;
    private String flowType;
    private Map<String, Object> originalData;
    private Map<String, Object> supplementRequirements;
    private String step;
    private String nextStepUrl;
    private String sessionId;
    private String initTime;
    private Map<String, Object> customerInfo;
    private List<String> availableActions;
}