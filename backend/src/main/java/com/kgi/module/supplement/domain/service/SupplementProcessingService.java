package com.kgi.module.supplement.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.supplement.domain.model.DocumentRequirement;
import com.kgi.module.supplement.domain.model.SupplementRequest;
import com.kgi.module.supplement.domain.model.UploadedDocument;
import com.kgi.module.supplement.domain.repository.SupplementRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 補件處理服務
 * 負責補件申請的業務邏輯處理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplementProcessingService {
    
    private final SupplementRepository supplementRepository;
    private final DocumentValidationService documentValidationService;
    private final SupplementNotificationService notificationService;
    
    /**
     * 建立補件請求
     */
    @Transactional
    public SupplementRequest createSupplementRequest(
            String originalRemittanceId,
            SupplementRequest.ApplicantType applicantType,
            String applicantIdentifier,
            String applicantName,
            SupplementRequest.SupplementType supplementType,
            String reason,
            List<DocumentRequirement> requiredDocuments) {
        
        log.info("建立補件請求: remittanceId={}, applicantType={}, supplementType={}", 
                originalRemittanceId, applicantType, supplementType);
        
        // 生成補件編號
        String supplementId = generateSupplementId(applicantType);
        
        // 建立補件請求
        SupplementRequest supplementRequest = SupplementRequest.createNew(
                supplementId,
                originalRemittanceId,
                applicantType,
                applicantIdentifier,
                applicantName,
                supplementType,
                reason,
                requiredDocuments
        );
        
        // 保存補件請求
        SupplementRequest savedRequest = supplementRepository.save(supplementRequest);
        
        // 發送補件通知
        notificationService.sendSupplementRequestNotification(savedRequest);
        
        log.info("補件請求建立成功: supplementId={}", supplementId);
        return savedRequest;
    }
    
    /**
     * 開始補件流程
     */
    @Transactional
    public void startSupplementProcess(String supplementId, String contactPhone, String contactEmail) {
        log.info("開始補件流程: supplementId={}", supplementId);
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 驗證狀態
        if (supplementRequest.getStatus() != SupplementRequest.SupplementStatus.PENDING) {
            throw new BusinessException("INVALID_STATUS", "只有待補件狀態的申請才能開始補件流程");
        }
        
        // 開始補件
        supplementRequest.startSupplement(contactPhone, contactEmail);
        
        // 保存更新
        supplementRepository.save(supplementRequest);
        
        // 發送開始補件通知
        notificationService.sendSupplementStartNotification(supplementRequest);
        
        log.info("補件流程已開始: supplementId={}", supplementId);
    }
    
    /**
     * 上傳文件
     */
    @Transactional
    public void uploadDocument(String supplementId, UploadedDocument document) {
        log.info("上傳文件: supplementId={}, documentType={}", 
                supplementId, document.getDocumentType());
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 驗證狀態
        if (!supplementRequest.canProcess()) {
            throw new BusinessException("INVALID_STATUS", "當前狀態不允許上傳文件");
        }
        
        // 驗證文件
        documentValidationService.validateDocument(document);
        
        // 上傳文件
        supplementRequest.uploadDocument(document);
        
        // 保存更新
        supplementRepository.save(supplementRequest);
        
        // 如果所有文件都已上傳，發送審核通知
        if (supplementRequest.isAllRequiredDocumentsUploaded()) {
            notificationService.sendSupplementCompleteNotification(supplementRequest);
        }
        
        log.info("文件上傳成功: supplementId={}, documentType={}", 
                supplementId, document.getDocumentType());
    }
    
    /**
     * 審核補件
     */
    @Transactional
    public void reviewSupplement(String supplementId, boolean approved, 
                               String processorEmployeeId, String processorName, String notes) {
        log.info("審核補件: supplementId={}, approved={}, processor={}", 
                supplementId, approved, processorEmployeeId);
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 驗證狀態
        if (supplementRequest.getStatus() != SupplementRequest.SupplementStatus.UNDER_REVIEW) {
            throw new BusinessException("INVALID_STATUS", "只有審核中狀態的申請才能進行審核");
        }
        
        if (approved) {
            // 核准補件
            supplementRequest.approve(processorEmployeeId, processorName, notes);
            
            // 發送核准通知
            notificationService.sendSupplementApprovedNotification(supplementRequest);
            
            log.info("補件已核准: supplementId={}", supplementId);
        } else {
            // 退件
            supplementRequest.reject(processorEmployeeId, processorName, notes);
            
            // 發送退件通知
            notificationService.sendSupplementRejectedNotification(supplementRequest);
            
            log.info("補件已退件: supplementId={}", supplementId);
        }
        
        // 保存更新
        supplementRepository.save(supplementRequest);
    }
    
    /**
     * 完成補件
     */
    @Transactional
    public void completeSupplement(String supplementId) {
        log.info("完成補件: supplementId={}", supplementId);
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 驗證狀態
        if (supplementRequest.getStatus() != SupplementRequest.SupplementStatus.APPROVED) {
            throw new BusinessException("INVALID_STATUS", "只有已核准的申請才能完成補件");
        }
        
        // 完成補件
        supplementRequest.complete();
        
        // 保存更新
        supplementRepository.save(supplementRequest);
        
        // 發送完成通知
        notificationService.sendSupplementCompletedNotification(supplementRequest);
        
        log.info("補件已完成: supplementId={}", supplementId);
    }
    
    /**
     * 取消補件
     */
    @Transactional
    public void cancelSupplement(String supplementId, String reason) {
        log.info("取消補件: supplementId={}, reason={}", supplementId, reason);
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 檢查是否可以取消
        if (supplementRequest.getStatus() == SupplementRequest.SupplementStatus.COMPLETED ||
            supplementRequest.getStatus() == SupplementRequest.SupplementStatus.CANCELLED) {
            throw new BusinessException("INVALID_STATUS", "已完成或已取消的申請無法再次取消");
        }
        
        // 取消補件
        supplementRequest.cancel(reason);
        
        // 保存更新
        supplementRepository.save(supplementRequest);
        
        // 發送取消通知
        notificationService.sendSupplementCancelledNotification(supplementRequest);
        
        log.info("補件已取消: supplementId={}", supplementId);
    }
    
    /**
     * 延長補件截止時間
     */
    @Transactional
    public void extendDeadline(String supplementId, int days, String reason) {
        log.info("延長補件截止時間: supplementId={}, days={}, reason={}", 
                supplementId, days, reason);
        
        SupplementRequest supplementRequest = getSupplementRequest(supplementId);
        
        // 檢查是否可以延長
        if (supplementRequest.getStatus() == SupplementRequest.SupplementStatus.COMPLETED ||
            supplementRequest.getStatus() == SupplementRequest.SupplementStatus.CANCELLED ||
            supplementRequest.getStatus() == SupplementRequest.SupplementStatus.EXPIRED) {
            throw new BusinessException("INVALID_STATUS", "已完成、已取消或已逾期的申請無法延長截止時間");
        }
        
        // 延長截止時間
        supplementRequest.extendDeadline(days, reason);
        
        // 保存更新
        supplementRepository.save(supplementRequest);
        
        // 發送延長通知
        notificationService.sendSupplementDeadlineExtendedNotification(supplementRequest);
        
        log.info("補件截止時間已延長: supplementId={}, newDeadline={}", 
                supplementId, supplementRequest.getDeadline());
    }
    
    /**
     * 查詢補件狀態
     */
    public SupplementRequest getSupplementStatus(String supplementId) {
        return getSupplementRequest(supplementId);
    }
    
    /**
     * 查詢申請人的補件列表
     */
    public List<SupplementRequest> getSupplementsByApplicant(String applicantIdentifier) {
        return supplementRepository.findByApplicantIdentifier(applicantIdentifier);
    }
    
    /**
     * 查詢原始解款的補件列表
     */
    public List<SupplementRequest> getSupplementsByRemittance(String originalRemittanceId) {
        return supplementRepository.findByOriginalRemittanceId(originalRemittanceId);
    }
    
    /**
     * 處理逾期補件
     */
    @Transactional
    public void processExpiredSupplements() {
        log.info("開始處理逾期補件");
        
        List<SupplementRequest> expiredRequests = supplementRepository.findExpiredRequests();
        
        for (SupplementRequest request : expiredRequests) {
            if (request.isExpired()) {
                supplementRepository.save(request);
                
                // 發送逾期通知
                notificationService.sendSupplementExpiredNotification(request);
                
                log.info("補件已標記為逾期: supplementId={}", request.getSupplementId());
            }
        }
        
        log.info("逾期補件處理完成，共處理 {} 筆", expiredRequests.size());
    }
    
    /**
     * 發送提醒
     */
    @Transactional
    public void sendReminders() {
        log.info("開始發送補件提醒");
        
        List<SupplementRequest> requestsNeedingReminder = supplementRepository.findRequestsNeedingReminder();
        
        for (SupplementRequest request : requestsNeedingReminder) {
            if (request.needsReminder()) {
                request.sendReminder();
                supplementRepository.save(request);
                
                // 發送提醒通知
                notificationService.sendSupplementReminderNotification(request);
                
                log.info("已發送補件提醒: supplementId={}", request.getSupplementId());
            }
        }
        
        log.info("補件提醒發送完成，共發送 {} 筆", requestsNeedingReminder.size());
    }
    
    // ==================== 私有方法 ====================
    
    private SupplementRequest getSupplementRequest(String supplementId) {
        Optional<SupplementRequest> optionalRequest = supplementRepository.findBySupplementId(supplementId);
        
        if (!optionalRequest.isPresent()) {
            throw new BusinessException("SUPPLEMENT_NOT_FOUND", "找不到補件申請: " + supplementId);
        }
        
        return optionalRequest.get();
    }
    
    private String generateSupplementId(SupplementRequest.ApplicantType applicantType) {
        String prefix = applicantType == SupplementRequest.ApplicantType.INDIVIDUAL ? "SUP" : "CSUP";
        String timestamp = String.valueOf(System.currentTimeMillis());
        return prefix + timestamp.substring(timestamp.length() - 10);
    }
}