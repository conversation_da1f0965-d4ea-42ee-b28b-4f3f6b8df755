package com.kgi.module.supplement.domain.model;

import lombok.*;

/**
 * 文件需求值對象
 * 定義補件所需的文件類型和要求
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentRequirement {
    
    /**
     * 文件類型
     */
    private String documentType;
    
    /**
     * 文件名稱
     */
    private String documentName;
    
    /**
     * 文件說明
     */
    private String description;
    
    /**
     * 是否必要
     */
    private Boolean isRequired;
    
    /**
     * 允許的檔案格式
     */
    private String[] allowedFormats;
    
    /**
     * 最大檔案大小（MB）
     */
    private Integer maxFileSizeMB;
    
    /**
     * 範例檔案URL
     */
    private String sampleFileUrl;
    
    /**
     * 額外備註
     */
    private String notes;
    
    /**
     * 建立身分證明文件需求
     */
    public static DocumentRequirement createIdentityDocument() {
        return DocumentRequirement.builder()
                .documentType("IDENTITY")
                .documentName("身分證明文件")
                .description("請上傳清晰的身分證正反面照片")
                .isRequired(true)
                .allowedFormats(new String[]{"jpg", "jpeg", "png", "pdf"})
                .maxFileSizeMB(5)
                .notes("照片需清晰可辨識，不可模糊或遮擋")
                .build();
    }
    
    /**
     * 建立銀行帳戶證明需求
     */
    public static DocumentRequirement createBankAccountProof() {
        return DocumentRequirement.builder()
                .documentType("BANK_ACCOUNT")
                .documentName("銀行帳戶證明")
                .description("請上傳銀行存摺封面或帳戶證明文件")
                .isRequired(true)
                .allowedFormats(new String[]{"jpg", "jpeg", "png", "pdf"})
                .maxFileSizeMB(5)
                .notes("需顯示完整帳號和戶名")
                .build();
    }
    
    /**
     * 建立收入證明需求
     */
    public static DocumentRequirement createIncomeProof() {
        return DocumentRequirement.builder()
                .documentType("INCOME")
                .documentName("收入證明")
                .description("請上傳薪資單、扣繳憑單或其他收入證明")
                .isRequired(false)
                .allowedFormats(new String[]{"jpg", "jpeg", "png", "pdf"})
                .maxFileSizeMB(10)
                .notes("需為最近3個月內的證明文件")
                .build();
    }
    
    /**
     * 建立營業執照需求
     */
    public static DocumentRequirement createBusinessLicense() {
        return DocumentRequirement.builder()
                .documentType("BUSINESS_LICENSE")
                .documentName("營業執照")
                .description("請上傳有效的營業執照影本")
                .isRequired(true)
                .allowedFormats(new String[]{"jpg", "jpeg", "png", "pdf"})
                .maxFileSizeMB(5)
                .notes("營業執照需在有效期限內")
                .build();
    }
    
    /**
     * 建立授權書需求
     */
    public static DocumentRequirement createAuthorizationLetter() {
        return DocumentRequirement.builder()
                .documentType("AUTHORIZATION")
                .documentName("授權委託書")
                .description("請上傳簽名蓋章的授權委託書")
                .isRequired(true)
                .allowedFormats(new String[]{"jpg", "jpeg", "png", "pdf"})
                .maxFileSizeMB(5)
                .notes("需有授權人親筆簽名或公司印章")
                .build();
    }
    
    /**
     * 驗證檔案格式
     */
    public boolean isValidFormat(String fileName) {
        if (allowedFormats == null || allowedFormats.length == 0) {
            return true;
        }
        
        String fileExtension = getFileExtension(fileName);
        if (fileExtension == null) {
            return false;
        }
        
        for (String format : allowedFormats) {
            if (format.equalsIgnoreCase(fileExtension)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 驗證檔案大小
     */
    public boolean isValidFileSize(long fileSizeBytes) {
        if (maxFileSizeMB == null) {
            return true;
        }
        
        long maxSizeBytes = maxFileSizeMB * 1024L * 1024L;
        return fileSizeBytes <= maxSizeBytes;
    }
    
    /**
     * 取得檔案副檔名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return null;
        }
        
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 取得允許格式的字串表示
     */
    public String getAllowedFormatsString() {
        if (allowedFormats == null || allowedFormats.length == 0) {
            return "任何格式";
        }
        
        return String.join(", ", allowedFormats);
    }
    
    /**
     * 獲取最大文件大小（位元組）（為了向後相容性）
     */
    public Long getMaxFileSize() {
        if (maxFileSizeMB == null) {
            return null;
        }
        return maxFileSizeMB * 1024L * 1024L;
    }
    
    /**
     * 設定最大文件大小（位元組）（為了向後相容性）
     */
    public void setMaxFileSize(Long maxFileSize) {
        if (maxFileSize == null) {
            this.maxFileSizeMB = null;
        } else {
            this.maxFileSizeMB = (int) (maxFileSize / (1024L * 1024L));
        }
    }
    
    /**
     * 獲取允許格式（為了向後相容性）
     */
    public String getAllowedFormats() {
        return getAllowedFormatsString();
    }
    
    /**
     * 設定允許格式（為了向後相容性）
     */
    public void setAllowedFormats(String allowedFormats) {
        if (allowedFormats != null && !allowedFormats.trim().isEmpty()) {
            this.allowedFormats = allowedFormats.split("[,\\s]+");
        } else {
            this.allowedFormats = null;
        }
    }
}