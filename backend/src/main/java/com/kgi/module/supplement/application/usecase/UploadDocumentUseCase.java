package com.kgi.module.supplement.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.supplement.domain.model.UploadedDocument;
import com.kgi.module.supplement.domain.service.SupplementProcessingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 上傳文件用例
 * 負責處理補件文件上傳的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UploadDocumentUseCase {
    
    private final SupplementProcessingService supplementProcessingService;
    
    /**
     * 執行文件上傳
     */
    @Transactional
    public UploadDocumentResult execute(UploadDocumentCommand command) {
        log.info("執行文件上傳: supplementId={}, documentType={}", 
                command.getSupplementId(), command.getDocumentType());
        
        try {
            // 驗證輸入參數
            validateCommand(command);
            
            // 建立上傳文件物件
            UploadedDocument document = buildUploadedDocument(command);
            
            // 上傳文件
            supplementProcessingService.uploadDocument(command.getSupplementId(), document);
            
            // 建立回應結果
            UploadDocumentResult result = UploadDocumentResult.builder()
                    .success(true)
                    .documentId(document.getDocumentId())
                    .fileName(document.getFileName())
                    .fileSize(document.getFileSize())
                    .uploadTime(document.getUploadTime())
                    .status(document.getStatus())
                    .message("文件上傳成功")
                    .build();
            
            log.info("文件上傳成功: supplementId={}, documentId={}", 
                    command.getSupplementId(), document.getDocumentId());
            
            return result;
            
        } catch (Exception e) {
            log.error("文件上傳失敗: {}", e.getMessage(), e);
            
            // 回傳失敗結果
            return UploadDocumentResult.builder()
                    .success(false)
                    .message("文件上傳失敗: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 驗證命令參數
     */
    private void validateCommand(UploadDocumentCommand command) {
        if (command == null) {
            throw new BusinessException("INVALID_COMMAND", "命令參數不能為空");
        }
        
        if (isBlank(command.getSupplementId())) {
            throw new BusinessException("INVALID_COMMAND", "補件編號不能為空");
        }
        
        if (isBlank(command.getDocumentType())) {
            throw new BusinessException("INVALID_COMMAND", "文件類型不能為空");
        }
        
        if (isBlank(command.getFileName())) {
            throw new BusinessException("INVALID_COMMAND", "文件名稱不能為空");
        }
        
        if (command.getFileSize() == null || command.getFileSize() <= 0) {
            throw new BusinessException("INVALID_COMMAND", "文件大小必須大於0");
        }
        
        if (isBlank(command.getFileContent()) && isBlank(command.getFilePath())) {
            throw new BusinessException("INVALID_COMMAND", "文件內容或文件路徑不能同時為空");
        }
    }
    
    /**
     * 建立上傳文件物件
     */
    private UploadedDocument buildUploadedDocument(UploadDocumentCommand command) {
        // 生成文件ID
        String documentId = generateDocumentId();
        
        return UploadedDocument.builder()
                .documentId(documentId)
                .documentType(command.getDocumentType())
                .originalFileName(command.getFileName())
                .fileSize(command.getFileSize())
                .filePath(command.getFilePath())
                .fileContent(command.getFileContent())
                .mimeType(command.getMimeType())
                .uploadTime(LocalDateTime.now())
                .status(UploadedDocument.DocumentStatus.UPLOADED)
                .description(command.getDescription())
                .uploadedBy(command.getUploadedBy())
                .uploaderIp(command.getUploaderIp())
                .build();
    }
    
    /**
     * 生成文件ID
     */
    private String generateDocumentId() {
        return "DOC" + System.currentTimeMillis();
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 上傳文件命令
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UploadDocumentCommand {
        
        /**
         * 補件編號
         */
        private String supplementId;
        
        /**
         * 文件類型
         */
        private String documentType;
        
        /**
         * 文件名稱
         */
        private String fileName;
        
        /**
         * 文件大小（位元組）
         */
        private Long fileSize;
        
        /**
         * 文件路徑（如果已儲存）
         */
        private String filePath;
        
        /**
         * 文件內容（如果直接上傳）
         */
        private String fileContent;
        
        /**
         * MIME類型
         */
        private String mimeType;
        
        /**
         * 文件描述
         */
        private String description;
        
        /**
         * 上傳者
         */
        private String uploadedBy;
        
        /**
         * 上傳者IP
         */
        private String uploaderIp;
    }
    
    /**
     * 上傳文件結果
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class UploadDocumentResult {
        
        /**
         * 是否成功
         */
        private Boolean success;
        
        /**
         * 文件ID
         */
        private String documentId;
        
        /**
         * 文件名稱
         */
        private String fileName;
        
        /**
         * 文件大小
         */
        private Long fileSize;
        
        /**
         * 上傳時間
         */
        private LocalDateTime uploadTime;
        
        /**
         * 文件狀態
         */
        private UploadedDocument.DocumentStatus status;
        
        /**
         * 回應訊息
         */
        private String message;
        
        /**
         * 錯誤代碼（如果失敗）
         */
        private String errorCode;
    }
}