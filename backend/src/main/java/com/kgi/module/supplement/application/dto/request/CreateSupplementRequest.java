package com.kgi.module.supplement.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 建立補件請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSupplementRequest {
    
    /**
     * 原始匯款編號
     */
    @NotBlank(message = "匯款編號不能為空")
    private String originalRemittanceId;
    
    /**
     * 申請人類型 (INDIVIDUAL/CORPORATE)
     */
    @NotBlank(message = "申請人類型不能為空")
    @Pattern(regexp = "^(INDIVIDUAL|CORPORATE)$", message = "申請人類型格式錯誤")
    private String applicantType;
    
    /**
     * 申請人識別碼 (身分證號或統一編號)
     */
    @NotBlank(message = "申請人識別碼不能為空")
    private String applicantIdentifier;
    
    /**
     * 申請人姓名/企業名稱
     */
    @NotBlank(message = "申請人姓名不能為空")
    @Size(max = 100, message = "申請人姓名長度不能超過100字元")
    private String applicantName;
    
    /**
     * 補件類型
     */
    @NotBlank(message = "補件類型不能為空")
    private String supplementType;
    
    /**
     * 補件原因
     */
    @NotBlank(message = "補件原因不能為空")
    @Size(max = 500, message = "補件原因長度不能超過500字元")
    private String reason;
    
    /**
     * 補件說明
     */
    @Size(max = 1000, message = "補件說明長度不能超過1000字元")
    private String description;
    
    /**
     * 聯絡電話
     */
    @Pattern(regexp = "^09\\d{8}$|^0\\d{1,2}-\\d{6,8}$", message = "聯絡電話格式錯誤")
    private String contactPhone;
    
    /**
     * 聯絡信箱
     */
    @Pattern(regexp = "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$", message = "聯絡信箱格式錯誤")
    private String contactEmail;
    
    /**
     * 優先級別
     */
    @Builder.Default
    private String priority = "NORMAL";
}