package com.kgi.module.supplement.application.usecase;

import com.kgi.module.remittance.domain.service.CustomerTypeService;
import com.kgi.core.domain.service.ExternalApiService;
import com.kgi.module.remittance.domain.service.RemittanceValidationService;
import com.kgi.module.supplement.application.dto.response.SupplementInitializeResponse;
import com.kgi.module.supplement.domain.service.SupplementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 初始化補件流程用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InitializeSupplementUseCase {
    
    private final CustomerTypeService customerTypeService;
    private final RemittanceValidationService validationService;
    private final ExternalApiService externalApiService;
    private final SupplementService supplementService;
    
    public SupplementInitializeResponse execute(String type, String caseNo) {
        log.info("執行初始化補件流程: type={}, caseNo={}", type, caseNo);
        
        // 1. 驗證URL參數
        if (!"S".equals(type)) {
            throw new IllegalArgumentException("無效的流程類型，必須是S(補件): " + type);
        }
        
        if (!validationService.isValidCaseNumber(caseNo)) {
            throw new IllegalArgumentException("無效的案件編號格式: " + caseNo);
        }
        
        // 2. 查詢TO API原始資料
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(caseNo);
        if (originalData == null || originalData.isEmpty()) {
            throw new IllegalArgumentException("查無此案件編號的資料: " + caseNo);
        }
        
        // 3. 自動判斷客戶類型
        String payeeId = (String) originalData.get("PayeeID");
        String customerType = customerTypeService.determineCustomerType(payeeId);
        
        // 4. 查詢補件需求
        Map<String, Object> supplementRequirements = supplementService.getSupplementRequirements(caseNo, customerType);
        
        // 5. 建立回應
        return SupplementInitializeResponse.builder()
                .caseNo(caseNo)
                .customerType(customerType)
                .flowType("SUPPLEMENT")
                .originalData(originalData)
                .supplementRequirements(supplementRequirements)
                .step("SUPPLEMENT_NOTIFICATION")
                .nextStepUrl("/api/ibr/application/supplement/notification")
                .sessionId(generateSessionId())
                .initTime(LocalDateTime.now().toString())
                .customerInfo(buildCustomerInfo(customerType, originalData))
                .availableActions(getAvailableSupplementActions(customerType))
                .build();
    }
    
    private String generateSessionId() {
        return "SUPP-SID-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    private Map<String, Object> buildCustomerInfo(String customerType, Map<String, Object> originalData) {
        Map<String, Object> info = new HashMap<>();
        
        if (customerTypeService.isIndividual(customerType)) {
            info.put("type", "個人客戶");
            info.put("payeeName", originalData.get("PayeeName"));
            info.put("payeeId", originalData.get("PayeeID"));
            info.put("supplementMethod", "線上上傳文件");
        } else {
            info.put("type", "企業客戶");
            info.put("companyName", originalData.get("PayeeName"));
            info.put("unifiedNumber", originalData.get("PayeeID"));
            info.put("supplementMethod", "線上上傳文件 + 工商憑證驗證");
        }
        
        return info;
    }
    
    private List<String> getAvailableSupplementActions(String customerType) {
        if (customerTypeService.isIndividual(customerType)) {
            return List.of("查看補件需求", "上傳文件", "身份驗證", "提交補件");
        } else {
            return List.of("查看補件需求", "上傳文件", "工商憑證驗證", "提交補件");
        }
    }
}