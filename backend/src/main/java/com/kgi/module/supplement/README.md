# Supplement Module - 補件作業模組

## 📋 模組概述

補件作業模組負責處理匯款解付過程中的文件補件和資料修正作業，提供完整的補件流程管理和追蹤功能。

## 🎯 核心功能

### 主要業務流程
1. **補件需求識別** - 系統自動識別需要補件的項目
2. **補件通知發送** - 透過多種管道通知客戶補件
3. **補件類型分類** - 區分個人/法人不同補件需求
4. **文件上傳處理** - 支援多種文件格式上傳
5. **補件內容審核** - 人工/自動審核補件內容
6. **補件狀態追蹤** - 即時追蹤補件進度
7. **補件完成確認** - 確認補件符合要求

### 特殊功能
- **智能補件提醒** - 根據缺件類型智能提醒
- **批次補件處理** - 支援多筆補件批次處理
- **補件期限管理** - 自動計算和提醒補件期限
- **歷史補件查詢** - 完整補件歷史記錄

## 🏗️ 架構設計

### Clean Architecture 分層

```
┌─────────────────────────────────────────────┐
│               Adapters Layer                │
│  SupplementController                       │
├─────────────────────────────────────────────┤
│              Application Layer              │
│  Facade │ UseCase │ DTO (Request/Response) │
├─────────────────────────────────────────────┤
│                Domain Layer                 │
│  SupplementRequest │ DocumentInfo          │
│  SupplementType │ Repository              │
├─────────────────────────────────────────────┤
│            Infrastructure Layer             │
│  JPA Repository │ File Storage Service    │
└─────────────────────────────────────────────┘
```

### 核心實體和值對象

#### 🔸 SupplementRequest (補件請求實體)
```java
- supplementId: String          // 補件編號
- remittanceId: String          // 關聯匯款編號
- requestType: SupplementType   // 補件類型
- status: SupplementStatus      // 補件狀態
- requiredDocuments: List<DocumentRequirement> // 必要文件清單
- submittedDocuments: List<DocumentInfo> // 已提交文件
- dueDate: LocalDateTime        // 補件期限
- requestDate: LocalDateTime    // 補件要求日期
- completedDate: LocalDateTime  // 完成日期
- notes: String                 // 備註說明
```

#### 🔸 DocumentInfo (文件資訊值對象)
```java
- documentId: String           // 文件編號
- fileName: String             // 檔案名稱
- fileType: String             // 檔案類型
- fileSize: Long               // 檔案大小
- uploadDate: LocalDateTime    // 上傳日期
- filePath: String             // 檔案路徑
- checksum: String             // 檔案校驗碼
- status: DocumentStatus       // 文件狀態
```

#### 🔸 DocumentRequirement (文件需求值對象)
```java
- requirementId: String        // 需求編號
- documentType: String         // 文件類型
- description: String          // 文件描述
- isMandatory: Boolean         // 是否必要
- acceptedFormats: List<String> // 接受格式
- maxFileSize: Long            // 最大檔案大小
- example: String              // 範例說明
```

## 🔗 API 端點

### RESTful API 設計

```http
GET    /api/ibr/supplement/{remittanceId}/requirements # 查詢補件需求
POST   /api/ibr/supplement/request                     # 建立補件需求
POST   /api/ibr/supplement/{supplementId}/documents    # 上傳補件文件
GET    /api/ibr/supplement/{supplementId}/status       # 查詢補件狀態
PUT    /api/ibr/supplement/{supplementId}/review       # 審核補件
POST   /api/ibr/supplement/batch/process              # 批次處理補件
GET    /api/ibr/supplement/notifications              # 查詢補件通知
```

### 請求/響應格式

#### 查詢補件需求響應
```json
{
  "StatusCode": "00",
  "TxntMsg": "查詢補件需求成功",
  "data": {
    "remittanceId": "IBR2025060100001",
    "supplementRequired": true,
    "requirements": [
      {
        "requirementId": "REQ001",
        "documentType": "身分證正本掃描",
        "description": "請提供清晰的身分證正反面掃描檔",
        "isMandatory": true,
        "acceptedFormats": ["PDF", "JPG", "PNG"],
        "maxFileSize": 5242880
      }
    ],
    "dueDate": "2025-06-08T23:59:59"
  },
  "timestamp": 1640995200000
}
```

#### 文件上傳請求
```json
{
  "supplementId": "SUP2025060100001",
  "documents": [
    {
      "requirementId": "REQ001",
      "fileName": "身分證.pdf",
      "fileType": "PDF",
      "fileContent": "base64EncodedContent...",
      "description": "身分證正反面掃描檔"
    }
  ]
}
```

## 📊 業務狀態管理

### 補件狀態 (SupplementStatus)
- `REQUIRED` - 需要補件
- `NOTIFIED` - 已通知
- `IN_PROGRESS` - 補件中
- `PARTIAL_SUBMITTED` - 部分提交
- `SUBMITTED` - 已提交
- `UNDER_REVIEW` - 審核中
- `APPROVED` - 審核通過
- `REJECTED` - 審核退件
- `COMPLETED` - 補件完成
- `EXPIRED` - 已逾期

### 文件狀態 (DocumentStatus)
- `UPLOADED` - 已上傳
- `VERIFIED` - 已驗證
- `APPROVED` - 審核通過
- `REJECTED` - 審核退件
- `REPLACED` - 已更換

### 補件類型 (SupplementType)
- `IDENTITY_VERIFICATION` - 身份驗證補件
- `BANK_INFORMATION` - 銀行資訊補件
- `COMPLIANCE_DOCUMENT` - 合規文件補件
- `SIGNATURE_VERIFICATION` - 簽章驗證補件
- `ADDITIONAL_INFORMATION` - 其他資訊補件

## 🔒 安全機制

### 文件安全
- **檔案類型檢查** - 嚴格檢查上傳檔案類型
- **病毒掃描** - 上傳檔案自動病毒掃描
- **檔案大小限制** - 限制單檔和總檔案大小
- **檔案完整性驗證** - 使用校驗碼驗證檔案完整性

### 存取控制
- **權限控制** - 僅授權人員可查看補件內容
- **資料遮罩** - 敏感資訊自動遮罩處理
- **操作記錄** - 完整記錄所有操作軌跡
- **定期清理** - 自動清理過期補件檔案

## 🔄 外部系統整合

### 整合系統
- **文件管理系統** - 檔案儲存和管理
- **通知服務** - 簡訊/郵件/推播通知
- **OCR識別服務** - 文件內容自動識別
- **影像處理服務** - 文件品質檢測和優化

### 事件發布
```java
// 補件需求產生事件
SupplementRequiredEvent
// 補件文件上傳事件
DocumentUploadedEvent
// 補件審核完成事件
SupplementReviewedEvent
// 補件逾期提醒事件
SupplementDueReminderEvent
// 補件完成事件
SupplementCompletedEvent
```

## 📈 效能考量

### 檔案處理最佳化
- **非同步上傳處理** - 大檔案非同步處理
- **分片上傳支援** - 支援大檔案分片上傳
- **CDN加速** - 檔案下載CDN加速
- **壓縮處理** - 自動檔案壓縮減少儲存空間

### 快取策略
- **補件需求快取** - 常用補件需求快取
- **檔案元資料快取** - 檔案資訊快取
- **使用者權限快取** - 使用者操作權限快取

## 🧪 測試策略

### 單元測試
- 檔案類型驗證邏輯
- 補件期限計算邏輯
- 文件狀態轉換邏輯
- 權限控制邏輯

### 整合測試  
- 檔案上傳流程測試
- 通知服務整合測試
- OCR服務整合測試
- API端點測試

### 安全測試
- 檔案上傳安全測試
- 檔案下載權限測試
- 惡意檔案防護測試

## 📝 使用範例

### 基本補件流程
```java
// 1. 查詢補件需求
SupplementRequirementResponse requirements = 
    supplementFacade.getSupplementRequirements("IBR2025060100001");

// 2. 建立補件請求
CreateSupplementRequest createRequest = CreateSupplementRequest.builder()
    .remittanceId("IBR2025060100001")
    .supplementType(SupplementType.IDENTITY_VERIFICATION)
    .requirements(requirements.getRequirements())
    .build();

SupplementResponse response = supplementFacade.createSupplement(createRequest);

// 3. 上傳補件文件
DocumentUploadRequest uploadRequest = DocumentUploadRequest.builder()
    .supplementId(response.getSupplementId())
    .documents(documentList)
    .build();

DocumentUploadResponse uploadResponse = 
    supplementFacade.uploadDocuments(uploadRequest);
```

### 批次補件處理
```java
// 批次處理補件審核
BatchSupplementRequest batchRequest = BatchSupplementRequest.builder()
    .supplementIds(supplementIds)
    .action(SupplementAction.APPROVE)
    .reviewNotes("批次審核通過")
    .build();

BatchSupplementResponse response = 
    supplementFacade.processBatch(batchRequest);
```

## 📊 監控指標

### 業務指標
- **補件完成率** - 在期限內完成補件的比例
- **平均補件時間** - 從需求到完成的平均時間
- **補件退件率** - 補件審核退件的比例
- **自動審核通過率** - 自動審核通過的比例

### 技術指標
- **檔案上傳成功率** - 檔案上傳成功比例
- **檔案處理時間** - 檔案處理平均時間
- **儲存空間使用率** - 檔案儲存空間使用情況

## 🔮 未來擴展

### 計劃功能
- **AI自動審核** - 使用AI技術自動審核補件
- **智能文件識別** - 自動識別文件類型和內容
- **行動端拍照上傳** - 手機拍照直接上傳功能
- **電子簽章整合** - 支援數位簽章補件

### 效能優化
- **分散式檔案儲存** - 支援分散式檔案儲存系統
- **檔案去重複化** - 相同檔案去重複儲存
- **智能檔案歸檔** - 自動檔案歸檔和清理

---

**模組負責人**: IBR開發團隊  
**最後更新**: 2025年6月1日  
**相關文件**: [IBR系統概覽](../../../../../../../.internal/ai_docs/00_IBR_system_overview.md)