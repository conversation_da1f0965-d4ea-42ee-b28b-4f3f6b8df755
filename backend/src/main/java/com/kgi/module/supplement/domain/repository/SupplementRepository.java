package com.kgi.module.supplement.domain.repository;

import com.kgi.module.supplement.domain.model.SupplementRequest;

import java.util.List;
import java.util.Optional;

/**
 * 補件倉儲介面
 * 定義補件相關的資料存取操作
 */
public interface SupplementRepository {
    
    /**
     * 保存補件請求
     */
    SupplementRequest save(SupplementRequest supplementRequest);
    
    /**
     * 根據補件ID查詢
     */
    Optional<SupplementRequest> findBySupplementId(String supplementId);
    
    /**
     * 根據申請人識別號查詢補件列表
     */
    List<SupplementRequest> findByApplicantIdentifier(String applicantIdentifier);
    
    /**
     * 根據原始解款編號查詢補件列表
     */
    List<SupplementRequest> findByOriginalRemittanceId(String originalRemittanceId);
    
    /**
     * 根據狀態查詢補件列表
     */
    List<SupplementRequest> findByStatus(SupplementRequest.SupplementStatus status);
    
    /**
     * 查詢指定狀態的補件列表（多個狀態）
     */
    List<SupplementRequest> findByStatusIn(List<SupplementRequest.SupplementStatus> statuses);
    
    /**
     * 查詢已逾期的補件請求
     */
    List<SupplementRequest> findExpiredRequests();
    
    /**
     * 查詢需要提醒的補件請求
     */
    List<SupplementRequest> findRequestsNeedingReminder();
    
    /**
     * 根據申請人類型查詢補件列表
     */
    List<SupplementRequest> findByApplicantType(SupplementRequest.ApplicantType applicantType);
    
    /**
     * 根據補件類型查詢補件列表
     */
    List<SupplementRequest> findBySupplementType(SupplementRequest.SupplementType supplementType);
    
    /**
     * 查詢處理人的補件列表
     */
    List<SupplementRequest> findByProcessorEmployeeId(String processorEmployeeId);
    
    /**
     * 查詢緊急補件列表
     */
    List<SupplementRequest> findUrgentRequests();
    
    /**
     * 根據ID刪除補件請求
     */
    void deleteBySupplementId(String supplementId);
    
    /**
     * 檢查補件是否存在
     */
    boolean existsBySupplementId(String supplementId);
    
    /**
     * 統計各狀態的補件數量
     */
    long countByStatus(SupplementRequest.SupplementStatus status);
    
    /**
     * 統計申請人的補件數量
     */
    long countByApplicantIdentifier(String applicantIdentifier);
    
    /**
     * 查詢所有補件請求（分頁）
     */
    List<SupplementRequest> findAll(int page, int size);
    
    /**
     * 根據多個條件查詢補件列表
     */
    List<SupplementRequest> findByCriteria(
            SupplementRequest.ApplicantType applicantType,
            SupplementRequest.SupplementType supplementType,
            SupplementRequest.SupplementStatus status,
            String applicantIdentifier,
            String originalRemittanceId,
            Boolean isUrgent,
            int page,
            int size
    );
}