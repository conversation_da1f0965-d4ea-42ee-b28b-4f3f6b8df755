package com.kgi.module.supplement.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.supplement.domain.model.UploadedDocument;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 文件驗證服務
 * 負責驗證上傳文件的格式、大小、內容等
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentValidationService {
    
    // 允許的文件類型
    private static final List<String> ALLOWED_FILE_TYPES = Arrays.asList(
            "pdf", "jpg", "jpeg", "png", "doc", "docx", "xls", "xlsx"
    );
    
    // 最大文件大小 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    // 最小文件大小 (1KB)
    private static final long MIN_FILE_SIZE = 1024;
    
    /**
     * 驗證上傳的文件
     */
    public void validateDocument(UploadedDocument document) {
        log.debug("驗證文件: fileName={}, fileSize={}", 
                document.getFileName(), document.getFileSize());
        
        // 基本欄位驗證
        validateBasicFields(document);
        
        // 文件類型驗證
        validateFileType(document);
        
        // 文件大小驗證
        validateFileSize(document);
        
        // 文件名稱驗證
        validateFileName(document);
        
        // 文件內容驗證（如果需要）
        validateFileContent(document);
        
        log.debug("文件驗證通過: fileName={}", document.getFileName());
    }
    
    /**
     * 驗證基本欄位
     */
    private void validateBasicFields(UploadedDocument document) {
        if (document == null) {
            throw new BusinessException("INVALID_DOCUMENT", "文件資訊不能為空");
        }
        
        if (isBlank(document.getFileName())) {
            throw new BusinessException("INVALID_DOCUMENT", "文件名稱不能為空");
        }
        
        if (document.getFileSize() == null || document.getFileSize() <= 0) {
            throw new BusinessException("INVALID_DOCUMENT", "文件大小必須大於0");
        }
        
        if (isBlank(document.getDocumentType())) {
            throw new BusinessException("INVALID_DOCUMENT", "文件類型不能為空");
        }
        
        if (document.getUploadTime() == null) {
            // 設置預設上傳時間
            document.setUploadTime(LocalDateTime.now());
        }
    }
    
    /**
     * 驗證文件類型
     */
    private void validateFileType(UploadedDocument document) {
        String fileName = document.getFileName().toLowerCase();
        String fileExtension = getFileExtension(fileName);
        
        if (isBlank(fileExtension)) {
            throw new BusinessException("INVALID_FILE_TYPE", "文件必須有副檔名");
        }
        
        if (!ALLOWED_FILE_TYPES.contains(fileExtension)) {
            throw new BusinessException("INVALID_FILE_TYPE", 
                    String.format("不支援的文件類型: %s，支援的類型: %s", 
                            fileExtension, String.join(", ", ALLOWED_FILE_TYPES)));
        }
    }
    
    /**
     * 驗證文件大小
     */
    private void validateFileSize(UploadedDocument document) {
        long fileSize = document.getFileSize();
        
        if (fileSize < MIN_FILE_SIZE) {
            throw new BusinessException("FILE_TOO_SMALL", 
                    String.format("文件太小，最小大小為 %d KB", MIN_FILE_SIZE / 1024));
        }
        
        if (fileSize > MAX_FILE_SIZE) {
            throw new BusinessException("FILE_TOO_LARGE", 
                    String.format("文件太大，最大大小為 %d MB", MAX_FILE_SIZE / (1024 * 1024)));
        }
    }
    
    /**
     * 驗證文件名稱
     */
    private void validateFileName(UploadedDocument document) {
        String fileName = document.getFileName();
        
        // 檢查文件名稱長度
        if (fileName.length() > 255) {
            throw new BusinessException("INVALID_FILE_NAME", "文件名稱過長，最多255個字符");
        }
        
        // 檢查文件名稱中的特殊字符
        if (fileName.matches(".*[<>:\"/\\\\|?*].*")) {
            throw new BusinessException("INVALID_FILE_NAME", "文件名稱包含不允許的特殊字符");
        }
        
        // 檢查是否為隱藏文件
        if (fileName.startsWith(".")) {
            throw new BusinessException("INVALID_FILE_NAME", "不允許上傳隱藏文件");
        }
    }
    
    /**
     * 驗證文件內容（基礎檢查）
     */
    private void validateFileContent(UploadedDocument document) {
        // 根據文件類型進行內容驗證
        String documentType = document.getDocumentType();
        String fileExtension = getFileExtension(document.getFileName().toLowerCase());
        
        // 驗證文件類型與內容是否匹配
        switch (documentType) {
            case "IDENTITY_CARD":
                validateIdentityDocument(document, fileExtension);
                break;
            case "BANK_STATEMENT":
                validateBankStatement(document, fileExtension);
                break;
            case "INCOME_PROOF":
                validateIncomeProof(document, fileExtension);
                break;
            case "BUSINESS_LICENSE":
                validateBusinessLicense(document, fileExtension);
                break;
            default:
                // 其他類型的基本驗證
                validateGeneralDocument(document, fileExtension);
                break;
        }
    }
    
    /**
     * 驗證身分證明文件
     */
    private void validateIdentityDocument(UploadedDocument document, String fileExtension) {
        // 身分證明文件通常應該是圖片或PDF格式
        List<String> allowedTypes = Arrays.asList("pdf", "jpg", "jpeg", "png");
        if (!allowedTypes.contains(fileExtension)) {
            throw new BusinessException("INVALID_DOCUMENT_TYPE", 
                    "身分證明文件必須是PDF或圖片格式");
        }
        
        // 可以添加更多針對身分證明文件的驗證邏輯
        // 例如：OCR識別、格式檢查等
    }
    
    /**
     * 驗證銀行對帳單
     */
    private void validateBankStatement(UploadedDocument document, String fileExtension) {
        // 銀行對帳單通常是PDF格式
        if (!"pdf".equals(fileExtension)) {
            throw new BusinessException("INVALID_DOCUMENT_TYPE", 
                    "銀行對帳單必須是PDF格式");
        }
        
        // 可以添加更多針對銀行對帳單的驗證邏輯
    }
    
    /**
     * 驗證收入證明
     */
    private void validateIncomeProof(UploadedDocument document, String fileExtension) {
        // 收入證明可以是PDF、Word或圖片格式
        List<String> allowedTypes = Arrays.asList("pdf", "doc", "docx", "jpg", "jpeg", "png");
        if (!allowedTypes.contains(fileExtension)) {
            throw new BusinessException("INVALID_DOCUMENT_TYPE", 
                    "收入證明必須是PDF、Word文檔或圖片格式");
        }
    }
    
    /**
     * 驗證營業執照
     */
    private void validateBusinessLicense(UploadedDocument document, String fileExtension) {
        // 營業執照通常是圖片或PDF格式
        List<String> allowedTypes = Arrays.asList("pdf", "jpg", "jpeg", "png");
        if (!allowedTypes.contains(fileExtension)) {
            throw new BusinessException("INVALID_DOCUMENT_TYPE", 
                    "營業執照必須是PDF或圖片格式");
        }
    }
    
    /**
     * 驗證一般文件
     */
    private void validateGeneralDocument(UploadedDocument document, String fileExtension) {
        // 一般文件的基本驗證已在validateFileType中完成
        log.debug("一般文件驗證通過: fileName={}", document.getFileName());
    }
    
    /**
     * 驗證文件是否可以重新上傳
     */
    public boolean canReupload(UploadedDocument existingDocument) {
        if (existingDocument == null) {
            return true;
        }
        
        // 如果文件狀態是被拒絕，可以重新上傳
        return existingDocument.getStatus() == UploadedDocument.DocumentStatus.REJECTED;
    }
    
    /**
     * 驗證文件是否需要人工審核
     */
    public boolean requiresManualReview(UploadedDocument document) {
        // 根據文件類型和大小決定是否需要人工審核
        
        // 大文件需要人工審核
        if (document.getFileSize() > 5 * 1024 * 1024) { // 5MB
            return true;
        }
        
        // 某些敏感文件類型需要人工審核
        List<String> sensitiveTypes = Arrays.asList("IDENTITY_CARD", "BUSINESS_LICENSE");
        if (sensitiveTypes.contains(document.getDocumentType())) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 取得文件品質評分
     */
    public int getDocumentQualityScore(UploadedDocument document) {
        int score = 100;
        
        // 根據文件大小扣分
        if (document.getFileSize() < 100 * 1024) { // 小於100KB
            score -= 20;
        }
        
        // 根據文件類型扣分
        String fileExtension = getFileExtension(document.getFileName().toLowerCase());
        if ("jpg".equals(fileExtension) || "jpeg".equals(fileExtension)) {
            score -= 10; // 壓縮格式可能影響品質
        }
        
        return Math.max(score, 0);
    }
    
    // ==================== 私有輔助方法 ====================
    
    private String getFileExtension(String fileName) {
        if (isBlank(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex + 1).toLowerCase();
    }
    
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
}