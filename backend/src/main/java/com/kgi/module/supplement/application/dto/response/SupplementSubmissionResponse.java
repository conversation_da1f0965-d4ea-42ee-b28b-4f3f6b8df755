package com.kgi.module.supplement.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 補件提交回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementSubmissionResponse {
    
    /**
     * 補件編號
     */
    private String supplementId;
    
    /**
     * 提交確認編號
     */
    private String confirmationNumber;
    
    /**
     * 提交狀態
     */
    private String submissionStatus;
    
    /**
     * 提交時間
     */
    private LocalDateTime submissionTime;
    
    /**
     * 申請人姓名 (遮罩)
     */
    private String applicantName;
    
    /**
     * 已提交文件數量
     */
    private Integer submittedDocumentCount;
    
    /**
     * 預估審核時間
     */
    private LocalDateTime estimatedReviewTime;
    
    /**
     * 預估完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 提交摘要
     */
    private SubmissionSummary submissionSummary;
    
    /**
     * 下一步驟
     */
    private List<String> nextSteps;
    
    /**
     * 重要提醒
     */
    private List<String> importantReminders;
    
    /**
     * 聯絡資訊
     */
    private ContactInformation contactInformation;
    
    /**
     * 追蹤資訊
     */
    private TrackingInformation trackingInformation;
    
    /**
     * 提交摘要內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubmissionSummary {
        
        /**
         * 必要文件完成度
         */
        private Integer requiredDocumentCompleteness;
        
        /**
         * 選擇性文件完成度
         */
        private Integer optionalDocumentCompleteness;
        
        /**
         * 整體完成度
         */
        private Integer overallCompleteness;
        
        /**
         * 提交文件列表
         */
        private List<SubmittedDocument> submittedDocuments;
        
        /**
         * 缺少文件列表
         */
        private List<String> missingDocuments;
        
        /**
         * 品質檢查結果
         */
        private QualityCheckResult qualityCheck;
    }
    
    /**
     * 已提交文件內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubmittedDocument {
        
        /**
         * 文件編號
         */
        private String documentId;
        
        /**
         * 文件類型
         */
        private String documentType;
        
        /**
         * 檔案名稱
         */
        private String fileName;
        
        /**
         * 上傳時間
         */
        private LocalDateTime uploadTime;
        
        /**
         * 驗證狀態
         */
        private String validationStatus;
        
        /**
         * 品質評分
         */
        private Integer qualityScore;
    }
    
    /**
     * 品質檢查結果內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QualityCheckResult {
        
        /**
         * 整體品質評分
         */
        private Integer overallScore;
        
        /**
         * 品質等級
         */
        private String qualityGrade;
        
        /**
         * 通過檢查的文件數
         */
        private Integer passedDocuments;
        
        /**
         * 需要改善的文件數
         */
        private Integer improvementNeededDocuments;
        
        /**
         * 不合格的文件數
         */
        private Integer failedDocuments;
        
        /**
         * 建議改善項目
         */
        private List<String> improvements;
    }
    
    /**
     * 聯絡資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInformation {
        
        /**
         * 客服專線
         */
        private String customerServiceHotline;
        
        /**
         * 電子信箱
         */
        private String email;
        
        /**
         * 線上客服
         */
        private String onlineService;
        
        /**
         * 營業時間
         */
        private String businessHours;
        
        /**
         * 緊急聯絡方式
         */
        private String emergencyContact;
    }
    
    /**
     * 追蹤資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingInformation {
        
        /**
         * 追蹤網址
         */
        private String trackingUrl;
        
        /**
         * 查詢密碼
         */
        private String queryPassword;
        
        /**
         * 手機簡訊通知
         */
        private Boolean smsNotificationEnabled;
        
        /**
         * 電子郵件通知
         */
        private Boolean emailNotificationEnabled;
        
        /**
         * 推播通知
         */
        private Boolean pushNotificationEnabled;
        
        /**
         * 通知頻率
         */
        private String notificationFrequency;
    }
}