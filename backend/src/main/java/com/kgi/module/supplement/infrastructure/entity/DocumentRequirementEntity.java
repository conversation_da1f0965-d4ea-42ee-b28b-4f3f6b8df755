package com.kgi.module.supplement.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件需求實體
 * 對應資料庫中的文件需求表
 */
@Entity
@Table(name = "document_requirements")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentRequirementEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplement_id", nullable = false)
    private SupplementRequestEntity supplementRequest;
    
    @Column(name = "document_type", length = 100, nullable = false)
    private String documentType;
    
    @Column(name = "document_name", length = 200, nullable = false)
    private String documentName;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "is_required", nullable = false)
    private Boolean isRequired;
    
    @Column(name = "max_file_size")
    private Long maxFileSize;
    
    @Column(name = "allowed_formats", length = 200)
    private String allowedFormats;
    
    @Column(name = "is_uploaded", nullable = false)
    @Builder.Default
    private Boolean isUploaded = false;
    
    @Column(name = "uploaded_time")
    private LocalDateTime uploadedTime;
    
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (updatedTime == null) {
            updatedTime = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}