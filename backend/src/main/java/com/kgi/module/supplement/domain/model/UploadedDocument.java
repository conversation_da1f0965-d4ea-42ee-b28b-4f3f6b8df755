package com.kgi.module.supplement.domain.model;

import lombok.*;

import java.time.LocalDateTime;

/**
 * 已上傳文件值對象
 * 記錄使用者上傳的補件文件資訊
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UploadedDocument {
    
    /**
     * 文件ID
     */
    private String documentId;
    
    /**
     * 文件類型（對應DocumentRequirement的documentType）
     */
    private String documentType;
    
    /**
     * 原始檔案名稱
     */
    private String originalFileName;
    
    /**
     * 儲存的檔案名稱
     */
    private String storedFileName;
    
    /**
     * 檔案路徑
     */
    private String filePath;
    
    /**
     * 檔案大小（bytes）
     */
    private Long fileSize;
    
    /**
     * 檔案格式/MIME類型
     */
    private String mimeType;
    
    /**
     * 上傳時間
     */
    private LocalDateTime uploadTime;
    
    /**
     * 上傳者IP
     */
    private String uploaderIp;
    
    /**
     * 檔案狀態
     */
    private DocumentStatus status;
    
    /**
     * 驗證結果
     */
    private ValidationResult validationResult;
    
    /**
     * 檔案說明
     */
    private String description;
    
    /**
     * 檔案內容（如果直接上傳）
     */
    private String fileContent;
    
    /**
     * 審核時間
     */
    private LocalDateTime reviewTime;
    
    /**
     * 審核者員工編號
     */
    private String reviewerEmployeeId;
    
    /**
     * 審核備註
     */
    private String reviewNotes;
    
    /**
     * 品質評分
     */
    private Integer qualityScore;
    
    /**
     * 上傳者
     */
    private String uploadedBy;
    
    /**
     * 檔案MD5雜湊值
     */
    private String md5Hash;
    
    /**
     * 檔案縮圖路徑（圖片類型）
     */
    private String thumbnailPath;
    
    /**
     * 是否已病毒掃描
     */
    private Boolean virusScanned;
    
    /**
     * 病毒掃描結果
     */
    private Boolean virusScanClean;
    
    /**
     * 文件狀態枚舉
     */
    public enum DocumentStatus {
        UPLOADING("上傳中"),
        UPLOADED("已上傳"),
        VALIDATED("已驗證"),
        REJECTED("已拒絕"),
        APPROVED("已核准"),
        ARCHIVED("已歸檔");
        
        private final String description;
        
        DocumentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 驗證結果枚舉
     */
    public enum ValidationResult {
        PENDING("待驗證"),
        VALID("格式正確"),
        INVALID_FORMAT("格式錯誤"),
        INVALID_SIZE("檔案過大"),
        INVALID_CONTENT("內容不符"),
        VIRUS_DETECTED("發現病毒"),
        CORRUPTED("檔案損毀");
        
        private final String description;
        
        ValidationResult(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 建立新的上傳文件記錄
     */
    public static UploadedDocument createNew(
            String documentId,
            String documentType,
            String originalFileName,
            String storedFileName,
            String filePath,
            Long fileSize,
            String mimeType,
            String uploaderIp) {
        
        return UploadedDocument.builder()
                .documentId(documentId)
                .documentType(documentType)
                .originalFileName(originalFileName)
                .storedFileName(storedFileName)
                .filePath(filePath)
                .fileSize(fileSize)
                .mimeType(mimeType)
                .uploadTime(LocalDateTime.now())
                .uploaderIp(uploaderIp)
                .status(DocumentStatus.UPLOADING)
                .validationResult(ValidationResult.PENDING)
                .virusScanned(false)
                .build();
    }
    
    /**
     * 完成上傳
     */
    public void completeUpload(String md5Hash) {
        this.status = DocumentStatus.UPLOADED;
        this.md5Hash = md5Hash;
    }
    
    /**
     * 設定驗證結果
     */
    public void setValidation(ValidationResult result, String description) {
        this.validationResult = result;
        this.description = description;
        
        if (result == ValidationResult.VALID) {
            this.status = DocumentStatus.VALIDATED;
        } else {
            this.status = DocumentStatus.REJECTED;
        }
    }
    
    /**
     * 核准文件
     */
    public void approve() {
        this.status = DocumentStatus.APPROVED;
    }
    
    /**
     * 拒絕文件
     */
    public void reject(String reason) {
        this.status = DocumentStatus.REJECTED;
        this.description = reason;
    }
    
    /**
     * 設定病毒掃描結果
     */
    public void setVirusScanResult(boolean isClean) {
        this.virusScanned = true;
        this.virusScanClean = isClean;
        
        if (!isClean) {
            this.validationResult = ValidationResult.VIRUS_DETECTED;
            this.status = DocumentStatus.REJECTED;
        }
    }
    
    /**
     * 設定縮圖路徑
     */
    public void setThumbnail(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }
    
    /**
     * 檢查是否為圖片檔案
     */
    public boolean isImageFile() {
        if (mimeType == null) {
            return false;
        }
        
        return mimeType.startsWith("image/");
    }
    
    /**
     * 檢查是否為PDF檔案
     */
    public boolean isPdfFile() {
        return "application/pdf".equals(mimeType);
    }
    
    /**
     * 取得檔案副檔名
     */
    public String getFileExtension() {
        if (originalFileName == null || originalFileName.isEmpty()) {
            return null;
        }
        
        int lastDotIndex = originalFileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == originalFileName.length() - 1) {
            return null;
        }
        
        return originalFileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 取得檔案大小（人類可讀格式）
     */
    public String getFileSizeFormatted() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 檢查檔案是否有效
     */
    public boolean isValid() {
        return status == DocumentStatus.VALIDATED || 
               status == DocumentStatus.APPROVED;
    }
    
    /**
     * 檢查是否可以下載
     */
    public boolean isDownloadable() {
        return status != DocumentStatus.UPLOADING && 
               validationResult != ValidationResult.VIRUS_DETECTED;
    }
    
    /**
     * 獲取檔案名稱（為了向後相容性）
     */
    public String getFileName() {
        return originalFileName;
    }
    
    /**
     * 設定檔案名稱（為了向後相容性）
     */
    public void setFileName(String fileName) {
        this.originalFileName = fileName;
    }
}