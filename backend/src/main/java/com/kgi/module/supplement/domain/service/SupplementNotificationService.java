package com.kgi.module.supplement.domain.service;

import com.kgi.module.supplement.domain.model.SupplementRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 補件通知服務
 * 負責發送補件相關的通知
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplementNotificationService {
    
    /**
     * 發送補件請求通知
     */
    public void sendSupplementRequestNotification(SupplementRequest supplementRequest) {
        log.info("發送補件請求通知: supplementId={}", supplementRequest.getSupplementId());
        
        // TODO: 實際實作通知邏輯
        // 可以整合郵件、簡訊等通知方式
        
        String message = String.format(
                "親愛的客戶，您的解款申請 %s 需要補件，補件編號：%s，請於 %s 前完成補件。",
                supplementRequest.getOriginalRemittanceId(),
                supplementRequest.getSupplementId(),
                supplementRequest.getDeadline()
        );
        
        log.info("補件請求通知已發送: {}", message);
    }
    
    /**
     * 發送開始補件通知
     */
    public void sendSupplementStartNotification(SupplementRequest supplementRequest) {
        log.info("發送開始補件通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您已開始補件流程，補件編號：%s，請上傳所需文件。",
                supplementRequest.getSupplementId()
        );
        
        log.info("開始補件通知已發送: {}", message);
    }
    
    /**
     * 發送補件完成通知
     */
    public void sendSupplementCompleteNotification(SupplementRequest supplementRequest) {
        log.info("發送補件完成通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件已提交完成，補件編號：%s，正在進行審核。",
                supplementRequest.getSupplementId()
        );
        
        log.info("補件完成通知已發送: {}", message);
    }
    
    /**
     * 發送補件核准通知
     */
    public void sendSupplementApprovedNotification(SupplementRequest supplementRequest) {
        log.info("發送補件核准通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件已審核通過，補件編號：%s，解款流程將繼續進行。",
                supplementRequest.getSupplementId()
        );
        
        log.info("補件核准通知已發送: {}", message);
    }
    
    /**
     * 發送補件拒絕通知
     */
    public void sendSupplementRejectedNotification(SupplementRequest supplementRequest) {
        log.info("發送補件拒絕通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件未通過審核，補件編號：%s，原因：%s",
                supplementRequest.getSupplementId(),
                supplementRequest.getProcessingNotes()
        );
        
        log.info("補件拒絕通知已發送: {}", message);
    }
    
    /**
     * 發送補件已完成通知
     */
    public void sendSupplementCompletedNotification(SupplementRequest supplementRequest) {
        log.info("發送補件已完成通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件流程已完成，補件編號：%s，解款申請將繼續處理。",
                supplementRequest.getSupplementId()
        );
        
        log.info("補件已完成通知已發送: {}", message);
    }
    
    /**
     * 發送補件取消通知
     */
    public void sendSupplementCancelledNotification(SupplementRequest supplementRequest) {
        log.info("發送補件取消通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件已取消，補件編號：%s，原因：%s",
                supplementRequest.getSupplementId(),
                supplementRequest.getProcessingNotes()
        );
        
        log.info("補件取消通知已發送: {}", message);
    }
    
    /**
     * 發送補件逾期通知
     */
    public void sendSupplementExpiredNotification(SupplementRequest supplementRequest) {
        log.info("發送補件逾期通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件已逾期，補件編號：%s，截止時間：%s",
                supplementRequest.getSupplementId(),
                supplementRequest.getDeadline()
        );
        
        log.info("補件逾期通知已發送: {}", message);
    }
    
    /**
     * 發送補件截止時間延長通知
     */
    public void sendSupplementDeadlineExtendedNotification(SupplementRequest supplementRequest) {
        log.info("發送補件截止時間延長通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "您的補件截止時間已延長，補件編號：%s，新的截止時間：%s",
                supplementRequest.getSupplementId(),
                supplementRequest.getDeadline()
        );
        
        log.info("補件截止時間延長通知已發送: {}", message);
    }
    
    /**
     * 發送補件提醒通知
     */
    public void sendSupplementReminderNotification(SupplementRequest supplementRequest) {
        log.info("發送補件提醒通知: supplementId={}", supplementRequest.getSupplementId());
        
        String message = String.format(
                "提醒：您的補件即將截止，補件編號：%s，截止時間：%s，請儘快完成補件。",
                supplementRequest.getSupplementId(),
                supplementRequest.getDeadline()
        );
        
        log.info("補件提醒通知已發送: {}", message);
    }
}