package com.kgi.module.supplement.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 補件狀態回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementStatusResponse {
    
    /**
     * 案件編號
     */
    private String caseNo;
    
    /**
     * 補件編號
     */
    private String supplementId;
    
    /**
     * 原始匯款編號
     */
    private String originalRemittanceId;
    
    /**
     * 當前狀態
     */
    private String status;
    
    /**
     * 當前狀態
     */
    private String currentStatus;
    
    /**
     * 狀態描述
     */
    private String statusDescription;
    
    /**
     * 補件原因
     */
    private String reason;
    
    /**
     * 進度百分比 (0-100)
     */
    private Integer progressPercentage;
    
    /**
     * 截止期限 (字串格式)
     */
    private String deadline;
    
    /**
     * 截止期限
     */
    private LocalDateTime deadlineTime;
    
    /**
     * 剩餘天數
     */
    private Integer remainingDays;
    
    /**
     * 嘗試次數
     */
    private Integer attemptCount;
    
    /**
     * 最大嘗試次數
     */
    private Integer maxAttempts;
    
    /**
     * 已上傳文件清單
     */
    private List<Map<String, Object>> uploadedDocuments;
    
    /**
     * 已上傳文件數量
     */
    private Integer uploadedDocumentCount;
    
    /**
     * 必要文件數量
     */
    private Integer requiredDocuments;
    
    /**
     * 最後更新時間 (字串格式)
     */
    private String lastUpdateTime;
    
    /**
     * 最後更新時間
     */
    private LocalDateTime lastUpdateDateTime;
    
    /**
     * 狀態歷史
     */
    private List<StatusHistory> statusHistory;
    
    /**
     * 待辦事項
     */
    private List<PendingAction> pendingActions;
    
    /**
     * 審核回饋
     */
    private List<ReviewFeedback> reviewFeedbacks;
    
    /**
     * 預估完成時間
     */
    private LocalDateTime estimatedCompletion;
    
    /**
     * 狀態歷史內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusHistory {
        
        /**
         * 狀態
         */
        private String status;
        
        /**
         * 狀態描述
         */
        private String description;
        
        /**
         * 時間戳記
         */
        private LocalDateTime timestamp;
        
        /**
         * 操作人員
         */
        private String operator;
        
        /**
         * 備註
         */
        private String notes;
    }
    
    /**
     * 待辦事項內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PendingAction {
        
        /**
         * 動作類型
         */
        private String actionType;
        
        /**
         * 動作描述
         */
        private String description;
        
        /**
         * 優先級
         */
        private String priority;
        
        /**
         * 期限
         */
        private LocalDateTime dueDate;
        
        /**
         * 是否必要
         */
        private Boolean required;
    }
    
    /**
     * 審核回饋內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReviewFeedback {
        
        /**
         * 回饋類型
         */
        private String feedbackType;
        
        /**
         * 回饋內容
         */
        private String content;
        
        /**
         * 嚴重程度
         */
        private String severity;
        
        /**
         * 建議措施
         */
        private String recommendation;
        
        /**
         * 回饋時間
         */
        private LocalDateTime feedbackTime;
        
        /**
         * 審核人員
         */
        private String reviewer;
    }
}