package com.kgi.module.supplement.application.usecase;

import com.kgi.core.domain.service.ExternalApiService;
import com.kgi.module.supplement.application.dto.request.SupplementSubmitRequest;
import com.kgi.module.supplement.application.dto.response.SupplementSubmitResponse;
import com.kgi.core.domain.service.DocumentService;
import com.kgi.module.supplement.domain.service.SupplementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 提交補件用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubmitSupplementUseCase {
    
    private final SupplementService supplementService;
    private final DocumentService documentService;
    private final ExternalApiService externalApiService;
    
    public SupplementSubmitResponse execute(SupplementSubmitRequest request) {
        log.info("執行補件提交: caseNo={}, customerType={}", 
                request.getCaseNo(), request.getCustomerType());
        
        // 1. 驗證文件完整性
        List<Map<String, Object>> requiredDocs = supplementService.getRequiredDocuments(request.getCustomerType());
        List<Map<String, Object>> uploadedDocs = documentService.getDocumentList(request.getCaseNo());
        
        List<String> uploadedDocTypes = uploadedDocs.stream()
                .map(doc -> (String) doc.get("documentType"))
                .collect(Collectors.toList());
        
        Map<String, Object> completenessCheck = documentService.validateDocumentCompleteness(
                request.getCaseNo(), requiredDocs, uploadedDocTypes);
        
        if (!(boolean) completenessCheck.get("isComplete")) {
            throw new IllegalArgumentException("必要文件尚未上傳完成: " + 
                    completenessCheck.get("missingDocuments"));
        }
        
        // 2. 驗證補件資料
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(request.getCaseNo());
        Map<String, Object> validationResult = supplementService.validateSupplementData(
                request.getCustomerType(), request.getSupplementData(), originalData);
        
        if (!(boolean) validationResult.get("isValid")) {
            throw new IllegalArgumentException("補件資料驗證失敗: " + 
                    validationResult.get("errors"));
        }
        
        // 3. 準備提交資料
        Map<String, Object> submissionData = new HashMap<>();
        submissionData.put("caseNo", request.getCaseNo());
        submissionData.put("customerType", request.getCustomerType());
        submissionData.put("supplementData", request.getSupplementData());
        submissionData.put("uploadedDocuments", uploadedDocs);
        submissionData.put("submissionTime", LocalDateTime.now().toString());
        submissionData.put("supplementId", supplementService.generateSupplementId());
        
        // 4. 呼叫 FROM API
        Map<String, Object> fromApiResult = externalApiService.callFromApi(submissionData);
        
        // 5. 儲存提交記錄
        supplementService.saveSupplementSubmission(submissionData);
        
        // 6. 更新補件狀態
        supplementService.updateSupplementStatus(request.getCaseNo(), "SUBMITTED");
        
        // 7. 建立回應
        return SupplementSubmitResponse.builder()
                .caseNo(request.getCaseNo())
                .customerType(request.getCustomerType())
                .supplementId((String) submissionData.get("supplementId"))
                .submissionStatus("SUCCESS")
                .fromApiResult(fromApiResult)
                .submissionTime(LocalDateTime.now().toString())
                .message("補件提交成功")
                .nextStep("SUPPLEMENT_COMPLETE")
                .nextStepUrl("/api/ibr/application/supplement/complete")
                .build();
    }
}