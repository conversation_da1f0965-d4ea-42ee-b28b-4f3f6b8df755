package com.kgi.module.supplement.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 補件通知響應
 */
@Data
@Builder
public class SupplementNotificationResponse {
    private String caseNo;
    private String customerType;
    private String notificationType;
    private String reason;
    private List<Map<String, Object>> requiredDocuments;
    private String deadline;
    private Integer attemptCount;
    private Integer maxAttempts;
    private String instructions;
    private String nextStep;
    private String nextStepUrl;
    private String notificationTime;
}