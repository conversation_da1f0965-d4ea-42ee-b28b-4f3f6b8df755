package com.kgi.module.supplement.application.usecase;

import com.kgi.module.supplement.application.dto.request.*;
import com.kgi.module.supplement.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 補件管理用例
 * 處理補件流程相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SupplementManagementUseCase {
    
    /**
     * 建立補件請求
     */
    public SupplementCreationResponse createSupplementRequest(CreateSupplementRequest request) {
        log.info("建立補件請求: remittanceId={}, applicantType={}, identifier={}", 
                request.getOriginalRemittanceId(), 
                request.getApplicantType(),
                maskIdentifier(request.getApplicantIdentifier()));
        
        // TODO: 實作補件請求建立邏輯
        return SupplementCreationResponse.builder()
                .supplementId("SUP" + System.currentTimeMillis())
                .originalRemittanceId(request.getOriginalRemittanceId())
                .applicantIdentifier(maskIdentifier(request.getApplicantIdentifier()))
                .applicantName(request.getApplicantName())
                .supplementType(request.getSupplementType())
                .status("CREATED")
                .creationTime(LocalDateTime.now())
                .deadline(LocalDateTime.now().plusDays(7))
                .requiredDocuments(getRequiredDocuments(request.getSupplementType()))
                .nextSteps(List.of(
                    "1. 準備所需文件",
                    "2. 上傳文件至系統",
                    "3. 確認文件內容無誤",
                    "4. 提交補件申請"
                ))
                .importantNotices(List.of(
                    "請於期限內完成補件，逾期將影響申請進度",
                    "上傳文件須清晰可辨識",
                    "如有疑問請洽客服專線"
                ))
                .contactInfo(getContactInfo())
                .build();
    }
    
    /**
     * 查詢補件狀態
     */
    public SupplementStatusResponse getSupplementStatus(String supplementId) {
        log.info("查詢補件狀態: supplementId={}", supplementId);
        
        // TODO: 實作補件狀態查詢邏輯
        return SupplementStatusResponse.builder()
                .supplementId(supplementId)
                .originalRemittanceId("IBR" + System.currentTimeMillis())
                .currentStatus("IN_PROGRESS")
                .statusDescription("補件進行中")
                .progressPercentage(60)
                .deadline(LocalDateTime.now().plusDays(5).toString())
                .remainingDays(5)
                .uploadedDocumentCount(2)
                .requiredDocuments(3)
                .lastUpdateTime(LocalDateTime.now().minusHours(2).toString())
                .statusHistory(getStatusHistory())
                .pendingActions(getPendingActions())
                .reviewFeedbacks(getReviewFeedbacks())
                .estimatedCompletion(LocalDateTime.now().plusDays(3))
                .build();
    }
    
    /**
     * 提交補件
     */
    public SupplementSubmissionResponse submitSupplement(SupplementSubmitRequest request) {
        log.info("提交補件: supplementId={}", request.getSupplementId());
        
        // TODO: 實作補件提交邏輯
        return SupplementSubmissionResponse.builder()
                .supplementId(request.getSupplementId())
                .confirmationNumber("CNF" + System.currentTimeMillis())
                .submissionStatus("SUBMITTED")
                .submissionTime(LocalDateTime.now())
                .applicantName("王小明")
                .submittedDocumentCount(request.getDocumentIds() != null ? request.getDocumentIds().size() : 0)
                .estimatedReviewTime(LocalDateTime.now().plusHours(24))
                .estimatedCompletionTime(LocalDateTime.now().plusDays(2))
                .submissionSummary(buildSubmissionSummary(request))
                .nextSteps(List.of(
                    "等待系統審核文件",
                    "如有問題將主動聯繫",
                    "可隨時查詢處理進度"
                ))
                .importantReminders(List.of(
                    "請保持聯絡方式暢通",
                    "如需補充資料將另行通知",
                    "預計2個工作天完成審核"
                ))
                .contactInformation(buildContactInformation())
                .trackingInformation(buildTrackingInformation(request.getSupplementId()))
                .build();
    }
    
    /**
     * 取消補件
     */
    public String cancelSupplement(String supplementId, String reason) {
        log.info("取消補件: supplementId={}, reason={}", supplementId, reason);
        
        // TODO: 實作補件取消邏輯
        return "補件已成功取消";
    }
    
    /**
     * 延長補件期限
     */
    public String extendDeadline(String supplementId, int days, String reason) {
        log.info("延長補件期限: supplementId={}, days={}, reason={}", 
                supplementId, days, reason);
        
        // TODO: 實作期限延長邏輯
        return "補件期限已延長 " + days + " 天";
    }
    
    /**
     * 查詢補件歷史
     */
    public List<SupplementStatusResponse> getSupplementHistory(String applicantIdentifier, int page, int size) {
        log.info("查詢補件歷史: applicantIdentifier={}, page={}, size={}", 
                maskIdentifier(applicantIdentifier), page, size);
        
        // TODO: 實作補件歷史查詢邏輯
        return List.of();
    }
    
    private List<SupplementCreationResponse.RequiredDocument> getRequiredDocuments(String supplementType) {
        return List.of(
            SupplementCreationResponse.RequiredDocument.builder()
                    .type("IDENTITY")
                    .name("身分證明文件")
                    .description("身分證正反面或護照個人資料頁")
                    .required(true)
                    .allowedFormats(List.of("JPG", "PNG", "PDF"))
                    .maxSizeMB(5)
                    .example("身分證正面、反面清晰照片")
                    .build(),
            SupplementCreationResponse.RequiredDocument.builder()
                    .type("BANK_ACCOUNT")
                    .name("銀行帳戶證明")
                    .description("銀行存摺封面或帳戶證明文件")
                    .required(true)
                    .allowedFormats(List.of("JPG", "PNG", "PDF"))
                    .maxSizeMB(5)
                    .example("存摺封面顯示帳號、戶名")
                    .build()
        );
    }
    
    private SupplementCreationResponse.ContactInfo getContactInfo() {
        return SupplementCreationResponse.ContactInfo.builder()
                .servicePhone("0800-123-456")
                .serviceEmail("<EMAIL>")
                .businessHours("週一至週五 09:00-18:00")
                .onlineServiceUrl("https://www.kgi.com/customer-service")
                .build();
    }
    
    private List<SupplementStatusResponse.StatusHistory> getStatusHistory() {
        return List.of(
            SupplementStatusResponse.StatusHistory.builder()
                    .status("CREATED")
                    .description("補件請求已建立")
                    .timestamp(LocalDateTime.now().minusDays(2))
                    .operator("SYSTEM")
                    .notes("系統自動建立")
                    .build(),
            SupplementStatusResponse.StatusHistory.builder()
                    .status("IN_PROGRESS")
                    .description("開始上傳文件")
                    .timestamp(LocalDateTime.now().minusHours(4))
                    .operator("USER")
                    .notes("使用者開始上傳文件")
                    .build()
        );
    }
    
    private List<SupplementStatusResponse.PendingAction> getPendingActions() {
        return List.of(
            SupplementStatusResponse.PendingAction.builder()
                    .actionType("UPLOAD_DOCUMENT")
                    .description("上傳銀行帳戶證明")
                    .priority("HIGH")
                    .dueDate(LocalDateTime.now().plusDays(5))
                    .required(true)
                    .build()
        );
    }
    
    private List<SupplementStatusResponse.ReviewFeedback> getReviewFeedbacks() {
        return List.of();
    }
    
    private SupplementSubmissionResponse.SubmissionSummary buildSubmissionSummary(SupplementSubmitRequest request) {
        return SupplementSubmissionResponse.SubmissionSummary.builder()
                .requiredDocumentCompleteness(100)
                .optionalDocumentCompleteness(50)
                .overallCompleteness(85)
                .submittedDocuments(List.of())
                .missingDocuments(List.of())
                .qualityCheck(SupplementSubmissionResponse.QualityCheckResult.builder()
                        .overallScore(85)
                        .qualityGrade("良好")
                        .passedDocuments(2)
                        .improvementNeededDocuments(0)
                        .failedDocuments(0)
                        .improvements(List.of())
                        .build())
                .build();
    }
    
    private SupplementSubmissionResponse.ContactInformation buildContactInformation() {
        return SupplementSubmissionResponse.ContactInformation.builder()
                .customerServiceHotline("0800-123-456")
                .email("<EMAIL>")
                .onlineService("https://www.kgi.com/chat")
                .businessHours("週一至週五 09:00-18:00")
                .emergencyContact("0800-999-999")
                .build();
    }
    
    private SupplementSubmissionResponse.TrackingInformation buildTrackingInformation(String supplementId) {
        return SupplementSubmissionResponse.TrackingInformation.builder()
                .trackingUrl("https://www.kgi.com/supplement/track/" + supplementId)
                .queryPassword("TRACK" + System.currentTimeMillis() % 10000)
                .smsNotificationEnabled(true)
                .emailNotificationEnabled(true)
                .pushNotificationEnabled(false)
                .notificationFrequency("DAILY")
                .build();
    }
    
    private String maskIdentifier(String identifier) {
        if (identifier == null || identifier.length() < 4) {
            return identifier;
        }
        return identifier.substring(0, 2) + "***" + identifier.substring(identifier.length() - 2);
    }
}