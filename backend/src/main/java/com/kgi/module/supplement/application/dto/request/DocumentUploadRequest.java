package com.kgi.module.supplement.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 文件上傳請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadRequest {
    
    /**
     * 補件編號
     */
    @NotBlank(message = "補件編號不能為空")
    private String supplementId;
    
    /**
     * 文件類型
     */
    @NotBlank(message = "文件類型不能為空")
    private String documentType;
    
    /**
     * 文件名稱
     */
    @NotBlank(message = "文件名稱不能為空")
    @Size(max = 255, message = "文件名稱長度不能超過255字元")
    private String fileName;
    
    /**
     * 文件大小 (bytes)
     */
    private Long fileSize;
    
    /**
     * 文件描述
     */
    @Size(max = 500, message = "文件描述長度不能超過500字元")
    private String description;
    
    /**
     * 檔案內容類型
     */
    private String contentType;
    
    /**
     * 文件標籤
     */
    private String tags;
    
    /**
     * 是否為必要文件
     */
    @Builder.Default
    private Boolean required = false;
}