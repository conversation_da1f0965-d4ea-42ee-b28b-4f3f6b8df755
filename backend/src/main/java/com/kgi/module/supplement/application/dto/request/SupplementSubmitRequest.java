package com.kgi.module.supplement.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 補件提交請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementSubmitRequest {
    
    /**
     * 案件編號
     */
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    /**
     * 客戶類型
     */
    @NotBlank(message = "客戶類型不能為空")
    private String customerType;
    
    /**
     * 補件資料
     */
    private Map<String, Object> supplementData;
    
    /**
     * 補件編號
     */
    @NotBlank(message = "補件編號不能為空")
    private String supplementId;
    
    /**
     * 提交說明
     */
    @Size(max = 1000, message = "提交說明長度不能超過1000字元")
    private String notes;
    
    /**
     * 確認條款
     */
    @Builder.Default
    private Boolean termsAccepted = false;
    
    /**
     * 確認資料正確性
     */
    @Builder.Default
    private Boolean dataConfirmed = false;
    
    /**
     * 上傳的文件ID列表
     */
    private List<String> documentIds;
    
    /**
     * 提交檢查清單
     */
    private SubmissionChecklist checklist;
    
    /**
     * 提交檢查清單內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubmissionChecklist {
        
        /**
         * 必要文件是否齊全
         */
        @Builder.Default
        private Boolean requiredDocumentsComplete = false;
        
        /**
         * 文件是否清晰可讀
         */
        @Builder.Default
        private Boolean documentsReadable = false;
        
        /**
         * 個人資料是否正確
         */
        @Builder.Default
        private Boolean personalDataCorrect = false;
        
        /**
         * 聯絡資訊是否有效
         */
        @Builder.Default
        private Boolean contactInfoValid = false;
        
        /**
         * 是否已閱讀注意事項
         */
        @Builder.Default
        private Boolean noticeRead = false;
    }
}