package com.kgi.module.supplement.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 補件提交響應
 */
@Data
@Builder
public class SupplementSubmitResponse {
    private String caseNo;
    private String customerType;
    private String supplementId;
    private String submissionStatus;
    private Map<String, Object> fromApiResult;
    private String submissionTime;
    private String message;
    private String nextStep;
    private String nextStepUrl;
}