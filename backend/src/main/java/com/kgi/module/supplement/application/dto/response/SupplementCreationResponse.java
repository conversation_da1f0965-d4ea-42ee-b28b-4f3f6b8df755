package com.kgi.module.supplement.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 補件建立回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementCreationResponse {
    
    /**
     * 補件編號
     */
    private String supplementId;
    
    /**
     * 原始匯款編號
     */
    private String originalRemittanceId;
    
    /**
     * 申請人識別碼 (遮罩)
     */
    private String applicantIdentifier;
    
    /**
     * 申請人姓名
     */
    private String applicantName;
    
    /**
     * 補件類型
     */
    private String supplementType;
    
    /**
     * 補件狀態
     */
    private String status;
    
    /**
     * 建立時間
     */
    private LocalDateTime creationTime;
    
    /**
     * 預計完成期限
     */
    private LocalDateTime deadline;
    
    /**
     * 所需文件列表
     */
    private List<RequiredDocument> requiredDocuments;
    
    /**
     * 下一步驟說明
     */
    private List<String> nextSteps;
    
    /**
     * 重要提醒
     */
    private List<String> importantNotices;
    
    /**
     * 聯絡資訊
     */
    private ContactInfo contactInfo;
    
    /**
     * 所需文件內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequiredDocument {
        
        /**
         * 文件類型
         */
        private String type;
        
        /**
         * 文件名稱
         */
        private String name;
        
        /**
         * 文件描述
         */
        private String description;
        
        /**
         * 是否必要
         */
        private Boolean required;
        
        /**
         * 檔案格式限制
         */
        private List<String> allowedFormats;
        
        /**
         * 檔案大小限制 (MB)
         */
        private Integer maxSizeMB;
        
        /**
         * 範例說明
         */
        private String example;
    }
    
    /**
     * 聯絡資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        
        /**
         * 客服電話
         */
        private String servicePhone;
        
        /**
         * 客服信箱
         */
        private String serviceEmail;
        
        /**
         * 營業時間
         */
        private String businessHours;
        
        /**
         * 線上客服網址
         */
        private String onlineServiceUrl;
    }
}