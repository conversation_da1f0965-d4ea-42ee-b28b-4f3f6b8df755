package com.kgi.module.supplement.domain.model;

import com.kgi.core.domain.model.BaseEntity;
import com.kgi.core.domain.model.TaiwanId;
import com.kgi.core.domain.model.UnifiedNumber;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 補件請求領域實體
 * 管理解款申請的補件需求和處理流程
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SupplementRequest extends BaseEntity {
    
    /**
     * 補件編號
     */
    private String supplementId;
    
    /**
     * 原解款編號
     */
    private String originalRemittanceId;
    
    /**
     * 申請人類型
     */
    private ApplicantType applicantType;
    
    /**
     * 申請人識別號（身分證號或統一編號）
     */
    private String applicantIdentifier;
    
    /**
     * 申請人姓名/企業名稱
     */
    private String applicantName;
    
    /**
     * 聯絡電話
     */
    private String contactPhone;
    
    /**
     * 聯絡Email
     */
    private String contactEmail;
    
    /**
     * 補件類型
     */
    private SupplementType supplementType;
    
    /**
     * 補件狀態
     */
    private SupplementStatus status;
    
    /**
     * 補件原因
     */
    private String reason;
    
    /**
     * 補件說明
     */
    private String description;
    
    
    /**
     * 補件截止時間
     */
    private LocalDateTime deadline;
    
    /**
     * 建立時間
     */
    private LocalDateTime requestTime;
    
    /**
     * 最後更新時間
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 完成時間
     */
    private LocalDateTime completionTime;
    
    /**
     * 處理人員工編號
     */
    private String processorEmployeeId;
    
    /**
     * 處理人姓名
     */
    private String processorName;
    
    /**
     * 審核備註
     */
    private String processingNotes;
    
    /**
     * 是否緊急件
     */
    private Boolean isUrgent;
    
    /**
     * 自動提醒次數
     */
    private Integer reminderCount;
    
    /**
     * 最後提醒時間
     */
    private LocalDateTime lastReminderTime;
    
    /**
     * 文件需求清單
     */
    @Builder.Default
    private List<DocumentRequirement> requiredDocuments = new ArrayList<>();
    
    /**
     * 已上傳文件清單
     */
    @Builder.Default
    private List<UploadedDocument> uploadedDocuments = new ArrayList<>();
    
    /**
     * 申請人類型枚舉
     */
    public enum ApplicantType {
        INDIVIDUAL("個人"),
        CORPORATE("法人");
        
        private final String description;
        
        ApplicantType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 補件類型枚舉
     */
    public enum SupplementType {
        IDENTITY_VERIFICATION("身分證明文件"),
        BANK_ACCOUNT_PROOF("銀行帳戶證明"),
        INCOME_PROOF("收入證明"),
        PURPOSE_PROOF("匯款目的證明"),
        BUSINESS_LICENSE("營業執照"),
        AUTHORIZED_LETTER("授權書"),
        OTHER_DOCUMENTS("其他文件");
        
        private final String description;
        
        SupplementType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 補件狀態枚舉
     */
    public enum SupplementStatus {
        PENDING("待補件"),
        IN_PROGRESS("補件中"),
        SUBMITTED("已提交"),
        UNDER_REVIEW("審核中"),
        APPROVED("已核准"),
        REJECTED("已退件"),
        COMPLETED("已完成"),
        EXPIRED("已逾期"),
        CANCELLED("已取消");
        
        private final String description;
        
        SupplementStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 建立新的補件請求
     */
    public static SupplementRequest createNew(
            String supplementId,
            String originalRemittanceId,
            ApplicantType applicantType,
            String applicantIdentifier,
            String applicantName,
            SupplementType supplementType,
            String reason,
            List<DocumentRequirement> requiredDocuments) {
        
        LocalDateTime now = LocalDateTime.now();
        
        return SupplementRequest.builder()
                .supplementId(supplementId)
                .originalRemittanceId(originalRemittanceId)
                .applicantType(applicantType)
                .applicantIdentifier(applicantIdentifier)
                .applicantName(applicantName)
                .supplementType(supplementType)
                .status(SupplementStatus.PENDING)
                .reason(reason)
                .requiredDocuments(requiredDocuments)
                .requestTime(now)
                .lastUpdateTime(now)
                .deadline(now.plusDays(7)) // 預設7天內補件
                .isUrgent(false)
                .reminderCount(0)
                .build();
    }
    
    /**
     * 開始補件流程
     */
    public void startSupplement(String contactPhone, String contactEmail) {
        this.status = SupplementStatus.IN_PROGRESS;
        this.contactPhone = contactPhone;
        this.contactEmail = contactEmail;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 上傳文件
     */
    public void uploadDocument(UploadedDocument document) {
        if (this.uploadedDocuments == null) {
            this.uploadedDocuments = new java.util.ArrayList<>();
        }
        this.uploadedDocuments.add(document);
        this.lastUpdateTime = LocalDateTime.now();
        
        // 檢查是否所有必要文件都已上傳
        if (isAllRequiredDocumentsUploaded()) {
            this.status = SupplementStatus.UNDER_REVIEW;
        }
    }
    
    /**
     * 檢查是否所有必要文件都已上傳
     */
    public boolean isAllRequiredDocumentsUploaded() {
        if (requiredDocuments == null || requiredDocuments.isEmpty()) {
            return true;
        }
        
        if (uploadedDocuments == null || uploadedDocuments.isEmpty()) {
            return false;
        }
        
        return requiredDocuments.stream()
                .allMatch(required -> uploadedDocuments.stream()
                        .anyMatch(uploaded -> uploaded.getDocumentType().equals(required.getDocumentType())));
    }
    
    /**
     * 核准補件
     */
    public void approve(String processorEmployeeId, String processorName, String notes) {
        this.status = SupplementStatus.APPROVED;
        this.processorEmployeeId = processorEmployeeId;
        this.processorName = processorName;
        this.processingNotes = notes;
        this.completionTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 退件
     */
    public void reject(String processorEmployeeId, String processorName, String reason) {
        this.status = SupplementStatus.REJECTED;
        this.processorEmployeeId = processorEmployeeId;
        this.processorName = processorName;
        this.processingNotes = reason;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 完成補件
     */
    public void complete() {
        this.status = SupplementStatus.COMPLETED;
        this.completionTime = LocalDateTime.now();
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 取消補件
     */
    public void cancel(String reason) {
        this.status = SupplementStatus.CANCELLED;
        this.processingNotes = reason;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 檢查是否逾期
     */
    public boolean isExpired() {
        if (deadline == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(deadline) && status == SupplementStatus.PENDING) {
            this.status = SupplementStatus.EXPIRED;
            this.lastUpdateTime = now;
            return true;
        }
        
        return status == SupplementStatus.EXPIRED;
    }
    
    /**
     * 發送提醒
     */
    public void sendReminder() {
        this.reminderCount = (this.reminderCount == null ? 0 : this.reminderCount) + 1;
        this.lastReminderTime = LocalDateTime.now();
    }
    
    /**
     * 延長截止時間
     */
    public void extendDeadline(int days, String reason) {
        this.deadline = this.deadline.plusDays(days);
        this.processingNotes = (this.processingNotes == null ? "" : this.processingNotes + "\n") + 
                             "延長截止時間: " + reason;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 檢查是否可以處理
     */
    public boolean canProcess() {
        return status == SupplementStatus.UNDER_REVIEW || 
               status == SupplementStatus.IN_PROGRESS;
    }
    
    /**
     * 檢查是否需要提醒
     */
    public boolean needsReminder() {
        if (status != SupplementStatus.PENDING && status != SupplementStatus.IN_PROGRESS) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        
        // 如果從未提醒過，且距離截止時間不到3天
        if (lastReminderTime == null && deadline != null && 
            now.plusDays(3).isAfter(deadline)) {
            return true;
        }
        
        // 如果距離上次提醒超過24小時，且距離截止時間不到1天
        if (lastReminderTime != null && deadline != null &&
            now.isAfter(lastReminderTime.plusHours(24)) &&
            now.plusDays(1).isAfter(deadline)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 取得進度百分比
     */
    public Integer getProgressPercentage() {
        switch (status) {
            case PENDING:
                return 10;
            case IN_PROGRESS:
                if (requiredDocuments == null || requiredDocuments.isEmpty()) {
                    return 50;
                }
                int uploaded = uploadedDocuments == null ? 0 : uploadedDocuments.size();
                int required = requiredDocuments.size();
                return 30 + (uploaded * 40 / required);
            case UNDER_REVIEW:
                return 80;
            case APPROVED:
            case COMPLETED:
                return 100;
            case REJECTED:
            case CANCELLED:
            case EXPIRED:
                return 0;
            default:
                return 0;
        }
    }
    
    // ==================== 向後相容性方法 ====================
    
    /**
     * 獲取建立時間（為了向後相容性）
     */
    public LocalDateTime getCreatedTime() {
        return requestTime;
    }
    
    /**
     * 設定建立時間（為了向後相容性）
     */
    public void setCreatedTime(LocalDateTime createdTime) {
        this.requestTime = createdTime;
    }
    
    /**
     * 獲取更新時間（為了向後相容性）
     */
    public LocalDateTime getUpdatedTime() {
        return lastUpdateTime;
    }
    
    /**
     * 設定更新時間（為了向後相容性）
     */
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.lastUpdateTime = updatedTime;
    }
    
    /**
     * 獲取完成時間（為了向後相容性）
     */
    public LocalDateTime getCompletedTime() {
        return completionTime;
    }
    
    /**
     * 設定完成時間（為了向後相容性）
     */
    public void setCompletedTime(LocalDateTime completedTime) {
        this.completionTime = completedTime;
    }
    
    /**
     * 獲取提交時間（為了向後相容性）
     */
    public LocalDateTime getSubmittedTime() {
        // 使用狀態變更時的時間作為提交時間
        return lastUpdateTime;
    }
    
    /**
     * 設定提交時間（為了向後相容性）
     */
    public void setSubmittedTime(LocalDateTime submittedTime) {
        this.lastUpdateTime = submittedTime;
    }
    
    /**
     * 獲取處理時間（為了向後相容性）
     */
    public LocalDateTime getProcessedTime() {
        // 使用狀態變更時的時間作為處理時間
        return lastUpdateTime;
    }
    
    /**
     * 設定處理時間（為了向後相容性）
     */
    public void setProcessedTime(LocalDateTime processedTime) {
        this.lastUpdateTime = processedTime;
    }
    
    /**
     * 獲取文件需求清單（為了向後相容性）
     */
    public List<DocumentRequirement> getDocumentRequirements() {
        return requiredDocuments;
    }
    
    /**
     * 設定文件需求清單（為了向後相容性）
     */
    public void setDocumentRequirements(List<DocumentRequirement> documentRequirements) {
        this.requiredDocuments = documentRequirements;
    }
}