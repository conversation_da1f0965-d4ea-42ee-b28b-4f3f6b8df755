package com.kgi.module.supplement.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 補件請求實體
 * 對應資料庫中的補件請求表
 */
@Entity
@Table(name = "supplement_requests")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplementRequestEntity {
    
    @Id
    @Column(name = "supplement_id", length = 50)
    private String supplementId;
    
    @Column(name = "original_remittance_id", length = 50, nullable = false)
    private String originalRemittanceId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "applicant_type", nullable = false)
    private ApplicantType applicantType;
    
    @Column(name = "applicant_identifier", length = 50, nullable = false)
    private String applicantIdentifier;
    
    @Column(name = "applicant_name", length = 100, nullable = false)
    private String applicantName;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "supplement_type", nullable = false)
    private SupplementType supplementType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private SupplementStatus status;
    
    @Column(name = "reason", length = 500, nullable = false)
    private String reason;
    
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "deadline", nullable = false)
    private LocalDateTime deadline;
    
    @Column(name = "is_urgent", nullable = false)
    private Boolean isUrgent;
    
    @Column(name = "processor_employee_id", length = 50)
    private String processorEmployeeId;
    
    @Column(name = "processing_notes", length = 1000)
    private String processingNotes;
    
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @Column(name = "submitted_time")
    private LocalDateTime submittedTime;
    
    @Column(name = "processed_time")
    private LocalDateTime processedTime;
    
    @Column(name = "completed_time")
    private LocalDateTime completedTime;
    
    @OneToMany(mappedBy = "supplementRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<DocumentRequirementEntity> documentRequirements = new ArrayList<>();
    
    @OneToMany(mappedBy = "supplementRequest", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<UploadedDocumentEntity> uploadedDocuments = new ArrayList<>();
    
    /**
     * 申請人類型枚舉
     */
    public enum ApplicantType {
        /**
         * 個人
         */
        INDIVIDUAL,
        
        /**
         * 企業
         */
        CORPORATE
    }
    
    /**
     * 補件類型枚舉
     */
    public enum SupplementType {
        /**
         * 身分驗證補件
         */
        IDENTITY_VERIFICATION,
        
        /**
         * 資金來源證明
         */
        FUND_SOURCE_PROOF,
        
        /**
         * 收入證明
         */
        INCOME_PROOF,
        
        /**
         * 銀行對帳單
         */
        BANK_STATEMENT,
        
        /**
         * 營業執照（企業用）
         */
        BUSINESS_LICENSE,
        
        /**
         * 董事會決議（企業用）
         */
        BOARD_RESOLUTION,
        
        /**
         * 其他文件
         */
        OTHER_DOCUMENTS
    }
    
    /**
     * 補件狀態枚舉
     */
    public enum SupplementStatus {
        /**
         * 待開始
         */
        PENDING,
        
        /**
         * 進行中
         */
        IN_PROGRESS,
        
        /**
         * 已提交
         */
        SUBMITTED,
        
        /**
         * 審核中
         */
        UNDER_REVIEW,
        
        /**
         * 已核准
         */
        APPROVED,
        
        /**
         * 已拒絕
         */
        REJECTED,
        
        /**
         * 已完成
         */
        COMPLETED,
        
        /**
         * 已取消
         */
        CANCELLED,
        
        /**
         * 已逾期
         */
        EXPIRED
    }
    
    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (updatedTime == null) {
            updatedTime = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}