package com.kgi.module.supplement.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件上傳回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadResponse {
    
    /**
     * 文件編號
     */
    private String documentId;
    
    /**
     * 案件編號
     */
    private String caseNo;
    
    /**
     * 補件編號
     */
    private String supplementId;
    
    /**
     * 文件類型
     */
    private String documentType;
    
    /**
     * 檔案名稱
     */
    private String fileName;
    
    /**
     * 檔案大小
     */
    private Long fileSize;
    
    /**
     * 格式化檔案大小
     */
    private String fileSizeFormatted;
    
    /**
     * 內容類型
     */
    private String contentType;
    
    /**
     * MIME類型
     */
    private String mimeType;
    
    /**
     * 文件描述
     */
    private String description;
    
    /**
     * 上傳時間
     */
    private LocalDateTime uploadTime;
    
    /**
     * 上傳時間字串
     */
    private String uploadTimeStr;
    
    /**
     * 訊息
     */
    private String message;
    
    /**
     * 檔案狀態
     */
    private String status;
    
    /**
     * 檔案雜湊值
     */
    private String fileHash;
    
    /**
     * 存儲路徑
     */
    private String storagePath;
    
    /**
     * 驗證結果
     */
    private ValidationResult validationResult;
    
    /**
     * 縮圖URL (圖片類型檔案)
     */
    private String thumbnailUrl;
    
    /**
     * 預覽URL
     */
    private String previewUrl;
    
    /**
     * 下載URL
     */
    private String downloadUrl;
    
    /**
     * 到期時間
     */
    private LocalDateTime expirationTime;
    
    /**
     * 驗證結果內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationResult {
        
        /**
         * 驗證是否通過
         */
        private Boolean passed;
        
        /**
         * 檔案格式檢查
         */
        private Boolean formatValid;
        
        /**
         * 檔案大小檢查
         */
        private Boolean sizeValid;
        
        /**
         * 病毒掃描結果
         */
        private Boolean virusFree;
        
        /**
         * 內容檢查結果
         */
        private Boolean contentValid;
        
        /**
         * 驗證訊息
         */
        private List<String> messages;
        
        /**
         * 警告訊息
         */
        private List<String> warnings;
        
        /**
         * 錯誤訊息
         */
        private List<String> errors;
        
        /**
         * 驗證時間
         */
        private LocalDateTime validationTime;
        
        /**
         * 品質評分 (0-100)
         */
        private Integer qualityScore;
        
        /**
         * 改善建議
         */
        private List<String> suggestions;
    }
}