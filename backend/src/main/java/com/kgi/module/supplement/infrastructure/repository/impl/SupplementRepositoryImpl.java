package com.kgi.module.supplement.infrastructure.repository.impl;

import com.kgi.module.supplement.domain.model.*;
import com.kgi.module.supplement.domain.repository.SupplementRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 補件倉儲實作
 * 使用內存存儲實現（開發環境）
 */
@Slf4j
@Repository
public class SupplementRepositoryImpl implements SupplementRepository {
    
    // 使用內存存儲
    private final Map<String, SupplementRequest> supplementStore = new ConcurrentHashMap<>();
    private final Map<String, List<String>> caseSupplementIndex = new ConcurrentHashMap<>();
    
    @Override
    public SupplementRequest save(SupplementRequest supplementRequest) {
        log.debug("保存補件請求: supplementId={}", supplementRequest.getSupplementId());
        
        // 儲存補件請求
        supplementStore.put(supplementRequest.getSupplementId(), supplementRequest);
        
        // 更新案件索引
        caseSupplementIndex.computeIfAbsent(supplementRequest.getOriginalRemittanceId(), 
                k -> new ArrayList<>()).add(supplementRequest.getSupplementId());
        
        return supplementRequest;
    }
    
    @Override
    public Optional<SupplementRequest> findBySupplementId(String supplementId) {
        return Optional.ofNullable(supplementStore.get(supplementId));
    }
    
    @Override
    public List<SupplementRequest> findByApplicantIdentifier(String applicantIdentifier) {
        return supplementStore.values().stream()
                .filter(s -> applicantIdentifier.equals(s.getApplicantIdentifier()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByOriginalRemittanceId(String originalRemittanceId) {
        List<String> supplementIds = caseSupplementIndex.get(originalRemittanceId);
        if (supplementIds == null) {
            return Collections.emptyList();
        }
        
        return supplementIds.stream()
                .map(supplementStore::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByStatus(SupplementRequest.SupplementStatus status) {
        return supplementStore.values().stream()
                .filter(s -> s.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByStatusIn(List<SupplementRequest.SupplementStatus> statuses) {
        return supplementStore.values().stream()
                .filter(s -> statuses.contains(s.getStatus()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findExpiredRequests() {
        LocalDateTime now = LocalDateTime.now();
        return supplementStore.values().stream()
                .filter(s -> s.getDeadline() != null && s.getDeadline().isBefore(now))
                .filter(s -> s.getStatus() != SupplementRequest.SupplementStatus.COMPLETED)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findRequestsNeedingReminder() {
        LocalDateTime reminderTime = LocalDateTime.now().plusDays(1); // 1天內到期的提醒
        return supplementStore.values().stream()
                .filter(s -> s.getDeadline() != null && s.getDeadline().isBefore(reminderTime))
                .filter(s -> s.getStatus() == SupplementRequest.SupplementStatus.PENDING)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByApplicantType(SupplementRequest.ApplicantType applicantType) {
        return supplementStore.values().stream()
                .filter(s -> s.getApplicantType() == applicantType)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findBySupplementType(SupplementRequest.SupplementType supplementType) {
        return supplementStore.values().stream()
                .filter(s -> s.getSupplementType() == supplementType)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByProcessorEmployeeId(String processorEmployeeId) {
        return supplementStore.values().stream()
                .filter(s -> processorEmployeeId.equals(s.getProcessorEmployeeId()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findUrgentRequests() {
        return supplementStore.values().stream()
                .filter(SupplementRequest::getIsUrgent)
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteBySupplementId(String supplementId) {
        log.debug("刪除補件請求: supplementId={}", supplementId);
        
        SupplementRequest supplement = supplementStore.remove(supplementId);
        if (supplement != null) {
            // 清理案件索引
            List<String> caseSupplements = caseSupplementIndex.get(supplement.getOriginalRemittanceId());
            if (caseSupplements != null) {
                caseSupplements.remove(supplementId);
            }
        }
    }
    
    @Override
    public boolean existsBySupplementId(String supplementId) {
        return supplementStore.containsKey(supplementId);
    }
    
    @Override
    public long countByStatus(SupplementRequest.SupplementStatus status) {
        return supplementStore.values().stream()
                .mapToLong(s -> s.getStatus() == status ? 1 : 0)
                .sum();
    }
    
    @Override
    public long countByApplicantIdentifier(String applicantIdentifier) {
        return supplementStore.values().stream()
                .mapToLong(s -> applicantIdentifier.equals(s.getApplicantIdentifier()) ? 1 : 0)
                .sum();
    }
    
    @Override
    public List<SupplementRequest> findAll(int page, int size) {
        return supplementStore.values().stream()
                .sorted((s1, s2) -> s2.getCreatedTime().compareTo(s1.getCreatedTime()))
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<SupplementRequest> findByCriteria(
            SupplementRequest.ApplicantType applicantType,
            SupplementRequest.SupplementType supplementType,
            SupplementRequest.SupplementStatus status,
            String applicantIdentifier,
            String originalRemittanceId,
            Boolean isUrgent,
            int page,
            int size) {
        
        var stream = supplementStore.values().stream();
        
        if (applicantType != null) {
            stream = stream.filter(s -> s.getApplicantType() == applicantType);
        }
        if (supplementType != null) {
            stream = stream.filter(s -> s.getSupplementType() == supplementType);
        }
        if (status != null) {
            stream = stream.filter(s -> s.getStatus() == status);
        }
        if (applicantIdentifier != null) {
            stream = stream.filter(s -> applicantIdentifier.equals(s.getApplicantIdentifier()));
        }
        if (originalRemittanceId != null) {
            stream = stream.filter(s -> originalRemittanceId.equals(s.getOriginalRemittanceId()));
        }
        if (isUrgent != null) {
            stream = stream.filter(s -> s.getIsUrgent() == isUrgent);
        }
        
        return stream
                .sorted((s1, s2) -> s2.getCreatedTime().compareTo(s1.getCreatedTime()))
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }
}