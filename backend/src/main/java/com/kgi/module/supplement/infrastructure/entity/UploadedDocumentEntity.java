package com.kgi.module.supplement.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 上傳文件實體
 * 對應資料庫中的上傳文件表
 */
@Entity
@Table(name = "uploaded_documents")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadedDocumentEntity {
    
    @Id
    @Column(name = "document_id", length = 50)
    private String documentId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplement_id", nullable = false)
    private SupplementRequestEntity supplementRequest;
    
    @Column(name = "document_type", length = 100, nullable = false)
    private String documentType;
    
    @Column(name = "file_name", length = 255, nullable = false)
    private String fileName;
    
    @Column(name = "file_size", nullable = false)
    private Long fileSize;
    
    @Column(name = "file_path", length = 500)
    private String filePath;
    
    @Lob
    @Column(name = "file_content")
    private String fileContent;
    
    @Column(name = "mime_type", length = 100)
    private String mimeType;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private DocumentStatus status;
    
    @Column(name = "description", length = 500)
    private String description;
    
    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;
    
    @Column(name = "review_time")
    private LocalDateTime reviewTime;
    
    @Column(name = "reviewer_employee_id", length = 50)
    private String reviewerEmployeeId;
    
    @Column(name = "review_notes", length = 1000)
    private String reviewNotes;
    
    @Column(name = "quality_score")
    private Integer qualityScore;
    
    @Column(name = "uploaded_by", length = 50)
    private String uploadedBy;
    
    @Column(name = "uploader_ip", length = 45)
    private String uploaderIp;
    
    @Column(name = "created_time", nullable = false)
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    /**
     * 文件狀態枚舉
     */
    public enum DocumentStatus {
        /**
         * 已上傳
         */
        UPLOADED,
        
        /**
         * 待審核
         */
        PENDING_REVIEW,
        
        /**
         * 審核中
         */
        UNDER_REVIEW,
        
        /**
         * 已通過
         */
        APPROVED,
        
        /**
         * 已拒絕
         */
        REJECTED,
        
        /**
         * 已刪除
         */
        DELETED
    }
    
    @PrePersist
    protected void onCreate() {
        if (createdTime == null) {
            createdTime = LocalDateTime.now();
        }
        if (updatedTime == null) {
            updatedTime = LocalDateTime.now();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}