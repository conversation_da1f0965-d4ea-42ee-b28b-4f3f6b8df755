package com.kgi.module.supplement.infrastructure.repository.jpa;

import com.kgi.module.supplement.infrastructure.entity.SupplementRequestEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 補件請求 JPA Repository
 * 提供基礎的資料庫存取操作
 */
@Repository
public interface SupplementJpaRepository extends JpaRepository<SupplementRequestEntity, String> {
    
    /**
     * 根據補件ID查詢
     */
    Optional<SupplementRequestEntity> findBySupplementId(String supplementId);
    
    /**
     * 根據申請人識別號查詢補件列表
     */
    List<SupplementRequestEntity> findByApplicantIdentifier(String applicantIdentifier);
    
    /**
     * 根據原始解款編號查詢補件列表
     */
    List<SupplementRequestEntity> findByOriginalRemittanceId(String originalRemittanceId);
    
    /**
     * 根據狀態查詢補件列表
     */
    List<SupplementRequestEntity> findByStatus(SupplementRequestEntity.SupplementStatus status);
    
    /**
     * 查詢指定狀態的補件列表（多個狀態）
     */
    List<SupplementRequestEntity> findByStatusIn(List<SupplementRequestEntity.SupplementStatus> statuses);
    
    /**
     * 查詢已逾期的補件請求
     */
    @Query("SELECT s FROM SupplementRequestEntity s WHERE s.deadline < :now AND s.status IN :activeStatuses")
    List<SupplementRequestEntity> findExpiredRequests(
            @Param("now") LocalDateTime now,
            @Param("activeStatuses") List<SupplementRequestEntity.SupplementStatus> activeStatuses);
    
    /**
     * 查詢需要提醒的補件請求（即將到期）
     */
    @Query("SELECT s FROM SupplementRequestEntity s WHERE s.deadline BETWEEN :now AND :reminderTime AND s.status IN :activeStatuses")
    List<SupplementRequestEntity> findRequestsNeedingReminder(
            @Param("now") LocalDateTime now,
            @Param("reminderTime") LocalDateTime reminderTime,
            @Param("activeStatuses") List<SupplementRequestEntity.SupplementStatus> activeStatuses);
    
    /**
     * 根據申請人類型查詢補件列表
     */
    List<SupplementRequestEntity> findByApplicantType(SupplementRequestEntity.ApplicantType applicantType);
    
    /**
     * 根據補件類型查詢補件列表
     */
    List<SupplementRequestEntity> findBySupplementType(SupplementRequestEntity.SupplementType supplementType);
    
    /**
     * 查詢處理人的補件列表
     */
    List<SupplementRequestEntity> findByProcessorEmployeeId(String processorEmployeeId);
    
    /**
     * 查詢緊急補件列表
     */
    @Query("SELECT s FROM SupplementRequestEntity s WHERE s.isUrgent = true AND s.status IN :activeStatuses ORDER BY s.createdTime ASC")
    List<SupplementRequestEntity> findUrgentRequests(@Param("activeStatuses") List<SupplementRequestEntity.SupplementStatus> activeStatuses);
    
    /**
     * 根據補件ID刪除補件請求
     */
    void deleteBySupplementId(String supplementId);
    
    /**
     * 檢查補件是否存在
     */
    boolean existsBySupplementId(String supplementId);
    
    /**
     * 統計各狀態的補件數量
     */
    long countByStatus(SupplementRequestEntity.SupplementStatus status);
    
    /**
     * 統計申請人的補件數量
     */
    long countByApplicantIdentifier(String applicantIdentifier);
    
    /**
     * 根據多個條件查詢補件列表（分頁）
     */
    @Query("SELECT s FROM SupplementRequestEntity s WHERE " +
           "(:applicantType IS NULL OR s.applicantType = :applicantType) AND " +
           "(:supplementType IS NULL OR s.supplementType = :supplementType) AND " +
           "(:status IS NULL OR s.status = :status) AND " +
           "(:applicantIdentifier IS NULL OR s.applicantIdentifier = :applicantIdentifier) AND " +
           "(:originalRemittanceId IS NULL OR s.originalRemittanceId = :originalRemittanceId) AND " +
           "(:isUrgent IS NULL OR s.isUrgent = :isUrgent)")
    Page<SupplementRequestEntity> findByCriteria(
            @Param("applicantType") SupplementRequestEntity.ApplicantType applicantType,
            @Param("supplementType") SupplementRequestEntity.SupplementType supplementType,
            @Param("status") SupplementRequestEntity.SupplementStatus status,
            @Param("applicantIdentifier") String applicantIdentifier,
            @Param("originalRemittanceId") String originalRemittanceId,
            @Param("isUrgent") Boolean isUrgent,
            Pageable pageable);
    
    /**
     * 查詢指定時間範圍內的補件請求
     */
    @Query("SELECT s FROM SupplementRequestEntity s WHERE s.createdTime BETWEEN :startTime AND :endTime")
    List<SupplementRequestEntity> findByCreatedTimeBetween(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 統計今日新增的補件數量
     */
    @Query("SELECT COUNT(s) FROM SupplementRequestEntity s WHERE CAST(s.createdTime AS DATE) = CURRENT_DATE")
    long countTodayRequests();
    
    /**
     * 統計本月已完成的補件數量
     */
    @Query("SELECT COUNT(s) FROM SupplementRequestEntity s WHERE " +
           "EXTRACT(YEAR FROM s.completedTime) = EXTRACT(YEAR FROM CURRENT_DATE) AND " +
           "EXTRACT(MONTH FROM s.completedTime) = EXTRACT(MONTH FROM CURRENT_DATE) AND " +
           "s.status = 'COMPLETED'")
    long countMonthlyCompletedRequests();
    
    /**
     * 查詢平均處理時間（小時）
     */
    @Query("SELECT AVG(DATEDIFF(HOUR, s.createdTime, s.completedTime)) FROM SupplementRequestEntity s WHERE s.status = com.kgi.module.supplement.infrastructure.entity.SupplementRequestEntity$SupplementStatus.COMPLETED")
    Double findAverageProcessingTimeInHours();
}