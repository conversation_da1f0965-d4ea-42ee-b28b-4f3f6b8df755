package com.kgi.module.supplement.application.usecase;

import com.kgi.module.supplement.application.dto.response.SupplementNotificationResponse;
import com.kgi.module.supplement.domain.service.SupplementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 取得補件通知用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetSupplementNotificationUseCase {
    
    private final SupplementService supplementService;
    
    public SupplementNotificationResponse execute(String caseNo, String customerType) {
        log.info("取得補件通知: caseNo={}, customerType={}", caseNo, customerType);
        
        // 取得補件需求
        Map<String, Object> requirements = supplementService.getSupplementRequirements(caseNo, customerType);
        
        // 建立回應
        return SupplementNotificationResponse.builder()
                .caseNo(caseNo)
                .customerType(customerType)
                .notificationType("SUPPLEMENT_REQUIRED")
                .reason((String) requirements.get("reason"))
                .requiredDocuments(supplementService.getRequiredDocuments(customerType))
                .deadline((String) requirements.get("deadline"))
                .attemptCount((Integer) requirements.get("attemptCount"))
                .maxAttempts((Integer) requirements.get("maxAttempts"))
                .instructions(getInstructions(customerType))
                .nextStep("DOCUMENT_UPLOAD")
                .nextStepUrl("/api/ibr/application/supplement/documents/upload")
                .notificationTime(LocalDateTime.now().toString())
                .build();
    }
    
    private String getInstructions(String customerType) {
        if ("INDIVIDUAL".equals(customerType)) {
            return """
                請依照以下步驟完成補件：
                1. 準備清晰的身分證正反面影像
                2. 確保證件資訊清楚可見
                3. 上傳檔案格式支援 JPG、PNG、PDF
                4. 單一檔案大小不超過 10MB
                5. 完成上傳後進行身份驗證
                """;
        } else {
            return """
                請依照以下步驟完成補件：
                1. 準備公司登記證明及相關文件
                2. 確保文件為最新版本且蓋有公司大小章
                3. 上傳檔案格式支援 JPG、PNG、PDF
                4. 單一檔案大小不超過 10MB
                5. 完成上傳後使用工商憑證進行驗證
                """;
        }
    }
}