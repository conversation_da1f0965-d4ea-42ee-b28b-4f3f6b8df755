package com.kgi.module.supplement.application.usecase;

import com.kgi.module.supplement.application.dto.response.SupplementCompleteResponse;
import com.kgi.module.supplement.application.dto.response.SupplementStatusResponse;
import com.kgi.core.domain.service.DocumentService;
import com.kgi.module.supplement.domain.service.SupplementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 查詢補件狀態用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetSupplementStatusUseCase {
    
    private final SupplementService supplementService;
    private final DocumentService documentService;
    
    public SupplementStatusResponse execute(String caseNo) {
        log.info("查詢補件狀態: caseNo={}", caseNo);
        
        // 取得補件需求和狀態
        Map<String, Object> requirements = supplementService.getSupplementRequirements(caseNo, "");
        List<Map<String, Object>> uploadedDocs = documentService.getDocumentList(caseNo);
        
        // 建立回應
        return SupplementStatusResponse.builder()
                .caseNo(caseNo)
                .status((String) requirements.get("status"))
                .reason((String) requirements.get("reason"))
                .deadline((String) requirements.get("deadline"))
                .attemptCount((Integer) requirements.get("attemptCount"))
                .maxAttempts((Integer) requirements.get("maxAttempts"))
                .uploadedDocuments(uploadedDocs)
                .lastUpdateTime(LocalDateTime.now().toString())
                .build();
    }
    
    public SupplementCompleteResponse getCompleteInfo(String caseNo) {
        log.info("取得補件完成資訊: caseNo={}", caseNo);
        
        // 建立完成資訊
        return SupplementCompleteResponse.builder()
                .caseNo(caseNo)
                .status("COMPLETED")
                .completionTime(LocalDateTime.now().toString())
                .supplementId("SUPP-" + caseNo)
                .message("補件已完成，您的申請正在處理中")
                .estimatedProcessingTime("1-2個工作天")
                .contactInfo(Map.of(
                    "customerService", "0800-123-456",
                    "email", "<EMAIL>",
                    "serviceHours", "週一至週五 09:00-17:30"
                ))
                .build();
    }
}