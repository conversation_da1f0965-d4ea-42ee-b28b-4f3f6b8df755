package com.kgi.module.notification.adapters.rest;

import com.kgi.core.application.vo.ResponseVO;
import com.kgi.core.domain.code.ReturnCode;
import com.kgi.module.notification.application.dto.request.SendNotificationRequest;
import com.kgi.module.notification.application.dto.response.NotificationStatusResponse;
import com.kgi.module.notification.application.dto.response.SendNotificationResponse;
import com.kgi.module.notification.application.facade.NotificationFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知 Controller
 * 提供通知發送和查詢功能
 */
@Slf4j
@RestController
@RequestMapping("/api/notification")
@RequiredArgsConstructor
@CrossOrigin
@Tag(name = "Notification", description = "通知服務 API")
public class NotificationController {
    
    private final NotificationFacade notificationFacade;
    
    /**
     * 發送通知
     */
    @PostMapping("/send")
    @Operation(summary = "發送通知", description = "發送 Email 或 SMS 通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功",
            content = @Content(schema = @Schema(implementation = ResponseVO.class))),
        @ApiResponse(responseCode = "400", description = "請求參數錯誤"),
        @ApiResponse(responseCode = "500", description = "伺服器錯誤")
    })
    public ResponseEntity<ResponseVO<SendNotificationResponse>> sendNotification(@Valid @RequestBody SendNotificationRequest request) {
        ResponseVO<SendNotificationResponse> response = new ResponseVO<>();
        
        try {
            log.info("Sending notification - channel: {}, recipient: {}, templateId: {}", 
                    request.getChannel(), request.getRecipient(), request.getTemplateId());
            
            SendNotificationResponse result = notificationFacade.sendNotification(request);
            
            if ("ERROR".equals(result.getStatus()) || "RATE_LIMITED".equals(result.getStatus())) {
                response.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
                response.setRtnMessage(result.getMessage());
                response.setRtnObj(result);
            } else {
                response.setRtnCode(ReturnCode.SUCCESS.getRtnCode());
                response.setRtnMessage("通知發送成功");
                response.setRtnObj(result);
            }
            
        } catch (Exception e) {
            log.error("Failed to send notification", e);
            response.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
            response.setRtnMessage("發送通知失敗");
        }
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 查詢通知狀態
     */
    @GetMapping("/status/{notificationId}")
    @Operation(summary = "查詢通知狀態", description = "根據通知 ID 查詢發送狀態")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功",
            content = @Content(schema = @Schema(implementation = ResponseVO.class))),
        @ApiResponse(responseCode = "404", description = "通知不存在"),
        @ApiResponse(responseCode = "500", description = "伺服器錯誤")
    })
    public ResponseEntity<ResponseVO<NotificationStatusResponse>> getNotificationStatus(
            @Parameter(description = "通知 ID") @PathVariable String notificationId) {
        ResponseVO<NotificationStatusResponse> response = new ResponseVO<>();
        
        try {
            log.info("Getting notification status for: {}", notificationId);
            
            NotificationStatusResponse result = notificationFacade.getNotificationStatus(notificationId);
            
            if ("NOT_FOUND".equals(result.getStatus())) {
                response.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
                response.setRtnMessage("通知不存在");
                response.setRtnObj(null);
            } else {
                response.setRtnCode(ReturnCode.SUCCESS.getRtnCode());
                response.setRtnMessage("查詢成功");
                response.setRtnObj(result);
            }
            
        } catch (Exception e) {
            log.error("Failed to get notification status", e);
            response.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
            response.setRtnMessage("查詢失敗");
        }
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 查詢通知歷史
     */
    @GetMapping("/history")
    @Operation(summary = "查詢通知歷史", description = "根據 uniqId 查詢通知發送歷史")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功",
            content = @Content(schema = @Schema(implementation = ResponseVO.class))),
        @ApiResponse(responseCode = "500", description = "伺服器錯誤")
    })
    public ResponseEntity<ResponseVO<List<NotificationStatusResponse>>> getNotificationHistory(
            @Parameter(description = "唯一識別碼") @RequestParam String uniqId,
            @Parameter(description = "通知通道 (EMAIL/SMS)") @RequestParam(required = false) String channel) {
        
        ResponseVO<List<NotificationStatusResponse>> response = new ResponseVO<>();
        
        try {
            log.info("Getting notification history for uniqId: {}, channel: {}", uniqId, channel);
            
            List<NotificationStatusResponse> history = notificationFacade.getNotificationHistory(uniqId, channel);
            
            response.setRtnCode(ReturnCode.SUCCESS.getRtnCode());
            response.setRtnMessage("查詢成功");
            response.setRtnObj(history);
            
        } catch (Exception e) {
            log.error("Failed to get notification history", e);
            response.setRtnCode(ReturnCode.ERROR_VALUE.getRtnCode());
            response.setRtnMessage("查詢失敗");
        }
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
