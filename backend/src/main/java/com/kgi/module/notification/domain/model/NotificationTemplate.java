package com.kgi.module.notification.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * 通知模板實體
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplate {
    
    private String templateId;
    private String templateName;
    private NotificationChannel channel;
    private String subject;
    private String content;
    private Set<String> requiredParameters;
    private boolean active;
    
    /**
     * 檢查參數是否完整
     */
    public boolean validateParameters(Set<String> providedParameters) {
        return providedParameters.containsAll(requiredParameters);
    }
    
    /**
     * 替換模板參數
     */
    public String renderContent(java.util.Map<String, String> parameters) {
        String renderedContent = content;
        for (java.util.Map.Entry<String, String> entry : parameters.entrySet()) {
            renderedContent = renderedContent.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return renderedContent;
    }
    
    /**
     * 替換主題參數
     */
    public String renderSubject(java.util.Map<String, String> parameters) {
        if (subject == null) {
            return null;
        }
        String renderedSubject = subject;
        for (java.util.Map.Entry<String, String> entry : parameters.entrySet()) {
            renderedSubject = renderedSubject.replace("{" + entry.getKey() + "}", entry.getValue());
        }
        return renderedSubject;
    }
}