package com.kgi.module.notification.application.usecase;

import com.kgi.module.notification.application.dto.request.SendNotificationRequest;
import com.kgi.module.notification.application.dto.response.SendNotificationResponse;
import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.service.NotificationDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 發送通知用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SendNotificationUseCase {
    
    private final NotificationDomainService notificationDomainService;
    private static final int SEND_INTERVAL_MINUTES = 1;
    
    @Transactional
    public SendNotificationResponse execute(SendNotificationRequest request) {
        log.info("Sending notification - channel: {}, recipient: {}, templateId: {}", 
                request.getChannel(), request.getRecipient(), request.getTemplateId());
        
        try {
            // 轉換通道類型
            NotificationChannel channel = NotificationChannel.fromCode(request.getChannel());
            
            // 檢查發送頻率限制
            if (!notificationDomainService.canSendNotification(
                    request.getUniqId(), channel, SEND_INTERVAL_MINUTES)) {
                return SendNotificationResponse.builder()
                        .status("RATE_LIMITED")
                        .message("請稍後再試，發送頻率過高")
                        .build();
            }
            
            // 建立通知
            Notification notification = notificationDomainService.createNotification(
                    channel,
                    request.getRecipient(),
                    request.getTemplateId(),
                    request.getParameters(),
                    request.getUniqId(),
                    request.getUniqType()
            );
            
            // 如果設定附件，更新通知
            if (request.getAttachment() != null) {
                notification.setAttachment(request.getAttachment());
            }
            
            // 建立回應
            return SendNotificationResponse.builder()
                    .notificationId(notification.getNotificationId())
                    .status(notification.getStatus().getCode())
                    .message("通知已加入發送佇列")
                    .createdAt(notification.getCreatedAt())
                    .estimatedSendTime(LocalDateTime.now().plusSeconds(30))
                    .build();
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid notification request: {}", e.getMessage());
            return SendNotificationResponse.builder()
                    .status("ERROR")
                    .message(e.getMessage())
                    .createdAt(LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            log.error("Failed to send notification", e);
            return SendNotificationResponse.builder()
                    .status("ERROR")
                    .message("發送通知失敗，請稍後再試")
                    .createdAt(LocalDateTime.now())
                    .build();
        }
    }
}