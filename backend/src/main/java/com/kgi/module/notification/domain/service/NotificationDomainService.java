package com.kgi.module.notification.domain.service;

import com.kgi.module.notification.domain.model.*;
import com.kgi.module.notification.domain.repository.NotificationRepository;
import com.kgi.module.notification.domain.repository.NotificationTemplateRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 通知領域服務
 * 處理通知相關的核心業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationDomainService {
    
    private final NotificationRepository notificationRepository;
    private final NotificationTemplateRepository templateRepository;
    
    /**
     * 建立通知
     */
    public Notification createNotification(
            NotificationChannel channel,
            String recipient,
            String templateId,
            Map<String, String> parameters,
            String uniqId,
            String uniqType) {
        
        // 查詢模板
        NotificationTemplate template = templateRepository.findByTemplateId(templateId)
                .orElseThrow(() -> new IllegalArgumentException("Template not found: " + templateId));
        
        // 驗證模板通道
        if (!template.getChannel().equals(channel)) {
            throw new IllegalArgumentException("Template channel mismatch");
        }
        
        // 驗證必要參數
        if (!template.validateParameters(parameters.keySet())) {
            throw new IllegalArgumentException("Missing required parameters");
        }
        
        // 建立收件人資訊
        RecipientInfo recipientInfo = RecipientInfo.builder()
                .email(channel == NotificationChannel.EMAIL ? recipient : null)
                .phone(channel == NotificationChannel.SMS ? recipient : null)
                .build();
        
        // 驗證收件人資訊
        if (!recipientInfo.isValidForChannel(channel)) {
            throw new IllegalArgumentException("Invalid recipient for channel: " + channel);
        }
        
        // 渲染內容
        String content = template.renderContent(parameters);
        String subject = template.renderSubject(parameters);
        
        // 建立通知
        Notification notification = Notification.builder()
                .notificationId(generateNotificationId())
                .channel(channel)
                .recipient(recipient)
                .templateId(templateId)
                .parameters(parameters)
                .status(NotificationStatus.PENDING)
                .content(content)
                .subject(subject)
                .uniqId(uniqId)
                .uniqType(uniqType)
                .retryCount(0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        
        return notificationRepository.save(notification);
    }
    
    /**
     * 處理通知發送成功
     */
    public void handleSendSuccess(Long notificationId) {
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new IllegalArgumentException("Notification not found: " + notificationId));
        
        notification.markAsSent();
        notificationRepository.save(notification);
        
        log.info("Notification {} sent successfully", notification.getNotificationId());
    }
    
    /**
     * 處理通知發送失敗
     */
    public void handleSendFailure(Long notificationId, String errorMessage, int maxRetryCount) {
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new IllegalArgumentException("Notification not found: " + notificationId));
        
        notification.markAsFailed(errorMessage);
        
        if (notification.canRetry(maxRetryCount)) {
            log.info("Notification {} failed but can retry. Retry count: {}", 
                    notification.getNotificationId(), notification.getRetryCount());
        } else {
            log.error("Notification {} failed and exceeded max retry count", 
                    notification.getNotificationId());
        }
        
        notificationRepository.save(notification);
    }
    
    /**
     * 檢查是否可以發送新通知
     * 例如：檢查發送頻率限制
     */
    public boolean canSendNotification(String uniqId, NotificationChannel channel, int intervalMinutes) {
        LocalDateTime timeLimit = LocalDateTime.now().minusMinutes(intervalMinutes);
        
        var recentNotifications = notificationRepository.findByUniqIdAndChannel(uniqId, channel);
        
        return recentNotifications.stream()
                .noneMatch(n -> n.getSentAt() != null && n.getSentAt().isAfter(timeLimit));
    }
    
    /**
     * 生成唯一的通知 ID
     */
    private String generateNotificationId() {
        return "NOTIF_" + UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
}