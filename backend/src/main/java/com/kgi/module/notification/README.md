# Notification Module (通知模組)

## 概述
這是根據 DDD 架構設計的通知模組，提供統一的 Email 和 SMS 發送功能。

## 模組架構

```
notification/
├── adapters/rest/           # REST API 控制器
│   └── NotificationController.java
├── application/
│   ├── dto/
│   │   ├── request/         # 請求 DTO
│   │   └── response/        # 回應 DTO
│   ├── facade/              # 應用服務門面
│   └── usecase/             # 業務用例
├── domain/
│   ├── model/               # 領域模型
│   ├── repository/          # 倉儲介面
│   └── service/             # 領域服務
├── infrastructure/
│   ├── entity/              # 資料庫實體
│   ├── external/            # 外部服務整合
│   ├── mapper/              # 物件映射器
│   └── repository/          # 倉儲實作
└── config/                  # 模組配置
```

## 功能特性

### 1. 統一通知介面
- 支援 Email 和 SMS 雙通道
- 策略模式選擇發送通道
- 支援模板管理

### 2. 批次處理
- 排程任務定期發送待處理通知
- 支援重試機制
- 錯誤處理和日誌記錄

### 3. 狀態管理
- 追蹤通知發送狀態
- 記錄發送歷史
- 提供查詢介面

## API 端點

### 1. 發送通知
```
POST /api/notification/send
```

**請求範例：**
```json
{
  "channel": "SMS",
  "recipient": "0912345678",
  "templateId": "OTP_TEMPLATE",
  "parameters": {
    "otpCode": "123456",
    "expireTime": "5"
  },
  "uniqId": "TEST123456",
  "uniqType": "IBR"
}
```

### 2. 查詢通知狀態
```
GET /api/notification/status/{notificationId}
```

### 3. 查詢通知歷史
```
GET /api/notification/history?uniqId={uniqId}&channel={channel}
```

## 配置設定

### application.yml 配置
```yaml
kgi:
  notification:
    enabled: true
    batch-size: 100
    retry-count: 3
    retry-delay: 60000
    
  mail-hunter:
    url: ${MAIL_HUNTER_URL}
    ftp-server: ${MAIL_HUNTER_FTP_SERVER}
    ftp-user: ${MAIL_HUNTER_FTP_USER}
    ftp-password: ${MAIL_HUNTER_FTP_PASSWORD}
    
  sms-hunter:
    url: ${SMS_HUNTER_URL}
    api-key: ${SMS_HUNTER_API_KEY}
```

## 使用方式

### 1. 注入服務
```java
@Autowired
private NotificationFacade notificationFacade;

// 發送通知
SendNotificationResponse response = notificationFacade.sendNotification(request);
```

### 2. 使用 UseCase
```java
@Autowired
private SendNotificationUseCase sendNotificationUseCase;

// 發送通知
SendNotificationResponse response = sendNotificationUseCase.execute(request);
```

## 模板管理

### Email 模板
- 支援 HTML 格式
- 動態參數替換
- 附件支援

### SMS 模板
- 純文字格式
- 短網址生成
- 字數限制檢查

## 排程任務

### SendMailHunterJob
- 每 20 秒執行一次
- 批次處理待發送郵件
- FTP 上傳附件

### SendSMSHunterJob
- 每 30 秒執行一次
- 批次處理待發送簡訊
- 短網址處理

## 錯誤處理

### 重試機制
- 最多重試 3 次
- 指數退避策略
- 錯誤記錄

### 狀態碼
| 狀態碼 | 說明 |
|--------|------|
| 0 | 待發送 |
| 1 | 發送成功 |
| 2 | 發送中 |
| 7 | 發送失敗 |
| 9 | 已取消 |

## 擴充說明

### 新增通知通道
1. 實作 `NotificationChannel` 介面
2. 註冊到 `NotificationChannelFactory`
3. 配置相關參數

### 自訂模板
1. 實作 `NotificationTemplate` 介面
2. 註冊模板到資料庫
3. 配置模板參數

---

最後更新：2025/6/14