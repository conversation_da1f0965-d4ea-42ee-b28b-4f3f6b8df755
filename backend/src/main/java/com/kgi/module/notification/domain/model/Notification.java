package com.kgi.module.notification.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知聚合根
 * 代表一個通知實體，包含所有通知相關的資訊
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    
    private Long id;
    private String notificationId;
    private NotificationChannel channel;
    private String recipient;
    private String templateId;
    private Map<String, String> parameters;
    private NotificationStatus status;
    private String content;
    private String subject;
    private String attachment;
    private Integer retryCount;
    private String errorMessage;
    private String uniqId;
    private String uniqType;
    private LocalDateTime createdAt;
    private LocalDateTime sentAt;
    private LocalDateTime updatedAt;
    
    /**
     * 標記為已發送
     */
    public void markAsSent() {
        this.status = NotificationStatus.SENT;
        this.sentAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 標記為失敗
     */
    public void markAsFailed(String errorMessage) {
        this.status = NotificationStatus.FAILED;
        this.errorMessage = errorMessage;
        this.retryCount = this.retryCount == null ? 1 : this.retryCount + 1;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 是否可以重試
     */
    public boolean canRetry(int maxRetryCount) {
        return this.retryCount != null && this.retryCount < maxRetryCount;
    }
    
    /**
     * 增加重試次數
     */
    public void incrementRetryCount() {
        this.retryCount = this.retryCount == null ? 1 : this.retryCount + 1;
        this.updatedAt = LocalDateTime.now();
    }
}