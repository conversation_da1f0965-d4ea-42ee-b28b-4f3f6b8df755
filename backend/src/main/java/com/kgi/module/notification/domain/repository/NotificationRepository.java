package com.kgi.module.notification.domain.repository;

import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.model.NotificationStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知倉儲介面
 * 定義通知持久化的抽象操作
 */
public interface NotificationRepository {
    
    /**
     * 儲存通知
     */
    Notification save(Notification notification);
    
    /**
     * 根據 ID 查詢通知
     */
    Optional<Notification> findById(Long id);
    
    /**
     * 根據通知 ID 查詢通知
     */
    Optional<Notification> findByNotificationId(String notificationId);
    
    /**
     * 查詢待發送的通知
     */
    List<Notification> findPendingNotifications(int limit);
    
    /**
     * 根據 uniqId 查詢通知歷史
     */
    List<Notification> findByUniqId(String uniqId);
    
    /**
     * 根據 uniqId 和通道查詢通知歷史
     */
    List<Notification> findByUniqIdAndChannel(String uniqId, NotificationChannel channel);
    
    /**
     * 查詢需要重試的通知
     */
    List<Notification> findFailedNotificationsForRetry(int maxRetryCount, LocalDateTime retryAfter);
    
    /**
     * 更新通知狀態
     */
    void updateStatus(Long id, NotificationStatus status);
    
    /**
     * 批量更新通知狀態
     */
    void batchUpdateStatus(List<Long> ids, NotificationStatus status);
    
    /**
     * 統計通知數量
     */
    long countByStatusAndCreatedAtBetween(NotificationStatus status, LocalDateTime startTime, LocalDateTime endTime);
}