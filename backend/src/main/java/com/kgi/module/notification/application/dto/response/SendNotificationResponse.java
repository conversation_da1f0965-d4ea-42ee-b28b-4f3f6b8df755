package com.kgi.module.notification.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 發送通知回應 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendNotificationResponse {
    
    private String notificationId;
    private String status;
    private String message;
    private LocalDateTime createdAt;
    private LocalDateTime estimatedSendTime;
}