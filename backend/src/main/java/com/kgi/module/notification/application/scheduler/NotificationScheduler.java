package com.kgi.module.notification.application.scheduler;

import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationStatus;
import com.kgi.module.notification.domain.repository.NotificationRepository;
import com.kgi.module.notification.infrastructure.external.NotificationSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知排程任務
 * 定期處理待發送的通知
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationScheduler {
    
    private final NotificationRepository notificationRepository;
    private final NotificationSender notificationSender;
    
    @Value("${kgi.notification.batch-size:100}")
    private int batchSize;
    
    @Value("${kgi.notification.retry-delay:60}")
    private int retryDelayMinutes;
    
    @Value("${kgi.notification.retry-count:3}")
    private int maxRetryCount;
    
    @Value("${kgi.notification.scheduler.enabled:true}")
    private boolean schedulerEnabled;
    
    /**
     * 處理待發送的通知
     * 每 20 秒執行一次
     */
    @Scheduled(fixedDelay = 20000)
    public void processPendingNotifications() {
        if (!schedulerEnabled) {
            return;
        }
        
        log.debug("Processing pending notifications...");
        
        try {
            // 查詢待發送的通知
            List<Notification> pendingNotifications = notificationRepository.findPendingNotifications(batchSize);
            
            if (!pendingNotifications.isEmpty()) {
                log.info("Found {} pending notifications to process", pendingNotifications.size());
                
                // 批量更新狀態為發送中
                List<Long> ids = pendingNotifications.stream()
                        .map(Notification::getId)
                        .toList();
                notificationRepository.batchUpdateStatus(ids, NotificationStatus.SENDING);
                
                // 逐個發送
                for (Notification notification : pendingNotifications) {
                    try {
                        notificationSender.sendNotification(notification);
                    } catch (Exception e) {
                        log.error("Failed to send notification: " + notification.getNotificationId(), e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing pending notifications", e);
        }
    }
    
    /**
     * 處理需要重試的通知
     * 每 5 分鐘執行一次
     */
    @Scheduled(fixedDelay = 300000)
    public void processRetryNotifications() {
        if (!schedulerEnabled) {
            return;
        }
        
        log.debug("Processing retry notifications...");
        
        try {
            // 查詢需要重試的通知
            LocalDateTime retryAfter = LocalDateTime.now().minusMinutes(retryDelayMinutes);
            List<Notification> retryNotifications = notificationRepository
                    .findFailedNotificationsForRetry(maxRetryCount, retryAfter);
            
            if (!retryNotifications.isEmpty()) {
                log.info("Found {} notifications to retry", retryNotifications.size());
                
                for (Notification notification : retryNotifications) {
                    try {
                        // 增加重試次數
                        notification.incrementRetryCount();
                        notification.setStatus(NotificationStatus.PENDING);
                        notificationRepository.save(notification);
                        
                        log.info("Notification {} queued for retry (attempt {})", 
                                notification.getNotificationId(), notification.getRetryCount());
                        
                    } catch (Exception e) {
                        log.error("Failed to retry notification: " + notification.getNotificationId(), e);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error processing retry notifications", e);
        }
    }
    
    /**
     * 統計發送情況
     * 每小時執行一次
     */
    @Scheduled(cron = "0 0 * * * *")
    public void logStatistics() {
        if (!schedulerEnabled) {
            return;
        }
        
        try {
            LocalDateTime hourAgo = LocalDateTime.now().minusHours(1);
            LocalDateTime now = LocalDateTime.now();
            
            long sentCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    NotificationStatus.SENT, hourAgo, now);
            long failedCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    NotificationStatus.FAILED, hourAgo, now);
            long pendingCount = notificationRepository.countByStatusAndCreatedAtBetween(
                    NotificationStatus.PENDING, LocalDateTime.MIN, now);
            
            log.info("Notification statistics - Last hour: sent={}, failed={}, current pending={}", 
                    sentCount, failedCount, pendingCount);
            
        } catch (Exception e) {
            log.error("Error logging statistics", e);
        }
    }
}