package com.kgi.module.notification.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 收件人資訊值物件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecipientInfo {
    
    private String email;
    private String phone;
    private String name;
    private String customerId;
    
    /**
     * 根據通道取得收件人
     */
    public String getRecipientByChannel(NotificationChannel channel) {
        switch (channel) {
            case EMAIL:
                return email;
            case SMS:
                return phone;
            default:
                throw new IllegalArgumentException("Unsupported channel: " + channel);
        }
    }
    
    /**
     * 驗證收件人資訊是否有效
     */
    public boolean isValidForChannel(NotificationChannel channel) {
        switch (channel) {
            case EMAIL:
                return email != null && !email.isEmpty() && email.contains("@");
            case SMS:
                return phone != null && !phone.isEmpty() && phone.matches("^09\\d{8}$");
            default:
                return false;
        }
    }
}