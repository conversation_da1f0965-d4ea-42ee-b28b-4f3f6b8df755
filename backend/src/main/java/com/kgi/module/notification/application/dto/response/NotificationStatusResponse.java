package com.kgi.module.notification.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知狀態查詢回應 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationStatusResponse {
    
    private String notificationId;
    private String channel;
    private String recipient;
    private String templateId;
    private String status;
    private String statusDescription;
    private Map<String, String> parameters;
    private String errorMessage;
    private Integer retryCount;
    private LocalDateTime createdAt;
    private LocalDateTime sentAt;
    private LocalDateTime updatedAt;
}