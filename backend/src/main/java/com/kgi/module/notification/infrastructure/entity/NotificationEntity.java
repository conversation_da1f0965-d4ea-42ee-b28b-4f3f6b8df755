package com.kgi.module.notification.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 通知實體
 * 對應資料庫表 notification_history
 */
@Entity
@Table(name = "notification_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;
    
    @Column(name = "notification_id", unique = true, nullable = false, length = 50)
    private String notificationId;
    
    @Column(name = "channel", nullable = false, length = 10)
    private String channel;
    
    @Column(name = "recipient", nullable = false, length = 100)
    private String recipient;
    
    @Column(name = "template_id", length = 50)
    private String templateId;
    
    @Column(name = "parameters", columnDefinition = "TEXT")
    private String parameters;
    
    @Column(name = "status", nullable = false, length = 2)
    private String status;
    
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "subject", length = 255)
    private String subject;
    
    @Column(name = "attachment", length = 500)
    private String attachment;
    
    @Column(name = "retry_count")
    private Integer retryCount;
    
    @Column(name = "error_message", length = 500)
    private String errorMessage;
    
    @Column(name = "uniq_id", length = 50)
    private String uniqId;
    
    @Column(name = "uniq_type", length = 20)
    private String uniqType;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "sent_at")
    private LocalDateTime sentAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (retryCount == null) {
            retryCount = 0;
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}