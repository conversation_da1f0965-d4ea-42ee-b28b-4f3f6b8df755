package com.kgi.module.notification.infrastructure.external;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * SmsHunter 客戶端
 * 負責與簡訊服務整合
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmsHunterClient {
    
    @Value("${kgi.sms-hunter.url:http://smshunter.example.com}")
    private String smsHunterUrl;
    
    @Value("${kgi.sms-hunter.api-key:test-api-key}")
    private String apiKey;
    
    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;
    
    /**
     * 發送簡訊
     */
    public SendSmsResult sendSms(SendSmsRequest request) {
        log.info("Sending SMS to: {}, content length: {}", 
                request.getPhoneNumber(), request.getContent().length());
        
        if (useMockupData) {
            return mockSendSms(request);
        }
        
        try {
            // TODO: 實際呼叫 SMS API
            // 這裡應該使用 HttpClient 或 RestTemplate 呼叫實際的 API
            
            // 檢查簡訊長度
            if (request.getContent().length() > 70) {
                log.warn("SMS content exceeds 70 characters, will be sent as multiple messages");
            }
            
            // 生成短網址（如果需要）
            String processedContent = request.getContent();
            if (request.isGenerateShortUrl() && request.getContent().contains("http")) {
                processedContent = generateShortUrl(request.getContent());
            }
            
            // 模擬成功回應
            return SendSmsResult.builder()
                    .success(true)
                    .messageId("SMS_" + System.currentTimeMillis())
                    .message("SMS sent successfully")
                    .build();
            
        } catch (Exception e) {
            log.error("Failed to send SMS", e);
            return SendSmsResult.builder()
                    .success(false)
                    .message("Failed to send SMS: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 生成短網址
     */
    private String generateShortUrl(String content) {
        // TODO: 實際短網址生成邏輯
        return content.replaceAll("https?://[\\w./]+", "https://short.url/abc123");
    }
    
    private SendSmsResult mockSendSms(SendSmsRequest request) {
        log.info("Mock sending SMS to: {}", request.getPhoneNumber());
        
        // 模擬失敗情況
        if (request.getPhoneNumber().startsWith("0900000")) {
            return SendSmsResult.builder()
                    .success(false)
                    .message("Mock SMS sending failed - invalid number")
                    .build();
        }
        
        return SendSmsResult.builder()
                .success(true)
                .messageId("MOCK_SMS_" + System.currentTimeMillis())
                .message("Mock SMS sent successfully")
                .build();
    }
    
    /**
     * 發送簡訊請求
     */
    @lombok.Data
    @lombok.Builder
    public static class SendSmsRequest {
        private String phoneNumber;
        private String content;
        private String templateId;
        private Map<String, String> parameters;
        private boolean generateShortUrl;
    }
    
    /**
     * 發送簡訊結果
     */
    @lombok.Data
    @lombok.Builder
    public static class SendSmsResult {
        private boolean success;
        private String messageId;
        private String message;
    }
}