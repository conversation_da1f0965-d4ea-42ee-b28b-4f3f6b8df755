package com.kgi.module.notification.infrastructure.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通知模板實體
 * 對應資料庫表 notification_template
 */
@Entity
@Table(name = "notification_template")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationTemplateEntity {
    
    @Id
    @Column(name = "template_id", length = 50)
    private String templateId;
    
    @Column(name = "template_name", nullable = false, length = 100)
    private String templateName;
    
    @Column(name = "channel", nullable = false, length = 10)
    private String channel;
    
    @Column(name = "subject", length = 255)
    private String subject;
    
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @Column(name = "required_parameters", length = 500)
    private String requiredParameters;
    
    @Column(name = "active", nullable = false)
    private Boolean active;
}