package com.kgi.module.notification.infrastructure.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.model.NotificationStatus;
import com.kgi.module.notification.infrastructure.entity.NotificationEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知物件映射器
 * 負責領域模型和資料庫實體之間的轉換
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationMapper {
    
    private final ObjectMapper objectMapper;
    
    /**
     * 將領域模型轉換為資料庫實體
     */
    public NotificationEntity toEntity(Notification notification) {
        return NotificationEntity.builder()
                .id(notification.getId())
                .notificationId(notification.getNotificationId())
                .channel(notification.getChannel().getCode())
                .recipient(notification.getRecipient())
                .templateId(notification.getTemplateId())
                .parameters(serializeParameters(notification.getParameters()))
                .status(notification.getStatus().getCode())
                .content(notification.getContent())
                .subject(notification.getSubject())
                .attachment(notification.getAttachment())
                .retryCount(notification.getRetryCount())
                .errorMessage(notification.getErrorMessage())
                .uniqId(notification.getUniqId())
                .uniqType(notification.getUniqType())
                .createdAt(notification.getCreatedAt())
                .sentAt(notification.getSentAt())
                .updatedAt(notification.getUpdatedAt())
                .build();
    }
    
    /**
     * 將資料庫實體轉換為領域模型
     */
    public Notification toDomain(NotificationEntity entity) {
        return Notification.builder()
                .id(entity.getId())
                .notificationId(entity.getNotificationId())
                .channel(NotificationChannel.fromCode(entity.getChannel()))
                .recipient(entity.getRecipient())
                .templateId(entity.getTemplateId())
                .parameters(deserializeParameters(entity.getParameters()))
                .status(NotificationStatus.fromCode(entity.getStatus()))
                .content(entity.getContent())
                .subject(entity.getSubject())
                .attachment(entity.getAttachment())
                .retryCount(entity.getRetryCount())
                .errorMessage(entity.getErrorMessage())
                .uniqId(entity.getUniqId())
                .uniqType(entity.getUniqType())
                .createdAt(entity.getCreatedAt())
                .sentAt(entity.getSentAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
    
    /**
     * 序列化參數
     */
    private String serializeParameters(Map<String, String> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(parameters);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize parameters", e);
            return null;
        }
    }
    
    /**
     * 反序列化參數
     */
    private Map<String, String> deserializeParameters(String parametersJson) {
        if (parametersJson == null || parametersJson.isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(parametersJson, new TypeReference<Map<String, String>>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize parameters", e);
            return new HashMap<>();
        }
    }
}