package com.kgi.module.notification.domain.model;

/**
 * 通知狀態枚舉
 */
public enum NotificationStatus {
    PENDING("0", "待發送"),
    SENT("1", "已發送"),
    SENDING("2", "發送中"),
    FAILED("7", "發送失敗"),
    CANCELLED("9", "已取消");
    
    private final String code;
    private final String description;
    
    NotificationStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static NotificationStatus fromCode(String code) {
        for (NotificationStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown notification status code: " + code);
    }
}