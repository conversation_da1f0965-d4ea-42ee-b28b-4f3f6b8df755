package com.kgi.module.notification.domain.repository;

import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.model.NotificationTemplate;

import java.util.List;
import java.util.Optional;

/**
 * 通知模板倉儲介面
 */
public interface NotificationTemplateRepository {
    
    /**
     * 根據模板 ID 查詢模板
     */
    Optional<NotificationTemplate> findByTemplateId(String templateId);
    
    /**
     * 根據通道查詢所有啟用的模板
     */
    List<NotificationTemplate> findActiveTemplatesByChannel(NotificationChannel channel);
    
    /**
     * 儲存模板
     */
    NotificationTemplate save(NotificationTemplate template);
    
    /**
     * 刪除模板
     */
    void deleteByTemplateId(String templateId);
    
    /**
     * 檢查模板是否存在
     */
    boolean existsByTemplateId(String templateId);
}