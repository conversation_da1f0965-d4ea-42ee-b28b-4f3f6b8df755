package com.kgi.module.notification.infrastructure.repository.impl;

import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.model.NotificationStatus;
import com.kgi.module.notification.domain.repository.NotificationRepository;
import com.kgi.module.notification.infrastructure.entity.NotificationEntity;
import com.kgi.module.notification.infrastructure.mapper.NotificationMapper;
import com.kgi.module.notification.infrastructure.repository.jpa.NotificationJpaRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 通知倉儲實作
 */
@Repository
@RequiredArgsConstructor
public class NotificationRepositoryImpl implements NotificationRepository {
    
    private final NotificationJpaRepository jpaRepository;
    private final NotificationMapper mapper;
    
    @Override
    @Transactional
    public Notification save(Notification notification) {
        NotificationEntity entity = mapper.toEntity(notification);
        NotificationEntity savedEntity = jpaRepository.save(entity);
        return mapper.toDomain(savedEntity);
    }
    
    @Override
    public Optional<Notification> findById(Long id) {
        return jpaRepository.findById(id)
                .map(mapper::toDomain);
    }
    
    @Override
    public Optional<Notification> findByNotificationId(String notificationId) {
        return jpaRepository.findByNotificationId(notificationId)
                .map(mapper::toDomain);
    }
    
    @Override
    public List<Notification> findPendingNotifications(int limit) {
        PageRequest pageRequest = PageRequest.of(0, limit);
        return jpaRepository.findByStatusOrderByCreatedAtAsc(
                NotificationStatus.PENDING.getCode(), pageRequest)
                .stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Notification> findByUniqId(String uniqId) {
        return jpaRepository.findByUniqIdOrderByCreatedAtDesc(uniqId)
                .stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Notification> findByUniqIdAndChannel(String uniqId, NotificationChannel channel) {
        return jpaRepository.findByUniqIdAndChannelOrderByCreatedAtDesc(uniqId, channel.getCode())
                .stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Notification> findFailedNotificationsForRetry(int maxRetryCount, LocalDateTime retryAfter) {
        return jpaRepository.findByStatusAndRetryCountLessThanAndUpdatedAtBefore(
                NotificationStatus.FAILED.getCode(), maxRetryCount, retryAfter)
                .stream()
                .map(mapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    @Transactional
    public void updateStatus(Long id, NotificationStatus status) {
        jpaRepository.findById(id).ifPresent(entity -> {
            entity.setStatus(status.getCode());
            entity.setUpdatedAt(LocalDateTime.now());
            if (status == NotificationStatus.SENT) {
                entity.setSentAt(LocalDateTime.now());
            }
            jpaRepository.save(entity);
        });
    }
    
    @Override
    @Transactional
    public void batchUpdateStatus(List<Long> ids, NotificationStatus status) {
        List<NotificationEntity> entities = jpaRepository.findAllById(ids);
        entities.forEach(entity -> {
            entity.setStatus(status.getCode());
            entity.setUpdatedAt(LocalDateTime.now());
            if (status == NotificationStatus.SENT) {
                entity.setSentAt(LocalDateTime.now());
            }
        });
        jpaRepository.saveAll(entities);
    }
    
    @Override
    public long countByStatusAndCreatedAtBetween(NotificationStatus status, LocalDateTime startTime, LocalDateTime endTime) {
        return jpaRepository.countByStatusAndCreatedAtBetween(status.getCode(), startTime, endTime);
    }
}