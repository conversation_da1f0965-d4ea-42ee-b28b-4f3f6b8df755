package com.kgi.module.notification.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 通知模組配置
 */
@Configuration
@EnableScheduling
@EnableTransactionManagement
@ComponentScan(basePackages = "com.kgi.module.notification")
@EntityScan(basePackages = "com.kgi.module.notification.infrastructure.entity")
@EnableJpaRepositories(basePackages = "com.kgi.module.notification.infrastructure.repository.jpa")
public class NotificationModuleConfig {
    
    /**
     * 提供 ObjectMapper Bean
     */
    @Bean
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper notificationObjectMapper() {
        return new ObjectMapper();
    }
}