package com.kgi.module.notification.infrastructure.external;

import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.service.NotificationDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 通知發送服務
 * 根據通道類型選擇適當的發送器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationSender {
    
    private final MailHunterClient mailHunterClient;
    private final SmsHunterClient smsHunterClient;
    private final NotificationDomainService domainService;
    
    @Value("${kgi.notification.retry-count:3}")
    private int maxRetryCount;
    
    /**
     * 發送通知
     */
    public void sendNotification(Notification notification) {
        log.info("Sending notification: {}, channel: {}", 
                notification.getNotificationId(), notification.getChannel());
        
        try {
            boolean success = false;
            String errorMessage = null;
            
            switch (notification.getChannel()) {
                case EMAIL:
                    var emailResult = sendEmail(notification);
                    success = emailResult.isSuccess();
                    errorMessage = emailResult.getMessage();
                    break;
                    
                case SMS:
                    var smsResult = sendSms(notification);
                    success = smsResult.isSuccess();
                    errorMessage = smsResult.getMessage();
                    break;
                    
                default:
                    errorMessage = "Unsupported channel: " + notification.getChannel();
            }
            
            if (success) {
                domainService.handleSendSuccess(notification.getId());
            } else {
                domainService.handleSendFailure(notification.getId(), errorMessage, maxRetryCount);
            }
            
        } catch (Exception e) {
            log.error("Failed to send notification: " + notification.getNotificationId(), e);
            domainService.handleSendFailure(notification.getId(), e.getMessage(), maxRetryCount);
        }
    }
    
    /**
     * 發送郵件
     */
    private MailHunterClient.SendEmailResult sendEmail(Notification notification) {
        // 處理附件
        String attachmentPath = null;
        if (notification.getAttachment() != null) {
            attachmentPath = mailHunterClient.uploadAttachment(notification.getAttachment());
        }
        
        var request = MailHunterClient.SendEmailRequest.builder()
                .recipient(notification.getRecipient())
                .subject(notification.getSubject())
                .content(notification.getContent())
                .templateId(notification.getTemplateId())
                .parameters(notification.getParameters())
                .attachment(attachmentPath)
                .build();
        
        return mailHunterClient.sendEmail(request);
    }
    
    /**
     * 發送簡訊
     */
    private SmsHunterClient.SendSmsResult sendSms(Notification notification) {
        var request = SmsHunterClient.SendSmsRequest.builder()
                .phoneNumber(notification.getRecipient())
                .content(notification.getContent())
                .templateId(notification.getTemplateId())
                .parameters(notification.getParameters())
                .generateShortUrl(true)
                .build();
        
        return smsHunterClient.sendSms(request);
    }
}