package com.kgi.module.notification.application.facade;

import com.kgi.module.notification.application.dto.request.SendNotificationRequest;
import com.kgi.module.notification.application.dto.response.NotificationStatusResponse;
import com.kgi.module.notification.application.dto.response.SendNotificationResponse;
import com.kgi.module.notification.application.usecase.GetNotificationStatusUseCase;
import com.kgi.module.notification.application.usecase.SendNotificationUseCase;
import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 通知模組 Facade
 * 提供統一的對外介面
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationFacade {
    
    private final SendNotificationUseCase sendNotificationUseCase;
    private final GetNotificationStatusUseCase getNotificationStatusUseCase;
    private final NotificationRepository notificationRepository;
    
    /**
     * 發送通知
     */
    public SendNotificationResponse sendNotification(SendNotificationRequest request) {
        return sendNotificationUseCase.execute(request);
    }
    
    /**
     * 查詢通知狀態
     */
    public NotificationStatusResponse getNotificationStatus(String notificationId) {
        return getNotificationStatusUseCase.execute(notificationId);
    }
    
    /**
     * 查詢通知歷史
     */
    public List<NotificationStatusResponse> getNotificationHistory(String uniqId, String channel) {
        log.info("Getting notification history for uniqId: {}, channel: {}", uniqId, channel);
        
        List<com.kgi.module.notification.domain.model.Notification> notifications;
        
        if (channel != null && !channel.isEmpty()) {
            NotificationChannel notificationChannel = NotificationChannel.fromCode(channel);
            notifications = notificationRepository.findByUniqIdAndChannel(uniqId, notificationChannel);
        } else {
            notifications = notificationRepository.findByUniqId(uniqId);
        }
        
        return notifications.stream()
                .map(this::mapToStatusResponse)
                .collect(Collectors.toList());
    }
    
    private NotificationStatusResponse mapToStatusResponse(com.kgi.module.notification.domain.model.Notification notification) {
        return NotificationStatusResponse.builder()
                .notificationId(notification.getNotificationId())
                .channel(notification.getChannel().getCode())
                .recipient(notification.getRecipient())
                .templateId(notification.getTemplateId())
                .status(notification.getStatus().getCode())
                .statusDescription(notification.getStatus().getDescription())
                .parameters(notification.getParameters())
                .errorMessage(notification.getErrorMessage())
                .retryCount(notification.getRetryCount())
                .createdAt(notification.getCreatedAt())
                .sentAt(notification.getSentAt())
                .updatedAt(notification.getUpdatedAt())
                .build();
    }
}