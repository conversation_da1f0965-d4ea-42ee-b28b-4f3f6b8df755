package com.kgi.module.notification.application.usecase;

import com.kgi.module.notification.application.dto.response.NotificationStatusResponse;
import com.kgi.module.notification.domain.model.Notification;
import com.kgi.module.notification.domain.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 查詢通知狀態用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetNotificationStatusUseCase {
    
    private final NotificationRepository notificationRepository;
    
    public NotificationStatusResponse execute(String notificationId) {
        log.info("Getting notification status for: {}", notificationId);
        
        return notificationRepository.findByNotificationId(notificationId)
                .map(this::mapToResponse)
                .orElse(NotificationStatusResponse.builder()
                        .notificationId(notificationId)
                        .status("NOT_FOUND")
                        .statusDescription("通知不存在")
                        .build());
    }
    
    private NotificationStatusResponse mapToResponse(Notification notification) {
        return NotificationStatusResponse.builder()
                .notificationId(notification.getNotificationId())
                .channel(notification.getChannel().getCode())
                .recipient(notification.getRecipient())
                .templateId(notification.getTemplateId())
                .status(notification.getStatus().getCode())
                .statusDescription(notification.getStatus().getDescription())
                .parameters(notification.getParameters())
                .errorMessage(notification.getErrorMessage())
                .retryCount(notification.getRetryCount())
                .createdAt(notification.getCreatedAt())
                .sentAt(notification.getSentAt())
                .updatedAt(notification.getUpdatedAt())
                .build();
    }
}