package com.kgi.module.notification.infrastructure.mapper;

import com.kgi.module.notification.domain.model.NotificationChannel;
import com.kgi.module.notification.domain.model.NotificationTemplate;
import com.kgi.module.notification.infrastructure.entity.NotificationTemplateEntity;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通知模板映射器
 */
@Component
public class NotificationTemplateMapper {
    
    /**
     * 將資料庫實體轉換為領域模型
     */
    public NotificationTemplate toDomain(NotificationTemplateEntity entity) {
        return NotificationTemplate.builder()
                .templateId(entity.getTemplateId())
                .templateName(entity.getTemplateName())
                .channel(NotificationChannel.fromCode(entity.getChannel()))
                .subject(entity.getSubject())
                .content(entity.getContent())
                .requiredParameters(parseRequiredParameters(entity.getRequiredParameters()))
                .active(entity.getActive())
                .build();
    }
    
    /**
     * 將領域模型轉換為資料庫實體
     */
    public NotificationTemplateEntity toEntity(NotificationTemplate template) {
        return NotificationTemplateEntity.builder()
                .templateId(template.getTemplateId())
                .templateName(template.getTemplateName())
                .channel(template.getChannel().getCode())
                .subject(template.getSubject())
                .content(template.getContent())
                .requiredParameters(serializeRequiredParameters(template.getRequiredParameters()))
                .active(template.isActive())
                .build();
    }
    
    /**
     * 解析必要參數字串
     */
    private Set<String> parseRequiredParameters(String parametersString) {
        if (parametersString == null || parametersString.isEmpty()) {
            return new HashSet<>();
        }
        return Arrays.stream(parametersString.split(","))
                .map(String::trim)
                .collect(Collectors.toSet());
    }
    
    /**
     * 序列化必要參數集合
     */
    private String serializeRequiredParameters(Set<String> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return "";
        }
        return String.join(",", parameters);
    }
}