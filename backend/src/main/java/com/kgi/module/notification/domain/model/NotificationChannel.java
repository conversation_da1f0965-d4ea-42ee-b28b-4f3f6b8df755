package com.kgi.module.notification.domain.model;

/**
 * 通知通道枚舉
 */
public enum NotificationChannel {
    EMAIL("EMAIL", "電子郵件"),
    SMS("SMS", "簡訊");
    
    private final String code;
    private final String description;
    
    NotificationChannel(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static NotificationChannel fromCode(String code) {
        for (NotificationChannel channel : values()) {
            if (channel.code.equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("Unknown notification channel code: " + code);
    }
}