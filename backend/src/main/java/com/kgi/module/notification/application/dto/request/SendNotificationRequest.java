package com.kgi.module.notification.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 發送通知請求 DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendNotificationRequest {
    
    @NotNull(message = "通知通道不能為空")
    private String channel;
    
    @NotBlank(message = "收件人不能為空")
    private String recipient;
    
    @NotBlank(message = "模板ID不能為空")
    private String templateId;
    
    private Map<String, String> parameters;
    
    @NotBlank(message = "唯一識別碼不能為空")
    private String uniqId;
    
    @NotBlank(message = "類型不能為空")
    private String uniqType;
    
    private String attachment;
    
    @Builder.Default
    private boolean async = true;
}