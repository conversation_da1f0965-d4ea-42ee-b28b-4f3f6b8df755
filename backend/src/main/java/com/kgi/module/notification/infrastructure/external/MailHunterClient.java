package com.kgi.module.notification.infrastructure.external;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * MailHunter 客戶端
 * 負責與 MailHunter 郵件服務整合
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MailHunterClient {
    
    @Value("${kgi.mail-hunter.url:http://mailhunter.example.com}")
    private String mailHunterUrl;
    
    @Value("${kgi.mail-hunter.owner-id:3134}")
    private String ownerId;
    
    @Value("${kgi.mail-hunter.project-code:eOpen}")
    private String projectCode;
    
    @Value("${kgi.mail-hunter.product-code:S05}")
    private String productCode;
    
    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;
    
    /**
     * 發送郵件
     */
    public SendEmailResult sendEmail(SendEmailRequest request) {
        log.info("Sending email to: {}, subject: {}, templateId: {}", 
                request.getRecipient(), request.getSubject(), request.getTemplateId());
        
        if (useMockupData) {
            return mockSendEmail(request);
        }
        
        try {
            // TODO: 實際呼叫 MailHunter API
            // 這裡應該使用 HttpClient 或 RestTemplate 呼叫實際的 API
            
            Map<String, Object> apiRequest = new HashMap<>();
            apiRequest.put("ownerId", ownerId);
            apiRequest.put("projectCode", projectCode);
            apiRequest.put("productCode", productCode);
            apiRequest.put("templateId", request.getTemplateId());
            apiRequest.put("email", request.getRecipient());
            apiRequest.put("subject", request.getSubject());
            apiRequest.put("content", request.getContent());
            apiRequest.put("parameters", request.getParameters());
            
            // 模擬成功回應
            return SendEmailResult.builder()
                    .success(true)
                    .messageId("MH_" + System.currentTimeMillis())
                    .message("Email sent successfully")
                    .build();
            
        } catch (Exception e) {
            log.error("Failed to send email", e);
            return SendEmailResult.builder()
                    .success(false)
                    .message("Failed to send email: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 上傳附件到 FTP
     */
    public String uploadAttachment(String filePath) {
        log.info("Uploading attachment: {}", filePath);
        
        if (useMockupData) {
            return "mock_attachment_" + System.currentTimeMillis() + ".pdf";
        }
        
        // TODO: 實際 FTP 上傳邏輯
        return filePath;
    }
    
    private SendEmailResult mockSendEmail(SendEmailRequest request) {
        log.info("Mock sending email to: {}", request.getRecipient());
        
        // 模擬失敗情況
        if (request.getRecipient().contains("fail")) {
            return SendEmailResult.builder()
                    .success(false)
                    .message("Mock email sending failed")
                    .build();
        }
        
        return SendEmailResult.builder()
                .success(true)
                .messageId("MOCK_MH_" + System.currentTimeMillis())
                .message("Mock email sent successfully")
                .build();
    }
    
    /**
     * 發送郵件請求
     */
    @lombok.Data
    @lombok.Builder
    public static class SendEmailRequest {
        private String recipient;
        private String subject;
        private String content;
        private String templateId;
        private Map<String, String> parameters;
        private String attachment;
    }
    
    /**
     * 發送郵件結果
     */
    @lombok.Data
    @lombok.Builder
    public static class SendEmailResult {
        private boolean success;
        private String messageId;
        private String message;
    }
}