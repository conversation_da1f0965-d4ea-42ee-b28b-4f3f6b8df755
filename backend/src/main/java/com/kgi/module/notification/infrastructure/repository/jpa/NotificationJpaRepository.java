package com.kgi.module.notification.infrastructure.repository.jpa;

import com.kgi.module.notification.infrastructure.entity.NotificationEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知 JPA Repository
 */
@Repository
public interface NotificationJpaRepository extends JpaRepository<NotificationEntity, Long> {
    
    Optional<NotificationEntity> findByNotificationId(String notificationId);
    
    List<NotificationEntity> findByStatusOrderByCreatedAtAsc(String status, Pageable pageable);
    
    List<NotificationEntity> findByUniqIdOrderByCreatedAtDesc(String uniqId);
    
    List<NotificationEntity> findByUniqIdAndChannelOrderByCreatedAtDesc(String uniqId, String channel);
    
    List<NotificationEntity> findByStatusAndRetryCountLessThanAndUpdatedAtBefore(
            String status, Integer maxRetryCount, LocalDateTime updatedAtBefore);
    
    long countByStatusAndCreatedAtBetween(String status, LocalDateTime startTime, LocalDateTime endTime);
}