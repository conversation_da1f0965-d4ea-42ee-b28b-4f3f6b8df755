package com.kgi.module.notification.infrastructure.repository.impl;

import com.kgi.module.notification.domain.model.NotificationTemplate;
import com.kgi.module.notification.domain.repository.NotificationTemplateRepository;
import com.kgi.module.notification.domain.model.NotificationChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 通知模板倉儲實作
 * 暫時使用記憶體存儲，未來可改為資料庫實作
 */
@Slf4j
@Repository
public class NotificationTemplateRepositoryImpl implements NotificationTemplateRepository {
    
    // 暫時使用記憶體存儲模板
    private final Map<String, NotificationTemplate> templates = new HashMap<>();
    
    public NotificationTemplateRepositoryImpl() {
        // 初始化預設模板
        initDefaultTemplates();
    }
    
    @Override
    public Optional<NotificationTemplate> findByTemplateId(String templateId) {
        return Optional.ofNullable(templates.get(templateId));
    }
    
    @Override
    public NotificationTemplate save(NotificationTemplate template) {
        templates.put(template.getTemplateId(), template);
        log.info("Saved notification template: {}", template.getTemplateId());
        return template;
    }
    
    @Override
    public List<NotificationTemplate> findActiveTemplatesByChannel(NotificationChannel channel) {
        return templates.values().stream()
                .filter(template -> template.getChannel() == channel && template.isActive())
                .collect(Collectors.toList());
    }
    
    @Override
    public void deleteByTemplateId(String templateId) {
        templates.remove(templateId);
        log.info("Deleted notification template: {}", templateId);
    }
    
    @Override
    public boolean existsByTemplateId(String templateId) {
        return templates.containsKey(templateId);
    }
    
    /**
     * 初始化預設模板
     */
    private void initDefaultTemplates() {
        // OTP 簡訊模板
        NotificationTemplate otpSmsTemplate = NotificationTemplate.builder()
                .templateId("OTP_SMS")
                .templateName("OTP 簡訊模板")
                .channel(NotificationChannel.SMS)
                .subject("")
                .content("動態密碼為{{otpCode}}為凱基銀行線上申請")
                .requiredParameters(Set.of("otpCode"))
                .active(true)
                .build();
        templates.put(otpSmsTemplate.getTemplateId(), otpSmsTemplate);
        
        // OTP Email 模板
        NotificationTemplate otpEmailTemplate = NotificationTemplate.builder()
                .templateId("OTP_EMAIL")
                .templateName("OTP Email 模板")
                .channel(NotificationChannel.EMAIL)
                .subject("凱基銀行 - 驗證碼通知")
                .content("親愛的客戶您好：\n\n" +
                         "您的驗證碼為：{{otpCode}}\n" +
                         "此驗證碼將於 5 分鐘後失效，請盡速完成驗證。\n\n" +
                         "凱基銀行 敬上")
                .requiredParameters(Set.of("otpCode"))
                .active(true)
                .build();
        templates.put(otpEmailTemplate.getTemplateId(), otpEmailTemplate);
        
        // 匯款通知 Email 模板
        NotificationTemplate remittanceEmailTemplate = NotificationTemplate.builder()
                .templateId("REMITTANCE_NOTIFICATION")
                .templateName("匯款通知 Email 模板")
                .channel(NotificationChannel.EMAIL)
                .subject("凱基銀行 - 匯款通知")
                .content("親愛的 {{customerName}} 您好：\n\n" +
                         "您的匯款申請已處理完成。\n" +
                         "匯款金額：{{amount}} {{currency}}\n" +
                         "交易編號：{{transactionId}}\n\n" +
                         "如有任何問題，請聯繫我們的客服中心。\n\n" +
                         "凱基銀行 敬上")
                .requiredParameters(Set.of(
                        "customerName",
                        "amount",
                        "currency",
                        "transactionId"
                ))
                .active(true)
                .build();
        templates.put(remittanceEmailTemplate.getTemplateId(), remittanceEmailTemplate);
        
        log.info("Initialized {} default notification templates", templates.size());
    }
}