package com.kgi.module.corporate.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 批次處理服務
 * 處理企業解款的批次作業功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchProcessingService {
    
    private final CorporateRemittanceService corporateRemittanceService;
    private final Map<String, Map<String, Object>> batchJobStatuses = new ConcurrentHashMap<>();
    private final Executor batchExecutor = Executors.newFixedThreadPool(5);
    
    /**
     * 提交批次解款申請
     */
    @Transactional
    public Map<String, Object> submitBatchRemittance(List<Map<String, Object>> remittanceRequests, String submitter) {
        log.info("提交批次解款申請: count={}, submitter={}", remittanceRequests.size(), maskId(submitter));
        
        try {
            // 1. 驗證批次申請
            validateBatchRequest(remittanceRequests);
            
            // 2. 生成批次編號
            String batchId = generateBatchId();
            
            // 3. 建立批次作業記錄
            Map<String, Object> batchJob = createBatchJob(batchId, remittanceRequests, submitter);
            
            // 4. 非同步處理批次申請
            CompletableFuture.runAsync(() -> processBatchRemittanceAsync(batchId, remittanceRequests), batchExecutor);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("batchId", batchId);
            result.put("totalCount", remittanceRequests.size());
            result.put("submissionTime", LocalDateTime.now());
            result.put("status", "PROCESSING");
            result.put("estimatedCompletionTime", LocalDateTime.now().plusMinutes(remittanceRequests.size() * 2));
            
            log.info("批次解款申請提交成功: batchId={}, count={}", batchId, remittanceRequests.size());
            return result;
            
        } catch (Exception e) {
            log.error("批次解款申請提交失敗: {}", e.getMessage(), e);
            throw new BusinessException("BATCH_SUBMISSION_FAILED", "批次申請提交失敗: " + e.getMessage());
        }
    }
    
    /**
     * 查詢批次處理狀態
     */
    public Map<String, Object> getBatchStatus(String batchId) {
        log.info("查詢批次處理狀態: batchId={}", batchId);
        
        try {
            Map<String, Object> batchJob = batchJobStatuses.get(batchId);
            if (batchJob == null) {
                throw new BusinessException("BATCH_NOT_FOUND", "找不到批次作業: " + batchId);
            }
            
            Map<String, Object> result = new HashMap<>(batchJob);
            result.put("queryTime", LocalDateTime.now());
            
            // 計算處理進度
            int totalCount = (Integer) batchJob.get("totalCount");
            int processedCount = (Integer) batchJob.get("processedCount");
            int successCount = (Integer) batchJob.get("successCount");
            int failedCount = (Integer) batchJob.get("failedCount");
            
            result.put("progressPercentage", (processedCount * 100.0) / totalCount);
            result.put("successRate", processedCount > 0 ? (successCount * 100.0) / processedCount : 0);
            
            return result;
            
        } catch (BusinessException e) {
            log.error("批次狀態查詢失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批次狀態查詢異常: {}", e.getMessage(), e);
            throw new BusinessException("BATCH_STATUS_QUERY_ERROR", "批次狀態查詢異常: " + e.getMessage());
        }
    }
    
    /**
     * 取消批次處理
     */
    @Transactional
    public Map<String, Object> cancelBatch(String batchId, String operator) {
        log.info("取消批次處理: batchId={}, operator={}", batchId, maskId(operator));
        
        try {
            Map<String, Object> batchJob = batchJobStatuses.get(batchId);
            if (batchJob == null) {
                throw new BusinessException("BATCH_NOT_FOUND", "找不到批次作業: " + batchId);
            }
            
            String currentStatus = (String) batchJob.get("status");
            if ("COMPLETED".equals(currentStatus) || "CANCELLED".equals(currentStatus)) {
                throw new BusinessException("BATCH_CANNOT_CANCEL", "批次作業已完成或已取消，無法取消");
            }
            
            // 更新批次狀態
            batchJob.put("status", "CANCELLED");
            batchJob.put("cancelledBy", operator);
            batchJob.put("cancellationTime", LocalDateTime.now());
            batchJob.put("cancellationReason", "使用者取消");
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("batchId", batchId);
            result.put("status", "CANCELLED");
            result.put("cancelledBy", maskId(operator));
            result.put("cancellationTime", LocalDateTime.now());
            
            log.info("批次處理已取消: batchId={}", batchId);
            return result;
            
        } catch (BusinessException e) {
            log.error("批次取消失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("批次取消異常: {}", e.getMessage(), e);
            throw new BusinessException("BATCH_CANCELLATION_ERROR", "批次取消異常: " + e.getMessage());
        }
    }
    
    /**
     * 重新處理失敗項目
     */
    @Transactional
    public Map<String, Object> retryFailedItems(String batchId, String operator) {
        log.info("重新處理失敗項目: batchId={}, operator={}", batchId, maskId(operator));
        
        try {
            Map<String, Object> batchJob = batchJobStatuses.get(batchId);
            if (batchJob == null) {
                throw new BusinessException("BATCH_NOT_FOUND", "找不到批次作業: " + batchId);
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> failedItems = (List<Map<String, Object>>) batchJob.get("failedItems");
            if (failedItems == null || failedItems.isEmpty()) {
                throw new BusinessException("NO_FAILED_ITEMS", "沒有失敗項目需要重新處理");
            }
            
            // 生成重試批次編號
            String retryBatchId = generateRetryBatchId(batchId);
            
            // 建立重試批次作業
            List<Map<String, Object>> retryRequests = new ArrayList<>();
            for (Map<String, Object> failedItem : failedItems) {
                @SuppressWarnings("unchecked")
                Map<String, Object> originalRequest = (Map<String, Object>) failedItem.get("originalRequest");
                retryRequests.add(originalRequest);
            }
            
            Map<String, Object> retryBatch = createBatchJob(retryBatchId, retryRequests, operator);
            retryBatch.put("originalBatchId", batchId);
            retryBatch.put("isRetry", true);
            
            // 非同步處理重試申請
            CompletableFuture.runAsync(() -> processBatchRemittanceAsync(retryBatchId, retryRequests), batchExecutor);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("retryBatchId", retryBatchId);
            result.put("originalBatchId", batchId);
            result.put("retryCount", retryRequests.size());
            result.put("submissionTime", LocalDateTime.now());
            result.put("status", "PROCESSING");
            
            log.info("失敗項目重試提交成功: retryBatchId={}, count={}", retryBatchId, retryRequests.size());
            return result;
            
        } catch (BusinessException e) {
            log.error("失敗項目重試失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("失敗項目重試異常: {}", e.getMessage(), e);
            throw new BusinessException("RETRY_FAILED_ITEMS_ERROR", "重試異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢批次處理歷史
     */
    public Map<String, Object> getBatchHistory(String submitter, int days) {
        log.info("查詢批次處理歷史: submitter={}, days={}", maskId(submitter), days);
        
        try {
            List<Map<String, Object>> history = new ArrayList<>();
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
            
            for (Map<String, Object> batchJob : batchJobStatuses.values()) {
                LocalDateTime submissionTime = (LocalDateTime) batchJob.get("submissionTime");
                String batchSubmitter = (String) batchJob.get("submitter");
                
                if (submissionTime.isAfter(cutoffDate) && 
                    (submitter == null || submitter.equals(batchSubmitter))) {
                    
                    Map<String, Object> historyItem = new HashMap<>();
                    historyItem.put("batchId", batchJob.get("batchId"));
                    historyItem.put("submissionTime", batchJob.get("submissionTime"));
                    historyItem.put("status", batchJob.get("status"));
                    historyItem.put("totalCount", batchJob.get("totalCount"));
                    historyItem.put("successCount", batchJob.get("successCount"));
                    historyItem.put("failedCount", batchJob.get("failedCount"));
                    historyItem.put("submitter", maskId((String) batchJob.get("submitter")));
                    
                    history.add(historyItem);
                }
            }
            
            // 按提交時間降序排列
            history.sort((a, b) -> {
                LocalDateTime timeA = (LocalDateTime) a.get("submissionTime");
                LocalDateTime timeB = (LocalDateTime) b.get("submissionTime");
                return timeB.compareTo(timeA);
            });
            
            Map<String, Object> result = new HashMap<>();
            result.put("history", history);
            result.put("totalBatches", history.size());
            result.put("queryPeriod", days + "天");
            result.put("queryTime", LocalDateTime.now());
            
            return result;
            
        } catch (Exception e) {
            log.error("批次歷史查詢異常: {}", e.getMessage(), e);
            throw new BusinessException("BATCH_HISTORY_QUERY_ERROR", "批次歷史查詢異常: " + e.getMessage());
        }
    }
    
    // ==================== 私有方法 ====================
    
    private void validateBatchRequest(List<Map<String, Object>> remittanceRequests) {
        if (remittanceRequests == null || remittanceRequests.isEmpty()) {
            throw new BusinessException("EMPTY_BATCH_REQUEST", "批次申請不能為空");
        }
        
        if (remittanceRequests.size() > 100) {
            throw new BusinessException("BATCH_SIZE_EXCEEDED", "批次申請數量不能超過100筆");
        }
        
        // 驗證每筆申請的必要欄位
        for (int i = 0; i < remittanceRequests.size(); i++) {
            Map<String, Object> request = remittanceRequests.get(i);
            validateSingleRequest(request, i + 1);
        }
    }
    
    private void validateSingleRequest(Map<String, Object> request, int index) {
        String[] requiredFields = {"unifiedNumber", "companyName", "amount", "currency"};
        
        for (String field : requiredFields) {
            if (!request.containsKey(field) || request.get(field) == null) {
                throw new BusinessException("MISSING_REQUIRED_FIELD", 
                        String.format("第%d筆申請缺少必要欄位: %s", index, field));
            }
        }
    }
    
    private String generateBatchId() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        return "BATCH" + timestamp.substring(timestamp.length() - 8) + 
               UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
    
    private String generateRetryBatchId(String originalBatchId) {
        return "RETRY_" + originalBatchId + "_" + UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
    
    private Map<String, Object> createBatchJob(String batchId, List<Map<String, Object>> requests, String submitter) {
        Map<String, Object> batchJob = new HashMap<>();
        batchJob.put("batchId", batchId);
        batchJob.put("submitter", submitter);
        batchJob.put("submissionTime", LocalDateTime.now());
        batchJob.put("status", "PROCESSING");
        batchJob.put("totalCount", requests.size());
        batchJob.put("processedCount", 0);
        batchJob.put("successCount", 0);
        batchJob.put("failedCount", 0);
        batchJob.put("successItems", new ArrayList<Map<String, Object>>());
        batchJob.put("failedItems", new ArrayList<Map<String, Object>>());
        batchJob.put("originalRequests", new ArrayList<>(requests));
        
        batchJobStatuses.put(batchId, batchJob);
        return batchJob;
    }
    
    private void processBatchRemittanceAsync(String batchId, List<Map<String, Object>> requests) {
        log.info("開始非同步處理批次解款: batchId={}, count={}", batchId, requests.size());
        
        try {
            Map<String, Object> batchJob = batchJobStatuses.get(batchId);
            if (batchJob == null) {
                log.error("找不到批次作業: {}", batchId);
                return;
            }
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> successItems = (List<Map<String, Object>>) batchJob.get("successItems");
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> failedItems = (List<Map<String, Object>>) batchJob.get("failedItems");
            
            for (int i = 0; i < requests.size(); i++) {
                // 檢查是否已取消
                if ("CANCELLED".equals(batchJob.get("status"))) {
                    log.info("批次處理已取消: {}", batchId);
                    break;
                }
                
                Map<String, Object> request = requests.get(i);
                
                try {
                    // 處理單筆解款申請
                    String remittanceId = corporateRemittanceService.processCorporateRemittance(request);
                    
                    // 記錄成功項目
                    Map<String, Object> successItem = new HashMap<>();
                    successItem.put("index", i + 1);
                    successItem.put("remittanceId", remittanceId);
                    successItem.put("unifiedNumber", request.get("unifiedNumber"));
                    successItem.put("companyName", request.get("companyName"));
                    successItem.put("amount", request.get("amount"));
                    successItem.put("processTime", LocalDateTime.now());
                    successItem.put("status", "SUCCESS");
                    
                    successItems.add(successItem);
                    batchJob.put("successCount", (Integer) batchJob.get("successCount") + 1);
                    
                    log.debug("批次項目處理成功: batchId={}, index={}, remittanceId={}", 
                            batchId, i + 1, remittanceId);
                    
                } catch (Exception e) {
                    // 記錄失敗項目
                    Map<String, Object> failedItem = new HashMap<>();
                    failedItem.put("index", i + 1);
                    failedItem.put("unifiedNumber", request.get("unifiedNumber"));
                    failedItem.put("companyName", request.get("companyName"));
                    failedItem.put("amount", request.get("amount"));
                    failedItem.put("errorMessage", e.getMessage());
                    failedItem.put("processTime", LocalDateTime.now());
                    failedItem.put("status", "FAILED");
                    failedItem.put("originalRequest", request);
                    
                    failedItems.add(failedItem);
                    batchJob.put("failedCount", (Integer) batchJob.get("failedCount") + 1);
                    
                    log.error("批次項目處理失敗: batchId={}, index={}, error={}", 
                            batchId, i + 1, e.getMessage());
                }
                
                // 更新處理進度
                batchJob.put("processedCount", i + 1);
                
                // 模擬處理時間
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            
            // 更新最終狀態
            if (!"CANCELLED".equals(batchJob.get("status"))) {
                batchJob.put("status", "COMPLETED");
                batchJob.put("completionTime", LocalDateTime.now());
            }
            
            log.info("批次解款處理完成: batchId={}, success={}, failed={}", 
                    batchId, batchJob.get("successCount"), batchJob.get("failedCount"));
            
        } catch (Exception e) {
            log.error("批次處理異常: batchId={}, error={}", batchId, e.getMessage(), e);
            
            Map<String, Object> batchJob = batchJobStatuses.get(batchId);
            if (batchJob != null) {
                batchJob.put("status", "ERROR");
                batchJob.put("errorMessage", e.getMessage());
                batchJob.put("errorTime", LocalDateTime.now());
            }
        }
    }
    
    private String maskId(String id) {
        if (id == null || id.length() < 4) {
            return id;
        }
        return id.substring(0, 2) + "***" + id.substring(id.length() - 2);
    }
}