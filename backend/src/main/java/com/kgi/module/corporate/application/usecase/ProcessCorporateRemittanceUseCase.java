package com.kgi.module.corporate.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.core.domain.valueobject.UnifiedNumber;
import com.kgi.module.corporate.application.dto.request.CorporateRemittanceRequest;
import com.kgi.module.corporate.application.dto.response.CorporateRemittanceResponse;
import com.kgi.module.corporate.domain.model.*;
import com.kgi.module.corporate.domain.repository.CorporateRemittanceRepository;
import com.kgi.module.corporate.domain.service.CorporateRemittanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 企業解款處理UseCase
 * 負責處理企業解款申請的完整業務流程
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessCorporateRemittanceUseCase {
    
    private final CorporateRemittanceRepository repository;
    private final CorporateRemittanceService corporateRemittanceService;
    
    /**
     * 處理企業解款申請
     */
    @Transactional
    public CorporateRemittanceResponse processRemittance(CorporateRemittanceRequest request) {
        log.info("處理企業解款申請: unifiedNumber={}, amount={} {}", 
                request.getUnifiedNumber(), request.getAmount(), request.getCurrency());
        
        try {
            // 1. 驗證統一編號
            UnifiedNumber unifiedNumber = validateUnifiedNumber(request.getUnifiedNumber());
            
            // 2. 驗證企業資格
            validateCorporateEligibility(unifiedNumber, request.getAmount(), request.getCurrency());
            
            // 3. 檢查重複申請
            checkDuplicateApplication(request.getTheirRefNo());
            
            // 4. 建立領域物件
            CorporateRemittance remittance = buildCorporateRemittance(request);
            
            // 5. 執行風險評估
            performRiskAssessment(remittance);
            
            // 6. 儲存解款記錄
            CorporateRemittance savedRemittance = repository.save(remittance);
            
            // 7. 啟動審核流程
            if (remittance.requiresManagerApproval()) {
                startApprovalWorkflow(savedRemittance);
            }
            
            log.info("企業解款申請處理完成: remittanceId={}", savedRemittance.getRemittanceId());
            
            return buildResponse(savedRemittance);
            
        } catch (Exception e) {
            log.error("企業解款申請處理失敗: {}", e.getMessage(), e);
            throw new BusinessException("CORPORATE_REMITTANCE_FAILED", "企業解款申請處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 驗證統一編號
     */
    private UnifiedNumber validateUnifiedNumber(String unifiedNumberStr) {
        if (!UnifiedNumber.isValid(unifiedNumberStr)) {
            throw new BusinessException("INVALID_UNIFIED_NUMBER", "統一編號格式錯誤");
        }
        return UnifiedNumber.of(unifiedNumberStr);
    }
    
    /**
     * 驗證企業資格
     */
    private void validateCorporateEligibility(UnifiedNumber unifiedNumber, BigDecimal amount, String currency) {
        boolean eligible = corporateRemittanceService.validateCorporateEligibility(
                unifiedNumber.getValue(), amount, currency);
        
        if (!eligible) {
            throw new BusinessException("INELIGIBLE_CORPORATE", "企業不符合解款資格");
        }
    }
    
    /**
     * 檢查重複申請
     */
    private void checkDuplicateApplication(String theirRefNo) {
        // TODO: 實作重複申請檢查邏輯
        log.debug("檢查重複申請: theirRefNo={}", theirRefNo);
    }
    
    /**
     * 建立企業解款領域物件
     */
    private CorporateRemittance buildCorporateRemittance(CorporateRemittanceRequest request) {
        // 建立企業資訊
        CorporateInfo corporateInfo = buildCorporateInfo(request);
        
        // 建立解款詳情
        RemittanceDetails remittanceDetails = buildRemittanceDetails(request);
        
        // 生成解款ID
        String remittanceId = generateRemittanceId();
        
        return CorporateRemittance.builder()
                .remittanceId(remittanceId)
                .unifiedNumber(UnifiedNumber.of(request.getUnifiedNumber()))
                .corporateInfo(corporateInfo)
                .remittanceDetails(remittanceDetails)
                .build();
    }
    
    /**
     * 建立企業資訊
     */
    private CorporateInfo buildCorporateInfo(CorporateRemittanceRequest request) {
        CorporateInfo.ContactPerson contactPerson = CorporateInfo.ContactPerson.builder()
                .name(request.getContactPersonName())
                .title(request.getContactPersonTitle())
                .phoneNumber(request.getContactPersonPhone())
                .email(request.getContactPersonEmail())
                .idNumber(request.getContactPersonId())
                .build();
        
        CorporateInfo.CorporateAddress address = CorporateInfo.CorporateAddress.builder()
                .fullAddress(request.getCompanyAddress())
                .build();
        
        return CorporateInfo.builder()
                .companyName(request.getCompanyName())
                .companyNameEnglish(request.getCompanyNameEnglish())
                .businessType(request.getBusinessType())
                .industryCode(request.getIndustryCode())
                .contactPerson(contactPerson)
                .address(address)
                .telephoneNumber(request.getCompanyTelephone())
                .email(request.getCompanyEmail())
                .build();
    }
    
    /**
     * 建立解款詳情
     */
    private RemittanceDetails buildRemittanceDetails(CorporateRemittanceRequest request) {
        // 建立收款人銀行資訊
        RemittanceDetails.BeneficiaryInfo.BankInfo bankInfo = 
                RemittanceDetails.BeneficiaryInfo.BankInfo.builder()
                        .bankName(request.getBeneficiaryBankName())
                        .bankCode(request.getBeneficiaryBankCode())
                        .swiftCode(request.getBeneficiarySwiftCode())
                        .bankAddress(request.getBeneficiaryBankAddress())
                        .correspondentBank(request.getCorrespondentBank())
                        .correspondentSwift(request.getCorrespondentSwift())
                        .build();
        
        // 建立收款人資訊
        RemittanceDetails.BeneficiaryInfo beneficiaryInfo = 
                RemittanceDetails.BeneficiaryInfo.builder()
                        .beneficiaryName(request.getBeneficiaryName())
                        .beneficiaryNameLocal(request.getBeneficiaryNameLocal())
                        .beneficiaryAccount(request.getBeneficiaryAccount())
                        .beneficiaryBank(bankInfo)
                        .beneficiaryAddress(request.getBeneficiaryAddress())
                        .beneficiaryCountry(request.getBeneficiaryCountry())
                        .relationshipWithPayer(request.getRelationshipWithPayer())
                        .build();
        
        // 建立用途資訊
        RemittanceDetails.RemittancePurpose purpose = 
                RemittanceDetails.RemittancePurpose.builder()
                        .purposeCode(request.getPurposeCode())
                        .purposeDescription(request.getPurposeDescription())
                        .contractNumber(request.getContractNumber())
                        .invoiceNumber(request.getInvoiceNumber())
                        .contractDate(request.getContractDate())
                        .build();
        
        return RemittanceDetails.builder()
                .theirRefNo(request.getTheirRefNo())
                .amount(request.getAmount())
                .currency(request.getCurrency())
                .exchangeRate(request.getExchangeRate())
                .twdAmount(request.getTwdAmount())
                .fee(request.getFee())
                .totalDeduction(request.getTotalDeduction())
                .beneficiaryInfo(beneficiaryInfo)
                .purpose(purpose)
                .sourceOfFunds(request.getSourceOfFunds())
                .supportingDocuments(request.getSupportingDocuments())
                .valueDate(request.getValueDate())
                .specialInstructions(request.getSpecialInstructions())
                .build();
    }
    
    /**
     * 執行風險評估
     */
    private void performRiskAssessment(CorporateRemittance remittance) {
        log.debug("執行風險評估: remittanceId={}", remittance.getRemittanceId());
        
        // 計算風險分數
        BigDecimal riskScore = calculateRiskScore(remittance);
        
        // 判定風險等級
        String riskLevel = determineRiskLevel(riskScore);
        
        // 設定風險評估結果
        remittance.setRiskAssessment(riskLevel, riskScore);
        
        log.info("風險評估完成: remittanceId={}, riskLevel={}, riskScore={}", 
                remittance.getRemittanceId(), riskLevel, riskScore);
    }
    
    /**
     * 計算風險分數
     */
    private BigDecimal calculateRiskScore(CorporateRemittance remittance) {
        BigDecimal score = BigDecimal.ZERO;
        
        // 金額風險
        if (remittance.getRemittanceDetails().isLargeAmount()) {
            score = score.add(new BigDecimal("30"));
        }
        
        // 收款國家風險
        if (remittance.getRemittanceDetails().getBeneficiaryInfo().isHighRiskCountry()) {
            score = score.add(new BigDecimal("40"));
        }
        
        // 企業行業風險
        if (remittance.getCorporateInfo().requiresAdditionalCompliance()) {
            score = score.add(new BigDecimal("20"));
        }
        
        // 用途風險
        if (remittance.getRemittanceDetails().getPurpose() != null && 
            !remittance.getRemittanceDetails().getPurpose().isTradeRelated()) {
            score = score.add(new BigDecimal("15"));
        }
        
        return score;
    }
    
    /**
     * 判定風險等級
     */
    private String determineRiskLevel(BigDecimal riskScore) {
        if (riskScore.compareTo(new BigDecimal("70")) >= 0) {
            return "HIGH";
        } else if (riskScore.compareTo(new BigDecimal("40")) >= 0) {
            return "MEDIUM";
        } else {
            return "LOW";
        }
    }
    
    /**
     * 啟動審核流程
     */
    private void startApprovalWorkflow(CorporateRemittance remittance) {
        log.debug("啟動審核流程: remittanceId={}", remittance.getRemittanceId());
        
        // 提交審核
        remittance.submitForApproval();
        
        // TODO: 整合工作流引擎
        // workflowService.startApprovalProcess(remittance.getRemittanceId());
        
        // 更新狀態
        repository.save(remittance);
    }
    
    /**
     * 生成解款ID
     */
    private String generateRemittanceId() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        return "CORP" + timestamp.substring(timestamp.length() - 10);
    }
    
    /**
     * 建立回應物件
     */
    private CorporateRemittanceResponse buildResponse(CorporateRemittance remittance) {
        return CorporateRemittanceResponse.builder()
                .remittanceId(remittance.getRemittanceId())
                .unifiedNumber(remittance.getUnifiedNumber().getMasked())
                .companyName(remittance.getCorporateInfo().getCompanyName())
                .amount(remittance.getRemittanceDetails().getAmount())
                .currency(remittance.getRemittanceDetails().getCurrency())
                .status(remittance.getStatus().getDescription())
                .riskLevel(remittance.getRiskLevel())
                .applicationTime(remittance.getApplicationTime())
                .requiresApproval(remittance.requiresManagerApproval())
                .message("企業解款申請提交成功")
                .build();
    }
}