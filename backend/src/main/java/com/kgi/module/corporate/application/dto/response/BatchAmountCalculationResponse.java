package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 批次金額計算回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchAmountCalculationResponse {
    
    /**
     * 計算是否成功
     */
    private Boolean success;
    
    /**
     * 批次計算ID
     */
    private String calculationId;
    
    /**
     * 總筆數
     */
    private Integer totalCount;
    
    /**
     * 成功計算筆數
     */
    private Integer successCount;
    
    /**
     * 失敗計算筆數
     */
    private Integer failedCount;
    
    /**
     * 個別計算結果
     */
    private List<IndividualCalculationResult> calculations;
    
    /**
     * 批次合計資訊
     */
    private BatchSummary batchSummary;
    
    /**
     * 計算時間
     */
    private LocalDateTime calculationTime;
    
    /**
     * 錯誤訊息
     */
    private String errorMessage;
    
    /**
     * 個別匯款計算結果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndividualCalculationResult {
        /**
         * 匯款編號
         */
        private String remittanceId;
        
        /**
         * 計算是否成功
         */
        private Boolean success;
        
        /**
         * 原始金額
         */
        private BigDecimal originalAmount;
        
        /**
         * 原始幣別
         */
        private String originalCurrency;
        
        /**
         * 台幣金額
         */
        private BigDecimal twdAmount;
        
        /**
         * 適用匯率
         */
        private BigDecimal exchangeRate;
        
        /**
         * 手續費明細
         */
        private FeeBreakdown fees;
        
        /**
         * 實際解款金額
         */
        private BigDecimal netAmount;
        
        /**
         * 錯誤訊息
         */
        private String errorMessage;
        
        /**
         * 警告訊息
         */
        private List<String> warnings;
    }
    
    /**
     * 手續費明細
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeBreakdown {
        /**
         * 解款手續費
         */
        private BigDecimal remittanceFee;
        
        /**
         * 郵電費
         */
        private BigDecimal telegraphicFee;
        
        /**
         * 其他費用
         */
        private BigDecimal otherFees;
        
        /**
         * 總手續費
         */
        private BigDecimal totalFees;
        
        /**
         * 手續費明細項目
         */
        private List<FeeItem> feeItems;
    }
    
    /**
     * 手續費項目
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeItem {
        /**
         * 費用類型
         */
        private String feeType;
        
        /**
         * 費用名稱
         */
        private String feeName;
        
        /**
         * 費用金額
         */
        private BigDecimal amount;
        
        /**
         * 費用幣別
         */
        private String currency;
        
        /**
         * 計算方式
         */
        private String calculationMethod;
    }
    
    /**
     * 批次合計資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchSummary {
        /**
         * 總原始金額 (依幣別分組)
         */
        private List<CurrencyAmount> totalOriginalAmounts;
        
        /**
         * 總台幣金額
         */
        private BigDecimal totalTwdAmount;
        
        /**
         * 總手續費
         */
        private BigDecimal totalFees;
        
        /**
         * 總實際解款金額
         */
        private BigDecimal totalNetAmount;
        
        /**
         * 涉及的幣別數
         */
        private Integer currencyCount;
        
        /**
         * 平均匯率 (依幣別)
         */
        private List<CurrencyExchangeRate> averageRates;
        
        /**
         * 優惠折扣總額
         */
        private BigDecimal totalDiscount;
    }
    
    /**
     * 幣別金額
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrencyAmount {
        /**
         * 幣別
         */
        private String currency;
        
        /**
         * 金額
         */
        private BigDecimal amount;
        
        /**
         * 筆數
         */
        private Integer count;
    }
    
    /**
     * 幣別匯率
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrencyExchangeRate {
        /**
         * 幣別
         */
        private String currency;
        
        /**
         * 平均匯率
         */
        private BigDecimal averageRate;
        
        /**
         * 最高匯率
         */
        private BigDecimal highestRate;
        
        /**
         * 最低匯率
         */
        private BigDecimal lowestRate;
    }
}