package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批次金額計算請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchAmountCalculationRequest {
    
    /**
     * 統一編號
     */
    @NotNull(message = "統一編號不能為空")
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    private String companyName;
    
    /**
     * 匯款編號列表
     */
    @NotEmpty(message = "匯款編號列表不能為空")
    @Size(min = 1, max = 100, message = "匯款編號數量應在1-100筆之間")
    private List<String> remittanceIds;
    
    /**
     * 計算選項
     */
    private CalculationOptions calculationOptions;
    
    /**
     * 是否使用即時匯率
     */
    @Builder.Default
    private Boolean useRealTimeRate = true;
    
    /**
     * 是否包含手續費
     */
    @Builder.Default
    private Boolean includeFees = true;
    
    /**
     * 是否包含稅費
     */
    @Builder.Default
    private Boolean includeTax = false;
    
    /**
     * 客戶等級
     */
    @Builder.Default
    private String customerLevel = "STANDARD";
    
    /**
     * 計算選項
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalculationOptions {
        /**
         * 是否計算優惠
         */
        @Builder.Default
        private Boolean calculateDiscount = true;
        
        /**
         * 是否顯示費用明細
         */
        @Builder.Default
        private Boolean showFeeBreakdown = true;
        
        /**
         * 是否驗證限額
         */
        @Builder.Default
        private Boolean validateLimits = true;
        
        /**
         * 匯率類型
         */
        @Builder.Default
        private String rateType = "SPOT";
        
        /**
         * 費用模式
         */
        @Builder.Default
        private String feeMode = "STANDARD";
    }
}