package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業申請提交回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateApplicationSubmissionResponse {
    
    /**
     * 申請編號
     */
    private String applicationId;
    
    /**
     * 申請狀態
     */
    private String status;
    
    /**
     * 統一編號
     */
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    private String companyName;
    
    /**
     * 提交時間
     */
    private LocalDateTime submitTime;
    
    /**
     * 預計處理時間
     */
    private LocalDateTime estimatedProcessTime;
    
    /**
     * 追蹤號碼
     */
    private String trackingNumber;
    
    /**
     * 批次摘要
     */
    private BatchSummary batchSummary;
    
    /**
     * 收據URL
     */
    private String receiptUrl;
    
    /**
     * 下一步驟
     */
    private List<String> nextSteps;
    
    /**
     * 聯絡資訊
     */
    private ContactInfo contactInfo;
    
    /**
     * 是否需要主管核准
     */
    private Boolean requiresManagerApproval;
    
    /**
     * 審核相關資訊
     */
    private ApprovalInfo approvalInfo;
    
    /**
     * 批次摘要資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchSummary {
        /**
         * 申請筆數
         */
        private Integer totalCount;
        
        /**
         * 總金額 (台幣)
         */
        private BigDecimal totalAmount;
        
        /**
         * 總手續費
         */
        private BigDecimal totalFees;
        
        /**
         * 實際解款總額
         */
        private BigDecimal totalNetAmount;
        
        /**
         * 涉及幣別
         */
        private List<String> currencies;
        
        /**
         * 各幣別金額明細
         */
        private List<CurrencyBreakdown> currencyBreakdowns;
    }
    
    /**
     * 幣別明細
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrencyBreakdown {
        /**
         * 幣別
         */
        private String currency;
        
        /**
         * 金額
         */
        private BigDecimal amount;
        
        /**
         * 筆數
         */
        private Integer count;
        
        /**
         * 台幣金額
         */
        private BigDecimal twdAmount;
    }
    
    /**
     * 聯絡資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactInfo {
        /**
         * 客服電話
         */
        private String phoneNumber;
        
        /**
         * 客服信箱
         */
        private String email;
        
        /**
         * 服務時間
         */
        private String serviceHours;
        
        /**
         * 企業專線
         */
        private String corporateHotline;
        
        /**
         * 緊急聯絡方式
         */
        private String emergencyContact;
    }
    
    /**
     * 審核資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApprovalInfo {
        /**
         * 需要審核原因
         */
        private List<String> reasons;
        
        /**
         * 預計審核時間
         */
        private LocalDateTime estimatedApprovalTime;
        
        /**
         * 審核層級
         */
        private String approvalLevel;
        
        /**
         * 審核者角色
         */
        private String approverRole;
        
        /**
         * 審核文件需求
         */
        private List<String> requiredDocuments;
    }
}