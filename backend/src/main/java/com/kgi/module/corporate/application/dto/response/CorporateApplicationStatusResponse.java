package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業申請狀態回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateApplicationStatusResponse {
    
    /**
     * 申請編號
     */
    private String applicationId;
    
    /**
     * 當前狀態
     */
    private String currentStatus;
    
    /**
     * 狀態描述
     */
    private String statusDescription;
    
    /**
     * 統一編號
     */
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    private String companyName;
    
    /**
     * 狀態歷史
     */
    private List<StatusHistory> statusHistory;
    
    /**
     * 處理進度百分比
     */
    private Integer progressPercentage;
    
    /**
     * 預計完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 需要的操作
     */
    private List<RequiredAction> requiredActions;
    
    /**
     * 最新更新
     */
    private LastUpdate lastUpdate;
    
    /**
     * 審核資訊
     */
    private ApprovalDetails approvalDetails;
    
    /**
     * 狀態歷史記錄
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusHistory {
        /**
         * 狀態
         */
        private String status;
        
        /**
         * 時間戳記
         */
        private LocalDateTime timestamp;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 處理者
         */
        private String processedBy;
        
        /**
         * 處理者角色
         */
        private String processorRole;
        
        /**
         * 備註
         */
        private String remarks;
    }
    
    /**
     * 需要的操作
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RequiredAction {
        /**
         * 操作類型
         */
        private String action;
        
        /**
         * 操作描述
         */
        private String description;
        
        /**
         * 到期日
         */
        private LocalDateTime dueDate;
        
        /**
         * 優先級
         */
        private String priority;
        
        /**
         * 是否必要
         */
        private Boolean required;
        
        /**
         * 操作連結
         */
        private String actionUrl;
    }
    
    /**
     * 最新更新
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LastUpdate {
        /**
         * 時間戳記
         */
        private LocalDateTime timestamp;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 更新者
         */
        private String updatedBy;
        
        /**
         * 更新類型
         */
        private String updateType;
        
        /**
         * 變更內容
         */
        private String changes;
    }
    
    /**
     * 審核詳細資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApprovalDetails {
        /**
         * 是否需要審核
         */
        private Boolean requiresApproval;
        
        /**
         * 審核狀態
         */
        private String approvalStatus;
        
        /**
         * 審核者
         */
        private String approver;
        
        /**
         * 審核時間
         */
        private LocalDateTime approvalTime;
        
        /**
         * 審核意見
         */
        private String approvalComments;
        
        /**
         * 審核層級
         */
        private String approvalLevel;
        
        /**
         * 下一審核者
         */
        private String nextApprover;
    }
}