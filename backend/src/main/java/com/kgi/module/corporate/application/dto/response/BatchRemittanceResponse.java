package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 批次法人解款回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchRemittanceResponse {
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 批次名稱
     */
    private String batchName;
    
    /**
     * 批次狀態
     */
    private String status;
    
    /**
     * 提交時間
     */
    private LocalDateTime submissionTime;
    
    /**
     * 完成時間
     */
    private LocalDateTime completionTime;
    
    /**
     * 總筆數
     */
    private Integer totalCount;
    
    /**
     * 處理完成筆數
     */
    private Integer processedCount;
    
    /**
     * 成功筆數
     */
    private Integer successCount;
    
    /**
     * 失敗筆數
     */
    private Integer failedCount;
    
    /**
     * 處理進度百分比
     */
    private Double progressPercentage;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 總金額
     */
    private BigDecimal totalAmount;
    
    /**
     * 成功處理金額
     */
    private BigDecimal successAmount;
    
    /**
     * 預估完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 提交者資訊
     */
    private String submitterInfo;
    
    /**
     * 成功項目列表
     */
    private List<BatchItemResult> successItems;
    
    /**
     * 失敗項目列表
     */
    private List<BatchItemResult> failedItems;
    
    /**
     * 批次統計資訊
     */
    private BatchStatistics statistics;
    
    /**
     * 操作日誌
     */
    private List<String> operationLogs;
    
    /**
     * 批次項目結果內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchItemResult {
        
        /**
         * 項目索引
         */
        private Integer index;
        
        /**
         * 解款編號
         */
        private String remittanceId;
        
        /**
         * 統一編號
         */
        private String unifiedNumber;
        
        /**
         * 企業名稱
         */
        private String companyName;
        
        /**
         * 金額
         */
        private BigDecimal amount;
        
        /**
         * 幣別
         */
        private String currency;
        
        /**
         * 處理狀態
         */
        private String status;
        
        /**
         * 處理時間
         */
        private LocalDateTime processTime;
        
        /**
         * 錯誤訊息 (如有)
         */
        private String errorMessage;
        
        /**
         * 錯誤代碼 (如有)
         */
        private String errorCode;
    }
    
    /**
     * 批次統計資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchStatistics {
        
        /**
         * 平均處理時間 (秒)
         */
        private Double averageProcessingTime;
        
        /**
         * 最快處理時間 (秒)
         */
        private Double fastestProcessingTime;
        
        /**
         * 最慢處理時間 (秒)
         */
        private Double slowestProcessingTime;
        
        /**
         * 總處理時間 (秒)
         */
        private Long totalProcessingTime;
        
        /**
         * 按幣別統計
         */
        private Map<String, CurrencyStatistics> currencyStatistics;
        
        /**
         * 按狀態統計
         */
        private Map<String, Integer> statusStatistics;
        
        /**
         * 錯誤類型統計
         */
        private Map<String, Integer> errorTypeStatistics;
    }
    
    /**
     * 幣別統計內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CurrencyStatistics {
        
        /**
         * 幣別
         */
        private String currency;
        
        /**
         * 筆數
         */
        private Integer count;
        
        /**
         * 總金額
         */
        private BigDecimal totalAmount;
        
        /**
         * 平均金額
         */
        private BigDecimal averageAmount;
        
        /**
         * 最大金額
         */
        private BigDecimal maxAmount;
        
        /**
         * 最小金額
         */
        private BigDecimal minAmount;
    }
}