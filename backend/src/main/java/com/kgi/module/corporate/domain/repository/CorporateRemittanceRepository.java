package com.kgi.module.corporate.domain.repository;

import com.kgi.core.domain.valueobject.UnifiedNumber;
import com.kgi.module.corporate.domain.model.CorporateRemittance;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 企業解款Repository接口
 * 定義企業解款資料存取的規範
 */
public interface CorporateRemittanceRepository {
    
    /**
     * 儲存企業解款記錄
     */
    CorporateRemittance save(CorporateRemittance remittance);
    
    /**
     * 根據解款ID查詢
     */
    Optional<CorporateRemittance> findByRemittanceId(String remittanceId);
    
    /**
     * 根據統一編號查詢企業的解款記錄
     */
    List<CorporateRemittance> findByUnifiedNumber(UnifiedNumber unifiedNumber);
    
    /**
     * 根據統一編號和狀態查詢
     */
    List<CorporateRemittance> findByUnifiedNumberAndStatus(
            UnifiedNumber unifiedNumber, 
            CorporateRemittance.ProcessingStatus status);
    
    /**
     * 根據狀態查詢解款記錄
     */
    List<CorporateRemittance> findByStatus(CorporateRemittance.ProcessingStatus status);
    
    /**
     * 分頁查詢企業解款記錄
     */
    List<CorporateRemittance> findByUnifiedNumberWithPagination(
            UnifiedNumber unifiedNumber, 
            int page, 
            int size);
    
    /**
     * 查詢待審核的解款記錄
     */
    List<CorporateRemittance> findPendingApprovals();
    
    /**
     * 查詢高風險解款記錄
     */
    List<CorporateRemittance> findHighRiskRemittances();
    
    /**
     * 查詢逾期未處理的解款記錄
     */
    List<CorporateRemittance> findOverdueRemittances();
    
    /**
     * 查詢指定時間範圍內的解款記錄
     */
    List<CorporateRemittance> findByDateRange(LocalDate startDate, LocalDate endDate);
    
    /**
     * 查詢指定統一編號在指定時間範圍內的解款記錄
     */
    List<CorporateRemittance> findByUnifiedNumberAndDateRange(
            UnifiedNumber unifiedNumber, 
            LocalDate startDate, 
            LocalDate endDate);
    
    /**
     * 統計企業在指定期間的解款總金額
     */
    BigDecimal sumAmountByUnifiedNumberAndDateRange(
            UnifiedNumber unifiedNumber, 
            LocalDate startDate, 
            LocalDate endDate,
            String currency);
    
    /**
     * 統計企業在指定期間的解款筆數
     */
    Long countByUnifiedNumberAndDateRange(
            UnifiedNumber unifiedNumber, 
            LocalDate startDate, 
            LocalDate endDate);
    
    /**
     * 查詢企業的日限額使用情況
     */
    BigDecimal getDailyUsedAmount(UnifiedNumber unifiedNumber, String currency, LocalDate date);
    
    /**
     * 查詢企業的月限額使用情況
     */
    BigDecimal getMonthlyUsedAmount(UnifiedNumber unifiedNumber, String currency, int year, int month);
    
    /**
     * 查詢企業的年限額使用情況
     */
    BigDecimal getYearlyUsedAmount(UnifiedNumber unifiedNumber, String currency, int year);
    
    /**
     * 查詢需要主管審核的解款記錄
     */
    List<CorporateRemittance> findRequiringManagerApproval();
    
    /**
     * 查詢指定審核人員處理的解款記錄
     */
    List<CorporateRemittance> findByApprover(String approver);
    
    /**
     * 查詢批次處理相關的解款記錄
     */
    List<CorporateRemittance> findForBatchProcessing(
            CorporateRemittance.ProcessingStatus status, 
            int batchSize);
    
    /**
     * 根據風險等級查詢解款記錄
     */
    List<CorporateRemittance> findByRiskLevel(String riskLevel);
    
    /**
     * 查詢金額超過指定閾值的解款記錄
     */
    List<CorporateRemittance> findByAmountGreaterThan(BigDecimal threshold, String currency);
    
    /**
     * 查詢指定收款國家的解款記錄
     */
    List<CorporateRemittance> findByBeneficiaryCountry(String country);
    
    /**
     * 統計指定時間範圍內各狀態的解款筆數
     */
    java.util.Map<CorporateRemittance.ProcessingStatus, Long> countByStatusAndDateRange(
            LocalDate startDate, LocalDate endDate);
    
    /**
     * 統計指定時間範圍內各幣別的解款金額
     */
    java.util.Map<String, BigDecimal> sumAmountByCurrencyAndDateRange(
            LocalDate startDate, LocalDate endDate);
    
    /**
     * 查詢平均處理時間
     */
    Double getAverageProcessingHours(LocalDate startDate, LocalDate endDate);
    
    /**
     * 查詢SLA達成率
     */
    Double getSlaComplianceRate(LocalDate startDate, LocalDate endDate);
    
    /**
     * 檢查解款ID是否存在
     */
    boolean existsByRemittanceId(String remittanceId);
    
    /**
     * 檢查企業是否有進行中的解款
     */
    boolean hasOngoingRemittance(UnifiedNumber unifiedNumber);
    
    /**
     * 刪除解款記錄（軟刪除）
     */
    void deleteByRemittanceId(String remittanceId);
    
    /**
     * 批次更新解款狀態
     */
    int batchUpdateStatus(
            List<String> remittanceIds, 
            CorporateRemittance.ProcessingStatus newStatus);
    
    /**
     * 查詢企業解款統計資訊
     */
    CorporateRemittanceStatistics getStatisticsByUnifiedNumber(
            UnifiedNumber unifiedNumber, 
            LocalDate startDate, 
            LocalDate endDate);
    
    /**
     * 企業解款統計資訊
     */
    interface CorporateRemittanceStatistics {
        Long getTotalCount();
        BigDecimal getTotalAmount();
        Long getCompletedCount();
        Long getPendingCount();
        Long getRejectedCount();
        Double getSuccessRate();
        Double getAverageAmount();
        Long getAverageProcessingHours();
    }
}