package com.kgi.module.corporate.application.dto.request;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 企業解款申請請求DTO
 * 接收前端企業解款申請的資料
 */
@Data
@Builder
public class CorporateRemittanceRequest {
    
    // 企業基本資訊
    private String unifiedNumber;
    
    private String companyName;
    
    private String companyNameEnglish;
    
    private String businessType;
    
    private String industryCode;
    
    // 聯絡人資訊
    private String contactPersonName;
    
    private String contactPersonTitle;
    
    private String contactPersonPhone;
    
    private String contactPersonEmail;
    
    private String contactPersonId;
    
    // 企業地址
    private String companyAddress;
    
    private String companyTelephone;
    
    private String companyEmail;
    
    // 解款交易資訊
    private String theirRefNo;
    
    private BigDecimal amount;
    
    private String currency;
    
    private BigDecimal exchangeRate;
    
    private BigDecimal twdAmount;
    
    private BigDecimal fee;
    
    private BigDecimal totalDeduction;
    
    // 收款人資訊
    private String beneficiaryName;
    
    private String beneficiaryNameLocal;
    
    private String beneficiaryAccount;
    
    private String beneficiaryBankName;
    
    private String beneficiaryBankCode;
    
    private String beneficiarySwiftCode;
    
    private String beneficiaryBankAddress;
    
    private String beneficiaryAddress;
    
    private String beneficiaryCountry;
    
    private String correspondentBank;
    
    private String correspondentSwift;
    
    private String relationshipWithPayer;
    
    // 解款用途資訊
    private String purposeCode;
    
    private String purposeDescription;
    
    private String contractNumber;
    
    private String invoiceNumber;
    
    private LocalDate contractDate;
    
    private String sourceOfFunds;
    
    private String supportingDocuments;
    
    private LocalDate valueDate;
    
    private String specialInstructions;
    
    // 內部處理欄位
    private String submittedBy;
    
    private String applicationChannel;
    
    private String ipAddress;
    
    /**
     * 驗證企業基本資訊完整性
     */
    public boolean isValidCorporateInfo() {
        return unifiedNumber != null && !unifiedNumber.trim().isEmpty() &&
               companyName != null && !companyName.trim().isEmpty() &&
               contactPersonName != null && !contactPersonName.trim().isEmpty() &&
               contactPersonPhone != null && !contactPersonPhone.trim().isEmpty() &&
               contactPersonEmail != null && !contactPersonEmail.trim().isEmpty() &&
               companyAddress != null && !companyAddress.trim().isEmpty();
    }
    
    /**
     * 驗證解款交易資訊完整性
     */
    public boolean isValidTransactionInfo() {
        return theirRefNo != null && !theirRefNo.trim().isEmpty() &&
               amount != null && amount.compareTo(BigDecimal.ZERO) > 0 &&
               currency != null && !currency.trim().isEmpty();
    }
    
    /**
     * 驗證收款人資訊完整性
     */
    public boolean isValidBeneficiaryInfo() {
        return beneficiaryName != null && !beneficiaryName.trim().isEmpty() &&
               beneficiaryAccount != null && !beneficiaryAccount.trim().isEmpty() &&
               beneficiarySwiftCode != null && !beneficiarySwiftCode.trim().isEmpty() &&
               beneficiaryCountry != null && !beneficiaryCountry.trim().isEmpty();
    }
    
    /**
     * 驗證用途資訊完整性
     */
    public boolean isValidPurposeInfo() {
        return purposeCode != null && !purposeCode.trim().isEmpty() &&
               purposeDescription != null && !purposeDescription.trim().isEmpty() &&
               sourceOfFunds != null && !sourceOfFunds.trim().isEmpty();
    }
    
    /**
     * 檢查是否為大額交易
     */
    public boolean isLargeAmount() {
        if (amount == null) return false;
        
        switch (currency.toUpperCase()) {
            case "USD":
                return amount.compareTo(new BigDecimal("500000")) > 0;
            case "EUR":
                return amount.compareTo(new BigDecimal("450000")) > 0;
            case "JPY":
                return amount.compareTo(new BigDecimal("55000000")) > 0;
            default:
                return amount.compareTo(new BigDecimal("500000")) > 0;
        }
    }
}