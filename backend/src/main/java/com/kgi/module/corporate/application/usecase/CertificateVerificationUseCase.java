package com.kgi.module.corporate.application.usecase;

import com.kgi.module.corporate.application.dto.request.*;
import com.kgi.module.corporate.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 憑證驗證用例
 * 處理工商憑證相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CertificateVerificationUseCase {
    
    /**
     * 驗證企業資料
     */
    public CompanyInfoVerificationResponse verifyCompanyInfo(CompanyInfoVerificationRequest request) {
        log.info("驗證企業資料: unifiedNumber={}", 
                maskUnifiedNumber(request.getUnifiedNumber()));
        
        // TODO: 實作企業資料驗證邏輯
        return CompanyInfoVerificationResponse.builder()
                .success(true)
                .build();
    }
    
    /**
     * 驗證工商憑證
     */
    public CertificateVerificationResponse verifyCertificate(CertificateVerificationRequest request) {
        log.info("驗證工商憑證: unifiedNumber={}", 
                maskUnifiedNumber(request.getUnifiedNumber()));
        
        // TODO: 實作工商憑證驗證邏輯
        return CertificateVerificationResponse.builder()
                .success(true)
                .build();
    }
    
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return "****";
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
}