package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 企業資訊驗證請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfoVerificationRequest {
    
    /**
     * 統一編號
     */
    @NotNull(message = "統一編號不能為空")
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    @NotNull(message = "企業名稱不能為空")
    @Size(min = 1, max = 100, message = "企業名稱長度應在1-100字元之間")
    private String companyName;
    
    /**
     * 企業英文名稱
     */
    @Size(max = 100, message = "企業英文名稱長度不能超過100字元")
    private String companyNameEn;
    
    /**
     * 負責人姓名
     */
    @NotNull(message = "負責人姓名不能為空")
    @Size(min = 1, max = 50, message = "負責人姓名長度應在1-50字元之間")
    private String representativeName;
    
    /**
     * 負責人身分證號
     */
    @Pattern(regexp = "^[A-Z][12]\\d{8}$", message = "身分證號格式錯誤")
    private String representativeIdCard;
    
    /**
     * 企業地址
     */
    @Size(max = 200, message = "企業地址長度不能超過200字元")
    private String address;
    
    /**
     * 驗證類型
     */
    @Builder.Default
    private String verificationType = "BASIC";
    
    /**
     * 是否驗證營運狀態
     */
    @Builder.Default
    private Boolean checkBusinessStatus = true;
    
    /**
     * 是否進行風險評估
     */
    @Builder.Default
    private Boolean performRiskAssessment = false;
}