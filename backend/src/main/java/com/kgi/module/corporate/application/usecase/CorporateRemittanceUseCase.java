package com.kgi.module.corporate.application.usecase;

import com.kgi.module.corporate.application.dto.request.*;
import com.kgi.module.corporate.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業匯款用例
 * 處理企業匯款相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CorporateRemittanceUseCase {
    
    /**
     * 搜尋企業匯款記錄
     */
    public CorporateRemittanceSearchResponse searchRemittances(CorporateRemittanceSearchRequest request) {
        log.info("搜尋企業匯款記錄: unifiedNumber={}", 
                maskUnifiedNumber(request.getUnifiedNumber()));
        
        // TODO: 實作企業匯款搜尋邏輯
        return CorporateRemittanceSearchResponse.builder()
                .remittances(List.of())
                .totalCount(0L)
                .currentPage(request.getPage())
                .pageSize(request.getPageSize())
                .totalPages(0)
                .hasNext(false)
                .hasPrevious(false)
                .searchTime(LocalDateTime.now())
                .summary(CorporateRemittanceSearchResponse.CorporateSearchSummary.builder()
                        .availableCount(0)
                        .processedCount(0)
                        .expiredCount(0)
                        .selectedCount(0)
                        .build())
                .build();
    }
    
    /**
     * 取得匯款詳情
     */
    public CorporateRemittanceDetailResponse getRemittanceDetail(String remittanceId, String unifiedNumber) {
        log.info("取得企業匯款詳情: remittanceId={}, unifiedNumber={}", 
                remittanceId, maskUnifiedNumber(unifiedNumber));
        
        // TODO: 實作匯款詳情查詢邏輯
        return CorporateRemittanceDetailResponse.builder()
                .remittanceId(remittanceId)
                .processingStatus("AVAILABLE")
                .statusDescription("可申請解款")
                .canApplyRemittance(true)
                .build();
    }
    
    /**
     * 批次金額計算
     */
    public BatchAmountCalculationResponse calculateBatchAmount(BatchAmountCalculationRequest request) {
        log.info("批次金額計算: unifiedNumber={}, count={}", 
                maskUnifiedNumber(request.getUnifiedNumber()), 
                request.getRemittanceIds() != null ? request.getRemittanceIds().size() : 0);
        
        // TODO: 實作批次金額計算邏輯
        return BatchAmountCalculationResponse.builder()
                .success(true)
                .calculationId("CALC" + System.currentTimeMillis())
                .totalCount(request.getRemittanceIds() != null ? request.getRemittanceIds().size() : 0)
                .successCount(0)
                .failedCount(0)
                .calculations(List.of())
                .calculationTime(LocalDateTime.now())
                .build();
    }
    
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return "****";
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
}