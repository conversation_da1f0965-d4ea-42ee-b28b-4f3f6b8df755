package com.kgi.module.corporate.infrastructure.repository;

import com.kgi.module.corporate.infrastructure.entity.CorporateRemittanceEntity;
import com.kgi.module.corporate.domain.repository.CorporateRemittanceRepository.CorporateRemittanceStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 企業解款JPA Repository
 * 提供資料庫層的查詢操作
 */
@Repository
public interface CorporateRemittanceJpaRepository extends JpaRepository<CorporateRemittanceEntity, Long> {
    
    /**
     * 根據解款ID查詢（排除已刪除）
     */
    Optional<CorporateRemittanceEntity> findByRemittanceIdAndIsDeletedFalse(String remittanceId);
    
    /**
     * 根據統一編號查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByUnifiedNumberAndIsDeletedFalse(String unifiedNumber);
    
    /**
     * 根據統一編號和狀態查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByUnifiedNumberAndStatusAndIsDeletedFalse(
            String unifiedNumber, String status);
    
    /**
     * 根據狀態查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByStatusAndIsDeletedFalse(String status);
    
    /**
     * 根據風險等級查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByRiskLevelAndIsDeletedFalse(String riskLevel);
    
    /**
     * 根據審核人員查詢（排除已刪除）
     */
    @Query("SELECT c FROM CorporateRemittanceEntity c WHERE c.approver = :approver AND c.isDeleted = false")
    List<CorporateRemittanceEntity> findByApproverAndIsDeletedFalse(@Param("approver") String approver);
    
    /**
     * 根據時間範圍查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByApplicationTimeBetweenAndIsDeletedFalse(
            LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 根據統一編號和時間範圍查詢（排除已刪除）
     */
    List<CorporateRemittanceEntity> findByUnifiedNumberAndApplicationTimeBetweenAndIsDeletedFalse(
            String unifiedNumber, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 分頁查詢企業解款記錄
     */
    @Query(value = "SELECT * FROM corporate_remittance WHERE unified_number = :unifiedNumber " +
                   "AND is_deleted = false ORDER BY application_time DESC LIMIT :limit OFFSET :offset",
           nativeQuery = true)
    List<CorporateRemittanceEntity> findByUnifiedNumberWithPagination(
            @Param("unifiedNumber") String unifiedNumber,
            @Param("offset") int offset,
            @Param("limit") int limit);
    
    /**
     * 查詢逾期未處理的解款記錄
     */
    @Query("SELECT c FROM CorporateRemittanceEntity c WHERE c.applicationTime < :overdueThreshold " +
           "AND c.status IN ('PENDING', 'UNDER_REVIEW', 'APPROVED', 'PROCESSING') " +
           "AND c.isDeleted = false")
    List<CorporateRemittanceEntity> findOverdueRemittances(@Param("overdueThreshold") LocalDateTime overdueThreshold);
    
    /**
     * 統計指定期間的解款總金額
     */
    @Query("SELECT COALESCE(SUM(c.amount), 0) FROM CorporateRemittanceEntity c " +
           "WHERE c.unifiedNumber = :unifiedNumber " +
           "AND c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.currency = :currency " +
           "AND c.status IN ('COMPLETED') " +
           "AND c.isDeleted = false")
    BigDecimal sumAmountByUnifiedNumberAndDateRangeAndCurrency(
            @Param("unifiedNumber") String unifiedNumber,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("currency") String currency);
    
    /**
     * 統計指定期間的解款筆數
     */
    Long countByUnifiedNumberAndApplicationTimeBetweenAndIsDeletedFalse(
            String unifiedNumber, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查詢需要主管審核的解款記錄
     */
    @Query("SELECT c FROM CorporateRemittanceEntity c WHERE " +
           "(c.riskLevel = 'HIGH' OR c.amount > 500000) " +
           "AND c.status = 'UNDER_REVIEW' " +
           "AND c.isDeleted = false")
    List<CorporateRemittanceEntity> findRequiringManagerApproval();
    
    /**
     * 查詢批次處理相關的解款記錄
     */
    @Query(value = "SELECT * FROM corporate_remittance WHERE status = :status " +
                   "AND is_deleted = false ORDER BY application_time ASC LIMIT :batchSize",
           nativeQuery = true)
    List<CorporateRemittanceEntity> findForBatchProcessing(
            @Param("status") String status,
            @Param("batchSize") int batchSize);
    
    /**
     * 根據金額和幣別查詢大額交易
     */
    @Query("SELECT c FROM CorporateRemittanceEntity c WHERE c.amount > :threshold " +
           "AND c.currency = :currency AND c.isDeleted = false")
    List<CorporateRemittanceEntity> findByAmountGreaterThanAndCurrencyAndIsDeletedFalse(
            @Param("threshold") BigDecimal threshold,
            @Param("currency") String currency);
    
    /**
     * 根據收款國家查詢
     */
    @Query("SELECT c FROM CorporateRemittanceEntity c WHERE c.beneficiaryCountry = :country " +
           "AND c.isDeleted = false")
    List<CorporateRemittanceEntity> findByBeneficiaryCountryAndIsDeletedFalse(@Param("country") String country);
    
    /**
     * 統計各狀態的解款筆數
     */
    @Query("SELECT c.status, COUNT(c) FROM CorporateRemittanceEntity c " +
           "WHERE c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.isDeleted = false GROUP BY c.status")
    List<Object[]> countByStatusAndDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 統計各幣別的解款金額
     */
    @Query("SELECT c.currency, COALESCE(SUM(c.amount), 0) FROM CorporateRemittanceEntity c " +
           "WHERE c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.status = 'COMPLETED' " +
           "AND c.isDeleted = false GROUP BY c.currency")
    List<Object[]> sumAmountByCurrencyAndDateRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查詢平均處理時間（小時）
     */
    @Query("SELECT AVG(TIMESTAMPDIFF(HOUR, c.applicationTime, c.completionTime)) " +
           "FROM CorporateRemittanceEntity c " +
           "WHERE c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.status = 'COMPLETED' " +
           "AND c.completionTime IS NOT NULL " +
           "AND c.isDeleted = false")
    Double getAverageProcessingHours(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查詢SLA達成率
     */
    @Query("SELECT (COUNT(CASE WHEN TIMESTAMPDIFF(HOUR, c.applicationTime, c.completionTime) <= 4 THEN 1 END) * 100.0 / COUNT(*)) " +
           "FROM CorporateRemittanceEntity c " +
           "WHERE c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.status = 'COMPLETED' " +
           "AND c.completionTime IS NOT NULL " +
           "AND c.isDeleted = false")
    Double getSlaComplianceRate(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    /**
     * 檢查解款ID是否存在
     */
    boolean existsByRemittanceIdAndIsDeletedFalse(String remittanceId);
    
    /**
     * 檢查企業是否有進行中的解款
     */
    boolean existsByUnifiedNumberAndStatusInAndIsDeletedFalse(
            String unifiedNumber, List<String> statuses);
    
    /**
     * 批次更新解款狀態
     */
    @Modifying
    @Query("UPDATE CorporateRemittanceEntity c SET c.status = :newStatus, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.remittanceId IN :remittanceIds")
    int batchUpdateStatus(
            @Param("remittanceIds") List<String> remittanceIds,
            @Param("newStatus") String newStatus);
    
    /**
     * 查詢企業解款統計資訊
     */
    @Query("SELECT " +
           "COUNT(c) as totalCount, " +
           "COALESCE(SUM(c.amount), 0) as totalAmount, " +
           "COUNT(CASE WHEN c.status = 'COMPLETED' THEN 1 END) as completedCount, " +
           "COUNT(CASE WHEN c.status IN ('PENDING', 'UNDER_REVIEW', 'APPROVED', 'PROCESSING') THEN 1 END) as pendingCount, " +
           "COUNT(CASE WHEN c.status = 'REJECTED' THEN 1 END) as rejectedCount, " +
           "(COUNT(CASE WHEN c.status = 'COMPLETED' THEN 1 END) * 100.0 / COUNT(*)) as successRate, " +
           "AVG(c.amount) as averageAmount, " +
           "AVG(CASE WHEN c.completionTime IS NOT NULL THEN TIMESTAMPDIFF(HOUR, c.applicationTime, c.completionTime) END) as averageProcessingHours " +
           "FROM CorporateRemittanceEntity c " +
           "WHERE c.unifiedNumber = :unifiedNumber " +
           "AND c.applicationTime BETWEEN :startTime AND :endTime " +
           "AND c.isDeleted = false")
    CorporateRemittanceStatistics getStatisticsByUnifiedNumber(
            @Param("unifiedNumber") String unifiedNumber,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
}