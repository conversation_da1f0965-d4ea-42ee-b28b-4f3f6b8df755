package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 企業資訊驗證回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyInfoVerificationResponse {
    
    /**
     * 驗證是否成功
     */
    private Boolean success;
    
    /**
     * 驗證時間
     */
    private LocalDateTime verificationTime;
    
    /**
     * 基本企業資訊
     */
    private CompanyBasicInfo basicInfo;
    
    /**
     * 營運狀態資訊
     */
    private BusinessStatusInfo businessStatus;
    
    /**
     * 風險評估結果
     */
    private RiskAssessmentInfo riskAssessment;
    
    /**
     * 授權代表人驗證結果
     */
    private RepresentativeInfo representativeInfo;
    
    /**
     * 驗證結果摘要
     */
    private VerificationSummary verificationSummary;
    
    /**
     * 基本企業資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyBasicInfo {
        
        /**
         * 統一編號
         */
        private String unifiedNumber;
        
        /**
         * 企業名稱
         */
        private String companyName;
        
        /**
         * 企業英文名稱
         */
        private String companyNameEn;
        
        /**
         * 負責人姓名
         */
        private String representativeName;
        
        /**
         * 成立日期
         */
        private LocalDate establishDate;
        
        /**
         * 實收資本額
         */
        private Long capital;
        
        /**
         * 企業地址
         */
        private String address;
        
        /**
         * 營業項目
         */
        private String businessScope;
        
        /**
         * 最後更新日期
         */
        private LocalDate lastUpdateDate;
    }
    
    /**
     * 營運狀態資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessStatusInfo {
        
        /**
         * 營運狀態
         */
        private String status;
        
        /**
         * 狀態描述
         */
        private String statusDescription;
        
        /**
         * 是否可進行交易
         */
        private Boolean canTrade;
        
        /**
         * 狀態變更原因
         */
        private String reason;
        
        /**
         * 是否為正常營運
         */
        private Boolean active;
        
        /**
         * 狀態檢查時間
         */
        private LocalDateTime statusCheckTime;
    }
    
    /**
     * 風險評估資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessmentInfo {
        
        /**
         * 風險等級
         */
        private String riskLevel;
        
        /**
         * 風險分數
         */
        private Integer riskScore;
        
        /**
         * 信用評級
         */
        private String creditRating;
        
        /**
         * 評估時間
         */
        private LocalDateTime assessmentTime;
        
        /**
         * 風險因子列表
         */
        private List<RiskFactor> riskFactors;
        
        /**
         * 建議措施
         */
        private List<String> recommendations;
        
        /**
         * 是否為高風險客戶
         */
        private Boolean highRisk;
    }
    
    /**
     * 授權代表人資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RepresentativeInfo {
        
        /**
         * 代表人姓名
         */
        private String representativeName;
        
        /**
         * 身分證號 (遮罩)
         */
        private String idCard;
        
        /**
         * 是否為法定代表人
         */
        private Boolean isOfficialRepresentative;
        
        /**
         * 是否有授權
         */
        private Boolean authorized;
        
        /**
         * 授權類型
         */
        private String authorizationType;
        
        /**
         * 授權等級
         */
        private String authorityLevel;
        
        /**
         * 授權文件
         */
        private String authorizationDocument;
        
        /**
         * 驗證時間
         */
        private LocalDateTime validationTime;
    }
    
    /**
     * 驗證結果摘要內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationSummary {
        
        /**
         * 統一編號驗證
         */
        private Boolean unifiedNumberValid;
        
        /**
         * 企業資料驗證
         */
        private Boolean companyDataValid;
        
        /**
         * 營運狀態驗證
         */
        private Boolean businessStatusValid;
        
        /**
         * 授權驗證
         */
        private Boolean authorizationValid;
        
        /**
         * 整體驗證結果
         */
        private Boolean overallValid;
        
        /**
         * 驗證失敗原因
         */
        private List<String> failureReasons;
        
        /**
         * 警告訊息
         */
        private List<String> warnings;
        
        /**
         * 下一步建議
         */
        private List<String> nextSteps;
    }
    
    /**
     * 風險因子內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        
        /**
         * 因子類型
         */
        private String type;
        
        /**
         * 風險等級
         */
        private String level;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 影響分數
         */
        private Integer impactScore;
        
        /**
         * 建議措施
         */
        private String recommendation;
    }
}