package com.kgi.module.corporate.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;

/**
 * 工商憑證驗證服務
 * 處理企業工商憑證的驗證與管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessCertificateService {
    
    /**
     * 驗證工商憑證
     */
    public Map<String, Object> verifyCertificate(String certificateData, String pin) {
        log.info("開始驗證工商憑證");
        
        try {
            // 1. 解析憑證資料
            Map<String, Object> certificateInfo = parseCertificateData(certificateData);
            
            // 2. 驗證PIN碼
            if (!validatePin(certificateData, pin)) {
                throw new BusinessException("INVALID_PIN", "PIN碼驗證失敗");
            }
            
            // 3. 檢查憑證有效性
            if (!isCertificateValid(certificateInfo)) {
                throw new BusinessException("INVALID_CERTIFICATE", "憑證已過期或無效");
            }
            
            // 4. 驗證憑證簽章
            if (!verifySignature(certificateData)) {
                throw new BusinessException("SIGNATURE_VERIFICATION_FAILED", "憑證簽章驗證失敗");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("certificateInfo", certificateInfo);
            result.put("verificationTime", LocalDateTime.now());
            result.put("validUntil", certificateInfo.get("validUntil"));
            
            log.info("工商憑證驗證成功: unifiedNumber={}", 
                    maskUnifiedNumber((String) certificateInfo.get("unifiedNumber")));
            
            return result;
            
        } catch (BusinessException e) {
            log.error("工商憑證驗證失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("工商憑證驗證異常: {}", e.getMessage(), e);
            throw new BusinessException("CERTIFICATE_VERIFICATION_ERROR", "憑證驗證異常: " + e.getMessage());
        }
    }
    
    /**
     * 讀取智慧卡憑證
     */
    public Map<String, Object> readSmartCardCertificate(String readerName) {
        log.info("讀取智慧卡憑證: reader={}", readerName);
        
        try {
            // 1. 檢查讀卡機可用性
            if (!isReaderAvailable(readerName)) {
                throw new BusinessException("READER_NOT_AVAILABLE", "讀卡機不可用");
            }
            
            // 2. 檢查卡片存在
            if (!isCardPresent(readerName)) {
                throw new BusinessException("CARD_NOT_PRESENT", "未偵測到卡片");
            }
            
            // 3. 讀取憑證資料
            String certificateData = readCertificateFromCard(readerName);
            
            // 4. 解析憑證基本資訊
            Map<String, Object> certificateInfo = parseCertificateData(certificateData);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("certificateData", certificateData);
            result.put("certificateInfo", certificateInfo);
            result.put("readerName", readerName);
            result.put("readTime", LocalDateTime.now());
            
            log.info("智慧卡憑證讀取成功: unifiedNumber={}", 
                    maskUnifiedNumber((String) certificateInfo.get("unifiedNumber")));
            
            return result;
            
        } catch (BusinessException e) {
            log.error("智慧卡憑證讀取失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("智慧卡憑證讀取異常: {}", e.getMessage(), e);
            throw new BusinessException("SMART_CARD_READ_ERROR", "智慧卡讀取異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢可用的讀卡機列表
     */
    public Map<String, Object> getAvailableReaders() {
        log.info("查詢可用讀卡機列表");
        
        try {
            java.util.List<Map<String, Object>> readers = new java.util.ArrayList<>();
            
            // 模擬讀卡機列表
            String[] readerNames = {
                "Microsoft Usbccid Smartcard Reader (WUDF) 0",
                "ACS ACR38U-CCID 0",
                "CASTLES EZ100PU 0",
                "Gemalto PC Twin Reader 0"
            };
            
            for (int i = 0; i < readerNames.length; i++) {
                Map<String, Object> reader = new HashMap<>();
                reader.put("name", readerNames[i]);
                reader.put("index", i);
                reader.put("available", true);
                reader.put("cardPresent", i == 0); // 假設第一個讀卡機有卡片
                reader.put("type", getReaderType(readerNames[i]));
                readers.add(reader);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("readers", readers);
            result.put("count", readers.size());
            result.put("scanTime", LocalDateTime.now());
            
            log.info("查詢到 {} 個可用讀卡機", readers.size());
            return result;
            
        } catch (Exception e) {
            log.error("查詢讀卡機失敗: {}", e.getMessage(), e);
            throw new BusinessException("READER_SCAN_ERROR", "讀卡機掃描失敗: " + e.getMessage());
        }
    }
    
    /**
     * 產生數位簽章
     */
    public Map<String, Object> generateDigitalSignature(String data, String certificateData, String pin) {
        log.info("產生數位簽章");
        
        try {
            // 1. 驗證憑證和PIN
            Map<String, Object> certVerification = verifyCertificate(certificateData, pin);
            if (!(Boolean) certVerification.get("success")) {
                throw new BusinessException("CERTIFICATE_INVALID", "憑證驗證失敗，無法產生簽章");
            }
            
            // 2. 計算資料雜湊
            String dataHash = calculateHash(data);
            
            // 3. 使用私鑰簽章
            String signature = signWithPrivateKey(dataHash, certificateData, pin);
            
            // 4. 建立簽章資訊
            Map<String, Object> signatureInfo = new HashMap<>();
            signatureInfo.put("algorithm", "SHA256withRSA");
            signatureInfo.put("timestamp", LocalDateTime.now());
            signatureInfo.put("signerInfo", ((Map<String, Object>) certVerification.get("certificateInfo")).get("subject"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("signature", signature);
            result.put("dataHash", dataHash);
            result.put("signatureInfo", signatureInfo);
            
            log.info("數位簽章產生成功");
            return result;
            
        } catch (BusinessException e) {
            log.error("數位簽章產生失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("數位簽章產生異常: {}", e.getMessage(), e);
            throw new BusinessException("SIGNATURE_GENERATION_ERROR", "數位簽章產生異常: " + e.getMessage());
        }
    }
    
    /**
     * 驗證數位簽章
     */
    public boolean verifyDigitalSignature(String data, String signature, String certificateData) {
        log.info("驗證數位簽章");
        
        try {
            // 1. 檢查憑證有效性
            Map<String, Object> certificateInfo = parseCertificateData(certificateData);
            if (!isCertificateValid(certificateInfo)) {
                log.warn("簽章憑證已過期或無效");
                return false;
            }
            
            // 2. 計算資料雜湊
            String dataHash = calculateHash(data);
            
            // 3. 使用公鑰驗證簽章
            boolean isValid = verifyWithPublicKey(dataHash, signature, certificateData);
            
            log.info("數位簽章驗證結果: {}", isValid ? "有效" : "無效");
            return isValid;
            
        } catch (Exception e) {
            log.error("數位簽章驗證異常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    // ==================== 私有方法 ====================
    
    private Map<String, Object> parseCertificateData(String certificateData) {
        // 模擬憑證解析
        Map<String, Object> info = new HashMap<>();
        info.put("subject", "CN=測試企業有限公司,O=TEST CORP LTD,L=TAIPEI,C=TW");
        info.put("issuer", "CN=台灣CA中心,O=Taiwan CA,C=TW");
        info.put("unifiedNumber", "12345678");
        info.put("companyName", "測試企業有限公司");
        info.put("serialNumber", "1234567890ABCDEF");
        info.put("validFrom", LocalDateTime.now().minusYears(1));
        info.put("validUntil", LocalDateTime.now().plusYears(2));
        info.put("keyUsage", java.util.Arrays.asList("數位簽章", "金鑰協議"));
        return info;
    }
    
    private boolean validatePin(String certificateData, String pin) {
        // 模擬PIN碼驗證
        return pin != null && pin.length() >= 6 && pin.length() <= 12;
    }
    
    private boolean isCertificateValid(Map<String, Object> certificateInfo) {
        LocalDateTime validUntil = (LocalDateTime) certificateInfo.get("validUntil");
        return validUntil.isAfter(LocalDateTime.now());
    }
    
    private boolean verifySignature(String certificateData) {
        // 模擬憑證簽章驗證
        return true;
    }
    
    private boolean isReaderAvailable(String readerName) {
        // 模擬讀卡機可用性檢查
        return readerName != null && !readerName.trim().isEmpty();
    }
    
    private boolean isCardPresent(String readerName) {
        // 模擬卡片存在檢查
        return true;
    }
    
    private String readCertificateFromCard(String readerName) {
        // 模擬從卡片讀取憑證
        return "3082045C3082034... (模擬憑證資料)";
    }
    
    private String getReaderType(String readerName) {
        if (readerName.contains("Microsoft")) return "Windows內建";
        if (readerName.contains("ACS")) return "ACS讀卡機";
        if (readerName.contains("CASTLES")) return "CASTLES讀卡機";
        if (readerName.contains("Gemalto")) return "Gemalto讀卡機";
        return "未知類型";
    }
    
    private String calculateHash(String data) {
        // 簡化的雜湊計算
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(data.getBytes());
            return java.util.Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            throw new RuntimeException("雜湊計算失敗", e);
        }
    }
    
    private String signWithPrivateKey(String dataHash, String certificateData, String pin) {
        // 模擬私鑰簽章
        return "ABC123DEF456..." + dataHash.substring(0, 8);
    }
    
    private boolean verifyWithPublicKey(String dataHash, String signature, String certificateData) {
        // 模擬公鑰驗證
        return signature.endsWith(dataHash.substring(0, 8));
    }
    
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return unifiedNumber;
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
}