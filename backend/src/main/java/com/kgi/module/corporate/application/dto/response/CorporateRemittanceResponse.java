package com.kgi.module.corporate.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 企業解款回應DTO
 * 回傳企業解款處理結果給前端
 */
@Data
@Builder
public class CorporateRemittanceResponse {
    
    /**
     * 解款編號
     */
    private String remittanceId;
    
    /**
     * 遮罩後的統一編號
     */
    private String unifiedNumber;
    
    /**
     * 公司名稱
     */
    private String companyName;
    
    /**
     * 解款金額
     */
    private BigDecimal amount;
    
    /**
     * 幣別
     */
    private String currency;
    
    /**
     * 處理狀態
     */
    private String status;
    
    /**
     * 風險等級
     */
    private String riskLevel;
    
    /**
     * 申請時間
     */
    private LocalDateTime applicationTime;
    
    /**
     * 是否需要審核
     */
    private Boolean requiresApproval;
    
    /**
     * 預計完成時間
     */
    private LocalDateTime estimatedCompletionTime;
    
    /**
     * 回應訊息
     */
    private String message;
    
    /**
     * 審核資訊
     */
    private ApprovalInfo approvalInfo;
    
    /**
     * 處理進度
     */
    private ProcessingProgress processingProgress;
    
    /**
     * 審核資訊內嵌類別
     */
    @Data
    @Builder
    public static class ApprovalInfo {
        private String approver;
        private LocalDateTime approvalTime;
        private String comments;
        private String approvalLevel;
        private Boolean approved;
    }
    
    /**
     * 處理進度內嵌類別
     */
    @Data
    @Builder
    public static class ProcessingProgress {
        private String currentStage;
        private String nextStage;
        private Integer completionPercentage;
        private String statusDescription;
        private LocalDateTime lastUpdateTime;
    }
    
    /**
     * 建立成功回應
     */
    public static CorporateRemittanceResponse success(String remittanceId, String message) {
        return CorporateRemittanceResponse.builder()
                .remittanceId(remittanceId)
                .message(message)
                .build();
    }
    
    /**
     * 建立錯誤回應
     */
    public static CorporateRemittanceResponse error(String message) {
        return CorporateRemittanceResponse.builder()
                .message(message)
                .build();
    }
    
    /**
     * 檢查是否為成功回應
     */
    public boolean isSuccess() {
        return remittanceId != null && !remittanceId.trim().isEmpty();
    }
    
    /**
     * 取得格式化的金額字串
     */
    public String getFormattedAmount() {
        if (amount == null || currency == null) {
            return "";
        }
        
        return String.format("%,.2f %s", amount, currency);
    }
    
    /**
     * 取得處理進度百分比
     */
    public Integer getProgressPercentage() {
        if (processingProgress != null && processingProgress.getCompletionPercentage() != null) {
            return processingProgress.getCompletionPercentage();
        }
        
        // 根據狀態推算進度
        if (status == null) return 0;
        
        switch (status.toUpperCase()) {
            case "待處理":
                return 10;
            case "審核中":
                return 30;
            case "已核准":
                return 60;
            case "處理中":
                return 80;
            case "已完成":
                return 100;
            case "已拒絕":
            case "失敗":
                return 0;
            default:
                return 0;
        }
    }
    
    /**
     * 檢查是否需要客戶等待
     */
    public boolean requiresCustomerWait() {
        if (status == null) return false;
        
        return status.equals("審核中") || status.equals("處理中");
    }
    
    /**
     * 取得下一步驟提示
     */
    public String getNextActionHint() {
        if (status == null) return "";
        
        switch (status) {
            case "待處理":
                return "申請已提交，等待初審";
            case "審核中":
                return requiresApproval ? "需要主管審核，請耐心等待" : "正在進行自動審核";
            case "已核准":
                return "審核通過，正在處理解款";
            case "處理中":
                return "解款處理中，預計" + getEstimatedTimeDescription() + "完成";
            case "已完成":
                return "解款已完成";
            case "已拒絕":
                return "申請被拒絕，請查看拒絕原因";
            case "失敗":
                return "處理失敗，請聯繫客服";
            default:
                return "";
        }
    }
    
    /**
     * 取得預計完成時間描述
     */
    private String getEstimatedTimeDescription() {
        if (estimatedCompletionTime == null) {
            return "2-4小時內";
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (estimatedCompletionTime.isAfter(now)) {
            long hours = java.time.Duration.between(now, estimatedCompletionTime).toHours();
            if (hours > 0) {
                return hours + "小時內";
            } else {
                long minutes = java.time.Duration.between(now, estimatedCompletionTime).toMinutes();
                return minutes + "分鐘內";
            }
        }
        
        return "即將完成";
    }
}