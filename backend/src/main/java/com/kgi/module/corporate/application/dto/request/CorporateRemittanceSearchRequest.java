package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

/**
 * 企業匯款搜尋請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateRemittanceSearchRequest {
    
    /**
     * 統一編號
     */
    @NotNull(message = "統一編號不能為空")
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    private String companyName;
    
    /**
     * 企業英文名稱
     */
    private String companyEnglishName;
    
    /**
     * 匯款起始日期
     */
    private LocalDate startDate;
    
    /**
     * 匯款結束日期
     */
    private LocalDate endDate;
    
    /**
     * 匯款金額範圍 - 最小值
     */
    private Double minAmount;
    
    /**
     * 匯款金額範圍 - 最大值
     */
    private Double maxAmount;
    
    /**
     * 匯款幣別
     */
    private String currency;
    
    /**
     * 匯款銀行代碼
     */
    private String bankCode;
    
    /**
     * 付款人名稱
     */
    private String payerName;
    
    /**
     * 付款人國家
     */
    private String payerCountry;
    
    /**
     * 匯款性質列表
     */
    private List<String> remittanceNatures;
    
    /**
     * 匯款狀態過濾
     */
    private List<String> statusFilter;
    
    /**
     * 是否包含已處理的匯款
     */
    @Builder.Default
    private Boolean includeProcessed = true;
    
    /**
     * 是否包含已取消的匯款
     */
    @Builder.Default
    private Boolean includeCancelled = false;
    
    /**
     * 是否只查詢可申請的匯款
     */
    @Builder.Default
    private Boolean onlyAvailable = false;
    
    /**
     * 排序方式
     */
    @Builder.Default
    private SortOption sortBy = SortOption.DATE_DESC;
    
    /**
     * 分頁大小
     */
    @Builder.Default
    private Integer pageSize = 20;
    
    /**
     * 頁碼 (從0開始)
     */
    @Builder.Default
    private Integer page = 0;
    
    /**
     * 排序選項枚舉
     */
    public enum SortOption {
        /**
         * 按日期降序 (最新的在前)
         */
        DATE_DESC,
        
        /**
         * 按日期升序 (最舊的在前)
         */
        DATE_ASC,
        
        /**
         * 按金額降序 (金額大的在前)
         */
        AMOUNT_DESC,
        
        /**
         * 按金額升序 (金額小的在前)
         */
        AMOUNT_ASC,
        
        /**
         * 按狀態排序
         */
        STATUS,
        
        /**
         * 按銀行排序
         */
        BANK
    }
}