package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 企業條款同意回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateTermsAgreementResponse {
    
    /**
     * 同意記錄ID
     */
    private String agreementId;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 統一編號
     */
    private String unifiedNumber;
    
    /**
     * 條款版本
     */
    private String termsVersion;
    
    /**
     * 同意時間
     */
    private LocalDateTime agreedAt;
    
    /**
     * 有效期限
     */
    private LocalDateTime expiryDate;
    
    /**
     * 代表人身分證號
     */
    private String representativeId;
    
    /**
     * 訊息
     */
    private String message;
    
    /**
     * 錯誤代碼
     */
    private String errorCode;
}