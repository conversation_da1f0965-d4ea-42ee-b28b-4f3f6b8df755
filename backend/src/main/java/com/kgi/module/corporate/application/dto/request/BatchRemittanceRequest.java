package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批次法人解款申請請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchRemittanceRequest {
    
    /**
     * 批次名稱
     */
    @NotNull(message = "批次名稱不能為空")
    @Size(min = 1, max = 100, message = "批次名稱長度應在1-100字元之間")
    private String batchName;
    
    /**
     * 提交者ID
     */
    @NotNull(message = "提交者ID不能為空")
    private String submitterId;
    
    /**
     * 提交者姓名
     */
    @NotNull(message = "提交者姓名不能為空")
    private String submitterName;
    
    /**
     * 批次解款申請列表
     */
    @NotEmpty(message = "批次申請列表不能為空")
    @Size(min = 1, max = 100, message = "批次申請數量應在1-100筆之間")
    @Valid
    private List<CorporateRemittanceRequest> remittanceRequests;
    
    /**
     * 批次備註
     */
    @Size(max = 500, message = "批次備註長度不能超過500字元")
    private String remarks;
    
    /**
     * 處理優先級
     */
    @Builder.Default
    private String priority = "NORMAL";
    
    /**
     * 是否需要主管審核
     */
    @Builder.Default
    private Boolean requiresManagerApproval = false;
}