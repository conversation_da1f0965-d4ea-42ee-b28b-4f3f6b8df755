package com.kgi.module.corporate.application.usecase;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.module.corporate.application.dto.request.CorporateTermsAgreementRequest;
import com.kgi.module.corporate.application.dto.response.CorporateTermsAgreementResponse;
import com.kgi.module.corporate.application.dto.response.CorporateTermsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業條款用例
 * 處理企業條款相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CorporateTermsUseCase {
    
    /**
     * 取得企業條款
     */
    public CorporateTermsResponse getTerms(String language, String version) {
        log.info("取得企業條款: language={}, version={}", language, version);
        
        try {
            // TODO: 實作實際的條款查詢邏輯
            
            return CorporateTermsResponse.builder()
                    .version(version != null ? version : "1.0")
                    .title("KGI銀行企業數位解款服務條款")
                    .content("企業客戶使用KGI銀行數位解款服務之相關條款...")
                    .language(language)
                    .effectiveDate(LocalDateTime.now().minusDays(30))
                    .expiryDate(LocalDateTime.now().plusYears(1))
                    .isLatest(version == null || "1.0".equals(version))
                    .sections(List.of(
                            CorporateTermsResponse.TermsSection.builder()
                                    .sectionNumber("1")
                                    .title("服務範圍")
                                    .content("本服務適用於具有統一編號之企業客戶...")
                                    .required(true)
                                    .build(),
                            CorporateTermsResponse.TermsSection.builder()
                                    .sectionNumber("2")
                                    .title("資格限制")
                                    .content("企業客戶須完成工商憑證驗證...")
                                    .required(true)
                                    .build()
                    ))
                    .build();
                    
        } catch (Exception e) {
            log.error("取得企業條款失敗: {}", e.getMessage(), e);
            throw new BusinessException("TERMS_RETRIEVAL_FAILED", "取得企業條款失敗: " + e.getMessage());
        }
    }
    
    /**
     * 企業條款同意
     */
    @Transactional
    public CorporateTermsAgreementResponse agreeToTerms(CorporateTermsAgreementRequest request) {
        log.info("企業條款同意: unifiedNumber={}, version={}", 
                maskUnifiedNumber(request.getUnifiedNumber()), request.getTermsVersion());
        
        try {
            // 驗證輸入參數
            validateTermsAgreementRequest(request);
            
            // TODO: 實作實際的條款同意邏輯
            // 1. 檢查企業資格
            // 2. 記錄同意記錄
            // 3. 更新企業狀態
            
            String agreementId = generateAgreementId();
            
            return CorporateTermsAgreementResponse.builder()
                    .success(true)
                    .agreementId(agreementId)
                    .unifiedNumber(request.getUnifiedNumber())
                    .termsVersion(request.getTermsVersion())
                    .agreedAt(LocalDateTime.now())
                    .expiryDate(LocalDateTime.now().plusYears(1))
                    .representativeId(request.getRepresentativeId())
                    .message("企業條款同意成功")
                    .build();
                    
        } catch (BusinessException e) {
            log.warn("企業條款同意失敗: {}", e.getMessage());
            return CorporateTermsAgreementResponse.builder()
                    .success(false)
                    .message(e.getMessage())
                    .errorCode(e.getErrorCode())
                    .build();
                    
        } catch (Exception e) {
            log.error("企業條款同意異常: {}", e.getMessage(), e);
            return CorporateTermsAgreementResponse.builder()
                    .success(false)
                    .message("企業條款同意失敗")
                    .errorCode("TERMS_AGREEMENT_FAILED")
                    .build();
        }
    }
    
    /**
     * 驗證條款同意請求
     */
    private void validateTermsAgreementRequest(CorporateTermsAgreementRequest request) {
        if (request == null) {
            throw new BusinessException("INVALID_REQUEST", "請求參數不能為空");
        }
        
        if (isBlank(request.getUnifiedNumber())) {
            throw new BusinessException("INVALID_UNIFIED_NUMBER", "統一編號不能為空");
        }
        
        if (!isValidUnifiedNumber(request.getUnifiedNumber())) {
            throw new BusinessException("INVALID_UNIFIED_NUMBER", "統一編號格式錯誤");
        }
        
        if (isBlank(request.getTermsVersion())) {
            throw new BusinessException("INVALID_TERMS_VERSION", "條款版本不能為空");
        }
        
        if (request.getAgreedAt() == null) {
            throw new BusinessException("INVALID_AGREED_TIME", "同意時間不能為空");
        }
    }
    
    /**
     * 驗證統一編號格式
     */
    private boolean isValidUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return false;
        }
        
        // 檢查是否全為數字
        return unifiedNumber.matches("\\d{8}");
    }
    
    /**
     * 生成同意記錄ID
     */
    private String generateAgreementId() {
        return "AGR" + System.currentTimeMillis();
    }
    
    /**
     * 遮罩統一編號
     */
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return "****";
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
    
    /**
     * 檢查字串是否為空
     */
    private boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
}