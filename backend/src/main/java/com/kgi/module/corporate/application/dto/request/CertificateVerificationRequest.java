package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 工商憑證驗證請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CertificateVerificationRequest {
    
    /**
     * 憑證資料 (Base64編碼)
     */
    @NotNull(message = "憑證資料不能為空")
    private String certificateData;
    
    /**
     * PIN碼
     */
    @NotNull(message = "PIN碼不能為空")
    @Size(min = 6, max = 12, message = "PIN碼長度應在6-12位之間")
    private String pin;
    
    /**
     * 統一編號
     */
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 讀卡機名稱
     */
    private String readerName;
    
    /**
     * 驗證類型
     */
    @Builder.Default
    private String verificationType = "AUTHENTICATION";
    
    /**
     * 客戶端資訊
     */
    private String clientInfo;
    
    /**
     * 時間戳記
     */
    private Long timestamp;
}