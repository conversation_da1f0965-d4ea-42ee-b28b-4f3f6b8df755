package com.kgi.module.corporate.application.usecase;

import com.kgi.module.corporate.application.dto.request.*;
import com.kgi.module.corporate.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業申請用例
 * 處理企業申請相關的業務邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CorporateApplicationUseCase {
    
    /**
     * 提交企業申請
     */
    public CorporateApplicationSubmissionResponse submitApplication(CorporateApplicationSubmitRequest request) {
        log.info("提交企業申請: unifiedNumber={}, remittanceCount={}", 
                maskUnifiedNumber(request.getUnifiedNumber()),
                request.getSelectedRemittances() != null ? request.getSelectedRemittances().size() : 0);
        
        // TODO: 實作企業申請提交邏輯
        return CorporateApplicationSubmissionResponse.builder()
                .applicationId("APP" + System.currentTimeMillis())
                .status("SUBMITTED")
                .unifiedNumber(request.getUnifiedNumber())
                .companyName(request.getCompanyName())
                .submitTime(LocalDateTime.now())
                .estimatedProcessTime(LocalDateTime.now().plusDays(1))
                .trackingNumber("TRK" + System.currentTimeMillis())
                .nextSteps(List.of("等待系統審核", "請保持聯絡方式暢通"))
                .requiresManagerApproval(false)
                .build();
    }
    
    /**
     * 查詢申請狀態
     */
    public CorporateApplicationStatusResponse getApplicationStatus(String applicationId, String unifiedNumber) {
        log.info("查詢企業申請狀態: applicationId={}, unifiedNumber={}", 
                applicationId, maskUnifiedNumber(unifiedNumber));
        
        // TODO: 實作申請狀態查詢邏輯
        return CorporateApplicationStatusResponse.builder()
                .applicationId(applicationId)
                .currentStatus("PROCESSING")
                .statusDescription("申請處理中")
                .unifiedNumber(unifiedNumber)
                .progressPercentage(50)
                .estimatedCompletionTime(LocalDateTime.now().plusDays(1))
                .statusHistory(List.of())
                .requiredActions(List.of())
                .build();
    }
    
    /**
     * 查詢申請歷史
     */
    public List<CorporateApplicationStatusResponse> getApplicationHistory(String unifiedNumber, int page, int size) {
        log.info("查詢企業申請歷史: unifiedNumber={}, page={}, size={}", 
                maskUnifiedNumber(unifiedNumber), page, size);
        
        // TODO: 實作申請歷史查詢邏輯
        return List.of();
    }
    
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return "****";
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
}