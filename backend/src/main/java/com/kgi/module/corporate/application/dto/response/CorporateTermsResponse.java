package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業條款回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateTermsResponse {
    
    /**
     * 條款版本
     */
    private String version;
    
    /**
     * 條款內容
     */
    private String content;
    
    /**
     * 條款標題
     */
    private String title;
    
    /**
     * 語言
     */
    private String language;
    
    /**
     * 生效日期
     */
    private LocalDateTime effectiveDate;
    
    /**
     * 到期日期
     */
    private LocalDateTime expiryDate;
    
    /**
     * 是否為最新版本
     */
    private Boolean isLatest;
    
    /**
     * 條款章節
     */
    private List<TermsSection> sections;
    
    /**
     * 條款章節內部類
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermsSection {
        /**
         * 章節編號
         */
        private String sectionNumber;
        
        /**
         * 章節標題
         */
        private String title;
        
        /**
         * 章節內容
         */
        private String content;
        
        /**
         * 是否為必要條款
         */
        private Boolean required;
    }
}