package com.kgi.module.corporate.infrastructure.repository;

import com.kgi.core.domain.valueobject.UnifiedNumber;
import com.kgi.module.corporate.domain.model.CorporateRemittance;
import com.kgi.module.corporate.domain.repository.CorporateRemittanceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 企業解款Repository內存實作
 * 使用 ConcurrentHashMap 作為儲存機制
 */
@Slf4j
@Repository
public class CorporateRemittanceRepositoryImpl implements CorporateRemittanceRepository {
    
    private final Map<String, CorporateRemittance> remittanceStorage = new ConcurrentHashMap<>();
    
    @Override
    public CorporateRemittance save(CorporateRemittance remittance) {
        log.debug("儲存企業解款記錄: {}", remittance.getRemittanceId());
        
        if (remittance.getRemittanceId() == null) {
            // 無法設定 remittanceId，因為沒有 setter 方法
            // 需要在創建時就設定 remittanceId
            throw new IllegalArgumentException("RemittanceId must be set before saving");
        }
        
        remittanceStorage.put(remittance.getRemittanceId(), remittance);
        log.debug("企業解款記錄儲存成功: id={}", remittance.getRemittanceId());
        return remittance;
    }
    
    @Override
    public Optional<CorporateRemittance> findByRemittanceId(String remittanceId) {
        log.debug("根據解款ID查詢: {}", remittanceId);
        return Optional.ofNullable(remittanceStorage.get(remittanceId));
    }
    
    @Override
    public List<CorporateRemittance> findByUnifiedNumber(UnifiedNumber unifiedNumber) {
        log.debug("根據統一編號查詢解款記錄: {}", unifiedNumber.getValue());
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getUnifiedNumber() != null && 
                           r.getUnifiedNumber().getValue().equals(unifiedNumber.getValue()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByUnifiedNumberAndStatus(UnifiedNumber unifiedNumber, 
                                                                  CorporateRemittance.ProcessingStatus status) {
        log.debug("根據統一編號和狀態查詢: unifiedNumber={}, status={}", 
                unifiedNumber.getValue(), status);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getUnifiedNumber() != null && 
                           r.getUnifiedNumber().getValue().equals(unifiedNumber.getValue()))
                .filter(r -> r.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByStatus(CorporateRemittance.ProcessingStatus status) {
        log.debug("根據狀態查詢解款記錄: {}", status);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getStatus() == status)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByUnifiedNumberWithPagination(UnifiedNumber unifiedNumber, 
                                                                        int page, int size) {
        log.debug("分頁查詢企業解款記錄: unifiedNumber={}, page={}, size={}", 
                unifiedNumber.getValue(), page, size);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getUnifiedNumber() != null && 
                           r.getUnifiedNumber().getValue().equals(unifiedNumber.getValue()))
                .sorted((a, b) -> b.getApplicationTime().compareTo(a.getApplicationTime()))
                .skip((long) page * size)
                .limit(size)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findPendingApprovals() {
        log.debug("查詢待審核的解款記錄");
        return findByStatus(CorporateRemittance.ProcessingStatus.UNDER_REVIEW);
    }
    
    @Override
    public List<CorporateRemittance> findHighRiskRemittances() {
        log.debug("查詢高風險解款記錄");
        
        return remittanceStorage.values().stream()
                .filter(r -> "HIGH".equals(r.getRiskLevel()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findOverdueRemittances() {
        log.debug("查詢逾期未處理的解款記錄");
        
        LocalDateTime overdueThreshold = LocalDateTime.now().minusHours(4);
        return remittanceStorage.values().stream()
                .filter(r -> r.getApplicationTime() != null &&
                           r.getApplicationTime().isBefore(overdueThreshold) &&
                           (r.getStatus() == CorporateRemittance.ProcessingStatus.PENDING ||
                            r.getStatus() == CorporateRemittance.ProcessingStatus.UNDER_REVIEW))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("查詢時間範圍內的解款記錄: {} to {}", startDate, endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getApplicationTime() != null &&
                           r.getApplicationTime().isAfter(startDateTime) &&
                           r.getApplicationTime().isBefore(endDateTime))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByUnifiedNumberAndDateRange(UnifiedNumber unifiedNumber, 
                                                                     LocalDate startDate, 
                                                                     LocalDate endDate) {
        log.debug("查詢企業在指定時間範圍內的解款記錄: unifiedNumber={}, {} to {}", 
                unifiedNumber.getValue(), startDate, endDate);
        
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getUnifiedNumber() != null && 
                           r.getUnifiedNumber().getValue().equals(unifiedNumber.getValue()))
                .filter(r -> r.getApplicationTime() != null &&
                           r.getApplicationTime().isAfter(startDateTime) &&
                           r.getApplicationTime().isBefore(endDateTime))
                .collect(Collectors.toList());
    }
    
    @Override
    public BigDecimal sumAmountByUnifiedNumberAndDateRange(UnifiedNumber unifiedNumber, 
                                                           LocalDate startDate, 
                                                           LocalDate endDate, 
                                                           String currency) {
        log.debug("統計企業在指定期間的解款總金額: unifiedNumber={}, {} to {}, currency={}", 
                unifiedNumber.getValue(), startDate, endDate, currency);
        
        return findByUnifiedNumberAndDateRange(unifiedNumber, startDate, endDate).stream()
                .filter(r -> r.getRemittanceDetails() != null && 
                           currency.equals(r.getRemittanceDetails().getCurrency()))
                .map(r -> r.getRemittanceDetails().getAmount())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    @Override
    public Long countByUnifiedNumberAndDateRange(UnifiedNumber unifiedNumber, 
                                                 LocalDate startDate, 
                                                 LocalDate endDate) {
        log.debug("統計企業在指定期間的解款筆數: unifiedNumber={}, {} to {}", 
                unifiedNumber.getValue(), startDate, endDate);
        
        return (long) findByUnifiedNumberAndDateRange(unifiedNumber, startDate, endDate).size();
    }
    
    @Override
    public BigDecimal getDailyUsedAmount(UnifiedNumber unifiedNumber, String currency, LocalDate date) {
        log.debug("查詢企業日限額使用情況: unifiedNumber={}, currency={}, date={}", 
                unifiedNumber.getValue(), currency, date);
        
        return sumAmountByUnifiedNumberAndDateRange(unifiedNumber, date, date, currency);
    }
    
    @Override
    public BigDecimal getMonthlyUsedAmount(UnifiedNumber unifiedNumber, String currency, 
                                           int year, int month) {
        log.debug("查詢企業月限額使用情況: unifiedNumber={}, currency={}, {}/{}", 
                unifiedNumber.getValue(), currency, year, month);
        
        LocalDate startOfMonth = LocalDate.of(year, month, 1);
        LocalDate endOfMonth = startOfMonth.plusMonths(1).minusDays(1);
        
        return sumAmountByUnifiedNumberAndDateRange(unifiedNumber, startOfMonth, endOfMonth, currency);
    }
    
    @Override
    public BigDecimal getYearlyUsedAmount(UnifiedNumber unifiedNumber, String currency, int year) {
        log.debug("查詢企業年限額使用情況: unifiedNumber={}, currency={}, year={}", 
                unifiedNumber.getValue(), currency, year);
        
        LocalDate startOfYear = LocalDate.of(year, 1, 1);
        LocalDate endOfYear = LocalDate.of(year, 12, 31);
        
        return sumAmountByUnifiedNumberAndDateRange(unifiedNumber, startOfYear, endOfYear, currency);
    }
    
    @Override
    public List<CorporateRemittance> findRequiringManagerApproval() {
        log.debug("查詢需要主管審核的解款記錄");
        
        return remittanceStorage.values().stream()
                .filter(r -> r.requiresManagerApproval())
                .filter(r -> r.getStatus() == CorporateRemittance.ProcessingStatus.UNDER_REVIEW)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByApprover(String approver) {
        log.debug("查詢指定審核人員處理的解款記錄: {}", approver);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getApprovalInfo() != null && 
                           approver.equals(r.getApprovalInfo().getApprover()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findForBatchProcessing(CorporateRemittance.ProcessingStatus status, 
                                                            int batchSize) {
        log.debug("查詢批次處理相關的解款記錄: status={}, batchSize={}", status, batchSize);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getStatus() == status)
                .sorted((a, b) -> a.getApplicationTime().compareTo(b.getApplicationTime()))
                .limit(batchSize)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByRiskLevel(String riskLevel) {
        log.debug("根據風險等級查詢解款記錄: {}", riskLevel);
        
        return remittanceStorage.values().stream()
                .filter(r -> riskLevel.equals(r.getRiskLevel()))
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByAmountGreaterThan(BigDecimal threshold, String currency) {
        log.debug("查詢金額超過指定閾值的解款記錄: {} {}", threshold, currency);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getRemittanceDetails() != null && 
                           currency.equals(r.getRemittanceDetails().getCurrency()))
                .filter(r -> r.getRemittanceDetails() != null && 
                           r.getRemittanceDetails().getAmount() != null && 
                           r.getRemittanceDetails().getAmount().compareTo(threshold) > 0)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<CorporateRemittance> findByBeneficiaryCountry(String country) {
        log.debug("查詢指定收款國家的解款記錄: {}", country);
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getRemittanceDetails() != null && 
                           r.getRemittanceDetails().getBeneficiaryInfo() != null &&
                           country.equals(r.getRemittanceDetails().getBeneficiaryInfo().getBeneficiaryCountry()))
                .collect(Collectors.toList());
    }
    
    @Override
    public Map<CorporateRemittance.ProcessingStatus, Long> countByStatusAndDateRange(LocalDate startDate, 
                                                                                     LocalDate endDate) {
        log.debug("統計指定時間範圍內各狀態的解款筆數: {} to {}", startDate, endDate);
        
        return findByDateRange(startDate, endDate).stream()
                .collect(Collectors.groupingBy(
                        CorporateRemittance::getStatus,
                        Collectors.counting()
                ));
    }
    
    @Override
    public Map<String, BigDecimal> sumAmountByCurrencyAndDateRange(LocalDate startDate, LocalDate endDate) {
        log.debug("統計指定時間範圍內各幣別的解款金額: {} to {}", startDate, endDate);
        
        return findByDateRange(startDate, endDate).stream()
                .collect(Collectors.groupingBy(
                        r -> r.getRemittanceDetails() != null ? r.getRemittanceDetails().getCurrency() : "UNKNOWN",
                        Collectors.reducing(BigDecimal.ZERO, 
                                          r -> r.getRemittanceDetails() != null ? r.getRemittanceDetails().getAmount() : BigDecimal.ZERO, 
                                          BigDecimal::add)
                ));
    }
    
    @Override
    public Double getAverageProcessingHours(LocalDate startDate, LocalDate endDate) {
        log.debug("查詢平均處理時間: {} to {}", startDate, endDate);
        
        List<CorporateRemittance> completedRemittances = findByDateRange(startDate, endDate).stream()
                .filter(r -> r.getStatus() == CorporateRemittance.ProcessingStatus.COMPLETED)
                .filter(r -> r.getCompletionTime() != null && r.getApplicationTime() != null)
                .collect(Collectors.toList());
        
        if (completedRemittances.isEmpty()) {
            return 0.0;
        }
        
        double totalHours = completedRemittances.stream()
                .mapToDouble(r -> {
                    long minutes = java.time.Duration.between(r.getApplicationTime(), r.getCompletionTime()).toMinutes();
                    return minutes / 60.0;
                })
                .sum();
        
        return totalHours / completedRemittances.size();
    }
    
    @Override
    public Double getSlaComplianceRate(LocalDate startDate, LocalDate endDate) {
        log.debug("查詢SLA達成率: {} to {}", startDate, endDate);
        
        List<CorporateRemittance> completedRemittances = findByDateRange(startDate, endDate).stream()
                .filter(r -> r.getStatus() == CorporateRemittance.ProcessingStatus.COMPLETED)
                .collect(Collectors.toList());
        
        if (completedRemittances.isEmpty()) {
            return 100.0;
        }
        
        long slaCompliantCount = completedRemittances.stream()
                .filter(r -> !r.isSlaBreached())
                .count();
        
        return (double) slaCompliantCount / completedRemittances.size() * 100;
    }
    
    @Override
    public boolean existsByRemittanceId(String remittanceId) {
        log.debug("檢查解款ID是否存在: {}", remittanceId);
        return remittanceStorage.containsKey(remittanceId);
    }
    
    @Override
    public boolean hasOngoingRemittance(UnifiedNumber unifiedNumber) {
        log.debug("檢查企業是否有進行中的解款: {}", unifiedNumber.getValue());
        
        Set<CorporateRemittance.ProcessingStatus> ongoingStatuses = Set.of(
                CorporateRemittance.ProcessingStatus.PENDING,
                CorporateRemittance.ProcessingStatus.UNDER_REVIEW,
                CorporateRemittance.ProcessingStatus.APPROVED,
                CorporateRemittance.ProcessingStatus.PROCESSING
        );
        
        return remittanceStorage.values().stream()
                .filter(r -> r.getUnifiedNumber() != null && 
                           r.getUnifiedNumber().getValue().equals(unifiedNumber.getValue()))
                .anyMatch(r -> ongoingStatuses.contains(r.getStatus()));
    }
    
    @Override
    public void deleteByRemittanceId(String remittanceId) {
        log.debug("刪除解款記錄: {}", remittanceId);
        remittanceStorage.remove(remittanceId);
    }
    
    @Override
    public int batchUpdateStatus(List<String> remittanceIds, CorporateRemittance.ProcessingStatus newStatus) {
        log.debug("批次更新解款狀態: count={}, newStatus={}", remittanceIds.size(), newStatus);
        
        int updateCount = 0;
        for (String id : remittanceIds) {
            CorporateRemittance remittance = remittanceStorage.get(id);
            if (remittance != null) {
                // 無法直接設定狀態，需要使用領域方法
                // 這裡簡化處理，實際應該根據新狀態調用相應的領域方法
                log.warn("批次更新狀態功能需要改進，目前無法直接設定狀態");
                updateCount++;
            }
        }
        
        return updateCount;
    }
    
    @Override
    public CorporateRemittanceStatistics getStatisticsByUnifiedNumber(UnifiedNumber unifiedNumber, 
                                                                      LocalDate startDate, 
                                                                      LocalDate endDate) {
        log.debug("查詢企業解款統計資訊: unifiedNumber={}, {} to {}", 
                unifiedNumber.getValue(), startDate, endDate);
        
        List<CorporateRemittance> remittances = findByUnifiedNumberAndDateRange(unifiedNumber, startDate, endDate);
        
        // 計算各狀態筆數
        Map<CorporateRemittance.ProcessingStatus, Long> statusCounts = remittances.stream()
                .collect(Collectors.groupingBy(CorporateRemittance::getStatus, Collectors.counting()));
        
        // 計算總金額
        BigDecimal totalAmount = remittances.stream()
                .filter(r -> r.getRemittanceDetails() != null)
                .map(r -> r.getRemittanceDetails().getAmount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 計算平均處理時間
        List<Long> processingHours = remittances.stream()
                .filter(r -> r.getStatus() == CorporateRemittance.ProcessingStatus.COMPLETED)
                .map(CorporateRemittance::getProcessingTimeHours)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        long avgProcessingHours = processingHours.isEmpty() ? 0 : 
                processingHours.stream().mapToLong(Long::longValue).sum() / processingHours.size();
        
        // 創建並返回統計結果
        return new CorporateRemittanceStatistics() {
            @Override
            public Long getTotalCount() {
                return (long) remittances.size();
            }
            
            @Override
            public BigDecimal getTotalAmount() {
                return totalAmount;
            }
            
            @Override
            public Long getCompletedCount() {
                return statusCounts.getOrDefault(CorporateRemittance.ProcessingStatus.COMPLETED, 0L);
            }
            
            @Override
            public Long getPendingCount() {
                return statusCounts.getOrDefault(CorporateRemittance.ProcessingStatus.PENDING, 0L);
            }
            
            @Override
            public Long getRejectedCount() {
                return statusCounts.getOrDefault(CorporateRemittance.ProcessingStatus.REJECTED, 0L);
            }
            
            @Override
            public Double getSuccessRate() {
                if (remittances.isEmpty()) return 0.0;
                return (double) getCompletedCount() / getTotalCount() * 100;
            }
            
            @Override
            public Double getAverageAmount() {
                if (remittances.isEmpty()) return 0.0;
                return totalAmount.divide(new BigDecimal(remittances.size()), 2, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            
            @Override
            public Long getAverageProcessingHours() {
                return avgProcessingHours;
            }
        };
    }
}