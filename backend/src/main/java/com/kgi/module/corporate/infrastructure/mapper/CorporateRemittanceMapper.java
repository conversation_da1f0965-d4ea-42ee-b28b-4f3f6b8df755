package com.kgi.module.corporate.infrastructure.mapper;

import com.kgi.core.domain.valueobject.UnifiedNumber;
import com.kgi.module.corporate.domain.model.*;
import com.kgi.module.corporate.infrastructure.entity.CorporateRemittanceEntity;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企業解款Mapper
 * 負責Domain Model與Entity之間的轉換
 */
@Component
public class CorporateRemittanceMapper {
    
    /**
     * Domain Model轉Entity
     */
    public CorporateRemittanceEntity toEntity(CorporateRemittance domain) {
        if (domain == null) {
            return null;
        }
        
        return CorporateRemittanceEntity.builder()
                .id(domain.getId())
                .remittanceId(domain.getRemittanceId())
                .unifiedNumber(domain.getUnifiedNumber().getValue())
                
                // 企業資訊
                .companyName(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getCompanyName() : null)
                .companyNameEnglish(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getCompanyNameEnglish() : null)
                .businessType(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getBusinessType() : null)
                .industryCode(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getIndustryCode() : null)
                
                // 聯絡人資訊
                .contactPersonName(getContactPersonName(domain.getCorporateInfo()))
                .contactPersonTitle(getContactPersonTitle(domain.getCorporateInfo()))
                .contactPersonPhone(getContactPersonPhone(domain.getCorporateInfo()))
                .contactPersonEmail(getContactPersonEmail(domain.getCorporateInfo()))
                .contactPersonId(getContactPersonId(domain.getCorporateInfo()))
                
                // 企業地址
                .companyAddress(getCompanyAddress(domain.getCorporateInfo()))
                .companyTelephone(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getTelephoneNumber() : null)
                .companyEmail(domain.getCorporateInfo() != null ? domain.getCorporateInfo().getEmail() : null)
                
                // 解款詳情
                .theirRefNo(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getTheirRefNo() : null)
                .amount(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getAmount() : null)
                .currency(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getCurrency() : null)
                .exchangeRate(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getExchangeRate() : null)
                .twdAmount(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getTwdAmount() : null)
                .fee(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getFee() : null)
                .totalDeduction(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getTotalDeduction() : null)
                
                // 收款人資訊
                .beneficiaryName(getBeneficiaryName(domain.getRemittanceDetails()))
                .beneficiaryNameLocal(getBeneficiaryNameLocal(domain.getRemittanceDetails()))
                .beneficiaryAccount(getBeneficiaryAccount(domain.getRemittanceDetails()))
                .beneficiaryBankName(getBeneficiaryBankName(domain.getRemittanceDetails()))
                .beneficiaryBankCode(getBeneficiaryBankCode(domain.getRemittanceDetails()))
                .beneficiarySwiftCode(getBeneficiarySwiftCode(domain.getRemittanceDetails()))
                .beneficiaryBankAddress(getBeneficiaryBankAddress(domain.getRemittanceDetails()))
                .beneficiaryAddress(getBeneficiaryAddress(domain.getRemittanceDetails()))
                .beneficiaryCountry(getBeneficiaryCountry(domain.getRemittanceDetails()))
                .correspondentBank(getCorrespondentBank(domain.getRemittanceDetails()))
                .correspondentSwift(getCorrespondentSwift(domain.getRemittanceDetails()))
                
                // 用途資訊
                .purposeCode(getPurposeCode(domain.getRemittanceDetails()))
                .purposeDescription(getPurposeDescription(domain.getRemittanceDetails()))
                .contractNumber(getContractNumber(domain.getRemittanceDetails()))
                .invoiceNumber(getInvoiceNumber(domain.getRemittanceDetails()))
                .sourceOfFunds(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getSourceOfFunds() : null)
                .supportingDocuments(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getSupportingDocuments() : null)
                .specialInstructions(domain.getRemittanceDetails() != null ? domain.getRemittanceDetails().getSpecialInstructions() : null)
                
                // 處理狀態
                .status(domain.getStatus() != null ? domain.getStatus().name() : null)
                .applicationTime(domain.getApplicationTime())
                .completionTime(domain.getCompletionTime())
                
                // 審核資訊
                .approved(domain.getApprovalInfo() != null ? domain.getApprovalInfo().isApproved() : null)
                .approver(domain.getApprovalInfo() != null ? domain.getApprovalInfo().getApprover() : null)
                .approverRole(getApproverRole(domain.getApprovalInfo()))
                .approvalTime(domain.getApprovalInfo() != null ? domain.getApprovalInfo().getApprovalTime() : null)
                .approvalComments(domain.getApprovalInfo() != null ? domain.getApprovalInfo().getComments() : null)
                .rejectionReason(domain.getApprovalInfo() != null ? domain.getApprovalInfo().getRejectionReason() : null)
                
                // 風險評估
                .riskLevel(domain.getRiskLevel())
                .riskScore(domain.getRiskScore())
                
                // 審計欄位
                .createdAt(domain.getCreatedAt())
                .updatedAt(domain.getUpdatedAt())
                .version(domain.getVersion())
                .isDeleted(domain.getIsDeleted())
                .createdBy(domain.getCreatedBy())
                .updatedBy(domain.getUpdatedBy())
                .remark(domain.getRemark())
                
                .build();
    }
    
    /**
     * Entity轉Domain Model
     */
    public CorporateRemittance toDomain(CorporateRemittanceEntity entity) {
        if (entity == null) {
            return null;
        }
        
        CorporateRemittance domain = CorporateRemittance.builder()
                .remittanceId(entity.getRemittanceId())
                .unifiedNumber(UnifiedNumber.of(entity.getUnifiedNumber()))
                .corporateInfo(buildCorporateInfo(entity))
                .remittanceDetails(buildRemittanceDetails(entity))
                .build();
        
        // 設置審核資訊
        if (entity.getApproved() != null || entity.getApprover() != null) {
            ApprovalInfo approvalInfo = ApprovalInfo.builder()
                    .approved(entity.getApproved() != null ? entity.getApproved() : false)
                    .approver(entity.getApprover())
                    .approverRole(entity.getApproverRole())
                    .approvalTime(entity.getApprovalTime())
                    .comments(entity.getApprovalComments())
                    .rejectionReason(entity.getRejectionReason())
                    .approvalLevel(parseApprovalLevel(entity.getApproverRole()))
                    .build();
            
            // 使用反射或直接設置（需要在domain中提供setter）
            setApprovalInfo(domain, approvalInfo);
        }
        
        // 設置基礎欄位
        setBaseFields(domain, entity);
        
        return domain;
    }
    
    // ==================== 私有輔助方法 ====================
    
    private CorporateInfo buildCorporateInfo(CorporateRemittanceEntity entity) {
        CorporateInfo.ContactPerson contactPerson = CorporateInfo.ContactPerson.builder()
                .name(entity.getContactPersonName())
                .title(entity.getContactPersonTitle())
                .phoneNumber(entity.getContactPersonPhone())
                .email(entity.getContactPersonEmail())
                .idNumber(entity.getContactPersonId())
                .build();
        
        CorporateInfo.CorporateAddress address = CorporateInfo.CorporateAddress.builder()
                .fullAddress(entity.getCompanyAddress())
                .build();
        
        return CorporateInfo.builder()
                .companyName(entity.getCompanyName())
                .companyNameEnglish(entity.getCompanyNameEnglish())
                .businessType(entity.getBusinessType())
                .industryCode(entity.getIndustryCode())
                .contactPerson(contactPerson)
                .address(address)
                .telephoneNumber(entity.getCompanyTelephone())
                .email(entity.getCompanyEmail())
                .build();
    }
    
    private RemittanceDetails buildRemittanceDetails(CorporateRemittanceEntity entity) {
        RemittanceDetails.BeneficiaryInfo.BankInfo bankInfo = RemittanceDetails.BeneficiaryInfo.BankInfo.builder()
                .bankName(entity.getBeneficiaryBankName())
                .bankCode(entity.getBeneficiaryBankCode())
                .swiftCode(entity.getBeneficiarySwiftCode())
                .bankAddress(entity.getBeneficiaryBankAddress())
                .correspondentBank(entity.getCorrespondentBank())
                .correspondentSwift(entity.getCorrespondentSwift())
                .build();
        
        RemittanceDetails.BeneficiaryInfo beneficiaryInfo = RemittanceDetails.BeneficiaryInfo.builder()
                .beneficiaryName(entity.getBeneficiaryName())
                .beneficiaryNameLocal(entity.getBeneficiaryNameLocal())
                .beneficiaryAccount(entity.getBeneficiaryAccount())
                .beneficiaryBank(bankInfo)
                .beneficiaryAddress(entity.getBeneficiaryAddress())
                .beneficiaryCountry(entity.getBeneficiaryCountry())
                .build();
        
        RemittanceDetails.RemittancePurpose purpose = RemittanceDetails.RemittancePurpose.builder()
                .purposeCode(entity.getPurposeCode())
                .purposeDescription(entity.getPurposeDescription())
                .contractNumber(entity.getContractNumber())
                .invoiceNumber(entity.getInvoiceNumber())
                .build();
        
        return RemittanceDetails.builder()
                .theirRefNo(entity.getTheirRefNo())
                .amount(entity.getAmount())
                .currency(entity.getCurrency())
                .exchangeRate(entity.getExchangeRate())
                .twdAmount(entity.getTwdAmount())
                .fee(entity.getFee())
                .totalDeduction(entity.getTotalDeduction())
                .beneficiaryInfo(beneficiaryInfo)
                .purpose(purpose)
                .sourceOfFunds(entity.getSourceOfFunds())
                .supportingDocuments(entity.getSupportingDocuments())
                .specialInstructions(entity.getSpecialInstructions())
                .build();
    }
    
    private ApprovalInfo.ApprovalLevel parseApprovalLevel(String approverRole) {
        if (approverRole == null) return null;
        
        try {
            return ApprovalInfo.ApprovalLevel.valueOf(approverRole);
        } catch (IllegalArgumentException e) {
            return ApprovalInfo.ApprovalLevel.OPERATOR; // 預設值
        }
    }
    
    // 輔助方法：獲取聯絡人資訊
    private String getContactPersonName(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getContactPerson() != null ? 
               corporateInfo.getContactPerson().getName() : null;
    }
    
    private String getContactPersonTitle(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getContactPerson() != null ? 
               corporateInfo.getContactPerson().getTitle() : null;
    }
    
    private String getContactPersonPhone(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getContactPerson() != null ? 
               corporateInfo.getContactPerson().getPhoneNumber() : null;
    }
    
    private String getContactPersonEmail(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getContactPerson() != null ? 
               corporateInfo.getContactPerson().getEmail() : null;
    }
    
    private String getContactPersonId(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getContactPerson() != null ? 
               corporateInfo.getContactPerson().getIdNumber() : null;
    }
    
    private String getCompanyAddress(CorporateInfo corporateInfo) {
        return corporateInfo != null && corporateInfo.getAddress() != null ? 
               corporateInfo.getAddress().getFormattedAddress() : null;
    }
    
    // 輔助方法：獲取收款人資訊
    private String getBeneficiaryName(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryName() : null;
    }
    
    private String getBeneficiaryNameLocal(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryNameLocal() : null;
    }
    
    private String getBeneficiaryAccount(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryAccount() : null;
    }
    
    private String getBeneficiaryBankName(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getBankName() : null;
    }
    
    private String getBeneficiaryBankCode(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getBankCode() : null;
    }
    
    private String getBeneficiarySwiftCode(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getSwiftCode() : null;
    }
    
    private String getBeneficiaryBankAddress(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getBankAddress() : null;
    }
    
    private String getBeneficiaryAddress(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryAddress() : null;
    }
    
    private String getBeneficiaryCountry(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryCountry() : null;
    }
    
    private String getCorrespondentBank(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getCorrespondentBank() : null;
    }
    
    private String getCorrespondentSwift(RemittanceDetails details) {
        return details != null && details.getBeneficiaryInfo() != null && 
               details.getBeneficiaryInfo().getBeneficiaryBank() != null ? 
               details.getBeneficiaryInfo().getBeneficiaryBank().getCorrespondentSwift() : null;
    }
    
    // 輔助方法：獲取用途資訊
    private String getPurposeCode(RemittanceDetails details) {
        return details != null && details.getPurpose() != null ? 
               details.getPurpose().getPurposeCode() : null;
    }
    
    private String getPurposeDescription(RemittanceDetails details) {
        return details != null && details.getPurpose() != null ? 
               details.getPurpose().getPurposeDescription() : null;
    }
    
    private String getContractNumber(RemittanceDetails details) {
        return details != null && details.getPurpose() != null ? 
               details.getPurpose().getContractNumber() : null;
    }
    
    private String getInvoiceNumber(RemittanceDetails details) {
        return details != null && details.getPurpose() != null ? 
               details.getPurpose().getInvoiceNumber() : null;
    }
    
    private String getApproverRole(ApprovalInfo approvalInfo) {
        return approvalInfo != null && approvalInfo.getApprovalLevel() != null ? 
               approvalInfo.getApprovalLevel().name() : null;
    }
    
    // 使用反射設置私有欄位（如果Domain Model的欄位是私有的）
    private void setApprovalInfo(CorporateRemittance domain, ApprovalInfo approvalInfo) {
        try {
            java.lang.reflect.Field field = CorporateRemittance.class.getDeclaredField("approvalInfo");
            field.setAccessible(true);
            field.set(domain, approvalInfo);
        } catch (Exception e) {
            // 如果反射失敗，則忽略
        }
    }
    
    private void setBaseFields(CorporateRemittance domain, CorporateRemittanceEntity entity) {
        try {
            // 設置基礎欄位
            setField(domain, "id", entity.getId());
            setField(domain, "createdAt", entity.getCreatedAt());
            setField(domain, "updatedAt", entity.getUpdatedAt());
            setField(domain, "version", entity.getVersion());
            setField(domain, "isDeleted", entity.getIsDeleted());
            setField(domain, "createdBy", entity.getCreatedBy());
            setField(domain, "updatedBy", entity.getUpdatedBy());
            setField(domain, "remark", entity.getRemark());
            setField(domain, "status", CorporateRemittance.ProcessingStatus.valueOf(entity.getStatus()));
            setField(domain, "applicationTime", entity.getApplicationTime());
            setField(domain, "completionTime", entity.getCompletionTime());
            setField(domain, "riskLevel", entity.getRiskLevel());
            setField(domain, "riskScore", entity.getRiskScore());
        } catch (Exception e) {
            // 如果反射失敗，則忽略
        }
    }
    
    private void setField(Object obj, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(obj, value);
        } catch (Exception e) {
            // 如果反射失敗，則忽略
        }
    }
}