package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業申請提交請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateApplicationSubmitRequest {
    
    /**
     * 統一編號
     */
    @NotNull(message = "統一編號不能為空")
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 企業名稱
     */
    @NotNull(message = "企業名稱不能為空")
    @Size(min = 1, max = 100, message = "企業名稱長度應在1-100字元之間")
    private String companyName;
    
    /**
     * 代表人身分證號
     */
    @NotNull(message = "代表人身分證號不能為空")
    @Pattern(regexp = "^[A-Z][12]\\d{8}$", message = "身分證號格式錯誤")
    private String representativeId;
    
    /**
     * 代表人姓名
     */
    @NotNull(message = "代表人姓名不能為空")
    @Size(min = 1, max = 50, message = "代表人姓名長度應在1-50字元之間")
    private String representativeName;
    
    /**
     * 選中的匯款列表
     */
    @NotEmpty(message = "選中的匯款列表不能為空")
    @Valid
    private List<SelectedRemittance> selectedRemittances;
    
    /**
     * 收款帳戶資訊
     */
    @NotNull(message = "收款帳戶資訊不能為空")
    @Valid
    private BankAccountInfo bankAccountInfo;
    
    /**
     * 確認項目
     */
    @NotNull(message = "確認項目不能為空")
    @Valid
    private ApplicationConfirmations confirmations;
    
    /**
     * 數位簽章
     */
    private String digitalSignature;
    
    /**
     * 提交時間
     */
    @NotNull(message = "提交時間不能為空")
    private LocalDateTime submitTime;
    
    /**
     * 申請備註
     */
    @Size(max = 500, message = "申請備註長度不能超過500字元")
    private String remarks;
    
    /**
     * 聯絡電話
     */
    @Pattern(regexp = "^\\d{2,4}-\\d{7,8}$|^09\\d{8}$", message = "聯絡電話格式錯誤")
    private String contactPhone;
    
    /**
     * 聯絡信箱
     */
    private String contactEmail;
    
    /**
     * 選中的匯款資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SelectedRemittance {
        /**
         * 匯款編號
         */
        @NotNull(message = "匯款編號不能為空")
        private String remittanceId;
        
        /**
         * 外部參考號
         */
        private String theirRefNo;
        
        /**
         * 是否選中
         */
        @Builder.Default
        private Boolean selected = true;
        
        /**
         * 備註
         */
        @Size(max = 200, message = "備註長度不能超過200字元")
        private String remarks;
    }
    
    /**
     * 銀行帳戶資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BankAccountInfo {
        /**
         * 銀行代碼
         */
        @NotNull(message = "銀行代碼不能為空")
        @Pattern(regexp = "^\\d{3}$", message = "銀行代碼格式錯誤")
        private String bankCode;
        
        /**
         * 銀行名稱
         */
        @NotNull(message = "銀行名稱不能為空")
        private String bankName;
        
        /**
         * 帳號
         */
        @NotNull(message = "帳號不能為空")
        @Size(min = 10, max = 20, message = "帳號長度應在10-20位之間")
        private String accountNumber;
        
        /**
         * 戶名
         */
        @NotNull(message = "戶名不能為空")
        private String accountName;
        
        /**
         * 分行代碼
         */
        private String branchCode;
        
        /**
         * 分行名稱
         */
        private String branchName;
    }
    
    /**
     * 申請確認項目
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApplicationConfirmations {
        /**
         * 確認資料正確
         */
        @NotNull(message = "必須確認資料正確")
        private Boolean dataAccuracy;
        
        /**
         * 確認條款同意
         */
        @NotNull(message = "必須確認條款同意")
        private Boolean termsAgreed;
        
        /**
         * 確認身份驗證
         */
        @NotNull(message = "必須確認身份驗證")
        private Boolean identityVerified;
        
        /**
         * 確認匯款資訊
         */
        @NotNull(message = "必須確認匯款資訊")
        private Boolean remittanceConfirmed;
        
        /**
         * 確認手續費說明
         */
        @NotNull(message = "必須確認手續費說明")
        private Boolean feesAcknowledged;
        
        /**
         * 確認風險告知
         */
        @NotNull(message = "必須確認風險告知")
        private Boolean riskDisclosed;
    }
}