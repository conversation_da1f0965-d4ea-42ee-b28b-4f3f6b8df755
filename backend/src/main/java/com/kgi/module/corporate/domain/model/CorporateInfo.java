package com.kgi.module.corporate.domain.model;

import lombok.Builder;
import lombok.Value;

/**
 * 企業資訊值對象
 * 包含企業客戶的基本資訊
 */
@Value
@Builder
public class CorporateInfo {
    
    String companyName;
    String companyNameEnglish;
    String businessType;
    String industryCode;
    ContactPerson contactPerson;
    CorporateAddress address;
    String telephoneNumber;
    String email;
    String website;
    
    /**
     * 聯絡人資訊
     */
    @Value
    @Builder
    public static class ContactPerson {
        String name;
        String title;
        String phoneNumber;
        String email;
        String idNumber;
        
        /**
         * 驗證聯絡人資訊完整性
         */
        public boolean isValid() {
            return name != null && !name.trim().isEmpty() &&
                   phoneNumber != null && !phoneNumber.trim().isEmpty() &&
                   email != null && !email.trim().isEmpty();
        }
    }
    
    /**
     * 企業地址資訊
     */
    @Value
    @Builder
    public static class CorporateAddress {
        String country;
        String city;
        String district;
        String street;
        String zipCode;
        String fullAddress;
        
        /**
         * 取得完整地址
         */
        public String getFormattedAddress() {
            if (fullAddress != null && !fullAddress.trim().isEmpty()) {
                return fullAddress;
            }
            
            StringBuilder sb = new StringBuilder();
            if (country != null) sb.append(country);
            if (city != null) sb.append(city);
            if (district != null) sb.append(district);
            if (street != null) sb.append(street);
            
            return sb.toString();
        }
        
        /**
         * 驗證地址完整性
         */
        public boolean isValid() {
            return (fullAddress != null && !fullAddress.trim().isEmpty()) ||
                   (city != null && !city.trim().isEmpty() && 
                    street != null && !street.trim().isEmpty());
        }
    }
    
    /**
     * 驗證企業資訊完整性
     */
    public boolean isValid() {
        return companyName != null && !companyName.trim().isEmpty() &&
               contactPerson != null && contactPerson.isValid() &&
               address != null && address.isValid() &&
               telephoneNumber != null && !telephoneNumber.trim().isEmpty();
    }
    
    /**
     * 檢查是否為金融業
     */
    public boolean isFinancialIndustry() {
        return industryCode != null && 
               (industryCode.startsWith("64") || // 金融中介業
                industryCode.startsWith("65") || // 保險業
                industryCode.startsWith("66"));  // 金融輔助業
    }
    
    /**
     * 檢查是否需要額外合規檢查
     */
    public boolean requiresAdditionalCompliance() {
        return isFinancialIndustry() || 
               isHighRiskIndustry();
    }
    
    /**
     * 檢查是否為高風險行業
     */
    private boolean isHighRiskIndustry() {
        if (industryCode == null) return false;
        
        // 高風險行業代碼（現金密集型、貿易等）
        String[] highRiskCodes = {
            "47", // 零售業
            "46", // 批發業
            "49", // 陸上運輸業
            "50", // 水上運輸業
            "51", // 航空運輸業
            "68", // 不動產業
            "77", // 租賃業
            "92", // 博弈業
        };
        
        for (String code : highRiskCodes) {
            if (industryCode.startsWith(code)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 取得遮罩後的聯絡人電話
     */
    public String getMaskedContactPhone() {
        if (contactPerson == null || contactPerson.getPhoneNumber() == null) {
            return null;
        }
        
        String phone = contactPerson.getPhoneNumber();
        if (phone.length() <= 4) {
            return phone;
        }
        
        return phone.substring(0, 2) + "****" + phone.substring(phone.length() - 2);
    }
    
    /**
     * 取得遮罩後的聯絡人身分證號
     */
    public String getMaskedContactId() {
        if (contactPerson == null || contactPerson.getIdNumber() == null) {
            return null;
        }
        
        String id = contactPerson.getIdNumber();
        if (id.length() != 10) {
            return id;
        }
        
        return id.substring(0, 3) + "****" + id.substring(7);
    }
}