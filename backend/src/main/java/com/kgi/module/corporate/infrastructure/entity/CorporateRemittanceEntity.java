package com.kgi.module.corporate.infrastructure.entity;

import com.kgi.core.domain.model.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 企業解款實體
 * 對應資料庫表 corporate_remittance
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "corporate_remittance")
@lombok.EqualsAndHashCode(callSuper = false)
public class CorporateRemittanceEntity extends BaseEntity {
    
    @Column(name = "remittance_id", unique = true, nullable = false, length = 50)
    private String remittanceId;
    
    @Column(name = "remit_ref_no", length = 16)
    private String remitRefNo;
    
    @Column(name = "unified_number", nullable = false, length = 8)
    private String unifiedNumber;
    
    // 企業資訊
    @Column(name = "company_name", nullable = false, length = 200)
    private String companyName;
    
    @Column(name = "company_name_english", length = 200)
    private String companyNameEnglish;
    
    @Column(name = "business_type", length = 100)
    private String businessType;
    
    @Column(name = "industry_code", length = 10)
    private String industryCode;
    
    // 聯絡人資訊
    @Column(name = "contact_person_name", length = 100)
    private String contactPersonName;
    
    @Column(name = "contact_person_title", length = 100)
    private String contactPersonTitle;
    
    @Column(name = "contact_person_phone", length = 20)
    private String contactPersonPhone;
    
    @Column(name = "contact_person_email", length = 100)
    private String contactPersonEmail;
    
    @Column(name = "contact_person_id", length = 20)
    private String contactPersonId;
    
    // 企業地址
    @Column(name = "company_address", length = 500)
    private String companyAddress;
    
    @Column(name = "company_telephone", length = 20)
    private String companyTelephone;
    
    @Column(name = "company_email", length = 100)
    private String companyEmail;
    
    // 解款詳情
    @Column(name = "their_ref_no", nullable = false, length = 100)
    private String theirRefNo;
    
    @Column(name = "payer_name", length = 70)
    private String payerName;
    
    @Column(name = "payer_country", length = 2)
    private String payerCountry;
    
    @Column(name = "amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;
    
    @Column(name = "currency", nullable = false, length = 3)
    private String currency;
    
    @Column(name = "exchange_rate", precision = 10, scale = 6)
    private BigDecimal exchangeRate;
    
    @Column(name = "twd_amount", precision = 15, scale = 2)
    private BigDecimal twdAmount;
    
    @Column(name = "fee", precision = 10, scale = 2)
    private BigDecimal fee;
    
    @Column(name = "total_deduction", precision = 10, scale = 2)
    private BigDecimal totalDeduction;
    
    // 收款人資訊
    @Column(name = "beneficiary_name", nullable = false, length = 200)
    private String beneficiaryName;
    
    @Column(name = "beneficiary_name_local", length = 200)
    private String beneficiaryNameLocal;
    
    @Column(name = "beneficiary_account", nullable = false, length = 50)
    private String beneficiaryAccount;
    
    @Column(name = "beneficiary_bank_name", length = 200)
    private String beneficiaryBankName;
    
    @Column(name = "beneficiary_bank_code", length = 20)
    private String beneficiaryBankCode;
    
    @Column(name = "beneficiary_swift_code", length = 15)
    private String beneficiarySwiftCode;
    
    @Column(name = "beneficiary_bank_address", length = 500)
    private String beneficiaryBankAddress;
    
    @Column(name = "beneficiary_address", length = 500)
    private String beneficiaryAddress;
    
    @Column(name = "beneficiary_country", length = 3)
    private String beneficiaryCountry;
    
    @Column(name = "correspondent_bank", length = 200)
    private String correspondentBank;
    
    @Column(name = "correspondent_swift", length = 15)
    private String correspondentSwift;
    
    // 用途資訊
    @Column(name = "purpose_code", length = 10)
    private String purposeCode;
    
    @Column(name = "purpose_description", length = 500)
    private String purposeDescription;
    
    @Column(name = "contract_number", length = 100)
    private String contractNumber;
    
    @Column(name = "invoice_number", length = 100)
    private String invoiceNumber;
    
    @Column(name = "source_of_funds", length = 10)
    private String sourceOfFunds;
    
    @Column(name = "supporting_documents", length = 1000)
    private String supportingDocuments;
    
    @Column(name = "special_instructions", length = 1000)
    private String specialInstructions;
    
    // TO API 必要欄位
    @Column(name = "payee_eng_name", length = 70)
    private String payeeEngName;
    
    @Column(name = "payee_id", length = 16)
    private String payeeID;
    
    @Column(name = "payee_tel", length = 20)
    private String payeeTel;
    
    @Column(name = "payee_mail", length = 40)
    private String payeeMail;
    
    @Column(name = "while_flag", length = 1)
    private String whileFlag;
    
    @Column(name = "memo", length = 40)
    private String memo;
    
    // 處理狀態
    @Column(name = "status", nullable = false, length = 20)
    private String status;
    
    @Column(name = "application_time", nullable = false)
    private LocalDateTime applicationTime;
    
    @Column(name = "completion_time")
    private LocalDateTime completionTime;
    
    // 審核資訊
    @Column(name = "approved")
    private Boolean approved;
    
    @Column(name = "approver", length = 50)
    private String approver;
    
    @Column(name = "approver_role", length = 50)
    private String approverRole;
    
    @Column(name = "approval_time")
    private LocalDateTime approvalTime;
    
    @Column(name = "approval_comments", length = 1000)
    private String approvalComments;
    
    @Column(name = "rejection_reason", length = 1000)
    private String rejectionReason;
    
    // 風險評估
    @Column(name = "risk_level", length = 10)
    private String riskLevel;
    
    @Column(name = "risk_score", precision = 5, scale = 2)
    private BigDecimal riskScore;
    
    @Column(name = "risk_factors", length = 1000)
    private String riskFactors;
    
    // 索引欄位
    @Column(name = "processor", length = 50)
    private String processor;
    
    @Column(name = "processing_notes", length = 2000)
    private String processingNotes;
}