package com.kgi.module.corporate.domain.model;

import com.kgi.core.domain.model.BaseEntity;
import com.kgi.core.domain.valueobject.UnifiedNumber;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 企業解款領域模型
 * 代表企業客戶的跨境解款業務實體
 */
@Getter
@NoArgsConstructor
public class CorporateRemittance extends BaseEntity {
    
    private String remittanceId;
    private UnifiedNumber unifiedNumber;
    private CorporateInfo corporateInfo;
    private RemittanceDetails remittanceDetails;
    private ApprovalInfo approvalInfo;
    private ProcessingStatus status;
    private LocalDateTime applicationTime;
    private LocalDateTime completionTime;
    private String riskLevel;
    private BigDecimal riskScore;
    
    @Builder
    public CorporateRemittance(String remittanceId, UnifiedNumber unifiedNumber, 
                               CorporateInfo corporateInfo, RemittanceDetails remittanceDetails) {
        this.remittanceId = remittanceId;
        this.unifiedNumber = unifiedNumber;
        this.corporateInfo = corporateInfo;
        this.remittanceDetails = remittanceDetails;
        this.status = ProcessingStatus.PENDING;
        this.applicationTime = LocalDateTime.now();
        this.riskLevel = "MEDIUM";
        this.riskScore = BigDecimal.ZERO;
    }
    
    /**
     * 提交審核
     */
    public void submitForApproval() {
        if (this.status != ProcessingStatus.PENDING) {
            throw new IllegalStateException("只有待處理狀態的解款可以提交審核");
        }
        this.status = ProcessingStatus.UNDER_REVIEW;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 審核通過
     */
    public void approve(String approver, String comments) {
        if (this.status != ProcessingStatus.UNDER_REVIEW) {
            throw new IllegalStateException("只有審核中的解款可以被核准");
        }
        this.approvalInfo = ApprovalInfo.builder()
                .approved(true)
                .approver(approver)
                .comments(comments)
                .approvalTime(LocalDateTime.now())
                .build();
        this.status = ProcessingStatus.APPROVED;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 審核拒絕
     */
    public void reject(String approver, String comments) {
        if (this.status != ProcessingStatus.UNDER_REVIEW) {
            throw new IllegalStateException("只有審核中的解款可以被拒絕");
        }
        this.approvalInfo = ApprovalInfo.builder()
                .approved(false)
                .approver(approver)
                .comments(comments)
                .approvalTime(LocalDateTime.now())
                .build();
        this.status = ProcessingStatus.REJECTED;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 執行解款
     */
    public void execute(String processor) {
        if (this.status != ProcessingStatus.APPROVED) {
            throw new IllegalStateException("只有已核准的解款可以被執行");
        }
        this.status = ProcessingStatus.PROCESSING;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 完成解款
     */
    public void complete() {
        if (this.status != ProcessingStatus.PROCESSING) {
            throw new IllegalStateException("只有處理中的解款可以被完成");
        }
        this.status = ProcessingStatus.COMPLETED;
        this.completionTime = LocalDateTime.now();
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 解款失敗
     */
    public void fail(String reason) {
        if (this.status != ProcessingStatus.PROCESSING) {
            throw new IllegalStateException("只有處理中的解款可以標記為失敗");
        }
        this.status = ProcessingStatus.FAILED;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 設定風險評估結果
     */
    public void setRiskAssessment(String riskLevel, BigDecimal riskScore) {
        this.riskLevel = riskLevel;
        this.riskScore = riskScore;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 檢查是否可以取消
     */
    public boolean canBeCancelled() {
        return this.status == ProcessingStatus.PENDING || 
               this.status == ProcessingStatus.UNDER_REVIEW;
    }
    
    /**
     * 取消解款
     */
    public void cancel(String reason) {
        if (!canBeCancelled()) {
            throw new IllegalStateException("當前狀態的解款無法取消");
        }
        this.status = ProcessingStatus.CANCELLED;
        setUpdatedAt(LocalDateTime.now());
    }
    
    /**
     * 檢查是否為高風險交易
     */
    public boolean isHighRisk() {
        return "HIGH".equals(this.riskLevel) || 
               (this.riskScore != null && this.riskScore.compareTo(new BigDecimal("80")) >= 0);
    }
    
    /**
     * 檢查是否需要主管審核
     */
    public boolean requiresManagerApproval() {
        return isHighRisk() || 
               this.remittanceDetails.getAmount().compareTo(new BigDecimal("500000")) > 0;
    }
    
    /**
     * 取得處理總時間（小時）
     */
    public Long getProcessingTimeHours() {
        if (this.completionTime == null) {
            return null;
        }
        return java.time.Duration.between(this.applicationTime, this.completionTime).toHours();
    }
    
    /**
     * 檢查是否超過SLA時限
     */
    public boolean isSlaBreached() {
        if (this.status == ProcessingStatus.COMPLETED || 
            this.status == ProcessingStatus.FAILED) {
            return false;
        }
        
        // 企業解款SLA：4小時內完成
        LocalDateTime slaDeadline = this.applicationTime.plusHours(4);
        return LocalDateTime.now().isAfter(slaDeadline);
    }
    
    /**
     * 處理狀態枚舉
     */
    public enum ProcessingStatus {
        PENDING("待處理"),
        UNDER_REVIEW("審核中"),
        APPROVED("已核准"),
        REJECTED("已拒絕"),
        PROCESSING("處理中"),
        COMPLETED("已完成"),
        FAILED("失敗"),
        CANCELLED("已取消");
        
        private final String description;
        
        ProcessingStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}