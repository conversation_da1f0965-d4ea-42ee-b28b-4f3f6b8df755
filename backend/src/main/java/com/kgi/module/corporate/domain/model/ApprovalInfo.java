package com.kgi.module.corporate.domain.model;

import lombok.Builder;
import lombok.Value;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 審核資訊值對象
 * 記錄解款審核的相關資訊
 */
@Value
@Builder
public class ApprovalInfo {
    
    boolean approved;
    String approver;
    String approverRole;
    LocalDateTime approvalTime;
    String comments;
    String rejectionReason;
    ApprovalLevel approvalLevel;
    List<ApprovalHistory> approvalHistory;
    
    /**
     * 審核等級枚舉
     */
    public enum ApprovalLevel {
        OPERATOR("作業人員", 1),
        SUPERVISOR("主管", 2),
        MANAGER("經理", 3),
        SENIOR_MANAGER("資深經理", 4),
        COMPLIANCE_OFFICER("法遵人員", 5),
        DIRECTOR("總監", 6);
        
        private final String description;
        private final int level;
        
        ApprovalLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getLevel() {
            return level;
        }
        
        /**
         * 檢查是否有足夠權限
         */
        public boolean canApprove(ApprovalLevel requiredLevel) {
            return this.level >= requiredLevel.level;
        }
    }
    
    /**
     * 審核歷史記錄
     */
    @Value
    @Builder
    public static class ApprovalHistory {
        String approver;
        ApprovalLevel approverLevel;
        String action;
        String comments;
        LocalDateTime timestamp;
        
        /**
         * 操作類型
         */
        public enum Action {
            SUBMITTED("提交"),
            APPROVED("核准"),
            REJECTED("拒絕"),
            RETURNED("退回"),
            ESCALATED("升級"),
            COMMENTED("備註");
            
            private final String description;
            
            Action(String description) {
                this.description = description;
            }
            
            public String getDescription() {
                return description;
            }
        }
    }
    
    /**
     * 檢查是否已完成審核
     */
    public boolean isCompleted() {
        return approvalTime != null;
    }
    
    /**
     * 取得審核結果描述
     */
    public String getApprovalStatusDescription() {
        if (!isCompleted()) {
            return "審核中";
        }
        
        return approved ? "已核准" : "已拒絕";
    }
    
    /**
     * 檢查審核權限是否足夠
     */
    public boolean hasRequiredApprovalLevel(ApprovalLevel requiredLevel) {
        return approvalLevel != null && 
               approvalLevel.canApprove(requiredLevel);
    }
    
    /**
     * 取得審核處理時間（分鐘）
     */
    public Long getProcessingTimeMinutes() {
        if (approvalTime == null || approvalHistory == null || approvalHistory.isEmpty()) {
            return null;
        }
        
        LocalDateTime submitTime = approvalHistory.stream()
                .filter(h -> "SUBMITTED".equals(h.getAction()))
                .map(ApprovalHistory::getTimestamp)
                .findFirst()
                .orElse(null);
        
        if (submitTime == null) {
            return null;
        }
        
        return java.time.Duration.between(submitTime, approvalTime).toMinutes();
    }
    
    /**
     * 檢查是否超過審核時限
     */
    public boolean isApprovalOverdue() {
        if (isCompleted()) {
            return false;
        }
        
        if (approvalHistory == null || approvalHistory.isEmpty()) {
            return false;
        }
        
        LocalDateTime submitTime = approvalHistory.stream()
                .filter(h -> "SUBMITTED".equals(h.getAction()))
                .map(ApprovalHistory::getTimestamp)
                .findFirst()
                .orElse(null);
        
        if (submitTime == null) {
            return false;
        }
        
        // 審核時限：2小時
        LocalDateTime deadline = submitTime.plusHours(2);
        return LocalDateTime.now().isAfter(deadline);
    }
    
    /**
     * 取得遮罩後的審核人員ID
     */
    public String getMaskedApprover() {
        if (approver == null || approver.length() < 4) {
            return approver;
        }
        
        return approver.substring(0, 2) + "***" + approver.substring(approver.length() - 2);
    }
    
    /**
     * 檢查是否需要上級審核
     */
    public boolean requiresEscalation() {
        return !approved && rejectionReason != null && 
               rejectionReason.contains("需要上級審核");
    }
    
    /**
     * 建立審核記錄
     */
    public static ApprovalInfo createForApproval(String approver, ApprovalLevel level, 
                                                 String comments) {
        return ApprovalInfo.builder()
                .approved(true)
                .approver(approver)
                .approvalLevel(level)
                .approvalTime(LocalDateTime.now())
                .comments(comments)
                .build();
    }
    
    /**
     * 建立拒絕記錄
     */
    public static ApprovalInfo createForRejection(String approver, ApprovalLevel level, 
                                                  String rejectionReason) {
        return ApprovalInfo.builder()
                .approved(false)
                .approver(approver)
                .approvalLevel(level)
                .approvalTime(LocalDateTime.now())
                .rejectionReason(rejectionReason)
                .build();
    }
}