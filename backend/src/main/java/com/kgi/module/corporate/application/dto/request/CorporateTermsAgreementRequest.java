package com.kgi.module.corporate.application.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 企業條款同意請求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateTermsAgreementRequest {
    
    /**
     * 統一編號
     */
    @NotNull(message = "統一編號不能為空")
    @Pattern(regexp = "^\\d{8}$", message = "統一編號格式錯誤")
    private String unifiedNumber;
    
    /**
     * 同意的條款版本
     */
    @NotNull(message = "條款版本不能為空")
    @Size(min = 1, max = 20, message = "條款版本長度應在1-20字元之間")
    private String termsVersion;
    
    /**
     * 同意時間
     */
    @NotNull(message = "同意時間不能為空")
    private LocalDateTime agreedAt;
    
    /**
     * IP位址
     */
    private String ipAddress;
    
    /**
     * 使用者代理
     */
    private String userAgent;
    
    /**
     * 代表人身分證號
     */
    @Pattern(regexp = "^[A-Z][12]\\d{8}$", message = "身分證號格式錯誤")
    private String representativeId;
    
    /**
     * 同意方式
     */
    @Builder.Default
    private String agreementMethod = "DIGITAL";
    
    /**
     * 企業名稱
     */
    private String companyName;
    
    /**
     * 代表人姓名
     */
    private String representativeName;
}