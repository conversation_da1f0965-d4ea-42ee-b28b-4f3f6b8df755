package com.kgi.module.corporate.domain.service;

import com.kgi.core.domain.exception.BusinessException;
import com.kgi.core.domain.valueobject.UnifiedNumber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * 企業資訊服務
 * 處理企業基本資料驗證與查詢
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyInfoService {
    
    /**
     * 驗證企業統一編號
     */
    public Map<String, Object> validateUnifiedNumber(String unifiedNumber) {
        log.info("驗證企業統一編號: {}", maskUnifiedNumber(unifiedNumber));
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 1. 格式驗證
            if (!UnifiedNumber.isValid(unifiedNumber)) {
                result.put("valid", false);
                result.put("errorCode", "INVALID_FORMAT");
                result.put("errorMessage", "統一編號格式錯誤");
                return result;
            }
            
            // 2. 檢查碼驗證
            if (!validateChecksum(unifiedNumber)) {
                result.put("valid", false);
                result.put("errorCode", "INVALID_CHECKSUM");
                result.put("errorMessage", "統一編號檢查碼錯誤");
                return result;
            }
            
            // 3. 黑名單檢查
            if (isBlacklisted(unifiedNumber)) {
                result.put("valid", false);
                result.put("errorCode", "BLACKLISTED");
                result.put("errorMessage", "企業列入黑名單");
                return result;
            }
            
            result.put("valid", true);
            result.put("unifiedNumber", unifiedNumber);
            result.put("validationTime", LocalDateTime.now());
            
            log.info("統一編號驗證成功: {}", maskUnifiedNumber(unifiedNumber));
            return result;
            
        } catch (Exception e) {
            log.error("統一編號驗證異常: {}", e.getMessage(), e);
            throw new BusinessException("UNIFIED_NUMBER_VALIDATION_ERROR", "統一編號驗證異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢企業基本資料
     */
    public Map<String, Object> getCompanyInfo(String unifiedNumber) {
        log.info("查詢企業基本資料: {}", maskUnifiedNumber(unifiedNumber));
        
        try {
            // 1. 驗證統一編號
            Map<String, Object> validation = validateUnifiedNumber(unifiedNumber);
            if (!(Boolean) validation.get("valid")) {
                throw new BusinessException("INVALID_UNIFIED_NUMBER", "統一編號無效");
            }
            
            // 2. 從政府資料庫查詢企業資訊
            Map<String, Object> companyData = queryGovernmentDatabase(unifiedNumber);
            
            // 3. 從內部資料庫查詢歷史資料
            Map<String, Object> internalData = queryInternalDatabase(unifiedNumber);
            
            // 4. 整合資料
            Map<String, Object> result = new HashMap<>();
            result.put("unifiedNumber", unifiedNumber);
            result.put("companyName", companyData.get("companyName"));
            result.put("companyNameEn", companyData.get("companyNameEn"));
            result.put("representativeName", companyData.get("representativeName"));
            result.put("establishDate", companyData.get("establishDate"));
            result.put("capital", companyData.get("capital"));
            result.put("address", companyData.get("address"));
            result.put("businessScope", companyData.get("businessScope"));
            result.put("status", companyData.get("status"));
            result.put("lastUpdateDate", companyData.get("lastUpdateDate"));
            
            // 內部資料
            result.put("riskLevel", internalData.get("riskLevel"));
            result.put("creditRating", internalData.get("creditRating"));
            result.put("relationshipManager", internalData.get("relationshipManager"));
            result.put("lastTransactionDate", internalData.get("lastTransactionDate"));
            result.put("totalTransactionAmount", internalData.get("totalTransactionAmount"));
            result.put("accountStatus", internalData.get("accountStatus"));
            
            result.put("queryTime", LocalDateTime.now());
            
            log.info("企業資料查詢成功: {} - {}", 
                    maskUnifiedNumber(unifiedNumber), 
                    companyData.get("companyName"));
            
            return result;
            
        } catch (BusinessException e) {
            log.error("企業資料查詢失敗: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("企業資料查詢異常: {}", e.getMessage(), e);
            throw new BusinessException("COMPANY_INFO_QUERY_ERROR", "企業資料查詢異常: " + e.getMessage());
        }
    }
    
    /**
     * 驗證企業營運狀態
     */
    public Map<String, Object> validateBusinessStatus(String unifiedNumber) {
        log.info("驗證企業營運狀態: {}", maskUnifiedNumber(unifiedNumber));
        
        try {
            Map<String, Object> companyInfo = getCompanyInfo(unifiedNumber);
            String status = (String) companyInfo.get("status");
            
            Map<String, Object> result = new HashMap<>();
            result.put("unifiedNumber", unifiedNumber);
            result.put("status", status);
            result.put("active", "ACTIVE".equals(status));
            result.put("validationTime", LocalDateTime.now());
            
            // 檢查營運狀態
            switch (status) {
                case "ACTIVE":
                    result.put("canTrade", true);
                    result.put("statusDescription", "正常營運中");
                    break;
                case "SUSPENDED":
                    result.put("canTrade", false);
                    result.put("statusDescription", "營運暫停");
                    result.put("reason", "未依規定申報");
                    break;
                case "DISSOLVED":
                    result.put("canTrade", false);
                    result.put("statusDescription", "已解散");
                    result.put("reason", "公司解散登記");
                    break;
                case "REVOKED":
                    result.put("canTrade", false);
                    result.put("statusDescription", "營業許可已撤銷");
                    result.put("reason", "違反相關法規");
                    break;
                default:
                    result.put("canTrade", false);
                    result.put("statusDescription", "狀態不明");
            }
            
            log.info("企業營運狀態驗證完成: {} - {}", 
                    maskUnifiedNumber(unifiedNumber), 
                    result.get("statusDescription"));
            
            return result;
            
        } catch (Exception e) {
            log.error("企業營運狀態驗證異常: {}", e.getMessage(), e);
            throw new BusinessException("BUSINESS_STATUS_VALIDATION_ERROR", "營運狀態驗證異常: " + e.getMessage());
        }
    }
    
    /**
     * 檢查企業授權代表人
     */
    public Map<String, Object> validateAuthorizedRepresentative(String unifiedNumber, String representativeName, String idCard) {
        log.info("檢查企業授權代表人: {} - {}", 
                maskUnifiedNumber(unifiedNumber), 
                maskName(representativeName));
        
        try {
            Map<String, Object> companyInfo = getCompanyInfo(unifiedNumber);
            String officialRepresentative = (String) companyInfo.get("representativeName");
            
            Map<String, Object> result = new HashMap<>();
            result.put("unifiedNumber", unifiedNumber);
            result.put("representativeName", representativeName);
            result.put("idCard", maskIdCard(idCard));
            result.put("validationTime", LocalDateTime.now());
            
            // 檢查是否為法定代表人
            boolean isOfficialRepresentative = officialRepresentative.equals(representativeName);
            result.put("isOfficialRepresentative", isOfficialRepresentative);
            
            if (isOfficialRepresentative) {
                result.put("authorized", true);
                result.put("authorizationType", "法定代表人");
                result.put("authorityLevel", "FULL");
            } else {
                // 檢查是否有授權書
                boolean hasAuthorization = checkAuthorizationDocument(unifiedNumber, representativeName, idCard);
                result.put("authorized", hasAuthorization);
                
                if (hasAuthorization) {
                    result.put("authorizationType", "授權代表人");
                    result.put("authorityLevel", "LIMITED");
                    result.put("authorizationDocument", "存在有效授權書");
                } else {
                    result.put("authorizationType", "無授權");
                    result.put("authorityLevel", "NONE");
                    result.put("reason", "非法定代表人且無有效授權書");
                }
            }
            
            log.info("企業授權代表人檢查完成: {} - 授權狀態: {}", 
                    maskUnifiedNumber(unifiedNumber), 
                    result.get("authorized"));
            
            return result;
            
        } catch (Exception e) {
            log.error("企業授權代表人檢查異常: {}", e.getMessage(), e);
            throw new BusinessException("REPRESENTATIVE_VALIDATION_ERROR", "授權代表人檢查異常: " + e.getMessage());
        }
    }
    
    /**
     * 查詢企業風險等級
     */
    public Map<String, Object> getCompanyRiskAssessment(String unifiedNumber) {
        log.info("查詢企業風險等級: {}", maskUnifiedNumber(unifiedNumber));
        
        try {
            // 1. 取得企業基本資料
            Map<String, Object> companyInfo = getCompanyInfo(unifiedNumber);
            
            // 2. 計算風險分數
            int riskScore = calculateRiskScore(companyInfo);
            
            // 3. 判定風險等級
            String riskLevel = determineRiskLevel(riskScore);
            
            Map<String, Object> result = new HashMap<>();
            result.put("unifiedNumber", unifiedNumber);
            result.put("riskScore", riskScore);
            result.put("riskLevel", riskLevel);
            result.put("assessmentTime", LocalDateTime.now());
            
            // 風險因子分析
            List<Map<String, Object>> riskFactors = analyzeRiskFactors(companyInfo);
            result.put("riskFactors", riskFactors);
            
            // 建議措施
            result.put("recommendations", getRecommendations(riskLevel));
            
            log.info("企業風險等級評估完成: {} - 風險等級: {} (分數: {})", 
                    maskUnifiedNumber(unifiedNumber), riskLevel, riskScore);
            
            return result;
            
        } catch (Exception e) {
            log.error("企業風險評估異常: {}", e.getMessage(), e);
            throw new BusinessException("RISK_ASSESSMENT_ERROR", "風險評估異常: " + e.getMessage());
        }
    }
    
    // ==================== 私有方法 ====================
    
    private boolean validateChecksum(String unifiedNumber) {
        // 台灣統一編號檢查碼演算法
        int[] weights = {1, 2, 1, 2, 1, 2, 4, 1};
        int sum = 0;
        
        for (int i = 0; i < 8; i++) {
            int digit = Character.getNumericValue(unifiedNumber.charAt(i));
            int product = digit * weights[i];
            sum += (product / 10) + (product % 10);
        }
        
        int checkDigit = (sum % 10 == 0) ? 0 : (10 - (sum % 10));
        int lastDigit = Character.getNumericValue(unifiedNumber.charAt(7));
        
        return checkDigit == lastDigit;
    }
    
    private boolean isBlacklisted(String unifiedNumber) {
        // 模擬黑名單檢查
        String[] blacklist = {"12345678", "87654321", "11111111"};
        for (String blocked : blacklist) {
            if (blocked.equals(unifiedNumber)) {
                return true;
            }
        }
        return false;
    }
    
    private Map<String, Object> queryGovernmentDatabase(String unifiedNumber) {
        // 模擬政府資料庫查詢
        Map<String, Object> data = new HashMap<>();
        data.put("companyName", "測試科技股份有限公司");
        data.put("companyNameEn", "Test Technology Co., Ltd.");
        data.put("representativeName", "王大明");
        data.put("establishDate", LocalDate.of(2020, 1, 15));
        data.put("capital", 50000000L); // 5000萬
        data.put("address", "台北市信義區信義路五段7號");
        data.put("businessScope", "軟體設計服務業、資料處理服務業");
        data.put("status", "ACTIVE");
        data.put("lastUpdateDate", LocalDate.now().minusDays(30));
        return data;
    }
    
    private Map<String, Object> queryInternalDatabase(String unifiedNumber) {
        // 模擬內部資料庫查詢
        Map<String, Object> data = new HashMap<>();
        data.put("riskLevel", "MEDIUM");
        data.put("creditRating", "A-");
        data.put("relationshipManager", "李專員");
        data.put("lastTransactionDate", LocalDate.now().minusDays(7));
        data.put("totalTransactionAmount", 125000000L); // 1.25億
        data.put("accountStatus", "ACTIVE");
        return data;
    }
    
    private boolean checkAuthorizationDocument(String unifiedNumber, String representativeName, String idCard) {
        // 模擬授權書檢查
        return Math.random() > 0.3; // 70%的機率有授權書
    }
    
    private int calculateRiskScore(Map<String, Object> companyInfo) {
        int score = 50; // 基礎分數
        
        // 成立時間因子
        LocalDate establishDate = (LocalDate) companyInfo.get("establishDate");
        int yearsInBusiness = LocalDate.now().getYear() - establishDate.getYear();
        if (yearsInBusiness < 1) score += 20;
        else if (yearsInBusiness < 3) score += 10;
        else if (yearsInBusiness > 10) score -= 10;
        
        // 資本額因子
        Long capital = (Long) companyInfo.get("capital");
        if (capital < ********) score += 15; // 低於1000萬
        else if (capital > *********) score -= 15; // 高於1億
        
        // 營運狀態因子
        String status = (String) companyInfo.get("status");
        if (!"ACTIVE".equals(status)) score += 30;
        
        // 交易歷史因子
        Long totalAmount = (Long) companyInfo.get("totalTransactionAmount");
        if (totalAmount == null || totalAmount == 0) score += 20;
        else if (totalAmount > *********) score -= 20; // 超過5億交易量
        
        return Math.max(0, Math.min(100, score));
    }
    
    private String determineRiskLevel(int riskScore) {
        if (riskScore >= 80) return "HIGH";
        if (riskScore >= 50) return "MEDIUM";
        return "LOW";
    }
    
    private List<Map<String, Object>> analyzeRiskFactors(Map<String, Object> companyInfo) {
        List<Map<String, Object>> factors = new ArrayList<>();
        
        LocalDate establishDate = (LocalDate) companyInfo.get("establishDate");
        int yearsInBusiness = LocalDate.now().getYear() - establishDate.getYear();
        
        if (yearsInBusiness < 3) {
            Map<String, Object> factor = new HashMap<>();
            factor.put("type", "BUSINESS_HISTORY");
            factor.put("level", "MEDIUM");
            factor.put("description", "營運時間較短，風險較高");
            factors.add(factor);
        }
        
        Long capital = (Long) companyInfo.get("capital");
        if (capital < ********) {
            Map<String, Object> factor = new HashMap<>();
            factor.put("type", "CAPITAL");
            factor.put("level", "HIGH");
            factor.put("description", "資本額偏低，財務風險較高");
            factors.add(factor);
        }
        
        return factors;
    }
    
    private List<String> getRecommendations(String riskLevel) {
        List<String> recommendations = new ArrayList<>();
        
        switch (riskLevel) {
            case "HIGH":
                recommendations.add("需要主管審核");
                recommendations.add("限制單筆交易金額");
                recommendations.add("要求提供額外擔保");
                break;
            case "MEDIUM":
                recommendations.add("進行人工審核");
                recommendations.add("定期檢視交易狀況");
                break;
            case "LOW":
                recommendations.add("可進行標準流程處理");
                break;
        }
        
        return recommendations;
    }
    
    private String maskUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return unifiedNumber;
        }
        return unifiedNumber.substring(0, 2) + "****" + unifiedNumber.substring(6);
    }
    
    private String maskName(String name) {
        if (name == null || name.length() < 2) {
            return name;
        }
        return name.charAt(0) + "*".repeat(name.length() - 1);
    }
    
    private String maskIdCard(String idCard) {
        if (idCard == null || idCard.length() != 10) {
            return idCard;
        }
        return idCard.substring(0, 2) + "******" + idCard.substring(8);
    }
}