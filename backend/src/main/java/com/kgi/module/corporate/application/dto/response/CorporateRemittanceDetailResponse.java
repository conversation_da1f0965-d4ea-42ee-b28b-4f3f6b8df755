package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 企業匯款詳情回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorporateRemittanceDetailResponse {
    
    /**
     * 匯款編號
     */
    private String remittanceId;
    
    /**
     * 外部參考號
     */
    private String theirRefNo;
    
    /**
     * 付款人資訊
     */
    private PayerInfo payerInfo;
    
    /**
     * 受益人資訊
     */
    private BeneficiaryInfo beneficiaryInfo;
    
    /**
     * 匯款金額資訊
     */
    private AmountInfo amountInfo;
    
    /**
     * 匯款詳細資訊
     */
    private RemittanceDetails remittanceDetails;
    
    /**
     * 處理狀態
     */
    private String processingStatus;
    
    /**
     * 狀態描述
     */
    private String statusDescription;
    
    /**
     * 是否可申請解款
     */
    private Boolean canApplyRemittance;
    
    /**
     * 限制原因
     */
    private String restrictionReason;
    
    /**
     * 風險評估
     */
    private RiskAssessment riskAssessment;
    
    /**
     * 付款人資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayerInfo {
        /**
         * 付款人名稱
         */
        private String name;
        
        /**
         * 付款人地址
         */
        private String address;
        
        /**
         * 付款人國家
         */
        private String country;
        
        /**
         * 付款人銀行
         */
        private String bank;
        
        /**
         * 付款人帳號
         */
        private String accountNumber;
    }
    
    /**
     * 受益人資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BeneficiaryInfo {
        /**
         * 受益人名稱
         */
        private String name;
        
        /**
         * 受益人英文名稱
         */
        private String englishName;
        
        /**
         * 受益人統一編號
         */
        private String unifiedNumber;
        
        /**
         * 受益人地址
         */
        private String address;
        
        /**
         * 受益人銀行代碼
         */
        private String bankCode;
        
        /**
         * 受益人銀行名稱
         */
        private String bankName;
        
        /**
         * 受益人帳號
         */
        private String accountNumber;
    }
    
    /**
     * 金額資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AmountInfo {
        /**
         * 原始金額
         */
        private BigDecimal originalAmount;
        
        /**
         * 原始幣別
         */
        private String originalCurrency;
        
        /**
         * 台幣金額
         */
        private BigDecimal twdAmount;
        
        /**
         * 匯率
         */
        private BigDecimal exchangeRate;
        
        /**
         * 手續費
         */
        private BigDecimal processingFee;
        
        /**
         * 郵電費
         */
        private BigDecimal telegraphicFee;
        
        /**
         * 其他費用
         */
        private BigDecimal otherFees;
        
        /**
         * 實際解款金額
         */
        private BigDecimal netAmount;
    }
    
    /**
     * 匯款詳細資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RemittanceDetails {
        /**
         * 匯款性質
         */
        private String remittanceNature;
        
        /**
         * 匯款目的
         */
        private String purpose;
        
        /**
         * 收款日期
         */
        private LocalDateTime receivedDate;
        
        /**
         * 到期日
         */
        private LocalDateTime expiryDate;
        
        /**
         * SWIFT代碼
         */
        private String swiftCode;
        
        /**
         * 附言
         */
        private String message;
        
        /**
         * 匯款備註
         */
        private List<String> remarks;
    }
    
    /**
     * 風險評估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        /**
         * 風險等級
         */
        private String riskLevel;
        
        /**
         * 風險分數
         */
        private Integer riskScore;
        
        /**
         * 風險因子
         */
        private List<String> riskFactors;
        
        /**
         * 是否需要加強審查
         */
        private Boolean requiresEnhancedReview;
        
        /**
         * 評估時間
         */
        private LocalDateTime assessmentTime;
    }
}