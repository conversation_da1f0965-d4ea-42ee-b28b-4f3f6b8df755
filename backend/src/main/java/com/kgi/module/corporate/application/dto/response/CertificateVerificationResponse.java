package com.kgi.module.corporate.application.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 工商憑證驗證回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CertificateVerificationResponse {
    
    /**
     * 驗證是否成功
     */
    private Boolean success;
    
    /**
     * 錯誤代碼
     */
    private String errorCode;
    
    /**
     * 錯誤訊息
     */
    private String errorMessage;
    
    /**
     * 憑證資訊
     */
    private CertificateInfo certificateInfo;
    
    /**
     * 驗證時間
     */
    private LocalDateTime verificationTime;
    
    /**
     * 憑證有效期限
     */
    private LocalDateTime validUntil;
    
    /**
     * 簽章資訊
     */
    private SignatureInfo signatureInfo;
    
    /**
     * 驗證結果詳細資訊
     */
    private Map<String, Object> verificationDetails;
    
    /**
     * 憑證資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CertificateInfo {
        
        /**
         * 憑證主體
         */
        private String subject;
        
        /**
         * 憑證發行者
         */
        private String issuer;
        
        /**
         * 統一編號
         */
        private String unifiedNumber;
        
        /**
         * 企業名稱
         */
        private String companyName;
        
        /**
         * 憑證序號
         */
        private String serialNumber;
        
        /**
         * 憑證生效時間
         */
        private LocalDateTime validFrom;
        
        /**
         * 憑證到期時間
         */
        private LocalDateTime validUntil;
        
        /**
         * 金鑰用途
         */
        private List<String> keyUsage;
        
        /**
         * 憑證演算法
         */
        private String algorithm;
        
        /**
         * 憑證指紋
         */
        private String fingerprint;
    }
    
    /**
     * 簽章資訊內部類別
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SignatureInfo {
        
        /**
         * 簽章演算法
         */
        private String algorithm;
        
        /**
         * 簽章值
         */
        private String signatureValue;
        
        /**
         * 簽章時間
         */
        private LocalDateTime signatureTime;
        
        /**
         * 簽章者資訊
         */
        private String signerInfo;
        
        /**
         * 資料雜湊值
         */
        private String dataHash;
        
        /**
         * 簽章驗證狀態
         */
        private Boolean verified;
    }
}