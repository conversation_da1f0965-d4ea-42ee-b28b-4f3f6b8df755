package com.kgi.module.query.application.usecase;

import com.kgi.module.query.application.dto.response.TransactionDetailsResponse;
import com.kgi.module.query.domain.service.RemittanceQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 查詢交易詳細資訊用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetTransactionDetailsUseCase {
    
    private final RemittanceQueryService queryService;
    
    public TransactionDetailsResponse execute(String caseNo) {
        log.info("執行交易詳細資訊查詢: caseNo={}", caseNo);
        
        // 1. 查詢交易基本資料
        Map<String, Object> transactionData = queryService.queryTransactionData(caseNo);
        
        if (transactionData == null || transactionData.isEmpty()) {
            return TransactionDetailsResponse.builder()
                    .caseNo(caseNo)
                    .found(false)
                    .message("查無此案件編號的交易資料")
                    .queryTime(LocalDateTime.now().toString())
                    .build();
        }
        
        // 2. 查詢詳細狀態資訊
        Map<String, Object> statusDetails = queryService.getTransactionStatusDetails(caseNo);
        
        // 3. 查詢處理歷程
        List<Map<String, Object>> processingHistory = queryService.getProcessingHistory(caseNo);
        
        // 4. 建立回應
        return TransactionDetailsResponse.builder()
                .caseNo(caseNo)
                .found(true)
                .transactionData(transactionData)
                .statusDetails(statusDetails)
                .processingHistory(processingHistory)
                .queryTime(LocalDateTime.now().toString())
                .build();
    }
}