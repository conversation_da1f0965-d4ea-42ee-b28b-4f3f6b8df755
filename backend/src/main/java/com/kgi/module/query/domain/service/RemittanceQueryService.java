package com.kgi.module.query.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 匯款查詢服務
 * 負責處理匯款交易的查詢邏輯
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RemittanceQueryService {
    
    /**
     * 根據案件編號查詢交易資料
     */
    public Map<String, Object> queryTransactionData(String caseNo) {
        log.info("查詢交易資料: caseNo={}", caseNo);
        
        // 模擬從資料庫查詢交易資料
        if ("IBR2025010101".equals(caseNo)) {
            Map<String, Object> data = new HashMap<>();
            data.put("caseNo", caseNo);
            data.put("customerType", "INDIVIDUAL");
            data.put("status", "COMPLETED");
            data.put("remitRefNo", "RM2025010101");
            data.put("theirRefNo", "CB2025010101");
            data.put("payerName", "JOHN DOE");
            data.put("payeeName", "王大明");
            data.put("payeeId", "A123456789");
            data.put("currency", "USD");
            data.put("amount", 50000);
            data.put("payeeAccount", "**********");
            data.put("payeeBankCode", "812");
            data.put("remittanceDate", "2025-01-01");
            data.put("createdTime", LocalDateTime.now().minusDays(2).toString());
            data.put("lastUpdatedTime", LocalDateTime.now().minusHours(1).toString());
            return data;
        }
        return null;
    }
    
    /**
     * 根據不同條件搜尋交易
     */
    public List<Map<String, Object>> searchTransactions(String searchType, String searchValue) {
        log.info("搜尋交易: searchType={}, searchValue={}", searchType, searchValue);
        
        if ("CASE_NO".equals(searchType) && "IBR2025010101".equals(searchValue)) {
            Map<String, Object> transaction = queryTransactionData("IBR2025010101");
            return transaction != null ? List.of(transaction) : List.of();
        }
        
        if ("CUSTOMER_ID".equals(searchType)) {
            return List.of(
                Map.of(
                    "caseNo", "IBR2025010101",
                    "status", "COMPLETED",
                    "remittanceDate", "2025-01-01",
                    "amount", 50000,
                    "currency", "USD"
                ),
                Map.of(
                    "caseNo", "IBR2024123101", 
                    "status", "PROCESSING",
                    "remittanceDate", "2024-12-31",
                    "amount", 25000,
                    "currency", "EUR"
                )
            );
        }
        
        return List.of();
    }
    
    /**
     * 取得客戶的交易歷史
     */
    public List<Map<String, Object>> getCustomerTransactionHistory(
            String customerId, String customerType, int page, int pageSize) {
        
        log.info("查詢客戶交易歷史: customerId={}, customerType={}", 
                maskId(customerId, customerType), customerType);
        
        // 模擬資料
        return List.of(
            Map.of(
                "caseNo", "IBR2025010101",
                "status", "COMPLETED",
                "statusDesc", "已完成",
                "remittanceDate", "2025-01-01",
                "amount", 50000,
                "currency", "USD",
                "payerName", "JOHN DOE"
            ),
            Map.of(
                "caseNo", "IBR2024123101",
                "status", "PROCESSING", 
                "statusDesc", "處理中",
                "remittanceDate", "2024-12-31",
                "amount", 25000,
                "currency", "EUR",
                "payerName", "JANE SMITH"
            )
        );
    }
    
    /**
     * 取得交易處理歷程
     */
    public List<Map<String, Object>> getProcessingHistory(String caseNo) {
        return List.of(
            Map.of(
                "step", "資料接收",
                "status", "COMPLETED",
                "description", "跨境平台匯款資料已接收",
                "completedTime", LocalDateTime.now().minusDays(2).toString(),
                "duration", "即時"
            ),
            Map.of(
                "step", "客戶通知",
                "status", "COMPLETED", 
                "description", "已發送Email和簡訊通知客戶",
                "completedTime", LocalDateTime.now().minusDays(2).plusMinutes(5).toString(),
                "duration", "5分鐘"
            ),
            Map.of(
                "step", "客戶申請",
                "status", "COMPLETED",
                "description", "客戶完成線上解款申請",
                "completedTime", LocalDateTime.now().minusDays(1).toString(),
                "duration", "1天"
            ),
            Map.of(
                "step", "身份驗證",
                "status", "COMPLETED",
                "description", "OTP簡訊驗證通過",
                "completedTime", LocalDateTime.now().minusDays(1).plusMinutes(10).toString(),
                "duration", "10分鐘"
            ),
            Map.of(
                "step", "資料處理",
                "status", "COMPLETED",
                "description", "解款資料處理完成",
                "completedTime", LocalDateTime.now().minusHours(2).toString(),
                "duration", "22小時"
            ),
            Map.of(
                "step", "解款完成",
                "status", "COMPLETED",
                "description", "解款已入帳完成",
                "completedTime", LocalDateTime.now().minusHours(1).toString(),
                "duration", "1小時"
            )
        );
    }
    
    /**
     * 取得交易狀態詳細資訊
     */
    public Map<String, Object> getTransactionStatusDetails(String caseNo) {
        Map<String, Object> status = new HashMap<>();
        status.put("caseNo", caseNo);
        status.put("currentStatus", "COMPLETED");
        status.put("statusDescription", "解款已完成");
        status.put("progressPercentage", 100);
        status.put("currentStep", "解款完成");
        status.put("nextStep", "無後續步驟");
        status.put("canTakeAction", false);
        status.put("availableActions", List.of());
        status.put("lastUpdatedTime", LocalDateTime.now().minusHours(1).toString());
        status.put("estimatedCompletionTime", null);
        status.put("contactInfo", Map.of(
            "customerService", "0800-123-456",
            "email", "<EMAIL>",
            "serviceHours", "週一至週五 09:00-17:30"
        ));
        return status;
    }
    
    /**
     * 計算交易統計摘要
     */
    public Map<String, Object> calculateTransactionSummary(List<Map<String, Object>> transactions) {
        long completedCount = transactions.stream()
                .filter(t -> "COMPLETED".equals(t.get("status")))
                .count();
        
        long processingCount = transactions.stream()
                .filter(t -> "PROCESSING".equals(t.get("status")))
                .count();
        
        double totalAmount = transactions.stream()
                .mapToDouble(t -> ((Number) t.get("amount")).doubleValue())
                .sum();
        
        return Map.of(
            "totalTransactions", transactions.size(),
            "completedTransactions", completedCount,
            "processingTransactions", processingCount,
            "totalAmount", totalAmount
        );
    }
    
    private String maskId(String id, String customerType) {
        if (id == null) return null;
        
        if ("INDIVIDUAL".equals(customerType) && id.length() == 10) {
            return id.substring(0, 3) + "****" + id.substring(7);
        } else if ("CORPORATE".equals(customerType) && id.length() == 8) {
            return id.substring(0, 2) + "****" + id.substring(6);
        }
        
        return id;
    }
}