package com.kgi.module.query.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 交易詳細資訊響應
 */
@Data
@Builder
public class TransactionDetailsResponse {
    private String caseNo;
    private boolean found;
    private String message;
    private Map<String, Object> transactionData;
    private Map<String, Object> statusDetails;
    private List<Map<String, Object>> processingHistory;
    private String queryTime;
}