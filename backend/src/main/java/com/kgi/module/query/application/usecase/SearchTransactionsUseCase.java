package com.kgi.module.query.application.usecase;

import com.kgi.module.query.application.dto.request.TransactionSearchRequest;
import com.kgi.module.query.application.dto.response.TransactionSearchResponse;
import com.kgi.module.query.domain.service.RemittanceQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 搜尋交易用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SearchTransactionsUseCase {
    
    private final RemittanceQueryService queryService;
    
    public TransactionSearchResponse execute(TransactionSearchRequest request) {
        log.info("執行交易搜尋: searchType={}, searchValue={}", 
                request.getSearchType(), request.getSearchValue());
        
        // 執行搜尋
        List<Map<String, Object>> searchResults = queryService.searchTransactions(
                request.getSearchType(), request.getSearchValue());
        
        // 建立回應
        return TransactionSearchResponse.builder()
                .searchType(request.getSearchType())
                .searchValue(request.getSearchValue())
                .resultCount(searchResults.size())
                .transactions(searchResults)
                .searchTime(LocalDateTime.now().toString())
                .build();
    }
}