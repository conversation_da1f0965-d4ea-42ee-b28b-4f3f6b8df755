package com.kgi.module.query.adapters.rest;

import com.kgi.core.adapters.rest.BaseController;
import com.kgi.core.application.dto.IbrApiResponse;
import com.kgi.module.query.application.dto.request.TransactionSearchRequest;
import com.kgi.module.query.application.dto.response.*;
import com.kgi.module.query.application.usecase.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 重構後的查詢控制器
 * 
 * 職責明確：
 * 1. 只負責接收HTTP請求和返回響應
 * 2. 參數驗證委託給 @Valid 和 DTO
 * 3. 業務邏輯委託給 UseCase
 * 4. 不包含任何業務邏輯實現
 * 
 * 根據URD的查詢流程：
 * 1. URL參數驗證 (type=Q, case=案件編號)
 * 2. 依編號查詢交易資料
 * 3. 顯示交易詳細資訊
 */
@Slf4j
@RestController
@RequestMapping("/api/ibr/query")
@RequiredArgsConstructor
public class QueryControllerRefactored extends BaseController {
    
    // 注入所有需要的 UseCase
    private final InitializeQueryUseCase initializeQueryUseCase;
    private final GetTransactionDetailsUseCase getTransactionDetailsUseCase;
    private final SearchTransactionsUseCase searchTransactionsUseCase;
    private final GetCustomerTransactionsUseCase getCustomerTransactionsUseCase;
    
    /**
     * 初始化查詢流程
     * GET /api/ibr/query/initialize?type=Q&caseNo=xxx
     */
    @GetMapping("/initialize")
    public ResponseEntity<IbrApiResponse<QueryInitializeResponse>> initializeQuery(
            @RequestParam String type,
            @RequestParam String caseNo) {
        
        log.info("初始化查詢流程: type={}, caseNo={}", type, caseNo);
        
        try {
            QueryInitializeResponse response = initializeQueryUseCase.execute(type, caseNo);
            return success(response, "查詢流程初始化成功");
        } catch (Exception e) {
            log.error("查詢流程初始化失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 查詢交易詳細資訊
     * GET /api/ibr/query/transaction/{caseNo}
     */
    @GetMapping("/transaction/{caseNo}")
    public ResponseEntity<IbrApiResponse<TransactionDetailsResponse>> getTransactionDetails(
            @PathVariable String caseNo) {
        
        log.info("查詢交易詳細資訊: caseNo={}", caseNo);
        
        try {
            TransactionDetailsResponse response = getTransactionDetailsUseCase.execute(caseNo);
            return success(response, "交易詳細資訊查詢成功");
        } catch (Exception e) {
            log.error("交易詳細資訊查詢失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 查詢交易狀態
     * GET /api/ibr/query/status/{caseNo}
     */
    @GetMapping("/status/{caseNo}")
    public ResponseEntity<IbrApiResponse<TransactionDetailsResponse>> getTransactionStatus(
            @PathVariable String caseNo) {
        
        log.info("查詢交易狀態: caseNo={}", caseNo);
        
        try {
            TransactionDetailsResponse response = getTransactionDetailsUseCase.execute(caseNo);
            return success(response, "交易狀態查詢成功");
        } catch (Exception e) {
            log.error("交易狀態查詢失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 多條件查詢
     * POST /api/ibr/query/search
     */
    @PostMapping("/search")
    public ResponseEntity<IbrApiResponse<TransactionSearchResponse>> searchTransactions(
            @Valid @RequestBody TransactionSearchRequest request) {
        
        log.info("執行交易搜尋: searchType={}, searchValue={}", 
                request.getSearchType(), request.getSearchValue());
        
        try {
            TransactionSearchResponse response = searchTransactionsUseCase.execute(request);
            return success(response, "交易搜尋完成");
        } catch (Exception e) {
            log.error("交易搜尋失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得客戶所有交易記錄
     * GET /api/ibr/query/customer/{customerId}/transactions
     */
    @GetMapping("/customer/{customerId}/transactions")
    public ResponseEntity<IbrApiResponse<CustomerTransactionsResponse>> getCustomerTransactions(
            @PathVariable String customerId,
            @RequestParam(defaultValue = "INDIVIDUAL") String customerType,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {
        
        log.info("查詢客戶交易記錄: customerId={}, customerType={}, page={}, pageSize={}", 
                customerId, customerType, page, pageSize);
        
        try {
            CustomerTransactionsResponse response = getCustomerTransactionsUseCase.execute(
                    customerId, customerType, page, pageSize);
            return success(response, "客戶交易記錄查詢成功");
        } catch (Exception e) {
            log.error("客戶交易記錄查詢失敗", e);
            return error(e.getMessage());
        }
    }
}