package com.kgi.module.query.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 客戶交易記錄響應
 */
@Data
@Builder
public class CustomerTransactionsResponse {
    private String customerId;
    private String customerType;
    private Map<String, Object> pagination;
    private List<Map<String, Object>> transactions;
    private Map<String, Object> summary;
    private String queryTime;
}