package com.kgi.module.query.domain.service;

import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 交易資料格式化服務
 * 負責格式化和遮罩敏感資料
 */
@Component
public class TransactionFormatter {
    
    /**
     * 格式化查詢結果
     */
    public Map<String, Object> formatQueryResult(String caseNo, Map<String, Object> transactionData) {
        String customerType = (String) transactionData.get("customerType");
        String status = (String) transactionData.get("status");
        
        Map<String, Object> result = new HashMap<>();
        
        // 基本資訊
        result.put("basicInfo", Map.of(
            "caseNo", caseNo,
            "customerType", customerType,
            "customerTypeDesc", formatCustomerType(customerType),
            "status", status,
            "statusDesc", formatStatus(status)
        ));
        
        // 匯款資訊
        result.put("remittanceInfo", Map.of(
            "payerName", transactionData.get("payerName"),
            "payeeName", transactionData.get("payeeName"),
            "payeeId", maskCustomerId((String) transactionData.get("payeeId"), customerType),
            "currency", transactionData.get("currency"),
            "amount", transactionData.get("amount"),
            "remittanceDate", transactionData.get("remittanceDate")
        ));
        
        // 帳戶資訊
        result.put("accountInfo", Map.of(
            "payeeAccount", maskAccountNumber((String) transactionData.get("payeeAccount")),
            "payeeBankCode", transactionData.get("payeeBankCode"),
            "bankName", getBankName((String) transactionData.get("payeeBankCode"))
        ));
        
        // 處理資訊
        result.put("processingInfo", Map.of(
            "createdTime", transactionData.get("createdTime"),
            "lastUpdatedTime", transactionData.get("lastUpdatedTime"),
            "estimatedCompletionTime", getEstimatedCompletionTime(status)
        ));
        
        return result;
    }
    
    /**
     * 格式化客戶類型
     */
    public String formatCustomerType(String customerType) {
        return "INDIVIDUAL".equals(customerType) ? "自然人" : "法人";
    }
    
    /**
     * 格式化狀態描述
     */
    public String formatStatus(String status) {
        Map<String, String> statusMap = Map.of(
            "INITIALIZED", "已初始化",
            "NOTIFIED", "已通知客戶",
            "PROCESSING", "處理中", 
            "SUPPLEMENT_REQUIRED", "需要補件",
            "COMPLETED", "已完成",
            "REJECTED", "已拒絕",
            "CANCELLED", "已取消"
        );
        return statusMap.getOrDefault(status, status);
    }
    
    /**
     * 取得銀行名稱
     */
    public String getBankName(String bankCode) {
        Map<String, String> bankMap = Map.of(
            "812", "台新銀行",
            "700", "中華郵政",
            "004", "台灣銀行",
            "006", "合作金庫",
            "007", "第一銀行"
        );
        return bankMap.getOrDefault(bankCode, "未知銀行");
    }
    
    /**
     * 遮罩客戶ID
     */
    public String maskCustomerId(String customerId, String customerType) {
        if (customerId == null) {
            return null;
        }
        
        if ("INDIVIDUAL".equals(customerType) && customerId.length() == 10) {
            // 身分證號遮罩
            return customerId.substring(0, 3) + "****" + customerId.substring(7);
        } else if ("CORPORATE".equals(customerType) && customerId.length() == 8) {
            // 統一編號遮罩
            return customerId.substring(0, 2) + "****" + customerId.substring(6);
        }
        
        return customerId;
    }
    
    /**
     * 遮罩帳號
     */
    public String maskAccountNumber(String accountNumber) {
        if (accountNumber == null || accountNumber.length() < 4) {
            return accountNumber;
        }
        
        return accountNumber.substring(0, 3) + "****" + 
               accountNumber.substring(accountNumber.length() - 3);
    }
    
    /**
     * 取得預計完成時間
     */
    public String getEstimatedCompletionTime(String status) {
        switch (status) {
            case "PROCESSING":
                return LocalDateTime.now().plusDays(1).toString();
            case "SUPPLEMENT_REQUIRED":
                return "待客戶補件完成後1-2個工作天";
            case "COMPLETED":
            case "REJECTED":
            case "CANCELLED":
                return null;
            default:
                return LocalDateTime.now().plusDays(2).toString();
        }
    }
}