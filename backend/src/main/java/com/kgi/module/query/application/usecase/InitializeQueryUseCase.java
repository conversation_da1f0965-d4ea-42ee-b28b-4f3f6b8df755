package com.kgi.module.query.application.usecase;

import com.kgi.module.query.application.dto.response.QueryInitializeResponse;
import com.kgi.module.query.domain.service.RemittanceQueryService;
import com.kgi.module.query.domain.service.TransactionFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 初始化查詢流程用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InitializeQueryUseCase {
    
    private final RemittanceQueryService queryService;
    private final TransactionFormatter formatter;
    
    public QueryInitializeResponse execute(String type, String caseNo) {
        log.info("執行查詢初始化: type={}, caseNo={}", type, caseNo);
        
        // 1. 驗證URL參數
        if (!"Q".equals(type)) {
            throw new IllegalArgumentException("無效的流程類型: " + type);
        }
        
        // 2. 查詢交易資料
        Map<String, Object> transactionData = queryService.queryTransactionData(caseNo);
        
        if (transactionData == null || transactionData.isEmpty()) {
            throw new IllegalArgumentException("查無此案件編號的交易資料: " + caseNo);
        }
        
        // 3. 格式化查詢結果
        Map<String, Object> formattedResult = formatter.formatQueryResult(caseNo, transactionData);
        
        // 4. 建立回應
        return QueryInitializeResponse.builder()
                .caseNo(caseNo)
                .flowType("QUERY")
                .transactionFound(true)
                .transactionData(formattedResult)
                .queryTime(LocalDateTime.now().toString())
                .sessionId(generateSessionId())
                .build();
    }
    
    private String generateSessionId() {
        return "QUERY_" + UUID.randomUUID().toString();
    }
}