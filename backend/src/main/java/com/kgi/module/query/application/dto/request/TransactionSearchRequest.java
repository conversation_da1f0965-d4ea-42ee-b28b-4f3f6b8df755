package com.kgi.module.query.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 交易搜尋請求
 */
@Data
public class TransactionSearchRequest {
    
    @NotBlank(message = "搜尋類型不可為空")
    private String searchType; // CASE_NO, CUSTOMER_ID, DATE_RANGE
    
    @NotBlank(message = "搜尋值不可為空")
    private String searchValue;
    
    private String startDate;
    private String endDate;
    private String status;
    private String currency;
}