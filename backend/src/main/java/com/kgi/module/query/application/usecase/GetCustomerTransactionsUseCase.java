package com.kgi.module.query.application.usecase;

import com.kgi.module.query.application.dto.response.CustomerTransactionsResponse;
import com.kgi.module.query.domain.service.RemittanceQueryService;
import com.kgi.module.query.domain.service.TransactionFormatter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 查詢客戶交易記錄用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetCustomerTransactionsUseCase {
    
    private final RemittanceQueryService queryService;
    private final TransactionFormatter formatter;
    
    public CustomerTransactionsResponse execute(
            String customerId, String customerType, int page, int pageSize) {
        
        log.info("執行客戶交易記錄查詢: customerId={}, customerType={}, page={}, pageSize={}", 
                formatter.maskCustomerId(customerId, customerType), customerType, page, pageSize);
        
        // 1. 查詢客戶交易歷史
        List<Map<String, Object>> transactions = queryService.getCustomerTransactionHistory(
                customerId, customerType, page, pageSize);
        
        // 2. 計算統計摘要
        Map<String, Object> summary = queryService.calculateTransactionSummary(transactions);
        
        // 3. 建立回應
        return CustomerTransactionsResponse.builder()
                .customerId(formatter.maskCustomerId(customerId, customerType))
                .customerType(customerType)
                .pagination(Map.of(
                    "currentPage", page,
                    "pageSize", pageSize,
                    "totalPages", 1,
                    "totalRecords", transactions.size()
                ))
                .transactions(transactions)
                .summary(summary)
                .queryTime(LocalDateTime.now().toString())
                .build();
    }
}