package com.kgi.module.test;

import com.kgi.core.config.RepositoryConfig;
import com.kgi.module.otp.domain.model.*;
import com.kgi.module.otp.domain.repository.OtpVerificationRepository;
import com.kgi.module.pcode2566.domain.model.AccountVerification;
import com.kgi.module.pcode2566.domain.model.BankAccount;
import com.kgi.module.pcode2566.domain.model.CustomerIdentity;
import com.kgi.module.pcode2566.domain.repository.AccountVerificationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 測試 Repository 切換機制的控制器
 */
@RestController
@RequestMapping("/api/test/repository")
@RequiredArgsConstructor
public class TestRepositoryController {
    
    private final RepositoryConfig repositoryConfig;
    private final OtpVerificationRepository otpRepository;
    private final AccountVerificationRepository accountRepository;
    
    @GetMapping("/status")
    public Map<String, Object> getRepositoryStatus() {
        Map<String, Object> result = new HashMap<>();
        
        // 當前模式
        result.put("currentMode", repositoryConfig.getType().name());
        result.put("isJpaMode", repositoryConfig.isJpaMode());
        result.put("isMemoryMode", repositoryConfig.isMemoryMode());
        
        // 測試 OTP Repository
        Map<String, Object> otpTest = testOtpRepository();
        result.put("otpRepositoryTest", otpTest);
        
        // 測試 Account Verification Repository
        Map<String, Object> accountTest = testAccountRepository();
        result.put("accountRepositoryTest", accountTest);
        
        return result;
    }
    
    private Map<String, Object> testOtpRepository() {
        Map<String, Object> test = new HashMap<>();
        try {
            // 創建測試 OTP
            String otpId = UUID.randomUUID().toString();
            OtpVerification otp = new OtpVerification();
            otp.setOtpId(otpId);
            otp.setUniqId("TEST-USER-001");
            otp.setUniqType("TEST");
            otp.setOtpCode(new OtpCode("123456"));
            otp.setChannel(OtpChannel.SMS);
            otp.setRecipient("**********");
            otp.setStatus(OtpStatus.SENT);
            otp.setCreatedAt(LocalDateTime.now());
            otp.setExpiredAt(LocalDateTime.now().plusMinutes(5));
            otp.setErrorCount(0);
            otp.setSendCount(1);
            
            // 儲存
            OtpVerification saved = otpRepository.save(otp);
            test.put("saveSuccess", saved != null && saved.getId() != null);
            test.put("savedId", saved.getId());
            
            // 查詢
            var found = otpRepository.findByOtpId(otpId);
            test.put("findSuccess", found.isPresent());
            
            if (found.isPresent()) {
                test.put("foundOtpId", found.get().getOtpId());
                test.put("foundUniqId", found.get().getUniqId());
            }
            
        } catch (Exception e) {
            test.put("error", e.getMessage());
            test.put("errorType", e.getClass().getSimpleName());
        }
        return test;
    }
    
    private Map<String, Object> testAccountRepository() {
        Map<String, Object> test = new HashMap<>();
        try {
            // 創建測試帳戶驗證
            AccountVerification verification = new AccountVerification();
            verification.setUniqId("TEST-USER-002");
            verification.setUniqType("TEST");
            verification.setBankAccount(BankAccount.builder()
                    .bankCode("012")
                    .accountNumber("****************")
                    .build());
            verification.setCustomerIdentity(new CustomerIdentity(
                    "A123456789",
                    "1990-01-01",
                    "**********"
            ));
            verification.setCreatedAt(LocalDateTime.now());
            verification.setErrorCount(0);
            
            // 儲存
            AccountVerification saved = accountRepository.save(verification);
            test.put("saveSuccess", saved != null && saved.getId() != null);
            test.put("savedId", saved.getId());
            
            // 查詢
            var found = accountRepository.findById(saved.getId());
            test.put("findSuccess", found.isPresent());
            
            if (found.isPresent()) {
                test.put("foundUniqId", found.get().getUniqId());
                test.put("foundBankCode", found.get().getBankAccount().getBankCode());
            }
            
        } catch (Exception e) {
            test.put("error", e.getMessage());
            test.put("errorType", e.getClass().getSimpleName());
        }
        return test;
    }
}