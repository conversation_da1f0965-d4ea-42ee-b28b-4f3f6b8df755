package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.RemittanceConfirmRequest;
import com.kgi.module.remittance.application.dto.response.RemittanceConfirmResponse;
import com.kgi.module.remittance.domain.service.RemittanceCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 匯款確認用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfirmRemittanceUseCase {
    
    private final RemittanceCalculationService calculationService;
    
    public RemittanceConfirmResponse execute(RemittanceConfirmRequest request) {
        log.info("執行匯款確認: caseNo={}, customerType={}, nature={}", 
                request.getCaseNo(), request.getCustomerType(), request.getSelectedNature());
        
        // 驗證匯款性質
        boolean isValidNature = calculationService.validateRemittanceNature(
            request.getCustomerType(), 
            request.getSelectedNature(), 
            request.getPurposeCode()
        );
        
        if (!isValidNature) {
            throw new IllegalArgumentException("匯款性質選擇無效: " + request.getSelectedNature());
        }
        
        // 計算金額與手續費
        Map<String, Object> amountCalculation = calculationService.calculateAmountAndFees(
            request.getCustomerType(), 
            request.getCaseNo(), 
            request.getSelectedNature()
        );
        
        // 儲存匯款確認記錄
        Map<String, Object> confirmationRecord = new HashMap<>();
        confirmationRecord.put("caseNo", request.getCaseNo());
        confirmationRecord.put("customerType", request.getCustomerType());
        confirmationRecord.put("selectedNature", request.getSelectedNature());
        confirmationRecord.put("purposeCode", request.getPurposeCode() != null ? request.getPurposeCode() : "");
        confirmationRecord.put("amountCalculation", amountCalculation);
        confirmationRecord.put("confirmationTime", LocalDateTime.now().toString());
        
        saveRemittanceConfirmation(confirmationRecord);
        
        // 建立回應
        return RemittanceConfirmResponse.builder()
                .caseNo(request.getCaseNo())
                .customerType(request.getCustomerType())
                .remittanceConfirmed(true)
                .selectedNature(request.getSelectedNature())
                .amountCalculation(amountCalculation)
                .nextStep("FINAL_CONFIRMATION")
                .nextStepUrl("/api/ibr/remittance/submit")
                .confirmationTime(LocalDateTime.now().toString())
                .build();
    }
    
    private void saveRemittanceConfirmation(Map<String, Object> confirmationRecord) {
        log.info("儲存匯款確認記錄: caseNo={}", confirmationRecord.get("caseNo"));
        // 實際應儲存到資料庫
    }
}