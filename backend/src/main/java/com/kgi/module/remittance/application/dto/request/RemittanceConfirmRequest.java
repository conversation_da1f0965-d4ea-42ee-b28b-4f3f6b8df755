package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 匯款確認請求
 */
@Data
public class RemittanceConfirmRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "客戶類型不能為空")
    private String customerType;
    
    @NotBlank(message = "匯款性質不能為空")
    private String selectedNature;
    
    private String purposeCode;  // 企業客戶可能需要的目的代碼
}