package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 匯款申請提交請求
 */
@Data
public class RemittanceSubmitRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "客戶類型不能為空")
    private String customerType;
    
    @NotNull(message = "確認資料不能為空")
    private Map<String, Object> confirmedData;
    
    private String digitalSignature;  // 企業客戶的數位簽章
    
    @NotNull(message = "必須進行最終確認")
    private Boolean finalConfirmation;
}