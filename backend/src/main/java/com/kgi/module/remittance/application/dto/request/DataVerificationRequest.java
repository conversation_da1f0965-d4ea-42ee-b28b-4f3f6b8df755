package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 客戶資料驗證請求
 */
@Data
public class DataVerificationRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "客戶類型不能為空")
    private String customerType;
    
    @NotNull(message = "輸入資料不能為空")
    private Map<String, Object> inputData;
}