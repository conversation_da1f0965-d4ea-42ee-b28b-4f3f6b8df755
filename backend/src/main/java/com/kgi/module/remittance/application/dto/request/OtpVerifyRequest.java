package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * OTP驗證請求
 */
@Data
public class OtpVerifyRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "OTP驗證碼不能為空")
    @Pattern(regexp = "^\\d{6}$", message = "OTP驗證碼必須是6位數字")
    private String otpCode;
    
    @NotBlank(message = "手機號碼不能為空")
    @Pattern(regexp = "^09\\d{8}$", message = "手機號碼格式錯誤")
    private String mobileNumber;
}