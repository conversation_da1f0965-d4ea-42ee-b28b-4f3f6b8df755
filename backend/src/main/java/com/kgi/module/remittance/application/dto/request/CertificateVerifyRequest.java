package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工商憑證驗證請求
 */
@Data
public class CertificateVerifyRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "憑證資料不能為空")
    private String certificateData;
    
    @NotBlank(message = "PIN碼不能為空")
    private String pin;
    
    private String readerName;
}