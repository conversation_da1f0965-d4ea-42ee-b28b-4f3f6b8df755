package com.kgi.module.remittance.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 初始化響應
 */
@Data
@Builder
public class InitializeResponse {
    private String caseNo;
    private String customerType;
    private String flowType;
    private Map<String, Object> originalData;
    private String step;
    private String nextStepUrl;
    private String sessionId;
    private String initTime;
    private Map<String, Object> customerInfo;
    private List<String> availableActions;
    
    public static InitializeResponse from(Map<String, Object> data) {
        return InitializeResponse.builder()
                .caseNo((String) data.get("caseNo"))
                .customerType((String) data.get("customerType"))
                .flowType((String) data.get("flowType"))
                .originalData((Map<String, Object>) data.get("originalData"))
                .step((String) data.get("step"))
                .nextStepUrl((String) data.get("nextStepUrl"))
                .sessionId((String) data.get("sessionId"))
                .initTime((String) data.get("initTime"))
                .customerInfo((Map<String, Object>) data.get("customerInfo"))
                .availableActions((List<String>) data.get("availableActions"))
                .build();
    }
}