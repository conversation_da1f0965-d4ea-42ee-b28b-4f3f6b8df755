package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.OtpSendRequest;
import com.kgi.module.remittance.application.dto.response.OtpSendResponse;
import com.kgi.module.remittance.domain.service.OtpService;
import com.kgi.module.remittance.domain.service.RemittanceValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 發送OTP用例
 */
@Slf4j
@Component
public class SendOtpUseCase {
    
    private final OtpService otpService;
    private final RemittanceValidationService validationService;
    
    public SendOtpUseCase(@Qualifier("remittanceOtpService") OtpService otpService,
                          RemittanceValidationService validationService) {
        this.otpService = otpService;
        this.validationService = validationService;
    }
    
    public OtpSendResponse execute(OtpSendRequest request) {
        log.info("執行發送OTP: caseNo={}, mobile={}", 
                request.getCaseNo(), otpService.maskMobileNumber(request.getMobileNumber()));
        
        // 驗證手機號碼格式
        if (!validationService.isValidMobileNumber(request.getMobileNumber())) {
            throw new IllegalArgumentException("手機號碼格式錯誤: " + request.getMobileNumber());
        }
        
        // 生成OTP
        String otpCode = otpService.generateOtpCode();
        
        // 儲存OTP記錄
        otpService.saveOtpRecord(
            request.getCaseNo(), 
            request.getMobileNumber(), 
            otpCode, 
            request.getVerificationType()
        );
        
        // 發送SMS
        boolean smsSent = otpService.sendSmsOtp(request.getMobileNumber(), otpCode);
        
        if (!smsSent) {
            throw new RuntimeException("OTP簡訊發送失敗");
        }
        
        // 建立回應
        return OtpSendResponse.builder()
                .caseNo(request.getCaseNo())
                .mobileNumber(otpService.maskMobileNumber(request.getMobileNumber()))
                .otpSent(true)
                .otpExpiry(LocalDateTime.now().plusMinutes(5).toString())
                .retryLimit(3)
                .sendTime(LocalDateTime.now().toString())
                .build();
    }
}