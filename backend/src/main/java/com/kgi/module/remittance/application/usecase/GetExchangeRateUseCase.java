package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.response.ExchangeRateResponse;
import com.kgi.module.remittance.domain.service.RemittanceCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 取得即時匯率用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetExchangeRateUseCase {
    
    private final RemittanceCalculationService calculationService;
    
    public ExchangeRateResponse execute(String currency) {
        return execute(currency, "INDIVIDUAL");
    }
    
    public ExchangeRateResponse execute(String currency, String customerType) {
        log.info("取得即時匯率: currency={}, customerType={}", currency, customerType);
        
        Map<String, Object> rateInfo = calculationService.getCurrentExchangeRate(
            currency, 
            "TWD", 
            customerType
        );
        
        return ExchangeRateResponse.builder()
                .fromCurrency((String) rateInfo.get("fromCurrency"))
                .toCurrency((String) rateInfo.get("toCurrency"))
                .customerType((String) rateInfo.get("customerType"))
                .rate((Double) rateInfo.get("rate"))
                .baseRate((Double) rateInfo.get("baseRate"))
                .preferentialDiscount((Double) rateInfo.get("preferentialDiscount"))
                .rateTime((String) rateInfo.get("rateTime"))
                .validUntil((String) rateInfo.get("validUntil"))
                .source((String) rateInfo.get("source"))
                .build();
    }
}