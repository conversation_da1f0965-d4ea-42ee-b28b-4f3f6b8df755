package com.kgi.module.remittance.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 匯款確認響應
 */
@Data
@Builder
public class RemittanceConfirmResponse {
    private String caseNo;
    private String customerType;
    private boolean remittanceConfirmed;
    private String selectedNature;
    private Map<String, Object> amountCalculation;
    private String nextStep;
    private String nextStepUrl;
    private String confirmationTime;
}