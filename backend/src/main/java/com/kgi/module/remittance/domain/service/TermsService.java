package com.kgi.module.remittance.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 條款服務
 * 負責處理條款內容、版本管理和同意記錄
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TermsService {
    
    private final CustomerTypeService customerTypeService;
    
    /**
     * 取得必要條款清單
     */
    public List<String> getRequiredTerms(String customerType) {
        if (customerTypeService.isIndividual(customerType)) {
            return List.of(
                "個人資料保護聲明",
                "數位解款服務條款", 
                "OTP使用條款"
            );
        } else if (customerTypeService.isCorporate(customerType)) {
            return List.of(
                "企業資料保護聲明",
                "企業數位解款服務條款",
                "工商憑證使用條款",
                "數位簽章條款"
            );
        }
        
        throw new IllegalArgumentException("未知的客戶類型: " + customerType);
    }
    
    /**
     * 取得條款內容
     */
    public Map<String, Object> getTermsContent(String customerType, String language) {
        log.info("取得條款內容: customerType={}, language={}", customerType, language);
        
        Map<String, Object> terms = new HashMap<>();
        
        if (customerTypeService.isIndividual(customerType)) {
            terms.put("personalDataProtection", getPersonalDataProtectionTerms(language));
            terms.put("digitalRemittanceTerms", getDigitalRemittanceTerms(language));
            terms.put("otpUsageTerms", getOtpUsageTerms(language));
        } else if (customerTypeService.isCorporate(customerType)) {
            terms.put("corporateDataProtection", getCorporateDataProtectionTerms(language));
            terms.put("corporateRemittanceTerms", getCorporateRemittanceTerms(language));
            terms.put("certificateUsageTerms", getCertificateUsageTerms(language));
            terms.put("digitalSignatureTerms", getDigitalSignatureTerms(language));
        }
        
        return terms;
    }
    
    /**
     * 儲存條款同意記錄
     */
    public void saveTermsAgreement(Map<String, Object> agreementRecord) {
        log.info("儲存條款同意記錄: caseNo={}, customerType={}", 
                agreementRecord.get("caseNo"), agreementRecord.get("customerType"));
        
        // 實際應該儲存到資料庫
        // 包含: 案件編號、客戶類型、同意的條款、IP、User-Agent、時間戳記等
    }
    
    /**
     * 驗證條款同意
     */
    public boolean validateTermsAgreement(String customerType, List<String> agreedTerms) {
        List<String> requiredTerms = getRequiredTerms(customerType);
        return agreedTerms != null && agreedTerms.containsAll(requiredTerms);
    }
    
    /**
     * 取得條款版本
     */
    public String getCurrentTermsVersion(String customerType) {
        // 實際應從資料庫或配置檔讀取
        return customerTypeService.isIndividual(customerType) ? "1.0.0" : "1.0.1";
    }
    
    /**
     * 取得條款生效日期
     */
    public String getTermsEffectiveDate(String customerType) {
        // 實際應從資料庫或配置檔讀取
        return "2025-01-01";
    }
    
    /**
     * 生成同意記錄ID
     */
    public String generateAgreementId() {
        return "AGR-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    // ========== 以下為各條款內容的方法 ==========
    
    private String getPersonalDataProtectionTerms(String language) {
        return """
            個人資料保護聲明
            
            本行依據個人資料保護法之規定，說明本行蒐集、處理及利用您個人資料之目的、類別、利用範圍及方式等事項...
            """;
    }
    
    private String getDigitalRemittanceTerms(String language) {
        return """
            數位解款服務條款
            
            歡迎使用本行數位解款服務，本條款規範您與本行間關於數位解款服務之權利義務關係...
            """;
    }
    
    private String getOtpUsageTerms(String language) {
        return """
            OTP使用條款
            
            一次性密碼（OTP）為本行提供之身份驗證機制，使用時請注意以下事項...
            """;
    }
    
    private String getCorporateDataProtectionTerms(String language) {
        return """
            企業資料保護聲明
            
            本行依據相關法規，說明本行蒐集、處理及利用貴公司資料之目的、類別、利用範圍及方式等事項...
            """;
    }
    
    private String getCorporateRemittanceTerms(String language) {
        return """
            企業數位解款服務條款
            
            歡迎貴公司使用本行企業數位解款服務，本條款規範貴公司與本行間關於企業數位解款服務之權利義務關係...
            """;
    }
    
    private String getCertificateUsageTerms(String language) {
        return """
            工商憑證使用條款
            
            工商憑證為經濟部工商憑證管理中心核發之電子憑證，使用時請遵守以下規定...
            """;
    }
    
    private String getDigitalSignatureTerms(String language) {
        return """
            數位簽章條款
            
            數位簽章具有法律效力，使用前請詳細閱讀以下條款...
            """;
    }
}