package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.DataVerificationRequest;
import com.kgi.module.remittance.application.dto.response.DataVerificationResponse;
import com.kgi.module.remittance.domain.service.CustomerTypeService;
import com.kgi.core.domain.service.ExternalApiService;
import com.kgi.module.remittance.domain.service.RemittanceValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 客戶資料驗證用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VerifyCustomerDataUseCase {
    
    private final ExternalApiService externalApiService;
    private final RemittanceValidationService validationService;
    private final CustomerTypeService customerTypeService;
    
    public DataVerificationResponse execute(DataVerificationRequest request) {
        log.info("執行客戶資料驗證: caseNo={}, customerType={}", 
                request.getCaseNo(), request.getCustomerType());
        
        // 取得原始TO API資料
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(request.getCaseNo());
        if (originalData == null || originalData.isEmpty()) {
            throw new IllegalArgumentException("查無此案件編號的資料: " + request.getCaseNo());
        }
        
        // 根據客戶類型執行資料比對
        Map<String, Object> comparisonResult = validationService.performDataComparison(
            request.getCustomerType(), 
            request.getInputData(), 
            originalData
        );
        
        boolean isValid = (boolean) comparisonResult.get("isValid");
        
        // 決定下一步驟
        String nextStep = isValid ? "IDENTITY_VERIFICATION" : "DATA_CORRECTION";
        String nextStepUrl = isValid ? 
            getIdentityVerificationUrl(request.getCustomerType()) : 
            "/api/ibr/remittance/data/verify";
        
        // 建立回應
        return DataVerificationResponse.builder()
                .caseNo(request.getCaseNo())
                .customerType(request.getCustomerType())
                .comparisonResult(comparisonResult)
                .nextStep(nextStep)
                .nextStepUrl(nextStepUrl)
                .verificationTime(LocalDateTime.now().toString())
                .build();
    }
    
    private String getIdentityVerificationUrl(String customerType) {
        if (customerTypeService.isIndividual(customerType)) {
            return "/api/ibr/remittance/otp/send";
        } else {
            return "/api/ibr/remittance/certificate/detect-readers";
        }
    }
}