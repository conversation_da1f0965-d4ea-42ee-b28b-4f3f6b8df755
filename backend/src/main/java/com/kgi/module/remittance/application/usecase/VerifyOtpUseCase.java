package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.OtpVerifyRequest;
import com.kgi.module.remittance.application.dto.response.OtpVerifyResponse;
import com.kgi.module.remittance.domain.service.OtpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 驗證OTP用例
 */
@Slf4j
@Component("remittanceVerifyOtpUseCase")
public class VerifyOtpUseCase {
    
    private final OtpService otpService;
    
    public VerifyOtpUseCase(@Qualifier("remittanceOtpService") OtpService otpService) {
        this.otpService = otpService;
    }
    
    public OtpVerifyResponse execute(OtpVerifyRequest request) {
        log.info("執行OTP驗證: caseNo={}", request.getCaseNo());
        
        // 驗證OTP
        boolean isValidOtp = otpService.validateOtpCode(
            request.getCaseNo(), 
            request.getMobileNumber(), 
            request.getOtpCode()
        );
        
        if (!isValidOtp) {
            throw new IllegalArgumentException("OTP驗證失敗，請確認驗證碼");
        }
        
        // 標記OTP已驗證
        otpService.markOtpAsVerified(
            request.getCaseNo(), 
            request.getMobileNumber(), 
            request.getOtpCode()
        );
        
        // 建立回應
        return OtpVerifyResponse.builder()
                .caseNo(request.getCaseNo())
                .otpVerified(true)
                .nextStep("REMITTANCE_CONFIRMATION")
                .nextStepUrl("/api/ibr/remittance/remittance/confirm")
                .verificationTime(LocalDateTime.now().toString())
                .build();
    }
}