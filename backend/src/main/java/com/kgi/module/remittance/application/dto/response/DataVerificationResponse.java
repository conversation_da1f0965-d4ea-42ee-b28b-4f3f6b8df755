package com.kgi.module.remittance.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 客戶資料驗證響應
 */
@Data
@Builder
public class DataVerificationResponse {
    private String caseNo;
    private String customerType;
    private Map<String, Object> comparisonResult;
    private String nextStep;
    private String nextStepUrl;
    private String verificationTime;
}