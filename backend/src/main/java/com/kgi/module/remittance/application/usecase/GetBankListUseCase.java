package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.response.BankListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 取得支援銀行清單用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GetBankListUseCase {
    
    public BankListResponse execute() {
        log.info("取得支援銀行清單");
        
        // 實際應從資料庫或外部服務取得
        List<Map<String, Object>> banks = getSupportedBankList();
        
        return BankListResponse.builder()
                .country("TW")
                .banks(banks)
                .totalCount(banks.size())
                .retrievedTime(LocalDateTime.now().toString())
                .build();
    }
    
    private List<Map<String, Object>> getSupportedBankList() {
        return List.of(
            Map.of(
                "bankCode", "812",
                "bankName", "台新銀行",
                "bankEngName", "Taishin International Bank",
                "swiftCode", "TSCBTWTP",
                "supported", true
            ),
            Map.of(
                "bankCode", "004",
                "bankName", "台灣銀行",
                "bankEngName", "Bank of Taiwan",
                "swiftCode", "BKTWTWTP",
                "supported", true
            ),
            Map.of(
                "bankCode", "006",
                "bankName", "合作金庫銀行",
                "bankEngName", "Taiwan Cooperative Bank",
                "swiftCode", "CCBCTWTP",
                "supported", true
            ),
            Map.of(
                "bankCode", "013",
                "bankName", "國泰世華銀行",
                "bankEngName", "Cathay United Bank",
                "swiftCode", "CBBKTWTP",
                "supported", true
            ),
            Map.of(
                "bankCode", "822",
                "bankName", "中國信託商業銀行",
                "bankEngName", "CTBC Bank",
                "swiftCode", "CTCBTWTP",
                "supported", true
            )
        );
    }
}