package com.kgi.module.remittance.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 匯款資料驗證服務
 * 負責驗證各種匯款相關的資料格式和業務規則
 */
@Slf4j
@Service("genericRemittanceValidationService")
@RequiredArgsConstructor
public class RemittanceValidationService {
    
    private static final Pattern CASE_NUMBER_PATTERN = Pattern.compile("^IBR\\d{10}$");
    private static final Pattern MOBILE_NUMBER_PATTERN = Pattern.compile("^09\\d{8}$");
    private static final Pattern ID_NUMBER_PATTERN = Pattern.compile("^[A-Z][12]\\d{8}$");
    private static final Pattern UNIFIED_NUMBER_PATTERN = Pattern.compile("^\\d{8}$");
    
    private final CustomerTypeService customerTypeService;
    
    /**
     * 驗證案件編號格式
     */
    public boolean isValidCaseNumber(String caseNo) {
        if (caseNo == null || caseNo.trim().isEmpty()) {
            return false;
        }
        return CASE_NUMBER_PATTERN.matcher(caseNo).matches();
    }
    
    /**
     * 驗證手機號碼格式
     */
    public boolean isValidMobileNumber(String mobileNumber) {
        if (mobileNumber == null || mobileNumber.trim().isEmpty()) {
            return false;
        }
        return MOBILE_NUMBER_PATTERN.matcher(mobileNumber).matches();
    }
    
    /**
     * 驗證身分證號格式
     */
    public boolean isValidIdNumber(String idNumber) {
        if (idNumber == null || idNumber.trim().isEmpty()) {
            return false;
        }
        return ID_NUMBER_PATTERN.matcher(idNumber).matches();
    }
    
    /**
     * 驗證統一編號格式
     */
    public boolean isValidUnifiedNumber(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.trim().isEmpty()) {
            return false;
        }
        return UNIFIED_NUMBER_PATTERN.matcher(unifiedNumber).matches();
    }
    
    /**
     * 執行客戶資料比對
     */
    public Map<String, Object> performDataComparison(
            String customerType, 
            Map<String, Object> inputData, 
            Map<String, Object> originalData) {
        
        Map<String, Object> result = new HashMap<>();
        
        if (customerTypeService.isIndividual(customerType)) {
            result = compareIndividualData(inputData, originalData);
        } else if (customerTypeService.isCorporate(customerType)) {
            result = compareCorporateData(inputData, originalData);
        }
        
        log.info("資料比對結果: customerType={}, isValid={}", 
                customerType, result.get("isValid"));
        
        return result;
    }
    
    /**
     * 比對個人資料
     */
    private Map<String, Object> compareIndividualData(
            Map<String, Object> inputData, 
            Map<String, Object> originalData) {
        
        Map<String, Object> result = new HashMap<>();
        
        // 比對身分證號
        String inputId = (String) inputData.get("idNumber");
        String originalId = (String) originalData.get("PayeeID");
        boolean idMatch = inputId != null && inputId.equalsIgnoreCase(originalId);
        
        // 比對姓名
        String inputName = (String) inputData.get("name");
        String originalName = (String) originalData.get("PayeeName");
        boolean nameMatch = inputName != null && inputName.equals(originalName);
        
        // 比對銀行帳號
        String inputAccount = (String) inputData.get("bankAccount");
        String originalAccount = (String) originalData.get("PayeeAccount");
        boolean accountMatch = inputAccount != null && inputAccount.equals(originalAccount);
        
        result.put("isValid", idMatch && nameMatch && accountMatch);
        result.put("idMatch", idMatch);
        result.put("nameMatch", nameMatch);
        result.put("accountMatch", accountMatch);
        
        if (!idMatch) result.put("idError", "身分證號不符");
        if (!nameMatch) result.put("nameError", "姓名不符");
        if (!accountMatch) result.put("accountError", "銀行帳號不符");
        
        return result;
    }
    
    /**
     * 比對企業資料
     */
    private Map<String, Object> compareCorporateData(
            Map<String, Object> inputData, 
            Map<String, Object> originalData) {
        
        Map<String, Object> result = new HashMap<>();
        
        // 比對統一編號
        String inputUnified = (String) inputData.get("unifiedNumber");
        String originalUnified = (String) originalData.get("PayeeID");
        boolean unifiedMatch = inputUnified != null && inputUnified.equals(originalUnified);
        
        // 比對企業名稱
        String inputCompany = (String) inputData.get("companyName");
        String originalCompany = (String) originalData.get("PayeeName");
        boolean companyMatch = inputCompany != null && inputCompany.equals(originalCompany);
        
        // 比對銀行帳號
        String inputAccount = (String) inputData.get("bankAccount");
        String originalAccount = (String) originalData.get("PayeeAccount");
        boolean accountMatch = inputAccount != null && inputAccount.equals(originalAccount);
        
        result.put("isValid", unifiedMatch && companyMatch && accountMatch);
        result.put("unifiedMatch", unifiedMatch);
        result.put("companyMatch", companyMatch);
        result.put("accountMatch", accountMatch);
        
        if (!unifiedMatch) result.put("unifiedError", "統一編號不符");
        if (!companyMatch) result.put("companyError", "企業名稱不符");
        if (!accountMatch) result.put("accountError", "銀行帳號不符");
        
        return result;
    }
}