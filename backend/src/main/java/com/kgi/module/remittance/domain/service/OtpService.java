package com.kgi.module.remittance.domain.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * OTP 服務
 * 負責處理一次性密碼的生成、發送和驗證
 */
@Slf4j
@Service("remittanceOtpService")
public class OtpService {
    
    // 模擬OTP儲存（實際應使用 Redis 或資料庫）
    private final Map<String, OtpRecord> otpStorage = new ConcurrentHashMap<>();
    
    /**
     * 生成6位數OTP碼
     */
    public String generateOtpCode() {
        return String.format("%06d", (int)(Math.random() * 1000000));
    }
    
    /**
     * 儲存OTP記錄
     */
    public void saveOtpRecord(String caseNo, String mobileNumber, String otpCode, String verificationType) {
        String key = generateOtpKey(caseNo, mobileNumber);
        OtpRecord record = new OtpRecord(
            caseNo, 
            mobileNumber, 
            otpCode, 
            verificationType,
            LocalDateTime.now(),
            LocalDateTime.now().plusMinutes(5),
            0
        );
        
        otpStorage.put(key, record);
        log.info("儲存OTP記錄: caseNo={}, mobile={}", caseNo, maskMobileNumber(mobileNumber));
    }
    
    /**
     * 發送OTP簡訊
     */
    public boolean sendSmsOtp(String mobileNumber, String otpCode) {
        // 實際環境應整合簡訊服務商 API
        log.info("發送OTP簡訊至: {}, 驗證碼: {}", maskMobileNumber(mobileNumber), otpCode);
        
        // 模擬發送成功
        return true;
    }
    
    /**
     * 驗證OTP碼
     */
    public boolean validateOtpCode(String caseNo, String mobileNumber, String otpCode) {
        String key = generateOtpKey(caseNo, mobileNumber);
        OtpRecord record = otpStorage.get(key);
        
        if (record == null) {
            log.warn("找不到OTP記錄: caseNo={}, mobile={}", caseNo, maskMobileNumber(mobileNumber));
            return false;
        }
        
        // 檢查是否過期
        if (LocalDateTime.now().isAfter(record.expiryTime)) {
            log.warn("OTP已過期: caseNo={}", caseNo);
            return false;
        }
        
        // 檢查嘗試次數
        if (record.attemptCount >= 3) {
            log.warn("OTP驗證次數超過限制: caseNo={}", caseNo);
            return false;
        }
        
        // 更新嘗試次數
        record.attemptCount++;
        
        // 驗證OTP
        boolean isValid = otpCode.equals(record.otpCode);
        
        if (!isValid) {
            log.warn("OTP驗證失敗: caseNo={}, 嘗試次數={}", caseNo, record.attemptCount);
        }
        
        return isValid;
    }
    
    /**
     * 標記OTP已驗證
     */
    public void markOtpAsVerified(String caseNo, String mobileNumber, String otpCode) {
        String key = generateOtpKey(caseNo, mobileNumber);
        OtpRecord record = otpStorage.get(key);
        
        if (record != null) {
            record.verified = true;
            record.verifiedTime = LocalDateTime.now();
            log.info("標記OTP已驗證: caseNo={}", caseNo);
        }
        
        // 驗證成功後移除記錄
        otpStorage.remove(key);
    }
    
    /**
     * 遮罩手機號碼
     */
    public String maskMobileNumber(String mobileNumber) {
        if (mobileNumber == null || mobileNumber.length() < 4) {
            return mobileNumber;
        }
        return mobileNumber.substring(0, 4) + "****" + 
               mobileNumber.substring(mobileNumber.length() - 2);
    }
    
    private String generateOtpKey(String caseNo, String mobileNumber) {
        return caseNo + ":" + mobileNumber;
    }
    
    /**
     * OTP記錄內部類
     */
    private static class OtpRecord {
        String caseNo;
        String mobileNumber;
        String otpCode;
        String verificationType;
        LocalDateTime createTime;
        LocalDateTime expiryTime;
        int attemptCount;
        boolean verified;
        LocalDateTime verifiedTime;
        
        OtpRecord(String caseNo, String mobileNumber, String otpCode, 
                  String verificationType, LocalDateTime createTime, 
                  LocalDateTime expiryTime, int attemptCount) {
            this.caseNo = caseNo;
            this.mobileNumber = mobileNumber;
            this.otpCode = otpCode;
            this.verificationType = verificationType;
            this.createTime = createTime;
            this.expiryTime = expiryTime;
            this.attemptCount = attemptCount;
            this.verified = false;
        }
    }
}