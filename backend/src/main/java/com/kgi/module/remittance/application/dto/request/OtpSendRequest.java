package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * OTP發送請求
 */
@Data
public class OtpSendRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "手機號碼不能為空")
    @Pattern(regexp = "^09\\d{8}$", message = "手機號碼格式錯誤")
    private String mobileNumber;
    
    private String verificationType = "REMITTANCE";
}