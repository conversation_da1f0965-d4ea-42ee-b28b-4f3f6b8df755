package com.kgi.module.remittance.application.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * 條款同意請求
 */
@Data
public class TermsAgreementRequest {
    
    @NotBlank(message = "案件編號不能為空")
    private String caseNo;
    
    @NotBlank(message = "客戶類型不能為空")
    private String customerType;
    
    @NotEmpty(message = "必須同意至少一項條款")
    private List<String> agreedTerms;
    
    private String userAgent;
    private String ipAddress;
}