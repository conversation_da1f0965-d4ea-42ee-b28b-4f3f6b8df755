package com.kgi.module.remittance.domain.service;

import com.kgi.core.domain.service.ExternalApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匯款計算服務
 * 負責處理匯款金額、手續費和匯率計算
 */
@Slf4j
@Service("genericRemittanceCalculationService")
@RequiredArgsConstructor
public class RemittanceCalculationService {
    
    private final CustomerTypeService customerTypeService;
    private final ExternalApiService externalApiService;
    
    /**
     * 計算匯款金額和手續費
     */
    public Map<String, Object> calculateAmountAndFees(String customerType, String caseNo, String selectedNature) {
        log.info("計算匯款金額和手續費: caseNo={}, customerType={}, nature={}", 
                caseNo, customerType, selectedNature);
        
        // 取得原始匯款資料
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(caseNo);
        if (originalData == null) {
            throw new IllegalArgumentException("查無案件資料: " + caseNo);
        }
        
        double amount = ((Number) originalData.get("Amount")).doubleValue();
        String currency = (String) originalData.get("Currency");
        
        // 根據客戶類型計算手續費
        double remittanceFee = calculateRemittanceFee(customerType, amount, selectedNature);
        double processingFee = calculateProcessingFee(customerType, amount);
        double totalFee = remittanceFee + processingFee;
        
        // 計算匯率
        Map<String, Object> exchangeRateInfo = getCurrentExchangeRate(currency, "TWD", customerType);
        double exchangeRate = (double) exchangeRateInfo.get("rate");
        
        Map<String, Object> result = new HashMap<>();
        result.put("originalAmount", amount);
        result.put("currency", currency);
        result.put("remittanceFee", remittanceFee);
        result.put("processingFee", processingFee);
        result.put("totalFee", totalFee);
        result.put("netAmount", amount - totalFee);
        result.put("exchangeRate", exchangeRate);
        result.put("amountTWD", (amount - totalFee) * exchangeRate);
        result.put("calculationTime", LocalDateTime.now().toString());
        result.put("feeDetails", buildFeeDetails(customerType, amount, selectedNature));
        
        return result;
    }
    
    /**
     * 計算匯款手續費
     */
    private double calculateRemittanceFee(String customerType, double amount, String nature) {
        if (customerTypeService.isIndividual(customerType)) {
            // 個人客戶手續費計算
            if ("FAMILY".equals(nature)) {
                return Math.min(amount * 0.005, 300); // 0.5%，最高300
            } else if ("EDUCATION".equals(nature)) {
                return 200; // 教育匯款固定200
            } else if ("MEDICAL".equals(nature)) {
                return 150; // 醫療匯款固定150
            }
            return 300; // 預設300
        } else {
            // 企業客戶手續費計算
            if (amount <= 100000) {
                return 500;
            } else if (amount <= 500000) {
                return 800;
            } else {
                return Math.min(amount * 0.002, 2000); // 0.2%，最高2000
            }
        }
    }
    
    /**
     * 計算處理費
     */
    private double calculateProcessingFee(String customerType, double amount) {
        // 固定處理費
        return customerTypeService.isIndividual(customerType) ? 100 : 200;
    }
    
    /**
     * 取得即時匯率
     */
    public Map<String, Object> getCurrentExchangeRate(String fromCurrency, String toCurrency, String customerType) {
        log.info("取得即時匯率: {} -> {}, customerType={}", fromCurrency, toCurrency, customerType);
        
        // 實際應從外部匯率服務取得
        double baseRate = getBaseExchangeRate(fromCurrency, toCurrency);
        
        // 根據客戶類型提供優惠匯率
        double preferentialRate = customerTypeService.isCorporate(customerType) ? 
            baseRate + 0.1 : baseRate;
        
        return Map.of(
            "fromCurrency", fromCurrency,
            "toCurrency", toCurrency,
            "customerType", customerType,
            "rate", preferentialRate,
            "baseRate", baseRate,
            "preferentialDiscount", preferentialRate - baseRate,
            "rateTime", LocalDateTime.now().toString(),
            "validUntil", LocalDateTime.now().plusMinutes(15).toString(),
            "source", "KGI Bank Exchange Rate System"
        );
    }
    
    /**
     * 取得基礎匯率
     */
    private double getBaseExchangeRate(String fromCurrency, String toCurrency) {
        // 模擬匯率資料
        Map<String, Double> rates = Map.of(
            "USD", 31.5,
            "EUR", 34.2,
            "JPY", 0.22,
            "CNY", 4.35,
            "HKD", 4.03
        );
        
        if ("TWD".equals(toCurrency)) {
            return rates.getOrDefault(fromCurrency, 1.0);
        }
        
        // 簡化處理，實際應支援多種貨幣轉換
        return 1.0;
    }
    
    /**
     * 建立手續費明細
     */
    private Map<String, Object> buildFeeDetails(String customerType, double amount, String nature) {
        Map<String, Object> details = new HashMap<>();
        
        if (customerTypeService.isIndividual(customerType)) {
            details.put("feeType", "個人匯款手續費");
            details.put("natureType", getNatureDescription(nature));
            details.put("feeRate", getFeeRate(nature));
        } else {
            details.put("feeType", "企業匯款手續費");
            details.put("amountRange", getAmountRange(amount));
            details.put("corporateDiscount", "大額匯款優惠");
        }
        
        return details;
    }
    
    /**
     * 驗證匯款性質
     */
    public boolean validateRemittanceNature(String customerType, String selectedNature, String purposeCode) {
        if (selectedNature == null || selectedNature.trim().isEmpty()) {
            return false;
        }
        
        List<String> validNatures = getValidRemittanceNatures(customerType);
        return validNatures.contains(selectedNature);
    }
    
    /**
     * 取得有效的匯款性質列表
     */
    public List<String> getValidRemittanceNatures(String customerType) {
        if (customerTypeService.isIndividual(customerType)) {
            return List.of("FAMILY", "EDUCATION", "MEDICAL", "GENERAL");
        } else {
            return List.of("19D", "250", "310", "410", "510");
        }
    }
    
    private String getNatureDescription(String nature) {
        return switch (nature) {
            case "FAMILY" -> "家用匯款";
            case "EDUCATION" -> "教育匯款";
            case "MEDICAL" -> "醫療匯款";
            default -> "一般匯款";
        };
    }
    
    private String getFeeRate(String nature) {
        return switch (nature) {
            case "FAMILY" -> "0.5% (最高300元)";
            case "EDUCATION" -> "固定200元";
            case "MEDICAL" -> "固定150元";
            default -> "固定300元";
        };
    }
    
    private String getAmountRange(double amount) {
        if (amount <= 100000) {
            return "10萬以下";
        } else if (amount <= 500000) {
            return "10-50萬";
        } else {
            return "50萬以上";
        }
    }
}