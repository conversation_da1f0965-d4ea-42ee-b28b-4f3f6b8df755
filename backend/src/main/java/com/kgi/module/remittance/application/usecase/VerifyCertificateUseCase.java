package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.CertificateVerifyRequest;
import com.kgi.module.remittance.application.dto.response.CertificateVerifyResponse;
import com.kgi.module.remittance.domain.service.CertificateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 工商憑證驗證用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VerifyCertificateUseCase {
    
    private final CertificateService certificateService;
    
    public CertificateVerifyResponse execute(CertificateVerifyRequest request) {
        log.info("執行工商憑證驗證: caseNo={}", request.getCaseNo());
        
        // 驗證工商憑證
        Map<String, Object> certVerificationResult = certificateService.performCertificateVerification(
            request.getCaseNo(),
            request.getCertificateData(),
            request.getPin(),
            request.getReaderName()
        );
        
        boolean isValidCert = (Boolean) certVerificationResult.get("isValid");
        
        if (!isValidCert) {
            String errorMessage = (String) certVerificationResult.get("errorMessage");
            throw new IllegalArgumentException("工商憑證驗證失敗: " + errorMessage);
        }
        
        // 儲存憑證驗證記錄
        certificateService.saveCertificateVerificationRecord(request.getCaseNo(), certVerificationResult);
        
        // 建立回應
        return CertificateVerifyResponse.builder()
                .caseNo(request.getCaseNo())
                .certificateVerified(true)
                .certificateInfo(certVerificationResult.get("certificateInfo"))
                .nextStep("REMITTANCE_CONFIRMATION")
                .nextStepUrl("/api/ibr/remittance/remittance/confirm")
                .verificationTime(LocalDateTime.now().toString())
                .build();
    }
}