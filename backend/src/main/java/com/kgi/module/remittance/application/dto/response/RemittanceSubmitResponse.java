package com.kgi.module.remittance.application.dto.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 匯款申請提交響應
 */
@Data
@Builder
public class RemittanceSubmitResponse {
    private String caseNo;
    private String customerType;
    private boolean applicationCompleted;
    private String applicationId;
    private Map<String, Object> fromApiResult;
    private String nextStep;
    private String nextStepUrl;
    private String completionTime;
    private String estimatedProcessingTime;
}