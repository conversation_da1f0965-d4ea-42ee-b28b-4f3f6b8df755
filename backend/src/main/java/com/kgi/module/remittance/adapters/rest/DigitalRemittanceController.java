package com.kgi.module.remittance.adapters.rest;

import com.kgi.core.adapters.rest.BaseController;
import com.kgi.core.application.dto.IbrApiResponse;
import com.kgi.module.remittance.application.usecase.*;
import com.kgi.module.remittance.application.dto.request.*;
import com.kgi.module.remittance.application.dto.response.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 重構後的統一數位解款申請控制器
 * 
 * 職責明確：
 * 1. 只負責接收HTTP請求和返回響應
 * 2. 參數驗證委託給 @Valid 和 DTO
 * 3. 業務邏輯委託給 UseCase
 * 4. 不包含任何業務邏輯實現
 * 
 * 核心流程 (individual/corporate共用)：
 * 1. initialize: URL參數驗證 + 客戶類型判斷
 * 2. terms: 條款同意 (個人/企業條款不同)
 * 3. verify-data: 資料驗證 (個人比對身分證+姓名，企業比對統編+企業名稱)
 * 4. identity: 身份驗證 (個人OTP，企業工商憑證)
 * 5. remittance: 匯款確認 (個人基本性質，企業複雜性質代碼)
 * 6. confirm: 最終確認 + FROM API呼叫
 */
@Slf4j
@RestController
@RequestMapping("/api/ibr/remittance")
@RequiredArgsConstructor
public class DigitalRemittanceController extends BaseController {
    
    // 注入所有需要的 UseCase
    private final InitializeRemittanceUseCase initializeUseCase;
    private final AgreeTermsUseCase agreeTermsUseCase;
    private final VerifyCustomerDataUseCase verifyCustomerDataUseCase;
    private final SendOtpUseCase sendOtpUseCase;
    private final VerifyOtpUseCase verifyOtpUseCase;
    private final VerifyCertificateUseCase verifyCertificateUseCase;
    private final ConfirmRemittanceUseCase confirmRemittanceUseCase;
    private final SubmitRemittanceUseCase submitRemittanceUseCase;
    private final GetBankListUseCase getBankListUseCase;
    private final GetExchangeRateUseCase getExchangeRateUseCase;
    
    /**
     * 初始化申請流程
     * GET /api/ibr/remittance/initialize?type=A&caseNo=xxx
     */
    @GetMapping("/initialize")
    public ResponseEntity<IbrApiResponse<InitializeResponse>> initializeApplication(
            @RequestParam String type,
            @RequestParam String caseNo) {
        
        log.info("初始化申請流程: type={}, caseNo={}", type, caseNo);
        
        try {
            Map<String, Object> result = initializeUseCase.execute(type, caseNo);
            InitializeResponse response = InitializeResponse.from(result);
            return success(response, "申請流程初始化成功");
        } catch (Exception e) {
            log.error("申請流程初始化失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 條款同意
     * POST /api/ibr/remittance/terms/agree
     */
    @PostMapping("/terms/agree")
    public ResponseEntity<IbrApiResponse<TermsAgreementResponse>> agreeTerms(
            @Valid @RequestBody TermsAgreementRequest request) {
        
        log.info("處理條款同意: caseNo={}", request.getCaseNo());
        
        try {
            TermsAgreementResponse response = agreeTermsUseCase.execute(request);
            return success(response, "條款同意完成");
        } catch (Exception e) {
            log.error("條款同意失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得條款內容
     * GET /api/ibr/remittance/terms?customerType=xxx&language=zh-TW
     */
    @GetMapping("/terms")
    public ResponseEntity<IbrApiResponse<TermsContentResponse>> getTerms(
            @RequestParam String customerType,
            @RequestParam(defaultValue = "zh-TW") String language) {
        
        log.info("取得條款內容: customerType={}, language={}", customerType, language);
        
        try {
            TermsContentResponse response = agreeTermsUseCase.getTermsContent(customerType, language);
            return success(response, "條款內容取得成功");
        } catch (Exception e) {
            log.error("取得條款內容失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 發送OTP (個人客戶)
     * POST /api/ibr/remittance/otp/send
     */
    @PostMapping("/otp/send")
    public ResponseEntity<IbrApiResponse<OtpSendResponse>> sendOtp(
            @Valid @RequestBody OtpSendRequest request) {
        
        log.info("發送OTP: caseNo={}", request.getCaseNo());
        
        try {
            OtpSendResponse response = sendOtpUseCase.execute(request);
            return success(response, "OTP發送成功");
        } catch (Exception e) {
            log.error("OTP發送失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 驗證OTP (個人客戶)
     * POST /api/ibr/remittance/otp/verify
     */
    @PostMapping("/otp/verify")
    public ResponseEntity<IbrApiResponse<OtpVerifyResponse>> verifyOtp(
            @Valid @RequestBody OtpVerifyRequest request) {
        
        log.info("驗證OTP: caseNo={}", request.getCaseNo());
        
        try {
            OtpVerifyResponse response = verifyOtpUseCase.execute(request);
            return success(response, "OTP驗證成功");
        } catch (Exception e) {
            log.error("OTP驗證失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 客戶資料驗證
     * POST /api/ibr/remittance/data/verify
     */
    @PostMapping("/data/verify")
    public ResponseEntity<IbrApiResponse<DataVerificationResponse>> verifyCustomerData(
            @Valid @RequestBody DataVerificationRequest request) {
        
        log.info("驗證客戶資料: caseNo={}", request.getCaseNo());
        
        try {
            DataVerificationResponse response = verifyCustomerDataUseCase.execute(request);
            return success(response, "客戶資料驗證完成");
        } catch (Exception e) {
            log.error("客戶資料驗證失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 驗證工商憑證 (企業客戶)
     * POST /api/ibr/remittance/certificate/verify
     */
    @PostMapping("/certificate/verify")
    public ResponseEntity<IbrApiResponse<CertificateVerifyResponse>> verifyCertificate(
            @Valid @RequestBody CertificateVerifyRequest request) {
        
        log.info("驗證工商憑證: caseNo={}", request.getCaseNo());
        
        try {
            CertificateVerifyResponse response = verifyCertificateUseCase.execute(request);
            return success(response, "工商憑證驗證成功");
        } catch (Exception e) {
            log.error("工商憑證驗證失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 匯款資料確認
     * POST /api/ibr/remittance/remittance/confirm
     */
    @PostMapping("/remittance/confirm")
    public ResponseEntity<IbrApiResponse<RemittanceConfirmResponse>> confirmRemittance(
            @Valid @RequestBody RemittanceConfirmRequest request) {
        
        log.info("確認匯款資料: caseNo={}", request.getCaseNo());
        
        try {
            RemittanceConfirmResponse response = confirmRemittanceUseCase.execute(request);
            return success(response, "匯款資料確認完成");
        } catch (Exception e) {
            log.error("匯款資料確認失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 提交申請
     * POST /api/ibr/remittance/submit
     */
    @PostMapping("/submit")
    public ResponseEntity<IbrApiResponse<RemittanceSubmitResponse>> submitRemittance(
            @Valid @RequestBody RemittanceSubmitRequest request) {
        
        log.info("提交匯款申請: caseNo={}", request.getCaseNo());
        
        try {
            RemittanceSubmitResponse response = submitRemittanceUseCase.execute(request);
            return success(response, "匯款申請提交成功");
        } catch (Exception e) {
            log.error("匯款申請提交失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得支援銀行清單
     * GET /api/ibr/remittance/banks
     */
    @GetMapping("/banks")
    public ResponseEntity<IbrApiResponse<BankListResponse>> getSupportedBanks() {
        
        log.info("取得支援銀行清單");
        
        try {
            BankListResponse response = getBankListUseCase.execute();
            return success(response, "銀行清單取得成功");
        } catch (Exception e) {
            log.error("取得銀行清單失敗", e);
            return error(e.getMessage());
        }
    }
    
    /**
     * 取得即時匯率
     * GET /api/ibr/remittance/exchange-rate?currency=USD
     */
    @GetMapping("/exchange-rate")
    public ResponseEntity<IbrApiResponse<ExchangeRateResponse>> getExchangeRate(
            @RequestParam String currency,
            @RequestParam(defaultValue = "INDIVIDUAL") String customerType) {
        
        log.info("取得即時匯率: currency={}, customerType={}", currency, customerType);
        
        try {
            ExchangeRateResponse response = getExchangeRateUseCase.execute(currency, customerType);
            return success(response, "匯率取得成功");
        } catch (Exception e) {
            log.error("取得匯率失敗", e);
            return error(e.getMessage());
        }
    }
}
    
