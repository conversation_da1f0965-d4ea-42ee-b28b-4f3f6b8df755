package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.TermsAgreementRequest;
import com.kgi.module.remittance.application.dto.response.TermsAgreementResponse;
import com.kgi.module.remittance.application.dto.response.TermsContentResponse;
import com.kgi.module.remittance.domain.service.TermsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 條款同意用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgreeTermsUseCase {
    
    private final TermsService termsService;
    
    public TermsAgreementResponse execute(TermsAgreementRequest request) {
        log.info("執行條款同意: caseNo={}, customerType={}", 
                request.getCaseNo(), request.getCustomerType());
        
        // 驗證必須同意所有必要條款
        if (!termsService.validateTermsAgreement(request.getCustomerType(), request.getAgreedTerms())) {
            throw new IllegalArgumentException("必須同意所有必要條款");
        }
        
        // 建立條款同意記錄
        Map<String, Object> agreementRecord = new HashMap<>();
        agreementRecord.put("caseNo", request.getCaseNo());
        agreementRecord.put("customerType", request.getCustomerType());
        agreementRecord.put("agreedTerms", request.getAgreedTerms());
        agreementRecord.put("userAgent", request.getUserAgent() != null ? request.getUserAgent() : "");
        agreementRecord.put("ipAddress", request.getIpAddress() != null ? request.getIpAddress() : "");
        agreementRecord.put("agreementTime", LocalDateTime.now().toString());
        agreementRecord.put("agreementId", termsService.generateAgreementId());
        
        // 儲存條款同意記錄
        termsService.saveTermsAgreement(agreementRecord);
        
        // 建立回應
        return TermsAgreementResponse.builder()
                .caseNo(request.getCaseNo())
                .customerType(request.getCustomerType())
                .termsAgreed(true)
                .nextStep("DATA_VERIFICATION")
                .nextStepUrl("/api/ibr/remittance/data/verify")
                .agreementTime(LocalDateTime.now().toString())
                .build();
    }
    
    public TermsContentResponse getTermsContent(String customerType, String language) {
        log.info("取得條款內容: customerType={}, language={}", customerType, language);
        
        Map<String, Object> termsContent = termsService.getTermsContent(customerType, language);
        
        return TermsContentResponse.builder()
                .customerType(customerType)
                .language(language)
                .terms(termsContent)
                .version(termsService.getCurrentTermsVersion(customerType))
                .effectiveDate(termsService.getTermsEffectiveDate(customerType))
                .retrievedTime(LocalDateTime.now().toString())
                .build();
    }
}