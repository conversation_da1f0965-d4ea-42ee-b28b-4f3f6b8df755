package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.application.dto.request.RemittanceSubmitRequest;
import com.kgi.module.remittance.application.dto.response.RemittanceSubmitResponse;
import com.kgi.core.domain.service.ExternalApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 提交匯款申請用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SubmitRemittanceUseCase {
    
    private final ExternalApiService externalApiService;
    
    public RemittanceSubmitResponse execute(RemittanceSubmitRequest request) {
        log.info("執行匯款申請提交: caseNo={}, customerType={}", 
                request.getCaseNo(), request.getCustomerType());
        
        // 驗證最終確認
        if (!Boolean.TRUE.equals(request.getFinalConfirmation())) {
            throw new IllegalArgumentException("必須進行最終確認");
        }
        
        // 彙整申請資料
        Map<String, Object> applicationData = compileApplicationData(
            request.getCaseNo(), 
            request.getCustomerType(), 
            request.getConfirmedData()
        );
        
        // 呼叫跨境平台FROM API
        Map<String, Object> fromApiResult = externalApiService.callFromApi(applicationData);
        
        // 生成申請ID
        String applicationId = generateApplicationId();
        
        // 儲存申請完成記錄
        Map<String, Object> completionRecord = new HashMap<>();
        completionRecord.put("caseNo", request.getCaseNo());
        completionRecord.put("customerType", request.getCustomerType());
        completionRecord.put("applicationData", applicationData);
        completionRecord.put("fromApiResult", fromApiResult);
        completionRecord.put("digitalSignature", request.getDigitalSignature() != null ? request.getDigitalSignature() : "");
        completionRecord.put("completionTime", LocalDateTime.now().toString());
        completionRecord.put("applicationId", applicationId);
        
        saveApplicationCompletion(completionRecord);
        
        // 建立回應
        return RemittanceSubmitResponse.builder()
                .caseNo(request.getCaseNo())
                .customerType(request.getCustomerType())
                .applicationCompleted(true)
                .applicationId(applicationId)
                .fromApiResult(fromApiResult)
                .nextStep("APPLICATION_COMPLETE")
                .nextStepUrl("/api/ibr/remittance/complete")
                .completionTime(LocalDateTime.now().toString())
                .estimatedProcessingTime("1-2個工作天")
                .build();
    }
    
    private Map<String, Object> compileApplicationData(
            String caseNo, String customerType, Map<String, Object> confirmedData) {
        
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(caseNo);
        
        Map<String, Object> compiledData = new HashMap<>();
        compiledData.put("caseNo", caseNo);
        compiledData.put("customerType", customerType);
        compiledData.put("originalData", originalData);
        compiledData.put("confirmedData", confirmedData);
        compiledData.put("compileTime", LocalDateTime.now().toString());
        
        return compiledData;
    }
    
    private String generateApplicationId() {
        return "APP-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    private void saveApplicationCompletion(Map<String, Object> completionRecord) {
        log.info("儲存申請完成記錄: applicationId={}", completionRecord.get("applicationId"));
        // 實際應儲存到資料庫
    }
}