package com.kgi.module.remittance.domain.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 客戶類型判斷服務
 * 負責根據 PayeeID 判斷客戶類型（個人/企業）
 */
@Slf4j
@Service
public class CustomerTypeService {
    
    /**
     * 判斷客戶類型
     * @param payeeId 收款人ID（身分證號或統一編號）
     * @return INDIVIDUAL（個人）或 CORPORATE（企業）
     */
    public String determineCustomerType(String payeeId) {
        if (payeeId == null || payeeId.trim().isEmpty()) {
            throw new IllegalArgumentException("PayeeID 不能為空");
        }
        
        // 個人: 身分證字號 (10位，第一位英文)
        if (payeeId.length() == 10 && Character.isLetter(payeeId.charAt(0))) {
            log.debug("判斷為個人客戶: {}", payeeId);
            return "INDIVIDUAL";
        }
        // 企業: 統一編號 (8位數字)
        else if (payeeId.length() == 8 && payeeId.matches("\\d{8}")) {
            log.debug("判斷為企業客戶: {}", payeeId);
            return "CORPORATE";
        }
        
        throw new IllegalArgumentException("無法判斷客戶類型，PayeeID格式錯誤: " + payeeId);
    }
    
    /**
     * 檢查是否為個人客戶
     */
    public boolean isIndividual(String customerType) {
        return "INDIVIDUAL".equals(customerType);
    }
    
    /**
     * 檢查是否為企業客戶
     */
    public boolean isCorporate(String customerType) {
        return "CORPORATE".equals(customerType);
    }
}