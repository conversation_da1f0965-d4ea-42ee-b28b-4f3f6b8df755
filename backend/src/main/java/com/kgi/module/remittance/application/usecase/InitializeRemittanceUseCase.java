package com.kgi.module.remittance.application.usecase;

import com.kgi.module.remittance.domain.service.CustomerTypeService;
import com.kgi.core.domain.service.ExternalApiService;
import com.kgi.module.remittance.domain.service.RemittanceValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 初始化匯款申請用例
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InitializeRemittanceUseCase {
    
    private final CustomerTypeService customerTypeService;
    private final RemittanceValidationService validationService;
    private final ExternalApiService externalApiService;
    
    public Map<String, Object> execute(String type, String caseNo) {
        log.info("執行初始化匯款申請: type={}, caseNo={}", type, caseNo);
        
        // 1. 驗證URL參數
        if (!"A".equals(type)) {
            throw new IllegalArgumentException("無效的流程類型，必須是A(申請): " + type);
        }
        
        if (!validationService.isValidCaseNumber(caseNo)) {
            throw new IllegalArgumentException("無效的案件編號格式: " + caseNo);
        }
        
        // 2. 查詢TO API原始資料
        Map<String, Object> originalData = externalApiService.getOriginalToApiData(caseNo);
        if (originalData == null || originalData.isEmpty()) {
            throw new IllegalArgumentException("查無此案件編號的資料: " + caseNo);
        }
        
        // 3. 自動判斷客戶類型
        String payeeId = (String) originalData.get("PayeeID");
        String customerType = customerTypeService.determineCustomerType(payeeId);
        
        // 4. 準備初始化資料
        Map<String, Object> initData = new HashMap<>();
        initData.put("caseNo", caseNo);
        initData.put("customerType", customerType);
        initData.put("flowType", "APPLICATION");
        initData.put("originalData", originalData);
        initData.put("step", "TERMS_AGREEMENT");
        initData.put("nextStepUrl", "/api/ibr/remittance/terms/agree");
        initData.put("sessionId", generateSessionId());
        initData.put("initTime", LocalDateTime.now().toString());
        initData.put("customerInfo", buildCustomerInfo(customerType, originalData));
        initData.put("availableActions", getAvailableActions(customerType));
        
        log.info("初始化完成: customerType={}", customerType);
        return initData;
    }
    
    private String generateSessionId() {
        return "SID-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    private Map<String, Object> buildCustomerInfo(String customerType, Map<String, Object> originalData) {
        Map<String, Object> info = new HashMap<>();
        
        if (customerTypeService.isIndividual(customerType)) {
            info.put("type", "個人客戶");
            info.put("payeeName", originalData.get("PayeeName"));
            info.put("payeeEngName", originalData.get("PayeeEngName"));
            info.put("payeeId", originalData.get("PayeeID"));
            info.put("identityVerificationMethod", "OTP簡訊驗證");
            info.put("supportedServices", List.of("一般匯款", "家用匯款", "教育匯款", "醫療匯款"));
        } else {
            info.put("type", "企業客戶");
            info.put("companyName", originalData.get("PayeeName"));
            info.put("companyEngName", originalData.get("PayeeEngName"));
            info.put("unifiedNumber", originalData.get("PayeeID"));
            info.put("identityVerificationMethod", "工商憑證驗證");
            info.put("supportedServices", List.of("國際貿易", "技術服務", "專業服務", "投資匯款", "資本匯款"));
        }
        
        return info;
    }
    
    private List<String> getAvailableActions(String customerType) {
        if (customerTypeService.isIndividual(customerType)) {
            return List.of("條款同意", "身份驗證", "OTP驗證", "匯款確認", "申請提交");
        } else {
            return List.of("企業條款同意", "統一編號驗證", "工商憑證驗證", "企業匯款確認", "數位簽章", "申請提交");
        }
    }
}