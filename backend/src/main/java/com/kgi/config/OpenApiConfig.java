package com.kgi.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * SpringDoc OpenAPI 3 配置
 * 
 * Swagger UI 路徑: http://localhost:8080/swagger-ui/index.html
 * OpenAPI 文檔: http://localhost:8080/v3/api-docs
 */
@Configuration
public class OpenApiConfig {
    
    @Value("${server.port:8080}")
    private String serverPort;
    
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("KGI IBR API 文檔")
                        .version("1.0.0")
                        .description("凱基銀行跨境匯款系統 API 文檔")
                        .termsOfService("https://www.kgibank.com/terms")
                        .contact(new Contact()
                                .name("KGI Bank IT Team")
                                .email("<EMAIL>")
                                .url("https://www.kgibank.com"))
                        .license(new License()
                                .name("Proprietary")
                                .url("https://www.kgibank.com/license")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("開發環境"),
                        new Server()
                                .url("https://api-test.kgibank.com")
                                .description("測試環境"),
                        new Server()
                                .url("https://api.kgibank.com")
                                .description("生產環境")
                ));
    }
}