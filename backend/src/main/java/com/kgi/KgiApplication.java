/**
 * Copyright (c) 2020 Madison Data Consulting
 * All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of
 * Madison Data Consulting ("Confidential Information").
 * <p>
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of license agreement you entered
 * into with Madison Data Consulting
 */

package com.kgi;

import com.kgi.core.config.JdbcTemplateConfiguration;
import com.kgi.core.config.KgiConfiguration;
import com.kgi.core.service.concurrent.DatabaseManagementService;
import org.apache.commons.lang.exception.ExceptionUtils;
// import org.camunda.bpm.spring.boot.starter.event.PostDeployEvent; // 暫時註解
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.boot.autoconfigure.domain.EntityScan;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;


@Slf4j
@EnableAsync
@EnableScheduling
@EnableJpaAuditing
@ServletComponentScan
@EnableRetry
@SpringBootApplication(scanBasePackages = {
    "com.kgi.core",
    "com.kgi.module"
})
@EntityScan(basePackages = {
    "com.kgi.core.entity",
    "com.kgi.module.*.infrastructure.entity"
})
// @Import({KgiConfiguration.class, JdbcTemplateConfiguration.class}) // 暫時禁用以測試基本啟動
public class KgiApplication implements EnvironmentAware, CommandLineRunner, ApplicationRunner {

    private static Logger logger = LoggerFactory.getLogger(KgiApplication.class);

    private static Properties properties = new Properties();

    private static int httpPort = 80;

    @Value("${kgi.useColdStart:false}")
    protected boolean useColdStart;

    // @Autowired // 暫時禁用
    // DatabaseManagementService databaseManagementService;

    // @Autowired // 暫時禁用
    // WFDatabaseManagementService wfdatabaseManagementService;

    @PostConstruct
    public void init(){
        // databaseManagementService.getDatabaseInitSettings(); // 暫時禁用
        // databaseManagementService.checkConnectionPoolStatus(); // 暫時禁用

        // wfdatabaseManagementService.getDatabaseInitSettings(); // 暫時禁用
        // wfdatabaseManagementService.checkConnectionPoolStatus(); // 暫時禁用
        
        log.info("KGI IBR 數位跨境匯款解付平台 - Spring Boot 3.4.3 版本啟動中...");
    }

    public static void main(String[] args) throws Exception {
        properties = getExternalConfig();

        ArrayList<String> arrayList = new ArrayList<String>(Arrays.asList(args));

        //arrayList.add("--spring.profiles.active=prod");
        if(properties.getProperty("spring.profiles.active") != null) {
            //spring.profiles.active=prod, sit, dev, uat
            arrayList.add("--spring.profiles.active=" + properties.getProperty("spring.profiles.active"));
        }

        if(properties.getProperty("server.port") != null) {
            httpPort = Integer.parseInt(properties.getProperty("server.port"));
        }

        SpringApplication springApp = new SpringApplication(KgiApplication.class);
        //Environment env = springApp.run(args).getEnvironment();
        Environment env = springApp.run(arrayList.stream().toArray(String[]::new)).getEnvironment();
        String protocol = "http";


        logger.info("\n----------------------------------------------------------\n\t" +
                        "Application '{}' is running! Access URLs:\n\t" +
                        "Profile(s): \t{dev,sit,uat,prod} (current: {})\n\t" +
                        "啟動成功\n" +
                        "----------------------------------------------------------",
                env.getProperty("spring.application.name"),
                env.getActiveProfiles());
    }

    /**
     * 測試期間開啟CORS
     * @return
     */
    @Bean
    public WebMvcConfigurer corsConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/CreditCardApply/first")
                        .allowedOriginPatterns("*")
                        .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                        .allowedHeaders("*")
                        .allowCredentials(false); // 設為false或使用allowedOriginPatterns
            }
        };
    }

    // @EventListener // 暫時註解
    // public void processPostDeploy(PostDeployEvent event) {
    //     // runtimeService.startProcessInstanceByKey("loanApproval");
    // }

    @PostConstruct
    public void testDependencyInjection() {
    }

    private static Properties getExternalConfig() {
        String configPath = "config/app-config.yml";

        //PropertiesPropertySource sources = null;
        Properties properties = new Properties();

        try {
            Resource resourceConfig = new FileSystemResource(configPath);
            logger.info("resourceConfig: " + resourceConfig);

            if(resourceConfig != null) {
                //https://gist.github.com/ManvendraSK/8b166b47514ca817d36e
                YamlPropertiesFactoryBean ypfb = new YamlPropertiesFactoryBean();

                ypfb.setResources(resourceConfig);
                ypfb.afterPropertiesSet();

                properties = ypfb.getObject();

                //sources = new PropertiesPropertySource("local.config.location", properties);
                logger.info("讀取外部檔案資訊: " + properties);
            }
        }catch (Exception e){
           logger.info("外部檔案不存在或格式錯誤..." + ExceptionUtils.getStackTrace(e));
        }

        return properties;
    }

    @Override
    public void setEnvironment(Environment environment) {
        if(properties == null)
            properties = getExternalConfig();

        PropertiesPropertySource sources = new PropertiesPropertySource("local.config.location", properties);
        ((ConfigurableEnvironment) environment).getPropertySources().addLast(sources);
    }

    @Override
    public void run(String... args) throws Exception {

    }


    /**
     * 可以根據需要在這裡進行啟動後的額外處理:
     * 1.開機預熱流程
     *
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
    }

}
