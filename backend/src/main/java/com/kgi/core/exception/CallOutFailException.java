package com.kgi.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 電文失敗
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class CallOutFailException extends Exception {

    // Custom error message
    private String message;

    // Custom error code
    private String errorCode;

    public CallOutFailException(String message) {
        super("電文發送失敗或接收值不正確[" + message + "]");
        this.message = message;
    }
}
