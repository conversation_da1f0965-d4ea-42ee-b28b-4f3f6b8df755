package com.kgi.core.exception;

import com.kgi.core.application.dto.IbrApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 全域例外處理器
 * 統一處理所有控制器拋出的例外
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 處理業務邏輯例外
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<IbrApiResponse<Object>> handleBusinessException(BusinessException e) {
        log.error("業務邏輯錯誤: {}", e.getMessage(), e);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", e.getErrorCode());
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        if (e.getDetails() != null) {
            errorDetails.put("details", e.getDetails());
        }
        
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(IbrApiResponse.error(e.getMessage(), e.getErrorCode()));
    }
    
    /**
     * 處理驗證例外
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<IbrApiResponse<Object>> handleValidationException(ValidationException e) {
        log.error("驗證錯誤: {}", e.getMessage(), e);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", e.getErrorCode());
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        if (e.getFieldErrors() != null) {
            errorDetails.put("fieldErrors", e.getFieldErrors());
        }
        
        IbrApiResponse<Object> response = IbrApiResponse.error(e.getMessage(), e.getErrorCode());
        response.setData(errorDetails);
        
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(response);
    }
    
    /**
     * 處理資源找不到例外
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<IbrApiResponse<Object>> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.error("資源找不到: {}", e.getMessage(), e);
        
        return ResponseEntity
                .status(HttpStatus.NOT_FOUND)
                .body(IbrApiResponse.error(e.getMessage(), e.getErrorCode()));
    }
    
    /**
     * 處理外部API呼叫例外
     */
    @ExceptionHandler(ExternalApiException.class)
    public ResponseEntity<IbrApiResponse<Object>> handleExternalApiException(ExternalApiException e) {
        log.error("外部API呼叫失敗: {} - {}", e.getApiName(), e.getMessage(), e);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", e.getErrorCode());
        errorDetails.put("apiName", e.getApiName());
        errorDetails.put("httpStatus", e.getHttpStatus());
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        
        IbrApiResponse<Object> response = IbrApiResponse.error(e.getMessage(), e.getErrorCode());
        response.setData(errorDetails);
        
        return ResponseEntity
                .status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(response);
    }
    
    /**
     * 處理Spring驗證錯誤 (@Valid)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<IbrApiResponse<Object>> handleMethodArgumentNotValid(
            MethodArgumentNotValidException e) {
        
        log.error("參數驗證失敗", e);
        
        Map<String, String> fieldErrors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            fieldErrors.put(fieldName, errorMessage);
        });
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", "VALIDATION_ERROR");
        errorDetails.put("fieldErrors", fieldErrors);
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        
        IbrApiResponse<Object> response = IbrApiResponse.error("輸入參數驗證失敗", "VALIDATION_ERROR");
        response.setData(errorDetails);
        
        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(response);
    }
    
    /**
     * 處理所有其他未預期的例外
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<IbrApiResponse<Object>> handleGenericException(Exception e) {
        log.error("未預期的錯誤", e);
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("errorCode", "INTERNAL_ERROR");
        errorDetails.put("errorType", e.getClass().getSimpleName());
        errorDetails.put("timestamp", LocalDateTime.now().toString());
        
        // 在開發環境可以顯示詳細錯誤，生產環境應該隱藏
        if (isDevelopmentEnvironment()) {
            errorDetails.put("message", e.getMessage());
            errorDetails.put("stackTrace", getStackTracePreview(e));
        }
        
        IbrApiResponse<Object> response = IbrApiResponse.error("系統發生錯誤，請稍後再試", "INTERNAL_ERROR");
        response.setData(errorDetails);
        
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(response);
    }
    
    /**
     * 判斷是否為開發環境
     */
    private boolean isDevelopmentEnvironment() {
        // TODO: 從配置文件讀取
        return true;
    }
    
    /**
     * 取得堆疊追蹤預覽
     */
    private String getStackTracePreview(Exception e) {
        StackTraceElement[] stackTrace = e.getStackTrace();
        if (stackTrace.length > 0) {
            StackTraceElement firstElement = stackTrace[0];
            return String.format("%s.%s(%s:%d)",
                    firstElement.getClassName(),
                    firstElement.getMethodName(),
                    firstElement.getFileName(),
                    firstElement.getLineNumber());
        }
        return "No stack trace available";
    }
}