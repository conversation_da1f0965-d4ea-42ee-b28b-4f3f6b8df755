package com.kgi.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class IncorrectResultException extends RuntimeException {

    private String name;

    // Custom error message
    private String message;

    // Custom error code
    private int errorCode;

    // Custom error message
    private Object result;

    public IncorrectResultException(String name) {
        super("微服務[ID=" + name + "]所需之值不正確");
        this.name = name;
    }

    public IncorrectResultException(int errorCode, String message) {
        this(errorCode, message, null);
    }

    public IncorrectResultException(int errorCode, String message,  Object result, Throwable throwable) {
        this.result = result;
        this.message = message;
        this.errorCode = errorCode;
    }

    public IncorrectResultException(int errorCode, String message, Throwable throwable) {
        this(errorCode, message, null, throwable);
    }

}
