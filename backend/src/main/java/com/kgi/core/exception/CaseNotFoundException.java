package com.kgi.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 起案
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class CaseNotFoundException extends Exception {

    // Custom error message
    private String message;

    // Custom error code
    private String errorCode;

    public CaseNotFoundException(String message) {
        super("申辦案件不存在[" + message + "]");
        this.message = message;
    }
}
