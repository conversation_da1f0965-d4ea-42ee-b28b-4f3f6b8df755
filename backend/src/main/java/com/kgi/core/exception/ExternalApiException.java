package com.kgi.core.exception;

/**
 * 外部API呼叫例外
 * 用於處理外部API呼叫失敗的情況
 */
public class ExternalApiException extends BusinessException {
    
    private final String apiName;
    private final int httpStatus;
    
    public ExternalApiException(String apiName, String message) {
        super("EXTERNAL_API_ERROR", message);
        this.apiName = apiName;
        this.httpStatus = 0;
    }
    
    public ExternalApiException(String apiName, int httpStatus, String message) {
        super("EXTERNAL_API_ERROR", message);
        this.apiName = apiName;
        this.httpStatus = httpStatus;
    }
    
    public String getApiName() {
        return apiName;
    }
    
    public int getHttpStatus() {
        return httpStatus;
    }
}