package com.kgi.core.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class PropertyNotUsingException extends Exception {

    private String name;

    // Custom error message
    private String message;

    // Custom error code
    private int errorCode;

    public PropertyNotUsingException(String name) {
        super("微服務[ID=" + name + "]未開啟本參數使用");
        this.name = name;
    }

    public PropertyNotUsingException(int errorCode, String message) {
        super(message);
        this.message = message;
        this.errorCode = errorCode;
    }


}
