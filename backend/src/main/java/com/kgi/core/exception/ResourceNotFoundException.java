package com.kgi.core.exception;

/**
 * 資源找不到例外
 * 用於處理查詢不到資源的情況
 */
public class ResourceNotFoundException extends BusinessException {
    
    public ResourceNotFoundException(String resourceType, String identifier) {
        super("RESOURCE_NOT_FOUND", 
              String.format("找不到%s: %s", resourceType, identifier));
    }
    
    public ResourceNotFoundException(String message) {
        super("RESOURCE_NOT_FOUND", message);
    }
}