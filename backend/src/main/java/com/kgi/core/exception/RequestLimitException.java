package com.kgi.core.exception;


import com.kgi.core.application.vo.ResponseVO;

import static com.kgi.core.domain.code.ReturnCode.ERROR_REQUEST_LIMIT;

public class RequestLimitException extends Exception {

    public RequestLimitException() {
        super();
    }

    public RequestLimitException(String msg) {
        super(msg);
    }

    public ResponseVO requestLimit() {
        ResponseVO res = new ResponseVO();
        res.setRtnCode(ERROR_REQUEST_LIMIT.getRtnCode());
        res.setRtnMessage(ERROR_REQUEST_LIMIT.getRtnMessage());
        return res;
    }

    public ResponseVO requestLimit(String code, String msg) {
        ResponseVO res = new ResponseVO();
        res.setRtnCode(code);
        res.setRtnMessage(msg);
        return res;
    }


}
