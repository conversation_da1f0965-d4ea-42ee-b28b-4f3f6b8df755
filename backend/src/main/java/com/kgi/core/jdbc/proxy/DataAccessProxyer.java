/**
 * Copyright (c) 2020 Madison Data Consulting
 * All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of
 * Madison Data Consulting ("Confidential Information").
 * <p>
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of license agreement you entered
 * into with Madison Data Consulting
 */

package com.kgi.core.jdbc.proxy;

import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DataAccessProxyer extends AbstractDataAccess implements IDataAccessProxyer {

    // ========== 建構方法 ==========
    private DataAccessProxyer(Connection conn) {
        this.conn = conn;
    }

    // ========== 生成方法 ==========
    public static DataAccessProxyer getInstance(Connection conn) {
        return new DataAccessProxyer(conn);
    }

    /**
     *
     */
    public int executeUpdate(String sqltext, Object... params) throws SQLException {
        if (StringUtils.isBlank(sqltext)) {
            throw new SQLException(M_SQL_SCRIPT_BLANK);
        }

        if (conn != null) {
            try {
                conn.setAutoCommit(false);

                sqltext = completeSqlText(sqltext);
                pstmt = conn.prepareStatement(sqltext);

                if (params != null && params.length != 0) {
                    for (int i = 0; i < params.length; i++) {
                        pstmt.setObject((i + 1), params[i]);
                    }
                }

                int rs = pstmt.executeUpdate();

                if (autoCommit) {
                    conn.commit();
                }

                return rs;
            } catch (SQLException e) {
                if (autoCommit) {
                    conn.rollback();
                }
                throw e;
            } finally {
                closePreparedStatement();
            }
        }

        return 0;
    }

    /**
     * add at 2013-01-16 for sp_rename
     */
    public int executeUpdateWithoutTx(String sqltext, Object... params) throws SQLException {
        if (StringUtils.isBlank(sqltext)) {
            throw new SQLException(M_SQL_SCRIPT_BLANK);
        }

        if (conn != null) {
            try {
                sqltext = completeSqlText(sqltext);
                pstmt = conn.prepareStatement(sqltext);

                if (params != null && params.length != 0) {
                    for (int i = 0; i < params.length; i++) {
                        pstmt.setObject((i + 1), params[i]);
                    }
                }

                int rs = pstmt.executeUpdate();

                conn.commit();

                return rs;
            } catch (SQLException e) {
                throw e;
            } finally {
                closePreparedStatement();
            }
        }

        return 0;
    }

    public void close() {
        this.close(Boolean.TRUE);
    }

    public void close(boolean isCloseConnection) {
        try {
            closeResultSet();
            closeStatement();
            closePreparedStatement();

            // 因工具共用, 故調整多一個參數來決定, 是不是要關閉 connection. edit by xavier on 20120713.
            if (isCloseConnection) {
                closeConnection();
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    // ========== 工具程式 ==========

    /**
     * 組出條件, 置換參數
     */
    private String completeSqlText(String sqltext) {
        if (sqltext.indexOf(P_CONDITION) != -1) {
            String conditionStr = "(1 = 1)";

            if (condition != null) {
                conditionStr = condition.toString();
            }

            sqltext = sqltext.replace(P_CONDITION, conditionStr);
        }
        // 組出置換參數
        if (paramMp != null && !paramMp.isEmpty()) {
            for (String paramKey : paramMp.keySet()) {
                String target = "$P{" + paramKey + "}";
                String value = String.valueOf(paramMp.get(paramKey));

                if (sqltext.indexOf(target) != -1 && value != null) {
                    sqltext = sqltext.replace(target, value);
                }
            }
        }

        this.sqlText = sqltext;

        return this.sqlText;
    }

    /**
     * @param metaData
     * @param colNmList
     * @param colLbList
     * @throws SQLException
     */
    private void getColumnInfo(ResultSetMetaData metaData, List<String> colNmList, List<String> colLbList)
            throws SQLException {
        int colSize = metaData.getColumnCount(); // 取得欄位索引長度

        for (int i = 1; i <= colSize; i++) {
            colNmList.add(metaData.getColumnName(i));
            colLbList.add(metaData.getColumnLabel(i));
        }
    }

    /**
     * 列印訊息
     *
     * @param msgtext
     */
    private void printInfo(String msgtext) {
        if (debugMode) {
            System.out.println(msgtext);
        }
    }

}
