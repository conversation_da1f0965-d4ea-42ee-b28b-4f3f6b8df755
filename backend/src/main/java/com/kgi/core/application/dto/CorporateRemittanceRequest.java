package com.kgi.core.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.Pattern;

/**
 * 法人數位解款請求格式
 * 繼承基礎請求並新增法人專用欄位
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CorporateRemittanceRequest extends DigitalRemittanceRequest {
    
    /**
     * 英文姓名無誤
     */
    @JsonProperty("engnameyn")
    private String engNameYN;
    
    /**
     * 居留證日期起
     * 格式: YYYYMMDD
     */
    @JsonProperty("residencedatebegin")
    @Pattern(regexp = "\\d{8}", message = "居留證起始日期格式錯誤，應為YYYYMMDD")
    private String residenceDateBegin;
    
    /**
     * 居留證日期迄
     * 格式: YYYYMMDD
     */
    @JsonProperty("residencedateend")
    @Pattern(regexp = "\\d{8}", message = "居留證結束日期格式錯誤，應為YYYYMMDD")
    private String residenceDateEnd;
    
    /**
     * 出生日期
     * 格式: YYYYMMDD
     */
    @JsonProperty("bod")
    @Pattern(regexp = "\\d{8}", message = "出生日期格式錯誤，應為YYYYMMDD")
    private String bod;
    
    /**
     * 匯款性質DETAIL
     */
    @JsonProperty("detail")
    private String detail;
}