package com.kgi.core.application.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.kgi.core.util.SystemConst;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 20240124 Momo WT20240119004
 * 因卡面流水號會因編輯而改變，改存卡面編號
 */
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AIOCaseDataVO implements Serializable {

    private String uniqId;


    // ----------------------------------------------------------------------
    // 以下為所有業務共用欄位
    // ----------------------------------------------------------------------
    /**
     * 使用者類別
     * 0: 新戶 1:信用卡戶 2:存款戶 3:純貸款戶
     */
    private String userType;

    /**
     * 業務員部門
     */
    private String agentDepart;

    /**
     * 業務員代號
     */
    private String agentNo;

    /**
     * 驗身-留存銀行電話號碼
     */
    private String bankPhone;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 身份證出生地
     */
    private String birthplace;

    /**
     * 我已閱讀並同意下列條款及告知事項
     * 0:否, 1:是
     */
    private Boolean ch1;

    /**
     * 金控共銷條款
     * 0:否, 1:是
     * 原: FinCrossllAgreeFlag
     */
    private Boolean ch4;

    /**
     * 第三方共銷條款
     * 0:否, 1:是
     * 原:ThirdCrossllAgreeFlag
     */
    private Boolean ch5;

    /**
     * 是否為額度型貸款戶
     * Y:額度型貸款戶
     * N:非額度型貸款戶
     */
    private String checkCashcardAccount;

    /**
     * 是否為信用卡戶
     * Y:信用卡戶
     * N:非信用卡戶
     */
    private String checkCreditcardAccount;

    /**
     * 是否為存款戶
     * Y:存款戶
     * N:非存款戶
     */
    private String checkKgibankAccount;

    /**
     * 是否為個人信貸戶
     * Y:個人信貸戶
     * N:非個人信貸戶
     */
    private String checkLoanAccount;

    /**
     * 是否為薪轉戶, 判斷客戶是否為薪轉戶(徵審)
     * Y:薪轉戶
     * N:非薪轉戶
     */
    private String checkSalaryAccount;

    /**
     * 中文姓名
     */
    private String chtName;

    /**
     * 公司名稱
     */
    private String corpName;

    /**
     * 公司統編
     */
    private String corpTaxNum;

    /**
     * 公司電話號碼
     */
    private String corpTel;

    /**
     * 公司電話區碼
     */
    private String corpTelArea;

    /**
     * 公司分機
     */
    private String corpTelExten;

    // /**
    // * 新增時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date createTime;

    /**
     * 驗身-存款帳戶銀行別
     * DropdownData.Name = ‘pCode2566Bank’
     */
    private String depositBank;

    /**
     * 最高教育程度
     * DropdownData.Name = ‘eopeducation’
     */
    private String education;

    /**
     * E-mail 網址
     */
    private String emailAddress;

    /**
     * 結果時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private String endTime;

    /**
     * 英文姓名
     */
    private String engName;

    /**
     * 是否有上傳財力證明
     * 00:無, 01:有, 02:無須上傳
     */
    private String hasUploadFina;

    /**
     * 有上傳ID
     * 00:無, 01:有, 02:無須上傳
     */
    private String hasUploadId;

    /**
     * 住家電話
     */
    private String homeTel;

    /**
     * 居住電話區碼
     */
    private String homeTelArea;

    /**
     * 身份證換發日期
     */
    private String idCardDate;

    /**
     * 身份證換發地點
     */
    private String idCardLocation;

    /**
     * 婚姻狀況
     * 未婚/已婚
     */
    private String idCardMarriage;

    /**
     * 身份證換發紀錄
     * 0:初領, 1:補領, 2:換發
     */
    private String idCardRecord;

    /**
     * 身分證字號或護照號碼
     */
    private String idno;

    /**
     * 設定網路銀行-使用者代號
     */
    private String ipAddress;

    /**
     * 職稱
     * DropdownData.Name = ‘eopjobtitle’
     */
    private String jobTitle;

    /**
     * 婚姻狀況
     * DropdownData.Name = ‘creditmarriage’
     */
    private String marriage;

    /**
     * 職業類別
     * DropdownData.Name = ‘eopoccupation’
     */
    private String occupation;

    /**
     * 身份證反面 縣市區域
     */
    private String ocrBackHouseHoldAddr1;

    /**
     * 身份證反面 里鄰
     */
    private String ocrBackHouseHoldAddr2;

    /**
     * 身份證反面 街道名稱
     */
    private String ocrBackHouseHoldAddr3;

    /**
     * 身份證反面 號
     */
    private String ocrBackHouseHoldAddr4;

    /**
     * 婚姻:未婚/已婚
     */
    private String ocrBackMarriage;

    /**
     * OCR身份證字號
     */
    private String ocrFrontBypassId;

    /**
     * OCR發證縣市
     */
    private String ocrFrontIdCardIssueCity;

    /**
     * OCR發證日期eeeMMdd
     */
    private String ocrFrontIdcardIssueDt;

    /**
     * OCR發證類型
     */
    private String ocrFrontIdcardIssueType;

    /**
     * OCR 身份證字號
     */
    private String ocrFrontIdno;

    /**
     * OCR姓名
     */
    private String ocrFrontName;

    /**
     * OCR回復代碼
     */
    private String ocrFrontRspCode;

    /**
     * 申辦流程名稱 TODO
     */
    private String pProductType;

    /**
     * 他行帳戶驗身的手機
     */
    private String pcode2566Phone;

    /**
     * 客戶填入之行動電話
     */
    private String phone;

    /**
     * 產品名稱
     * 改為多種 productId 搭配 uniqType 判斷, ex:
     * uniqType:01-貸款，02-卡加貸，03-立約，05-信用卡，06-aio-cc，07-aio-loan，08-aio-d3，09-aio-loan-d3，10-aio-loan-cc，11-aio-cc-d3，12-aio-loan-cc-d3
     */
    private String productId;

    /**
     * 推薦人身分
     */
    private String promoChannelID;

    /**
     * 單位代號
     */
    private String promoDepart;

    /**
     * 員工代號
     */
    private String promoMember;

    /**
     * 開戶目的-證券
     * 02:證券
     */
    private String purpose;

    /**
     * 轉介員編
     */
    private String referralEditor;

    /**
     * 轉介單位
     */
    private String referralUnit;

    /**
     * 戶藉地址 詳細地址
     */
    private String resAddr;

    /**
     * 戶籍地址區域 / 身份證鄉鎮市區
     */
    private String resAddrArea;

    /**
     * 戶藉地址 郵遞區號
     */
    private String resAddrZipCode;

    /**
     * 戶藉地址 電話
     */
    private String resTel;

    /**
     * 戶藉地址 區域
     */
    private String resTelArea;

    /**
     * 案件狀態
     */
    private String status;

    /**
     * 客戶類型
     * 1:信用卡, 2:現金卡, 3:存款, 4:貸款
     * 目的是給apply verify 簡訊驗證顯示用戶於本行有效行動電話之服務名稱
     */
    private String stempDataType;

    /**
     * 唯一代號類型
     * 01-貸款，02-卡加貸，03-立約，05-信用卡，06-aio-cc，07-aio-loan，08-aio-d3，09-aio-loan-d3，10-aio-loan-cc，11-aio-cc-d3，12-aio-loan-cc-d3
     */
    private String uniqType;

    // /**
    // * 更新時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private String updateTime;

    /**
     * 驗證狀態
     * 0:失敗, 1:成功
     */
    private String verifyStatus;

    /**
     * 驗證類型
     * 0:NCCC, 1:BANKACOUNT, 2:CHTMBC
     */
    private String verifyType;

    /**
     * 年收入
     */
    private String yearlyIncome;

    /**
     * 數位帳戶對帳單類型-D3
     * 2:電子帳單, 3:紙本
     */
    private String sendTypeD3;

    /**
     * 是否填寫完申請書
     * 00:無, 01:有
     */
    private String hasApplication;

    // ----------------------------------------------------------------------
    // 以下為正規化需拆分欄位

    // ----------------------------------------------------------------------
    // WT (2022/11/13) : D3/SAL/預約開戶
    // ----------------------------------------------------------------------
    /**
     * 驗身-銀行帳號
     */
    private String bankAccount;

    /**
     * 銀行分行選擇-區域
     * ED3_BranchList.BranchAddrDist
     */
    private String bankArea;

    /**
     * 銀行分行選擇-分行名稱
     * ED3_BranchList.BranchName
     */
    private String bankBranchName;

    /**
     * 銀行分行選擇-縣市
     * ED3_BranchList.BranchAddrCity
     */
    private String bankCountiesCities;

    /**
     * 銀行分行選擇-分行Id
     */
    private String branchID;

    /**
     * 本人聲明未持有美國籍且非為其他地區稅務居民身份
     * 0:否, 1:是
     */
    private Boolean ch7;

    /**
     * 交割戶同意
     * 0:否, 1:是
     */
    private Boolean ch8;

    /**
     * 通訊地址 詳細地址
     */
    private String commAddr;

    /**
     * 通訊地址 地址區域
     */
    private String commAddrArea;

    /**
     * 通訊地址 郵遞區號
     */
    private String commAddrZipCode;

    /**
     * 驗身-信用卡卡號
     */
    private String creditCardNumber;

    // /**
    // * D3 詳細寄送時間
    // * (ED3_CaseData SendedTime)
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date d3SentTime;

    /**
     * 帳戶資金來源
     * DropdownData.Name = ‘incomeSource’
     */
    private String incomeSource;

    /**
     * 是否有重大傷病
     * Y:是, N:否
     */
    private String injury;

    /**
     * email 驗證成功紀錄
     */
    private String isEmailVerify;

    /**
     * 是否申請外幣
     * 0:否, 1:是
     */
    private Boolean isForeign;

    /**
     * D3 是否同意查詢存款帳戶資料
     * Y:是, 空值:否
     */
    private String isQryBalance;

    /**
     * 是否申請信託
     * 0:否, 1:是
     */
    private Boolean isTrust;

    /**
     * 是否台灣稅務居民
     * Y:是, N:否
     */
    private String isTwTaxResident;

    /**
     * 預計與本行往來之業務
     * DropdownData.Name = ‘preTradeProduct’
     */
    private String preTradeProduct;

    /**
     * 流程(臨櫃/數三)
     */
    private String process;

    /**
     * 產品別:數位帳戶
     */
    private String productId_d3;

    /**
     * 銀行分行選擇-開戶目的
     * DropdownData.Name = ‘eoppurpose’
     */
    private String purpose_D3;

    /**
     * 證券分行地址
     */
    private String securitiesAddr;

    /**
     * 證券分行地址區碼
     * ED3_BranchList_SEC.SecuritiesAddrZipCode
     */
    private String securitiesAddrZipCode;

    /**
     * 證券分公司選擇-區域
     * ED3_BranchList_SEC.SecuritiesDist
     */
    private String securitiesArea;

    /**
     * 證券分行代碼
     * ED3_BranchList_SEC.SecuritiesCode
     */
    private String securitiesCode;

    /**
     * 證券分公司選擇-縣市
     * ED3_BranchList_SEC.SecuritiesCity
     */
    private String securitiesCountiesCities;

    /**
     * 證券分行名稱
     * ED3_BranchList_SEC.SecuritiesName
     */
    private String securitiesName;

    /**
     * 證券項目-交割戶
     * 0:否, 1:是
     */
    private Boolean setSecuritiesDelivery;

    /**
     * 證券項目-複委託
     */
    private String setSubBrokerage;

    /**
     * 數位帳戶狀態
     */
    private String status_d3;

    /**
     * 薪資轉帳狀態
     */
    private String status_sal;

    /**
     * D3 複委託幣別
     * 0:台幣, 1:外幣, 2:台幣+外幣
     */
    private String subBroBinKind;

    /**
     * 驗身-信用卡有效期日期
     */
    private String validityPeriod;

    /**
     * 主動推介
     * 0:否, 1:是
     */
    private Boolean ch9;

    /**
     * 薪轉台幣存摺
     * 電子:N, 紙本:Y
     */
    private String passBookTw;

    /**
     * 薪轉外幣存摺
     * 電子:N, 紙本:Y
     */
    private String passBookFore;

    /**
     * 是否為職業高風險
     * 原D3、SAL AmlResult 欄位
     * Y:是, N:否
     */
    private String highRisk;

    /**
     * 是否附財力證明
     * Y:是, N:否
     */
    private String hasFin;

    /**
     * FATCA及CRS聲明書
     * 屬於上線後加入欄位，因為正式區無法回補資料，故無法使用boolean
     * null 為 未填寫, 0: false, 1: true
     */
    private String termsFatca;

    /**
     * 薪轉帳戶
     */
    private String salaryAccountTw;

    // ----------------------------------------------------------------------
    // WT (2022/11/13) : 信用卡/異業
    // ----------------------------------------------------------------------
    /**
     * 圖檔是否上傳影像 (LOAN/CC)
     * 0:還沒, 1:已送出
     */
    private String addPhotoFlag;

    /**
     * 申辦卡別
     * CreditCardProduct.ProductId
     */
    private String applyForCard;

    /**
     * 公司縣市
     */
    private String companyCounties;

    /**
     * 公司地址 詳細地址
     */
    private String corpAddr;

    /**
     * 公司地址 區域
     */
    private String corpAddrArea;

    /**
     * 公司地址 郵遞區號
     */
    private String corpAddrZipCode;

    /**
     * 決策平台編號
     * 透過 api/KGI/ADD_LOAN_CASE 取得的編號，下列決策平台 API都需要用這個編號做後續發查用
     */
    private String decisionCaseNo;

    /**
     * 畢業國小
     */
    private String elementarySchoolName;

    /**
     * 不動產狀態
     * DropdownData.Name = ‘estate’
     */
    private String estateType;

    /**
     * 獨享優惠
     * 0:否, 1:是
     */
    private Boolean exclusiveOffer;

    /**
     * 首刷禮
     * CreditCardGift.GiftCode
     */
    private String firstPresent;

    /**
     * 是否已通過戶役政
     * 00:否, 01:是
     */
    private String hasQryIdInfo;

    /**
     * 居住地址(住家) 詳細地址
     */
    private String homeAddr;

    /**
     * 居住縣市區域
     */
    private String homeAddrArea;

    /**
     * 居住地址(住家) 郵遞號
     */
    private String homeAddrZipCode;

    /**
     * 到職月
     */
    private String month;

    /**
     * 到職日期
     */
    private String onBoardDate;

    /**
     * 產品別:信用卡
     * CreditCardProduct.ProductId
     */
    private String productId_cc;

    /**
     * 帳單類型
     * 2:電子帳單, 3:紙本
     */
    private String sendType;

    /**
     * 信用卡狀態
     */
    private String status_cc;

    /**
     * 發查 取得案件申請評分等..資訊
     */
    private String subcaseType;

    /**
     * 到職年
     */
    private String year;

    /**
     * 信用卡排程狀態
     */
    private String jobStatus_cc;

    /**
     * 副狀態 (CC)
     */
    private String substatus_cc;

    /**
     * 跨售channelId
     */
    private String channelId;

    /**
     * 聯名機構之個資利用同意條款
     * 屬於上線後加入欄位，因為正式區無法回補資料，故無法使用boolean
     * null 為 未填寫, 0: false, 1: true
     */
    private String termsJointNamePersonal;

    /**
     * 案件編號(APS) APS_Cc 的案件編號
     */
    private String apsNo_cc;

    /**
     * 選擇卡面編號
     * CreditCardCardFace.FaceCode
     */
    private String faceCode;

    // /**
    // * 戶役政驗證完成時間 (CC/LOAN)
    // */
    // @JsonFormat(locale = "zh", timezone = "GTM+8", pattern =
    // "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date qryIdInfoTime;

    // ----------------------------------------------------------------------
    // WT (2022/11/13) : 個人信貸/額度型貸款/貸款異業
    // (有部分共用欄會在 信用卡/數位帳戶 那邊出現
    // ----------------------------------------------------------------------
    /**
     * 是否為高風險
     * Y:身分為正常
     * N:身分為黑名單
     * V:身分為疑似有異常
     */
    private String amlResult;

    /**
     * 專案代號
     */
    private String pj;

    // /**
    // * 發動起案時間
    // * 產生 decisionCaseNo 的時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date addNewCaseTime;

    // /**
    // * 發查AML的時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date amlTime;

    /**
     * 是否為免財力證明
     * 00:否, 01:是
     */
    private String applyWithoutFina;

    /**
     * 個人信用貸款分期型貸款金額
     * 單位：萬元
     */
    private String apyAmount_PL;

    /**
     * 循環信用貸款額度型貸款金額
     */
    private String apyAmount_RPL;

    /**
     * 發查 取得案件申請評分等..資訊
     */
    private String cAbnormalReason;

    /**
     * 舊ID可否使用
     * 00:不可, 01:可
     */
    private String canUseOldIdImg;

    /**
     * APS 的案件編號
     * CREATE_DGT_CASE 產的
     */
    private String caseNoWeb;

    /**
     * 利率風險
     */
    private String cddRateRisk;

    /**
     * 同意使用與貴行往來之資料申辦貸款
     * 0:否, 1:是
     */
    private Boolean ch2;

    /**
     * 我已閱讀並同意貴行查詢聯徵信用資訊
     * 0:否, 1:是
     */
    private Boolean ch3;

    /**
     * 現金卡系統案件編號
     */
    private String gmCardNo;

    /**
     * 是否已產生KYC表
     * 00:否, 01:是
     */
    private String hasFinishKyc;

    /**
     * 是否已通過檢核利害關係人
     * 00:否, 01:是
     */
    private String hasNoRelp;

    /**
     * 行內是否有舊財力
     * 00:無, 01:有
     */
    private String hasOldFinaImg;

    /**
     * 行內是否有舊ID
     * 00:無, 01:有
     */
    private String hasOldIdImg;

    /**
     * 有qryAml資料
     * 空值:否, 01:是
     */
    private String hasQryAml;

    /**
     * 是否已通過Z07
     * 00:否, 01:是
     */
    private String hasQryZ07;

    /**
     * 發查 取得案件申請評分等..資訊
     */
    private String isCaseIntegrated;

    /**
     * 是否預核
     * 1:預核, 2:非預核
     */
    private String isPreAudit;

    // /**
    // * 發查聯徵的時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date jcicTime;

    /**
     * 同意使用與貴行往來之資料申辦貸款
     * (form AIOConfirm.java NO. 272)
     */
    private String loanPersonalDataResult;

    // /**
    // * 同意使用與貴行往來之資料申辦貸款 註記時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date loanPersonalDataTime;

    /**
     * 舊ID影編代號列表
     */
    private String oldIdImgNoList;

    /**
     * 個人信用貸款期間
     */
    private String peroid_PL;

    /**
     * 循環信用貸款期間
     */
    private String peroid_RPL;

    /**
     * 產品別:個人信用貸款
     * 2:個人信用貸款
     */
    private String productId_pl;

    /**
     * 產品別:循環信用貸款
     * 3:額度型貸款
     * 4:靈活卡
     */
    private String productId_rpl;

    /**
     * 資金用途
     * DropdownData.Name = ‘moneypurpose’
     */
    private String purpose_PL;

    // /**
    // * 詳細寄送時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date sendCustInfoTime;

    /**
     * 個人信用貸款狀態
     */
    private String status_pl;

    /**
     * 循環信用貸款狀態
     */
    private String status_rpl;

    /**
     * 分期型貸款用途 其他-內容
     */
    private String purposePLOtherText;

    /**
     * 職稱備註
     */
    private String jobTitleText;

    /**
     * 貸款排程狀態
     */
    private String jobStatus_loan;

    /**
     * 副狀態 (LOAN)
     */
    private String substatus_loan;

    /**
     * 貸款pl 專案別
     * DropdownData.Name = ‘ProjectPL’
     */
    private String prjCodePl;

    /**
     * 貸款rpl 專案別
     * DropdownData.Name = ‘ProjectRPL’
     */
    private String prjCodeRpl;

    /**
     * 電文 CHECK_NO_FINPF 的 MEMO 欄位值
     */
    private String checkNoFinpfMemo;

    /**
     * 檢查結果
     */
    private String checkRelpResult;

    // /**
    // * 檢查回复時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date checkRelpTime;

    /**
     * 圖片上傳系統完成
     * Y:是, N:否
     */
    private String imgSysCaseComplete;

    // /**
    // * 圖片上傳系統完成時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date imgSysCaseCompleteTime;

    /**
     * 信息源_CDE
     */
    private String infosourceCde;

    /**
     * 90天內是否改動過手機號碼註記
     * Y:是, N:否
     */
    private String isChanged;

    /**
     * 發查聯徵成功
     * Y:是, N:否
     */
    private String jcicOk;

    /**
     * 電文 CHECK_NO_FINPF 的 RESULT_CODE 欄位值
     */
    private String noFinpfResultCode;

    /**
     * 電文 CHECK_NO_FINPF 的 YEARLY_INCOME 欄位值
     */
    private String noFinpfYearlyIncome;

    /**
     * 發查Z07 回傳聯徵資料 html
     */
    private String z07Html;

    /**
     * 發查Z07
     * Y:是(有通報案件記錄), N:否
     */
    private String z07Result;

    // /**
    // * 發查Z07的時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date z07Time;

    /**
     * 跨國交易功能
     * 0:否, 1:是
     */
    private Boolean internationalTrade;

    /**
     * 拒貸90天內案件註記Pl
     * Y:是, N:否
     */
    private String rejectFlag_PL;

    /**
     * 拒貸90天內案件註記Rpl
     * Y:是, N:否
     */
    private String rejectFlag_RPL;

    /**
     * APS 傳來的案件狀態 (PL)
     */
    private String apsStatus_pl;

    /**
     * APS 傳來的案件狀態 (RPL)
     */
    private String apsStatus_rpl;

    /**
     * APS 傳來的案件狀態 (CC)
     * 1:核卡, 2:不核卡, 3:自撤
     */
    private String apsStatus_cc;

    /**
     * 案件編號(APS) APS_Pl 的案件編號
     */
    private String apsNo_pl;

    /**
     * 案件編號(APS)APS_Rpl 的案件編號
     */
    private String apsNo_rpl;

    /**
     * 是否填寫完成同一關係人
     * 00:無, 01:有
     */
    private String hasRelationDegree;

    /**
     * 執行秒貸流程並更新准駁狀態
     * 00:無, 01:有
     */
    private String hasApplicationClose;

    /**
     * 非約定轉帳功能
     */
    private Boolean nonAgreeTransfer;

    /**
     * 信用卡群組
     */
    private String cardGroup;

    /**
     * 台幣帳戶
     */
    private String accountTw;

    /**
     * 台幣帳戶清單
     */
    private String accountTwList;

    /**
     * 外幣帳戶
     */
    private String accountFore;

    /**
     * 外幣帳戶清單
     */
    private String accountForeList;

    /**
     * 扣繳金額 (1:應繳總金額, 2:最低應繳金額)
     */
    private String withholdingAmount;

    /**
     * 同意變更原授扣設定
     */
    private Boolean awChangeAgreement;

    // ------------------------------

    /**
     * 是否為重要客戶 Y:重要客戶 N:非重要客戶
     */
    private String importantCust;

    /**
     * 若為"Y"，為警示戶 or 告誡戶
     */
    private String doubtAboutIdentity;

    public void setupSubStatusLoan(SystemConst.CaseDataSubStatus subStatus) {
        setStatus(subStatus.getStatus());
        setSubstatus_loan(subStatus.getSubStatus());
    }

    public void setupSubStatusCc(SystemConst.CaseDataSubStatus subStatus) {
        setStatus(subStatus.getStatus());
        setSubstatus_cc(subStatus.getSubStatus());
    }

    // ----------------------------------------
    // TODO: 是否還在使用? 沒有進 AIOCaseData
    private String prevPage;
    private String otpSourceName = ""; // OTP_Source對應的名稱
    // ----------------------------------------

    /**
     * 財力證明圖片 (都是 onMockTransaction 在用)
     */
    private String financialBase64Image;
    /**
     * 財力證明種類 (都是 onMockTransaction 在用)
     */
    private String sType;
    /**
     * 財力編號 (都是 onMockTransaction 在用)
     */
    private String subSerial;
    private String jobStatus;
    /**
     * 副狀態 (LOAN)
     */
    private String substatus;


    /**
     * 房屋地址
     */
    private String address_HL;

    /**
     * 房屋地址縣市
     */
    private String addressCity_HL;

    /**
     * 房屋地址鄉鎮區
     */
    private String addressArea_HL;

    /**
     * 房屋地址完整地址
     */
    private String addressWhole_HL;

    /**
     * 房屋所有權人
     */
    private String ownership_HL;

    /**
     * 要往來服務據點
     */
    private String serviceBase;

    /**
     * 房屋使用狀況
     */
    private String useStatus_HL;

    /**
     * 房屋使用狀況其他
     */
    private String useStatusText_HL;

    /**
     * 選擇產品類型
     */
    private String chooseProduct;

    /**
     * 房屋貸款-保證人
     */
    private String guarantor_HL;

    /**
     * 房屋貸款-貸款金額
     */
    private String apyAmount_HL;

    /**
     * 房屋貸款-貸款期間
     */
    private String peroid_HL;

    /**
     * 房屋貸款-寬限期
     */
    private String peroid_grace;

    /**
     * 房屋貸款-貸款用途
     */
    private String purpose_HL;

    /**
     * 房屋貸款-貸款用途其他文字
     */
    private String purpose_HLText;

    /**
     * 房屋貸款-還款方式
     */
    private String repayment_HL;

    /**
     * 產品別:房貸
     */
    private String productId_hl;

    /**
     * 借款人案編
     */
    private String borrowUniqId;

    /**
     * 房屋貸款用途-選擇其他要再填寫原因
     */
    private String purposeHLOtherText;

    /**
     * 房屋貸款狀態
     */
    private String status_hl;

    /**
     * 房屋貸款-房屋區碼
     */
    private String houseAddrZipCode;

    /**
     * 房屋貸款-手機稽查
     */
    private String phoneAudit;

    /**
     * 房屋貸款-手機型號
     */
    private String phoneModel;

    /**
     * 上傳證件時是否有錯誤(辨識/比對/輸入)
     * 00:否, 01:是
     */
    private String hasErrIdCard;

    public Boolean isCh3() {
        return this.ch3;
    }

    public Boolean isCh4() {
        return this.ch4;
    }

    public Boolean isCh5() {
        return this.ch5;
    }

    public Boolean isCh9() {
        return this.ch9;
    }

    public Boolean isTermsFincShrRisk() {
        return this.termsFincShrRisk;
    }

    public Boolean isTermsFincShrAdv() {
        return this.termsFincShrAdv;
    }

    public Boolean isExclusiveOffer() {
        return this.exclusiveOffer;
    }

    public void setIsForeign(Boolean foreign) {
        this.isForeign = foreign;
    }

    public Boolean getIsForeign() {
        return this.isForeign;
    }

    public void setIsTrust(Boolean trust) {
        this.isTrust = trust;
    }

    public Boolean getIsTrust() {
        return this.isTrust;
    }

    /**
     * 預訂分行 ID
     */
    private String bookingBranchId;

    // /**
    // * 預定日期
    // * r2-5203 預約開戶已預約訊息format壞掉
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date bookingDate;

    // /**
    // * 預訂時間
    // */
    // @JsonFormat(pattern="yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    // private Date bookingTime;

    /**
     * 金融控股公司及參與資料共享公司間為辨識風險
     * confirmation-page 用 存進 aioCaseDataTerms，主檔不存
     */
    private Boolean termsFincShrRisk;

    /**
     * 金融控股公司及參與資料共享公司間為提升客戶便利性
     * confirmation-page 用 存進 aioCaseDataTerms，主檔不存
     */
    private Boolean termsFincShrAdv;

    /**
     * 投資理財
     */
    private String invest;

    // ----------------------------------------------------------------------
    // TODO: 以下為不再使用之欄位, 需檢查是否未清乾淨或需替換
    // ----------------------------------------------------------------------
    /**
     * TODO 預審案件編號
     */
    @Deprecated
    private String caseNoEst;

    /**
     * TODO 專案代號
     */
    @Deprecated
    private String prjCode;

    /**
     * 產品別:薪資帳戶
     * 目前因應程式一致性 使用 sal:0
     * 0 代表申辦 X代表不申辦
     */
    @Deprecated
    private String productId_sal;

    /**
     * 貸款類型-分期型
     */
    @Deprecated
    private String loanType;

    /**
     * 貸款類型-額度型
     */
    @Deprecated
    private String quotaType;

    /**
     * 額度型貸款產品名稱
     */
    @Deprecated
    private String quotaProductId;

    /**
     * 額度型貸款用途
     */
    @Deprecated
    private String quotaPurpose;

    /**
     * 貸款類型-分期型+額度型
     */
    @Deprecated
    private String installmentQuotaType;

    /**
     * 戶藉地址 詳細地址
     */
    @Deprecated
    private String resAddress;

    /**
     * 建立新的案例編號
     */
    @Deprecated
    private String createNewCaseCaseNo;

    /**
     * 申請代碼
     */
    @Deprecated
    private String applyCode;

    /**
     * APS 真正的案件編號 APS 起案之後會來新
     */
    @Deprecated
    private String caseNoAps;

    /**
     * 性別 1:男 2:女
     */
    @Deprecated
    private String gender;

    /**
     * 公司地址
     */
    @Deprecated
    private String companyAddress;

    /**
     * 公司電話分機
     */
    @Deprecated
    private String corpDepart;

    /**
     * 驗身-信用卡銀行別
     */
    @Deprecated
    private String deposit;

    /**
     * 驗身-信用卡卡號
     */
    @Deprecated
    private String creditCard;

    /**
     * 身份證發證地點
     */
    @Deprecated
    private String idCardTownship;

    /**
     * 本次開立帳戶-信託帳戶
     */
    @Deprecated
    private String trustAccount;

    /**
     * 申請凱基人壽保費12期分期0利率
     * 新戶首年可同享1%刷卡金回饋
     */
    @Deprecated
    private Boolean ch6 = false;

    /**
     * 4. 舊財力影編代號列表
     */
    @Deprecated
    private String oldFinaImgNoList;

    /**
     * 理債平台的前一案件編號
     */
    @Deprecated
    private String caseNoApsPrev;

    /**
     * CORP_CLASS：公司種別(1.政府機關2.教育機關3.上市/上櫃/知名大企業4.一般企業5.小公司/獨資行號)
     */
    @Deprecated
    private String corpClass;

    /**
     * CORP_TYPE：行業別
     */
    @Deprecated
    private String corpType;

    /**
     * 職稱中文
     */
    @Deprecated
    private String title;

    /**
     * 原信用卡專案 使用的欄位 -> APSNo_CC
     */
    @Deprecated
    private String creditNoAps;

    /**
     * 原信用卡專案 使用的欄位
     */
    @Deprecated
    private String deviceId;

    /** 轉介單位 */
    @Deprecated
    private String transDepart;

    /** 轉介人員 */
    @Deprecated
    private String transMember;

    /**
     * 跨售使用 優先核准金額上限
     */
    @Deprecated
    private String creditLimit;

    /**
     * 發查 取得案件申請評分等..資訊 PL
     */
    @Deprecated
    private String dgtcaseResult;

    // /**
    // * 發查 取得案件申請評分等..資訊 的時間 PL
    // */
    // @Deprecated
    // private Date dgtCaseTime;

    /**
     * D3凱基人壽用
     */
    @Deprecated
    private String isPayer;

    /**
     * APS 傳來的案件狀態 (CC/LOAN會互打)
     */
    @Deprecated
    private String apsStatus;

    @Deprecated
    private String breakPointPage;

    @Deprecated
    private String tradeProduct;

    /**
     * 與申請人關係
     */
    private String relationshipWithApplicant;

    /**
     * 通訊電話號碼
     */
    private String commTel;

    /**
     * 通訊電話區碼
     */
    private String commTelArea;

    /**
     * 客戶身分證字號
     */
    private String customerId;

    /**
     * 數位案件編號
     */
    private String dgtPlatformNo;

    /**
     * 同一關係人親屬資料
     */
    private String dATA_LIST;

    /**
     * 20240417 WT20240416003 Maggie
     * 新增個人資產總額欄位
     */
    private String totalPersonAssets;


    /**
     * 20240502 WT20240219004 Maggie
     * 新增 Email 驗證註記
     */
    private String mailVerifyed;

    /**
     * 20240502 WT20240219004 Maggie
     * 新增 Email 失聯註記
     */
    private String uncfEmail;

    /**
     * 同一關係人企業資料
     */
    private String dATA_COMP_LIST;
    public String getPProductType() {
        return pProductType;
    }

    public void setPProductType(String pProductType) {
        this.pProductType = pProductType;
    }

    /**
     * p P 問題, 前端需用 getpProductType 取pProductType
     *
     * @return
     */
    public String getpProductType() {
        return getPProductType();
    }

    /**
     * p P 問題, 前端需用 setpProductType 設定 pProductType
     *
     * @return
     */
    public void setpProductType(String pProductType) {
        setPProductType(pProductType);
    }

}
