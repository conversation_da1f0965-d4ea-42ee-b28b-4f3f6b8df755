package com.kgi.core.application.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 驗生VO
 * <AUTHOR>
 */
@Data
public class BasicInfoVO implements Serializable {
    //身分證號
    private String id;

    //姓名
    private String chtName;

    //英文名
    private String engName;

    //EMail
    private String email;

    //地址
    private String commAddr;

    //手機
    private String mobile;

    public static BasicInfoVO build(String chtName, String engName, String email, String address, String mobile) {
        BasicInfoVO basicInfoVO = new BasicInfoVO();
        basicInfoVO.setChtName(chtName);
        basicInfoVO.setEngName(engName);
        basicInfoVO.setEmail(email);
        basicInfoVO.setCommAddr(address);
        basicInfoVO.setMobile(mobile);

        return basicInfoVO;
    }
}
