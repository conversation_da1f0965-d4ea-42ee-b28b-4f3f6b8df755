package com.kgi.core.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 數位解款請求格式
 * 基於 IBR API 設計規範
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DigitalRemittanceRequest {
    
    /**
     * 跨境平台編號
     */
    @JsonProperty("theirrefno")
    @NotBlank(message = "跨境平台編號不能為空")
    private String theirRefNo;
    
    /**
     * 匯入匯款編號 (來自資通)
     */
    @JsonProperty("remitrefno")
    @NotBlank(message = "匯入匯款編號不能為空")
    private String remitRefNo;
    
    /**
     * 匯款英文名
     */
    @JsonProperty("payername")
    @NotBlank(message = "匯款人姓名不能為空")
    private String payerName;
    
    /**
     * 匯款幣別
     */
    @JsonProperty("currency")
    @NotBlank(message = "匯款幣別不能為空")
    @Pattern(regexp = "[A-Z]{3}", message = "匯款幣別格式錯誤")
    private String currency;
    
    /**
     * 匯款金額
     */
    @JsonProperty("amount")
    @NotNull(message = "匯款金額不能為空")
    private BigDecimal amount;
    
    /**
     * 英文姓名
     */
    @JsonProperty("payeeengname")
    @NotBlank(message = "收款人英文姓名不能為空")
    private String payeeEngName;
    
    /**
     * 收款人中文名稱
     */
    @JsonProperty("payeename")
    @NotBlank(message = "收款人中文姓名不能為空")
    private String payeeName;
    
    /**
     * 收款人ID
     */
    @JsonProperty("payeeid")
    @NotBlank(message = "收款人ID不能為空")
    private String payeeId;
    
    /**
     * 收款人帳號
     */
    @JsonProperty("payeeaccount")
    @NotBlank(message = "收款人帳號不能為空")
    private String payeeAccount;
    
    /**
     * 收款行代碼
     */
    @JsonProperty("payeebankcode")
    @NotBlank(message = "收款行代碼不能為空")
    private String payeeBankCode;
    
    /**
     * 收款人電話/手機
     */
    @JsonProperty("payeetel")
    private String payeeTel;
    
    /**
     * 收款人信箱
     */
    @JsonProperty("payeemail")
    private String payeeMail;
    
    /**
     * 匯款性質
     */
    @JsonProperty("sourceoffound")
    @NotBlank(message = "匯款性質不能為空")
    private String sourceOfFound;
    
    /**
     * 電子簽章 (法人用)
     */
    @JsonProperty("signature")
    private String signature;
    
    /**
     * 白名單標記
     * 值=S時為補通訊用，只帶中文名字和兩個編號。其它為空
     */
    @JsonProperty("whileflag")
    private String whileFlag;
}