package com.kgi.core.application.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class RoutingVO {

//    @JsonIgnore
    private String pi;

    //TODO: token 多久失效？
    private String token;

    private String sourceId;

    private String targetId;

    private String module;

    private Object data;

    private Boolean frontEndBack = false;

    public RoutingVO(){}

    public static RoutingVO build(String sourceId, String targetId, String module, Object data) {
        RoutingVO routingVO = new RoutingVO();
        routingVO.setSourceId(sourceId);
        routingVO.setTargetId(targetId);
        routingVO.setModule(module);
        routingVO.setData(data);
        return routingVO;
    }
}
