package com.kgi.core.application.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class CheckOtpVO {

    public String uniqId;
    public String txnID;
    public String txnDate;
    public String sk;
    public String otp;

    public boolean check() {
        return StringUtils.isNotEmpty(this.uniqId) && StringUtils.isNotEmpty(this.txnID) && StringUtils.isNotEmpty(this.txnDate) && StringUtils.isNotEmpty(this.sk) && StringUtils.isNotEmpty(this.otp);
    }
}
