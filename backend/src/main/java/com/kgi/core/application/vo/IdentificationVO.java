package com.kgi.core.application.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 驗生VO
 * <AUTHOR>
 */
@Data
public class IdentificationVO implements Serializable {

    //身分證
    private String idName;

    //生日
    private String birthday;

    //手機號碼
    private String mobile;

    public static IdentificationVO build(String idName, String birthday, String mobile) {
        IdentificationVO identificationVO = new IdentificationVO();

        identificationVO.setIdName(idName);
        identificationVO.setBirthday(birthday);
        identificationVO.setMobile(mobile);

        return identificationVO;
    }
}
