package com.kgi.core.application.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ApplyStartVO {

    /** 產品類別 */
    private String productId;

    private String productId_pl;

    private String productId_rpl;

    private String productId_cc;

    private String productId_d3;

    private String productId_sal;

    /** 唯一標識 UniqId */
    private String uniqId;

    /** 是否為既有戶 */
    @Deprecated
    private boolean isExistingAccount;

    /** 異業代號 */
    private String entry;

    /** 異業代號 */
    private String pj;

    /** 異業代號 */
    private String cs;


    /** 異業資料 */
    private String entryOther;

    /** 01: 體驗 02: 申請 03: 立約 04: 專人聯絡 05: 信用卡 */
    private String uniqType;

    /** 試算資料 */
    private String calMain;

    /** 試算按號 */
    private String expCaseNo;

    /** 身分證 */
//    @CheckNullAndEmpty
    private String idno;

    /** 生日 */
//    @CheckNullAndEmpty
    private String birthday;

    /** 手機 */
    private String phone;

    /** 手機 */
    private String mobile;

    /** 業務員代號 */
    private String agentNo;

    /** 通道 */
    private String channelId;

    /** 本人聲明未持有美國籍且非為其他地區稅務居民身份 */
    private boolean ch7 = false;

    /** 本人已閱讀並同意下列條款及告知事項 */
    private boolean ch1 = false;

    /** 同意使用與貴行往來之資料申辦貸款 */
    private boolean ch2 = false;

    /** FATCA及CRS聲明書
     *  屬於上線後加入欄位，因為正式區無法回補資料，故無法使用boolean
     *  null 為 未填寫
     *  0: false
     *  1: true
     * */
    private String termsFatca;

    /** 轉介單位 */
    private String referralUnit;

    /** 轉介員編 */
    private String referralEditor;

    private String ipAddress ;

    private String inputYear ;
    private String inputMonth ;
    private String inputDate ;

    /** 對 workflow 起哪個流程的變數 */
    private String pProductType;

    /** 以下為 frontend 尚無對應之欄位 */
    private String forceNewApply;
    private String browser ;
    private String platform ;
    private String os ;
    private String clToken ;
    private String routingType;
    /** 短網址 */
    private String shortUrl;

    private String userAgent;

    /** cc、 loan 異業 token **/
    private String csToken;
    
    /** D3凱證 進入token **/
    private String ksCifToken;

    /** single_D3 promoChannelID 有值 代表從凱證入口: KS 或 凱銀入口: KB  **/
    private String promoChannelID ;
    private String promoDepart;
    private String promoMember;

    /** 預約開戶的預約資訊 目前只有 login 時用得到 */
    private Date bookingDate; // 預約時間
    private String bookingBranchId; // 預約分行

    /**
     * 紀錄條款 版本號
     */
    private List<TermsVO> termsList;

    /**
     * 專案代碼
     */
    private String ccPrjCode;


    /**
     * MGM 同意條款
     */
    private String agreeRecommenders;

    /**
     * 0:使用
     * 1:不使用
     */
    private String useTM;

    /**
     * 借款人案編
     */
    private String borrowUniqId;

    /**
     * 信用卡群組
     */
    private String cardGroup;

    /**
     * 返回url
     */
    private String redirectUrl;

    /**
     * pl專案代號
     */
    private String prjCodePl;

    /**
     * rpl專案代號
     */
    private String prjCodeRpl;

    public void setProductDefault(String uniqId, String uniqType) {
        this.uniqId = uniqId;
        this.uniqType = uniqType;
    }

}
