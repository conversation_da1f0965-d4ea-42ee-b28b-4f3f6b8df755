package com.kgi.core.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IBR 數位解款統一響應格式
 * 基於數位解款 API 規範設計
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IbrApiResponse<T> {
    
    /**
     * 狀態碼
     * "00": 成功
     * "99": 失敗
     */
    @JsonProperty("StatusCode")
    private String statusCode;
    
    /**
     * 回覆訊息
     */
    @JsonProperty("TxntMsg")
    private String txntMsg;
    
    /**
     * 業務資料
     */
    @JsonProperty("data")
    private T data;
    
    /**
     * 預留欄位
     */
    @JsonProperty("Remark")
    private String remark;
    
    /**
     * 時間戳記
     */
    @JsonProperty("timestamp")
    private Long timestamp;
    
    /**
     * 成功響應
     */
    public static <T> IbrApiResponse<T> success(T data) {
        return IbrApiResponse.<T>builder()
                .statusCode("00")
                .txntMsg("成功")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 成功響應帶訊息
     */
    public static <T> IbrApiResponse<T> success(T data, String message) {
        return IbrApiResponse.<T>builder()
                .statusCode("00")
                .txntMsg(message)
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 失敗響應
     */
    public static <T> IbrApiResponse<T> error(String message) {
        return IbrApiResponse.<T>builder()
                .statusCode("99")
                .txntMsg(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 失敗響應帶錯誤碼
     */
    public static <T> IbrApiResponse<T> error(String message, String remark) {
        return IbrApiResponse.<T>builder()
                .statusCode("99")
                .txntMsg(message)
                .remark(remark)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}