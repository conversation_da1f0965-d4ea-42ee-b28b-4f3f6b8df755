package com.kgi.core.application.vo;

import lombok.Data;

@Data
public class OtpVO {
    // 案件編號
    String uniqId;
    // email
    String email;
    /**
     * 判斷 頁面是 OTP or OTP2、OTP3
     * OTP: Phone
     * OTP2: BankPhone
     * OTP3: BankPhone
     */
    String usePhone;
    // 金鑰
    String sk;
    // 發送的ID
    String txnId;
    // 發送日期
    String txnDate;
    // 驗證碼
    String otp;
    // 案件類型
    String uniqType;
    // 是否補件類型
    String isAdditionalDoc;

}
