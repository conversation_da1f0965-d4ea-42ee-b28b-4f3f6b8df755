package com.kgi.core.application.vo;


import lombok.Data;

@Data
public class OtherInfoVO {

    private String acctFundsSource="" ; // 資金來源
    private String sendTypeD3="" ; // 數位帳戶對帳單類型
    private String highestEducation="" ; //最高教育程度
    private String elementarySchoolName ="" ; //畢業國小
    private String marriage="" ; //婚姻狀況
    private String immovablesStatus="" ; //不動產狀態
    private String preTradeProduct ="" ; //預計往來業務
    private String injury ="" ;//是否領有全民健康保險 重大傷病證明
}
