package com.kgi.core.application.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * User VO
 * 每個系統的 UserVO 長相不太一樣
 * EX: D3: InitDataRequest/LoginView/CaseResp (idno, birthday, promoChannelID, promoDepart ...etc.)
 *    STP: InitDataRequest/LoginView/CaseResp (idno, birthday, entry ...etc.)
 * <AUTHOR>
 */
@Data
public class UserVO implements Serializable {

    //身分證號
    private String id;

    //TODO: id or idno?
    private String idno;

    //生日
    private String birthday;

    private String forceNewApply;

    private String isResumeBreakPoint;

    //手機
    private String mobile;

    @JsonIgnore
    private String ipAddress;

    @JsonIgnore
    private String browser;

    @JsonIgnore
    private String platform;

    @JsonIgnore
    private String os;

    private String borrowUniqId;

    public static UserVO build(String id, String birthday, String mobile) {
        UserVO userVO = new UserVO();
        userVO.setId(id);
        userVO.setBirthday(birthday);
        userVO.setMobile(mobile);

        return userVO;
    }
}
