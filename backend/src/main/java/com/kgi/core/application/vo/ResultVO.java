package com.kgi.core.application.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ResultVO implements Serializable {

    private String status;
    private String message;
    private String result;

    public static ResultVO GetResultWithMessage(String status, String message, String result) {
        ResultVO resultVO = new ResultVO();
        resultVO.setStatus(status);
        resultVO.setMessage(message);
        resultVO.setResult(result);
        return resultVO;
    }

    @Override
    public String toString() {
        return "{" +
                "status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", result='" + result + '\'' +
                '}';
    }
}
