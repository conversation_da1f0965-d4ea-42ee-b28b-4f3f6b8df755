package com.kgi.core.application.vo;

import com.google.gson.Gson;
import com.kgi.core.domain.code.ReturnCode;
import com.kgi.core.util.EncryptUtils;
import com.kgi.core.util.OWASPSecurity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.owasp.esapi.ESAPI;

import java.io.Serializable;

/**
 * <AUTHOR>
 * WebResult -> ResponseVO
 */
@Slf4j
@Data
public class ResponseVO<T> implements Serializable {

    private String rtnCode; // 回傳代碼
    private String rtnMessage; // 回傳訊息

    private T rtnObj; // 回傳物件(結果)

    public ResponseVO() {}

    ResponseVO(String rtnCode, String rtnMessage, T rtnObj) {
        this.rtnCode = rtnCode;
        this.rtnMessage = rtnMessage;
        this.rtnObj = rtnObj;
    }


    public ResponseVO encrypt(){
        try {
            this.rtnObj = (T) EncryptUtils.base64EncodeByUTF8(new Gson().toJson(this.rtnObj));
            log.trace("回傳資料Encode... = " + this.rtnObj);
        }catch (Exception e){
            log.error("回傳資料Encode Fail");
        }
        return this;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public void setRtnCode(String rtnCode) {
        this.rtnCode = rtnCode;
    }

    public String getRtnMessage() {
        return rtnMessage;
    }

    public void setRtnMessage(String rtnMessage) {
        this.rtnMessage = rtnMessage;
    }

    public T getRtnObj() {
        return rtnObj;
    }

    public void setRtnObj(T rtnObj) {
        this.rtnObj = rtnObj;
    }

    public static ResponseVO GetResultWithMessage(String status, String message) {
        return GetResultWithMessage(status, message, null);
    }

    public static ResponseVO GetResultWithMessage(String status, String message, Object rtnObj) {
        try {
            log.info("GetResultWithMessage  status = " + status);
            log.info("GetResultWithMessage  message = " + message);
//            log.info("GetResultWithMessage  rtnObj = " + rtnObj);
            if(ESAPI.validator().isValidInput("ResponseVO", message, OWASPSecurity.TYPE_SPACE, Integer.MAX_VALUE, false , false)){
                log.info("valid Success");
                return new ResponseVO(String.valueOf(status), message, rtnObj);
            }else{
                log.error("valid Invalid Result");
                return new ResponseVO(ReturnCode.ERROR.getRtnCode(), "Invalid Result", null);
            }
        } catch (Exception e) {
            log.error("valid Invalid Result Exception:" + ExceptionUtils.getStackTrace(e));
            return new ResponseVO(ReturnCode.ERROR.getRtnCode(), "Invalid Result", null);
        }
    }

    @Deprecated
    //TODO: codes最佳化,開發之後改名為 GetResponseVOWithMessage
    public static ResponseVO GetResultWithMessage(Integer status, String message, Object rtnObj) {
        return GetResultWithMessage(String.valueOf(status), message, rtnObj);
    }

    //codes最佳化,開發之後改名為 GetFailResponseVO
    public static ResponseVO GetFailResult(){
        return new ResponseVO(ReturnCode.ERROR.getRtnCode(), ReturnCode.ERROR.getRtnMessage(), null);
    }

    //codes最佳化,開發之後改名為 GetSystemFailResponseVO
    public static ResponseVO GetSystemFailResult(){
        return new ResponseVO(ReturnCode.ERROR.getRtnCode(), ReturnCode.ERROR.getRtnMessage(), null);
    }

    //codes最佳化,開發之後改名為 GetSuccessResponseVO
    public static ResponseVO GetSuccessResult(Object rtnObj) {
        return new ResponseVO(ReturnCode.SUCCESS.getRtnCode(), ReturnCode.SUCCESS.getRtnMessage(), rtnObj);
    }

    @Override
    public String toString() {
        return com.kgi.core.util.JsonUtil.toJson(this);
    }
}
