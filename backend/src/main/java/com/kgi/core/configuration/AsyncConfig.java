package com.kgi.core.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 非同步執行配置
 * 設定非同步任務執行器
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 非同步任務執行器
     */
    @Bean(name = "taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心執行緒數
        executor.setCorePoolSize(5);
        
        // 最大執行緒數
        executor.setMaxPoolSize(20);
        
        // 佇列容量
        executor.setQueueCapacity(100);
        
        // 執行緒名稱前綴
        executor.setThreadNamePrefix("IBR-Async-");
        
        // 等待任務完成後關閉
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待時間（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }

    /**
     * 通知專用執行器
     */
    @Bean(name = "notificationExecutor")
    public Executor notificationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("IBR-Notification-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        return executor;
    }
}