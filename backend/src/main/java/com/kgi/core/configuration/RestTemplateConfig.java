package com.kgi.core.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * REST客戶端配置
 * 設定外部API呼叫相關配置
 */
@Configuration
public class RestTemplateConfig {

    /**
     * REST模板配置（簡化版本以避免依賴問題）
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }

    /**
     * HTTP客戶端請求工廠配置（使用內建的 SimpleClientHttpRequestFactory）
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        
        // 連線超時設定（10秒）
        factory.setConnectTimeout(10000);
        
        // 讀取超時設定（30秒）
        factory.setReadTimeout(30000);
        
        return factory;
    }
}