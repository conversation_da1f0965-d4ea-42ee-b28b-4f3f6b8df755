package com.kgi.core.controller;

import com.kgi.core.service.mock.base.MockDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Mock 測試控制器
 * 僅在 Mock 模式啟用時可用，用於測試和開發
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/api/mock")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "kgi.useMockupData", havingValue = "true")
public class MockTestController {

    private final MockDataRepository mockDataRepository;

    /**
     * 檢查 Mock 服務狀態
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getMockStatus() {
        log.info("Checking mock service status");
        
        return ResponseEntity.ok(Map.of(
            "mockEnabled", true,
            "dataCount", mockDataRepository.getDataCount(),
            "message", "Mock services are running",
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * 取得 Mock 資料
     */
    @GetMapping("/data/{key}")
    public ResponseEntity<Object> getMockData(@PathVariable String key) {
        log.info("Getting mock data for key: {}", key);
        
        Object data = mockDataRepository.getData(key, Object.class);
        if (data == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(data);
    }

    /**
     * 設定 Mock 資料
     */
    @PostMapping("/data/{key}")
    public ResponseEntity<Map<String, String>> setMockData(
            @PathVariable String key,
            @RequestBody Object data) {
        log.info("Setting mock data for key: {}", key);
        
        mockDataRepository.saveData(key, data);
        
        return ResponseEntity.ok(Map.of(
            "message", "Mock data saved successfully",
            "key", key
        ));
    }

    /**
     * 刪除 Mock 資料
     */
    @DeleteMapping("/data/{key}")
    public ResponseEntity<Map<String, String>> deleteMockData(@PathVariable String key) {
        log.info("Deleting mock data for key: {}", key);
        
        mockDataRepository.removeData(key);
        
        return ResponseEntity.ok(Map.of(
            "message", "Mock data deleted successfully",
            "key", key
        ));
    }

    /**
     * 重置所有 Mock 資料
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, String>> resetMockData() {
        log.info("Resetting all mock data");
        
        mockDataRepository.clearAll();
        mockDataRepository.initDefaultData();
        
        return ResponseEntity.ok(Map.of(
            "message", "Mock data reset successfully",
            "dataCount", String.valueOf(mockDataRepository.getDataCount())
        ));
    }
}