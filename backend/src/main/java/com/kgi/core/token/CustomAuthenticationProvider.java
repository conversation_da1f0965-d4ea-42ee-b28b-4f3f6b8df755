package com.kgi.core.token;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.kgi.core.application.vo.ApplyStartVO;
import com.kgi.core.application.vo.ResponseVO;
import com.kgi.core.util.CheckUtil;
import com.kgi.core.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;

import java.util.ArrayList;

/**
 * TODO: clarify from STP (airloanEx)
 */
public class CustomAuthenticationProvider implements AuthenticationProvider {


    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public CustomAuthenticationProvider(ApplicationContext appContext) {
        appContext.getAutowireCapableBeanFactory().autowireBean(this);
    }

    //For testing
    boolean skipBreakPointCheck = true;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        logger.info("[CustomAuthenticationProvider] authentication");

//        System.out.println("getPrincipal: " + com.kgi.core.util.JsonUtil.toJson(authentication.getPrincipal()));

        ApplyStartVO applyStartVO = (ApplyStartVO) authentication.getPrincipal();

        if (!CheckUtil.check(applyStartVO)) {
            throw new BadCredentialsException("驗證失敗");
        } else {
            ResponseVO<ApplyStartVO> responseVO = null;

            try {
                responseVO = ResponseVO.GetSuccessResult(applyStartVO);
            } catch (Exception e) {
                e.printStackTrace();
            }
            Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ").create();
            String principal = JsonUtil.toJson(responseVO);
            // 依據回傳的結果設定權限(如果有)
            ArrayList<GrantedAuthority> authorities = new ArrayList<>();
            // 生成令牌
            Authentication auth = new UsernamePasswordAuthenticationToken(principal, null, authorities);
           logger.info("Authentication END");
            return auth;
        }


    }

    // 是否可以提供输入類型的驗證服務
    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }

    private Authentication authJsonResp(String principal, ArrayList<GrantedAuthority> authorities){
      return new UsernamePasswordAuthenticationToken(principal, null, authorities);
    }
}
