package com.kgi.core.token;

import com.kgi.core.application.vo.ApplyStartVO;
import com.kgi.core.application.vo.ResponseVO;
import com.kgi.core.util.JsonUtil;
import com.kgi.core.util.RequestUtil;
import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;


public class JWTLoginFilter extends AbstractAuthenticationProcessingFilter {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private final UserAgentAnalyzer uaa;

    public JWTLoginFilter(String url, AuthenticationManager authManager) {
        super(new AntPathRequestMatcher(url));
        setAuthenticationManager(authManager);

        uaa = UserAgentAnalyzer
                .newBuilder()
                .withAllFields()
                .build();
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse res)
            throws AuthenticationException, IOException, ServletException {

        logger.info("Entering JWTLoginFilter attemptAuthentication ...");

        // 登入資訊解密 有需要的話在此做
        // JSON反序列化成 AccountCredentials
        String accCredentials = new BufferedReader(
                new InputStreamReader(req.getInputStream(), StandardCharsets.UTF_8))
                .lines()
                .collect(Collectors.joining("\n"));

        ApplyStartVO applyStartVO = JsonUtil.toPOJO(accCredentials, ApplyStartVO.class);

        applyStartVO.setIpAddress(RequestUtil.getIpAddr(req));

        // UserAgent 詳細如下，
        // - user_agent_string: 'Mozilla/5.0 (Linux; Android 7.0; Nexus 6 Build/NBD90Z) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/53.0.2785.124 Mobile Safari/537.36'
        // DeviceClass                      : 'Phone'
        // DeviceName                       : 'Google Nexus 6'
        // DeviceBrand                      : 'Google'
        // OperatingSystemClass             : 'Mobile'
        // OperatingSystemName              : 'Android'
        // OperatingSystemVersion           : '7.0'
        // OperatingSystemVersionMajor      : '7'
        // OperatingSystemNameVersion       : 'Android 7.0'
        // OperatingSystemNameVersionMajor  : 'Android 7'
        // OperatingSystemVersionBuild      : 'NBD90Z'
        // LayoutEngineClass                : 'Browser'
        // LayoutEngineName                 : 'Blink'
        // LayoutEngineVersion              : '53.0'
        // LayoutEngineVersionMajor         : '53'
        // LayoutEngineNameVersion          : 'Blink 53.0'
        // LayoutEngineNameVersionMajor     : 'Blink 53'
        // AgentClass                       : 'Browser'
        // AgentName                        : 'Chrome'
        // AgentVersion                     : '53.0.2785.124'
        // AgentVersionMajor                : '53'
        // AgentNameVersion                 : 'Chrome 53.0.2785.124'
        // AgentNameVersionMajor            : 'Chrome 53'
        UserAgent userAgent = uaa.parse(req.getHeader("User-Agent"));
        String browser = userAgent.getValue("AgentNameVersionMajor");
        String platform = userAgent.getValue("DeviceClass");
        String os = userAgent.getValue("OperatingSystemNameVersionMajor");

        // 避免 os 版本無法正常解譯
        if(os.contains("?")) {
            os = os.replace("?", "") + System.getProperty("os.version");
        }

        applyStartVO.setBrowser(browser);
        applyStartVO.setPlatform(platform);
        applyStartVO.setOs(os);
        applyStartVO.setUserAgent(userAgent.getUserAgentString());

        UsernamePasswordAuthenticationToken uToken = new UsernamePasswordAuthenticationToken(applyStartVO, null);
        Authentication authentication = getAuthenticationManager().authenticate(uToken);

        return authentication; // 返回驗證TOKEN
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                            Authentication auth) throws IOException, ServletException {
        logger.info("successfulAuthentication auth: " + auth.getName());
        byte[] binary = new BigInteger(auth.getName().getBytes()).toByteArray();
        BigInteger result = new BigInteger(binary);
        String binaryString = result.toString();
        logger.info("binaryString length: " + binaryString.length());
        TokenAuthenticationService.addAuthentication(req, res, binaryString);
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                              AuthenticationException failed) throws IOException, ServletException {
        logger.info("unsuccessfulAuthentication");
        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
        response.setStatus(HttpServletResponse.SC_OK);
        String msg = failed.getMessage();
        if (ESAPI.validator().isValidInput("unsuccessfulAuthentication", msg, "Space", Integer.MAX_VALUE, false)) {
            response.getWriter().print(ResponseVO.GetResultWithMessage(500, msg, null));

        } else {
            response.getWriter().print(ResponseVO.GetResultWithMessage(500, "", null));
        }
    }
}
