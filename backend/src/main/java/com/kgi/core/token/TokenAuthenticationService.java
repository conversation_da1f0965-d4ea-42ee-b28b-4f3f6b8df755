package com.kgi.core.token;

import com.google.gson.Gson;
import com.kgi.core.application.vo.AIOCaseDataVO;
import com.kgi.core.application.vo.CaseInitVO;
import com.kgi.core.application.vo.ResponseVO;
import com.kgi.core.application.vo.UserVO;
import com.kgi.core.domain.code.ReturnCode;
import com.kgi.core.util.JsonUtil;
import com.kgi.core.util.OWASPSecurity;
import com.kgi.core.util.SystemConst;
import com.kgi.core.util.TokenUtil;
import com.nimbusds.jose.crypto.DirectDecrypter;
import com.nimbusds.jwt.EncryptedJWT;
import org.modelmapper.ModelMapper;
import org.modelmapper.convention.MatchingStrategies;
import org.owasp.esapi.ESAPI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.math.BigInteger;

public class TokenAuthenticationService {

    /**
     * 有效時間 5天
     */
    //static final long EXPIRATIONTIME = 432_000_000;
    /**
     * JWT密碼
     */
    //static final String SECRET = "P@ssw02d";

    //static final byte[] key = "kgieopKGIOBD201907".getBytes();
    /**
     * Token前缀
     */
    //static final String TOKEN_PREFIX = "Bearer";
    /**
     * 存放Token的Header Key
     */
    //static final String HEADER_STRING = "Authorization";

    protected static Logger logger = LoggerFactory.getLogger(TokenAuthenticationService.class);

    public static void addAuthentication(HttpServletRequest request, HttpServletResponse response, String principal) {

        // 產生JWT
        try {
            String inputValue = OWASPSecurity.getAntiXSSContentAllowNull(principal, Integer.MAX_VALUE);
            BigInteger binaryBigInt = new BigInteger(inputValue);
            byte[] binaryBytes = binaryBigInt.toByteArray();
            String value = new String(binaryBytes);

            Gson gson = new Gson();
            ResponseVO<AIOCaseDataVO> caseVO = gson.fromJson(value, ResponseVO.class);
            logger.info("addAuthentication caseVO: " + caseVO.toString());
            //產生 JWT
            AIOCaseDataVO applyStartVO =  JsonUtil.toPOJO(caseVO.getRtnObj(), AIOCaseDataVO.class);
            UserVO userVO = new UserVO();

            ModelMapper modelMapper = new ModelMapper();
            modelMapper.getConfiguration().setSkipNullEnabled(true).setMatchingStrategy(MatchingStrategies.STRICT);
            modelMapper.map(applyStartVO, userVO);

            String jwt = TokenUtil.addAuthentication(gson.toJson(userVO));

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
            response.setStatus(HttpServletResponse.SC_OK);

            //TODO: 組合出 token + cif VO
            CaseInitVO caseInitVO = new CaseInitVO(jwt, caseVO);
            PrintWriter pw = response.getWriter();

            ResponseVO re = ResponseVO.GetResultWithMessage(ReturnCode.SUCCESS.getRtnCode(), ReturnCode.SUCCESS.getRtnMessage(), caseInitVO);
            ESAPI.encoder().encodeForHTML(re.toString());
            pw.print(re);
        } catch (Exception e) {
            logger.error("未知錯誤", e);
        }
    }


    static Authentication getAuthentication(HttpServletRequest request) throws Exception {

        //從Header中拿到token
        String token = request.getHeader(SystemConst.HEADER_STRING);
//        System.out.println("[TokenAuthenticationService] getAuthentication token:" + token);

        String header = null;
        if (token != null) {
            // 解析 Token
            String jwt = token.substring(SystemConst.TOKEN_PREFIX.length());

            EncryptedJWT jweObject = EncryptedJWT.parse(jwt);

            // do the decryption
            jweObject.decrypt(new DirectDecrypter(SystemConst.key));

            header = jweObject.getJWTClaimsSet().getSubject();

            // 回傳驗證過的資料
            return (header != null&&!header.equals("")) ? new UsernamePasswordAuthenticationToken(header, null, null) : null;

        }
        return null;
    }

}
