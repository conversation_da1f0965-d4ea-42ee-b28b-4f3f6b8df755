package com.kgi.core.token;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;


public class JWTAuthenticationFilter extends GenericFilterBean {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        Authentication authentication = null;
        try {

            authentication = TokenAuthenticationService.getAuthentication((HttpServletRequest) request);
        } catch (Exception e) {
            logger.error("jwtToken 失效啦!!!!" + ExceptionUtils.getStackTrace(e));
            logger.error("[ JWTAuthenticationFilter.java ]=(doFilter - 28) 增加log 追蹤  backend problem 無法登入問題 :" + request);

            throw new IOException("未知錯誤");
        }

        SecurityContextHolder.getContext().setAuthentication(authentication);
        try {
            logger.info("[ JWTAuthenticationFilter.java ]=(doFilter - 35) request:" +request ) ;
            logger.info("[ JWTAuthenticationFilter.java ]=(doFilter - 35) response:" +response ) ;
            chain.doFilter(request, response);
        }catch (Exception e){
            logger.info("token 無法通過驗證請再檢查: " + ExceptionUtils.getStackTrace(e));
        }
    }
}
