package com.kgi.core.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 通知服務
 * 負責發送Email和SMS通知給客戶
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {
    
    /**
     * 發送客戶通知
     */
    public Map<String, Object> sendCustomerNotification(
            Map<String, Object> toApiData, 
            String caseNo, 
            Map<String, String> urls) {
        
        String email = (String) toApiData.get("PayeeMail");
        String phone = (String) toApiData.get("PayeeTel");
        String payeeName = (String) toApiData.get("PayeeName");
        
        log.info("發送客戶通知: caseNo={}, email={}, phone={}", caseNo, email, maskPhone(phone));
        
        // 發送Email
        boolean emailSent = sendEmailNotification(email, payeeName, caseNo, urls);
        
        // 發送SMS
        boolean smsSent = sendSmsNotification(phone, payeeName, caseNo, urls.get("applicationUrl"));
        
        Map<String, Object> result = new HashMap<>();
        result.put("emailSent", emailSent);
        result.put("smsSent", smsSent);
        result.put("recipientEmail", email);
        result.put("recipientPhone", maskPhone(phone));
        result.put("caseNo", caseNo);
        result.put("applicationUrl", urls.get("applicationUrl"));
        result.put("queryUrl", urls.get("queryUrl"));
        result.put("sentTime", LocalDateTime.now().toString());
        
        return result;
    }
    
    /**
     * 發送Email通知
     */
    private boolean sendEmailNotification(String email, String name, String caseNo, Map<String, String> urls) {
        try {
            String subject = "凱基銀行跨境解款通知";
            String content = buildEmailContent(name, caseNo, urls);
            
            // TODO: 實際發送Email
            log.info("Email發送成功: to={}, subject={}", email, subject);
            
            return true;
        } catch (Exception e) {
            log.error("Email發送失敗", e);
            return false;
        }
    }
    
    /**
     * 發送SMS通知
     */
    private boolean sendSmsNotification(String phone, String name, String caseNo, String applicationUrl) {
        try {
            String message = buildSmsContent(name, caseNo, applicationUrl);
            
            // TODO: 實際發送SMS
            log.info("SMS發送成功: to={}", maskPhone(phone));
            
            return true;
        } catch (Exception e) {
            log.error("SMS發送失敗", e);
            return false;
        }
    }
    
    /**
     * 建立Email內容
     */
    private String buildEmailContent(String name, String caseNo, Map<String, String> urls) {
        StringBuilder content = new StringBuilder();
        content.append("親愛的 ").append(name).append(" 您好：\n\n");
        content.append("您有一筆跨境匯款待處理，案件編號：").append(caseNo).append("\n\n");
        content.append("請點擊以下連結進行處理：\n");
        content.append("- 申請解款：").append(urls.get("applicationUrl")).append("\n");
        content.append("- 查詢進度：").append(urls.get("queryUrl")).append("\n");
        content.append("- 補件申請：").append(urls.get("supplementUrl")).append("\n\n");
        content.append("如有任何問題，請洽詢客服專線：0800-123-456\n\n");
        content.append("凱基銀行 敬上");
        
        return content.toString();
    }
    
    /**
     * 建立SMS內容
     */
    private String buildSmsContent(String name, String caseNo, String applicationUrl) {
        return String.format(
            "【凱基銀行】%s您好，您有跨境匯款待處理(案號:%s)，請點擊連結處理: %s",
            name, caseNo, applicationUrl
        );
    }
    
    /**
     * 遮罩電話號碼
     */
    private String maskPhone(String phone) {
        if (phone == null || phone.length() < 4) {
            return phone;
        }
        return phone.substring(0, 4) + "****" + phone.substring(phone.length() - 2);
    }
}