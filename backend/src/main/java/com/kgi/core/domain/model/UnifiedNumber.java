package com.kgi.core.domain.model;

import com.kgi.core.domain.exception.BusinessException;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import jakarta.persistence.Embeddable;
import java.util.regex.Pattern;

/**
 * 統一編號值對象
 * 包含格式驗證和檢查碼驗證
 */
@Getter
@EqualsAndHashCode
@Embeddable
public class UnifiedNumber {
    
    private static final Pattern UNIFIED_NUMBER_PATTERN = Pattern.compile("^\\d{8}$");
    private static final int[] WEIGHTS = {1, 2, 1, 2, 1, 2, 4, 1};
    
    private final String value;
    
    private UnifiedNumber(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new BusinessException("統一編號不能為空", "INVALID_UNIFIED_NUMBER");
        }
        
        String trimmedValue = value.trim();
        if (!isValid(trimmedValue)) {
            throw new BusinessException("統一編號格式錯誤", "INVALID_UNIFIED_NUMBER");
        }
        
        this.value = trimmedValue;
    }
    
    /**
     * 驗證統一編號
     */
    public static boolean isValid(String number) {
        if (number == null || !UNIFIED_NUMBER_PATTERN.matcher(number).matches()) {
            return false;
        }
        
        return validateChecksum(number);
    }
    
    /**
     * 驗證檢查碼
     * 統一編號檢查碼演算法
     */
    private static boolean validateChecksum(String number) {
        int[] digits = number.chars()
                .map(c -> c - '0')
                .toArray();
        
        int sum = 0;
        for (int i = 0; i < 8; i++) {
            int product = digits[i] * WEIGHTS[i];
            sum += (product / 10) + (product % 10);
        }
        
        // 特殊情況：第7位數字為7時的額外驗證
        boolean normalCheck = sum % 10 == 0;
        boolean specialCheck = digits[6] == 7 && (sum - 10) % 10 == 0;
        
        return normalCheck || specialCheck;
    }
    
    /**
     * 建立統一編號實例
     */
    public static UnifiedNumber of(String value) {
        return new UnifiedNumber(value);
    }
    
    /**
     * 驗證當前統一編號實例
     */
    public boolean isValid() {
        return isValid(this.value);
    }
    
    /**
     * 格式化顯示
     */
    public String getFormatted() {
        if (value.length() != 8) {
            return value;
        }
        return value.substring(0, 2) + "-" + value.substring(2, 8);
    }
    
    /**
     * 取得遮罩統一編號（前2位和後2位顯示，中間用*代替）
     */
    public String getMaskedValue() {
        if (value.length() != 8) {
            return value;
        }
        return value.substring(0, 2) + "****" + value.substring(6, 8);
    }
    
    @Override
    public String toString() {
        return value;
    }
}