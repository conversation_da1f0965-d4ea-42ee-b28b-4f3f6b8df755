package com.kgi.core.domain.model;

import com.kgi.core.domain.exception.BusinessException;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import jakarta.persistence.Embeddable;
import java.util.regex.Pattern;

/**
 * 台灣身分證號值對象
 * 包含格式驗證和檢查碼驗證
 */
@Getter
@EqualsAndHashCode
@Embeddable
public class TaiwanId {
    
    private static final Pattern ID_PATTERN = Pattern.compile("^[A-Z][12]\\d{8}$");
    
    private static final int[] AREA_CODES = {
        10, 11, 12, 13, 14, 15, 16, 17, 34, 18, 19, 20, 21, 
        22, 35, 23, 24, 25, 26, 27, 28, 29, 32, 30, 31, 33
    };
    
    private final String value;
    
    private TaiwanId(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new BusinessException("台灣身分證號不能為空", "INVALID_TAIWAN_ID");
        }
        
        String trimmedValue = value.trim().toUpperCase();
        if (!isValid(trimmedValue)) {
            throw new BusinessException("台灣身分證號格式錯誤", "INVALID_TAIWAN_ID");
        }
        
        this.value = trimmedValue;
    }
    
    /**
     * 建立台灣身分證號實例
     */
    public static TaiwanId of(String value) {
        return new TaiwanId(value);
    }
    
    /**
     * 驗證當前台灣身分證號實例
     */
    public boolean isValid() {
        return isValid(this.value);
    }
    
    /**
     * 驗證台灣身分證號
     */
    public static boolean isValid(String id) {
        if (id == null || !ID_PATTERN.matcher(id).matches()) {
            return false;
        }
        
        return validateChecksum(id);
    }
    
    /**
     * 驗證檢查碼
     */
    private static boolean validateChecksum(String id) {
        // 取得地區碼
        char firstChar = id.charAt(0);
        int areaCode = AREA_CODES[firstChar - 'A'];
        
        // 計算檢查碼
        int sum = (areaCode / 10) + (areaCode % 10) * 9;
        
        for (int i = 1; i < 9; i++) {
            sum += Character.getNumericValue(id.charAt(i)) * (9 - i);
        }
        
        int checkDigit = Character.getNumericValue(id.charAt(9));
        return (sum + checkDigit) % 10 == 0;
    }
    
    /**
     * 遮罩顯示（中間四位用*代替）
     */
    public String getMasked() {
        if (value.length() != 10) {
            return value;
        }
        return value.substring(0, 3) + "****" + value.substring(7);
    }
    
    /**
     * 取得遮罩值（別名方法）
     */
    public String getMaskedValue() {
        return getMasked();
    }
    
    @Override
    public String toString() {
        return value;
    }
}