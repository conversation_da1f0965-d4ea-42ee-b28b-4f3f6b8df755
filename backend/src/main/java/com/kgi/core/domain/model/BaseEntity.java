package com.kgi.core.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 基礎實體類
 * 包含審計欄位和共用方法
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity {
    
    /**
     * 主鍵ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 創建時間
     */
    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 最後修改時間
     */
    @LastModifiedDate
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 版本號 (樂觀鎖)
     */
    @Version
    @Column(name = "version")
    private Integer version;
    
    /**
     * 是否刪除 (軟刪除)
     */
    @Builder.Default
    @Column(name = "is_deleted")
    private Boolean isDeleted = false;
    
    /**
     * 創建者
     */
    @Column(name = "created_by", length = 50)
    private String createdBy;
    
    /**
     * 最後修改者
     */
    @Column(name = "updated_by", length = 50)
    private String updatedBy;
    
    /**
     * 備註
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 軟刪除
     */
    public void softDelete() {
        this.isDeleted = true;
    }
    
    /**
     * 檢查是否為新實體
     */
    public boolean isNew() {
        return this.id == null;
    }
}