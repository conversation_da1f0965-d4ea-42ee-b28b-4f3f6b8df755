package com.kgi.core.domain.valueobject;

import com.kgi.core.domain.exception.BusinessException;
import lombok.Value;

/**
 * 統一編號 Value Object
 * 處理台灣企業統一編號的驗證與操作
 */
@Value
public class UnifiedNumber {
    
    String value;
    
    private UnifiedNumber(String value) {
        if (!isValid(value)) {
            throw new BusinessException("INVALID_UNIFIED_NUMBER", "統一編號格式錯誤: " + value);
        }
        this.value = value;
    }
    
    /**
     * 創建統一編號實例
     */
    public static UnifiedNumber of(String value) {
        return new UnifiedNumber(value);
    }
    
    /**
     * 驗證統一編號格式
     */
    public static boolean isValid(String unifiedNumber) {
        if (unifiedNumber == null || unifiedNumber.length() != 8) {
            return false;
        }
        
        // 檢查是否為8位數字
        if (!unifiedNumber.matches("^\\d{8}$")) {
            return false;
        }
        
        // 檢查統一編號檢查碼
        return validateChecksum(unifiedNumber);
    }
    
    /**
     * 驗證統一編號檢查碼
     */
    private static boolean validateChecksum(String unifiedNumber) {
        int[] multipliers = {1, 2, 1, 2, 1, 2, 4, 1};
        int sum = 0;
        
        for (int i = 0; i < 8; i++) {
            int digit = Character.getNumericValue(unifiedNumber.charAt(i));
            int product = digit * multipliers[i];
            
            // 如果乘積是兩位數，將十位數和個位數分別加入總和
            if (product >= 10) {
                sum += (product / 10) + (product % 10);
            } else {
                sum += product;
            }
        }
        
        // 檢查碼驗證
        int remainder = sum % 10;
        if (remainder == 0) {
            return true;
        }
        
        // 第7位數字是7的特殊處理
        if (unifiedNumber.charAt(6) == '7') {
            return (sum + 1) % 10 == 0;
        }
        
        return false;
    }
    
    /**
     * 遮罩統一編號（僅顯示前兩位和後兩位）
     */
    public String getMasked() {
        if (value == null || value.length() != 8) {
            return value;
        }
        return value.substring(0, 2) + "****" + value.substring(6);
    }
    
    /**
     * 取得原始值
     */
    public String getValue() {
        return value;
    }
    
    @Override
    public String toString() {
        return value;
    }
}