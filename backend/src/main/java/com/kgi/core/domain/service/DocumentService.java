package com.kgi.core.domain.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 文件服務
 * 負責處理補件文件的上傳、驗證和管理
 */
@Slf4j
@Service
public class DocumentService {
    
    // 支援的檔案類型
    private static final Set<String> SUPPORTED_FILE_TYPES = Set.of(
        "image/jpeg", "image/jpg", "image/png", "application/pdf"
    );
    
    // 檔案大小限制 (10MB)
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;
    
    /**
     * 上傳文件
     */
    public Map<String, Object> uploadDocument(
            String caseNo,
            String documentType,
            MultipartFile file) {
        
        log.info("上傳文件: caseNo={}, documentType={}, fileName={}", 
                caseNo, documentType, file.getOriginalFilename());
        
        // 驗證檔案
        validateFile(file);
        
        // 生成檔案ID
        String documentId = generateDocumentId();
        
        // 儲存檔案 (實際應儲存到檔案系統或雲端儲存)
        String filePath = saveFile(caseNo, documentId, file);
        
        // 建立文件記錄
        Map<String, Object> documentRecord = new HashMap<>();
        documentRecord.put("documentId", documentId);
        documentRecord.put("caseNo", caseNo);
        documentRecord.put("documentType", documentType);
        documentRecord.put("fileName", file.getOriginalFilename());
        documentRecord.put("fileSize", file.getSize());
        documentRecord.put("mimeType", file.getContentType());
        documentRecord.put("filePath", filePath);
        documentRecord.put("uploadTime", LocalDateTime.now().toString());
        documentRecord.put("status", "UPLOADED");
        
        // 儲存文件記錄到資料庫
        saveDocumentRecord(documentRecord);
        
        return documentRecord;
    }
    
    /**
     * 驗證檔案
     */
    private void validateFile(MultipartFile file) {
        // 檢查檔案是否為空
        if (file.isEmpty()) {
            throw new IllegalArgumentException("檔案不能為空");
        }
        
        // 檢查檔案大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("檔案大小超過限制 (最大10MB)");
        }
        
        // 檢查檔案類型
        String contentType = file.getContentType();
        if (contentType == null || !SUPPORTED_FILE_TYPES.contains(contentType)) {
            throw new IllegalArgumentException("不支援的檔案類型: " + contentType);
        }
    }
    
    /**
     * 儲存檔案
     */
    private String saveFile(String caseNo, String documentId, MultipartFile file) {
        // 實際應儲存到檔案系統或雲端儲存
        // 這裡返回模擬路徑
        return "/documents/" + caseNo + "/" + documentId + "/" + file.getOriginalFilename();
    }
    
    /**
     * 驗證文件完整性
     */
    public Map<String, Object> validateDocumentCompleteness(
            String caseNo, 
            List<Map<String, Object>> requiredDocuments,
            List<String> uploadedDocumentTypes) {
        
        Map<String, Object> result = new HashMap<>();
        List<String> missingDocuments = new ArrayList<>();
        
        for (Map<String, Object> doc : requiredDocuments) {
            if ((boolean) doc.get("required")) {
                String docType = (String) doc.get("documentType");
                if (!uploadedDocumentTypes.contains(docType)) {
                    missingDocuments.add((String) doc.get("name"));
                }
            }
        }
        
        result.put("isComplete", missingDocuments.isEmpty());
        result.put("missingDocuments", missingDocuments);
        result.put("uploadedCount", uploadedDocumentTypes.size());
        result.put("requiredCount", requiredDocuments.stream()
                .filter(doc -> (boolean) doc.get("required"))
                .count());
        
        return result;
    }
    
    /**
     * 取得文件清單
     */
    public List<Map<String, Object>> getDocumentList(String caseNo) {
        log.info("查詢文件清單: caseNo={}", caseNo);
        
        // 實際應從資料庫查詢
        // 這裡返回模擬資料
        return List.of(
            Map.of(
                "documentId", "DOC-12345678",
                "documentType", "ID_CARD_FRONT",
                "fileName", "id_front.jpg",
                "fileSize", 1024000L,
                "uploadTime", LocalDateTime.now().minusMinutes(10).toString(),
                "status", "UPLOADED"
            )
        );
    }
    
    /**
     * 刪除文件
     */
    public void deleteDocument(String caseNo, String documentId) {
        log.info("刪除文件: caseNo={}, documentId={}", caseNo, documentId);
        
        // 實際應刪除檔案和資料庫記錄
        // 這裡只記錄日誌
    }
    
    /**
     * 生成文件ID
     */
    private String generateDocumentId() {
        return "DOC-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    /**
     * 儲存文件記錄
     */
    private void saveDocumentRecord(Map<String, Object> documentRecord) {
        log.info("儲存文件記錄: documentId={}", documentRecord.get("documentId"));
        // 實際應儲存到資料庫
    }
    
    /**
     * 取得檔案擴展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }
}