/**
 * Copyright (c) 2020 Madison Data Consulting
 * All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of
 * Madison Data Consulting ("Confidential Information").
 * <p>
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of license agreement you entered
 * into with Madison Data Consulting
 */

package com.kgi.core.domain.code;

import com.kgi.core.util.SystemConst;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;

/**
 * [處理結果]代碼
 *
 * <AUTHOR>
 */

public enum ReturnCode {
    SUCCESS("000", "執行成功", false), //
    SUCCESS_NO_DATA("001", "執行成功，無資料", false), //
    SUCCESS_ASK_RESUME_BREAKPOINT("002", "是否要回復上一次申辦的資料", false), //
    ERROR_DB_LOCK("003", "DB LOCK", true), //
    SUCCESS_VERIFICATION_MESSAGE("004", "Verification訊息提示", true), //

    ERROR_RETRY("1000", "RETRY", true), // Transactional conflict

    ERROR_SYSTEM("99", "系統問題", true), // 系統問題
    ERROR_INVALID("90", "執行失敗(不合法的存取)", true), // 執行失敗(不合法的存取)

    ERROR_NEW_CASE_CHK("97", "系統問題", true), // 系統問題
    ERROR_NON_VALUE("007", "No value", true), // No value

    /** r2-3141 圖片超過10MB */
    ERROR_PIC("999", "圖片超過10MB", true),

    /** 起案代號 */
    APPLY_IDENTIFIED_CHECK_RUNNING("0", "起案中", false),
    APPLY_IDENTIFIED_CHECK_SUCCESS("1", "起案成功", false),
    APPLY_IDENTIFIED_CHECK_ERROR("2", "起案異常，如有任何需要協助，請洽客服人員", false),
    APPLY_IDENTIFIED_CHECK_FAIL("3", "起案失敗，如有任何需要協助，請洽客服人員", false),

    //
    /** VerifyLog 驗證別-銀行信用驗證 */
    SUCCESS_P_CODE_2566("0", "銀行驗證成功", false),
    ERROR_P_CODE_2566("1", "銀行驗證失敗", false),
    ERROR_P_CODE_2566_3("2", "銀行驗證失敗三次", false), //

    /**
     * #5496
     * 空白 >> 改成 跳出訊息顯示"您提供的銀行無法檢核，請使用其他銀行帳號進行驗證或請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶，如有相關問題請洽本行服務專線(02)8023-9088"
     *
     * 20240126 WT20231122012b v4 Momo
     * 空白 >> 改成 跳出訊息顯示"因目前發卡行無法提供檢核，請更換其他銀行帳號進行驗證。"
     *
     * r2-3960 手機90天內曾異動跳不同錯誤訊息
     * 判斷 空白 >> 跳出訊息顯示"因目前發卡行無法提供檢核，請更換其他銀行帳號進行驗證。"
     * 判讀 Y >> 跳出訊息顯示"您申請的數位存款線上開戶無法申辦，如有相關問題請洽本行服務專線(02)8023-9088"
     * 判斷 N >> 通過核驗
     */
    ERROR_P_CODE_2566_Res_empty("3","您提供的銀行無法檢核，請使用其他銀行帳號進行驗證或請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶，如有相關問題請洽本行服務專線(02)8023-9088", false),//

    ERROR_P_CODE_2566_Res_Y("4","您提供驗證銀行留存的手機號碼近期有異動，為防止不法分子假冒您的身分，無法提供您線上開立本行數位帳戶，請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶，如有相關問題請洽本行服務專線(02)8023-9088", false),//

    /**VerifyLog 驗證別-信用卡信用驗證*/
    SUCCESS_NCC("0","信用卡驗證成功", false),
    ERROR_NCC("1","信用卡驗證失敗", false),
    ERROR_NCC_3("2","信用卡驗證失敗三次", false),//
    ERROR_KGI_NCC("3","本行信用卡未滿6個月驗證失敗", false),

    /** KSCIF **/
    SUCCESS_KSCIF("0", "成功", false),

    // LOAN ERROR
    ERROR_SYSTEM_LOAN("1000", "LOAN 錯誤", true), // LOAN 錯誤
    ERROR_SYSTEM_LOAN_Depart("1002", "轉介員編錯誤 請確認轉介員編", false), // LOAN 錯誤
    // LOAN 起案輪詢
    APPLY_IDENTIFIED_CHECK("1001", "LOAN 起案中", false),
    /**
     * 銀行驗證錯誤訊息(貸款)
     */
    ERROR_PCODE2566_ID_BIRTHDAY_LOAN("1002", "因目前發卡行無法提供檢核，請更換其他銀行帳號進行驗證。", false),
    ERROR_PCODE2566_DIGITAL_LOAN("1003", "請使用臨櫃開立之帳戶驗證", false),
    ERROR_PCODE2566_RETURN_OTHER_LOAN("1004", "您帳戶驗證失敗，請與您開戶銀行確認", false),
    ERROR_PCODE2566_KGI("1005", "請確認留存於本行資訊是否正確", false),
    ERROR_PCODE2566_KGI_D1("1006", "提醒您，無法使用數位帳戶驗證，如有問題請洽客服。", false),
    ERROR_USERID_REPEAT("1007", "使用者代號已重複，請重新輸入新的使用者代號", false),

    // CC ERROR
    ERROR_SYSTEM_CC("2000", "CC 錯誤", true), // CC 錯誤

    ERROR_SYSTEM_CC_PRJ_NOT_SETTING("2001", "信用卡常駐專案尚未設定", true), // 信用卡常駐專案尚未設定
    ERROR_PROJECT_CODE_EXPIRED("2002", "您欲申辦的信用卡專案已到期，是否要辦理常駐專案?", false), // 此專案過期 找大專案
    ERROR_PROJECT_CODE_EXPIRED_2("2003", "您欲申辦的信用卡專案已到期，是否要辦理常駐專案?", false), // 此專案過期 找尋其相同channel的專案
    ERROR_PROJECT_CODE_NOT_EXIST("2004", "您欲申辦的信用卡專案不存在，是否要辦理常駐專案?", false), // 此專案不存在 找大專案
    ERROR_EXISTED_DUAL_CURRENCY("2005","你已持有雙幣卡，無需申請，請至其他信用卡線上申請。", false),// 已持有雙幣信用卡

    SUCCESS_LOWRATE_CARD("0000", "執行成功", false), // 符合申辦魔利卡資格
    ERROR_EXISTED_LOWRATE_CARD("0003", "已持有有效魔利卡", false), // 已持有有效魔利卡
    ERROR_LOWRATE_CARD("0003", "您本次可以申辦的信用卡卡別如下頁所示，如無您要申辦的卡別，請洽本行業務人員。", false), // 不符合申辦魔利卡資格

    // D3 ERROR
    ERROR_SYSTEM_D3("3000", "D3 錯誤", true), // D3 錯誤
    ERROR_SYSTEM2_D3("3001", "系統錯誤", true), // 系統錯誤
    ERROR_EXISTED_ACCOUNT_CL("3002", "很抱歉，您已開立本行存款帳戶，無法使用本服務，請聯繫您的凱基人壽業務員辦理續期保費變更服務，謝謝！", false), //
    ERROR_EXISTED_ACCOUNT("3003", "本功能僅提供首次開戶之客戶使用，您已持有本行存款帳戶，若您有其他開戶需求，歡迎蒞臨全省各分行辦理。", false), //
    ERROR_ACCIDENT_ACCOUNT("3004", "您是本行事故戶，因此無法再申辦數位存款帳戶", false), //
    ERROR_ACCIDENT_CANCEL_BREAKPOINT("3005", "您一天內三次不接續斷點被鎖定，因此暫時無法申辦數位存款帳戶", false), //
    ERROR_EXISTED_PHONE("3006", "已有其他客戶使用此手機號碼，請確認是否正確", false), //
    ERROR_ACCIDENT_D3APPLY_CASE("3007", "一個月內有未完成申辦數位存款帳戶，因此暫時無法申辦數位存款帳戶", false), //
    ERROR_Ed3BreakPoint_ACCOUNT("3008", "您已在本行其他管道申請數位存款帳戶，如您想繼續申請，請點選「繼續申請」，將以本次申請資料作為最終審查依據；如您不想申請，請點選「離開」，前次申請案件將於30天後撤件。", false), // r2-3143
                                                                                                           // 身分證 申辦過舊d3
                                                                                                           // 未完成

    ERROR_APPLY_D2_TRUST("3008", "已開立過信託帳戶", false), //
    ERROR_APPLY_D2_NON_USE_KGI_CARD("3009", "數二未使用本行信用卡驗身", false), //
    ERROR_D2_CHT_NAME("3010", "姓名與您於臨櫃申辦帳戶時留存之資料不符，如須更新，請致電客服。", false), //
    ERROR_D2_OTP_PHONE("3011", "您的交易OTP設定非臨櫃辦理，不適用數位開戶，若您有開戶需求，請本人帶雙重身分證件及印鑑至就近分行辦理，謝謝。", false), //

    UNNECESSARY_SETTING("3100", "您無需設定電話銀行及網／行銀密碼", false), //
    EXISTED_DATA("3101", "資料庫已存在, 新戶辦網路銀行和新戶辦電話銀行", false), //
    ERROR_APPLY_D2_D3EXISTED("3102", "您已申辦過數位帳戶，無法透過線上櫃台重複開戶，請改洽分行辦理。", false), //
    ERROR_APPLY_D2_NO_ACC("3103", "帳戶皆無法使用", true), //

    APPLY_D2("3104", "可申辦數二", true), //
    APPLY_D3("3105", "可申辦數二", true), //
    ERROR_APPLY_WARNING_NEW_ACCOUNT("2214", "您暫時無法線上開戶，請攜帶雙證件至本行櫃檯辦理", false),

    // 洗錢警示戶訊息，給非D3案件用
    ERROR_APPLY_D2_NO_EMAIL("F039", "因您於本行未留存電子信箱，需先設定後才能辦理，請洽客服協助，謝謝!", false), //
    /** 雙幣卡系列 */
    IS_DC_D3_NEW_ACC("2215", "85081雙幣卡無可扣款帳戶", false), // 85081雙幣卡無可扣款帳戶
    // 有台幣帳戶但無外幣帳戶
    ERROR_APPLY_DC_NO_FOREIGN_ACC("3106", "申辦雙幣哩程卡須開立凱基臺、外幣存款帳戶並設定為自動扣款帳戶。你未持凱基外幣存款帳戶，請先線上申請或洽凱基銀行分行辦理外幣帳戶開戶！", false),

    // 有台/外幣帳戶，但其中有一個帳戶不可設為自扣帳戶
    ERROR_APPLY_DC_NO_ACC("3107", "申辦雙幣哩程卡須開立凱基臺幣、外幣存款帳戶設定消費自動扣繳。你未持符合設定自動扣繳的凱基存款帳戶，請洽凱基銀行分行辦理開戶！", false),

    // 有台/外幣帳戶，可申辦雙幣哩程卡
    APPLY_DC("3108", "可申辦雙幣哩程卡", false),

    // 有台/外幣帳戶，可申辦雙幣哩程卡+數位帳戶
    APPLY_DC_D3("3109", "可申辦雙幣哩程卡+數位帳戶", false),
    // 洗錢警示戶訊息，給非D3案件用
    ERROR_APPLY_WARNING_ACCOUNT("3110", "您暫時無法線上開戶，請攜帶雙證件至本行櫃檯辦理", false),
    // 有外幣帳戶但無台幣帳戶
    ERROR_APPLY_DC_NO_TW_ACC("3111", "申辦雙幣哩程卡須開立凱基臺、外幣存款帳戶並設定為自動扣款帳戶。你未持凱基臺幣存款帳戶，請先線上申請或洽凱基銀行分行辦理臺幣帳戶開戶！", false),

    // 雙幣卡-他行帳號核印中
    ERROR_NCCACHEDDA0Q_INTHEPROCESS("3113", "請洽凱基銀行分行辦理！", false),

    // #6022 銷戶日為大於 2 天
    ERROR_APPLY_D3_CLOSE_DT_FAIL("3114","您辦理存款帳戶結清，本行系統處理中！請於帳戶結清二日後，再申辦第三類數位存款帳戶，謝謝", false),
    // #6022 帳戶已銷戶可申辦(不查肚子裡)
    APPLY_DIGITAL_AFTER_CLOSE_DT("3115", "帳戶已銷戶，可申辦不看肚子" , false),
    ERROR_85081_STATUS_CODE_9922("3116", "[85081]無法確認身分，請洽客服人員協助，謝謝。" , false),

    // SAL ERROR
    ERROR_SYSTEM_SAL("4000", "SAL 錯誤", true), // SAL 錯誤
    ERROR_SYSTEM_SAL_SHORTURL_NOT_EOP("4001", "並不是從短網址進入的", false), //
    ERROR_SYSTEM_SAL_SHORTURL_INVALID("4002", "該Url並不是有效的", false), //
    ERROR_SYSTEM_SAL_SHORTURL_NOT_CHECKLIST("4003", "此身分證不存在檢核名單內", false), //
    ERROR_SYSTEM_SAL_ALREADY_HAS("4004", "您已經申辦過薪轉帳號", false), //
    ERROR_SYSTEM_SAL_ALREADY_EXPIRED("4005", "此網址效期已過期，造成困擾請見諒。請聯繫薪轉業務窗口，提供正確效期網址方便您的申辦，感謝!", false), //
    SAL_HAS_DG_ACCT("4006", "您於本行已開有數位存款帳戶，請至凱基銀行各分行辦理數位存款轉成一般帳戶，該帳戶即可作為薪轉撥薪帳戶", false), //

    // APPOINTMENT ERROR
    ERROR_SYSTEM_APPT("5000", "預約 錯誤", true), // 預約 錯誤
    ERROR_SYSTEM_APPT_ALREADY_APPLY("5001", "您已預約 ", false), //
    ERROR_SYSTEM_APPT_PHONE("5002", "本手機號碼為您臨櫃辦帳戶時留存之資料，如須更新資料，請致電客服。", false), //
    ERROR_OTHER_SETTING_PROCESS("5003", "網行銀設定處理中", false), //
    ERROR_OTHER_SETTING_FAIL("5004", "網行銀設定失敗", false), //
    APPT_HAS_DG_ACCT("5005", "您於本行已開有數位存款帳戶，請至凱基銀行各分行辦理數位存款轉成一般帳戶", false), //
    APPT_HAS_DG_ACCT_HELP("5006", "您於本行已開有數位存款帳戶，請聯絡客服人員協助您", false), //
    APPT_BOOKING_TIME_FULL("5007", "您所選擇的預約時段已滿，請改選其它日期/時段", false), //

    SUCCESS_BREAK_POINT_DETECTED("9000", "您前次申請的業務尚未完成，是否接續斷點？", false), //
    ERROR_NOT_ALLOW_FOR_BUSINESS("9001", "請洽您的服務專員", false), //
    ALLOW_FOR_BUSINESS("9002", "您此次申辦的產品組合", false), //
    BIRTHDATE_NOT_SAME("9003", "生日檢核不符", false),
    ERROR_OTHER_SETTING("9004", "網銀設定流程異常", true), // 網銀設定流程異常
    SUCCESS_BREAK_POINT_NOAPS("9005", "您前次申請的業務尚未完成，是否接續斷點？", false), //
    SUCCESS_BREAK_POINT_CREDITCARD_TIMEOUT("9006", "您申請的XXX專案活動已經結束，是否重新申請本行信用卡？", false), //
    SUCCESS_BREAK_POINT_CREDITCARD("9007", "您申請的XXX專案申請本行信用卡，是否接續申請？", false), //
    HAS_DG_ACCT("9008", "有數位產品帳戶", false), //
    ERROR_NOT_ALLOW_FOR_BUSINESS_CREATE_DGT_LOAN("9009", "案件起案發生異常，請洽服務專員(A001)", false), //
    ERROR_NOT_ALLOW_FOR_BUSINESS_REPEAT_APPLY("9010", "近期已申請過此產品，請洽服務專員(A002)", false), //
    ERROR_NOT_RE_SEND_IMG("9011", "請重新上傳圖檔", false),
    ERROR_NOT_KS_URL("9012", "請使用業務專員提供網址重新由凱證官網進入", false),
    ERROR_PROD_IS_BLOCK("9013", "目前系統維護中，請於維護期間結束後進行申辦，詳細維護說明資訊請見官網公告", false),
    USE_TMCASE_CHECK("9014", "是否要使用代填資料", false),
    ERROR_NOT_TMCASE("9015", "你的登入資料有誤，請撥打", false),
    ERROR_NOT_TMCASE_END("9016", "與你的專員確認", false),
    ERROR_NON_HL_UNIQID("9013", "請洽服務專員確認案件編號", false), // 房貸保證人案編錯誤

    ERROR_DEC("9100", "呼叫DEC Server失敗", true), // "呼叫DEC Server失敗!!!"
    ERROR_ALN("9101", "呼叫ALN Server失敗", true), // "呼叫ALN Server失敗!!!
    ERROR_APS("9102", "呼叫APS Server失敗", true), // "呼叫APS Server失敗!!!
    ERROR_EDDA("9103", "呼叫EDDA Server失敗", true), // "呼叫EDDA Server失敗!!!"
    ERROR_ESAPI("9104", "呼叫ESAPI Server失敗", true), // "呼叫ESAPI Server失敗!!!"
    ERROR_VALIDATION("9105", "呼叫VALIDATION Server失敗", true), // "呼叫VALIDATION Server失敗!!!"
    ERROR_SMS("9106", "呼叫SMS Server失敗", true), // "呼叫SMS Server失敗!!!"
    ERROR_CRP("9107", "呼叫CRP Server失敗", true), // "呼叫CRP Server失敗!!!"
    ERROR_OCR("9108", "呼叫OCR Server失敗", true), // "呼叫OCR Server失敗!!!"
    ERROR_ESB("9109", "呼叫ESB Server失敗", true), // "呼叫ESB Server失敗!!!"
    ERROR_CS("9110", "呼叫客服電子單 Server失敗", true), // "呼叫客服電子單 Server失敗!!!"
    ERROR_APIM("9119", "呼叫APIM Server失敗", true), // "呼叫APIM Server失敗!!!"
    ERROR_AIOAPS("9120", "呼叫AIOAPS Server失敗", true), // "呼叫AIOAPS Server失敗!!!"
    ERROR_OTP_SEND_TIME("9121", "呼叫間隔小於1分鐘，無法重送簡訊", true), // "呼叫間隔小於1分鐘，無法重送簡訊!!!"

    // mobile ap for free login
    ERROR_MOBILE_AP("9800", "呼叫APP伺服器失敗", true), // "呼叫APP伺服器失敗!!!"
    ERROR_MOBILE_AP_CAN_NOT_GET_DATA("9801", "取無資料", true), // "取無資料 可能token 過期"
    ERROR_MOBILE_AP_UPDATE_API_450_THRICE("9802", "呼叫三次updateSession app端皆回應450,停止背景呼叫", true), // 呼叫三次updateSession app端皆回應450,停止背景呼叫

    /**
     * 信用卡調額 使用
     */
    // 透過 token 跟行銀取得 AppCIF 失敗（OnboardingCif/CustInfo）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF("9601", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF_DATAERROR("9602", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETCIF_OBD("9603", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    // 透過 id 查詢信用卡電文額度（NCCME472000Q）
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720("9701", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720_DATAERROR("9702", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GET4720_OBD("9703", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    // 取得 OBD token 失敗
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_GETTOKEN("9801", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    // 透過 OBD 案編，取得 CaseData 資料
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_DATAERROR_SAVE("9802", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    // 透過 OBD 案編，取得 CaseData 資料
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_DATAERROR_GET("9803", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    // 通知要新增客服電子單CS041申請額度調整
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SEND("9901", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SEND_CATCH("9902", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    // 通知要補件客服電子單CS041申請額度調整
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SUPPLY("9903", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),
    ERROR_MOBILE_AP_CARD_LIMIT_ADJ_CS041SUPPLY_CATCH("9904", "目前系統忙碌中，請稍後重新申請。若有疑問請聯繫智能客服或洽客服專線02-80239088。", true),

    /**
     * D3-OCR用
     */
    ERROR_OCR_ID_FRONT_EMPTY("9811", "您上傳的照片可能模糊不清或上傳不正確的證件，建議重新上傳。", false),
    ERROR_OCR_ID_FRONT_EMPTY_FOUR_TIMES("9812", "您拍攝的照片可能模糊不清或證件種類不符，請調整角度與光線，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。", false),
    ERROR_OCR_ID_FRONT_NOT_SAME("9813", "您上傳的身分證與申請人資料不同，請重新提供本人最新版身分證或重新登入申請！！", false),
    ERROR_OCR_ID_FRONT_OVER_FOUR_TIMES("9814", "您上傳的身分證與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。", false),
    ERROR_OCR_BIRTH_PLACE("9815", "很抱歉，您的出生地非臺灣，無法提供您線上開立本行數位帳戶，請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶", false),
    ERROR_OCR_HOUSE_PLACE("9816", "您提供證件之戶籍地址為公所或戶政事務所，無法提供您線上開立本行數位帳戶，請您攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶", false),
    ERROR_OCR_SEC_HEALTH_VS_ERR("9817", "您上傳的第二證件與身分證資料不同，請重新提供！", false),
    ERROR_OCR_SEC_DRIVE_VS_ERR("9818", "您上傳的駕照與身分證資料不同，請重新提供！", false),
    ERROR_OCR_SEC_PASSPORT_VS_ERR("9819", "您上傳的護照與身分證資料不同，請重新提供！", false),
    ERROR_OCR_SEC_HEALTH_VS_FOUR_ERR("9820", "您上傳的健保卡與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。", false),
    ERROR_OCR_SEC_DRIVE_VS_FOUR_ERR("9821", "您上傳的駕照與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。", false),
    ERROR_OCR_SEC_PASSPORT_VS_FOUR_ERR("9822", "您上傳的護照與填寫資料不符，比對錯誤次數過多，建議您確認資料或證件的正確性，或攜帶雙重證明文件至本行任一分行櫃檯辦理存款開戶。", false),
    ERROR_OCR_SEC_PASSPORT_IS_OLD_ERR("9823", "您的證件效期已屆滿或過期，請提供其他第二證件。", false),
    ERROR_OCR_MARKED("9824", "已註記於 DB 直接回前端。", false),
    ERROR_OCR_NO_FACE("9825", "您的證件無個人相片，請提供其他第二證件。", false),
    ERROR_OCR_SEC_ERR("9826", "您選擇上傳的第二證件類型與上傳證件並不相符，請確認上傳證件類型後重新上傳。",false),
    ERROR_OCR_COLOR_ERR("117", "您提供的影像模糊不清，建議您將證件橫放後直立擺設，且須四角含證件膠框都需於拍攝範圍內，並確認證件上無任何反光，無誤後按下對焦後拍攝，麻煩重新拍攝或上傳。", false),
    ERROR_OCR_SIZE_ERR("119", "您提供的影像模糊不清，建議您將證件橫放後直立擺設，且須四角含證件膠框都需於拍攝範圍內，並確認證件上無任何反光，無誤後按下對焦後拍攝，麻煩重新拍攝或上傳。", false),
    ERROR_OCR_ERR("101", "證件辨識失敗請重新拍攝", false),


    ERROR_GET_UNIQID_FAIL("9992", "uniqId 為空，阻擋流程", true), // 流程異常
    ERROR_GET_VIDEO_SERVICE_URL_FAIL("9993", "系統維護作業，暫停服務", false), // 流程異常
    ERROR_WORKFLOW_ERROR("9994", "流程異常", true), // 流程異常
    ERROR_REQUEST_LIMIT("9995", "IP訪問過於頻繁", true), // IP訪問過於頻繁
    ERROR_DB("9996", "執行DB失敗", true), // 執行DB失敗
    ERROR_VALUE("9997", "參數錯誤", true), // 參數錯誤
    ERROR_WF("9998", "Process Not Found", true), // Process Not Found
    ERROR_APPLY_WARNING_ACCOUNT_DC("8823", "為加強確認客戶身分，請您攜帶雙證件至本行櫃檯辦理開戶", true), // 雙幣卡警示戶、告誡戶不可申辦
    ERROR_APPLY_WARNING_ACCOUNT_CC_CANCEL("8823_cc_cancel", "提醒你的存款帳戶已無法執行自動扣繳，之前填寫的設定已取消；若有核卡，信用卡款請自行以其他方式繳納，如有疑問請洽客服人員", true), // 一般卡警示戶、告誡戶取消授扣
    ERROR("9999", "執行失敗", true); // 執行失敗

    /*
     * eD3 被別系統呼叫時，需回傳的 return code
     */

    private String rtnCode; // 回傳代碼
    private String rtnMessage; // 回傳訊息
    private boolean isProType; // true=> 正式區 回傳 "請洽您的服務專員" false=> 測試區 回傳原本訊息

    private ReturnCode() {
    }

    private ReturnCode(String rtnCode, String rtnMessage, boolean isProType) {
        this.rtnCode = rtnCode;
        this.rtnMessage = rtnMessage;
        this.isProType = isProType;
    }

    public String getRtnCode() {
        return rtnCode;
    }

    public String getRtnMessage() {
        System.out.println("isProType = " + isProType);
        System.out.println("ReturnCodeEnvironment.configType  = " + ReturnCodeEnvironment.configType);
        if (ReturnCodeEnvironment.configType && isProType) {
            return "請洽您的服務專員";
        } else {
            return rtnMessage;
        }
    }

    // 為了分辨正式區和測試區 顯示的錯誤訊息替換
    @Component
    public static class ReturnCodeEnvironment {
        @Resource
        private Environment env;
        protected static boolean configType = false;

        @PostConstruct
        public void getEnvironment() {
            configType = SystemConst.ACTIVE_PROFILES_PROD.equals(env.getActiveProfiles()[0]);
        }

        public boolean getConfigType() {
            return configType;
        }

        public void setConfigType(boolean configType) {
            this.configType = configType;
        }
    }

}
