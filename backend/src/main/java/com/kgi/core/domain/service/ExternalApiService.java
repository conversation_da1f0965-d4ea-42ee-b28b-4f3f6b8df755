package com.kgi.core.domain.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 外部API整合服務
 * 負責與 TO API 和 FROM API 的整合
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExternalApiService {
    
    /**
     * 查詢原始 TO API 資料
     */
    public Map<String, Object> getOriginalToApiData(String caseNo) {
        log.info("查詢 TO API 原始資料: caseNo={}", caseNo);
        
        // 實際環境應該從資料庫查詢
        // 這裡使用模擬資料
        if ("IBR2025010101".equals(caseNo)) {
            return createIndividualMockData();
        } else if ("IBR2025010102".equals(caseNo)) {
            return createCorporateMockData();
        }
        
        log.warn("查無案件資料: caseNo={}", caseNo);
        return null;
    }
    
    /**
     * 呼叫 FROM API
     */
    public Map<String, Object> callFromApi(Map<String, Object> remittanceData) {
        log.info("呼叫 FROM API: caseNo={}", remittanceData.get("caseNo"));
        
        try {
            // 實際環境應該使用 RestTemplate 或 WebClient
            // 這裡模擬 API 呼叫
            
            Map<String, Object> fromApiRequest = buildFromApiRequest(remittanceData);
            
            // 模擬 API 回應
            Map<String, Object> response = new HashMap<>();
            response.put("resultCode", "200");
            response.put("message", "處理成功");
            response.put("refNo", "FROM-" + UUID.randomUUID().toString().substring(0, 8));
            response.put("processTime", LocalDateTime.now().toString());
            
            log.info("FROM API 回應: {}", response);
            return response;
            
        } catch (Exception e) {
            log.error("FROM API 呼叫失敗", e);
            throw new RuntimeException("FROM API 呼叫失敗: " + e.getMessage());
        }
    }
    
    /**
     * 建立 FROM API 請求資料
     */
    private Map<String, Object> buildFromApiRequest(Map<String, Object> remittanceData) {
        Map<String, Object> request = new HashMap<>();
        
        // 映射必要欄位
        request.put("caseNo", remittanceData.get("caseNo"));
        request.put("customerType", remittanceData.get("customerType"));
        request.put("payeeId", remittanceData.get("payeeId"));
        request.put("payeeName", remittanceData.get("payeeName"));
        request.put("amount", remittanceData.get("amount"));
        request.put("currency", remittanceData.get("currency"));
        request.put("bankCode", remittanceData.get("bankCode"));
        request.put("accountNo", remittanceData.get("accountNo"));
        
        // 加入驗證資訊
        Map<String, Object> verificationInfo = new HashMap<>();
        verificationInfo.put("otpVerified", remittanceData.get("otpVerified"));
        verificationInfo.put("certificateVerified", remittanceData.get("certificateVerified"));
        verificationInfo.put("verificationTime", remittanceData.get("verificationTime"));
        request.put("verificationInfo", verificationInfo);
        
        return request;
    }
    
    /**
     * 建立個人模擬資料
     */
    private Map<String, Object> createIndividualMockData() {
        Map<String, Object> map = new HashMap<>();
        map.put("TheirRefNo", "CB2025010101");
        map.put("RemitRefNo", "RM2025010101");
        map.put("payername", "JOHN DOE");
        map.put("Currency", "USD");
        map.put("Amount", 50000);
        map.put("PayeeEngName", "WANG TA MING");
        map.put("PayeeName", "王大明");
        map.put("PayeeID", "A123456789");
        map.put("PayeeAccount", "**********");
        map.put("PayeeBankCode", "812");
        map.put("PayeeTel", "**********");
        map.put("PayeeMail", "<EMAIL>");
        map.put("SourceOfFund", "家用匯款");
        map.put("WhileFlag", "Y");
        return map;
    }
    
    /**
     * 建立企業模擬資料
     */
    private Map<String, Object> createCorporateMockData() {
        Map<String, Object> map = new HashMap<>();
        map.put("TheirRefNo", "CB2025010102");
        map.put("RemitRefNo", "RM2025010102");
        map.put("payername", "ABC TRADING CO");
        map.put("Currency", "USD");
        map.put("Amount", 100000);
        map.put("PayeeEngName", "WISTRON CORPORATION");
        map.put("PayeeName", "緯創資通股份有限公司");
        map.put("PayeeID", "********");
        map.put("PayeeAccount", "**********");
        map.put("PayeeBankCode", "812");
        map.put("PayeeTel", "02-********");
        map.put("PayeeMail", "<EMAIL>");
        map.put("SourceOfFund", "國際貿易");
        map.put("WhileFlag", "Y");
        return map;
    }
}