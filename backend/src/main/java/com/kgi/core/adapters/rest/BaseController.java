package com.kgi.core.adapters.rest;

import com.kgi.core.application.dto.IbrApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;

/**
 * 基礎控制器
 * 提供共用的控制器功能
 */
@Slf4j
@CrossOrigin(origins = "*", maxAge = 3600)
public abstract class BaseController {
    
    /**
     * 成功響應
     */
    protected <T> ResponseEntity<IbrApiResponse<T>> success(T data) {
        log.info("API調用成功: {}", data);
        return ResponseEntity.ok(IbrApiResponse.success(data));
    }
    
    /**
     * 成功響應帶訊息
     */
    protected <T> ResponseEntity<IbrApiResponse<T>> success(T data, String message) {
        log.info("API調用成功: {}, 訊息: {}", data, message);
        return ResponseEntity.ok(IbrApiResponse.success(data, message));
    }
    
    /**
     * 失敗響應
     */
    protected <T> ResponseEntity<IbrApiResponse<T>> error(String message) {
        log.error("API調用失敗: {}", message);
        return ResponseEntity.badRequest().body(IbrApiResponse.error(message));
    }
    
    /**
     * 失敗響應帶錯誤碼
     */
    protected <T> ResponseEntity<IbrApiResponse<T>> error(String errorCode, String message) {
        log.error("API調用失敗: {}, 錯誤碼: {}", message, errorCode);
        return ResponseEntity.badRequest().body(IbrApiResponse.error(message, errorCode));
    }
    
    /**
     * 記錄請求資訊
     */
    protected void logRequest(String operation, Object request) {
        log.info("執行操作: {}, 請求參數: {}", operation, request);
    }
    
    /**
     * 記錄響應資訊
     */
    protected void logResponse(String operation, Object response) {
        log.info("操作完成: {}, 響應結果: {}", operation, response);
    }
}