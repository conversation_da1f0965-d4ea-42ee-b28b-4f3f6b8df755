package com.kgi.core.adapters.rest;

import com.kgi.core.application.dto.IbrApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康檢查控制器
 * 提供系統狀態和健康檢查 API
 */
@Slf4j
@RestController
@RequestMapping("/api/health")
public class HealthController extends BaseController {
    
    /**
     * 健康檢查
     */
    @GetMapping
    public ResponseEntity<IbrApiResponse<Map<String, Object>>> health() {
        log.info("健康檢查請求");
        
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("application", "KGI數位跨境匯款解付平台");
        healthInfo.put("version", "1.0.0");
        healthInfo.put("environment", "development");
        
        return success(healthInfo, "系統運行正常");
    }
    
    /**
     * 系統資訊
     */
    @GetMapping("/info")
    public ResponseEntity<IbrApiResponse<Map<String, Object>>> info() {
        log.info("系統資訊請求");
        
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("java.version", System.getProperty("java.version"));
        systemInfo.put("java.vendor", System.getProperty("java.vendor"));
        systemInfo.put("os.name", System.getProperty("os.name"));
        systemInfo.put("os.version", System.getProperty("os.version"));
        systemInfo.put("available.processors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("max.memory", Runtime.getRuntime().maxMemory());
        systemInfo.put("total.memory", Runtime.getRuntime().totalMemory());
        systemInfo.put("free.memory", Runtime.getRuntime().freeMemory());
        
        return success(systemInfo, "系統資訊獲取成功");
    }
    
    /**
     * 數位解款平台狀態
     */
    @GetMapping("/ibr")
    public ResponseEntity<IbrApiResponse<Map<String, Object>>> ibrStatus() {
        log.info("IBR 平台狀態檢查");
        
        Map<String, Object> ibrStatus = new HashMap<>();
        ibrStatus.put("platform", "數位跨境匯款解付平台");
        ibrStatus.put("status", "運行中");
        ibrStatus.put("services", Map.of(
                "個人解款", "可用",
                "企業解款", "可用",
                "補件服務", "可用",
                "計算服務", "可用",
                "工作流程", "可用"
        ));
        ibrStatus.put("last_checked", LocalDateTime.now());
        
        return success(ibrStatus, "IBR 平台運行正常");
    }
}