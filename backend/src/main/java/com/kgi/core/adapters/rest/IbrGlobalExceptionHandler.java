package com.kgi.core.adapters.rest;

import com.kgi.core.application.dto.IbrApiResponse;
import com.kgi.core.domain.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * IBR 全局異常處理器
 * 基於跨境匯款業務的錯誤處理機制
 */
@Slf4j
@RestControllerAdvice
public class IbrGlobalExceptionHandler {
    
    /**
     * 處理業務異常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<IbrApiResponse<Void>> handleBusinessException(BusinessException e) {
        log.error("業務異常: {}", e.getMessage(), e);
        IbrApiResponse<Void> response = IbrApiResponse.<Void>builder()
                .statusCode("99")
                .txntMsg(e.getMessage())
                .remark(e.getErrorCode())
                .timestamp(System.currentTimeMillis())
                .build();
        return ResponseEntity.ok(response);
    }
    
    /**
     * 處理參數驗證異常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<IbrApiResponse<Void>> handleValidationException(MethodArgumentNotValidException e) {
        List<String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "參數驗證失敗: " + String.join(", ", errors);
        log.error("參數驗證異常: {}", errorMessage);
        
        IbrApiResponse<Void> response = IbrApiResponse.<Void>builder()
                .statusCode("99")
                .txntMsg(errorMessage)
                .remark("VALIDATION_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 處理綁定異常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<IbrApiResponse<Void>> handleBindException(BindException e) {
        List<String> errors = e.getFieldErrors()
                .stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "資料綁定失敗: " + String.join(", ", errors);
        log.error("綁定異常: {}", errorMessage);
        
        IbrApiResponse<Void> response = IbrApiResponse.<Void>builder()
                .statusCode("99")
                .txntMsg(errorMessage)
                .remark("BIND_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 處理約束驗證異常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<IbrApiResponse<Void>> handleConstraintViolationException(ConstraintViolationException e) {
        List<String> errors = e.getConstraintViolations()
                .stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());
        
        String errorMessage = "約束驗證失敗: " + String.join(", ", errors);
        log.error("約束驗證異常: {}", errorMessage);
        
        IbrApiResponse<Void> response = IbrApiResponse.<Void>builder()
                .statusCode("99")
                .txntMsg(errorMessage)
                .remark("CONSTRAINT_VIOLATION")
                .timestamp(System.currentTimeMillis())
                .build();
        return ResponseEntity.badRequest().body(response);
    }
    
    /**
     * 處理系統異常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<IbrApiResponse<Void>> handleSystemException(Exception e) {
        log.error("系統異常: {}", e.getMessage(), e);
        IbrApiResponse<Void> response = IbrApiResponse.<Void>builder()
                .statusCode("99")
                .txntMsg("系統內部錯誤，請聯繫客服")
                .remark("SYSTEM_ERROR")
                .timestamp(System.currentTimeMillis())
                .build();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}