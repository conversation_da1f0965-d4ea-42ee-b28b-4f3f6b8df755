package com.kgi.core.service.concurrent;

import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

import javax.sql.DataSource;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.Semaphore;

@Slf4j
public class DatabaseManagementService {
    private DataSource dataSource;

    private final Semaphore semaphore;
    private final Queue<Integer> connectionPoolQueue = new LinkedList<>(); // Create a queue to manage connection requests
    private static final Integer QUEUE_SIZE = 10;

    private final int maxConnections;

    private int maxPoolSize = 0;
    private int minimumIdle = 0;
    private int connectionTimeout = 0;

    public DatabaseManagementService(DataSource dataSource) {
        this.dataSource = dataSource;

        maxConnections = 10; // Set the maximum number of connections
        semaphore = new Semaphore(maxConnections, true); // Create a semaphore with a maximum of maxConnections permits

//        HikariConfig config = new HikariConfig(); // Create a new HikariConfig object
//        config.setDataSource(dataSource);
//        if (dataSource instanceof HikariDataSource) {
//            connectionTimeout = (int)((HikariDataSource)dataSource).getConnectionTimeout();
//            maxPoolSize = ((HikariDataSource)dataSource).getMaximumPoolSize();
//            minimumIdle = ((HikariDataSource)dataSource).getMinimumIdle();
//        }else {
//            maxPoolSize = config.getMaximumPoolSize();
//            minimumIdle = config.getMinimumIdle();
//            connectionTimeout = (int) config.getConnectionTimeout();
//        }
    }

//    public <T> void save(IConcurrentService service, T entity) {
//        if (service == null) {
//            throw new IllegalArgumentException("Service cannot be null.");
//        }
//        if (entity == null) {
//            throw new IllegalArgumentException("Entity cannot be null.");
//        }
//
//        int number = -1;
//        try {
//            semaphore.acquire(); // Try to acquire a permit from the semaphore
//            synchronized (connectionPoolQueue) {
//                number = connectionPoolQueue.size() + 1; // Assign the next available number to the connection request
//                connectionPoolQueue.add(number); // Add the connection request to the queue
//            }
//            checkConnectionPoolStatus(); // Check the connection pool status
//            service.save(entity); // Save the book
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } finally {
//            if (number != -1) {
//                synchronized (connectionPoolQueue) {
//                    connectionPoolQueue.remove(number); // Remove the connection request from the queue
//                }
//                semaphore.release(); // Release the permit back to the semaphore
//            }
//        }
//    }

//    public <T> void (IConcurrentService service) {
//
//
//        int number = -1;
//        try {
//            semaphore.acquire(); // Try to acquire a permit from the semaphore
//            synchronized (connectionPoolQueue) {
//                number = connectionPoolQueue.size() + 1; // Assign the next available number to the connection request
//                connectionPoolQueue.add(number); // Add the connection request to the queue
//            }
//            checkConnectionPoolStatus(); // Check the connection pool status
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        } finally {
//            if (number != -1) {
//                synchronized (connectionPoolQueue) {
//                    connectionPoolQueue.remove(number); // Remove the connection request from the queue
//                }
//                semaphore.release(); // Release the permit back to the semaphore
//            }
//        }
//    }

    public void getDatabaseInitSettings() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = ((HikariDataSource) dataSource);
            HikariPoolMXBean poolMXBean = ((HikariDataSource) dataSource).getHikariPoolMXBean();

            Marker marker = MarkerFactory.getMarker("serverFileLog");

            log.info(marker,
                    "\n----------------------------------------------------------\n\t" +
                            "Total connections: \t\t\t{}\n\t" +
                            "Idle connections: \t\t\t{}\n\t" +
                            "Active connections: \t\t{}\n\t" +
                            "Waiting threads: \t\t\t{}\n\t" +
                            "max pool size: \t\t\t\t{}\n\t" +
                            "min idle connections: \t\t{}\n\t" +
                            "MaxLifetime: \t\t\t\t{} ms\n\t" +
                            "Connection timeout: \t\t{} ms\n" +
                            "----------------------------------------------------------\n",
                    poolMXBean.getTotalConnections(),
                    poolMXBean.getIdleConnections(),
                    poolMXBean.getActiveConnections(),
                    poolMXBean.getThreadsAwaitingConnection(), //connectionTimeout, 超過時間會報 SQLException 顯示超時
                    hikariDataSource.getMaximumPoolSize(),
                    hikariDataSource.getMinimumIdle(),
                    hikariDataSource.getMaxLifetime(),
                    hikariDataSource.getConnectionTimeout());

//            log.info("\n----------------------------------------------------------\n\t" +
//                            "Total connections: \t\t\t{}\n\t" +
//                            "Idle connections: \t\t\t{}\n\t" +
//                            "Active connections: \t\t{}\n\t" +
//                            "Waiting threads: \t\t\t{}\n\t" +
//                            "max pool size: \t\t\t\t{}\n\t" +
//                            "min idle connections: \t\t{}\n\t" +
//                            "MaxLifetime: \t\t\t\t{} ms\n\t" +
//                            "Connection timeout: \t\t{} ms\n" +
//                            "----------------------------------------------------------\n",
//                    poolMXBean.getTotalConnections(),
//                    poolMXBean.getIdleConnections(),
//                    poolMXBean.getActiveConnections(),
//                    poolMXBean.getThreadsAwaitingConnection(), //connectionTimeout, 超過時間會報 SQLException 顯示超時
//                    hikariDataSource.getMaximumPoolSize(),
//                    hikariDataSource.getMinimumIdle(),
//                    hikariDataSource.getMaxLifetime(),
//                    hikariDataSource.getConnectionTimeout());

            synchronized (connectionPoolQueue) {
                System.out.println("Connection requests in queue: " + connectionPoolQueue.toString());
            }
        } else {
            // Handle other types of data source
            System.out.println("Unsupported data source type: " + dataSource.getClass().getName());
        }
    }

    public void checkConnectionPoolStatus() {
        if (dataSource instanceof HikariDataSource) {
            HikariPoolMXBean poolMXBean = ((HikariDataSource) dataSource).getHikariPoolMXBean();

            Marker marker = MarkerFactory.getMarker("concurrentLog");
            log.info(marker,
                    "\n----------------------------------------------------------\n" +
                     "Active/Idle/Total connections/Waiting threads: \t\t{}/{}/{}/{}\t",
//                     "Waiting threads in queue: \t\t\t{}\n" +
//                     "----------------------------------------------------------\n",
                    poolMXBean.getActiveConnections(),
                    poolMXBean.getIdleConnections(),
                    poolMXBean.getTotalConnections(),
                    poolMXBean.getThreadsAwaitingConnection() //connectionTimeout, 超過時間會報 SQLException 顯示超時
                    );

            synchronized (connectionPoolQueue) {
                System.out.println("Connection requests in queue: " + connectionPoolQueue.toString());
            }
        } else {
            // Handle other types of data source
            System.out.println("Unsupported data source type: " + dataSource.getClass().getName());
        }
    }
}
