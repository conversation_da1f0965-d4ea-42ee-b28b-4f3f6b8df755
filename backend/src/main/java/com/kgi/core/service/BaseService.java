package com.kgi.core.service;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public abstract class BaseService {
    @Value("${kgi.useMockupData}")
    private boolean useMockupData;

    protected static final org.slf4j.Logger logger = LoggerFactory.getLogger(BaseService.class);

    /**
     * 微服務 初始方法, 可於此準備需要的資料
     * @throws Exception
     */
//    protected abstract void onInit() throws Exception;

}
