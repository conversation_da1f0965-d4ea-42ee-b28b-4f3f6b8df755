package com.kgi.core.service.mock.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Mock 服務基類
 * 提供所有 Mock 服務的共用功能
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
public abstract class BaseMockService {

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected MockDataRepository mockDataRepository;

    @Value("${kgi.mock.delay.enabled:true}")
    protected boolean delayEnabled;

    @Value("${kgi.mock.delay.min:100}")
    protected int minDelay;

    @Value("${kgi.mock.delay.max:500}")
    protected int maxDelay;

    @Value("${kgi.mock.error.rate:0.0}")
    protected double errorRate;

    protected final Random random = new Random();

    /**
     * 模擬網路延遲
     */
    protected void simulateDelay() {
        if (delayEnabled) {
            try {
                int delay = minDelay + random.nextInt(maxDelay - minDelay);
                log.debug("Simulating network delay: {} ms", delay);
                TimeUnit.MILLISECONDS.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("Delay simulation interrupted", e);
            }
        }
    }

    /**
     * 模擬隨機錯誤
     * @return true 如果應該模擬錯誤
     */
    protected boolean shouldSimulateError() {
        if (errorRate <= 0) {
            return false;
        }
        return random.nextDouble() < errorRate;
    }

    /**
     * 生成唯一識別碼
     */
    protected String generateId() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }

    /**
     * 生成交易編號
     * @param prefix 前綴
     */
    protected String generateTransactionId(String prefix) {
        return prefix + System.currentTimeMillis() + random.nextInt(1000);
    }

    /**
     * 生成案件編號
     */
    protected String generateCaseNo() {
        return "IBR" + System.currentTimeMillis();
    }

    /**
     * 記錄 Mock 服務呼叫
     * @param serviceName 服務名稱
     * @param method 方法名稱
     * @param request 請求參數
     * @param response 回應結果
     */
    protected void logMockCall(String serviceName, String method, Object request, Object response) {
        try {
            log.info("=== Mock Service Call ===");
            log.info("Service: {}", serviceName);
            log.info("Method: {}", method);
            log.info("Request: {}", objectMapper.writeValueAsString(request));
            log.info("Response: {}", objectMapper.writeValueAsString(response));
            log.info("========================");
        } catch (Exception e) {
            log.error("Error logging mock call", e);
        }
    }

    /**
     * 從 Mock 資料庫取得測試資料
     * @param key 資料鍵值
     * @param clazz 資料類型
     */
    protected <T> T getMockData(String key, Class<T> clazz) {
        return mockDataRepository.getData(key, clazz);
    }

    /**
     * 儲存 Mock 資料
     * @param key 資料鍵值
     * @param data 資料內容
     */
    protected void saveMockData(String key, Object data) {
        mockDataRepository.saveData(key, data);
    }

    /**
     * 生成成功回應
     */
    protected <T> MockResponse<T> success(T data) {
        return MockResponse.<T>builder()
                .success(true)
                .code("0000")
                .message("Success")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 生成失敗回應
     */
    protected <T> MockResponse<T> error(String code, String message) {
        return MockResponse.<T>builder()
                .success(false)
                .code(code)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * Mock 回應包裝類
     */
    @lombok.Data
    @lombok.Builder
    public static class MockResponse<T> {
        private boolean success;
        private String code;
        private String message;
        private T data;
        private long timestamp;
    }
}