package com.kgi.core.service.mock.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Mock 資料儲存庫
 * 管理所有 Mock 服務的測試資料
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
public class MockDataRepository {

    @Autowired
    private ObjectMapper objectMapper;

    private final Map<String, Object> dataStore = new ConcurrentHashMap<>();

    /**
     * 初始化預設測試資料
     */
    @PostConstruct
    public void initDefaultData() {
        log.info("Initializing mock data repository...");
        
        // 初始化個人客戶測試資料
        initIndividualTestData();
        
        // 初始化企業客戶測試資料
        initCorporateTestData();
        
        // 初始化補件測試資料
        initSupplementTestData();
        
        // 初始化銀行資料
        initBankData();
        
        // 初始化匯率資料
        initExchangeRateData();
        
        log.info("Mock data repository initialized with {} entries", dataStore.size());
    }

    /**
     * 取得資料
     */
    public <T> T getData(String key, Class<T> clazz) {
        Object data = dataStore.get(key);
        if (data == null) {
            log.warn("Mock data not found for key: {}", key);
            return null;
        }
        
        try {
            if (clazz.isInstance(data)) {
                return clazz.cast(data);
            }
            // 嘗試轉換
            String json = objectMapper.writeValueAsString(data);
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("Error converting mock data for key: {}", key, e);
            return null;
        }
    }

    /**
     * 儲存資料
     */
    public void saveData(String key, Object data) {
        dataStore.put(key, data);
        log.debug("Saved mock data for key: {}", key);
    }

    /**
     * 刪除資料
     */
    public void removeData(String key) {
        dataStore.remove(key);
        log.debug("Removed mock data for key: {}", key);
    }

    /**
     * 清空所有資料
     */
    public void clearAll() {
        dataStore.clear();
        log.info("Cleared all mock data");
    }

    /**
     * 初始化個人客戶測試資料
     */
    private void initIndividualTestData() {
        // 測試個人客戶資料
        Map<String, Object> testIndividual = new ConcurrentHashMap<>();
        testIndividual.put("customerType", "INDIVIDUAL");
        testIndividual.put("payeeId", "A********9");
        testIndividual.put("payeeName", "王大明");
        testIndividual.put("payeeEngName", "WANG DA MING");
        testIndividual.put("payeeAccount", "*************");
        testIndividual.put("payeeBankCode", "004");
        testIndividual.put("payeeTel", "**********");
        testIndividual.put("payeeMail", "<EMAIL>");
        testIndividual.put("currency", "USD");
        testIndividual.put("amount", 50000.00);
        
        saveData("test.individual.default", testIndividual);
        
        // 外籍人士測試資料
        Map<String, Object> testForeigner = new ConcurrentHashMap<>();
        testForeigner.putAll(testIndividual);
        testForeigner.put("payeeId", "AC********");
        testForeigner.put("residenceDateBegin", "********");
        testForeigner.put("residenceDateEnd", "********");
        
        saveData("test.individual.foreigner", testForeigner);
    }

    /**
     * 初始化企業客戶測試資料
     */
    private void initCorporateTestData() {
        Map<String, Object> testCorporate = new ConcurrentHashMap<>();
        testCorporate.put("customerType", "CORPORATE");
        testCorporate.put("payeeId", "********");
        testCorporate.put("payeeName", "凱基測試企業有限公司");
        testCorporate.put("payeeEngName", "KGI TEST COMPANY LTD");
        testCorporate.put("payeeAccount", "*************");
        testCorporate.put("payeeBankCode", "808");
        testCorporate.put("payeeTel", "**********");
        testCorporate.put("payeeMail", "<EMAIL>");
        testCorporate.put("currency", "USD");
        testCorporate.put("amount", 100000.00);
        testCorporate.put("principalName", "張三豐");
        testCorporate.put("principalId", "B987654321");
        
        saveData("test.corporate.default", testCorporate);
    }

    /**
     * 初始化補件測試資料
     */
    private void initSupplementTestData() {
        Map<String, Object> testSupplement = new ConcurrentHashMap<>();
        testSupplement.put("caseNo", "IBR202501010001");
        testSupplement.put("supplementType", "NAME_MISMATCH");
        testSupplement.put("originalPayeeName", "王大名");
        testSupplement.put("correctPayeeName", "王大明");
        testSupplement.put("status", "PENDING");
        testSupplement.put("deadline", "2025-01-31");
        
        saveData("test.supplement.nameMismatch", testSupplement);
    }

    /**
     * 初始化銀行資料
     */
    private void initBankData() {
        Map<String, Object> bankList = new ConcurrentHashMap<>();
        bankList.put("004", "臺灣銀行");
        bankList.put("005", "土地銀行");
        bankList.put("006", "合作金庫");
        bankList.put("007", "第一銀行");
        bankList.put("008", "華南銀行");
        bankList.put("009", "彰化銀行");
        bankList.put("808", "玉山銀行");
        bankList.put("812", "台新銀行");
        
        saveData("bank.list", bankList);
    }

    /**
     * 初始化匯率資料
     */
    private void initExchangeRateData() {
        Map<String, Object> exchangeRates = new ConcurrentHashMap<>();
        
        // USD 匯率
        Map<String, Object> usdRate = new ConcurrentHashMap<>();
        usdRate.put("currency", "USD");
        usdRate.put("buyRate", 31.50);
        usdRate.put("sellRate", 32.00);
        usdRate.put("midRate", 31.75);
        usdRate.put("updateTime", "2025-01-06 10:00:00");
        exchangeRates.put("USD", usdRate);
        
        // EUR 匯率
        Map<String, Object> eurRate = new ConcurrentHashMap<>();
        eurRate.put("currency", "EUR");
        eurRate.put("buyRate", 33.20);
        eurRate.put("sellRate", 33.80);
        eurRate.put("midRate", 33.50);
        eurRate.put("updateTime", "2025-01-06 10:00:00");
        exchangeRates.put("EUR", eurRate);
        
        // JPY 匯率
        Map<String, Object> jpyRate = new ConcurrentHashMap<>();
        jpyRate.put("currency", "JPY");
        jpyRate.put("buyRate", 0.2150);
        jpyRate.put("sellRate", 0.2250);
        jpyRate.put("midRate", 0.2200);
        jpyRate.put("updateTime", "2025-01-06 10:00:00");
        exchangeRates.put("JPY", jpyRate);
        
        saveData("exchange.rates", exchangeRates);
    }

    /**
     * 取得資料總數
     */
    public int getDataCount() {
        return dataStore.size();
    }

    /**
     * 檢查資料是否存在
     */
    public boolean hasData(String key) {
        return dataStore.containsKey(key);
    }
}