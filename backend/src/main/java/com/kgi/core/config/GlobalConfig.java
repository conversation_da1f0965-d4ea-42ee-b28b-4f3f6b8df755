package com.kgi.core.config;

import com.kgi.core.util.crypt.KGIOBDAESUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.Charset;

/**
 * From STP / D3
 */
@Configuration
public class GlobalConfig {
    @Value("${camunda.bpm.generic-user.id}")
    public String WF_USER_ID;

    @Value("${camunda.bpm.generic-user.password}")
    public String WF_USER_PAZZ;

    //------------------------------------
    // LOAN
    //------------------------------------
    @Value("${AbbeyRye}")
    public String AbbeyRye;
    // aio stp
    @Value("${RayakRoe}")
    public String RayakRoe;
    // aio stp
    @Value("${StaticFilePath}")
    public String StaticFilePath;
    // aio stp
    @Value("${PdfDirectoryPath}")
    public String PdfDirectoryPath;
    // aio stp
    @Value("${ConfigTableName}")
    public String ConfigTableName;

    @Value("${spring.profiles.active}")
    public String active;


    @Value("${OTPOpID}")
    public String OTPOpID;
    @Value("${OTPBillDep}")
    public String OTPBillDep;
    @Value("${MailOpID}")
    public String MailOpID;
    @Value("${ValidationServerUrl}")
    public String validationServerUrl;

    @Value("${AirLoanWebServerHost}")
    public String AirLoanWebServerHost;
    @Value("${AirLoanDomainWebServerHost}")
    public String AirLoanDomainWebServerHost;
    @Value("${AirLoanAPServerHost}")
    public String AirLoanAPServerHost;

    @Value("${BookingDay}")
    public String bookingDay;
    @Value("${BookingMonth}")
    public String bookingMonth;


    @Value("${KGI_WQ_CASE_DATA_URL}")
    public String WQCaseDataUrl;
    @Value("${KGI_WQ_CASE_DATA_NAME}")
    public String WQCaseDataApiName;
    @Value("${KGI_GET_PLOAN_STATUS_INFO_URL}")
    public String GetPLoanStatusInfoUrl;
    @Value("${KGI_GET_PLOAN_STATUS_INFO_NAME}")
    public String GetPLoanStatusInfoName;

    @Value("${EDDA_API_HOST}")
    public String EDDA_API_HOST;

    public static final String APIM_CS_SK = "APIM.CrossSell.SecretKey";

    public static final String STOCK_APIM_CS_AK = "stock.APIM.CrossSell.ApiKey";
    public static final String STOCK_APIM_CS_SK = "stock.APIM.CrossSell.SecretKey";

    public static final String HEADER_CONTENT_M = "M" + "D" + "5";
    public static final String HEADER_CONTENT_A = "A" + "u" + "t"+ "h" + "orization";

    /**
     * 解析密碼
     * @param pazzd
     * @return
     */
    public String getPazzd(String pazzd) {
        try {
            if(active.equals("local")){
                return pazzd;
            }else {
                KGIOBDAESUtil instance = KGIOBDAESUtil.getInstance(AbbeyRye, RayakRoe);
                return new String(instance.decrypt(pazzd), Charset.forName("UTF-8")) ;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 加密密碼
     * @param password
     * @return
     */
    public String encryptPazzd(String password) {
        try {
            if(active.equals("local")){
                return password;
            }else {
                KGIOBDAESUtil instance = KGIOBDAESUtil.getInstance(AbbeyRye, RayakRoe);
                return instance.encrypt(password) ;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
