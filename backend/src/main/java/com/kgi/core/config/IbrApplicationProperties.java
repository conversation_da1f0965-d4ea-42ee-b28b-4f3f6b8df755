package com.kgi.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * IBR應用程式配置屬性
 * 對應application.yml中的ibr配置項目
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ibr")
public class IbrApplicationProperties {
    
    /**
     * 應用程式基本資訊
     */
    private Application application = new Application();
    
    /**
     * API相關設定
     */
    private Api api = new Api();
    
    /**
     * 安全性設定
     */
    private Security security = new Security();
    
    /**
     * 業務邏輯設定
     */
    private Business business = new Business();
    
    /**
     * 應用程式基本資訊
     */
    @Data
    public static class Application {
        
        /**
         * 應用程式名稱
         */
        private String name = "KGI數位跨境匯款解付平台";
        
        /**
         * 版本號
         */
        private String version = "1.0.0";
        
        /**
         * 環境
         */
        private String environment = "development";
    }
    
    /**
     * API相關設定
     */
    @Data
    public static class Api {
        
        /**
         * API基礎路徑
         */
        private String basePath = "/api/ibr";
        
        /**
         * API版本
         */
        private String version = "v1";
        
        /**
         * 請求超時時間 (毫秒)
         */
        private int timeout = 30000;
    }
    
    /**
     * 安全性設定
     */
    @Data
    public static class Security {
        
        /**
         * JWT相關設定
         */
        private Jwt jwt = new Jwt();
        
        /**
         * JWT設定
         */
        @Data
        public static class Jwt {
            
            /**
             * JWT密鑰
             */
            private String secret = "kgi-ibr-jwt-secret-key-2024";
            
            /**
             * JWT過期時間 (毫秒)
             */
            private long expiration = 86400000L; // 24小時
            
            /**
             * 刷新令牌過期時間 (毫秒)
             */
            private long refreshExpiration = 604800000L; // 7天
        }
    }
    
    /**
     * 業務邏輯設定
     */
    @Data
    public static class Business {
        
        /**
         * OTP相關設定
         */
        private Otp otp = new Otp();
        
        /**
         * 匯率相關設定
         */
        private ExchangeRate exchangeRate = new ExchangeRate();
        
        /**
         * 匯款相關設定
         */
        private Remittance remittance = new Remittance();
        
        /**
         * 驗證相關設定
         */
        private Verification verification = new Verification();
        
        /**
         * OTP設定
         */
        @Data
        public static class Otp {
            
            /**
             * OTP過期時間 (分鐘)
             */
            private int expiryMinutes = 5;
            
            /**
             * 最大嘗試次數
             */
            private int maxAttempts = 3;
            
            /**
             * 重發冷卻時間 (秒)
             */
            private int resendCooldownSeconds = 60;
        }
        
        /**
         * 匯率設定
         */
        @Data
        public static class ExchangeRate {
            
            /**
             * 匯率快取持續時間 (分鐘)
             */
            private int cacheDurationMinutes = 15;
            
            /**
             * API超時時間 (秒)
             */
            private int apiTimeoutSeconds = 10;
        }
        
        /**
         * 匯款設定
         */
        @Data
        public static class Remittance {
            
            /**
             * 美元最大金額
             */
            private int maxAmountUsd = 50000;
            
            /**
             * 台幣最大金額
             */
            private int maxAmountTwd = 1500000;
            
            /**
             * 處理超時時間 (小時)
             */
            private int processingTimeoutHours = 24;
        }
        
        /**
         * 驗證設定
         */
        @Data
        public static class Verification {
            
            /**
             * 身分驗證超時時間 (分鐘)
             */
            private int idVerifyTimeoutMinutes = 10;
            
            /**
             * 銀行驗證超時時間 (分鐘)
             */
            private int bankVerifyTimeoutMinutes = 5;
        }
    }
    
    /**
     * 取得完整API路徑
     */
    public String getFullApiPath() {
        return api.getBasePath() + "/" + api.getVersion();
    }
    
    /**
     * 檢查是否為開發環境
     */
    public boolean isDevelopment() {
        return "development".equalsIgnoreCase(application.getEnvironment());
    }
    
    /**
     * 檢查是否為生產環境
     */
    public boolean isProduction() {
        return "production".equalsIgnoreCase(application.getEnvironment());
    }
    
    /**
     * 檢查是否為測試環境
     */
    public boolean isTest() {
        return "test".equalsIgnoreCase(application.getEnvironment());
    }
    
    /**
     * 取得OTP過期時間 (毫秒)
     */
    public long getOtpExpiryMillis() {
        return business.getOtp().getExpiryMinutes() * 60L * 1000L;
    }
    
    /**
     * 取得匯率快取過期時間 (毫秒)
     */
    public long getExchangeRateCacheMillis() {
        return business.getExchangeRate().getCacheDurationMinutes() * 60L * 1000L;
    }
    
    /**
     * 取得處理超時時間 (毫秒)
     */
    public long getProcessingTimeoutMillis() {
        return business.getRemittance().getProcessingTimeoutHours() * 60L * 60L * 1000L;
    }
}