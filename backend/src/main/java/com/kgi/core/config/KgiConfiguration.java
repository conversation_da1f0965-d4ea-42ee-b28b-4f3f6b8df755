/**
 * Copyright (c) 2020 Madison Data Consulting
 * All Rights Reserved.
 *
 * This software is the confidential and proprietary information of
 * Madison Data Consulting ("Confidential Information").
 *
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of license agreement you entered
 * into with Madison Data Consulting
 */

package com.kgi.core.config;

import com.kgi.core.service.concurrent.DatabaseManagementService;
import com.kgi.core.util.EncryptUtils;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.jdbc.JdbcProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.persistence.EntityManagerFactory;
import javax.sql.DataSource;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * multiple data sources we can’t anymore specify the JPA properties like dialect and ddl.auto in yml
 */
@Slf4j
@Configuration
// @EnableTransactionManagement // 暫時禁用
// @EntityScan(basePackages={"com.kgi.core.application.vo.entity.*"}) // 暫時禁用
// @EnableJpaRepositories( // 暫時禁用
//         basePackages = {"com.kgi.core.application.vo.repository"},
//         entityManagerFactoryRef = "kgiEntityManagerFactory",
//         transactionManagerRef = "kgiTransactionManager"
// )
//@EnableConfigurationProperties({ DataSourceProperties.class, JdbcProperties.class, KgiDataSourceProperties.class })
@EnableConfigurationProperties({ DataSourceProperties.class, JdbcProperties.class })
public class KgiConfiguration {

    @Autowired
    private Environment env; // Contains Properties Load by @PropertySources

    //    @Value("${primaryDBConfig.url}") //走 DB.properties
    @Value("${spring.datasource-kgi.url}") //改走 application.yml
    private String url;

    //    @Value("${primaryDBConfig.username}")
    @Value("${spring.datasource-kgi.username}")
    private String username;

    //    @Value("${primaryDBConfig.password}")
    @Value("${spring.datasource-kgi.password}")
    private String pazzed;

    @Value("${spring.datasource-kgi.hikari.connection-timeout}")
    private long connectionTimeout;

    @Value("${spring.datasource-kgi.hikari.maximum-pool-size}")
    private int maximumPoolSize;

    @Value("${spring.datasource-kgi.hikari.idle-timeout}")
    private int idleTimeout;

    @Value("${spring.datasource-kgi.hikari.minimum-idle}")
    private int minimumIdle;

    @Value("${spring.datasource-kgi.hikari.max-lifetime}")
    private int maxLifetime;

    @Value("${spring.datasource-kgi.hikari.auto-commit}")
    private boolean autoCommit;

    @Value("${spring.datasource-kgi.hikari.connection-test-query}")
    private String connectionTestQuery;

    @Value("${spring.datasource-kgi.encrypt}")
    private boolean encrypt;

    /**
     * DataSourceProperties
     * @return
     */
    @Primary
    @Bean(name = "kgiDataSourceProperties")
    @ConfigurationProperties(prefix="spring.datasource-kgi")
    public DataSourceProperties kgiDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * DataSource: spring.datasource-kgi
     * @param kgiDataSourceProperties
     * @return
     */
    @Primary
    @Bean(name = "kgiDataSource")
    public DataSource kgiDataSource(@Qualifier("kgiDataSourceProperties") DataSourceProperties kgiDataSourceProperties) throws UnsupportedEncodingException {

        /** 判斷YML 是否需要解密 或是 直接使用 YML值 */
        String pzd = this.encrypt ? EncryptUtils.base64Decode(pazzed.replace("Encryped:", "")) : this.pazzed;

        HikariDataSource hikariDataSource = kgiDataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class)
                .url(url)
                .username(username)
                .password(pzd)
                .build();
        hikariDataSource.setConnectionTimeout(connectionTimeout);
        hikariDataSource.setIdleTimeout(idleTimeout);
        hikariDataSource.setMaximumPoolSize(maximumPoolSize);
        hikariDataSource.setMinimumIdle(minimumIdle);
        hikariDataSource.setMaxLifetime(maxLifetime);
        hikariDataSource.setConnectionTestQuery(connectionTestQuery);

        System.out.println("[KgiDataSource] idleTimeout: " + hikariDataSource.getIdleTimeout());
        System.out.println("[KgiDataSource] connectionTimeout: " + hikariDataSource.getConnectionTimeout());
        System.out.println("[KgiDataSource] maximumPoolSize: " + hikariDataSource.getMaximumPoolSize());
        System.out.println("[KgiDataSource] minimumIdle: " + hikariDataSource.getMinimumIdle());
        System.out.println("[KgiDataSource] maxLifetime: " + hikariDataSource.getMaxLifetime());

        return hikariDataSource;
    }

    // /**
    //  * LocalContainerEntityManagerFactoryBean: EntityManagerFactory
    //  * 這邊可以寫入 hibernate 一般設定
    //  * @param kgiEntityManagerFactoryBuilder
    //  * @param kgiDataSource
    //  * @return
    //  */
    // @Primary
    // @Bean(name = "kgiEntityManagerFactory")
    // public LocalContainerEntityManagerFactoryBean kgiEntityManagerFactory(
    //         EntityManagerFactoryBuilder kgiEntityManagerFactoryBuilder, @Qualifier("kgiDataSource") DataSource kgiDataSource) {

    //     Map<String, String> kgiJpaProperties = new HashMap<>();
    //     kgiJpaProperties.put("hibernate.dialect", env.getProperty("spring.jpa.properties.hibernate.dialect-kgi")); //Workflow

    //     //System.out.println("hibernate.hbm2ddl.auto: " + env.getProperty("spring.jpa.hibernate.ddl-auto"));
    //     kgiJpaProperties.put("hibernate.hbm2ddl.auto", env.getProperty("spring.jpa.hibernate.ddl-auto"));  //update , create-drop
    // //        kgiJpaProperties.put("hibernate.naming.physical-strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
    //     kgiJpaProperties.put("hibernate.naming.physical-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringNamingStrategy");
    // //        kgiJpaProperties.put("hibernate.naming-strategy", "org.springframework.boot.orm.jpa.hibernate.SpringNamingStrategy");
    // //        kgiJpaProperties.put("hibernate.physical_naming_strategy", SpringPhysicalNamingStrategy.class.getName()); //table加上底線, ex: CASE_DATA
    // //        kgiJpaProperties.put("hibernate.implicit_naming_strategy", SpringImplicitNamingStrategy.class.getName());

    //     //org.hibernate.boot.model.naming.ImplicitNamingStrategyJpaCompliantImpl
    //     return kgiEntityManagerFactoryBuilder
    //             .dataSource(kgiDataSource)
    //             .packages("com.kgi.core.application.vo.entity") //multi datasource 必須指定 entity package
    //             .persistenceUnit("kgiDataSource")
    //             .properties(kgiJpaProperties)
    //             .build();
    // }

    /**
     * TransactionManager
     * @param kgiEntityManagerFactory
     * @return
     */
    // TODO: 修復EntityManagerFactory import問題
    // @Primary
    // @Bean(name = "kgiTransactionManager")
    // public PlatformTransactionManager kgiTransactionManager(
    //         @Qualifier("kgiEntityManagerFactory") EntityManagerFactory kgiEntityManagerFactory) {
    //
    //     return new JpaTransactionManager(kgiEntityManagerFactory);
    // }

    @Value("${graphql.url:/graphiql}")
    private String graphqlurl;
    /*
     * No cors origin global setting.
     */
    @Bean
    public WebMvcConfigurer corsConfigurerGraphQL() {

        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping(graphqlurl).allowedOrigins("http://localhost:4000");
            }
        };
    }

    @Bean
    public DatabaseManagementService databaseService(@Qualifier("kgiDataSource") DataSource kgiDataSource) {
        return new DatabaseManagementService(kgiDataSource); //Datasource config: spring.datasource-kgi
    }

    @Bean
    @ConfigurationProperties(prefix="spring.datasource-workflow")
    public DataSourceProperties workflowDataSourceProperties() {
        return new DataSourceProperties();
    }

}
