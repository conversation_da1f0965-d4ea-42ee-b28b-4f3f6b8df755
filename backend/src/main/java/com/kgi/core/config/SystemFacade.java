package com.kgi.core.config;

// Core services
import com.kgi.module.auth.domain.service.OtpService;
import com.kgi.module.auth.infrastructure.mock.OtpServiceMock;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import jakarta.annotation.PostConstruct;

/**
 * Mock 服務統一管理配置
 * 根據 useMockupData 和 useMockupCallOut 設定自動切換真實/Mock 服務
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Configuration
public class SystemFacade {

    @Value("${kgi.useMockupData:false}")
    private boolean useMockupData;

    @Value("${kgi.useMockupCallOut:false}")
    private boolean useMockupCallOut;

    @Value("${kgi.mockupServer:false}")
    private boolean mockupServer;

    @Value("${kgi.mockupServer.url:http://localhost:3000}")
    private String mockupServerUrl;

    @Value("${kgi.useMockupFTP:false}")
    private boolean useMockupFTP;

    /**
     * 判斷是否使用 Mock 服務
     */
    private boolean shouldUseMock() {
        return useMockupData || useMockupCallOut;
    }

    // ========== Core Services ==========

    /**
     * OTP 服務
     * 注意：這裡示範如何根據設定切換真實/Mock 服務
     * 實際上 OtpService 已經是一個具體類，不是介面
     * 所以這裡使用 @ConditionalOnProperty 來控制
     */
    @Bean("mockOtpService")
    @ConditionalOnProperty(name = "kgi.useMockupData", havingValue = "true")
    public OtpServiceMock otpServiceMock() {
        log.info("Creating Mock OTP Service");
        return new OtpServiceMock();
    }

    // ========== Configuration Info ==========

    /**
     * 啟動時顯示 Mock 設定狀態
     */
    @PostConstruct
    public void displayMockConfiguration() {
        log.info("=== Mock Configuration Status ===");
        log.info("useMockupData: {}", useMockupData);
        log.info("useMockupCallOut: {}", useMockupCallOut);
        log.info("mockupServer: {}", mockupServer);
        log.info("mockupServerUrl: {}", mockupServerUrl);
        log.info("useMockupFTP: {}", useMockupFTP);
        log.info("Mock Services Enabled: {}", shouldUseMock());
        log.info("================================");
    }
}