/**
 * Copyright (c) 2020 Madison Data Consulting
 * All Rights Reserved.
 * <p>
 * This software is the confidential and proprietary information of
 * Madison Data Consulting ("Confidential Information").
 * <p>
 * You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of license agreement you entered
 * into with Madison Data Consulting
 */

package com.kgi.core.config;

import com.kgi.core.jdbc.JdbcDAO;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.jdbc.JdbcProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Slf4j
@Configuration
@EnableConfigurationProperties({DataSourceProperties.class, JdbcProperties.class})
public class JdbcTemplateConfiguration {

    @Value("${spring.datasource-kgi.url}")
    private String url;

//    @Value("${spring.datasource-kgi.username}")
//    private String username;
//
//    @Value("${spring.datasource-kgi.password}")
//    private String password;
//
//    @Value("${spring.datasource-kgi.driver-class-name}")
//    private String driver;

    @Bean
    @ConfigurationProperties("spring.datasource.configuration")
    public HikariDataSource dataSource(DataSourceProperties dataSourceProperties) {
        log.info("[JdbcTemplateConfiguration] url: " + url);
        return dataSourceProperties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    @Bean
    public JdbcDAO jdbcDAO(DataSource dataSource) {
        JdbcDAO jdbcDAO = new JdbcDAO();
        jdbcDAO.setDataSource(dataSource);

        return jdbcDAO;
    }
}
