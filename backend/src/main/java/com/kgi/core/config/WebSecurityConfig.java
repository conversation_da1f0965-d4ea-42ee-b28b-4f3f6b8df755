package com.kgi.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

@Slf4j
@Configuration
// 暫時停用 Spring Security 以解決啟動問題
// @EnableWebSecurity
public class WebSecurityConfig {

    // 暫時註解掉 SecurityFilterChain 以解決啟動問題
    // @Bean
    // public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    //     log.info("配置 Spring Security - 開發環境簡化版本");
    //     
    //     http
    //         // 允許跨域請求
    //         .cors(cors -> cors.configurationSource(request -> {
    //             var corsConfiguration = new org.springframework.web.cors.CorsConfiguration();
    //             corsConfiguration.setAllowedOriginPatterns(java.util.List.of("*"));
    //             corsConfiguration.setAllowedMethods(java.util.List.of("GET", "POST", "PUT", "DELETE", "OPTIONS"));
    //             corsConfiguration.setAllowedHeaders(java.util.List.of("*"));
    //             corsConfiguration.setAllowCredentials(true);
    //             return corsConfiguration;
    //         }))
    //         // 關閉 CSRF 防護 (開發環境)
    //         .csrf(csrf -> csrf.disable())
    //         // 設定路徑存取權限
    //         .authorizeHttpRequests(authz -> authz
    //             // IBR API允許通過 (數位跨境匯款解付平台)
    //             .requestMatchers("/api/ibr/**").permitAll()
    //             // 健康檢查端點允許通過
    //             .requestMatchers("/api/health/**").permitAll()
    //             // Actuator監控端點允許通過
    //             .requestMatchers("/actuator/**").permitAll()
    //             // H2控制台允許通過 (開發環境)
    //             .requestMatchers("/h2-console/**").permitAll()
    //             // 測試端點允許通過
    //             .requestMatchers("/test/**", "/swagger-ui/**").permitAll()
    //             // 靜態資源允許通過
    //             .requestMatchers("/", "/resources/**", "/static/**", "/public/**").permitAll()
    //             .requestMatchers("/*.html", "/**/*.html", "/**/*.css", "/**/*.js", 
    //                            "/**/*.png", "/**/*.jpg", "/**/*.gif", "/**/*.svg", 
    //                            "/**/*.ico", "/**/*.ttf", "/**/*.woff", "/**/*.woff2").permitAll()
    //             // 暫時允許所有請求通過 (開發環境)
    //             .anyRequest().permitAll())
    //         // 關閉 session
    //         .sessionManagement(session -> session
    //             .sessionCreationPolicy(SessionCreationPolicy.STATELESS));
    // 
    //     return http.build();
    // }

    @Bean
    public BCryptPasswordEncoder encoder() {
        return new BCryptPasswordEncoder();
    }
}