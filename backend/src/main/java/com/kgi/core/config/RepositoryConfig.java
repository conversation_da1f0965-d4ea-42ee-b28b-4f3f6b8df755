package com.kgi.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * Repository 實作模式配置
 * 用於切換 Memory 和 JPA 兩種實作方式
 */
@Configuration
@ConfigurationProperties(prefix = "kgi.repository")
@Data
public class RepositoryConfig {
    
    /**
     * Repository 類型
     * - memory: 使用記憶體實作（適合開發測試）
     * - jpa: 使用 JPA 實作（適合生產環境）
     */
    private RepositoryType type = RepositoryType.MEMORY;
    
    /**
     * Repository 類型列舉
     */
    public enum RepositoryType {
        MEMORY("memory"),
        JPA("jpa");
        
        private final String value;
        
        RepositoryType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
    
    /**
     * 是否使用 JPA 模式
     */
    public boolean isJpaMode() {
        return RepositoryType.JPA.equals(type);
    }
    
    /**
     * 是否使用 Memory 模式
     */
    public boolean isMemoryMode() {
        return RepositoryType.MEMORY.equals(type);
    }
}