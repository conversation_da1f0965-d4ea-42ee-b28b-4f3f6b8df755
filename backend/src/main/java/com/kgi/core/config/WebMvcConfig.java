package com.kgi.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC配置
 * 處理CORS、攔截器、靜態資源等配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    
    /**
     * 配置CORS跨域設定
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // IBR 相關 API
        registry.addMapping("/api/ibr/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // 測試 API (僅開發環境使用)
        registry.addMapping("/api/test/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // 通知模組 API
        registry.addMapping("/api/notification/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // OTP 模組 API
        registry.addMapping("/api/otp/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // PCode2566 模組 API
        registry.addMapping("/api/pcode2566/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // 允許Actuator端點跨域
        registry.addMapping("/actuator/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
        
        // 允許H2控制台跨域
        registry.addMapping("/h2-console/**")
                .allowedOriginPatterns("*")
                .allowedMethods("GET", "POST")
                .allowedHeaders("*")
                .allowCredentials(false)
                .maxAge(3600);
    }
    
    /**
     * 配置靜態資源處理
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 開發環境下允許訪問靜態資源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        
        registry.addResourceHandler("/public/**")
                .addResourceLocations("classpath:/public/");
        
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        
        // API文檔相關資源
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/swagger-ui/");
        
        registry.addResourceHandler("/api-docs/**")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
    
    /**
     * 配置攔截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 可以在這裡添加自定義攔截器
        // 例如：日誌攔截器、權限檢查攔截器等
        
        // 示例：添加日誌攔截器
        // registry.addInterceptor(new LoggingInterceptor())
        //         .addPathPatterns("/api/ibr/**")
        //         .excludePathPatterns("/api/ibr/health", "/api/ibr/status");
    }
}