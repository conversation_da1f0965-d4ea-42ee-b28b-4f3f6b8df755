package com.kgi.core.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.resource.EncodedResourceResolver;
import org.springframework.web.servlet.resource.PathResourceResolver;

/**
 * 會覆蓋 application.properties / spring.resources.static-locations
 */
@Slf4j
@Configuration
public class StaticResourceHandler implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // When overriding default behavior, you need to add default(/) as well as added static paths(/webapp).
        log.info("addResourceHandlers...");

        // src/main/resources/static/...
        registry
                //增加 /static/** 映射到 classpath:/static/
                .addResourceHandler("/**") // « /static/*.*
                .addResourceLocations("classpath:/static/") // Default Static Location

                .setCacheControl(CacheControl.noCache()) //不要有 cache
//                .setCachePeriod( 3600 )
                .resourceChain(true) // 4.1
                .addResolver(new EncodedResourceResolver()) // 4.1
                .addResolver(new PathResourceResolver()); //4.1

//        https://www.baeldung.com/swagger-2-documentation-for-spring-rest-api
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/", "index.html");
    }
}
