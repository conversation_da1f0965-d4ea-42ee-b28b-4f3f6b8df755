package com.kgi.core.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 資料庫配置
 */
@Configuration
@EnableJpaRepositories(basePackages = "com.kgi.module.*.infrastructure.persistence")
@EntityScan(basePackages = "com.kgi.module.*.infrastructure.persistence.entity")
@EnableTransactionManagement
public class DatabaseConfig {
}