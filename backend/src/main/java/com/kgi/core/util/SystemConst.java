package com.kgi.core.util;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO: From STP
 * airloanEX/common/src/main/java/com/kgi/eopend3/common/SystemConst.java
 */
public class SystemConst {

	public static final String ACTIVE_PROFILES_PROD = "prod";

	/** QR_ChannelDepartListEOP 預設值 */
	public static final String QR_CHANNELDEPARTLISTEOP_DEFAULT = "-";

	/** 預設Channel Id */
	public static final String DEFAULT_CHANNEL_ID = "KB";

	/** 蝦皮Channel Id */
	public static final String SP_CHANNEL_ID = "SP";

	/** 露天Channel Id */
	public static final String RT_CHANNEL_ID = "RT";

	/** 露天Channel Id */
	public static final String JK_CHANNEL_ID = "JK";

	/** 凱基人壽Channel Id */
	public static final String CHANNEL_ID_CL = "CL";

	/** 凱基人壽Channel Id */
	public static final String READ_TIME_OUT = "Read timed out";

	/** 凱基人壽未收到投保通知書之等待日 */
	public static final int WAIT_FOR_CL_POLICY_INFO_DAYS = 15;

	/** RouteType */
	public static final String ROUTE_TYPE_INNER = "0";

	/** ESB Branch No */
	public static final String ESB_BRANCH_NO = "0660";
	public static final String ESB_TELLER = "90002980";

	/** 數三帳戶 */
	public static final String PRODUCT_TYPE_ED3 = "6";
	/** 數二帳戶 */
	public static final String PRODUCT_TYPE_D2 = "1";
	/** 貸款 */
	public static final String PRODUCT_TYPE_LOAN = "0";
	/** 信用卡 */
	public static final String PRODUCT_TYPE_CREDIT = "1";
	/** 數位帳戶 信用卡系統參數 */
	public static final String ADDPHOTO_PROD_TYPE_ED3 = "4";
	/** 薪轉開戶 信用卡系統參數 */
	public static final String ADDPHOTO_PROD_TYPE_EOP = "3";

	/** 凱證 sendCaseSts 成功碼 */
	public static final String KS_SENDCASESTS_API_SUCCESS = "0";
	/** 凱證 sendCaseSts 成功碼 */
	public static final String KS_SENDCASESTS_API_NOEXIST = "1";
	/** 凱證 getApyData 成功碼 */
	public static final String KS_GETAPYDATA_API_SUCCESS = "0";
	/** 凱證 getApyData 其它錯誤 */
	public static final String KS_GETAPYDATA_API_ERROR = "99";
	/** 凱證 getApyData 查無資料 */
	public static final String KS_GETAPYDATA_API_NODATA = "2";

	/** 凱證 getKGISCustInfo 成功碼 */
	public static final String KS_GETKGISCUSTINFO_API_SUCCESS = "0000";

	/** 短期間內密集申請_三個月內申請2次數位帳戶，均未完成開戶流程者 */
	public static final String MSG_EOPEN_TOO_MANY_TIMES = "您已有多筆線上開戶流程未完成，系統已暫停服務，若您仍有開戶需求，請洽各分行辦理。";
	/** 申辦資料與投保資料不符，如填寫無誤，仍可繼續申辦，但無法綁定保費續期扣繳(998) */
	public static final String MSG_ED3_CL_TOKEN_ID_INCORRECT = "申辦資料與投保資料不符，如填寫無誤，仍可繼續申辦，但無法綁定保費續期扣繳(998)";
	/** 凱基人壽系統有誤，請洽凱基人壽業務人員。(501) */
	public static final String MSG_ED3_CL_SYSTEM_TIMEOUT = "凱基人壽系統有誤，請洽凱基人壽業務人員。(501)";
	/** 凱基人壽系統有誤，請洽凱基人壽業務人員。(502) */
	public static final String MSG_ED3_CL_SYSTEM_ERROR = "凱基人壽系統有誤，請洽凱基人壽業務人員。(502)";
	/** 凱基人壽系統有誤，請洽凱基人壽業務人員。(503) */
	public static final String MSG_ED3_CL_ESB_ERROR = "凱基人壽系統有誤，請洽凱基人壽業務人員。(503)";
	/** 綁定凱基人壽保費續扣時效已過(999) */
	public static final String MSG_ED3_CL_TOKEN_EXPIRED = "綁定凱基人壽保費續扣時效已過，您仍可繼續申辦銀行產品，但無法綁定保費續期扣繳(999)";

	public static final String KGIHEADER = "KGIeOpenD3"; // KGIairloanEX / KGIeOpenD3

	public static final String KGI_BANK_ID = "809";

	// 貸款排程使用 讓排程知道目前步驟
	public static final String LOAN_PDF_STEP1 = "loan_pdf_step1";
	public static final String LOAN_PDF_STEP2 = "loan_pdf_step2";
	public static final String LOAN_PDF_STEP3 = "loan_pdf_step3";
	public static final String LOAN_PDF_STEP4 = "loan_pdf_step4";
	public static final String LOAN_PDF_STEP5 = "loan_pdf_step5";

	/**
	 * 信用卡搬過來的 再確認 Start
	 */
	/** 數位信貸各種驗證-驗證類型 */
	public static final String C_VERIFY_TYPE_NCCC = "0";
	public static final String C_VERIFY_TYPE_BANKACOUNT = "1";
	public static final String C_VERIFY_TYPE_CHTMBC = "2";
	public static final String C_VERIFY_TYPE_CROSSSELL = "3"; // @2021-02-04備註: CC新戶證券驗身 or CC既有戶 OTP 驗身這裡都會標成 3
	public static final String C_VERIFY_TYPE_NOTVERIFY = "4";
	public static final String C_VERIFY_TYPE_OTP = "5";

	public static final String C_VERIFY_NCCC = "他行卡驗身";
	public static final String C_VERIFY_BANKACOUNT = "他行帳戶驗身";
	public static final String C_VERIFY_CHTMBC = "MBC";
	public static final String C_VERIFY_OTP = "OTP";
	public static final String C_VERIFY_CROSSSELL = "凱基證券憑證驗身";
	public static final String C_VERIFY_NOTVERIFY = "線下驗身";

	/** Nccc驗身成功 */
	public static final String C_VERIFY_TYPE_NCCC_SUCCESS = "0";

	public static final String PCODE2566_UID = "5980";

	/**
	 * 信用卡搬過來的 再確認 End
	 */

	/** 立約產品代號 - 信貸 */
	public final static String CONTRACT_PRODUCT_ID_TYPE_LOAN = "1";
	/** 立約產品代號 - 現金卡 */
	public final static String CONTRACT_PRODUCT_ID_TYPE_CASH = "2";
	/** 立約產品代號 - 一般e貸寶 */
	public final static String CONTRACT_PRODUCT_ID_TYPE_ELOAN = "3";

	/** 回傳代碼的KEY */
	public final static String API_RSPCODE = "RspCode";
	/** 回傳訊息的KEY */
	public final static String API_RSPMSG = "RspMsg";
	/** 回傳的子回應 */
	public final static String API_SUBRSP = "SubRsp";

	/** 呼叫API預設值 */
	public final static int API_DEFAULT = -1;
	/** 呼叫API成功 */
	public final static int API_SUCCESS = 0;
	/** 呼叫API逾時 */
	public final static int API_TIMEOUT = 9000;
	/** 呼叫API其他錯誤(不明原因) */
	public final static int API_OTHER_ERROR = 9999;

	/** 貸款 */
	public static final String CASE_UNINQTYPE_LOAN = "01";
	/** 卡加貸 */
	public static final String CASE_UNINQTYPE_CREDIT_AND_LOAN = "02";
	/** 信用卡 */
	public static final String CASE_UNINQTYPE_CREDIT = "05";
	/** AIO_CC */
	public static final String CASE_UNINQTYPE_AIO_CC = "06";
	/** AIO_LOAN */
	public static final String CASE_UNINQTYPE_AIO_LOAN = "07";
	/** AIO_D3 */
	public static final String CASE_UNINQTYPE_AIO_D3 = "08";
	/** AIO_LOAN_D3 */
	public static final String CASE_UNINQTYPE_AIO_LOAN_D3 = "09";
	/** AIO_LOAN_CC */
	public static final String CASE_UNINQTYPE_AIO_LOAN_CC = "10";
	/** AIO_CC_D3 */
	public static final String CASE_UNINQTYPE_AIO_CC_D3 = "11";
	/** 薪轉 */
	public static final String CASE_UNINQTYPE_SAL = "12";
	/** 預約開戶 與 數三 相同 */
	public static final String CASE_UNINQTYPE_APPT = "13";
	/** 數三 */
	public static final String CASE_UNINQTYPE_D3 = "13";
	/** 凱基人壽 */
	public static final String CASE_UNINQTYPE_CL = "14";
	/** AIO_LOAN_CC_D3 */
	public static final String CASE_UNINQTYPE_AIO_LOAN_CC_D3 = "15";
	/** 數二 */
	public static final String CASE_UNINQTYPE_D2 = "16";
	/** 房貸 */
	public static final String CASE_UNINQTYPE_HL = "17";
	/** 信用卡調額 */
	public static final String CASE_UNINQTYPE_CREDIT_LIMIT = "18";

	/** 被呼叫之 API 回傳狀態 */
	/** API回傳狀態-成功 */
	public final static String RETURN_STATUS_SUCCESS = "00";
	/** API回傳狀態-失敗 */
	/** GetAccountStatus API回傳狀態-失敗 */
	public final static String RETURN_STATUS_FAIL = "99";
	public final static String RETURN_MSG_FAIL = "系統(案件)異常，請稍後再試";
	/** GetAccountStatus API回傳狀態-開戶影像未回傳資料 */
	public final static String RETURN_STATUS_NO_AORESULT = "98";
	public final static String RETURN_MSG_NO_AORESULT = "開戶系統未回傳案件狀態";
	/** GetAccountStatus API回傳狀態-查無案編 */
	public final static String RETURN_STATUS_NO_AIOCASEDATA = "97";
	public final static String RETURN_MSG_NO_AIOCASEDATA = "查無案件，請確認案件編號是否正確";
	/** GetAccountStatus API回傳狀態-退件 */
	public final static String RETURN_STATUS_AORESULT_REJECT = "96";
	public final static String RETURN_MSG_AORESULT_REJECT = "開戶系統回傳案件退件";

	/** GetAccountStatus API回傳狀態-退件 */
	public final static String RETURN_STATUS_90D_REJECT = "95";
	public final static String RETURN_MSG_90D_REJECT = "90天手機異動過，故不可線上開戶";


	/** 數位帳戶 */
	public static final String CL_API_PDCT_TYPE_D3 = "1";
	/** 信用卡 */
	public static final String CL_API_PDCT_TYPE_CC = "2";

	/** 凱基人壽API 產品狀態-成功 */
	public static final String CL_API_PDCT_STATUS_OK = "00";

	/** 縣市鄉鎮區代碼 */
	public final static String DROPDOWNDATA_NAME_ZIPCODEMAPPING = "zipcodemapping";

	/** 合作廠商 */
	public static final String DROPDOWNDATA_NAME_COLLABORATE_SECURITIES = "collaborateSecurities";

	/** 主動推薦 */
	public final static String TERMS_NAME_ACTIVE_R = "active_recommendation";
	/** 存款總約定書 */
	public final static String TERMS_NAME_EOP_DEPOSIT = "EOPDepositPromise";

	/** 金融控股公司及參與資料共享公司間為辨識風險 */
	public final static String TERMS_NAME_FINC_SHR_RISK = "AgreementFincShrRisk";
	/** 金融控股公司及參與資料共享公司間為提升客戶便利性 */
	public final static String TERMS_NAME_FINC_SHR_ADV = "AgreementFincShrAdv";
	/** 第三方共銷條款 */
	public final static String TERMS_NAME_COSELL_FLAG_B = "cosellAgreementFlagB";
	/** 金控共銷條款 */
	public final static String TERMS_NAME_COSELL_FLAG_A = "cosellAgreementFlagA";
	/** 我已閱讀並同意貴行查詢聯徵信用資訊 */
	public final static String TERMS_NAME_JOINT_CREDIT = "jointCredit";
	/** 金控共銷條款 */
	public final static String TERMS_NAME_CREDIT_INSTALLMENT_TEXT = "credit_installment_text";

	/** 證券 個資使用同意書 */
	public final static String TERMS_NAME_STOCK_DECLARATION = "declaration_stock";

	/** CH2 同意調閱本人與貴行既有往來資料 */
	public final static String AGREE_READ_OTHER_PRODUCT_DATA = "agreeReadOtherProductData";

	/** 完成頁 */
	public final static String BREAKPOINT_PAGE_FINISH = "N99";

	/** 案件狀態 -init */
	public final static String CASE_STATUS_INIT = "00";
	/** 案件狀態 -填寫中 */
	public final static String CASE_STATUS_WRITING = "01";
	/** 案件狀態 -填寫完成 */
	public final static String CASE_STATUS_WRITE_SUCCESS = "02";
	/** 案件狀態 -30天自動結案 or 預約開戶時間超過 */
	public final static String CASE_STATUS_SUSPENDBY30DAYS = "06";
	/** 案件狀態 -舊案件的斷點取消 */
	public final static String CASE_STATUS_OLD_UNIQID_CANCEL = "07";
	/** 案件狀態 -新案件取消 */
	public final static String CASE_STATUS_NEW_UNIQID_CANCEL = "08";
	/** 案件狀態 -送件中 */
	public final static String CASE_STATUS_SUBMITTING = "11";
	/** 案件狀態 -送件完成 */
	public final static String CASE_STATUS_SUBMIT_SUCCESS = "12";
	/** 案件狀態 -補件失敗 */
	public final static String CASE_STATUS_ADDTIONAL_FAIL = "18";
	/** 案件狀態 -送件失敗 */
	public final static String CASE_STATUS_SUBMIT_FAIL = "19";
	/** 案件狀態 - 凱基人壽投保逾時撤件 */
	public final static String CASE_STATUS_POLICYTIMEOUT = "20";
	// 以下是數合信貸用到的狀態碼
	/** 案件狀態 -拒貸已送件 */
	public final static String CASE_STATUS_REJECT_SENDED = "09";
	/** 案件狀態 -無對應之信用卡產品 */
	public final static String CASE_STATUS_PRODUCT_ERROR = "17";
	/** 案件狀態 -APS作業中 */
	public final static String CASE_STATUS_APS_PROCESSING = "21";
	/** 案件狀態 -APS作業完成 */
	public final static String CASE_STATUS_APS_COMPLETE = "22";
	/** 案件狀態 -加辦案件填寫中 */
	public final static String CASE_STATUS_ADDITION_WRITING = "51";
	/** 案件狀態 -加辦案件完成 */
	public final static String CASE_STATUS_ADDITION_SUCCESS = "52";
	/** 案件狀態 -加辦案件 在建立AioAdditionMapping時 遇到存在案件需將原加辦案件 狀態改掉 */
	public final static String CASE_STATUS_ADDITION_LOSE = "53";
	/** 案件狀態-等待立約 */
	public final static String CASE_STATUS_CONTRACT_WAIT = "91";
	/** 案件狀態-立約送出 */
	public final static String CASE_STATUS_CONTRACT_SUBMIT = "92";
	/** 案件狀態-立約成功 */
	public final static String CASE_STATUS_CONTRACT_SUCCESS = "93";
	/** 案件狀態-立約缺戶役政 */
	public final static String CASE_STATUS_CONTRACT_CHECK_rwv2c2 = "94";
	/**案件狀態- D3 帳戶驗身 90天更改手機 拒絕開戶退件*/
	public final static String CASE_STATUS_DIGITAL_REJECT = "21";

	/** 案件狀態-數存高風險職業類別或出生地非台灣不上傳開戶影像*/ //20240916新增出生地非台灣
	public final static String CASE_STATUS_DIGITAL_HIGH_RISK_MARK = "31";
	/** 案件狀態-數存高風險職業類別不上傳開戶影像*/
	public final static String CASE_STATUS_DIGITAL_REJECT_AND_HIGHRISK = "32";

	/** 預約開戶案件註銷逾期天數 */
	public final static String CANCEL_CASE_OVERDUE_DAYS = "30";
	/** 預約開戶案件刪除逾期天數 */
	public final static String DELETE_CASE_OVERDUE_DAYS = "60";


	/** TM案件狀態 -CMS填寫中 */
	public final static String TM_CASE_STATUS_INIT = "00";
	/** TM案件狀態 -填寫中 */
	public final static String TM_CASE_STATUS_WRITING = "01";
	/** TM案件狀態 -填寫完成 */
	public final static String TM_CASE_STATUS_WRITE_SUCCESS = "02";
	/** TM案件狀態 -TM進aio KGIB案件填寫中 */
	public final static String TM_CASE_STATUS_TM_TO_AIO = "03";
	/** TM案件狀態 -不使用代填 */
	public final static String TM_CASE_STATUS_DONT_USE_TM = "04";
	/** TM案件狀態 -不接續斷點 */
	public final static String TM_CASE_STATUS_DONT_USE_BREAKPOINT = "05";
	/** TM案件狀態 -TM案件主管刪除 */
	public final static String TM_CASE_STATUS_NEW_UNIQID_CANCEL = "06";
	/** TM案件狀態 -TM案件系統刪除 */
	public final static String TM_CASE_STATUS_SYSTEM_DELETE = "07";
	/** 案件狀態 -TM案件完成 */
	public final static String TM_CASE_STATUS_COMPLETE = "08";
	/** TM案件狀態 -使用一般件斷點 */
	public final static String TM_CASE_STATUS_USE_AIO_BREAKPOINT = "09";

	/** TM 使用代填資料 */
	public final static String TM_CASE_USE = "0";
	/** TM 不使用代填 */
	public final static String TM_CASE_NON_USE = "1";

	/** 婚姻狀態 - 已婚 */
	public final static String CASE_MARRIAGE_YES = "M";
	/** 婚姻狀態 - 未婚 */
	public final static String CASE_MARRIAGE_NO = "S";

	public static final String KGI_OTP_SUCESS = "0000";

	/** 使用者圖片狀態 - 暫存 */
	public static final String USERPHOTO_STATUS_TEMP = "0";
	/** 使用者圖片狀態 - 使用者處理中(上傳中還未送出) */
	public static final String USERPHOTO_STATUS_USER_PROCESS = "1";
	/** 使用者圖片狀態 - 使用者上傳完成 等待JOB處裡 */
	public static final String USERPHOTO_STATUS_WAIT_UPLOAD = "2";
	/** 使用者圖片狀態 - 圖片上傳成功 */
	public static final String USERPHOTO_STATUS_UPLOAD_SUCCESS = "3";
	/** 2021-05-04 等待JOB處理，UserPhoto 的 Status 須採用 4 因為 2 會被別的排程搶圖片 demand by Ben */
	public static final String USERPHOTO_STATUS_WAIT_UPLOAD_4 = "4";
	/** 使用者圖片狀態 - 圖片上傳失敗 */
	public static final String USERPHOTO_STATUS_UPLOAD_FAIL = "9";
	/** 使用者圖片狀態 - 特殊圖片暫存 */
	public static final String USERPHOTO_STATUS_SPECIAL_TEMP = "10";
	/** 使用者圖片狀態 - 補件使用 - 使用者處理中(上傳中還未送出) */
	public static final String USERPHOTO_STATUS_FROM_ADD = "11";
	/** 使用者圖片狀態 - 補件使用 - 貸款調額狀態 */
	public static final String USERPHOTO_STATUS_LOANAPPLY_MONEY = "12";
	/**
	 * 使用者圖片狀態 - 補件使用 -
	 * 使用者按下補件完成後
	 * 或是 信用卡調額，按下完成後
	 * */
	public static final String USERPHOTO_STATUS_FROM_ADD_SUCCESS = "5";
	/**
	 * 使用者圖片狀態 - 信用卡調額使用
	 * 使用者在財力頁面按下完成後
	 * */
	public static final String USERPHOTO_STATUS_FROM_CREDIT_LIMIT_SUCCESS = "6";
	/** 使用者圖片狀態 - 補件使用 - 資料下載回來還沒完整結束 */

	/** ED3_CLPolicyInfo 凱基人壽通知資料狀態-未檢核 */
	public static final String CL_POLICYINFO_STATUS_NOT_CHECK = "0";
	/** ED3_CLPolicyInfo 凱基人壽通知資料狀態-已檢核 */
	public static final String CL_POLICYINFO_STATUS_CHECKED = "1";

	/** 更新同意書及財力證明 */
	public static final String CL_POLICYINFO_ACTION_AGREEMENT = "1";
	/** 更新保單狀態 */
	public static final String CL_POLICYINFO_ACTION_UPDATE = "2";

	/** 保單承保狀態 - 結案(承保) */
	public static final String CL_POLICYINFO_POLICYSTS_YES = "1";
	/** 保單承保狀態 - 未承保 */
	public static final String CL_POLICYINFO_POLICYSTS_NO = "2";

	/** 凱基人壽代收付設定狀態 - 已處理中 */
	public static final String CL_POLICYINFO_PAYERSTATUS_OK = "0";
	/** 凱基人壽代收付設定狀態 - 處理中 */
	public static final String CL_POLICYINFO_PAYERSTATUS_ONGOING = "1";
	/** 凱基人壽代收付設定狀態 - 設定失敗 */
	public static final String CL_POLICYINFO_PAYERSTATUS_FAILED = "3";
	/** 凱基人壽代收付設定狀態 - 重試失敗 */
	public static final String CL_POLICYINFO_PAYERSTATUS_FAILED_AGAIN = "5";

	/** 凱基人壽徵審AUM設定狀態 - 已處理 */
	public static final String CL_POLICYINFO_AUMSTATUS_OK = "0";
	/** 凱基人壽徵審AUM設定狀態 - 處理中 */
	public static final String CL_POLICYINFO_AUMSTATUS_ONGOING = "1";
	/** 凱基人壽徵審AUM設定狀態 - 不需處理 */
	public static final String CL_POLICYINFO_AUMSTATUS_NO = "2";
	/** 凱基人壽徵審AUM設定狀態 - 設定失敗 */
	public static final String CL_POLICYINFO_AUMSTATUS_FAILED = "3";
	/** 凱基人壽徵審AUM設定狀態 - 重試失敗 */
	public static final String CL_POLICYINFO_AUMSTATUS_FAILED_AGAIN = "5";

	/** 凱基人壽案件通知傳送狀態 - 已處理中 */
	public static final String CL_CASESTS_CLSTATUS_OK = "0";
	/** 凱基人壽案件通知傳送狀態 - 處理中 */
	public static final String CL_CASESTS_CLSTATUS_ONGOING = "1";
	/** 凱基人壽案件通知傳送狀態 - 設定失敗 */
	public static final String CL_CASESTS_CLSTATUS_FAILED = "3";
	/** 凱基人壽案件通知傳送狀態 - 重試失敗 */
	public static final String CL_CASESTS_CLSTATUS_FAILED_AGAIN = "5";

	/** 凱基人壽產品狀態 - 成功 */
	public static final String CL_PDCTSTS_OK = "00";
	/** 凱基人壽產品狀態 - 保單不承保 */
	public static final String CL_PDCTSTS_NOT_APPLY = "10";
	/** 凱基人壽產品狀態 - 行動投保同意書逾時 */
	public static final String CL_PDCTSTS_EXPIRED = "11";
	/** 凱基人壽產品狀態 - QRCode逾15天 */
	public static final String CL_PDCTSTS_QR_EXPIRED = "20";
	/** 凱基人壽產品狀態 - KGI網頁申辦逾30天 */
	public static final String CL_PDCTSTS_WEB_EXPIRED = "21";
	/** 凱基人壽產品狀態 - KGI申辦失敗 */
	public static final String CL_PDCTSTS_APPLY_FAILED = "22";
	/** 凱基人壽產品狀態 - 續扣申辦逾120天 */
	public static final String CL_PDCTSTS_PAYER_EXPIRED = "23";
	/** 凱基人壽產品狀態 - 續扣約定檔設定失敗逾時 */
	public static final String CL_PDCTSTS_PAYER_FAILED = "24";

	/** VerifyLog 驗證別-中華電信 */
	public static final String VERIFYLOG_TYPE_CHT = "Cht";
	/** VerifyLog 驗證別-信用卡信用驗證 */
	public static final String VERIFYLOG_TYPE_NCCC = "NCCC";
	/** VerifyLog 驗證別-銀行信用驗證 */
	public static final String VERIFYLOG_TYPE_PCODE = "PCode2566";

	/**
	 * 注意數位信貸 CreditVerify.VerifyType 值是
	 * 0:信用卡 1:存款帳戶 2:行動門號 4:線下驗身
	 */
	/** 驗證別-信用卡信用驗證 */
	public static final String VERIFY_TYPE_NCCC = "1";
	/** 驗證別-銀行信用驗證 */
	public static final String VERIFY_TYPE_PCODE = "2";
	/** 驗證別-OTP驗證 */
	public static final String VERIFY_TYPE_OTP = "3";
	/** 驗證別-證券憑證 */
	public static final String VERIFY_TYPE_KS = "4";

	/** ed3IdentityVerification 驗證別-預約分行驗證 */
	public static final String VERIFY_TYPE_BOOKBRANCH = "3";

	/** IdentityVerification 驗證成功 */
	public static final String VERIFY_STATUS_SUCCESS = "1";
	/** IdentityVerification 驗證失敗 */
	public static final String VERIFY_STATUS_FAIL = "0";

	/** 本行帳戶驗身-實體帳戶 */
	public static final String VERIFY_TYPE_ACCT_ENTITY = "0";
	/** 本行帳戶驗身-數一帳戶 */
	public static final String VERIFY_TYPE_ACCT_D1 = "1";

	/** ED3_CaseData Process預約分行流程 */
	public static final String CASEDATA_PROCESS_BOOKBRANCH = "1";
	/** ED3_CaseData Process 數位帳戶流程 */
	public static final String CASEDATA_PROCESS_DIGIT = "2";
	/** ED3_CaseData Process 薪轉流程 */
	public static final String CASEDATA_PROCESS_SALARY = "3";

	/** ED3_CaseData ProductId 新戶選純帳戶 */
	public static final String CASEDATA_PRODUCTID_ACCOUNT = "1";
	/** ED3_CaseData ProductId 新戶選帳加卡 */
	public static final String CASEDATA_PRODUCTID_ACCOUNT_AND_CREDIT = "2";

	/** ED3_EX_CreditCaseData UserType 信用卡-使用者類型-新戶 */
	public static final String CREDITCASEDATA_USERTYPE_NEW = "0";
	/** ED3_EX_CreditCaseData UserType 信用卡-使用者類型-信用卡戶 */
	public static final String CREDITCASEDATA_USERTYPE_CREDIT = "1";
	/** ED3_EX_CreditCaseData UserType 信用卡-使用者類型-存款戶 */
	public static final String CREDITCASEDATA_USERTYPE_ACCOUNT = "2";
	/** ED3_EX_CreditCaseData UserType 信用卡-使用者類型-純貸款戶 */
	public static final String CREDITCASEDATA_USERTYPE_LOAN = "3";
	/** ED3_EX_CreditCaseData UserType 信用卡-使用者類型-其他 */
	public static final String CREDITCASEDATA_USERTYPE_OTHER = "4";

	/** ED3_CaseData UserType -使用者類型-新戶 */
	public static final String CASEDATA_USERTYPE_NEW = "0";
	/** ED3_CaseData UserType 信用卡-使用者類型-信用卡戶 */
	public static final String CASEDATA_USERTYPE_CREDIT = "1";
	/** ED3_CaseData UserType 信用卡-使用者類型-存款戶 */
	public static final String CASEDATA_USERTYPE_ACCOUNT = "2";
	/** ED3_CaseData UserType 信用卡-使用者類型-純貸款戶 */
	public static final String CASEDATA_USERTYPE_LOAN = "3";
	/** ED3_CaseData UserType 信用卡-使用者類型-其他 */
	public static final String CASEDATA_USERTYPE_OTHER = "4";
	/** ED3_CaseData UserType 信用卡-使用者類型-卡戶+存戶 */
	public static final String CASEDATA_USERTYPE_CCA = "5";

	/** ED3_CaseData IsPayer 凱基人壽案是否設定續期扣繳 */
	public static final String CASEDATA_ISPAYER_YES = "1";
	/** ED3_CaseData IsPayer 凱基人壽案是否設定續期扣繳 */
	public static final String CASEDATA_ISPAYER_NO = "0";

	/** ApplyItem 附加服務-同意 */
	public static final String APPLYITEM_COMMENT_Y = "Y";
	/** ApplyItem 附加服務-不同意 */
	public static final String APPLYITEM_COMMENT_N = "N";
	/** ApplyItem 附加服務-中華電信 */
	public static final String APPLYITEM_CHT = "1";
	/** ApplyItem 附加服務-信用卡 */
	public static final String APPLYITEM_CREDIT = "2";
	/** ApplyItem 附加服務-約定帳戶 */
	public static final String APPLYITEM_DEALACCOUNT = "3";
	/** ApplyItem 附加服務-電費 */
	public static final String APPLYITEM_ELECTRICTY = "4";
	/** ApplyItem 附加服務-瓦斯費 */
	public static final String APPLYITEM_GASFEE = "5";
	/** ApplyItem 附加服務-台北水費 */
	public static final String APPLYITEM_TAIPEIWATERFEE = "6";
	/** ApplyItem 附加服務-台灣水費 */
	public static final String APPLYITEM_TAIWANWATERFEE = "7";
	/** ApplyItem 附加服務-房貸 */
	public static final String APPLYITEM_HOUSELOAN = "8";
	/** ApplyItem 附加服務-信貸 */
	public static final String APPLYITEM_AIRLOAN_CONTACTME = "9";
	/** ApplyItem 附加服務-舊戶本身已有辦過網路銀行 */
	public static final String APPLYITEM_OLD_APPLYED_WEBBANK = "11";
	/** ApplyItem 附加服務-舊戶純註記是否申辦網路銀行 */
	public static final String APPLYITEM_OLD_NOTED_WEBBANK = "12";
	/** ApplyItem 附加服務-新戶辦網路銀行 */
	public static final String APPLYITEM_NEW_WEBBANK = "13";
	/** ApplyItem 附加服務-跳過信用卡驗身 */
	public static final String APPLYITEM_CREDIT_DONOT_VERIFY = "14";
	/** ApplyItem 附加服務-舊戶本身已有辦過電話銀行 */
	public static final String APPLYITEM_OLD_APPLYED_IVRBANK = "16";
	/** ApplyItem 附加服務-新戶辦電話銀行 */
	public static final String APPLYITEM_NEW_IVRBANK = "18";
	/** ApplyItem 附加服務-下次再驗 */
	public static final String APPLYITEM_NEXTTIME = "19";
	/** ApplyItem 附加服務-MGM同意條款 */
	public static final String APPLYITEM_MGM = "21";
	/** ApplyItem 附加服務-可申辦開心戶 */
	public static final String APPLYITEM_DACH = "22";
	/** ApplyItem 附加服務-臨櫃出示 */
	public static final String APPLYITEM_SITE_SHOW = "23";
	/** ApplyItem 附加服務-證卷交割戶開立狀態 */
	public static final String APPLYITEM_SECURITIES_DELIVERY = "24";
	/** ApplyItem 附加服務-既有戶申辦薪轉新申辦帳戶 */
	public static final String APPLYITEM_SAL_ADD_NEW_ACCOUNT = "25";
	/** ApplyItem 附加服務-帳戶驗身90天手機異動 */
	public static final String APPLYITEM_MODIFY_PHONE_90D = "26";
	/** ApplyItem 高風險職業註記 */
	public static final String APPLYITEM_HIGH_RISK_MARK = "27";
	/** ApplyItem 存款補件完成註記 */
	public static final String APPLYITEM_ADDITIONAL_SUCCESS = "28";


	// ApplyItem Title
	public static final String CHT = "APPLYITEM_CHT";
	public static final String GASFEE = "APPLYITEM_GASFEE";
	public static final String TAIPEIWATERFEE = "APPLYITEM_TAIPEIWATERFEE";
	public static final String TAIWANWATERFEE = "APPLYITEM_TAIWANWATERFEE";
	public static final String ELECTRICTY = "APPLYITEM_ELECTRICTY";
	public static final String DEALACCOUNT = "APPLYITEM_DEALACCOUNT";
	public static final String SAL_ADD_NEW_ACCOUNT = "APPLYITEM_SAL_ADD_NEW_ACCOUNT";

	/**
	 * apilog type
	 */
	public static final String APILOG_TYPE_CALLOUT = "1";
	public static final String APILOG_TYPE_CALLIN = "2";
	public static final String APILOG_TYPE_JOBLOG = "3";

	// 以前是6mb 現在改為 8mb r2-3141 再改成10MB
	public static final int IMAGE_SIZE_LIMIT = 10 * 1024 * 1366; // 上傳圖片大小限制 (base64 會大一點 1024/3*4 ==> 1366)

	/** ED3_UserPhoto */
	public static final String USERPHOTO_SUBTYPE_IDCARDFRONT = "1"; // 身分證正面
	public static final String USERPHOTO_SUBTYPE_IDCARDBANK = "2"; // 身分正反面
	public static final String USERPHOTO_SUBTYPE_HEALTHIDCARD = "3"; // 健保卡/駕照

	/** AioBreakpoint */
	public static final String PROCESS_STATUS = "process"; // 處理中
	public static final String SUCCESS_STATUS = "success"; // 成功
	public static final String ERROR_STATUS = "fail"; // 失敗

	public static final String API_SEND_OTP = "sendOTP";
	public static final String API_CHECK_OTP = "checkOTP";
	public static final String API_KGI_PCODE2566 = "kgiPcode2566";
	public static final String API_OTHER_PCDOE2566 = "otherPCode2566";
	public static final String API_KGI_NCCC = "kgiNCCC";
	public static final String API_KGI_NCCME41 = "kgiNCCCME4100";
	public static final String API_OTHER_NCCC = "otherNCCC";

	public static final String API_KGI_NCCME4720 = "NCCME472000Q";

	/** API 回覆 下次再驗：無 */
	public static final String API_NEXTTIME_NO = "0";
	/** API 回覆 下次再驗：有 */
	public static final String API_NEXTTIME_YES = "1";

	/**
	 * jwt有效時間 5天(432_000_000) -> 15mins(90000)
	 */
	public static final long EXPIRATIONTIME = 432_000_000;
	/**
	 * JWT密碼
	 */
	// public static final byte[] key = "kgied3fuco201910".getBytes();
	public static final byte[] key = "kgiaioflow202111".getBytes();
	/**
	 * Token前缀
	 */
	public static final String TOKEN_PREFIX = "Bearer";
	/**
	 * 存放Token的Header Key
	 */
	public static final String HEADER_STRING = "Authorization";

	/** 開戶狀態 - 成功 */
	public static final String AORESULT_STATUS_SUCCESS = "0";
	/** 開戶狀態 - 退件 */
	public static final String AORESULT_STATUS_REJECT = "1";
	/** 開戶狀態 - 補件 */
	public static final String AORESULT_STATUS_WAIT_UPLOAD = "2";
	/** 開戶狀態 - 未開臨櫃到期 */
	public static final String AORESULT_STATUS_WAIT_CANCEL = "4";
	public static final String OPENFAILMSG = "5L6G6KGM6YC+5pmC";

	/** 數位存款退件用 (手機90天異動)*/
	public static final String AORESULT_OBD_REJECT = "5"; //OBD自行退件(手機異動90天)
	public static final String AORESULT_OBD_REJECT_MSG = "OBD自動退件";


	/** 台幣帳戶結果狀態碼 - 成功 */
	public static final String AORESULT_STATUSTW_SUCCESS = "0";

	/** 是否為本行卡 */
	public static final String VERIFY_CARD_KGI = "Y";
	/** 是否為本行卡 */
	public static final String VERIFY_CARD_OTHER = "N";

	/** 拒絕申辦原因 - 舊戶 */
	public static final String REJECTLOG_COED_OLDUSER = "0";
	/** 拒絕申辦原因 - 兩次未接續斷點 */
	public static final String REJECTLOG_COED_SECONDAPPLYNOFINISH = "1";
	/** 拒絕申辦原因 - 手機申辦成功 */
	public static final String REJECTLOG_COED_PHONEAPPLYED = "2";
	/** 拒絕申辦原因 - 電子信箱申辦成功 */
	public static final String REJECTLOG_COED_EMAILAPPLYED = "3";
	/** 拒絕申辦原因 - 事故戶 */
	public static final String REJECTLOG_COED_INCIDENT = "4";
	/** 拒絕申辦原因 - 數二帳戶 */
	public static final String REJECTLOG_COED_OLDUSER_D2 = "5";
	/** 拒絕申辦原因 - 告誡戶 */
	public static final String REJECTLOG_COED_37 = "37";

	/** D3 排程狀態 */
	public static final String D3_SCHEDULE_STATUS = "0";

	/** MailHistory SMTP 單筆寄 */
	public static final String MAILHISTORY_TYPE_SMTP = "2";
	/** MailHistory JOB */
	public static final String MAILHISTORY_TYPE_JOB = "3";
	/** MailHistory 簡訊 */
	public static final String MAILHISTORY_TYPE_SM = "4";
	/** MailHistory 目前只有數三使用，全部一起寄 */
	public static final String MAILHISTORY_TYPE_SMTP_ALL = "5";

	/** 凱基人壽 CaseStsRq 成功碼 */
	public static final String CL_CASESTSRQ_API_SUCCESS = "200";
	/** 凱基人壽 GetCif 成功碼 */
	public static final String CL_GETCIF_API_SUCCESS = "200";
	/** 凱基人壽 GetCif token與ID比對 不符 */
	public static final String CL_GETCIF_API_ID_INCORRECT = "998";
	public static final String CL_GETCIF_API_SYSTEM_TIMEOUT = "501";
	public static final String CL_GETCIF_API_SYSTEM_ERROR = "502";
	public static final String CL_GETCIF_API_ESB_ERROR = "503";
	public static final String CL_GETCIF_API_TOKEN_EXPIRED = "999";

	/** 凱基人壽 GetCif 的 QryType */
	public static final String CL_GETCIF_QRYTYPE = "cif";

	/** 凱基人壽 GetCif 沒有加辦信用卡(FinInd) */
	public static final String CL_GETCIF_FININD_NO = "N";
	/** 凱基人壽 GetCif 有加辦信用卡(FinInd) */
	public static final String CL_GETCIF_FININD_YES = "Y";

	/** 凱基人壽申辦項目:數位帳戶 */
	public static final String CL_PDCTTYPE_ACCT = "1";

	/** 凱基人壽 API 信用卡效期無日期時的值 */
	public static final String CL_CASESTS_API_CREDIT_DATE = "00000000";

	/** 凱基人壽 GetCif 未婚 */
	public static final String CL_GETCIF_MARRIAGE_NO = "1";
	/** 凱基人壽 GetCif 已婚 */
	public static final String CL_GETCIF_MARRIAGE_YES = "2";

	/** 凱基人壽 GetCif token過期 */
	public static final String TOKEN_EXPIRED = "Y";
	/** 凱基人壽 GetCif token未過期 */
	public static final String TOKEN_NOT_EXPIRE = "N";

	/** KGI異常等級3 */
	public static final String ESB_REASON_CODE_08 = "08";
	/***/
	public static final String ESB_INCIDENT_TYPE_0206 = "0206";
	/** 人頭戶 */
	public static final String ESB_INCIDENT_TYPE_0200 = "0200";
	/** 設定事故戶 */
	public static final String ESB_RM_J = "J";
	/** 更正事故戶 */
	public static final String ESB_RM_C = "C";

	/** 代收付平台 委繳戶單筆授權資料維護API－成功 */
	public static final String PAYER_PAYER_STATUS_SUCCESS = "0";

	/** 代收付平台 資料庫－成功 */
	public static final String PAYER_DB_STATUS_SUCCESS = "0";
	public static final String PAYER_DB_STATUS_SUCCESS2 = "00";
	public static final String PAYER_DB_STATUS_SUCCESS3 = "80";
	public static final String ISPAYER_STATUSCODE_SUCCESS = "0";
	public static final String ISPAYER_STATUSDESC_HAS_ERROR = "資料異常";
	public static final int JOBLOG_STATUS_INFO = 1; // Info
	public static final int JOBLOG_STATUS_DATA_INCORRECT = 2; // 資料異常
	public static final int JOBLOG_STATUS_DISCONNECT = 3; // 連線中斷
	public static final int JOBLOG_STATUS_SYSTEM_ERR = 4; // 系統錯誤

	public static final String CASE_ISPAYER_YES = "1";
	public static final String CASE_ISPAYER_NO = "0";
	public static final String SEND_XML_FAILED = "9999";
	public static final String SEND_XML_SUCCESS = "0000";
	public static final String ED3_COMMON_PRODUCT_ID_ED3 = "ACCT";

	/** CSCommon的MEMO紀錄位置 */
	public static final String MEMO_CSCOMMON_MEMO = "cscommon.memo";
	/** CSCommon的CIF紀錄位置 */
	public static final String MEMO_CSCOMMON_CIF = "cscommon.cif";

	/********************** 數三 **********************/
	/** 凱證Channel Id */
	public static final String CHANNEL_ID_KS = "KS";

	/** 雙幣卡-凱證Channel Id*/
	public static final String CHANNEL_ID_KSD = "KSD";

	/**活動MAChannel Id*/
	public static final String CHANNEL_ID_MA = "MA";

	/** 帳單寄送方式 - 電子帳單 */
	public static final String CASE_BILLTYPE_E = "2";

	/** 開戶目的 - 證券 */
	public static final String CASE_PURPOSE_SECURITIES = "02";

	/********************** 信用卡 **********************/
	/** 案件狀態 -斷點取消 */
	public final static String CASE_STATUS_CANCEL = "07";

	/** 案件狀態 -查無申請書無法送件 */
	public final static String CASE_STATUS_SUBMIT_ERROR = "19";

	/** 白名單種類 -信用卡 */
	public final static String WhiteList_CREDIT = "0";
	/** 白名單種類 -OCR辨識  */
	public final static String WhiteList_OCR = "5";

	/** 案件狀態 -拒貸 */
	public final static String CASE_STATUS_REJECT = "08";

	/** 拒貸狀態 Jobreject*/
	public final static String STATUS_JOB_REJECT_TODAY = "010"; // 當日起案
	public final static String STATUS_JOB_REJECT_NEXTDAY = "020"; // 隔日起案

	// TODO 上線前記得拿掉
	public final static boolean isTEST = true;

	public static final String ADDPHOTO_STYPE_SALARY = "20";// 薪資轉帳證明
	public static final String ADDPHOTO_STYPE_BANKSAVE = "22";// 存款證明
	public static final String ADDPHOTO_STYPE_PAYMENT = "23";// 扣繳憑單
	public static final String ADDPHOTO_STYPE_INCOMELIST = "24";// 所得清單
	public static final String ADDPHOTO_STYPE_SECURITYSTOCK = "25";// 證券庫存
	public static final String ADDPHOTO_STYPE_OTHER = "98";
	public static final String ADDPHOTO_STYPE_OTHER2 = "99";// 不知道為什麼會有兩個OTHER 但ADDPHOTODAO>parseDest有存，就先留。

	/**
	 * 申辦產品
	 */
	/** 信用卡 */
	public static final String ADDPHOTO_PROD_TYPE_CREDIT = "1";
	/** 貸款 */
	public static final String ADDPHOTO_PROD_TYPE_LOAN = "2";

	public static final String TYPE_IDCARD_IDC = "0"; // 一般身分證(舊身分證正面)
	public static final String TYPE_IDCARD_EID = "1"; // 新身分證正面

	public static final String NOT_APPLY_PRODUCT = "X"; // 未申辦項目

	/** APIM 回應代碼 成功 */
	public static final String APIM_CONNECT_SUCCESS = "200";
	/** APIM 回應代碼 失敗 */
	public static final String APIM_CONNECT_ERROR = "502";

	/** 作為讓外部使用者呼叫 apim 戶役政 的識別 */
	public final static String ExternalCallPostMan = "ExternalCallPostMan";

	/** 預設 ChannelId=KB */
	public final static String KB_9743_CHANNEL_ID = "KB";

	/** 預設 ChannelId=GB */
	public final static String GB_CHANNEL_ID = "GB";

	/** 預設 ChannelId=GS */
	public final static String GS_CHANNEL_ID = "GS";

	/** 預設 ChannelId=GC */
	public final static String GC_CHANNEL_ID = "GC";

	/** 預設 DepartId=9743 */
	public final static String KB_9743_DEPART_ID = "9743";
	/** 綜拓 */
	public final static String KB_9735_DEPART_ID = "9735";
	/** AO 或 TM 單位 */
	public final static String KB_8882_DEPART_ID = "8882";
	public final static String KB_8220_DEPART_ID = "8220";
	public final static String KB_8230_DEPART_ID = "8230";
	public final static String KB_8240_DEPART_ID = "8240";
	public final static String KB_8250_DEPART_ID = "8250";
	public final static String KB_8260_DEPART_ID = "8260";
	public final static String KB_8270_DEPART_ID = "8270";
	public final static String KB_8280_DEPART_ID = "8280";
	public static final String QLOG = "QLOG";

	/** 客服聯絡 uniqId 的 prefix */
	public static final String CTAC = "CTAC";

	/** 申請流程 uniqId 的 prefix */
	public static final String LOAN = "LOAN";

	/**
	 * #5901 補件寄信
	 */
	/** TM_MEMBER=000000 */
	public final static String ADDITION_MEMBER_EMAIL_ERROR = "此員編查無Email";

	/** TM_MEMBER=000000 */
	public final static String TM_MEMBER_000000 = "000000";
	/** TM_MEMBER=888888 */
	public final static String TM_MEMBER_888888 = "888888";
	/** 已補件 */
	public final static String ADDITION_MAIL_TYPE_ADD = "1";
	public final static String ADDITION_MAIL_TYPE_ADD_TITLE = "已補件";
	public final static String ADDITION_MAIL_TYPE_ADD_CONTENT = "資料補回通知";
	/** 申請完成 */
	public final static String ADDITION_MAIL_TYPE_APPLY = "2";
	public final static String ADDITION_MAIL_TYPE_APPLY_TITLE = "已完成";
	public final static String ADDITION_MAIL_TYPE_APPLY_CONTENT = "線上申請完成";

	public static final String LOAN0000000000000 = "LOAN0000000000000";

	/** [信貸]申請流程 - 額度體驗題目編號 - EXPyyyyMMdd12345 */
	public static final String EXP = "EXP";

	public static final String EDDA = "EDDA";

	public static final String API_HTML2PDF = "api/v1/htmlToPdf";

	/** 婚姻狀態 - 未婚 single */
	public static final String CIF_MARRIAGE_NO = "S";
	/** 婚姻狀態 - 已婚 married */
	public final static String CIF_MARRIAGE_YES = "M";

	/** 未婚 single */
	public static final String CASEDATA_marriage_single = "2";
	/** 已婚 married */
	public static final String CASEDATA_marriage_married = "1";

	// TABLE: CaseDocument
	// 01:體驗 02:申請 03:立約 04:專人聯絡 05:信用卡
	public final static String UNIQ_TYPE_01 = "01"; // 01:體驗
	public final static String UNIQ_TYPE_02 = "02"; // 02:貸款
	public final static String UNIQ_TYPE_03 = "03"; // 03:立約
	public final static String UNIQ_TYPE_04 = "04"; // 04:專人聯絡
	public final static String UNIQ_TYPE_05 = "05"; // 05:信用卡
	public final static String UNIQ_TYPE_06 = "06"; // 05:AIO
	public final static String UNIQ_TYPE_07 = "07"; // 07:開心戶
	public final static String UNIQ_TYPE_08 = "08"; // 08:房貸

	// TABLE: CaseDocument
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF   = "loan.applypdf"; // 貸款PDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF_FINALE   = "loan.applypdf.finale"; // 貸款PDF
	public final static String DOCUMENT_TYPE_LOAN_REJECTPDF  = "loan.rejectpdf"; // 拒貸PDF
	public final static String DOCUMENT_TYPE_LOAN_KYC_PDF    = "loan.kyc.pdf"; // KYC表PDF
	public final static String DOCUMENT_TYPE_AIO_KYC_PDF    = "aio.kyc.pdf"; // AIO KYC表PDF
	public final static String DOCUMENT_TYPE_AIO_APPLYPDF   = "aio.applypdf"; // AIOPDF
	public final static String DOCUMENT_TYPE_AIO_APPLYPDF_FINALE   = "aio.applypdf.finale"; // AIOPDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF_HL   = "hl.applypdf"; // 借款人PDF
	public final static String DOCUMENT_TYPE_LOAN_KYC_PDF_HL    = "hl.kyc.pdf"; // 房屋貸款KYC表PDF
	public final static String DOCUMENT_TYPE_CC_KYC_PDF    = "cc.kyc.pdf"; // 信用卡KYC表PDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF_HL_FINALE   = "hl.applypdf.finale"; // 房屋貸款finale PDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF_HL_ASSURER  = "hl.assurer.applypdf"; // 保證人PDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYPDF_HL_RELATION  = "hl.relation"; // 借款人關係人PDF
	public final static String DOCUMENT_TYPE_LOAN_APPLYJPG_HL_RELATION  = "hl.applyjpg.relation"; // 借款人關係人jpg
	// 信用卡系統使用
	public final static String DOCUMENT_TYPE_CREDIT_APPLYPDF = "credit.applypdf"; // 信用卡PDF
	public final static String DOCUMENT_TYPE_CREDIT_APPLYPDF_FINALE = "credit.applypdf.finale"; // 信用卡PDF
	public final static String DOCUMENT_TYPE_CONTRACT_PDF = "contract.pdf"; // 立約PDF
	public final static String DOCUMENT_TYPE_CONTRACT_ACH_PDF = "contract.ach.pdf"; // 立約PDF
	public final static String DOCUMENT_TYPE_DACH_EDDA_APPLY_PDF = "dach.edda.apply.pdf"; // 開心戶

	/** OTP驗證 */
	public final static String CASEAUTH_AUTH_TYPE_OTP = "1";
	/** MBC驗證 */
	public final static String CASEAUTH_AUTH_TYPE_MBC = "2";
	/** 證券異業 */
	public final static String CASEAUTH_AUTH_TYPE_SECURITY = "3";
	/** 新戶其他 */
	public final static String CASEAUTH_AUTH_TYPE_NCCC_OR_PC2566 = "4";

	public static final String CREDITVERIFY_TYPE_9 = "9"; // 既有戶驗身

	public static final String CREDITVERIFY_9 = "OTP"; // 既有戶驗身

	/** 預約分行流程 */
	public final static String CASEDATA_PROCESS_REV_BRANCH = "1";
	/** 數位帳戶流程 */
	public final static String CASEDATA_PROCESS_ED3 = "2";

	/** 是否預核 - 1:預核 */
	public final static String IsPreAudit_1 = "1";
	/** 是否預核 - 2:非預核 */
	public final static String IsPreAudit_2 = "2";

	public enum CaseDataSubStatus {

		/** 客戶初進入，尚未OTP */
		CASE_STATUS_00_SubStatus_000("00 000"),
		/** 客戶初進入，已OTP */
		CASE_STATUS_00_SubStatus_010("00 010"),
		/** 客戶資料填寫中，尚未驗身 */
		CASE_STATUS_01_SubStatus_000("01 000"),
		/** **客戶資料填寫中，已驗身 */
		CASE_STATUS_01_SubStatus_010("01 010"),
		/** NEW_CASE_CHK 成功 */
		CASE_STATUS_01_SubStatus_020("01 020"),
		/** GET_AML_DATA 成功 */
		CASE_STATUS_01_SubStatus_030("01 030"),
		/** CHECK_NO_FINPF 成功 */
		CASE_STATUS_01_SubStatus_040("01 040"),
		/** CHECK_HAVE_FINPF2 成功 */
		CASE_STATUS_01_SubStatus_050("01 050"),
		/** CHECK_HAVE_FINPF3 成功 */
		CASE_STATUS_01_SubStatus_060("01 060"),
		/** CREATE_NEW_CASE 已呼叫新增案件(STP)成功 */
		CASE_STATUS_01_SubStatus_070("01 070"),
		/** CREATE_NEW_CASE 已呼叫新增案件(STP)fail */
		CASE_STATUS_01_SubStatus_071("01 071"),
		/** 完成新增案件，含以上所有電文。 */
		CASE_STATUS_01_SubStatus_090("01 090"),
		/** **產生申請書HTML(previewCasePDFHtml)**成功 */
		CASE_STATUS_01_SubStatus_100("01 100"),
		/** **產生申請書HTML(previewCasePDFHtml)**失敗 */
		CASE_STATUS_01_SubStatus_101("01 101"),

		/** 確認上傳身份證正反面 */
		CASE_STATUS_01_SubStatus_400("01 400"),
		/** 戶役政查詢成功 */
		CASE_STATUS_01_SubStatus_410("01 410"),
		/** 戶役政查詢失敗 */
		CASE_STATUS_01_SubStatus_411("01 411"),

		/** 風險評級成功 */
		CASE_STATUS_01_SubStatus_420("01 420"),
		/** 風險評級失敗 */
		CASE_STATUS_01_SubStatus_421("01 421"),

		/** 呼叫Z07查詢成功 */
		CASE_STATUS_01_SubStatus_430("01 430"),
		/** 呼叫Z07查詢失敗 */
		CASE_STATUS_01_SubStatus_431("01 431"),
		/** 呼叫外部負債資料查詢成功 */
		CASE_STATUS_01_SubStatus_440("01 440"),
		/** 呼叫外部負債資料查詢失敗 */
		CASE_STATUS_01_SubStatus_441("01 441"),
		/** 產生KYC表成功 */
		CASE_STATUS_01_SubStatus_450("01 450"),
		/** 產生KYC表失敗 */
		CASE_STATUS_01_SubStatus_451("01 451"),

		/** 確認上傳財力證明 */
		CASE_STATUS_01_SubStatus_600("01 600"),
		/** 確認上傳同一關係人表 */
		CASE_STATUS_01_SubStatus_700("01 700"),
		/** 客戶資料上傳完成 */
		CASE_STATUS_01_SubStatus_800("01 800"),

		/** UPDATE_CASE_INFO 發查聯徵中 */
		CASE_STATUS_01_SubStatus_900("01 900"),
		/** UPDATE_CASE_INFO 發查聯徵成功 */
		CASE_STATUS_01_SubStatus_910("01 910"),
		/** UPDATE_CASE_INFO 發查聯徵失敗 */
		CASE_STATUS_01_SubStatus_911("01 911"),

		/** 卡加貸信用卡已送件 */
		CASE_STATUS_03_SubStatus_000("03 000"),

		/** 紙本送出 */
		CASE_STATUS_04_SubStatus_000("04 000"),

		/** 送件完成 */
		CASE_STATUS_12_SubStatus_000("12 000"),
		/** GET_CASE_COMPLETE 後端整件狀態已完整 */
		CASE_STATUS_12_SubStatus_100("12 100"),
		/** GET_CASE_COMPLETE 後端整件狀態已完整，並呼叫APS啟動徵審處理完成 */
		CASE_STATUS_12_SubStatus_200("12 200"),

		/** APSStatus狀態已有資料 */
		CASE_STATUS_22_SubStatus_000("22 000"),
		/** 30天到期取消 */
		CASE_STATUS_06_SubStatus_000("06 000"),
		/** 案件取消 */
		CASE_STATUS_07_SubStatus_000("07 000"),
		/** 系統拒貸，申請書尚未上傳 */
		CASE_STATUS_09_SubStatus_000("09 000"),
		/** 系統拒貸，申請書已上傳影像系統 */
		CASE_STATUS_09_SubStatus_010("09 010");
		// CASE_STATUS_00_SubStatus_000("00 000"),
		// /** 客戶初進入，已OTP */
		// CASE_STATUS_00_SubStatus_010("00 010"),
		// /** 客戶資料填寫中，尚未驗身 */
		// CASE_STATUS_01_SubStatus_000("01 000"),
		// /** **客戶資料填寫中，已驗身 */
		// CASE_STATUS_01_SubStatus_010("01 010"),
		// /** NEW_CASE_CHK 成功 */
		// CASE_STATUS_01_SubStatus_020("01 020"),
		// /** GET_AML_DATA 成功 */
		// CASE_STATUS_01_SubStatus_030("01 030"),
		// /** CHECK_NO_FINPF 成功 */
		// CASE_STATUS_01_SubStatus_040("01 040"),
		// /** CHECK_HAVE_FINPF2 成功 */
		// CASE_STATUS_01_SubStatus_050("01 050"),
		// /** CHECK_HAVE_FINPF3 成功 */
		// CASE_STATUS_01_SubStatus_060("01 060"),
		// /** CREATE_NEW_CASE 已呼叫新增案件(STP)成功 */
		// CASE_STATUS_01_SubStatus_070("01 070"),
		// /** CREATE_NEW_CASE 已呼叫新增案件(STP)fail */
		// CASE_STATUS_01_SubStatus_071("01 071"),
		// /** 完成新增案件，含以上所有電文。 */
		// CASE_STATUS_01_SubStatus_090("01 090"),
		// /** **產生申請書HTML(previewCasePDFHtml)**成功 */
		// CASE_STATUS_01_SubStatus_100("01 100"),
		// /** **產生申請書HTML(previewCasePDFHtml)**失敗 */
		// CASE_STATUS_01_SubStatus_101("01 101"),
		//
		// /** 確認上傳身份證正反面 */
		// CASE_STATUS_01_SubStatus_400("01 400"),
		// /** 戶役政查詢成功 */
		// CASE_STATUS_01_SubStatus_410("01 410"),
		// /** 戶役政查詢失敗 */
		// CASE_STATUS_01_SubStatus_411("01 411"),
		//
		// /** 風險評級成功 */
		// CASE_STATUS_01_SubStatus_420("01 420"),
		// /** 風險評級失敗 */
		// CASE_STATUS_01_SubStatus_421("01 421"),
		//
		// /** 呼叫Z07查詢成功 */
		// CASE_STATUS_01_SubStatus_430("01 430"),
		// /** 呼叫Z07查詢失敗 */
		// CASE_STATUS_01_SubStatus_431("01 431"),
		// /** 呼叫外部負債資料查詢成功 */
		// CASE_STATUS_01_SubStatus_440("01 440"),
		// /** 呼叫外部負債資料查詢失敗 */
		// CASE_STATUS_01_SubStatus_441("01 441"),
		// /** 產生KYC表成功 */
		// CASE_STATUS_01_SubStatus_450("01 450"),
		// /** 產生KYC表失敗 */
		// CASE_STATUS_01_SubStatus_451("01 451"),
		//
		// /** 確認上傳財力證明 */
		// CASE_STATUS_01_SubStatus_600("01 600"),
		// /** 確認上傳同一關係人表 */
		// CASE_STATUS_01_SubStatus_700("01 700"),
		// /** 客戶資料上傳完成 */
		// CASE_STATUS_01_SubStatus_800("01 800"),
		//
		// /** UPDATE_CASE_INFO 發查聯徵中 */
		// CASE_STATUS_01_SubStatus_900("01 900"),
		// /** UPDATE_CASE_INFO 發查聯徵成功 */
		// CASE_STATUS_01_SubStatus_910("01 910"),
		// /** UPDATE_CASE_INFO 發查聯徵失敗 */
		// CASE_STATUS_01_SubStatus_911("01 911"),
		//
		// /** 卡加貸信用卡已送件 */
		// CASE_STATUS_03_SubStatus_000("03 000"),
		//
		// /** 紙本送出 */
		// CASE_STATUS_04_SubStatus_000("04 000"),
		//
		// /** 送件完成 */
		// CASE_STATUS_12_SubStatus_000("12 000"),
		// /** GET_CASE_COMPLETE 後端整件狀態已完整 */
		// CASE_STATUS_12_SubStatus_100("12 100"),
		// /** GET_CASE_COMPLETE 後端整件狀態已完整，並呼叫APS啟動徵審處理完成 */
		// CASE_STATUS_12_SubStatus_200("12 200");
		//
		// /** APSStatus狀態已有資料 */
		// CASE_STATUS_22_SubStatus_000("22 000"),
		// /** 30天到期取消 */
		// CASE_STATUS_06_SubStatus_000("06 000"),
		// /** 案件取消 */
		// CASE_STATUS_07_SubStatus_000("07 000"),
		// /** 系統拒貸，申請書尚未上傳 */
		// CASE_STATUS_09_SubStatus_000("09 000"),
		// /** 系統拒貸，申請書已上傳影像系統 */
		// CASE_STATUS_09_SubStatus_010("09 010"),
		//
		// /** 客戶初進入，尚未OTP */
		// CASE_STATUS_00_SubStatus_000("00 000"),
		// /** 客戶初進入，已OTP */
		// CASE_STATUS_00_SubStatus_010("00 010"),
		// /** 客戶資料填寫中，尚未驗身 */
		// CASE_STATUS_01_SubStatus_000("01 000"),
		// /** **客戶資料填寫中，已驗身 */
		// CASE_STATUS_01_SubStatus_010("01 010"),
		// /** NEW_CASE_CHK 成功 */
		// CASE_STATUS_01_SubStatus_020("01 020"),
		// /** GET_AML_DATA 成功 */
		// CASE_STATUS_01_SubStatus_030("01 030"),
		// /** CHECK_NO_FINPF 成功 */
		// CASE_STATUS_01_SubStatus_040("01 040"),
		// /** CHECK_HAVE_FINPF2 成功 */
		// CASE_STATUS_01_SubStatus_050("01 050"),
		// /** CHECK_HAVE_FINPF3 成功 */
		// CASE_STATUS_01_SubStatus_060("01 060"),
		// /** CREATE_NEW_CASE 已呼叫新增案件(STP)成功 */
		// CASE_STATUS_01_SubStatus_070("01 070"),
		// /** CREATE_NEW_CASE 已呼叫新增案件(STP)fail */
		// CASE_STATUS_01_SubStatus_071("01 071"),
		// /** 完成新增案件，含以上所有電文。 */
		// CASE_STATUS_01_SubStatus_090("01 090"),
		// /** **產生申請書HTML(previewCasePDFHtml)**成功 */
		// CASE_STATUS_01_SubStatus_100("01 100"),
		// /** **產生申請書HTML(previewCasePDFHtml)**失敗 */
		// CASE_STATUS_01_SubStatus_101("01 101"),
		//
		// /** 確認上傳身份證正反面 */
		// CASE_STATUS_01_SubStatus_400("01 400"),
		// /** 戶役政查詢成功 */
		// CASE_STATUS_01_SubStatus_410("01 410"),
		// /** 戶役政查詢失敗 */
		// CASE_STATUS_01_SubStatus_411("01 411"),
		//
		// /** 風險評級成功 */
		// CASE_STATUS_01_SubStatus_420("01 420"),
		// /** 風險評級失敗 */
		// CASE_STATUS_01_SubStatus_421("01 421"),
		//
		// /** 呼叫Z07查詢成功 */
		// CASE_STATUS_01_SubStatus_430("01 430"),
		// /** 呼叫Z07查詢失敗 */
		// CASE_STATUS_01_SubStatus_431("01 431"),
		// /** 呼叫外部負債資料查詢成功 */
		// CASE_STATUS_01_SubStatus_440("01 440"),
		// /** 呼叫外部負債資料查詢失敗 */
		// CASE_STATUS_01_SubStatus_441("01 441"),
		// /** 產生KYC表成功 */
		// CASE_STATUS_01_SubStatus_450("01 450"),
		// /** 產生KYC表失敗 */
		// CASE_STATUS_01_SubStatus_451("01 451"),
		//
		// /** 確認上傳財力證明 */
		// CASE_STATUS_01_SubStatus_600("01 600"),
		// /** 確認上傳同一關係人表 */
		// CASE_STATUS_01_SubStatus_700("01 700"),
		// /** 客戶資料上傳完成 */
		// CASE_STATUS_01_SubStatus_800("01 800"),
		//
		// /** UPDATE_CASE_INFO 發查聯徵中 */
		// CASE_STATUS_01_SubStatus_900("01 900"),
		// /** UPDATE_CASE_INFO 發查聯徵成功 */
		// CASE_STATUS_01_SubStatus_910("01 910"),
		// /** UPDATE_CASE_INFO 發查聯徵失敗 */
		// CASE_STATUS_01_SubStatus_911("01 911"),
		//
		// /** 卡加貸信用卡已送件 */
		// CASE_STATUS_03_SubStatus_000("03 000"),
		//
		// /** 紙本送出 */
		// CASE_STATUS_04_SubStatus_000("04 000"),
		//
		// /** 送件完成 */
		// CASE_STATUS_12_SubStatus_000("12 000"),
		// /** GET_CASE_COMPLETE 後端整件狀態已完整 */
		// CASE_STATUS_12_SubStatus_100("12 100"),
		// /** GET_CASE_COMPLETE 後端整件狀態已完整，並呼叫APS啟動徵審處理完成 */
		// CASE_STATUS_12_SubStatus_200("12 200"),
		//
		// /** APSStatus狀態已有資料 */
		// CASE_STATUS_22_SubStatus_000("22 000"),
		// /** 30天到期取消 */
		// CASE_STATUS_06_SubStatus_000("06 000"),
		// /** 案件取消 */
		// CASE_STATUS_07_SubStatus_000("07 000"),
		// /** 系統拒貸，申請書尚未上傳 */
		// CASE_STATUS_09_SubStatus_000("09 000"),
		// /** 系統拒貸，申請書已上傳影像系統 */
		// CASE_STATUS_09_SubStatus_010("09 010");

		public final String code;

		CaseDataSubStatus(String code) {
			this.code = code;
		}

		public String getStatus() {
			return this.code.split(" ")[0];
		}

		public String getSubStatus() {
			return this.code.split(" ")[1];
		}

	} // end CaseDataSubStatus

	public enum CaseDataJobStatus {

		/** 等待 - 重新產生申請書PDF */
		CASE_STATUS_01_JobStatus_000("01 000"),
		/** 等待 - 第一次開始產生申請書PDF */
		CASE_STATUS_01_JobStatus_010("01 010"),
		/** 第一次開始產生申請書PDF */
		CASE_STATUS_01_JobStatus_200("01 200"),
		/** 執行送件成功 */
		CASE_STATUS_01_JobStatus_210("01 210"),
		/** 執行送件失敗(連線失敗 */
		CASE_STATUS_01_JobStatus_211("01 211"),
		/** 圖片上傳ftp成功 */
		CASE_STATUS_01_JobStatus_220("01 220"),
		/** 圖片上傳ftp失敗 */
		CASE_STATUS_01_JobStatus_221("01 221"),
		/** XML上傳成功 */
		CASE_STATUS_01_JobStatus_230("01 230"),
		/** XML上傳失敗 */
		CASE_STATUS_01_JobStatus_231("01 231"),
		/** 呼叫影像WebService成功 */
		CASE_STATUS_01_JobStatus_240("01 240"),
		/** 呼叫影像WebService失敗 */
		CASE_STATUS_01_JobStatus_241("01 241"),
		/** 更新送件完成成功 */
		CASE_STATUS_01_JobStatus_250("01 250"),
		/** 更新送件完成失敗 */
		CASE_STATUS_01_JobStatus_251("01 251"),
		/** 第一次完成產生申請書PDF成功 */
		CASE_STATUS_01_JobStatus_260("01 260"),
		/** 第一次完成產生申請書PDF失敗 */
		CASE_STATUS_01_JobStatus_261("01 261"),

		/** 等待 - 開始產生KYC表PDF */
		CASE_STATUS_01_JobStatus_020("01 020"),
		/**  */
		CASE_STATUS_01_JobStatus_500("01 500"),
		/**  */
		CASE_STATUS_01_JobStatus_520("01 520"),
		/**  */
		CASE_STATUS_01_JobStatus_521("01 521"),
		/**  */
		CASE_STATUS_01_JobStatus_530("01 530"),
		/**  */
		CASE_STATUS_01_JobStatus_531("01 531"),
		/** 開始產生KYC表PDF成功 */
		CASE_STATUS_01_JobStatus_560("01 560"),
		/** 開始產生KYC表PDF失敗 */
		CASE_STATUS_01_JobStatus_561("01 561"),

		/** 等待 - UPDATE_CASE_INFO 發查聯徵中 */
		CASE_STATUS_01_JobStatus_030("01 030"),
		/** UPDATE_CASE_INFO 發查聯徵中 */
		CASE_STATUS_01_JobStatus_900("01 900"),

		/** 等待 - 第二次開始產生申請書PDF(含ID財力圖檔) */
		CASE_STATUS_01_JobStatus_040("01 040"),
		/** 等待 - 申請完成等待最後一次排程 */
		CASE_STATUS_01_JobStatus_050("01 050"),
		/** 第二次開始產生申請書PDF(含ID財力圖檔 */
		CASE_STATUS_02_JobStatus_200("02 200"),
		/** 執行送件成功 */
		CASE_STATUS_02_JobStatus_210("02 210"),
		/** 執行送件失敗(連線失敗 */
		CASE_STATUS_02_JobStatus_211("02 211"),
		/** 圖片上傳ftp成功 */
		CASE_STATUS_02_JobStatus_220("02 220"),
		/** 圖片上傳ftp失敗 */
		CASE_STATUS_02_JobStatus_221("02 221"),
		/** XML上傳成功 */
		CASE_STATUS_02_JobStatus_230("02 230"),
		/** XML上傳失敗 */
		CASE_STATUS_02_JobStatus_231("02 231"),
		/** 呼叫影像WebService成功 */
		CASE_STATUS_02_JobStatus_240("02 240"),
		/** 呼叫影像WebService失敗 */
		CASE_STATUS_02_JobStatus_241("02 241"),
		/** 更新送件完成成功 */
		CASE_STATUS_02_JobStatus_250("02 250"),
		/** 更新送件完成失敗 */
		CASE_STATUS_02_JobStatus_251("02 251"),
		/** 第二次完成產生申請書PDF(含ID財力圖檔)成功 */
		CASE_STATUS_02_JobStatus_260("02 260"),
		/** 第二次完成產生申請書PDF(含ID財力圖檔)失敗 */
		CASE_STATUS_02_JobStatus_261("02 261");

		public final String code;

		CaseDataJobStatus(String code) {
			this.code = code;
		}

		public String getStatus() {
			return this.code.split(" ")[0];
		}

		public String getJobStatus() {
			return this.code.split(" ")[1];
		}

	} // end CaseDataJobStatus

	/** 圖片上傳狀態-待處理(圖片上傳完成才會到此狀態) */
	public final static Integer PHOTO_STATUS_WAIT = 1;
	/** 圖片上傳狀態-客戶補件中 */
	public final static Integer PHOTO_STATUS_ADDING = 2;
	/** 圖片上傳狀態-已送影像系統 */
	public final static Integer PHOTO_STATUS_SUBMIT = 3;

	/** 利率體驗30天狀態 -可體驗 */
	public final static String QUOTACAL_CHKDATE_SUCCESS = "1";

	/** 利率體驗30天狀態 -不可體驗(30天內已有體驗) */
	public final static String QUOTACAL_CHKDATE_ERROR = "99";

	/** 上傳圖片線上線下狀態 -線上 */
	public final static Integer PHOTO_ONLINE_STATUS_ONLINE = 1;
	/** 上傳圖片線上線下狀態 -線下 */
	public final static Integer PHOTO_ONLINE_STATUS_OFFLINE = 2;

	/** 上傳圖片產品別種類 -信用卡 */
	public final static String PHOTO_PRODUCTTYPE_CREDIT = "1";
	/** 上傳圖片產品別種類 -貸款 */
	public final static String PHOTO_PRODUCTTYPE_LOAN = "2";
	/** 上傳圖片產品別種類 -薪轉 */
	public final static String PHOTO_PRODUCTTYPE_SAL = "3";
	/** 上傳圖片產品別種類 -數三 */
	public final static String PHOTO_PRODUCTTYPE_D3 = "4";
	/** 上傳圖片產品別種類 -多合一 */
	public final static String PHOTO_PRODUCTTYPE_AIO = "5";
	/** 上傳圖片產品別種類 -靈活卡 */
	public final static String PHOTO_PRODUCTTYPE_FIEX_CARD = "7";
	/** 上傳圖片產品別種類 -加辦 */
	public final static String PHOTO_PRODUCTTYPE_ADDITIONAL = "8";

	/** 上傳圖片產品別種類 -數二 */
	public final static String PHOTO_PRODUCTTYPE_D2 = "10";

	/** 上傳圖片產品別種類 -貸款調額 */
	public final static String PHOTO_PRODUCTTYPE_LOAN_APPLYMONEY = "9";

	/** 上傳圖片產品別種類 -信用卡調額 */
	public final static String PHOTO_PRODUCTTYPE_CREDIT_LIMIT = "12";

	/** 1 線上件 */
	public static final String USERPHOTO_ONLINE_1 = "1";

	/** 2 線下件 */
	public static final String USERPHOTO_ONLINE_2 = "2";

	// 申辦產品
	/** 1 信用卡線上申辦(CREDITCARD) */
	public static final String USERPHOTO_ProdType_1 = "1";
	/** 2 信貸線上申辦(LOAN) */
	public static final String USERPHOTO_ProdType_2 = "2";
	/** 3 銀行線上開戶(薪轉EOP) */
	public static final String USERPHOTO_ProdType_3 = "3";
	/** 4 證券線上開戶(ED3) */
	public static final String USERPHOTO_ProdType_4 = "4";
	/** 5 期貨線上開戶 */
	public static final String USERPHOTO_ProdType_5 = "5";

	// PDF Template
	/** 貸款Template */
	public static final String PDF_LOAN_TEMPLATE = "02";
	/** 信用卡Template */
	public static final String PDF_CreditCard_TEMPLATE = "05";

	/** 無主分類 */
	public static final String USERPHOTO_PTYPE_0 = "0";
	/** 身分證明文件 */
	public static final String USERPHOTO_PTYPE_1 = "1";
	/** 財力證明文件 */
	public static final String USERPHOTO_PTYPE_2 = "2";
	/** 其它財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_99 = "99";
	/** 薪資轉帳財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_20 = "20";
	/** 土地謄本財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_21 = "21";
	/** 存款證明財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_22 = "22";
	/** 扣繳憑單財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_23 = "23";
	/** 所得清單財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_24 = "24";
	/** AUM財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_25 = "25";
	/** 企業報稅資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_26 = "26";
	/** 薪轉存摺財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_27 = "27";
	/** 勞保投保明細財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_28 = "28";
	/** 主要往來存摺財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_29 = "29";
	/** 定存財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_30 = "30";
	/** 基金/債券現值資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_31 = "31";
	/** 保險可貸款金額資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_32 = "32";
	/** 股票庫存現值資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_33 = "33";
	/** 健保投保資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_34 = "34";
	/** 其他加分文件財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_35 = "35";
	public static final String USERPHOTO_SUBTYPE_36 = "36";
	/** r2-5077 信用卡CR 定存財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_37 = "37";
	/** r2-5077 信用卡CR 股票庫存現值資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_38 = "38";
	/** r2-5077 信用卡CR 基金/債券現值資料財力證明文件 */
	public static final String USERPHOTO_SUBTYPE_39 = "39";
	/** 其他文件PDF格式  WT20241101001 新增*/
	public static final String USERPHOTO_SUBTYPE_96 = "96";
	/** 財力文件PDF格式  WT20241101001 新增*/
	public static final String USERPHOTO_SUBTYPE_97 = "97";
	/** 其他文件 */
	public static final String USERPHOTO_SUBTYPE_98 = "98";

	/**
	 * CaseData.UserType=3 既有戶 此為貸款既有戶 應該要移除 根據 決策平台文件 的GET_OTP_STP DATA_TYPE 1:信用卡
	 * 2: 現金卡 3:存款 4: 貸款
	 */
	public static final String USER_TYPE_3_OLD_ONE = "3";
	/** CaseData.UserType=1 既有戶 */
	public static final String USER_TYPE_OLD_ONE = "1";
	/** CaseData.UserType=0 新戶 */
	public static final String USER_TYPE_0_NEW_ONE = "0";

	// TABLE Config 錯誤訊息
	/** '您無任何可供線上簽約之案件或申請案件尚未達立約階段', '沒有任何可以簽屬的立約案件' */
	public static final String CONT_Err_GET_DGT_CONST = "CONT_Err_GET_DGT_CONST";
	/** '系統問題，如有任何需要協助，請洽客服人員', '連接電文回傳 CHECK_CODE=99 系統錯誤' */
	public static final String CONT_Err_GETAPI = "CONT_Err_GETAPI";
	/** '系統問題，如有任何需要協助，請洽客服人員', '非預期的錯誤' */
	public static final String CONT_Err_Unexpected = "CONT_Err_Unexpected";
	/** '您有貸款申請案件尚未結案', '申請人在APS系統有案件尚未結案' */
	public static final String LOAN_Err_NEW_CASE_CHK = "LOAN_Err_NEW_CASE_CHK";

	// OBDC 立約流程
	/** OBDC 立約流程 1 PL */
	public static final String CONT_ProductId_1 = "1";
	/** OBDC 立約流程 3 RPL */
	public static final String CONT_ProductId_3 = "3";
	/** OBDC 立約流程 2 CASHCARD 現金卡 */
	public static final String CONT_ProductId_2 = "2";

	// LOAN 申請流程
	/** LOAN 申請流程 1 PA 預核名單preAudit(快速申辦不查聯徵) */
	public static final String LOAN_ProductId_1_PA = "1";
	/** LOAN 申請流程 2 PL */
	public static final String LOAN_ProductId_2_PL = "2";
	/** LOAN 申請流程 3 RPL */
	public static final String LOAN_ProductId_3_RPL = "3";
	/** LOAN 申請流程 4 GM(CASHCARD) 現金卡 */
	public static final String LOAN_ProductId_4_GM = "4";
	/** LOAN 申請流程 5 HL 房屋貸款-借款人 */
	public static final String LOAN_ProductId_5_HL = "5";
	/** LOAN 申請流程 6 HL-Assurer 房屋貸款-保證人 */
	public static final String LOAN_ProductId_6_HL = "6";

	/** 8220 北一區 */
	public static final String[] CROSS_ZONE_8220 = { "基隆市", "台北市", "新北市", "桃園", "宜蘭" };
	/** 8230 北二區 */
	public static final String[] CROSS_ZONE_8230 = { "基隆市", "台北市", "新北市", "桃園", "宜蘭", "花蓮" };
	/** 8240 桃竹區 */
	public static final String[] CROSS_ZONE_8240 = { "新北市", "桃園", "新竹", "苗栗" };
	/** 8250 台中區 */
	public static final String[] CROSS_ZONE_8250 = { "苗栗", "台中", "彰化", "南投" };
	/** 8260 台南區 */
	public static final String[] CROSS_ZONE_8260 = { "雲林", "嘉義", "台南", "高雄" };
	/** 8270 高雄區 */
	public static final String[] CROSS_ZONE_8270 = { "台南", "高雄", "屏東", "台東" };

	public static final String Get_CDD_Rate_SystemType_G08 = "G08";

	public static final Map<String, String[]> CROSS_ZONES;
	static {
		CROSS_ZONES = new HashMap<>();
		CROSS_ZONES.put("8220", CROSS_ZONE_8220);
		CROSS_ZONES.put("8230", CROSS_ZONE_8230);
		CROSS_ZONES.put("8240", CROSS_ZONE_8240);
		CROSS_ZONES.put("8250", CROSS_ZONE_8250);
		CROSS_ZONES.put("8260", CROSS_ZONE_8260);
		CROSS_ZONES.put("8270", CROSS_ZONE_8270);
	}

	/**
	 * DATA_TYPE 客戶類型(1.信用卡 2.現金卡(靈活卡) 3.存款 4.貸款) 目的是給apply verify
	 * 簡訊驗證顯示用戶於本行有效行動電話之服務名稱
	 */
	public enum StempDataType {

		/**
		 * GET_OTP_TEL_STP
		 * 若是既有戶 CHECK_CODE 會回 0
		 */
		HAS_DATA_TYPE_CHECK_CODE("0"),

		/**
		 * 信用卡
		 * 提示：申辦本行信用卡服務之
		 */
		Creditcard("1"),
		/**
		 * 現金卡
		 * 提示：申辦本行現金卡服務之
		 */
		Cashcard("2"),
		/**
		 * 存款
		 * 提示：申辦本行存款服務之
		 */
		Kgibank("3"),
		/**
		 * 貸款
		 * 提示：申辦本行貸款服務之
		 */
		Loan("4"),
		/**
		 * VIP
		 * 提示：VIP
		 */
		Vip("5");

		public final String code;

		StempDataType(String code) {
			this.code = code;
		}
	}

	// TODO: enum CaseDocument

	// public enum RouteGo {
	// // 1. /initPlContract
	// initPlContract,
	// // 2. /n/chooseIdentificationStylePlContract
	// chooseIdentificationStylePlContract,
	// // 3. /n/identifyByBankAccountPlContract
	// identifyByBankAccountPlContract,
	// // 4. /o/otpPlContract
	// otpPlContract,
	// // 5. /n/choosePlContract
	// choosePlContract,
	// // 6. /n/agreePlContract
	// agreePlContract,
	// // 7. /n/agreeRplContract
	// agreeRplContract,
	// // 8. /n/agreeCashcardContract
	// agreeCashcardContract,
	// // 9. /n/infoPlContract
	// infoPlContract,
	// // 10. /n/previewPlContract
	// previewPlContract,
	// // 11. /n/allFinishPlContract
	// allFinishPlContract,
	// // 13. /n/allFailurePlContract
	// allFailurePlContract
	// }

	public enum SituationPCode2566 {
		auth,
		sign
	}

	public static boolean isPL(String productId) {
		return SystemConst.CONTRACT_PRODUCT_ID_TYPE_LOAN.equals(productId);
	}

	public static boolean isRPL(String productId) {
		return SystemConst.CONTRACT_PRODUCT_ID_TYPE_ELOAN.equals(productId);
	}

	public static boolean isCASHCARD(String productId) {
		return SystemConst.CONTRACT_PRODUCT_ID_TYPE_CASH.equals(productId);
	}

	/** EOP_SHORTURL Status 短網址狀態 */
	public final static String QRCODE_STATUS_EDIT = "01"; // 使用者編輯中
	public final static String QRCODE_STATUS_SEND_CHECKED = "02"; // 待審核
	public final static String QRCODE_STATUS_SEND_APPROVED = "03"; // 待放行
	public final static String QRCODE_STATUS_APPROVED = "04"; // 放行
	// public final static String QRCODE_STATUS_SEND_CHECKESUSPEND = "05"; //停用待審核
	public final static String QRCODE_STATUS_SEND_SUSPEND = "06"; // 停用待放行
	public final static String QRCODE_STATUS_SUSPEND = "07";// 停用
	public final static String QRCODE_STATUS_DELETE = "08";// 刪除
	public final static String QRCODE_STATUS_SENDORBIT_SUCCESSS = "21";// 已送影像
	public final static String QRCODE_STATUS_SENDORBIT_FAIL = "22";// 送影像失敗
	public final static String QRCODE_STATUS_OVERTIME = "23";// 批號無案件，結束批號

	/**
	 * 前端登入時，斷點顯示使用：將此筆 AioCaseData 之 uniqId 存入 table「CaseProperties」紀錄使用者過了哪些頁面
	 * 初始登入只先設定「確認頁」、「上傳證件頁」、「上傳財力頁」
	 * 待「確認頁」時，再判斷是否需顯示「關係人設定頁」、「網行銀設定頁」
	 * CaseProperties：User.BreakPoint.Confirm 確認頁
	 * CaseProperties：User.BreakPoint.UploadId 上傳證件頁
	 * CaseProperties：User.BreakPoint.UploadFinancial 上傳財力頁
	 * CaseProperties：User.BreakPoint.SettingReleation 關係人設定頁
	 * CaseProperties：User.BreakPoint.SettingWebBank 網行銀設定頁
	 *
	 * R1 -> 既有戶、新戶，都一定要上傳證件或是財力證明
	 * R2 -> 既有戶，需打電文：
	 * 1.是否為免財力證明
	 * 2.是否為薪轉戶
	 * 3.是否有勾 CH2 (同意使用與貴行往來之資料申辦貸款)
	 * 4.有沒有證件影像編號
	 * 5.有沒有財力影像編號
	 */
	public static final String USER_BREAKPOINT_CONFIRM = "User.BreakPoint.Confirm"; // 確認頁
	public static final String USER_BREAKPOINT_UPLOAD_ID = "User.BreakPoint.UploadId"; // 上傳證件頁
	public static final String USER_BREAKPOINT_UPLOAD_FINANCIAL = "User.BreakPoint.UploadFinancial"; // 上傳財力頁
	public static final String USER_BREAKPOINT_SETTING_RELEATION = "User.BreakPoint.SettingReleation"; // 關係人設定頁
	public static final String USER_BREAKPOINT_SETTING_WEBBANK = "User.BreakPoint.SettingWebBank"; // 網行銀設定頁
	public static final String UNNECESSARY_SETTING = "User.NotSettingWebBank"; // 客戶不需在設定網行銀(發查1001Q卻發現它不必在設定惹 就會出現這個訊息)

	public static final String USER_BREAKPOINT_HAS = "0"; // 需進入 確認頁
	public static final String USER_BREAKPOINT_ALREADY = "1"; // 已進入 確認頁

	public static final String SAL_SHORTURL_NOT_EOP = "並不是從短網址進入的";
	public static final String SAL_SHORTURL_INVALID = "該Url並不是有效的";
	public static final String SAL_SHORTURL_NOT_CHECKLIST = "此身分證不存在檢核名單內";
	public static final String SAL_ALREADY_HAS = "您已經申辦過薪轉帳號";
	public static final String SAL_ALREADY_REPEATED_ENTRY = "已經進入過";

	public static final String ENTRY_AIO = "aio";
	public static final String ENTRY_ED3 = "d3";
	public static final String ENTRY_ED2 = "d2";
	public static final String ENTRY_AIO_D3 = "aio-d3";
	public static final String ENTRY_SAL = "sal";
	public static final String ENTRY_APPT = "appt";
	public static final String ENTRY_FLEX_CARD = "flex-card";

	/** 補件 申辦產品 信用卡 */
	public static final String ADD_PRODUCT_TYPE_CC = "1";
	/** 補件 申辦產品 貸款 */
	public static final String ADD_PRODUCT_TYPE_LOAN = "2";
	/** 補件 申辦產品 薪轉帳戶 */
	public static final String ADD_PRODUCT_TYPE_SAL = "3";
	/** 補件 申辦產品 數位帳戶 */
	public static final String ADD_PRODUCT_TYPE_D3 = "4";
	/** 補件 申辦產品 多合一 */
	public static final String ADD_PRODUCT_TYPE_AIO = "5";
	/** 補件 申辦產品 靈活卡 */
	public static final String ADD_PRODUCT_TYPE_FLEX_CARD = "7";
	/** 補件 申辦產品 加辦 */
	public static final String ADD_PRODUCT_TYPE_ADDITION = "8";
	/** 補件 申辦產品 貸款額度調整 */
	public static final String ADD_PRODUCT_TYPE_LOAN_APPLYMONEY = "9";
	/** 補件 申辦產品 房貸 */
	public static final String ADD_PRODUCT_TYPE_HL = "11";
	/** 補件 申辦產品 信用卡調整額度 */
	public static final String ADD_PRODUCT_TYPE_CREDIT_LIMIT = "12";

	/** EOP_AOResult 案件開戶狀態 成功 */
	public static final String AOPRESULT_STATUS_SUCCESS = "0";
	/** EOP_AOResult 案件開戶狀態 退件、失敗 */
	public static final String AOPRESULT_STATUS_FAIL = "1";
	/** EOP_AOResult 案件開戶狀態 補件 */
	public static final String AOPRESULT_STATUS_FURTHER_DOCUMENTS = "2";

	/** EOP_CaseData Type 新戶 */
	public static final String CASEDATA_TYPE_NEW = "1";
	/** EOP_CaseData Type 既有戶 */
	public static final String CASEDATA_TYPE_OLD = "2";

	/** IdMapping Type 整批開戶 */
	public static final String IDMAPPING_TYPE_QRCode = "1";
	/** IdMapping Type 整批開戶 */
	public static final String IDMAPPING_TYPE_D3 = "2";

	/**
	 * 檢核失敗提示: 缺ID
	 */
	public static final String CHECK_idcard = "缺ID";

	/**
	 * 檢核失敗提示: 缺戶役政
	 */
	public static final String CHECK_rwv2c2 = "缺戶役政";

	/**
	 * 檢核失敗提示: 缺財力
	 */
	public static final String CHECK_finpf = "缺財力";
	
	/**
	 * 檢核失敗提示: 自行提供財力
	 */	
	public static final String CHECK_finpf_SelfUpload = "自提財力";

	/**
	 * 檢核失敗提示: 缺同一關係人表
	 */
	public static final String CHECK_relationDegree = "缺同一關係人表";

	/**
	 * 檢核失敗提示: 缺風險評級
	 */
	public static final String CHECK_cddRateRisk = "風險評級為高";

	/**
	 * 檢核失敗提示: 缺AML
	 */
	public static final String CHECK_aml = "缺AML";

	/**
	 * 檢核失敗提示: 高風險職業類別負責人
	 */
	public static final String CHECK_high_occupation_and_boss = "是高風險職業類別負責人";

//	/**
//	 * 檢核失敗提示: 若使用者為既有戶，且確認頁的九大欄位，有更改過值
//	 */
//	public static final String CHECK_PDF_CHANGED = "既有戶申請書的內容修改過";

//	/**
//	 * 檢核失敗提示: 缺Z07
//	 */
//	public static final String CHECK_z07 = "缺Z07";

	/**
	 * 檢核失敗提示: 是利關人
	 */
	public static final String CHECK_relp = "是利關人";

	/**
	 * 檢核失敗提示: 缺KYC
	 */
	public static final String CHECK_kyc = "缺KYC";

	/**
	 * 檢核失敗提示: 是告誡戶
	 */
	public static final String CHECK_doubtAboutIdentity = "是告誡戶";

	/**
	 * 檢核失敗提示: APS異常件
	 */
	public static final String CHECK_aps = "APS異常件";




	/**
	 * 檢核失敗提示: 身分證件上傳異常
	 */
	public static final String CHECK_ID_CARD = "請確認身分圖檔";

	/**
	 * 檢核失敗提示: 缺風險評級
	 */
	public static final String CHECK_IS_VIP = "卡別為509";


	/**
	 * 專案別 pl 不指定
	 */
	public static final String PRJCODE_PL_DEFAULT = "_PLDefault";
	/**
	 * 專案別 Rpl 不指定
	 */
	public static final String PRJCODE_RPL_DEFAULT = "_RPLDefault";
	/**
	 * 專案別 GM 不指定
	 */
	public static final String PRJCODE_GM_DEFAULT = "_GMDefault";

	/**
	 * CaseProperty Pl RejectFlag Name
	 */
	public static final String REKECT_FLAG_PL_NAME = "NewCaseCheck.Reject.FlagPl";
	/**
	 * CaseProperty Rpl RejectFlag Name
	 */
	public static final String REKECT_FLAG_RPL_NAME = "NewCaseCheck.Reject.FlagRpl";

	/**
	 * CaseProperty GetOtpTelStp Is Changed
	 */
	public static final String GET_OTP_TEL_STP_ISCHANGE = "GetOtpTelStp.Is.Changed";

	/**
	 * CaseProperty GetOtpTelStp Is NewCc
	 */
	public static final String GET_OTP_TEL_STP_ISNEWCC = "GetOtpTelStp.Is.NewCc";

	/**
	 * CaseProperty GetOtpTelStp Is NewCc
	 */
	public static final String CASE_ADDITION_IS_ERROR_APPLY_WARNING_ACCOUNT = "Case.Addition.Is.Error.Apply.Warning.Account";

	/**
	 * 完成後處理代碼
	 */
	public static final String PROCESS_STEP_0000 = "0000";
	/**
	 * 重新呼叫起案電文並更新起案編號
	 */
	public static final String PROCESS_STEP_0100 = "0100";
	/**
	 * 「加強檢核」及「早期延滯」原因會標於aps0501abnormalDesc
	 */
	public static final String PROCESS_STEP_0101 = "0101";
	/**
	 * 開始發查聯徵
	 */
	public static final String PROCESS_STEP_0200 = "0200";
	/**
	 * 開始發動評分作業
	 */
	public static final String PROCESS_STEP_0300 = "0300";
	public static final String PROCESS_STEP_0400 = "0400";
	public static final String PROCESS_STEP_0401  = "0401";
	/**
	 * 上傳影像(含ID、財力)
	 */
	public static final String PROCESS_STEP_0500 = "0500";
	/**
	 * 上傳影像(含ID、財力) 快速件
	 */
	public static final String PROCESS_STEP_0501 = "0501";
	/**
	 * 呼叫APS人工整件送出電文
	 */
	public static final String PROCESS_STEP_0600 = "0600";
	/**
	 * 重新上傳拒貸申請書(含呼叫UPDATE_DGTCASE_FLOW-05)
	 */
	public static final String PROCESS_STEP_0900 = "0900";
	/**
	 * 重新上傳拒貸申請書(不含呼叫UPDATE_DGTCASE_FLOW-05)
	 */
	public static final String PROCESS_STEP_0902 = "0902";

	/**
	 * 貸款排程 010 020 030 040 050
	 */
	public static final String LOAN_JOB_STATUS_010 = "010";
	public static final String LOAN_JOB_STATUS_020 = "020";
	public static final String LOAN_JOB_STATUS_030 = "030";
	public static final String LOAN_JOB_STATUS_040 = "040";
	public static final String LOAN_JOB_STATUS_050 = "050";

	/**
	 * 數位申辦平台
	 */
	public static final String LOAN_DGT_SYSTEM_TYPE_AIRLOAN = "AIRLOAN";

	/**
	 * 信用卡徵審系統
	 */
	public static final String LOAN_DGT_SYSTEM_TYPE_CCUW = "CCUW";

	/**
	 * 寫入申請書相關欄位資料、行內查詢異常判斷、AML更新查詢結果
	 */
	public static final String LOAN_DGT_FLOW_STATUS_01 = "01";
	/**
	 * 寫入身分證換補發資料
	 */
	public static final String LOAN_DGT_FLOW_STATUS_02 = "02";
	/**
	 * 關鍵字、查詢聯徵、WHOSCALL、查詢決策判斷審核條件、查詢行內外資料、計算信評、判斷專案別、判斷是否符合秒貸(Y/N)
	 */
	public static final String LOAN_DGT_FLOW_STATUS_03 = "03";
	/**
	 * 寫入同一關係人資料
	 */
	public static final String LOAN_DGT_FLOW_STATUS_04 = "04";
	/**
	 * 執行秒貸流程並更新准駁狀態
	 */
	public static final String LOAN_DGT_FLOW_STATUS_05 = "05";

	/**
	 * ViewType 沒上傳
	 */
	public static final String LOAN_HAS_VIEW_TYPE_00 = "00";

	/**
	 * ViewType 已上傳
	 */
	public static final String LOAN_HAS_VIEW_TYPE_01 = "01";

	/**
	 * ViewType 無需上傳
	 */
	public static final String LOAN_HAS_VIEW_TYPE_02 = "02";

	/**
	 * ViewType 排程成功
	 */
	public static final String LOAN_HAS_VIEW_TYPE_03 = "03";

	/**
	 * ViewType 排程成功
	 */
	public static final String LOAN_HAS_VIEW_TYPE_04 = "04";

	/**
	 * AOResult
	 * 01: 補件成功
	 * 02: 補件失敗
	 */
	public static final String AdditionalStatus_SUCCESS = "01";
	public static final String AdditionalStatus_ERROR = "02";

	/**
	 * MailHistory Status 定義
	 * 0 : 尚未寄送
	 * 1 : 寄送成功
	 * 9 : 寄送失敗
	 * 3 : 不發送 因應 #5537 ， 2024/03/12 xavier
	 */
	public static final String MAIL_HISTORY_STATUS_NON_SEND = "0";
	public static final String MAIL_HISTORY_STATUS_SEND_SUCCESS = "1";
	public static final String MAIL_HISTORY_STATUS_SEND_ERROR = "9";
	public static final String MAIL_HISTORY_STATUS_NO_SEND  = "3";

	/**
	 * MailHistory MailType 定義
	 * 1 : MailHunter 寄送行外
	 * 2 : SMTP 寄送行內
	 * 3 : TM 通知(JOB)
	 * 4 : 簡訊通知
	 * 5 : 全部一起寄(IT部門 + Maill內所需)
	 */
	public static final String MAIL_HISTORY_MAILTYPE_OUTSIDE_LINE = "1";
	public static final String MAIL_HISTORY_MAILTYPE_SMTP_IN_LINE = "2";
	public static final String MAIL_HISTORY_MAILTYPE_TM = "3";
	public static final String MAIL_HISTORY_MAILTYPE_SMS = "4";
	public static final String MAIL_HISTORY_MAILTYPE_ALL = "5";

	/**
	 * 貸款異業
	 */
	public static final String LOAN_CROSS_SALL_D00050 = "D00050";
	public static final String LOAN_CROSS_SALL_D00060 = "D00060";
	public static final String LOAN_CROSS_SALL_D00070 = "D00070";
	public static final String LOAN_CROSS_SALL_D00080 = "D00080";
	public static final String LOAN_CROSS_SALL_D00090 = "D00090";

	/**
	 * 貸款Pcode2566錯誤
	 */
	public static final String PCODE_ERROR_01 = "01";
	public static final String PCODE_ERROR_02 = "02";
	public static final String PCODE_ERROR_03 = "03";
	public static final String PCODE_ERROR_04 = "04";

	/**
	 * 上傳影像排程申辦產品
	 */
	public static final String UPLOAD_LOAN_AND_CC = "UPLOAD_LOAN_AND_CC";
	public static final String UPLOAD_LOAN = "UPLOAD_LOAN";
	public static final String UPLOAD_CC = "UPLOAD_CC";
	public static final String UPLOAD_HL = "UPLOAD_HL";

	/**
	 * MailHunterHistory Product
	 */
	public static final String MAIL_Product_D3 = "d3";
	public static final String MAIL_Product_D2 = "d2";
	public static final String MAIL_Product_CC = "cc";
	public static final String MAIL_Product_LOAN = "loan";
	public static final String MAIL_Product_appt = "appt";
	public static final String MAIL_Product_HL = "hl";
	public static final String MAIL_Product_ADD ="add"; //補件流程
	public static final String MAIL_Product_D3Video = "d3_video";
	public static final String MAIL_Product_D3TransAcct = "d3_trans_acct";

	/**
	 * PprodcutType
	 */
	public static final String PprodcutType_Product_D3 = "d3";
	public static final String PprodcutType_Product_CC = "cc";
	public static final String PprodcutType_Product_LOAN = "loan";
	public static final String PprodcutType_Product_GM = "flex-card";
	public static final String PprodcutType_Product_HL = "hl";
	public static final String PprodcutType_Product_AIO_LOAN = "aio-loan";
	public static final String PprodcutType_Product_AIO_D3 = "aio-d3";
	public static final String PprodcutType_Product_DC = "dc";
	public final static String PprodcutType_AIO = "AIO";
	public final static String PprodcutType_CC = "CC";
	public final static String PprodcutType_CC_1GS = "1GS";
	public final static String PprodcutType_D3 = "D3";

	/**
	 * MailHunterHistory TemplateId、 ProductCode、 OwnerID
	 */
	public static final String MAIL_D3_TEMPLATE_ID = "MailHunter.ED3FinishTemplateId";
	public static final String MAIL_D2_TEMPLATE_ID = "MailHunter.ED2FinishTemplateId";
	public static final String MAIL_ADD_TEMPLATE_ID = "MailHunter.ED3AdditionalTemplateId";
	public static final String MAIL_D3_PRODUCT_CODE = "MailHunter.ED3ProductCode";
	public static final String MAIL_D3_OWNER_ID = "MailHunter.ED3OwnerID";
	public static final String MAIL_APPR_TEMPLATE_ID = "MailHunter.PrdouctAMsg";
	public static final String MAIL_DACH_TEMPLATE_ID = "MailHunter.DACHSuccessMsg";
	public static final String MAIL_D3Video_TEMPLATE_ID = "MailHunter.ED3FinishVideoTemplateId";
	public static final String MAIL_D3TransAcct_TEMPLATE_ID = "MailHunter.ED3LinkeTransTemplateId";

	// r2-3426 新增 template for CC
	public static final String MAIL_CC_TEMPLATE_ID = "MailHunter.CreditCaseTemplateId";

	// r2-5134 新增 template for CC 幣勝卡
	public static final String MAIL_BEWIN_TEMPLATE_ID = "MailHunter.CreditCaseBeWinTemplateId";

	public static final String MAIL_CCLOAN_TEMPLATE_ID = "MailHunter.ApplyDataTemplateId";
	public static final String MAIL_CCLOAN_PRODUCT_CODE = "MailHunter.CCLoanProductCode";
	public static final String MAIL_CCLOAN_OWNER_ID = "MailHunter.ED3OwnerID";

	/**
	 * 貸款起案
	 */
	public static final String LOAN_CREATE_APPLY_STATUS_PROCESS = "0";
	public static final String LOAN_CREATE_APPLY_STATUS_SUCCESS = "1";
	public static final String LOAN_CREATE_APPLY_STATUS_ABNORMAL = "2";
	public static final String LOAN_CREATE_APPLY_STATUS_FAIL = "3";

	public static final String APPLY_CONFIRM_VIEW = "APPLY_CONFIRM_VIEW";
	public static final String APPLY_Verification_VIEW = "APPLY_Verification_VIEW";

	/**
	 * 一般無限卡
	 */
	public static final String APPLY_CREDITCARD_NORMAL = "510001";

	/**
	 * 高端卡
	 */
	public static final String APPLY_CREDITCARD_VIP = "510002";

	/**
	 * 高端卡
	 */
	public static final String APPLY_CREDITCARD_TYPE_VIP = "510";

	/**
	 * 凱壽無限卡
	 */
	public static final String APPLY_CREDITCARD_TYPE_CL_VIP = "509";

	/**
	 * 魔力卡
	 */
	public static final String APPLY_CREDITCARD_TYPE_LOWRATE = "512";

	/**
	 * 幣勝卡
	 */
	public static final String APPLY_CREDITCARD_TYPE_BEWIN = "666";

	/**
	 * r2-3664
	 * 不能有首刷禮
	 */
	public static final String APPLY_NO_CARDGIFT_CHECK_CODE = "20";

	/**
	 * 產品別對應英文單字
	 */
	public static final String PRODUCT_TEXT_CC = "credit";
	public static final String PRODUCT_TEXT_LOAN_RPL = "loanRPL";
	public static final String PRODUCT_TEXT_LOAN_PL = "loanPL";
	public static final String PRODUCT_TEXT_D3 = "DigitalAC";
	public static final String PRODUCT_TEXT_AIO_RPL_D3 = "aio-rpl-d3"; // 快易抽

	/** 本行信用卡 資訊 未否滿6個月 */
	public static final String NCC41_VERIFY_ERROR = "9011";
	/** 本行信用卡 資訊 生日、卡號、有效期 錯誤 */
	public static final String NCC41_VERIFY_ERROR_Birth_OR_EXPDATE_OR_CARD = "9012";

	/** d3 免財力 */
	public static final String DGT3NoFin = "DGT3NoFin";

	/** d3 開心戶 */
	public static final String DGT3Happy = "DGT3Happy";

	/** 存款自動轉入 */
	public static final String DGT3ACH = "DGT3ACH";

	/**
	 * MailHunterHistory ProjectCode ALOAN
	 */
	public static final String ALOAN = "ALOAN";


	// 補貸款條額財力證明
	public static final String LOAN_CHANGE_GMSCSMT24R2_SO2403_3 = "3"; // 補財力證明及調額同意書簽署(只有這個才可以往下調額)
	public static final String LOAN_CHANGE_GMSCSMT24R2_SO2403_5 = "5"; // 補財力證明及契變書簽署及調額同意書千署(只有這個才可以往下調額)

	// 專案調額 貸款條額寄件TM單位
	public static final String LOAN_CHANGE_ADDITION_PROJECT_MAIL = "aio.loan.change.addition.project.mail";
	public static final String LOAN_CHANGE_ADDITION_PROJECT_MAIL_FAKER = "aio.loan.change.addition.project.mail.faker";
	// 一般調額件 貸款條額寄件TM單位
	public static final String LOAN_CHANGE_ADDITION_NORMAL_MAIL = "aio.loan.change.addition.normal.mail";
	public static final String LOAN_CHANGE_ADDITION_NORMAL_MAIL_FAKER = "aio.loan.change.addition.normal.mail.faker";
	public static final String LOAN_CHANGE_ADDITION_NORMAL_MAIL_TITLE = "aio.loan.change.addition.normal.mail.title";

	/** trackLog action */
	public static final String SAVE_ACTION = "save";
	public static final String UPDATE_ACTION = "update";
	public static final String DELETE_ACTION = "delete";
	public static final String SELECT_ACTION = "select";

	/** Digital START */
	public static final String SUB_BRO_BIN_KIND_T = "0"; // 台幣 (複委託)
	public static final String SUB_BRO_BIN_KIND_F = "1"; // 外幣 (複委託)
	public static final String SUB_BRO_BIN_KIND_ALL = "2"; // 台幣+外幣 (複委託)
	public static final String SUB_BRO_BIN_KIND_N = "3"; // 無 (複委託)

	public static final String D2_TWD = "12012600"; // 無摺綜活儲-數二_TWD
	public static final String D2_DELIVERY_TWD = "12022600"; // 無摺綜活儲-數二凱證_TWD
	public static final String D2_FOREIGN = "12032698"; // 無摺數二綜-外幣主帳戶

	public static final String D3_TWD_DELIVERY_PCODE = "12022000"; // //無摺綜活儲-數三凱證
	public static final String D3_TWD_DELIVERY_NCCC = "12022100"; // 無摺綜活儲-數三信卡凱證
	public static final String D3_TWD_PCODE = "12012000"; // 無摺綜活儲-數三帳號
	public static final String D3_TWD_NCCC = "12012100"; // 無摺綜活儲-數三信卡
	public static final String D3_ACC_NON = "00000000";

	public static final String D3_FOREIGN_PCODE = "12032098"; // 無摺綜活儲-數三外幣-帳戶
	public static final String D3_FOREIGN_NCCC = "12032198"; // 無摺綜活儲-數三外幣-信卡

	public static final String D1_ACC_05 = "12051300"; // 數一帳戶
	public static final String D1_ACC_06 = "12061300"; // 數一帳戶

	public static final String DIGITAL_EXIST_NON = "0"; // 未申辦數位案件
	public static final String DIGITAL_EXIST_D2 = "1"; // 申辦數二案件
	public static final String DIGITAL_EXIST_D3 = "2"; // 申辦數三案件
	public static final String DIGITAL_EXIST_D2_AO = "3"; // 申辦數二案件、且有Ed3_AoResult
	public static final String DIGITAL_EXIST_D3_AO = "4"; // 申辦數三案件、且有Ed3_AoResult
	public static final String DIGITAL_EXIST_APPT = "5"; // 申辦預約開戶案件
	public static final String DIGITAL_EXIST_APPT_AO = "6"; // 申辦預約開戶案件、且有Ed3_AoResult
	public static final String DIGITAL_EXIST_SAL = "7"; // 申辦薪轉案件
	public static final String DIGITAL_EXIST_SAL_AO = "8"; // 申辦薪轉案件、且有EOP_AoResult

	/**
	 * DeviceId 紀錄次數
	 */
	public static final String DEVICE_ID_FIRST = "device.id.first"; // 第一次紀錄
	public static final String DEVICE_ID_SECOND = "device.id.second"; // 第二次紀錄
	public static final String DEVICE_ID_CONNECTION_INFO = "aio.deviceId.connection.info"; // WEBSDK連線資訊
	public static final String DEVICE_ID_API_KEY = "aio.deviceId.api.key"; // 裝置風險 API Key
	public static final String DEVICE_ID_URL = "aio.Apim.deviceId.url"; // DeviceId 環境

	/**
	 * 業務自我檢視問題
	 */
	public static final String AIO_SIXQUESTIONS = "aio.SixQuestions"; // 業務自我檢視問題

	/** Digital END */

	/** userPhotoVerifyLog 驗證結果 */
	public static final String USER_PHOTO_VERIFY_LOG_STATUS_Y = "Y";
	public static final String USER_PHOTO_VERIFY_LOG_STATUS_N = "N";

	/** 取得瀏覽器資料 */
	public static final String BROWSER_VERSION = "browserVersionData";

	/** 取得手機系統資料 */
	public static final String MOBILE_DEVICE_VERSION = "mobileDeviceVersion";

	/** 同意條款 */
	public static final String AGREE_TERM_Y = "Y";
	public static final String AGREE_TERM_N = "N";

	/** 洗錢警示戶代號 */
	public static final String IS_ERROR_APPLY_WARNING_ACCOUNT = "Y";

	/** 貸款立約 人工處理API 成功訊息 */
	public static final String CASE_STATUS_CODE = "40";

	/** 線上件 */
	public static final String ONLINE = "線上件";

	/** 線下件 */
	public static final String NOT_ONLINE = "線下件";

	/** 房貸短網址的PRODUCTTYPE */
	public final static String HL_SHORT_URL_PRODUCTTYPE = "HL_ASSURER";

	/** 房貸 手機註記 手機與pCode2566相同 */
	public final static String PHONE_IS_PCODE2566 = "1";

	/** 房貸 手機註記 手機與pCode2566不相同 */
	public final static String PHONE_NOT_PCODE2566 = "2";

	/** 房貸 房屋使用狀況 其他 */
	public final static String USE_STATUS_TEXT_HL = "04";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA1 = "北一區";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA2 = "北二區";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA3 = "桃竹區";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA4 = "台中區";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA5 = "台南區";

	/** 房貸 房屋使用狀況 服務單位 */
	public final static String SERVICE_BASE_DATA6 = "高雄區";

	/** 房貸 案件狀態 驗身完成 */
	public final static String VERIFY_SUCCESS = "驗身成功";

	/** 房貸 案件狀態 填寫中 */
	public final static String VERIFY_NOT_SUCCESS = "填寫中";

	/** 房貸 信件title 完成*/
	public final static String HL_MAIL_FINAL = "完成";

	/** 房貸 信件title 斷點 */
	public final static String HL_MAIL_BREAK_POINT = "斷點";

	/** 房貸 已填寫完產品資訊*/
	public final static String FILLED_IN = "已填寫";

	/** 房貸借款人線上驗身成功 Value為是否發送完成件的狀態(0:未發送 1:發送)*/
	public final static String HL_VERIFY_STATUS_SUCCESS = "HLVerifyStatusSuccess";

	/** 房貸保證人線上驗身成功 Value為是否發送完成件的狀態(0:未發送 1:發送)*/
	public final static String HL_ASSURER_VERIFY_STATUS_SUCCESS = "HLAssurerVerifyStatusSuccess";

	/** 手機90天內曾異動跳_Y */
	public static final String P_CODE_2566_90D_Y = "Y";

	/** 手機90天內曾異動跳_Empty */
	public static final String P_CODE_2566_90D_E = "E";

	/** 手機90天內不曾異動跳_N */
	public static final String P_CODE_2566_90D_N = "N";

	/** 申辦過網銀_Y */
	public static final String HAS_WEB_BANK_Y = "Y";
	/** 申辦過電話銀_Y */
	public static final String HAS_IVR_BANK_Y = "Y";

	/** 匹配英文、標點符號 */
	public static final String VALIDATION_ENG_PUN = "engPun";

	/** 匹配數字、英文、標點符號 */
	public static final String VALIDATION_NUM_ENG_PUN = "numEngPun";

	/** 匹配數字、英文、標點符號 */
	public static final String VALIDATION_CHT_ENG_PUN = "chtEngPun";

	/** PASSPORT 護照類型 */
	public static final String PASSPORT_DOCUMENT_CODE = "DocumentCode";

	/** PASSPORT 簽發國家或地區代碼 */
	public static final String PASSPORT_ISSUING_STATE_ORGANIZATION = "IssuingStateOrganization";

	/** PASSPORT 姓名 */
	public static final String PASSPORT_NAME = "Name";

	/** PASSPORT 性別 */
	public static final String PASSPORT_SEX = "Sex";

	/** PASSPORT 護照有效期 */
	public static final String PASSPORT_DATE_OF_EXPIRY = "DateOfExpiry";

	/** PASSPORT 出生日期 */
	public static final String PASSPORT_DATE_OF_BIRTH = "DateOfBirth";

	/** PASSPORT 護照號碼 */
	public static final String PASSPORT_PASS_PORT_NUMBER = "PassportNumber";

	/** PASSPORT 國籍代碼 */
	public static final String PASSPORT_NATIONALITY = "Nationality";

	/** PASSPORT 身分證字號或個人號碼 */
	public static final String PASSPORT_PERSONAL_NUMBER_OTHER_OPTIONAL_DATA_ELEMENTS = "PersonalNumberOtherOptionalDataElements";


	/** dorpDownData 第二證件下拉選單 */
	public static final String SECOND_ID_LIST = "SecondIdList";



	/** ApplyItem 高風險職業註記 */
	public static final String HIGH_RISK_MARK_Y = "Y";
	public static final String HIGH_RISK_MARK_N = "N";
	/** dorpDownData 出生地下拉選單 */
	public static final String BIRTHPLACE_LIST = "OcrAddr";
	public static final String BIRTHPLACE_NOT_TAIWAN = "非台灣地區";

	/** VerifyResult Pcode 驗身狀態 */
	public static final String VERIFY_RESULT_SUCCESS = "00"; // 驗身通過
	public static final String VERIFY_RESULT_FAIL = "01"; // 驗身失敗

	/** PcodeReturn */
	public static final String PCODE_RETURN_Y = "000001Y"; // 驗身失敗

	/** 寄信對象 */
	public static final String SEND_OBJECT_ALL = "00"; // 寄給全部
	public static final String SEND_OBJECT_DEPART = "01"; // 寄給內部業務單位
	public static final String SEND_OBJECT_CUSTOMER = "02"; // 寄給客戶

	/** 6705001 電文 Email 回覆  */
	public static final String EMAIL_FROM_6705001_Y = "Y";
	public static final String EMAIL_FROM_6705001_N = "N";



	/** 8220 北一區 */
	public static final String[] CROSS_ZONE_8220_v2 = {"基隆市","台北市","臺北市","新北市","桃園市","宜蘭縣"};
	/** 8230 北二區 */
	public static final String[] CROSS_ZONE_8230_v2 = {"基隆市","台北市","臺北市","新北市","桃園市","宜蘭縣","花蓮縣"};
	/** 8280 北三區 */
	public static final String[] CROSS_ZONE_8280_v2 = {"基隆市","台北市","臺北市","新北市","桃園市","宜蘭縣","花蓮縣"};
	/** 8240 桃竹區 */
	public static final String[] CROSS_ZONE_8240_v2 = {"新北市","桃園市","新竹縣","新竹市","苗栗縣"};
	/** 8250 台中區 */
	public static final String[] CROSS_ZONE_8250_v2 = {"苗栗縣","台中市","臺中市","彰化縣","南投縣"};
	/** 8260 台南區 */
	public static final String[] CROSS_ZONE_8260_v2 = {"雲林縣","嘉義市","嘉義縣","台南市","臺南市","高雄市"};
	/** 8270 高雄區 */
	public static final String[] CROSS_ZONE_8270_v2 = {"台南市","臺南市","高雄市","屏東縣","台東縣","臺東縣"};

	/**
	 * 雙幣卡
	 */
	public static final String DC_USD= "531"; // 雙幣哩程卡(美金)
	public static final String DC_JPY= "532"; // 雙幣哩程卡(日圓)
	public static final String DC_EUR= "533"; // 雙幣哩程卡(歐元)

	/**
	 * 自扣設定
	 */
	public static final String ACHTypeAll= "1"; // 應繳總金額
	public static final String ACHTypeLow= "2"; // 應繳總金額


	/** 親友介紹 */
	public static final String INTRODUCTION_BY_RELATIVES_AND_FRIENDS = "34";

	/** 同業轉介 */
	public static final String INTRODUCTION_TO_THE_INDUSTRY = "80";
	
	/** 融資租賃 finance lease **/
	public static final String INTRODUCTION_BY_FINANCE_LEASE = "49";

	/** eKyc無任何繳款異常紀錄 */
	public static final String EKYC_PAYMENT_NORMAL = "無持有任何PL/RPL/GM/CC產品";

	/** eKyc繳款狀態_Y */
	public static final String EKYC_VERIFY_Y = "Y";

	/** eKyc繳款狀態_N */
	public static final String EKYC_VERIFY_N = "N";

	/** eKyc繳款狀態_* */
	public static final String EKYC_VERIFY_ASTERISK = "*";

	/** eKyc繳款狀態_* */
	public static final String EKYC_VERIFY_ASTERISK_U = "U";

	/** eKyc繳款狀態為Y 顯示的內容 */
	public static final String EKYC_NOT_LATE_PAYMENT = "無延滯繳款紀錄";

	/** eKyc繳款狀態為N 顯示的內容 */
	public static final String EKYC_IS_LATE_PAYMENT = "注意:有延滯繳款紀錄";

	/** eKyc繳款狀態為* 顯示的內容 */
	public static final String EKYC_NEW_CASE_WITH_THREE_MONTH = "往來未滿三個月的既有戶";




	/** 得知管道 自來件代號 */
	public static final String EKYC_INFOSOURCE_CODE = "16";

	/** 繳款紀錄檢查異常 */
	public static final String EKYC_PAYMENT_ERROR = "繳款紀錄檢查異常";

	/** 公司名稱檢查異常 */
	public static final String EKYC_COMPANY_CHECK_ERROR = "公司名稱檢查異常";

	/** 繳款紀錄 設定電文異常rsp */
	public static final String PAYMENT_ERROR = "{\"checkCode\":\"99\",\"errMsg\":\"繳款紀錄檢查異常\",\"data\":[]}";

	/** 非數存新戶 */
	public static final String IS_NOT_NEW_ACC = "N";

	/** 只有台幣 or 外幣，要辦D3 或 D2連結 */
	public static final String ERROR_DC_NO_SOMEONE_ACC_CONNECT = "/DigitalAC";

	/** 洗錢警示戶代號 */
	public static final String WARNING_ACCOUNT_CODE = "37"; // 告誡戶
	public static final String DANGER_ACCOUNT_CODE = "48"; // 警示戶



	/** 視訊服務 config KeyName */
	public static final String VIDEO_SERVICE_KEY = "videoServiceUrl";

	/** 代償全部銀行 */
	public static final String LOAN_STP_BANK_ALL = "ALL";

	// BNS0604250AQ BNS06042509Q 正確回傳的 StatusCode
	public static final String AQ_9Q_SUCCESS_1 = "0";
	public static final String AQ_9Q_SUCCESS_2 = "0000";
	public static final String AQ_9Q_SUCCESS_3 = "2214";
	public static final String AQ_9Q_NODATA = "0188";

	/** 代償全部銀行_0 */
	public static final String LOAN_STP_BANK_ALL_0 = "0";

	/** 代償部分銀行 */
	public static final String LOAN_STP_BANK_PART = "PART";

	/** 代償部分銀行_1 */
	public static final String LOAN_STP_BANK_PART_1 = "1";

	/** 預約開戶之分行搬遷公告資訊 */
	public static final String ApptBranchChangeInfo = "ApptBranchChangeInfo";

	/** 美元雙幣卡(productId_cc) */
	public static final String DC_CARD_USD = "531001";

	/** 日元雙幣卡(productId_cc) */
	public static final String DC_CARD_JPY = "532001";

	/** 歐元雙幣卡(productId_cc) */
	public static final String DC_CARD_EUR = "533001";

	/** 進度查詢專區 config KeyName */
	public static final String PROGRESS_AREA_URL = "ProgressAreaUrl";
	
	// Mid 取得電信業者代號
	public static final String GET_OPERATOR_LIST = "Operator";

	/**
	 * 信用卡調額 專區
	 */
	// 取得信用卡調額申請原因下拉選單
	public static final String CREDIT_LIMIT_GET_ADJ_APPLY_REASON = "adj_applyReason";
	// 信用卡調額 補件
	public static final String CREDIT_LIMIT_GET_ADJ_APPLY_ACTION_S = "S";
	// 新增CS041申請額度調整電子單API
	public static final String CREDIT_LIMIT_CALL_CS_QRYNAME_SEND = "CS041Send";
	// 補件CS041申請額度調整電子單的API
	public static final String CREDIT_LIMIT_CALL_CS_QRYNAME_SUPPLY = "CS041Supply";
	// 客服電子單上傳附件
	public static final String CREDIT_LIMIT_CALL_CS_QRYNAME_UPLOAD_ATTACH = "UploadAttach";

	/**
	 * 信用卡調額 AioCaseData 狀態
	 */
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_INIT = "30"; // 初始進入（打 /OnboardingCif/CustInfo API成功時）
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_WRITING = "31"; // 填寫中-申請頁
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_WRITING_FIN = "32"; // 填寫中-財力頁
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_WRITE_CS_ERROR = "33"; // FINISH，呼叫 CS /CS041Send、/CS041Supply起案失敗
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_WRITE_CS_SUCCESS = "34"; // FINISH，呼叫 CS /CS041Send、/CS041Supply起案成功
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_NOTIFY_ERROR = "35"; // 收到通知可取資料，但呼叫 CS 時失敗
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_NOTIFY_SUCCESS = "36"; // 收到通知可取資料，呼叫 CS 時成功
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_DATAINFO_ERROR = "37"; // 收到CS 呼 /getInformation，失敗
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_DATAINFO_SUCCESS = "38"; // 收到CS 呼 /getInformation，成功
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_UPLOAD_ATTACH_ERROR = "39"; // 自行上傳，呼叫 CS /UploadAttach上傳失敗
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_UPLOAD_ATTACH_SUCCESS = "40"; // 自行上傳，呼叫 CS /UploadAttach上傳成功

	/**
	 * 自行上傳 -> 上傳 CS 成功
	 * CS 資料資訊取得成功
	 */
	public static final String CREDIT_LIMIT_AIOCASEDATA_STATUS_FINISH_SUCCESS = "39";

	/** CS證轉銀辦數三代號 */
	public static final String CS_SBD = "SBD";
	/** CS證轉銀辦快易抽代號 */
	public static final String CS_SBRD = "SBRD";

	/**
	 * 接斷點 changeView 時，需要用到
	 */
	public static final String MODULE_AIO = "aio";
	public static final String MODULE_APPT = "appt";
	public static final String MODULE_CC = "cc";
	public static final String MODULE_D2 = "d2";
	public static final String MODULE_D3 = "d3";
	public static final String MODULE_DC = "dc";
	public static final String MODULE_HL = "hl";
	public static final String MODULE_LOAN = "loan";
	public static final String MODULE_SAL = "sal";

	public static final String PDF_AIO_EQUALS_CIF = "同留存於本行系統內資料";
	public static final String PDF_CHANGE = "1"; // 判斷既有戶，確認頁的值是否更改過內容(PDF)

	/** xml 高風險 標註 */
	public static final String XML_HIGH_RISK = "正卡疑似洗錢高風險";
	public static final String XML_HIGH_RISK_NOT = "正常";

	/** 信件寄送 type 寄給行內 **/
	public static final String SEND_MAIL_TYPE_KGI = "KGI";

	/** 信件寄送 type 寄給行外 **/
	public static final String SEND_MAIL_TYPE_CLIENT = "client";

	/*複委託TransType:台幣複委託 or 外幣複委託 */
	public static final String Trans_ForeAcct="017";
	public static final String Trans_TwAcct="070";

	/** 85081 StatusCode */
	public static final String StatusCode_85081 ="9922";

	/** 預審拒貸排程撈取時間 config */
	public static final String LOAN_REJECT_TIME = "loanRejectTime";

	public static final String SMARTAPI_RETURN_FAIL = "99"; // 別人的 API 有問題
	public final static String SMARTAPI_VALIDATE_FAIL = "98"; // req 參數轉 OWASPSecurity 有問題
	public final static String SMARTAPI_REQ_FAIL = "97"; // req 參數有缺漏

}
