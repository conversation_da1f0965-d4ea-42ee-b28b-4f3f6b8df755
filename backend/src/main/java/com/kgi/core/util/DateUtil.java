package com.kgi.core.util;

import lombok.extern.slf4j.Slf4j;
// import org.camunda.feel.syntaxtree.In; // 暫時註解

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class DateUtil {
	public static final String YYYYMMDD = "yyyyMMdd" ;

    /**
     * 預設日期格式 /
     */
    public static final String DATE_FORMAT_PATTERN = "yyyy/MM/dd";

    /**
     * 預設日期 - 時間格式 /
     */
    public static final String DATE_TIME_FORMAT_PATTERN = "yyyy/MM/dd HH:mm:ss";


    /**
     * 預設日期格式2 -
     */
    public static final String DATE_FORMAT_PATTERN2 = "yyyy-MM-dd";

    /**
     * 預設日期格式3 (SQL Server TimeStamp 格式 yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String DATE_FORMAT_PATTERN_TIMESTAMP = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 預設日期格式4 (SQL Server TimeStamp 格式 yyyy-MM-dd'T'HH:mm:ss.SSSZ
     */
    public static final String DATE_FORMAT_PATTERN4 = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

    /**
     * 預設日期格式4
     * D3在用的
     */
    public static final String SQL_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss" ;

    public static final String DATE_FORMAT_PATTERN_TIMESTAMP_SLASH = "yyyy/MM/dd HH:mm:ss.SSS";

    public static final String DATE_FORMAT_PATTERN_TIMESTAMP_MILL_ONE = "yyyy-MM-dd HH:mm:ss.S";

    public static final String YYYYMMDDHHMMSSS = DATE_FORMAT_PATTERN_TIMESTAMP;

    public static SimpleDateFormat formatter = new SimpleDateFormat(DATE_FORMAT_PATTERN_TIMESTAMP);

    public static String nowDateString() {
        return DateUtil.GetDateFormatString(DATE_FORMAT_PATTERN_TIMESTAMP);
    }

    public static String nowDateStringAioApiLog() {
        return DateUtil.GetDateFormatString(DATE_FORMAT_PATTERN_TIMESTAMP_SLASH);
    }

    public static String nowTimeStampString() {
        return DateUtil.GetDateFormatString(DATE_FORMAT_PATTERN_TIMESTAMP);
    }


    /** 取得格式化後的日期 (預設 yyyyMMddHHmmss) */
    public static String GetDateFormatString() {
        return GetDateFormatString("yyyyMMddHHmmss");
    }

    /**
     * Get current date.
     *
     * @return string
     */
    public static String getDate() {
        return _datetime(DATE_FORMAT_PATTERN);
    }

    /**
     * 將日期字串轉為 Date 物件，並指定特定格式
     *
     * @param text
     * @return Date
     */
    public static Date parseToDate(String text, String pattern) {
        Date date = null;
        try {
            if(pattern != null) //replace by new pattern
                formatter = new SimpleDateFormat(pattern);

            date = formatter.parse(text);
        }catch (ParseException e){
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 將日期字串轉為 LocalDate 物件
     *
     * @param input
     * @param pattern
     * @return
     */
    public static LocalDate parseToLocalDate(String input, String pattern) {
        return LocalDate.parse(input, DateTimeFormatter.ofPattern(pattern));
    }
    /**
     * 將日期字串轉為 Date 物件
     *
     * @param text
     * @return Date
     */
    public static Date parseToDate(String text) {
        LocalDateTime localDateTime = DateUtil.parse(text);
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 將日期字串轉為 Instant 物件
     *
     * @param text
     * @return Instant
     */
    public static Instant parseToInstant(String text) {
        LocalDateTime localDateTime = DateUtil.parse(text, DATE_FORMAT_PATTERN_TIMESTAMP_SLASH);
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return zdt.toInstant();
    }

    /**
     * 比對字串日期時間大小
     *
     * @param text
     * @return Instant
     */
    public static int compare(String text, String text2) {
        LocalDateTime localDateTime = DateUtil.parse(text, DATE_FORMAT_PATTERN_TIMESTAMP_MILL_ONE);
        LocalDateTime localDateTime2 = DateUtil.parse(text2, DATE_FORMAT_PATTERN_TIMESTAMP_MILL_ONE);
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.of("Asia/Taipei"));
        ZonedDateTime zdt2 = localDateTime2.atZone(ZoneId.of("Asia/Taipei"));

        return zdt.compareTo(zdt2);
    }
    
    /**
     * 比對字串日期時間大小
     *
     * @param text
     * @return Instant
     */
    public static int compare2(String text, String text2) {
        LocalDateTime t1 = DateUtil.parse(text, SQL_DATE_FORMAT);
        LocalDateTime t2 = DateUtil.parse(text2, SQL_DATE_FORMAT);

        int iRtn;
        // 比較日期和時間
        if (t1.isBefore(t2)) {
        	iRtn = 1;
        	// log.debug("t1的日期和時間在t2的定日期和時間之前。");
        } else if (t1.isAfter(t2)) {
        	iRtn = 2;
        	// log.debug("t1的日期和時間在t2的日期和時間之後。");
        } else {
        	iRtn = 3;
        	// log.debug("t1的日期和時間與t2的日期和時間相同。");
        }

        return iRtn;
    }
    
    /**
     * 將日期字串轉為 Instant 物件，並指定特定格式
     *
     * @param text
     * @return Instant
     */
    public static Instant parseToInstant(String text, String pattern) {
        LocalDateTime localDateTime = DateUtil.parse(text, pattern);
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return zdt.toInstant();
    }


    /**
     * 將日期字串轉為 LocalDateTime 物件
     *
     * @param text
     * @return LocalDateTime
     */
    public static LocalDateTime parse(String text) {
        return parse(text, DATE_FORMAT_PATTERN);
    }

    /**
     * 將日期字串轉為 LocalDateTime 物件，並指定特定格式
     *
     * @param text
     * @param pattern
     * @return LocalDateTime
     */
    public static LocalDateTime parse(String text, String pattern) {
        return LocalDateTime.parse(text, DateTimeFormatter.ofPattern(pattern));
    }
    /**
     * Get current date.
     * 1. (根據傳入的 指定格式 回傳今日日期)
     *
     * @param formatPattern
     * @return String
     */
    public static String getDate(String formatPattern) {
        return _datetime(formatPattern);
    }
    /**
     * Get current date/time.
     *
     * @return string
     */
    public static String getDatetime() {
        return _datetime("yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 格式化8碼日期
     * */
    public static String addDateSlash(String date) {
    	return addDateSlash(date, "/") ;

    }


    public static String addDateSlash(String date, String concat) {
    	if (date == null || date.length() != 8) {
    		return date ;
    	}

    	return new StringBuffer(date.substring(0, 4)).append(concat).append(date.substring(4, 6)).append(concat).append(date.substring(6, 8)).toString() ;

    }
    /**
     * Assign return data format.
     *
     * @param format
     * @return string
     */
    private static String _datetime(String format) {
        return _datetime(format, new Date());
    }
    /**
     * Assign return data format.
     *
     * @param format
     * @return string
     */
    private static String _datetime(String format, Date date) {
        SimpleDateFormat formater = new SimpleDateFormat(format);
        return formater.format(date).toString();
    }

    /**
     * 移除日期分隔號
     * */
    public static String removeDateSlash(String date) {
    	if (date == null || date.length() != 10) {
    		return date ;
    	}
    	return date.substring(0, 4) + date.substring(5, 7) + date.substring(8, 10) ;

    }

    /**
     * 取得格式化後的日期
     *
     * @param format 日期格式字串
     */
    public static String GetDateFormatString(String format) {
        return GetDateFormatString(new Date(), format);
    }

    public static String GetDateFormatString(Date date, String format) {
        if(date == null){
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }

    public static String GetDateFotmatString(Calendar cal, String format) {
        DateFormat df = new SimpleDateFormat(format);
        return df.format(cal.getTime());
    }



    public static void main(String argc[]) {
        DateUtil dateUtil = new DateUtil();
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN_TIMESTAMP);
        Instant.now().atZone(ZoneId.of("Asia/Taipei")).format(dateFormat);
        int test = dateUtil.compare(Instant.now().atZone(ZoneId.of("Asia/Taipei")).format(dateFormat), "2023-08-03 16:29:01.310");
        System.out.println(test);
    }

    public static LocalDate getLocalDate(String yyyyMMdd) {
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate localDate = LocalDate.parse(yyyyMMdd, format);
        return localDate;
    }

    public static LocalDate getLocalDatePattern2(String yyyyMMdd) {
        DateTimeFormatter format = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN2);
        LocalDate localDate = LocalDate.parse(yyyyMMdd, format);
        return localDate;
    }

    public static int calculateAge(String yyyyMMdd) {
        return calculateAge(getLocalDate(yyyyMMdd));
    }

    public static int calculateAgePattern2(String yyyyMMdd) {
        return calculateAge(getLocalDatePattern2(yyyyMMdd));
    }

    public static int calculateAge(LocalDate birthDate) {
        LocalDate currentDate = LocalDate.now();
        return calculateAge(birthDate, currentDate);
    }

    public static int calculateAge(LocalDate birthDate, LocalDate currentDate) {
        if ((birthDate != null) && (currentDate != null)) {
            return Period.between(birthDate, currentDate).getYears();
        } else {
            return 0;
        }
    }

    public static String getInstantDateTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
        return Instant.now().atZone(ZoneId.of("GMT+8")).format(formatter);
    }

    public static LocalDateTime dateToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}
