package com.kgi.core.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.Formatter;

@Slf4j
public class EncryptUtils {

	private static final String HMAC_SHA1_ALGORITHM ="HmacSHA1";

	private static final String ENCRYPT_KY = "9abdb139-d03c-4b67-8fb6-28be665b8e45";

	/**
	 * Base64 encode
	 */
	public static String base64Encode(String data) {
		return Base64.encodeBase64String(data.getBytes());
	}

	/**
	 * Base64 encode
	 */
	public static String base64EncodeByUTF8(String data) {
		return Base64.encodeBase64String(data.getBytes(StandardCharsets.UTF_8));
	}

	/**
	 * Base64 decode
	 *
	 * @throws UnsupportedEncodingException
	 */
	public static String base64Decode(String data) throws UnsupportedEncodingException {
		return new String(Base64.decodeBase64(data.getBytes()), "utf-8");
	}

	/**
	 * md5
	 */
	public static String md5Hex(String data) {
		return DigestUtils.md5Hex(data);
	}

	/**
	 * sha1
	 */
	public static String sha1Hex(String data) {
		return DigestUtils.sha1Hex(data);
	}

	/**
	 * sha256
	 */
	public static String sha256Hex(String data) {
		return DigestUtils.sha256Hex(data);
	}

	/**
	 * For AWS Content-MD5
	 * @param data
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String getBase64M_D_5(String data) throws UnsupportedEncodingException {
		byte[] source = data.getBytes("UTF-8");
		byte[] crypto = DigestUtils.md5(source);
		String result = new String(new Base64().encodeBase64String(crypto));
		return result;
	}

	private static String toHexString(byte[] bytes) {
		Formatter formatter = new Formatter();
		for (byte b : bytes) {
		formatter.format("%02x", b);
		}
		return formatter.toString();
	}

	/**
	 * For AWS Authorization
	 * @param data
	 * @param key
	 * @return
	 * @throws SignatureException
	 * @throws NoSuchAlgorithmException
	 * @throws InvalidKeyException
	 * @throws UnsupportedEncodingException
	 */
	public static String calculateRFC2104HMAC(String data, String key) throws SignatureException, NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
//		SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
		SecretKeySpec signingKey = new SecretKeySpec(key.getBytes("UTF-8"), HMAC_SHA1_ALGORITHM);
		Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
		mac.init(signingKey);
		return Base64.encodeBase64String(mac.doFinal(data.getBytes("UTF-8")));
	}

	public static void main(String[] args) throws Exception {
	}

}
