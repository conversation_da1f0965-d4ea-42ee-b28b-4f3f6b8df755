package com.kgi.core.util.crypt;

public class K<PERSON>OBDAES<PERSON>til extends MCrypt {

	private static volatile KGIOBDAESUtil instance;

	public KGIOBDAESUtil(String sKey,String iv) {
//		super("tqhYgN4Xe0XFOSdn", "DyDgD3HPbZWxqAo4");
		super(sKey, iv);
	}

	public static KGIOBDAESUtil getInstance(String sKey,String iv) {
		if (instance == null) {
			synchronized (KGIOBDAESUtil.class) {
				instance = new KGIOBDAESUtil(sKey,iv);
			}
		}
		return instance;
	}

}
