<?xml version="1.0" encoding="UTF-8"?>
<!-- 日誌級別從低到高分為 TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果設置為WARN，則低於WARN的訊息都不會輸出 -->
<!-- scan:當此屬性設置為true時，配置檔案如果發生改變，將會被重新加載，預設值為true -->
<!-- scanPeriod:設置監測配置檔案是否有修改的時間間隔，如果沒有給出時間單位，預設單位是毫秒。 當scan為true時，此屬性生效。預設的時間間隔為1分鐘。 -->
<!-- debug:當此屬性設置為true時，將列印出logback內部日誌訊息，實時查看logback運行狀態。預設值為false。 -->
<configuration  scan="true" scanPeriod="10 seconds">

	<contextName>logback</contextName>

	<!-- 文件切割大小 -->
	<property name="maxFileSize" value="500MB"/>
	<!-- 檔案保留天數 -->
	<property name="maxHistory" value="20"/>
	<!-- 檔案保留總大小 -->
	<property name="totalSizeCap" value="50GB"/>

	<!-- name的值是變量的名稱，value的值時變量定義的值。通過定義的值會被插入到logger上下文中。定義後，可以使“${}”來使用變量。 -->
	<property name="LOG_PATH" value="/logs" />

	<!--0. 日誌格式和顏色渲染 -->
	<!-- 彩色日誌依賴的渲染類 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
	<conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
	<conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

	<!-- 彩色日誌格式 -->
	<property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

	<!--1. 輸出到控制台-->
	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<!--此日誌appender是為開發使用，只配置最底級別，控制台輸出的日誌級別是大於或等於此級別的日誌訊息-->
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<!-- ERROR, DEBUG -->
			<level>ERROR</level>
		</filter>
		<encoder>
			<Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
			<!-- 設置字符集 -->
			<charset>UTF-8</charset>
		</encoder>
	</appender>

	<!--2. 輸出到檔案-->
	<!-- 2.1 level為 DEBUG 日誌，時間滾動輸出  -->
	<appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在記錄的日誌檔案的路徑及檔案名 -->
		<file>${LOG_PATH}/server_debug.log</file>
		<!--日誌檔案輸出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset> <!-- 設置字符集 -->
		</encoder>
		<!-- 日誌記錄器的滾動策略，按日期，按大小記錄 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 輸出文件的位置，以每一天做切割-->
			<fileNamePattern>${LOG_PATH}/web-debug-%d{yyyy-MM-dd}.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--日誌檔案保留天數-->
			<maxHistory>30</maxHistory>
		</rollingPolicy>

		<!-- 此日誌檔案只記錄debug級別的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>debug</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 2.2 level為 INFO 日誌，時間滾動輸出  -->
	<appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在記錄的日誌檔案的路徑及檔案名 -->
		<file>${LOG_PATH}/server_info.log</file>
		<!--日誌檔案輸出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset>
		</encoder>
		<!-- 日誌記錄器的滾動策略，按日期，按大小記錄 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 輸出文件的位置，以每一天做切割-->
			<fileNamePattern>${LOG_PATH}/web-info-%d{yyyy-MM-dd}.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--日誌檔案保留天數-->
			<maxHistory>15</maxHistory>
		</rollingPolicy>
		<!-- 此日誌檔案只記錄info級別的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>info</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 2.3 level為 WARN 日誌，時間滾動輸出  -->
	<appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在記錄的日誌檔案的路徑及檔案名 -->
		<file>${LOG_PATH}/server_warn.log</file>
		<!--日誌檔案輸出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset> <!-- 此處設置字符集 -->
		</encoder>
		<!-- 日誌記錄器的滾動策略，按日期，按大小記錄 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 輸出文件的位置，以每一天做切割-->
			<fileNamePattern>${LOG_PATH}/web-warn-%d{yyyy-MM-dd}.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--日誌檔案保留天數-->
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<!-- 此日誌檔案只記錄warn級別的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- 2.4 level為 ERROR 日誌，時間滾動輸出  -->
	<appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!-- 正在記錄的日誌檔案的路徑及檔案名 -->
		<file>${LOG_PATH}/server_error.log</file>
		<!--日誌檔案輸出格式-->
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
			<charset>UTF-8</charset> <!-- 此處設置字符集 -->
		</encoder>
		<!-- 日誌記錄器的滾動策略，按日期，按大小記錄 -->
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- 輸出文件的位置，以每一天做切割-->
			<fileNamePattern>${LOG_PATH}/web-error-%d{yyyy-MM-dd}.log</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--日誌檔案保留天數-->
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<!-- 此日誌檔案只記錄ERROR級別的 -->
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--
        <logger>用來設置某一個package或者具體的某一個類的日誌列印級別、以及指定<appender>。<logger>僅有一個name屬性， 一個可選的level和一個可選的 addtivity 屬性。
        name:用來指定受此logger約束的某一個包或者具體的某一個類。
        level:用來設置列印級別，大小寫無關：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，
              還有一個特俗值INHERITED或者同義詞NULL，代表強制執行上級的級別。
              如果未設置此屬性，那麽當前logger將會繼承上級的級別。
        addtivity:是否向上級logger傳遞列印訊息。預設是true。

        <logger name="org.springframework.web" level="info"/>
        <logger name="org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor" level="INFO"/>
    -->

	<logger name="com.kgi.workflow" level="DEBUG" additivity="false"/>

	<logger name="org.camunda.bpm" level="DEBUG" additivity="false"/>

	<!--
        root節點是必選節點，用來指定最基礎的日誌輸出級別，只有一個level屬性
        level:用來設置列印級別，大小寫無關：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，不能設置為INHERITED或者同義詞NULL。預設是DEBUG
        ，標識這個appender將會添加到這個logger。
    -->

	<root level="debug">
		<appender-ref ref="CONSOLE" />
		<!--    <appender-ref ref="DEBUG_FILE" />-->
		<!--    <appender-ref ref="INFO_FILE" />-->
		<!--    <appender-ref ref="WARN_FILE" />-->
		<!--    <appender-ref ref="ERROR_FILE" />-->
	</root>


</configuration>
