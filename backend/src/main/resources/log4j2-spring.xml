<?xml version="1.0" encoding="UTF-8"?>

<Configuration status="WARN">
	<Properties>
		<Property name="LOG_ROOT">./logs</Property>
		<Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %p %logger {thread_id="%tid"} - %msg%n</Property>
		<Property name="LOG_PATTERN_SHORT">%d{yyyy-MM-dd HH:mm:ss} %p %class{0} - %msg%n</Property>
	</Properties>

	<Appenders>

		<Console name="Console" target="SYSTEM_OUT" follow="true">
			<PatternLayout
				pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{thread_id='%tid'} - %msg%n" />
		</Console>

		<!--文件會印出所有訊息，這個log每次執行程序會自動清空，由append屬性決定，適合臨時測試用 -->
		<RollingFile
				name="serverFileLog"
				fileName="${LOG_ROOT}/server.log"
				filePattern="${LOG_ROOT}/server-%d{yyyy-MM-dd}-%i.log"
				append="true"
				ignoreExceptions="false">
			<PatternLayout pattern="${LOG_PATTERN}" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<!-- <TimeBasedTriggeringPolicy filePattern="${LOG_ROOT}/server.%d{yyyy-mm-dd}.log.gz" />-->
				<SizeBasedTriggeringPolicy size="20MB" />
			</Policies>
			<DefaultRolloverStrategy max="25" />
			<Filters>
				<!-- ACCEPT, DENY, NEUTRAL 如果 log.info 包括此 Marker 所做的處置 -->
				<MarkerFilter marker="serverFileLog" onMatch="ACCEPT" onMismatch="NEUTRAL" /> <!-- 符合 filter才列印, 其他中立不處置 -->
				<MarkerFilter marker="concurrentLog" onMatch="DENY" onMismatch="NEUTRAL" /> <!-- 符合 filter才列印, 其他中立不處置 -->
				<!--如果日誌事件LogEvent中的日誌等級為${level}及以上，則接受這個日誌事件-->
				<!-- <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/> -->
			</Filters>
		</RollingFile>

		<RollingFile
				name="caseLog"
				fileName="${LOG_ROOT}/case.log"
				filePattern="${LOG_ROOT}/case-%d{yyyy-MM-dd}-%i.log"
				append="true"
				ignoreExceptions="false">
			<PatternLayout pattern="${LOG_PATTERN}" />
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" modulate="true" />
				<SizeBasedTriggeringPolicy size="20MB" />
			</Policies>
			<DefaultRolloverStrategy max="25" />
		</RollingFile>

	</Appenders>

	<Loggers>
		<!-- 正式上線時可把 level 改為 INFO or WARN/ERROR-->
		<Root level="INFO" additivity="false">
			<AppenderRef ref="Console" />
			<AppenderRef ref="serverFileLog" />
		</Root>

		<Logger name="org.springframework.web" level="OFF">
<!--		<Logger name="org.springframework.web" level="ERROR">-->
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.core" level="INFO">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.boot" level="INFO">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.context" level="WARN">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.beans" level="WARN">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="com.zaxxer" level="WARN">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.boot.devtools" level="OFF">
			<AppenderRef ref="Console" />
		</Logger>

		<!-- Log everything in hibernate -->
		<Logger name="org.hibernate" level="ERROR" additivity="false">
            <AppenderRef ref="Console" />
        </Logger>

		<!-- Log SQL statements -->
		<Logger name="org.hibernate.SQL" level="INFO" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>

		<!-- Log JDBC bind parameters: TRACE / ERROR / INFO / OFF -->
		<Logger name="org.hibernate.type.descriptor.sql" level="INFO" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="org.springframework.data" level="INFO" additivity="false">
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="com.kgi.workflow" level="DEBUG" additivity="false">
			<AppenderRef ref="workflowFileLog" />
			<AppenderRef ref="Console" />
		</Logger>

		<Logger name="com.kgi" level="DEBUG" additivity="false">
			<AppenderRef ref="Console" />
			<AppenderRef ref="serverFileLog" />
		</Logger>

		<Logger name="com.kgi.core.service.concurrent.DatabaseManagementService" level="DEBUG" additivity="false">
			<appender-ref ref="serverFileLog" />
			<AppenderRef ref="concurrentLog" />
			<AppenderRef ref="Console" />
		</Logger>

		<!--  org.camunda.bpm.engine.OptimisticLockingException: ENGINE-03005 Execution of ‘UPDATE ExecutionEntity-->
		<!--  https://forum.camunda.org/t/org-camunda-bpm-engine-optimisticlockingexception-engine-03005-execution-of-update-executionentity/14880/5-->
		<Logger name="org.camunda.bpm.engine.cmd" level="WARN" additivity="false">
			<AppenderRef ref="Console" />
			<AppenderRef ref="serverFileLog" />
		</Logger>

		<Logger name="org.camunda.bpm.engine.impl.persistence" level="WARN" additivity="false">
			<AppenderRef ref="Console" />
			<AppenderRef ref="serverFileLog" />
		</Logger>

		<Logger name="com.kgi.core.service.AioCaseDataService" level="DEBUG" additivity="false">
			<AppenderRef ref="caseLog" />
			<AppenderRef ref="Console" />
		</Logger>

	</Loggers>

</Configuration>
