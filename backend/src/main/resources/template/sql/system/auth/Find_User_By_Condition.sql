SELECT
    tab1.UserID           AS UserID,
    tab1.UserName         AS UserName,
    tab1.Passwd           AS Passwd,
    tab1.EmployeeID       AS EmployeeID,
    tab1.StatusCode       AS StatusCode,
    tab1.ADFlag           AS ADFlag,
    tab1.BusinessEntityID AS BusinessEntityID,
    tab2.BusEntName       AS BusEntName,
    tab2.ParentBusEntID   AS ParentBusEntID
FROM
    MDUser tab1
LEFT JOIN
    BusinessEntity tab2
ON
    tab1.BusinessEntityID = tab2.BusinessEntityID
WHERE
    tab1.StatusCode = 'Y'
AND tab1.UserName = '$P{userName}'
AND tab1.Passwd = '$P{passwd}'
