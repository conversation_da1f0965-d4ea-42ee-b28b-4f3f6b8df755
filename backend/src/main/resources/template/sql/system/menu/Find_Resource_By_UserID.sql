--- 根據登入人員的 ID 取得其角色對應的 選單資源
SELECT
    Tb2.ResourceCateID   AS ResourceCateID,
    Tb2.ResourceCateName AS ResourceCateName,
    Tb2.IconTag          AS IconTag,
    Tb1.ResourceID       AS ResourceID,
    Tb1.ResourceName     AS ResourceName,
    Tb4.API_Name         AS API_Name,
    Tb4.Description      AS Description
FROM
    MDResource Tb1
LEFT JOIN
    MDResourceCate Tb2
ON
    Tb1.ResourceCateID = Tb2.ResourceCateID
LEFT JOIN
    MDResourceAPI Tb3
ON
    Tb1.ResourceID = Tb3.ResourceID
LEFT JOIN
    MDAPI Tb4
ON
    Tb3.APIID = Tb4.APIID
WHERE
    Tb2.EmmaPhase = '3'
AND Tb1.ResourceID IN
(
    SELECT DISTINCT
        Tb3.ResourceId
    FROM
        MDUserGroup Tb1
    LEFT JOIN
        MDFGroupRole Tb2
    ON
        Tb1.FGroupID = Tb2.FGroupID
    LEFT JOIN
        MDRoleResource Tb3
    ON
        Tb2.RoleID = Tb3.RoleID
    WHERE
        UserID = '$P{userId}' 
)
