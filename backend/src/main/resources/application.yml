# IBR 數位跨境匯款解付平台共用設定
ibr:
  application:
    name: "KGI數位跨境匯款解付平台"
    version: "1.0.0"
  api:
    base-path: "/api/ibr"
    version: "v1"
    timeout: 30000
  security:
    jwt:
      secret: "kgi-ibr-jwt-secret-key-2024"
      expiration: 86400000  # 24小時
      refresh-expiration: *********  # 7天
  # OTP 服務配置
  otp:
    length: 6
    validity: 300    # 5分鐘
    max-attempts: 3
    retry-interval: 60
    daily-limit: 10
  # SMS 服務配置  
  sms:
    enabled: true
    sender-name: "KGI-IBR"
  business:
    exchange-rate:
      cache-duration-minutes: 15
      api-timeout-seconds: 10
    remittance:
      max-amount-usd: 50000
      max-amount-twd: 1500000
      processing-timeout-hours: 24
    verification:
      id-verify-timeout-minutes: 10
      bank-verify-timeout-minutes: 5

spring:
  # H2 資料庫配置（開發環境 - 使用 AUTO_SERVER 模式）
  datasource:
    driver-class-name: org.h2.Driver
    # AUTO_SERVER=TRUE 允許多個進程同時連接
    url: jdbc:h2:file:./db/kgi-ibr;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1
    username: sa
    password: 
  # KGI 資料庫配置
  datasource-kgi:
    driver-class-name: org.h2.Driver
    # AUTO_SERVER=TRUE 允許多個進程同時連接
    url: jdbc:h2:file:./db/kgi-ibr;AUTO_SERVER=TRUE;DB_CLOSE_DELAY=-1
    username: sa
    password: 
    encrypt: false
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 10
      idle-timeout: 600000
      minimum-idle: 5
      max-lifetime: 1800000
      auto-commit: true
      connection-test-query: SELECT 1
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration
  lifecycle:
    timeout-per-shutdown-phase: 5s
  liquibase:
    change-log: classpath:db/changelog/master.yaml
  application:
    name: kgi-ibr
  profiles:
    active: local
  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: update  # 自動建立/更新資料表結構
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        cache:
          use_second_level_cache: false
          use_query_cache: false
  servlet:
    multipart:
      max-file-size: 30MB
      max-request-size: 30MB
  jackson:
    time-zone: Asia/Taipei
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  webservices:
    servlet:
      init:
        transformWsdlLocations: true
  web:
    resources:
      cache-period: 0
      static-locations: /WEB-INF/,/resources,classpath:/META-INF/resources/,classpath:/static/:#
  main:
    banner-mode: off
  # Quartz 排程器配置（使用內存存儲，避免資料庫表格問題）
  quartz:
    job-store-type: memory
    jdbc:
      initialize-schema: never
    properties:
      org:
        quartz:
          jobStore:
            class: org.quartz.simpl.RAMJobStore

# Management endpoints configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info
      base-path: /actuator
  endpoint:
    health:
      show-details: always
      enabled: true
  health:
    defaults:
      enabled: true

server:
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  error:
    include-stacktrace: on_param
    include-message: always
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json

logging:
  config: classpath:log4j2-spring.xml
  level:
    root: INFO
    com.kgi: DEBUG
    com.kgi.module.individual: DEBUG
    org:
      springframework:
        boot:
          autoconfigure: off
        security: DEBUG
        web: DEBUG

# Camunda BPM 配置（暫時使用預設值）
camunda:
  bpm:
    generic-user:
      id: admin
      password: admin

# GlobalConfig 所需的配置屬性（暫時使用預設值）
AbbeyRye: "defaultAbbeyRye"
RayakRoe: "defaultRayakRoe"
StaticFilePath: "./static"
PdfDirectoryPath: "./pdf"
ConfigTableName: "CONFIG"
OTPOpID: "OTP001"
OTPBillDep: "BILL001"
MailOpID: "MAIL001"
ValidationServerUrl: "http://localhost:8080/validation"
AirLoanWebServerHost: "http://localhost:8080"
AirLoanDomainWebServerHost: "http://localhost:8080"
AirLoanAPServerHost: "http://localhost:8080"
BookingDay: "15"
BookingMonth: "1"
KGI_WQ_CASE_DATA_URL: "http://localhost:8080/api/case-data"
KGI_WQ_CASE_DATA_NAME: "CaseData"
KGI_GET_PLOAN_STATUS_INFO_URL: "http://localhost:8080/api/ploan-status"
KGI_GET_PLOAN_STATUS_INFO_NAME: "PLoanStatus"
EDDA_API_HOST: "http://localhost:8080"

# KGI 其他配置
kgi:
  # Repository 實作模式配置
  repository:
    type: memory                   # memory | jpa - 預設使用記憶體模式
    # 未來可以透過 profile 切換：
    # - local: memory
    # - dev/sit/uat: jpa
    # - prod: jpa
  
  # Mock 服務配置（本地開發環境啟用）
  useMockupData: true              # 使用 mock 資料
  useMockupCallOut: true           # 使用 mock 外部呼叫
  mockupServer: true               # 使用 mock 伺服器
  mockupServerUrl: http://localhost:3000  # Mock 伺服器地址
  useMockupFTP: true               # 使用 mock FTP
  useColdStart: false              # 不使用冷啟動
  
  # Mock 延遲設定（模擬真實環境延遲）
  mock:
    delay:
      enabled: true                # 啟用延遲
      min: 100                     # 最小延遲（毫秒）
      max: 500                     # 最大延遲（毫秒）
    error:
      rate: 0.0                    # 錯誤率（0.0 = 0%, 0.1 = 10%）
    
  # Mock 資料設定
  mockData:
    otp:
      code: "123456"               # 測試用 OTP 碼
      validityMinutes: 5           # OTP 有效期限
    exchange:
      rate:
        USD: 32.36                 # USD 對 TWD 匯率
        GBP: 40.12                 # GBP 對 TWD 匯率
        EUR: 35.28                 # EUR 對 TWD 匯率
    remittance:
      processingTime: 2000         # 匯款處理時間（毫秒）
      successRate: 1.0             # 成功率（1.0 = 100%）

# 重定向 URL
KGI_REDIRECT_URL: "http://localhost:4200"

# Spring Mail 配置（使用預設值）
# spring.mail 配置已經在 @Value 註解中有預設值，不需要在這裡配置
