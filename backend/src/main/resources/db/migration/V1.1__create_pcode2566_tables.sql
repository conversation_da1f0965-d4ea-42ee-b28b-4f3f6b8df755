-- pCode2566 模組資料表建立腳本
-- 保持與原系統 CreditVerify 表相容

-- 1. 帳戶驗證表（新表，保留原 CreditVerify 表結構）
CREATE TABLE IF NOT EXISTS account_verification (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    verification_id VARCHAR(50) UNIQUE NOT NULL,
    uniq_id VARCHAR(50) NOT NULL,
    uniq_type VARCHAR(20),
    bank_code VARCHAR(3) NOT NULL,
    bank_name VARCHAR(50),
    account_number VARCHAR(20) NOT NULL,
    id_number VARCHAR(10) NOT NULL,
    birthday VARCHAR(8) NOT NULL,
    phone_number VARCHAR(10) NOT NULL,
    verification_code VARCHAR(10),
    verification_message VARCHAR(255),
    identity_status VARCHAR(1),
    account_status VARCHAR(1),
    open_status VARCHAR(2),
    phone_changed_within_90_days BIT DEFAULT 0,
    error_count INT DEFAULT 0,
    raw_response TEXT,
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    verified_at DATETIME,
    INDEX idx_verification_uniq_id (uniq_id),
    INDEX idx_verification_bank_account (bank_code, account_number),
    INDEX idx_verification_created_at (created_at)
);

-- 2. 建立相容性觸發器（同步更新 CreditVerify 表）
-- 這樣可以保持原系統繼續運作
/*
CREATE TRIGGER sync_to_credit_verify
ON account_verification
AFTER INSERT, UPDATE
AS
BEGIN
    -- 同步新增或更新的資料到 CreditVerify 表
    MERGE CreditVerify AS target
    USING (SELECT * FROM inserted) AS source
    ON target.uniqId = source.uniq_id
    WHEN MATCHED THEN
        UPDATE SET
            target.authErrCount = source.error_count,
            target.uTime = GETDATE()
    WHEN NOT MATCHED THEN
        INSERT (uniqId, uniqType, authErrCount, cTime, uTime)
        VALUES (source.uniq_id, source.uniq_type, source.error_count, source.created_at, GETDATE());
END;
*/