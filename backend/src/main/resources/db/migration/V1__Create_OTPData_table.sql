-- 建立 OTPData 表
CREATE TABLE IF NOT EXISTS OTPData (
    UniqId VARCHAR(255) NOT NULL PRIMARY KEY,
    UniqType VARCHAR(50) NOT NULL,
    PhoneNum VARCHAR(20) NOT NULL,
    OTPSource VARCHAR(50) NOT NULL,
    UTime TIMESTAMP NOT NULL,
    OTPType VARCHAR(50) NOT NULL,
    Email VARCHAR(255),
    Phone VARCHAR(20),
    OtpCode VARCHAR(10),
    CreateTime TIMESTAMP,
    TxnId VARCHAR(255),
    TxnDate TIMESTAMP,
    Sk VARCHAR(255)
);

-- 建立索引
CREATE INDEX idx_otpdata_uniqid_txnid ON OTPData(UniqId, TxnId);