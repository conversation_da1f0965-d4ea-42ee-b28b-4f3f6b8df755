-- OTP 模組增強腳本
-- 保持與原系統 OTPData 表相容，只增加索引優化查詢

-- 1. 為 OTPData 表增加索引以優化查詢（如果表已存在）
IF EXISTS (SELECT * FROM sysobjects WHERE name='OTPData' AND xtype='U')
BEGIN
    -- 優化根據 uniqId 查詢
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_otp_uniq_id')
        CREATE INDEX idx_otp_uniq_id ON OTPData(uniqId);
    
    -- 優化根據 sk 和 txnId 查詢
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_otp_session')
        CREATE INDEX idx_otp_session ON OTPData(SK, txnId);
    
    -- 優化時間查詢
    IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'idx_otp_time')
        CREATE INDEX idx_otp_time ON OTPData(cTime DESC, uTime DESC);
END;

-- 2. OTP 配置表（可選，用於管理 OTP 設定）
CREATE TABLE IF NOT EXISTS otp_configuration (
    config_key VARCHAR(50) PRIMARY KEY,
    config_value VARCHAR(255) NOT NULL,
    description VARCHAR(500),
    updated_at DATETIME DEFAULT GETDATE()
);

-- 3. 插入預設配置（SQL Server 語法）
IF NOT EXISTS (SELECT 1 FROM otp_configuration WHERE config_key = 'SEND_INTERVAL_SECONDS')
    INSERT INTO otp_configuration (config_key, config_value, description)
    VALUES ('SEND_INTERVAL_SECONDS', '60', '發送間隔（秒）');

IF NOT EXISTS (SELECT 1 FROM otp_configuration WHERE config_key = 'EXPIRE_MINUTES')
    INSERT INTO otp_configuration (config_key, config_value, description)
    VALUES ('EXPIRE_MINUTES', '5', 'OTP有效期（分鐘）');

IF NOT EXISTS (SELECT 1 FROM otp_configuration WHERE config_key = 'MAX_ATTEMPTS')
    INSERT INTO otp_configuration (config_key, config_value, description)
    VALUES ('MAX_ATTEMPTS', '3', '最大嘗試次數');

IF NOT EXISTS (SELECT 1 FROM otp_configuration WHERE config_key = 'DAILY_LIMIT')
    INSERT INTO otp_configuration (config_key, config_value, description)
    VALUES ('DAILY_LIMIT', '10', '每日發送上限');

IF NOT EXISTS (SELECT 1 FROM otp_configuration WHERE config_key = 'SMS_TEMPLATE')
    INSERT INTO otp_configuration (config_key, config_value, description)
    VALUES ('SMS_TEMPLATE', '動態密碼為{0}為凱基銀行線上申請', '簡訊模板');