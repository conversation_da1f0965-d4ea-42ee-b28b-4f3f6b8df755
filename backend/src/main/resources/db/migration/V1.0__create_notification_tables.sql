-- Notification 模組資料表建立腳本
-- 保持與原系統相容，只新增必要的表格

-- 1. 通知歷史表（相容原 MailHunterHistory 結構）
CREATE TABLE IF NOT EXISTS notification_history (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    notification_id VARCHAR(50) UNIQUE NOT NULL,
    channel VARCHAR(10) NOT NULL,
    recipient VARCHAR(100) NOT NULL,
    template_id VARCHAR(50),
    parameters TEXT,
    status VARCHAR(2) NOT NULL DEFAULT '0',
    content TEXT,
    subject VARCHAR(255),
    attachment VARCHAR(500),
    retry_count INT DEFAULT 0,
    error_message VARCHAR(500),
    uniq_id VARCHAR(50),
    uniq_type VARCHAR(20),
    created_at DATETIME NOT NULL DEFAULT GETDATE(),
    sent_at DATETIME,
    updated_at DATETIME,
    INDEX idx_notification_status (status),
    INDEX idx_notification_uniq_id (uniq_id),
    INDEX idx_notification_created_at (created_at)
);

-- 2. 通知模板表
CREATE TABLE IF NOT EXISTS notification_template (
    template_id VARCHAR(50) PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    channel VARCHAR(10) NOT NULL,
    subject VARCHAR(255),
    content TEXT NOT NULL,
    required_parameters VARCHAR(500),
    active BIT NOT NULL DEFAULT 1,
    INDEX idx_template_channel (channel)
);

-- 3. 插入預設模板
INSERT INTO notification_template (template_id, template_name, channel, subject, content, required_parameters, active)
VALUES 
    ('OTP_SMS', 'OTP簡訊模板', 'SMS', NULL, '動態密碼為{otpCode}為凱基銀行線上申請', 'otpCode', 1),
    ('OTP_EMAIL', 'OTP郵件模板', 'EMAIL', '凱基銀行驗證碼', '親愛的客戶您好：\n\n您的驗證碼為：{otpCode}\n此驗證碼將於{expireTime}分鐘後失效，請盡速完成驗證。\n\n凱基銀行 敬上', 'otpCode,expireTime', 1),
    ('REMITTANCE_SUCCESS_SMS', '匯款成功簡訊', 'SMS', NULL, '您的匯款{amount}元已成功，交易序號：{transactionId}', 'amount,transactionId', 1),
    ('REMITTANCE_SUCCESS_EMAIL', '匯款成功郵件', 'EMAIL', '凱基銀行匯款通知', '親愛的客戶您好：\n\n您的匯款已成功處理。\n匯款金額：{amount}元\n交易序號：{transactionId}\n匯款時間：{dateTime}\n\n凱基銀行 敬上', 'amount,transactionId,dateTime', 1);

-- 4. 建立相容性檢視（如果需要保持與舊表的相容）
-- CREATE VIEW MailHunterHistory AS
-- SELECT 
--     id,
--     notification_id as mail_id,
--     recipient as email,
--     subject,
--     content,
--     status,
--     created_at as create_time,
--     sent_at as send_time
-- FROM notification_history
-- WHERE channel = 'EMAIL';