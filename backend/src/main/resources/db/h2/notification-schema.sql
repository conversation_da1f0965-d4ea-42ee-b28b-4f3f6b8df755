-- Notification 模組 H2 資料庫 Schema
-- 對應原始系統的 MailHunterHistory 和 SMSHunterHistory 表

-- 1. 通知歷史記錄表 (統一處理 Email 和 SMS)
CREATE TABLE IF NOT EXISTS notification_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    notification_id VARCHAR(100) NOT NULL UNIQUE,
    channel VARCHAR(20) NOT NULL, -- EMAIL, SMS
    recipient VARCHAR(255) NOT NULL, -- Email address or phone number
    template_id VARCHAR(50),
    parameters CLOB, -- JSON format
    status CHAR(1) NOT NULL DEFAULT '0', -- 0=待發送, 1=已發送, 2=發送中, 7=失敗, 9=取消
    content CLOB,
    subject VARCHAR(500), -- For email
    attachment VARCHAR(500), -- File path for email attachments
    retry_count INT DEFAULT 0,
    error_message VARCHAR(1000),
    uniq_id VARCHAR(50), -- 業務案件編號
    uniq_type VARCHAR(20), -- IND, COR
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sent_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_uniq_id (uniq_id),
    INDEX idx_channel (channel),
    INDEX idx_created_at (created_at)
);

-- 2. 通知模板表
CREATE TABLE IF NOT EXISTS notification_template (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_id VARCHAR(50) NOT NULL UNIQUE,
    template_name VARCHAR(100) NOT NULL,
    channel VARCHAR(20) NOT NULL,
    subject VARCHAR(500), -- Email subject template
    content CLOB NOT NULL, -- Content template with placeholders
    required_parameters VARCHAR(1000), -- JSON array of required parameter names
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_template_id (template_id),
    INDEX idx_channel (channel)
);

-- 3. 插入預設模板資料
INSERT INTO notification_template (template_id, template_name, channel, subject, content, required_parameters) 
VALUES 
('OTP_SMS', 'OTP簡訊通知', 'SMS', NULL, '您的驗證碼為: ${otpCode}，請於5分鐘內完成驗證。', '["otpCode"]'),
('OTP_EMAIL', 'OTP郵件通知', 'EMAIL', 'KGI數位跨境匯款 - 驗證碼通知', '<p>親愛的客戶您好：</p><p>您的驗證碼為: <strong>${otpCode}</strong></p><p>請於5分鐘內完成驗證。</p><p>凱基銀行 敬上</p>', '["otpCode"]'),
('REMITTANCE_NOTIFICATION', '匯款通知', 'EMAIL', 'KGI數位跨境匯款 - 匯款狀態通知', '<p>親愛的客戶您好：</p><p>您的匯款申請（案件編號：${uniqId}）狀態已更新。</p><p>匯款金額：${amount} ${currency}</p><p>目前狀態：${status}</p><p>凱基銀行 敬上</p>', '["uniqId", "amount", "currency", "status"]')
ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP;

-- 4. 創建視圖以相容原始系統（如需要）
-- 模擬 MailHunterHistory 視圖
CREATE OR REPLACE VIEW mail_hunter_history AS
SELECT 
    id,
    notification_id AS mail_id,
    uniq_id,
    recipient AS mail_to,
    subject AS mail_subject,
    content AS mail_content,
    attachment AS mail_attachment,
    status AS mail_status,
    created_at AS mail_create_date,
    sent_at AS mail_send_date,
    error_message AS mail_error_msg
FROM notification_history
WHERE channel = 'EMAIL';

-- 模擬 SMSHunterHistory 視圖
CREATE OR REPLACE VIEW sms_hunter_history AS
SELECT 
    id,
    notification_id AS sms_id,
    uniq_id,
    recipient AS phone_number,
    content AS sms_content,
    status AS sms_status,
    created_at AS sms_create_date,
    sent_at AS sms_send_date,
    error_message AS sms_error_msg
FROM notification_history
WHERE channel = 'SMS';