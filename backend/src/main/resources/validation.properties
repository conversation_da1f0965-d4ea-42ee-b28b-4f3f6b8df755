# The ESAPI validator does many security checks on input, such as canonicalization
# and whitelist validation. Note that all of these validation rules are applied *after*
# canonicalization. Double-encoded characters (even with different encodings involved,
# are never allowed.
#
# To use:
#
# First set up a pattern below. You can choose any name you want, prefixed by the word
# "Validation." For example:
#   Validation.Email=^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\\.[a-zA-Z]{2,4}$
#
# Then you can validate in your code against the pattern like this:
#   Validator.getInstance().getValidDataFromBrowser( "Email", input );
#   Validator.getInstance().isValidDataFromBrowser( "Email", input );
#
Validator.SafeString=[A-Za-z0-9]{0,1024}$
Validator.Email=^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\\.[a-zA-Z]{2,4}$
#Validator.Email_hide=^[A-Za-z0-9\._%-\*]+@[A-Za-z0-9-]+\.[a-zA-Z]{2,4}
Validator.Email_hide=^[\\S]+@[\\S]+$
Validator.IPAddress=^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$
Validator.URL=^(ht|f)tp(s?)\\:\\/\\/[0-9a-zA-Z]([-.\\w]*[0-9a-zA-Z])*(:(0-9)*)*(\\/?)([a-zA-Z0-9\\-\\.\\?\\,\\:\\'\\/\\\\\\+=&amp;%\\$#_]*)?$
Validator.CreditCard=^(\\d{4}[- ]?){3}\\d{4}$
Validator.SSN=^(?!000)([0-6]\\d{2}|7([0-6]\\d|7[012]))([ -]?)(?!00)\\d\\d\\3(?!0000)\\d{4}$

Validator.AlphaNumWithSpl=^[A-Za-z0-9 ._-]+$
Validator.AlphaNumWithMag=^[A-Za-z0-9\\+\\=\\/\r\n]{0,4096}+$
Validator.AntiXSS=^[^<>;'`]+$
Validator.SafeJson=^[^<>'`]+$
Validator.Space=^[\\s\\S]+$
Validator.HTTPHeaderName=^[a-zA-Z0-9\\-_]{1,32}$
Validator.HTTPHeaderValue=^[a-zA-Z0-9()\\-=\\*\\.\\?;,+\\/:&_ ]*$
