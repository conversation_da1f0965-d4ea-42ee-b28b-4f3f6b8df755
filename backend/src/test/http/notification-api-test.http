### Notification API 測試

### 變數設定
@baseUrl = http://localhost:8080
@contentType = application/json

### 1. 發送簡訊通知
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "SMS",
  "recipient": "0912345678",
  "templateId": "OTP_SMS",
  "parameters": {
    "otpCode": "123456"
  },
  "uniqId": "TEST_SMS_001",
  "uniqType": "IBR"
}

### 2. 發送郵件通知
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "EMAIL",
  "recipient": "<EMAIL>",
  "templateId": "OTP_EMAIL",
  "parameters": {
    "otpCode": "654321",
    "expireTime": "5"
  },
  "uniqId": "TEST_EMAIL_001",
  "uniqType": "IBR"
}

### 3. 發送匯款成功通知（簡訊）
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "SMS",
  "recipient": "0912345678",
  "templateId": "REMITTANCE_SUCCESS_SMS",
  "parameters": {
    "amount": "10000",
    "transactionId": "TXN20250614001"
  },
  "uniqId": "REMIT_SMS_001",
  "uniqType": "REMITTANCE"
}

### 4. 查詢通知狀態
# 需要替換實際的 notificationId
GET {{baseUrl}}/api/notification/status/NOTIF_ABC123
Content-Type: {{contentType}}

### 5. 查詢通知歷史
GET {{baseUrl}}/api/notification/history?uniqId=TEST_SMS_001
Content-Type: {{contentType}}

### 6. 查詢特定通道的通知歷史
GET {{baseUrl}}/api/notification/history?uniqId=TEST_SMS_001&channel=SMS
Content-Type: {{contentType}}

### 7. 測試發送頻率限制（連續發送）
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "SMS",
  "recipient": "0912345678",
  "templateId": "OTP_SMS",
  "parameters": {
    "otpCode": "111111"
  },
  "uniqId": "TEST_RATE_LIMIT",
  "uniqType": "TEST"
}

### 8. 測試錯誤情況 - 無效模板
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "SMS",
  "recipient": "0912345678",
  "templateId": "INVALID_TEMPLATE",
  "parameters": {},
  "uniqId": "TEST_ERROR_001",
  "uniqType": "TEST"
}

### 9. 測試錯誤情況 - 缺少必要參數
POST {{baseUrl}}/api/notification/send
Content-Type: {{contentType}}

{
  "channel": "EMAIL",
  "recipient": "<EMAIL>",
  "templateId": "OTP_EMAIL",
  "parameters": {
    "otpCode": "123456"
  },
  "uniqId": "TEST_ERROR_002",
  "uniqType": "TEST"
}