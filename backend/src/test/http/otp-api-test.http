### OTP API 測試

### 變數設定
@baseUrl = http://localhost:8080
@contentType = application/json
@token = Bearer your-jwt-token-here

### 1. 發送簡訊 OTP（原有 API）
POST {{baseUrl}}/publicApi/getOtpCode
Content-Type: {{contentType}}
Authorization: {{token}}

{
  "uniqId": "TEST_OTP_001",
  "uniqType": "QUERY",
  "usePhone": "0912345678",
  "email": ""
}

### 2. 發送 Email OTP（原有 API）
POST {{baseUrl}}/publicApi/getOtpCode
Content-Type: {{contentType}}
Authorization: {{token}}

{
  "uniqId": "TEST_OTP_002",
  "uniqType": "IBR",
  "usePhone": "",
  "email": "<EMAIL>"
}

### 3. 驗證 OTP（原有 API）
# 需要從發送 OTP 的回應中取得 sk 和 txnId
POST {{baseUrl}}/publicApi/checkOtpCode
Content-Type: {{contentType}}
Authorization: {{token}}

{
  "uniqId": "TEST_OTP_001",
  "otp": "123456",
  "sk": "SK_FROM_SEND_RESPONSE",
  "txnId": "TXN_FROM_SEND_RESPONSE"
}

### 4. 發送簡訊 OTP（新 API）
POST {{baseUrl}}/api/otp/send
Content-Type: {{contentType}}

{
  "uniqId": "TEST_NEW_001",
  "uniqType": "IBR",
  "usePhone": "0912345678",
  "email": ""
}

### 5. 發送 Email OTP（新 API）
POST {{baseUrl}}/api/otp/send
Content-Type: {{contentType}}

{
  "uniqId": "TEST_NEW_002",
  "uniqType": "IBR",
  "usePhone": "",
  "email": "<EMAIL>"
}

### 6. 驗證 OTP（新 API）
POST {{baseUrl}}/api/otp/verify
Content-Type: {{contentType}}

{
  "uniqId": "TEST_NEW_001",
  "otp": "123456",
  "sk": "SK_FROM_SEND_RESPONSE",
  "txnId": "TXN_FROM_SEND_RESPONSE"
}

### 7. 測試發送頻率限制（60秒內重複發送）
# 第一次發送
POST {{baseUrl}}/api/otp/send
Content-Type: {{contentType}}

{
  "uniqId": "TEST_RATE_001",
  "uniqType": "TEST",
  "usePhone": "0912345678",
  "email": ""
}

### 8. 測試錯誤驗證（錯誤的 OTP）
POST {{baseUrl}}/api/otp/verify
Content-Type: {{contentType}}

{
  "uniqId": "TEST_NEW_001",
  "otp": "999999",
  "sk": "SK_FROM_SEND_RESPONSE",
  "txnId": "TXN_FROM_SEND_RESPONSE"
}

### 9. 測試超過最大嘗試次數（連續錯誤3次）
# 第一次錯誤
POST {{baseUrl}}/api/otp/verify
Content-Type: {{contentType}}

{
  "uniqId": "TEST_ATTEMPT_001",
  "otp": "111111",
  "sk": "SK_TEST",
  "txnId": "TXN_TEST"
}

### 10. 測試 OTP 過期（需要等待5分鐘後）
POST {{baseUrl}}/api/otp/verify
Content-Type: {{contentType}}

{
  "uniqId": "TEST_EXPIRED_001",
  "otp": "123456",
  "sk": "SK_OLD",
  "txnId": "TXN_OLD"
}

### 11. 測試缺少必要參數
POST {{baseUrl}}/api/otp/send
Content-Type: {{contentType}}

{
  "uniqId": "TEST_MISSING_001",
  "uniqType": "TEST"
}

### 12. 測試無效的手機號碼格式
POST {{baseUrl}}/api/otp/send
Content-Type: {{contentType}}

{
  "uniqId": "TEST_INVALID_001",
  "uniqType": "TEST",
  "usePhone": "123456",
  "email": ""
}